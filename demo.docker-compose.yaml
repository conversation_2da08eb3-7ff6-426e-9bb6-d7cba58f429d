version: "3.2"

services:
  #######################################################
  ######################## OTHER ########################
  #######################################################
  db:
    container_name: carco-v2-db
    image: mysql:8
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=carco
    volumes:
      - ./.envProd/.database:/var/lib/mysql
      - ./import-folder/:/tmp/import
    ports:
      - "33066:3306"
    networks:
      - v2-carco-internal
  cache:
    container_name: carco-v2-cache
    hostname: cache
    image: redis:6.0-alpine
    ports:
      - "6379"
    networks:
      - v2-carco-internal
  #################################################################
  ######################## Magento Admin ##########################
  #################################################################
  magento:
    container_name: carco-v2-magento-admin
    image: carco-v2-magento-admin
    hostname: magento
    working_dir: /magento
    user: dev:dev
    volumes:
      - ./htdocs:/magento
      - .env/.bashrc:/home/<USER>/.bashrc
      - .envProd/php/magento-php.ini:/usr/local/etc/php/conf.d/env_config.ini
      - .envProd/php/magento-fpm.conf:/usr/local/etc/php-fpm.d/zz-docker.conf
    networks:
      - v2-carco-internal
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 2G
  #################################################################
  ######################## Frontend ###############################
  #################################################################
  frontend:
    container_name: carco-v2-frontend
    image: todor0pfg/carco-theme:x86_64-release-20230403
    hostname: frontend
    volumes:
      - ./frontend/site/.next/cache:/frontend/.next/cache
      - ./frontend/site/.env:/frontend/.env
    networks:
      - v2-carco-internal
    deploy:
      replicas: 5
      resources:
        limits:
          memory: 1G
  ################################################################
  ######################## GRAPHQL ###############################
  ################################################################
  graphapi:
    container_name: carco-v2-graphapi
    image: todor0pfg/carco-theme-api:x86_64-release-20230403
    hostname: graphapi
    working_dir: /graphapi
    volumes:
      - .envProd/.bashrc:/home/<USER>/.bashrc
      - ./graphapi/service/base-config.yaml:/graphapi/base-config.yaml
    networks:
      - v2-carco-internal
    deploy:
      replicas: 5
      resources:
        limits:
          memory: 1G
  ###############################################################
  ######################## SERVER ###############################
  ###############################################################
  server:
    container_name: carco-v2-server
    image: todor0pfg/magento-server:x86_64
    volumes:
      - ./htdocs:/magento
      #     UPDATE THIS LINE TO USE THE PRODUCTION CONFIGURATION
      - .envProd/nginx/demo.site.conf:/etc/nginx/sites-enabled/site.conf:ro
    ports:
      - "9090:80"
    networks:
      - internal_network
      - v2-carco-internal
    environment:
      - VIRTUAL_HOST=praktis.demo.pfgbulgaria.com
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 512MB
    depends_on:
      - graphapi
      - magento
      - frontend
      - db
      - cache

networks:
  v2-carco-internal:
    driver: overlay
    attachable: true
  internal_network:
    external:
      name: webgate_internal
