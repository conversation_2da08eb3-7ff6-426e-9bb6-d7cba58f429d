# myPOS Checkout SDK PHP
PHP Checkout Library tht provides convenient integration with myPOS Online Checkout Payments.

## Requirements

PHP 5.4.8 or later.

## Installation

### Install via composer
```
composer require developermypos/mypos-checkout-sdk
```

### Manual Installation

Download our last version from [here](https://github.com/developermypos/myPOS-Checkout-SDK-PHP/releases). Then use `Loader.php` file:

```php
require_once './IPC/Loader.php';
```

## Documentation

myPOS Checkout SDK for PHP manual is available at [developers.mypos.eu/en/doc/online_payments/v1_4/150-php-sdk](https://developers.mypos.eu/en/doc/online_payments/v1_4/150-php-sdk).
