# Store install

## For docks check the <Project_Root>/_docs folder

## Install instruction (Ubuntu)

1. Copy the `docker-compose.yaml` from `.Docker` to the root folder
   ```
   cp .Docker/docker-compose.yaml.exml docker-compose.yaml
   ```

2. In the `docker-compose.yaml`
    - replace `<project>` with the store name like `praktis`
    - replace `<MYSQL_LOCALHSOT_PORT>` with a free port, so you can connect to the db

3. Build the containers
   ```
   make setup
   # OR
   docker-composer build
   ```

4. Start the containers
   ```
   make up
   # or 
   docker-compose up -d
   ```

5. Import the database
   ```
   cp <database_file.sql> import-folder/
   
   make import-db
   # or 
   docker-compose exec <project>-db sh /tmp/import/import-db.sh
   ```

## Store Installation

1. Mount your ssh keys, so composer can install private (pfg) repositories
      ```
   # add a docker-compose.yaml volume to the PHP FMP container
   - ~/.ssh/id_rsa:/home/<USER>/.ssh/id_rsa:ro
   - ~/.ssh/id_rsa.pub:/home/<USER>/.ssh/id_rsa.pub:ro 
   ```
    - **WINDOWS** - build the image with keys in the `.Docker/base/.ssh`

2. SSH to PHP container
   ```
   make ssh
   # or
   docker-compose exec <projectt>-fpm74 bash
   ```

3. Composer install the store
   ```
   cd /var/www/html
   composer install
   ```

## Configure

### Enable Xdebug

1. Mount the `.env/php.ini` file from step 1
2. Edit the `php.ini` and

```
#xdebug.remote_enable=0 
to 
xdebug.remote_enable=1
```

3. Make sure your IDE listens for xdebug sessions on localhost on the port from the config and you have this in
   the `php.ini`

```
xdebug.remote_host=host.docker.internal
xdebug.remote_port=9999
xdebug.idekey=PHPSTORM
```

4. Yor docker compose has this in the php-fpm

```
services:
  <project>-fpm74:
    .....
    extra_hosts:
      - "host.docker.internal:host-gateway"
    .....
```

### Frontend minification and other tools

1. Add the front end container from the `.Docker/docker-compose.yaml.exml` to the main `docker-compose.yaml`
2. enter container `make frontend`

### Enable crons container

1. Uncomment the cron container code in the docker-compose.yaml
2. docker-compose build
3. docker-compose up -d (as make up does not start the container)