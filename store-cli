#!/bin/bash

store="carco"
storeRootPath="$(pwd)"
dockerMagento=$(docker ps | grep carco_magento.1 | awk '{print $1}')
dockerServer=$(docker ps | grep carco_server.1 | awk '{print $1}')
dockerCache=$(docker ps | grep carco_cache.1 | awk '{print $1}')
dockerDb=$(docker ps | grep carco_db.1 | awk '{print $1}')
dockerFront=$(docker ps | grep carco_frontend.1 | awk '{print $1}')
dockerCrontab=$(docker ps | grep carco_crontab.1 | awk '{print $1}')

pArch=$(uname -m | sed 's/x86_64/amd64/g' | sed 's/aarch64/arm64/g')
env="prod"

feLocation="$storeRootPath/frontend.v2"

serverImage="todor0pfg/carco:server-${pArch}"
magentoImage="todor0pfg/carco:magento-${pArch}"
cronImage="todor0pfg/carco:crontab-${pArch}"
noCache=""
frontendImage="todor0pfg/carco:frontend-${pArch}-$(date +"%Y%m%d")"
graphapiImage="todor0pfg/carco:graphapi-${pArch}-$(date +"%Y%m%d")"

##############################################################
# DEPLOYMENT COMMANDS
##############################################################
if [ "$1" == "pull" ]; then
  echo "Pulling magento"
  cd "$storeRootPath"
  git pull
  cd "$feLocation"
  echo "Pulling frontend"
  git pull
  cd ..
  cd "$storeRootPath/graphapi"
  echo "Pulling graphapi"
  git pull
  cd ..
elif [ "$1" == "gitinfo" ]; then
  echo "Pulling magento"
  cd "$storeRootPath"
  git status
  cd "$feLocation"
  echo "Pulling frontend"
  git status
  cd ..
  cd "$storeRootPath/graphapi"
  echo "Pulling graphapi"
  git status
  cd ..
elif [ "$1" == "up" ]; then
  docker stack deploy -c docker-compose.yaml carco
elif [ "$1" == "down" ]; then
  docker stack rm carco
elif [ "$1" == "cc" ]; then
  if [ "$2" == "full" ]; then
    ./store-cli cc graphapi && ./store-cli cc magento
  elif [ "$2" == "magento" ]; then
    docker exec "$dockerCache" redis-cli -n 5 flushdb
  elif [ "$2" == "graphapi" ]; then
    docker exec "$dockerCache" redis-cli -n 1 flushdb
  else
    echo "Usage: store-cli cc [full|magento|graphapi]"
  fi
elif [ "$1" == "ssh" ]; then
  if [ "$2" == "magento" ]; then
    docker exec -it "$dockerMagento" bash
  elif [ "$2" == "server" ]; then
    docker exec -it "$dockerServer" bash
  elif [ "$2" == "frontend" ]; then
    docker exec -it "$dockerFront" bash
  elif [ "$2" == "crontab" ]; then
    docker exec -it "$dockerCrontab" bash
  else
    echo "Usage: store-cli ssh [server|frontend|magento|crontab]"
  fi
elif [ "$1" == "rebuild" ]; then
  if [ "$2" == "magento" ]; then
    cd "$storeRootPath"
    echo "Building:$magentoImage"
    docker build "_env/$env" -t "$magentoImage" -f "_env/$env/magento.Dockerfile" && \
    docker tag "$magentoImage"  "$magentoImage" && \
    docker push "$magentoImage"
  elif [ "$2" == "crontab" ]; then
    cd "$storeRootPath"
    echo "Building:$cronImage"
    docker build  "_env/$env" -t "$cronImage" -f "_env/$env/crontab.Dockerfile"  && \
    docker tag  "$cronImage" "$cronImage" && \
    docker push  "$cronImage"
  elif [ "$2" == "frontend" ]; then
    if [ "$3"]; then
      frontendImage="todor0pfg/carco:$3"
    fi
    echo "Building:$frontendImage"
    docker build --progress plain "$feLocation" -t "$frontendImage" -f "_env/$env/frontend.Dockerfile" && \
    docker tag "$frontendImage" "$frontendImage" && \
    docker push "$frontendImage"
  elif [ "$2" == "graphapi" ]; then
    echo "Building:$graphapiImage"
    docker build "$storeRootPath/graphapi" -t "$graphapiImage" -f "_env/$env/graphapi.Dockerfile" && \
    docker tag "$graphapiImage" "$graphapiImage" && \
    docker push "$graphapiImage"
    cd ..
  elif [ "$2" == "server" ]; then
    echo "Building:$serverImage"
    docker build "_env/$env" -t "$serverImage" -f "_env/$env/server.Dockerfile" && \
    docker tag "$serverImage" "$serverImage" && \
    docker push "$serverImage"
  else
    echo "Usage: store-cli rebuild [server|magento|frontend|graphapi|crontab]"
  fi
elif [ "$1" == "reload" ]; then
  if [ "$2" == "magento" ]; then
    docker service update --force carco_magento --with-registry-auth --image "$magentoImage"
  elif [ "$2" == "crontab" ]; then
    docker service update --force carco_crontab --with-registry-auth --image "$cronImage"
  elif [ "$2" == "frontend" ]; then
    if [ "$3" != "" ]; then
        frontendImage="$3"
    fi
    echo "carco_frontend:image:$frontendImage" && \
     docker service update --force carco_frontend --with-registry-auth --image "$frontendImage"
  elif [ "$2" == "server" ]; then
    docker service update --force carco_server --with-registry-auth --image "$serverImage"
  elif [ "$2" == "graphapi" ]; then
    docker service update --force carco_graphapi --with-registry-auth --image "$graphapiImage"
  else
    echo "Usage: store-cli reload [server|frontend|magento|graphapi]"
  fi
elif [ "$1" == "theme-reindex" ]; then
  docker exec -it "$dockerMagento" php /magento/shell/theme/PFG_Indexer.php --index catalog
elif [ "$1" == "exec" ]; then
  if [ "$2" == "magento" ]; then
    docker exec -it "$dockerMagento" bash
  elif [ "$2" == "server" ]; then
    docker exec -it "$dockerServer" bash
  elif [ "$2" == "frontend" ]; then
    docker exec -it "$dockerFront" bash
  elif [ "$2" == "cache" ]; then
    docker exec -it "$dockerCache" bash
  else
    echo "Usage: store-cli exec [server|frontend|magento|cache]"
  fi
elif [ "$1" == "logs" ]; then
  if [ "$2" == "frontend" ]; then
    docker service logs -tf carco_frontend
  elif [ "$2" == "server" ]; then
    docker service logs -tf carco_server
  elif [ "$2" == "ingress" ]; then
    kubectl logs -f -n ingress-nginx ingress-nginx-controller
  elif [ "$2" == "magento" ]; then
    docker service logs -tf carco_magento
  elif [ "$2" == "graphapi" ]; then
    docker service logs -tf carco_graphapi
  else
    echo "Usage: store-cli logs [server|frontend|magento]"
  fi
elif [ "$1" == "db" ]; then
  if [ "$2" == "import" ]; then
      docker exec -it "$dockerDb" bash -c "time mysql -uroot -p $3 < /tmp/import/$4"
  else
    echo "Usage: store-cli db import <db_name|carco> <file_name>"
  fi
elif [ "$1" == "stats" ]; then
  docker stack services --format '{{.ID}} -> {{.Replicas}} -> {{printf "%-18s" .Name}} -> {{.Image}}' carco
elif [ "$1" == "sync" ]; then
  if [ "$2" == "frontend" ]; then
    ./store-cli pull && ./store-cli rebuild frontend && ./store-cli reload frontend && ./store-cli reload server && ./cf-clear
  elif [ "$2" == "graphapi" ]; then
    ./store-cli pull && ./store-cli rebuild graphapi && ./store-cli reload graphapi && ./store-cli reload server
  else
    echo "Usage: store-cli sync [graphapi|frontend]"
  fi
elif [ "$1" == "images" ]; then
  ./store-cli load-images
elif [ "$1" == "load-images" ]; then
  echo "Loading magento: $magentoImage"
  docker pull "$magentoImage"
  echo "Loading cron: $cronImage"
  docker pull "$cronImage"
  echo "Loading image: $frontendImage"
  docker pull "$frontendImage"
  echo "Loading image: $graphapiImage"
  docker pull "$graphapiImage"
  echo "Loading server: $serverImage"
  docker pull "$serverImage"
fi

