# 🚀 Carco Development Environment - Xdebug Setup Guide

## ✅ Current Status

Your local development environment is now **fully configured** and ready for debugging!

### What's Working:
- ✅ **Magento Backend**: PHP 7.4.33 with Xdebug 3.1.4
- ✅ **Web Access**: http://localhost (shows development status page)
- ✅ **Magento Admin**: http://localhost/ecomin
- ✅ **Xdebug**: Configured and ready for debugging
- ✅ **Database**: MySQL 8 on localhost:33066
- ✅ **Redis Cache**: Available on localhost:6379

### Test Pages:
- **Development Status**: http://localhost
- **Xdebug Test**: http://localhost/xdebug-test.php
- **Magento Admin**: http://localhost/ecomin

## 🔧 Xdebug Configuration

### Current Settings:
- **Mode**: `develop,debug` (debugging enabled)
- **Client Host**: `host.docker.internal`
- **Client Port**: `9003`
- **IDE Key**: `PHPSTORM`
- **Start with Request**: `Yes` (automatic connection)

## 🛠️ IDE Setup Instructions

### PhpStorm Setup:

1. **Configure PHP Interpreter**:
   - Go to `Settings` → `PHP`
   - Add new interpreter: `From Docker, Vagrant, VM, WSL, Remote...`
   - Choose `Docker Compose`
   - Service: `magento`
   - PHP executable path: `/usr/local/bin/php`

2. **Configure Xdebug**:
   - Go to `Settings` → `PHP` → `Debug`
   - Set Xdebug port to `9003`
   - Check "Can accept external connections"
   - Uncheck "Force break at first line when no path mapping specified"

3. **Configure Server**:
   - Go to `Settings` → `PHP` → `Servers`
   - Add new server:
     - Name: `carco-magento`
     - Host: `localhost`
     - Port: `80`
     - Debugger: `Xdebug`
     - Use path mappings: ✅
     - Map your local `htdocs` folder to `/magento`

4. **Start Listening**:
   - Click the "Start Listening for PHP Debug Connections" button (phone icon)
   - Set breakpoints in your PHP code
   - Access any PHP page in your browser

### VS Code Setup:

1. **Install PHP Debug Extension**:
   ```bash
   code --install-extension xdebug.php-debug
   ```

2. **Create launch.json**:
   ```json
   {
       "version": "0.2.0",
       "configurations": [
           {
               "name": "Listen for Xdebug",
               "type": "php",
               "request": "launch",
               "port": 9003,
               "pathMappings": {
                   "/magento": "${workspaceFolder}/htdocs"
               }
           }
       ]
   }
   ```

3. **Start Debugging**:
   - Press `F5` or go to Run → Start Debugging
   - Set breakpoints in your PHP files
   - Access any PHP page in your browser

## 🧪 Testing Xdebug

1. **Quick Test**:
   - Visit: http://localhost/xdebug-test.php
   - Should show "✅ Xdebug is loaded!" with configuration details

2. **Breakpoint Test**:
   - Set a breakpoint in `htdocs/xdebug-test.php` at line 35
   - Refresh the page
   - Your IDE should stop at the breakpoint

3. **Magento Debugging**:
   - Set breakpoints in any Magento PHP file
   - Access the admin panel: http://localhost/ecomin
   - Debug Magento's execution flow

## 📁 Project Structure

```
carco.bg/
├── htdocs/                 # Magento root (mapped to /magento in container)
├── _env/dev/php/          # PHP/Xdebug configuration
├── docker-compose.yaml    # Updated with Xdebug settings
└── XDEBUG_SETUP.md       # This guide
```

## 🚀 Starting the Environment

```bash
# Start all services
docker-compose up -d

# Check status
docker ps

# View logs
docker-compose logs -f

# SSH into PHP container
docker-compose exec magento bash
```

## 🔍 Troubleshooting

### Xdebug Not Connecting:

1. **Check Xdebug Status**:
   ```bash
   docker exec carco-fpm74 php -m | grep xdebug
   ```

2. **Check Configuration**:
   ```bash
   docker exec carco-fpm74 php -i | grep xdebug.client_host
   ```

3. **Check Logs**:
   ```bash
   docker exec carco-fpm74 tail -f /tmp/xdebug.log
   ```

### Port Issues:
- Ensure port 9003 is not blocked by firewall
- Check if another debugger is using port 9003

### Path Mapping Issues:
- Verify your IDE path mappings match:
  - Local: `your-project/htdocs`
  - Remote: `/magento`

## 📝 Next Steps

1. **Set up Frontend** (optional):
   - Follow instructions in `_docs/dev.md`
   - The backend works independently

2. **Database Access**:
   - Host: `localhost`
   - Port: `33066`
   - Username: `root`
   - Password: `root`
   - Database: `carco`

3. **Import Database** (if needed):
   ```bash
   make import-db
   ```

## 🎉 You're Ready!

Your Magento development environment with Xdebug is now fully configured and ready for development. Happy debugging! 🐛🔍
