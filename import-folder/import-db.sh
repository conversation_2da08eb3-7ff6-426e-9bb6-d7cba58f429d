importFile=$(ls /tmp/import/*.sql | sort -V | tail -n 1)

if [ -f "$importFile" ]; then
  echo "Importing database from $importFile"
  dbName=$(echo $importFile | sed -e "s/.sql//g")
  dbName=$(echo $dbName | sed -e "s/\/tmp\/import\///g")
  echo "Creating database: $dbName"
  mysql -uroot -proot -e "create database $dbName"
  echo "Importing data in to $dbName from $importFile"
  time mysql -uroot -proot "$dbName" < "$importFile"
else
  echo "No import file found"
fi
