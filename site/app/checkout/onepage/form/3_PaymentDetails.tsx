'use client'
import React, { useCallback, useEffect } from 'react'
import FieldsWrap<PERSON>, {
  useFieldWrapperState,
} from '@app/checkout/onepage/form/Wrapper/FieldsWrapper'
import { addClasses } from '@lib/utils/style'
import { Radio } from '@mantine/core'
import { useFormContext } from '@context/FormContext'
import CustomRadio from '@atom/Form/CustomRadio'
import BankPaymentInstructions from '@app/checkout/onepage/form/PaymentDetails/BankPaymentInstructions'
import { CheckoutEvents } from '@app/checkout/onepage/_lib/_xstate'
import { useCheckoutFormContext } from '@app/checkout/onepage/_lib/_context'
import ContinueButton from '@app/checkout/onepage/form/Wrapper/ContinueButton'
import { preventDefaultCall } from '@lib/utils/func'
import {
  NewMagentoOrder,
  PaymentMethod,
  PaymentTypes,
} from '@app/checkout/onepage/_lib/_types'
import SectionPreview from '@app/checkout/onepage/form/SectionPreview'
import Button from '@atom/Button'
import CartPaymentWarning from '@app/checkout/onepage/form/PaymentDetails/CartPaymentWarning'
import { appTrackCheckoutStep } from '@lib/tracking/tracking'

const AVAILABLE_PAYMENT_TYPES: PaymentMethod[] = [
  {
    label: 'Наложен платеж',
    value: 'cashOnDelivery',
  },
  {
    label: 'С карта',
    value: 'creditCard',
  },
  {
    label: 'Банков превод',
    value: 'bankTransfer',
  },
  {
    label: 'MyPOS плащане',
    value: 'mypos',
    additionalInformation: 'Сигурно плащане с карта чрез MyPOS',
  },
]

interface Props {
  allowEdit?: boolean
}

const PaymentDetails: React.FC<Props> = (props) => {
  const form = useFormContext<NewMagentoOrder>()
  let _fieldVal = form.getInputProps('paymentMethod').value
  const { sendEvent } = useCheckoutFormContext()

  const onChange = useCallback(
    (value: PaymentTypes) => {
      form.setFieldValue('paymentMethod', value)
    },
    [form]
  )

  useEffect(() => {
    appTrackCheckoutStep(3)
  }, [])

  const fieldWrapperState = useFieldWrapperState('payment_details')

  const _selected = AVAILABLE_PAYMENT_TYPES.find((p) => p.value === _fieldVal)

  return (
    <FieldsWrapper
      title={'Начин на плащане'}
      {...fieldWrapperState}
      onEdit={() =>
        sendEvent(CheckoutEvents.EDIT_STEP, {
          step: 'payment_details',
        })
      }
      preview={
        <SectionPreview
          data={[
            {
              label: 'Начин на плащане',
              value: _selected?.label ?? 'Няма избран начин на плащане',
            },
          ]}
        />
      }
    >
      <Radio.Group
        name='paymentOptions'
        value={_fieldVal}
        onChange={onChange}
        error={form.getInputProps('paymentMethod').error}
        styles={{
          error: {
            fontWeight: 'bold',
            marginTop: '5px',
          },
        }}
      >
        <div
          className={addClasses(
            'w-full  flex flex-col gap-5',
            'desktop:m-0 desktop:flex-row'
          )}
        >
          {AVAILABLE_PAYMENT_TYPES.map((paymentType) => (
            <Button
              unstyled
              key={paymentType.value}
              className={addClasses(
                'flex flex-col items-start rounded-xl justify-center border min-w-[190px] mt-2 p-5',
                'desktop:items-center'
              )}
              onClick={() => onChange(paymentType.value)}
            >
              <div className="flex flex-col items-start">
                <CustomRadio
                  label={paymentType.label}
                  value={paymentType.value}
                  color='red'
                  className='flex'
                />
                {paymentType.additionalInformation && (
                  <p className="text-sm text-gray-600 mt-1 ml-6">
                    {paymentType.additionalInformation}
                  </p>
                )}
              </div>
            </Button>
          ))}
        </div>
      </Radio.Group>

      {_fieldVal == 'bankTransfer' && <BankPaymentInstructions />}
      {_fieldVal == 'creditCard' && <CartPaymentWarning />}
      {_fieldVal == 'mypos' && (
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="font-semibold text-blue-800 mb-2">MyPOS плащане</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Ще бъдете пренасочени към сигурната платформа на MyPOS</li>
            <li>• Поддържа всички основни кредитни и дебитни карти</li>
            <li>• Плащането се обработва в реално време</li>
            <li>• Всички данни се предават криптирано</li>
          </ul>
        </div>
      )}

      <div className={'flex justify-end mt-4'}>
        <ContinueButton
          onClick={preventDefaultCall(() => {
            const res = form.validate()
            if (!res || !res.hasErrors) {
              sendEvent(CheckoutEvents.NEXT_STEP)
            }
          })}
        >
          Продължи към завършване
        </ContinueButton>
      </div>
    </FieldsWrapper>
  )
}

export default PaymentDetails
