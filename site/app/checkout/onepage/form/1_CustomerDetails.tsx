'use client'
import React, { useEffect } from 'react'
import TextField from '@atom/Form/TextField'
import CustomerDetailsInvoice from '@app/checkout/onepage/form/CustomerDetails/CustomerDetailsInvoice'
import CustomerDetailsRegistration from '@app/checkout/onepage/form/CustomerDetails/CustomerDetailsRegistration'
import FieldsWrapper, {
  useFieldWrapperState,
} from '@app/checkout/onepage/form/Wrapper/FieldsWrapper'
import { useCheckoutFormContext } from '@app/checkout/onepage/_lib/_context'
import ContinueButton from '@app/checkout/onepage/form/Wrapper/ContinueButton'
import { preventDefaultCall } from '@lib/utils/func'
import { CheckoutEvents } from '@app/checkout/onepage/_lib/_xstate'
import { useFormContext } from '@context/FormContext'
import {
  CheckoutFormData,
  IInvoiceModel_V2,
  NewOrderCustomer,
} from '@app/checkout/onepage/_lib/_types'
import SectionPreview, {
  SectionPreviewRow,
} from '@app/checkout/onepage/form/SectionPreview'
import CustomerAddressSelect, {
  useCustomerAddressSelect,
} from '@app/checkout/onepage/form/CustomerDetails/CustomerAddressSelect'
import { Address } from '@lib/generated/graphql'
import CustomerAddressPreview from '@app/checkout/onepage/form/CustomerDetails/CustomerAddressPreview'
import { getAddressRows } from '@app/customer/_type'
import { useOnFieldUpdate } from '@lib/form/hooks'
import { appTrackCheckoutStep } from '@lib/tracking/tracking'

function getPreviewArray(props: Props): SectionPreviewRow[] {
  const { customer, invoice } = props

  const data: SectionPreviewRow[] = [
    {
      label: 'Име',
      value: customer?.firstname + ' ' + customer?.lastname,
    },
    {
      label: 'Телефон',
      value: customer?.phone || '*********** - не е въведен',
    },
    {
      label: 'Имейл',
      value: customer?.email || '@липсва',
    },
  ].filter(Boolean) as SectionPreviewRow[]

  if (invoice?.required) {
    data.push(
      {
        label: '-',
        value: '-',
      },
      {
        label: 'Инвойс',
        value: 'Да',
      }
    )

    if (invoice?.type === 'company') {
      data.push({
        label: 'ЕИК',
        value: invoice?.eik || '********* - не е въведен',
      })

      if (invoice?.ddsRegistration) {
        data.push({
          label: 'ДДС номер',
          value: invoice?.ddsNumber || '********* - не е въведен',
        })
      }

      data.push({
        label: 'Фирма',
        value: invoice?.company || 'Липсва',
      })

      data.push(
        {
          label: 'МОЛ',
          value: invoice?.mol || 'Липсва',
        },
        {
          label: 'Адрес',
          value: invoice?.address || 'Липсва',
        }
      )
    } else {
      data.push({
        label: 'Име',
        value: invoice?.personName || 'Липсва',
      })

      data.push({
        label: 'ЕГН',
        value: invoice?.pin || '********* - не е въведено',
      })

      data.push({
        label: 'Адрес',
        value: invoice?.address || 'Липсва',
      })
    }
  }

  return data
}

interface Props {
  customer?: NewOrderCustomer
  invoice?: IInvoiceModel_V2
}

const CustomerDetails: React.FC<Props> = () => {
  const { state, sendEvent, service } = useCheckoutFormContext()
  const { values, validate, setFieldValue } = useFormContext<CheckoutFormData>()

  const fieldWrapperState = useFieldWrapperState('customer_details')

  const selectState = useCustomerAddressSelect(
    state.context.customer,
    values.customer?.addressId
  )

  // observe customer_login event in service
  useEffect(() => {
    const sub = service.subscribe((state: any) => {
      if (state.event.type === CheckoutEvents.UPDATE_CUSTOMER) {
        //setFieldValue('customer', state.context.customer)
        const _c = state.context.customer
        // console.log('UPDATE_CUSTOMER', _c)
        if (_c) {
          setFieldValue('customer', {
            firstname: _c.firstname,
            lastname: _c.lastname,
            phone: _c.phone,
            email: _c.email,
            addressId: _c.addressId,
            register: false,
            password: '',
            confirmPassword: '',
          } as NewOrderCustomer)
        } else {
          setFieldValue('customer', {
            firstname: '',
            lastname: '',
            phone: '',
            email: '',
            register: false,
            password: '',
            confirmPassword: '',
            addressId: '',
          } as NewOrderCustomer)
        }
      }
    })

    return () => sub.unsubscribe()
  }, [service, setFieldValue])

  useEffect(() => {
    appTrackCheckoutStep(1)
  }, [])

  useOnFieldUpdate('customer.addressId', (value: string) => {
    const _c = state.context.customer ?? ({} as any)

    if (value) {
      const address = selectState.availableAddresses.find(
        (a) => a.addressId === value
      ) as Address

      if (address) {
        setFieldValue('customer', {
          firstname: address.firstname,
          lastname: address.lastname,
          phone: address.telephone,
          email: _c.email || '',
          register: false,
          password: '',
          confirmPassword: '',
          addressId: value,
        } as NewOrderCustomer)
        setFieldValue('shipping', {
          shippingMethod: '',
          option: '',
          address: {
            city: '',
            postcode: '',
            street: '',
            streetNumber: '',
            quarter: '',
            note: '',
            office: '',
            partner: '',
          },
        })
        return
      }
    }

    setFieldValue('customer', {
      firstname: _c.firstname || '',
      lastname: _c.lastname || '',
      phone: _c.phone || '',
      email: _c.email || '',
      register: false,
      password: '',
      confirmPassword: '',
      addressId: value,
    } as NewOrderCustomer)
  })

  return (
    <FieldsWrapper
      title={'Данни за клиента'}
      {...fieldWrapperState}
      onEdit={() =>
        sendEvent(CheckoutEvents.EDIT_STEP, {
          step: 'customer_details',
        })
      }
      preview={
        <SectionPreview
          data={getPreviewArray({
            customer: values.customer,
            invoice: values.invoice,
          })}
        />
      }
    >
      {selectState.availableAddresses.length > 0 && (
        <CustomerAddressSelect addresses={selectState.availableAddresses} />
      )}
      <div className={'flex flex-col desktop:grid desktop:grid-cols-2 gap-4'}>
        {!!selectState.selectedAddress ? (
          <CustomerAddressPreview
            className={'my-3'}
            rows={getAddressRows(
              state.context.customer,
              selectState.selectedAddress
            )}
          />
        ) : (
          <>
            <TextField name='customer.firstname' label={'Име'} required />
            <TextField name='customer.lastname' label={'Фамилия'} required />
            <TextField name='customer.phone' label={'Телефон'} required />
            <TextField name='customer.email' label={'E-mail'} required />
            <div className={'col-span-2 flex flex-col gap-2'}>
              {!selectState.isLoaded && (
                <CustomerDetailsRegistration customer={values.customer} />
              )}
              <CustomerDetailsInvoice invoice={values.invoice} />
            </div>
          </>
        )}
      </div>

      <div className={'flex justify-end mt-2'}>
        <ContinueButton
          onClick={preventDefaultCall(() => {
            const res = validate()
            if (!res.hasErrors) {
              sendEvent(CheckoutEvents.NEXT_STEP)
            }
          })}
        >
          Продължи към доставка
        </ContinueButton>
      </div>
    </FieldsWrapper>
  )
}

export default CustomerDetails
