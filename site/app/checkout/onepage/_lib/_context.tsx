'use client'
import React, { createContext, useContext, useEffect, useMemo } from 'react'
import { useMachine } from '@xstate/react'
import checkoutMachine, {
  CheckoutEvents,
  CheckoutMachineData,
} from '@app/checkout/onepage/_lib/_xstate'
import { State } from 'xstate'
import PopupWindow from '@molecule/Page/Modals/PopupWindow'
import Static<PERSON>enderer from '@atom/StaticRenderer'
import FormContext from '@context/FormContext'
import {
  CheckoutFormData,
  getCheckoutValidator,
  getMainAddress,
} from '@app/checkout/onepage/_lib/_types'
import {
  MagentoShippingMethodFragment,
  MapValue,
  OrderRedirect,
  ShippingMethodCode,
} from '@lib/generated/graphql'
import { usePageContext } from '@context/PageContext'
import { useCheckoutContextContext } from '@context/CheckoutContext'
import { useNavigation } from '@hooks/Navigation'
import { ICustomerModel } from '@app/customer/_type'
import { useXstateDebugEvents } from '@lib/xstate/debug'
import { appTrackCheckoutInit } from '@lib/tracking/tracking'

export interface CheckoutForm {
  sendEvent: (
    event: CheckoutEvents,
    payload?: any
  ) => State<CheckoutMachineData>
  state: State<CheckoutMachineData, any, any, any>
  service: any
}

function redirectToCardProcessor(url: string, data: [MapValue], method: string = 'POST', autoRedirect: boolean = true): void {
  if (method === 'GET' && autoRedirect) {
    // For MyPOS and other GET redirects, redirect directly
    const urlParams = new URLSearchParams()
    for (const { key, value } of data) {
      urlParams.append(key, value)
    }
    const redirectUrl = data.length > 0 ? `${url}?${urlParams.toString()}` : url
    window.location.href = redirectUrl
    return
  }

  // For POST redirects (traditional card processors)
  const form = document.createElement('form')
  form.method = method
  form.action = url
  for (const { key, value } of data) {
    const input = document.createElement('input')
    input.type = 'hidden'
    input.name = key
    input.value = value
    form.appendChild(input)
  }
  document.body.appendChild(form)
  form.submit()
}

const Context = createContext<CheckoutForm>({} as CheckoutForm)

interface Props {
  initialMessage?: string
  initialData?: CheckoutFormData
  cartId?: string
  customer?: ICustomerModel
  children: React.ReactNode
}

const CheckoutFormContext: React.FC<Props> = ({
  initialMessage,
  cartId,
  children,
  initialData,
  customer,
}) => {
  const { showError, showSuccess } = usePageContext()
  const { goTo } = useNavigation()
  const [state, sendEvent, service] = useMachine(checkoutMachine, {
    context: {
      cartId: cartId,
      userId: '',
      customer: customer,
    },
    devTools: true,
  })

  const { updateCart } = useCheckoutContextContext()

  useEffect(() => {
    const subscription = service.subscribe((state) => {
      if (state.event?.type === CheckoutEvents.UPDATE_CART) {
        updateCart(state.event.cart)
      } else if (state.event?.type === CheckoutEvents.SHOW_ERROR) {
        let msg = state.event.message.toLowerCase()
        if (msg.indexOf('invalid cart id') != -1) {
          updateCart(null)
          goTo('/checkout/cart')
        }
        showError(state.event.message)
      } else if (state.event?.type === CheckoutEvents.REDIRECT_TO_SUCCESS) {
        showSuccess('Пръчката е направена успешно!')
        goTo('/checkout/success')
      } else if (
        state.event?.type === CheckoutEvents.REDIRECT_TO_CARD_PROCESSOR
      ) {
        const data = state.event.data as OrderRedirect

        showSuccess('Ще бъдеш пренасовен към системата за плащане')
        redirectToCardProcessor(
          data.url,
          data.data as [MapValue],
          data.method || 'POST',
          data.autoRedirect !== false
        )
      }
    })

    return subscription.unsubscribe
  }, [service, updateCart, showError, showSuccess, goTo])

  useXstateDebugEvents(service, 'CheckoutFormContext')

  const _formData = useMemo(() => {
    const address = getMainAddress(customer)

    return {
      ...initialData,
      customer: {
        ...initialData?.customer,
        email: customer?.email || '',
        firstname: address?.firstname || '',
        lastname: address?.lastname || '',
        phone: address?.telephone || '',
      },
    }
  }, [initialData, customer])

  return (
    <Context.Provider
      value={
        {
          state: state as any,
          sendEvent: sendEvent as any,
          service: service,
        } as CheckoutForm
      }
    >
      {initialMessage && (
        <PopupWindow
          isOpen={state.value === 'showNotification'}
          closeFn={() => sendEvent(CheckoutEvents.CLOSE_NOTIFICATION)}
        >
          <StaticRenderer className={'p-6'} rawHtml={initialMessage} />
        </PopupWindow>
      )}

      <FormContext data={_formData} validationSchema={getCheckoutValidator()}>
        {children}
      </FormContext>
    </Context.Provider>
  )
}

export default CheckoutFormContext

export const useCheckoutFormContext = (): CheckoutForm => {
  return useContext(Context)
}

export const useShippingMethod = (
  key: ShippingMethodCode
): MagentoShippingMethodFragment | undefined => {
  const { state } = useCheckoutFormContext()

  return state.context.shippingMethods?.find((method) => method.code === key)
}
export const useMethodAdditionalAttribute = (
  key: ShippingMethodCode,
  attr: string
): string | undefined => {
  const method = useShippingMethod(key)

  if (!method) {
    return undefined
  }

  return method.additional?.find((_attr) => _attr && _attr.key === attr)?.value
}
