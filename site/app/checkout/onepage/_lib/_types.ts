import {
  getAddressNoteValidation,
  getCityValidation,
  getConfirmPasswordValidation,
  getPasswordValidation,
  getPostcodeValidation,
  getQuarterValidation,
  getStreetNumberValidation,
  getStreetValidation,
  getTelephoneValidation,
} from '@lib/form/form'
import { _consoleDebug } from '@lib/utils/func'
import { FormErrors, yupResolver } from '@mantine/form'
import * as Yup from 'yup'
import { ObjectSchema } from 'yup'
import { ValidationShape } from '@lib/form/types'
import {
  CheckoutFromStep,
  CheckoutFromSteps,
} from '@app/checkout/onepage/_lib/_xstate'
import {
  getOptionValue,
  SelectOption,
} from '@molecule/Page/UiElements/SearchableSelect/SearchableSelect'
import {
  Address,
  MagentoInvoice,
  MagentoInvoiceType,
  ShippingDataInput,
  ShippingMethodCode,
} from '@lib/generated/graphql'
import { ICustomerModel } from '@app/customer/_type'

export interface IBankTransferModel {
  bankName: string
  IBAN: string
  BIC: string
  disclaimer: string
}

export type PaymentTypes = 'cashOnDelivery' | 'creditCard' | 'bankTransfer' | 'mypos' | ''

export interface PaymentMethod {
  label: string
  value: PaymentTypes
  additionalInformation?: string
}

export const DeliveryOptionOffice = ShippingMethodCode.EcontToOffice
export const DeliveryOptionSpeedyOffice = ShippingMethodCode.SpeedyToOffice
export const DeliveryOptionAddress = ShippingMethodCode.EcontToDoor
export const DeliveryOptionSpeedyAddress = ShippingMethodCode.SpeedyToDoor
export const DeliveryOptionOwnTransport = ShippingMethodCode.OwnTransport
export const DeliveryOptionByPartner = ShippingMethodCode.PartnerPickup
export const DeliveryOptionByCarco = ShippingMethodCode.CarcoTransport

export type ShippingMethods =
  | typeof DeliveryOptionOffice
  | typeof DeliveryOptionAddress
  | typeof DeliveryOptionOwnTransport
  | typeof DeliveryOptionByPartner
  | typeof DeliveryOptionByCarco
  | typeof DeliveryOptionSpeedyOffice
  | typeof DeliveryOptionSpeedyAddress
  | ''

export interface IInvoiceModel_V2 extends MagentoInvoice {}

export interface NewOrderCustomer {
  firstname: string
  lastname: string
  phone: string
  email: string
  register: boolean
  password: string
  confirmPassword: string
  addressId: string
}

export interface NewOrderShippingData {
  shippingMethod: ShippingMethods
  option: string
  address: {
    city?: SelectOption
    postcode: string
    street?: string
    streetNumber?: string
    quarter?: SelectOption
    note?: string
    office?: SelectOption
    partner?: string
  }
}

export function newShippingDataToMethodData(
  order: NewMagentoOrder
): ShippingDataInput {
  const shipping = order.shipping

  return {
    shippingMethod: !!shipping.shippingMethod
      ? shipping.shippingMethod
      : undefined,
    address: {
      cityId: shipping.address.city
        ? getOptionValue(shipping.address.city)
        : '',
      note: shipping.address.note,
      officeId: shipping.address.office
        ? getOptionValue(shipping.address.office)
        : '',
      postCode: shipping.address.postcode,
      quarterId: shipping.address.quarter
        ? getOptionValue(shipping.address.quarter)
        : '',
      street: shipping.address.street,
      streetNumber: shipping.address.streetNumber,
    },
    customer: {
      email: order.customer.email,
      firstname: order.customer.firstname,
      lastname: order.customer.lastname,
      phone: order.customer.phone,
    },
  }
}

interface OrderTerms {
  orderComment: string
  ageConsent: boolean
  termsConsent: boolean
  privacyPolicyConsent: boolean
}

export interface NewMagentoOrder {
  cartId: string
  promoCode?: string
  customer: NewOrderCustomer
  invoice: IInvoiceModel_V2
  shipping: NewOrderShippingData
  paymentMethod: PaymentTypes
  confirmation: OrderTerms
}

type CustomerValidationShape = ValidationShape<
  Omit<NewMagentoOrder, 'shipping' | 'paymentMethod'>
>

export function getCustomerValidation(): ObjectSchema<
  ValidationShape<NewOrderCustomer>
> {
  //-ignore
  return Yup.object().shape<ValidationShape<NewOrderCustomer>>({
    addressId: Yup.string().notRequired(),
    firstname: Yup.string().required('Името е задължително'),
    lastname: Yup.string().required('Фамилията е задължителна'),
    phone: getTelephoneValidation(),
    email: Yup.string()
      .email('Невалиден имейл адрес')
      .required('Имейлът е задължителен'),
    register: Yup.boolean(),
    //-ignore
    password: Yup.string().when('register', {
      is: true,
      then: () => getPasswordValidation(),
    }),
    //-ignore
    confirmPassword: Yup.string().when('register', {
      is: true,
      then: () => getConfirmPasswordValidation(),
    }),
  })
}

export function isCompanyInvoice(then: any): any {
  //-ignore
  return Yup.string().when('required', {
    is: true,
    then: () =>
      Yup.string().when('type', {
        is: 'company',
        then: () => then,
      }),
  })
}

export function isPersonalInvoice(then: any): any {
  //-ignore
  return Yup.string().when('required', {
    is: true,
    then: () =>
      Yup.string().when('type', {
        is: 'personal',
        then: () => then,
      }),
  })
}

export function getInvoiceValidation(): ObjectSchema<
  ValidationShape<IInvoiceModel_V2>
> {
  return Yup.object().shape<ValidationShape<IInvoiceModel_V2>>({
    required: Yup.boolean(),
    type: Yup.string().required('Типът на фактурата е задължителен'),
    company: isCompanyInvoice(
      Yup.string().required('Името на фирмата е задължително')
    ),
    mol: isCompanyInvoice(Yup.string().required('МОЛ е задължително')),
    eik: isCompanyInvoice(
      Yup.string()
        .required('ЕИК е задължително')
        .length(9, 'ЕИК трябва да е 9 цифри')
    ),
    ddsRegistration: Yup.boolean(),
    ddsNumber: isCompanyInvoice(
      Yup.string().when('ddsRegistration', {
        is: true,
        then: () => Yup.string().required('ДДС номерът е задължителен'),
      })
    ),
    address: isCompanyInvoice(
      Yup.string().required('Адресът на фирмата е задължителен')
    ),
    personName: isPersonalInvoice(
      Yup.string().required('Името на лицето е задължително')
    ),
    pin: isPersonalInvoice(
      Yup.string()
        .required('ЕГН е задължително')
        .length(10, 'ЕГН трябва да е 10 цифри')
    ),
  })
}

export function getShippingValidation(): ObjectSchema<
  ValidationShape<NewOrderShippingData>
> {
  return Yup.object().shape<ValidationShape<NewOrderShippingData>>({
    shippingMethod: Yup.string().required('Методът на доставка е задължителен'),
    option: Yup.string().required('Не може да се изчисли доставка'),
    //-ignore
    address: Yup.object()
      .when('shippingMethod', {
        is: (v: any) => v == DeliveryOptionOffice,
        then: () =>
          Yup.object().shape({
            city: getCityValidation(),
            postcode: getPostcodeValidation(),
            office: Yup.object()
              .nullable()
              .test(
                'office_is_required',
                'Офисът е задължителен',
                function (value: any) {
                  return value && !!value.value
                }
              ),
          }),
      })
      .when('shippingMethod', {
        is: (v: any) => v == DeliveryOptionAddress,
        then: () =>
          Yup.object().shape({
            city: getCityValidation(),
            postcode: getPostcodeValidation(),
            street: getStreetValidation(),
            streetNumber: getStreetNumberValidation(),
            quarter: getQuarterValidation(),
            note: getAddressNoteValidation(),
          }),
      })
      .when('shippingMethod', {
        is: (v: any) => v == DeliveryOptionByPartner,
        then: () =>
          Yup.object().shape({
            city: Yup.object().nullable().required('Градът е задължителен'),
            partner: Yup.string().required('Партньорът е задължителен'),
          }),
      })
      .when('shippingMethod', {
        is: (v: any) => v == DeliveryOptionByCarco,
        then: () =>
          Yup.object().shape({
            city: Yup.string().required('Градът е задължителен'),
            postcode: Yup.string().required('Партньорът е задължителен'),
            street: Yup.string()
              .max(300, 'Максимум 300 символа')
              .required('Адреса е задължителен'),
          }),
      }),
  }) as any
}

export interface CheckoutFormData extends Partial<NewMagentoOrder> {
  validationSteps: CheckoutFromSteps[]
}

export function getInitialFormData(): CheckoutFormData {
  return {
    cartId: '',
    promoCode: '',
    validationSteps: ['customer_details'],
    customer: {
      confirmPassword: '',
      email: '',
      firstname: '',
      lastname: '',
      password: '',
      phone: '',
      register: false,
      addressId: '',
    },
    invoice: {
      required: false,
      type: MagentoInvoiceType.Company,
      company: '',
      mol: '',
      eik: '',
      ddsRegistration: false,
      ddsNumber: '',
      address: '',
      personName: '',
      pin: '',
    },
    shipping: {
      option: '',
      shippingMethod: '',
      address: {
        city: '',
        postcode: '',
        street: '',
        office: '',
      },
    },
    paymentMethod: '',
    confirmation: {
      orderComment: '',
      termsConsent: false,
      privacyPolicyConsent: false,
      ageConsent: false,
    },
  }
}

export type CheckoutValidator = (v: CheckoutFormData) => FormErrors

export function getCheckoutValidator(): CheckoutValidator {
  const schema = Yup.object().shape({
    validationSteps: Yup.array().of(Yup.string()),
    //-ignore
    customer: Yup.object().when('validationSteps', {
      is: (validationSteps: CheckoutFromSteps[]) =>
        validationSteps.includes(CheckoutFromStep.customer_details),
      then: () => getCustomerValidation(),
    }),
    //-ignore
    invoice: Yup.object().when('validationSteps', {
      is: (validationSteps: CheckoutFromSteps[]) =>
        validationSteps.includes(CheckoutFromStep.customer_details),
      then: () => getInvoiceValidation(),
    }),
    //-ignore
    shipping: Yup.object().when('validationSteps', {
      is: (validationSteps: CheckoutFromSteps[]) =>
        validationSteps.includes(CheckoutFromStep.shipping_details),
      then: () => getShippingValidation(),
    }),
    //-ignore
    paymentMethod: Yup.string().when('validationSteps', {
      is: (validationSteps: CheckoutFromSteps[]) =>
        validationSteps.includes(CheckoutFromStep.payment_details),
      then: () => Yup.string().required('Методът на плащане е задължителен'),
    }),
    //-ignore
    confirmation: Yup.object().when('validationSteps', {
      is: (validationSteps: CheckoutFromSteps[]) =>
        validationSteps.includes(CheckoutFromStep.accept_terms),
      then: () =>
        Yup.object().shape<ValidationShape<OrderTerms>>({
          orderComment: Yup.string().max(300, 'Максимална дължина 300 символа'),
          ageConsent: Yup.boolean().oneOf([true], 'Полето е задължително'),
          termsConsent: Yup.boolean().oneOf([true], 'Полето е задължително'),
          privacyPolicyConsent: Yup.boolean().oneOf(
            [true],
            'Полето е задължително'
          ),
        }),
    }),
  })

  const resolver = yupResolver(schema)
  return (v: any): FormErrors => {
    if (true) {
      _consoleDebug(v)
      _consoleDebug(resolver(v))
    }

    return resolver(v)
  }
}

export function getMainAddress(c?: ICustomerModel): Address {
  let address: Address = {
    __typename: 'Address',
    cityId: undefined,
    note: undefined,
    office: undefined,
    officeId: undefined,
    postCode: '',
    quarterId: undefined,
    street: '',
    streetNumber: undefined,
    telephone: '',
    addressId: '',
    city: '',
    firstname: c?.firstname || '',
    lastname: c?.lastname || '',
  }

  if (!c) {
    return address
  }

  if (c?.addresses?.length) {
    if (c.defaultShipping) {
      return (
        c.addresses.find((a) => a.addressId === c.defaultShipping) || address
      )
    }

    return c.addresses[0]
  }

  return address
}
