import CarcoIconQuality from '@atom/Icons/CarcoIconQuality'
import React from 'react'
import WhyCarcoWidgetItem from './WhyCarcoWidgetItem'

interface Props {
  flex?: boolean
  title?: string
  smallTitle?: boolean
  description?: string
}

const GuaranteedQuality: React.FC<Props> = (props) => {
  return (
    <WhyCarcoWidgetItem
      {...props}
      title={props.title ?? 'Гарантирано качество'}
      description={props.description ?? 'Проверени части'}
      smallTitle={props.smallTitle}
      centered
      icon={<CarcoIconQuality className={'h-[40px] tablet:h-[60px]'} />}
    />
  )
}

export default GuaranteedQuality
