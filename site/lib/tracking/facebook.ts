import {
  AddToCartData,
  CartQuoteData,
  OrderData,
  ProductData,
  SearchData,
} from '@lib/tracking/tracking'

export type FacebookPixelsEvent =
  | 'PageView'
  | 'AddToCart'
  | 'AddToWishlist'
  | 'InitiateCheckout'
  | 'Purchase'
  | 'Search'
  | 'ViewContent'
  | 'revoke'
  | 'grant'

type EventProp = string | string[] | {} | undefined
type FbPixelData = Record<string, EventProp>

interface FbAddToCartData extends FbPixelData {
  content_ids: string[] // product sku
  content_type: 'product' //
  content_name: string // product name
  contents?: Record<string, {}> // unknown
  currency: string // currency
  value: string // product price
}

interface FbPurchaseProductData {
  id: string
  quantity: number
}
interface FbPurchaseData extends FbPixelData {
  currency: string // currency of the order
  value: string // order value
  contents: FbPurchaseProductData[]
  content_type: 'product'
}

interface FbProductViewData extends FbPixelData {
  content_name: string
  content_ids: string[] // products skus
  content_type: 'product'
  currency: string // currency of the order
  value: string // order value
}

interface FbInitiateCheckoutData extends FbPixelData {
  content_category?: string // products categories
  content_ids: string[] // products skus
  currency: string // currency of the order
  num_items: string // number of products
  value: string // order value
}

interface FbAddToWishlistData extends FbPixelData {
  content_category?: string
  content_name: string
  content_ids: string[]
  currency: string
  value: string
}

interface FbSearchData extends FbPixelData {
  search_string: string
}

declare global {
  interface Window {
    fbq: (
      t: 'track' | 'consent' | 'init',
      e: FacebookPixelsEvent | string,
      v?: FbPixelData
    ) => void
  }
}

export function pixelTrack(
  event: FacebookPixelsEvent,
  data?: FbPixelData
): void {
  try {
    if (typeof window !== 'undefined' && window.fbq && typeof window.fbq === 'function') {
      window.fbq('track', event, data)
    }
  } catch (error) {
    // Silently handle Facebook Pixel errors to prevent breaking the app
    console.warn('Facebook Pixel tracking error:', error)
  }
}

export function pixelPageView(): void {
  pixelTrack('PageView')
}

export function pixelAddToCart(data: AddToCartData): void {
  const _data: FbAddToCartData = {
    content_ids: [data.sku],
    content_type: 'product',
    content_name: data.name,
    currency: 'BGN',
    value: (data.value ?? data.price).toString(),
  }
  pixelTrack('AddToCart', _data)
}

export function pixelOrderComplete(data: OrderData): void {
  const _data: FbPurchaseData = {
    contents: data.products.map((p) => ({
      id: p.sku,
      quantity: p.quantity || 1,
    })),
    currency: data.currency ?? 'BGN',
    value: data.total.toString(),
    content_type: 'product',
  }
  pixelTrack('Purchase', _data)
}

export function pixelProductView(data: ProductData): void {
  const _data: FbProductViewData = {
    content_name: data.name,
    content_type: 'product',
    content_ids: [data.sku],
    currency: data.currency,
    value: data.price.toString(),
  }
  pixelTrack('ViewContent', _data)
}

export function pixelCheckoutStarted(data: CartQuoteData): void {
  const _data: FbInitiateCheckoutData = {
    content_ids: data.products.map((p) => p.sku),
    currency: data.currency,
    num_items: data.products.length.toString(),
    value: data.total.toString(),
  }
  pixelTrack('InitiateCheckout', _data)
}

export function pixelAddToWishlist(data: ProductData): void {
  const _data: FbAddToWishlistData = {
    content_name: data.name,
    content_ids: [data.sku],
    currency: data.currency,
    value: data.price.toString(),
  }
  pixelTrack('AddToWishlist', _data)
}

export function pixelSearch(data: SearchData): void {
  const _data: FbSearchData = {
    search_string: data.search_term,
  }
  pixelTrack('Search', _data)
}
