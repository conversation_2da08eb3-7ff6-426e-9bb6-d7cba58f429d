import { GraphQLClient, RequestOptions } from 'graphql-request'
import gql from 'graphql-tag'
export type Maybe<T> = T | null
export type InputMaybe<T> = Maybe<T>
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K]
}
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>
}
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>
}
export type MakeEmpty<
  T extends { [key: string]: unknown },
  K extends keyof T,
> = { [_ in K]?: never }
export type Incremental<T> =
  | T
  | {
      [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never
    }
type GraphQLClientRequestHeaders = RequestOptions['requestHeaders']
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string }
  String: { input: string; output: string }
  Boolean: { input: boolean; output: boolean }
  Int: { input: number; output: number }
  Float: { input: number; output: number }
  Int64: { input: any; output: any }
  Upload: { input: any; output: any }
}

export type Address = {
  __typename?: 'Address'
  addressId: Scalars['String']['output']
  city: Scalars['String']['output']
  cityId?: Maybe<Scalars['String']['output']>
  firstname: Scalars['String']['output']
  lastname: Scalars['String']['output']
  note?: Maybe<Scalars['String']['output']>
  office?: Maybe<Scalars['String']['output']>
  officeId?: Maybe<Scalars['String']['output']>
  postCode: Scalars['String']['output']
  quarterId?: Maybe<Scalars['String']['output']>
  street: Scalars['String']['output']
  streetNumber?: Maybe<Scalars['String']['output']>
  telephone: Scalars['String']['output']
}

export type AddressData = {
  cityId?: InputMaybe<Scalars['String']['input']>
  note?: InputMaybe<Scalars['String']['input']>
  officeId?: InputMaybe<Scalars['String']['input']>
  postCode?: InputMaybe<Scalars['String']['input']>
  quarterId?: InputMaybe<Scalars['String']['input']>
  street?: InputMaybe<Scalars['String']['input']>
  streetNumber?: InputMaybe<Scalars['String']['input']>
}

export type AddressInput = {
  addressId: Scalars['String']['input']
  city: Scalars['String']['input']
  cityId?: InputMaybe<Scalars['Int']['input']>
  firstname: Scalars['String']['input']
  lastname: Scalars['String']['input']
  note?: InputMaybe<Scalars['String']['input']>
  office?: InputMaybe<Scalars['String']['input']>
  officeId?: InputMaybe<Scalars['Int']['input']>
  postCode: Scalars['String']['input']
  quarterId?: InputMaybe<Scalars['Int']['input']>
  street: Scalars['String']['input']
  streetNumber?: InputMaybe<Scalars['String']['input']>
  telephone: Scalars['String']['input']
}

export type AddressUpdateResponse = {
  __typename?: 'AddressUpdateResponse'
  Customer?: Maybe<Customer>
  newAddress?: Maybe<Address>
}

export type BlogCategory = {
  __typename?: 'BlogCategory'
  children: Array<BlogCategory>
  id: Scalars['Int']['output']
  name: Scalars['String']['output']
  posts: Array<BlogPost>
  urlKey: Scalars['String']['output']
}

export type BlogPost = {
  __typename?: 'BlogPost'
  content: Scalars['String']['output']
  mainImageUrl: Scalars['String']['output']
  previewImageUrl: Scalars['String']['output']
  publishedAt: Scalars['String']['output']
  summary: Scalars['String']['output']
  title: Scalars['String']['output']
  urlKey: Scalars['String']['output']
}

export type BooleanWithMessage = {
  __typename?: 'BooleanWithMessage'
  message: Scalars['String']['output']
  value: Scalars['Boolean']['output']
}

export type BrandsContent = {
  __typename?: 'BrandsContent'
  bottomDescription: Scalars['String']['output']
  topDescription: Scalars['String']['output']
}

export type Breadcrumb = {
  __typename?: 'Breadcrumb'
  label: Scalars['String']['output']
  url_path: Scalars['String']['output']
}

export type CmsContent = {
  __typename?: 'CMSContent'
  identifier: Scalars['String']['output']
  rawContent: Scalars['String']['output']
  title: Scalars['String']['output']
}

export type CalculateTotalsInput = {
  promoCode?: InputMaybe<Scalars['String']['input']>
  shipping?: InputMaybe<ShippingDataInput>
}

export type CarBrand = {
  __typename?: 'CarBrand'
  brand_id: Scalars['Int']['output']
  image_url: Scalars['String']['output']
  url: Scalars['String']['output']
  value: Scalars['String']['output']
}

export type CarBuyoutData = {
  brand: Scalars['String']['input']
  city: Scalars['String']['input']
  comment: Scalars['String']['input']
  doors: Scalars['String']['input']
  email: Scalars['String']['input']
  engine: Scalars['String']['input']
  model: Scalars['String']['input']
  phone: Scalars['String']['input']
  price: Scalars['String']['input']
  transmission: Scalars['String']['input']
  year: Scalars['String']['input']
}

export type CarModel = {
  __typename?: 'CarModel'
  brand_id: Scalars['Int']['output']
  model_id: Scalars['Int']['output']
  products_count: Scalars['Int']['output']
  url_value: Scalars['String']['output']
  value: Scalars['String']['output']
}

export type CarModification = {
  __typename?: 'CarModification'
  model_id: Scalars['Int']['output']
  modification_id: Scalars['Int']['output']
  url_value: Scalars['String']['output']
  value: Scalars['String']['output']
}

export type CarsModels = {
  __typename?: 'CarsModels'
  brand: Scalars['String']['output']
  models: Array<CarModel>
}

export type Cart = {
  __typename?: 'Cart'
  couponCode?: Maybe<Scalars['String']['output']>
  customer: Scalars['String']['output']
  id: Scalars['String']['output']
  items: Array<CartItem>
  shippingMethod: ShippingMethodCode
  totals: CartTotals
}

export type CartItem = {
  __typename?: 'CartItem'
  id: Scalars['ID']['output']
  product: MagentoProduct
  qty: Scalars['Int']['output']
  totals?: Maybe<CartItemTotals>
}

export type CartItemTotals = {
  __typename?: 'CartItemTotals'
  base_price: Total
  discount: Total
  row_total: Total
  sale_price: Total
}

export type CartTotals = {
  __typename?: 'CartTotals'
  discount?: Maybe<Total>
  grand_total: Total
  shipping?: Maybe<Total>
  subtotal: Total
}

export type CatalogPage = {
  __typename?: 'CatalogPage'
  applied_filters: Array<MagentoAppliedFilter>
  category: MagentoCategory
  filters: Array<MagentoFilter>
  page_info: MagentoPageInfo
  products: Array<MagentoProduct>
}

export enum CategoryType {
  MagentoCategory = 'MAGENTO_CATEGORY',
  SplashPage = 'SPLASH_PAGE',
}

export type City = {
  __typename?: 'City'
  cityType: CityType
  id: Scalars['Int']['output']
  name: Scalars['String']['output']
  nameEn: Scalars['String']['output']
  offices: Array<Office>
  postCode: Scalars['String']['output']
  quarters: Array<Quarter>
}

export enum CityType {
  Econt = 'ECONT',
  Speedy = 'SPEEDY',
}

export type ContactColumn = {
  __typename?: 'ContactColumn'
  contact: StoreContactInfo
  title: Scalars['String']['output']
}

export type ContactInput = {
  email: Scalars['String']['input']
  message: Scalars['String']['input']
  name: Scalars['String']['input']
  subject: Scalars['String']['input']
  telephone: Scalars['String']['input']
}

export enum ContentType {
  Block = 'BLOCK',
  Page = 'PAGE',
  Widget = 'WIDGET',
}

export type CookieCompliance = {
  __typename?: 'CookieCompliance'
  clarityID: Scalars['String']['output']
  cookieGroups: Array<Maybe<CookieGroup>>
  fbPixelID: Scalars['String']['output']
  gdprPopupContent: Scalars['String']['output']
  gtagID: Scalars['String']['output']
}

export type CookieGroup = {
  __typename?: 'CookieGroup'
  cookiePatterns?: Maybe<Array<Scalars['String']['output']>>
  grants?: Maybe<Array<Scalars['String']['output']>>
  isEssential: Scalars['Boolean']['output']
  text: Scalars['String']['output']
  title: Scalars['String']['output']
  vendors: Array<Scalars['String']['output']>
}

export type Customer = {
  __typename?: 'Customer'
  addresses: Array<Address>
  authToken: Token
  defaultBilling: Scalars['Int64']['output']
  defaultShipping: Scalars['Int64']['output']
  email: Scalars['String']['output']
  firstname: Scalars['String']['output']
  id: Scalars['Int64']['output']
  invoice: MagentoInvoice
  isSubscribed: Scalars['Boolean']['output']
  lastname: Scalars['String']['output']
  orders?: Maybe<Array<Order>>
  telephone: Scalars['String']['output']
}

export type CustomerOrdersArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>
  page?: InputMaybe<Scalars['Int']['input']>
}

export type CustomerData = {
  email: Scalars['String']['input']
  firstname: Scalars['String']['input']
  lastname: Scalars['String']['input']
  phone: Scalars['String']['input']
}

export type CustomerRegistrationData = {
  email: Scalars['String']['input']
  firstname: Scalars['String']['input']
  lastname: Scalars['String']['input']
  newsletterSubscribe: Scalars['Boolean']['input']
  password: Scalars['String']['input']
  passwordConfirm: Scalars['String']['input']
}

export enum EntityType {
  BlogCategory = 'BLOG_CATEGORY',
  BlogPost = 'BLOG_POST',
  Category = 'CATEGORY',
  CmsPage = 'CMS_PAGE',
  Product = 'PRODUCT',
}

export type ErrorReportInput = {
  categoryError: Scalars['Boolean']['input']
  funcError: Scalars['Boolean']['input']
  imageError: Scalars['Boolean']['input']
  message: Scalars['String']['input']
  productSku: Scalars['String']['input']
}

export type FastOrderInput = {
  email: Scalars['String']['input']
  name: Scalars['String']['input']
  phone: Scalars['String']['input']
  sku: Scalars['String']['input']
}

export enum FilterRenderType {
  Grid = 'GRID',
  Links = 'LINKS',
  LinkButton = 'LINK_BUTTON',
  List = 'LIST',
  ListLinks = 'LIST_LINKS',
  Range = 'RANGE',
}

export type Footer = {
  __typename?: 'Footer'
  aboutText: Scalars['String']['output']
  columns: Array<FooterColumn>
  newsletter: Newsletter
  paymentMethods: Array<Scalars['String']['output']>
}

export type FooterColumn = ContactColumn | LinksColumn

export type GeoLocation = {
  __typename?: 'GeoLocation'
  lat: Scalars['Float']['output']
  lng: Scalars['Float']['output']
}

export type HeadIncludes = {
  __typename?: 'HeadIncludes'
  meta: Array<MetaTags>
  scripts: Array<JsScript>
}

export type InvoiceInput = {
  address?: InputMaybe<Scalars['String']['input']>
  company?: InputMaybe<Scalars['String']['input']>
  ddsNumber?: InputMaybe<Scalars['String']['input']>
  ddsRegistration?: InputMaybe<Scalars['Boolean']['input']>
  eik?: InputMaybe<Scalars['String']['input']>
  mol?: InputMaybe<Scalars['String']['input']>
  personName?: InputMaybe<Scalars['String']['input']>
  pin?: InputMaybe<Scalars['String']['input']>
  required: Scalars['Boolean']['input']
  type?: InputMaybe<MagentoInvoiceType>
}

export type JsScript = {
  __typename?: 'JsScript'
  async: Scalars['Boolean']['output']
  content: Scalars['String']['output']
  defer: Scalars['Boolean']['output']
  src: Scalars['String']['output']
}

export type Link = {
  __typename?: 'Link'
  text: Scalars['String']['output']
  url: Scalars['String']['output']
}

export type LinksColumn = {
  __typename?: 'LinksColumn'
  links: Array<Link>
  title: Scalars['String']['output']
}

export type MagentoAppliedFilter = {
  __typename?: 'MagentoAppliedFilter'
  attribute_code: Scalars['String']['output']
  label: Scalars['String']['output']
  request_value: Scalars['String']['output']
  request_var: Scalars['String']['output']
  value: Scalars['String']['output']
}

export type MagentoBlog = {
  __typename?: 'MagentoBlog'
  currentPage: Scalars['Int']['output']
  posts: Array<BlogPost>
  totalPages: Scalars['Int']['output']
}

export type MagentoCategory = {
  __typename?: 'MagentoCategory'
  category_type: CategoryType
  children: Array<MagentoCategory>
  description: Scalars['String']['output']
  id: Scalars['Int']['output']
  name: Scalars['String']['output']
  products_count: Scalars['Int']['output']
  thumbnail: Scalars['String']['output']
  title: Scalars['String']['output']
  url_path: Scalars['String']['output']
}

export type MagentoFilter = {
  __typename?: 'MagentoFilter'
  attribute_code: Scalars['String']['output']
  label: Scalars['String']['output']
  options: Array<MagentoOption>
  position: Scalars['Int']['output']
  request_var: Scalars['String']['output']
  type: FilterRenderType
}

export type MagentoInvoice = {
  __typename?: 'MagentoInvoice'
  address: Scalars['String']['output']
  company: Scalars['String']['output']
  ddsNumber: Scalars['String']['output']
  ddsRegistration: Scalars['Boolean']['output']
  eik: Scalars['String']['output']
  mol: Scalars['String']['output']
  personName: Scalars['String']['output']
  pin: Scalars['String']['output']
  required: Scalars['Boolean']['output']
  type: MagentoInvoiceType
}

export enum MagentoInvoiceType {
  Company = 'company',
  Personal = 'personal',
}

export type MagentoOption = {
  __typename?: 'MagentoOption'
  id: Scalars['String']['output']
  label: Scalars['String']['output']
  value: Scalars['String']['output']
}

export type MagentoPageData = {
  __typename?: 'MagentoPageData'
  breadcrumbs?: Maybe<Array<Breadcrumb>>
  page?: Maybe<PageData>
  redirect?: Maybe<PageRedirect>
}

export type MagentoPageInfo = {
  __typename?: 'MagentoPageInfo'
  available_page_sizes: Array<Scalars['Int']['output']>
  available_sorts: Array<MagentoOption>
  current_page: Scalars['Int']['output']
  page_size: Scalars['Int']['output']
  products_count: Scalars['Int']['output']
  sort_direction: Scalars['String']['output']
  sort_field: Scalars['String']['output']
  total_pages: Scalars['Int']['output']
}

export type MagentoPageStaticContent = {
  __typename?: 'MagentoPageStaticContent'
  contact: StoreContactInfo
  cookieCompliance: CookieCompliance
  footer: Footer
  menu: MegaMenu
  miscellaneous: HeadIncludes
  settings?: Maybe<Array<Settings>>
}

export type MagentoProduct = {
  __typename?: 'MagentoProduct'
  additional: Array<ProductAttribute>
  attribute_set_id: Scalars['Int']['output']
  brand: CarBrand
  description: Scalars['String']['output']
  freeAfterXSeconds: Scalars['Int']['output']
  gallery: Array<ProductImage>
  id: Scalars['Int']['output']
  image: Scalars['String']['output']
  model: CarModel
  modification: CarModification
  name: Scalars['String']['output']
  price: Scalars['Float']['output']
  sku: Scalars['String']['output']
  special_price: Scalars['Float']['output']
  special_price_to_date: Scalars['String']['output']
  status: ProductStatus
  tierBrand: Scalars['String']['output']
  url_key: Scalars['String']['output']
  years: Scalars['String']['output']
}

export type MagentoStockStatus = {
  __typename?: 'MagentoStockStatus'
  freeAfterXSeconds: Scalars['Int']['output']
  sku: Scalars['String']['output']
  status: ProductStatus
}

export type MapValue = {
  __typename?: 'MapValue'
  key: Scalars['String']['output']
  value: Scalars['String']['output']
}

export type MegaMenu = {
  __typename?: 'MegaMenu'
  items: Array<MegaMenuItem>
}

export type MegaMenuItem = {
  __typename?: 'MegaMenuItem'
  image_url?: Maybe<Scalars['String']['output']>
  items?: Maybe<Array<MegaMenuItem>>
  label: Scalars['String']['output']
  layout: MenuItemLayout
  promo?: Maybe<MegaMenuPromo>
  url: Scalars['String']['output']
}

export type MegaMenuPromo = {
  __typename?: 'MegaMenuPromo'
  promo_percentage: Scalars['String']['output']
  text: Scalars['String']['output']
  valid_until: Scalars['String']['output']
}

export enum MenuItemLayout {
  Grid = 'GRID',
  List = 'LIST',
}

export type MetaTags = {
  __typename?: 'MetaTags'
  content: Scalars['String']['output']
  name: Scalars['String']['output']
}

export type Money = {
  __typename?: 'Money'
  currency: Scalars['String']['output']
  value: Scalars['Float']['output']
}

export type Mutation = {
  __typename?: 'Mutation'
  addToCart: Cart
  applyCouponToCart: Cart
  calculateShipping: ShippingMethodTotal
  collectTotals: Cart
  deleteAddress?: Maybe<Customer>
  fastOrder: BooleanWithMessage
  forgotPassword: Scalars['String']['output']
  loginCustomer: Scalars['String']['output']
  logoutCustomer: Scalars['Boolean']['output']
  registerCustomer: Scalars['String']['output']
  removeFromCart: Cart
  requestDeleteCustomer?: Maybe<BooleanWithMessage>
  resetPassword: Scalars['Boolean']['output']
  sendCarBuyoutRequest: BooleanWithMessage
  sendContactMessage: BooleanWithMessage
  sendErrorReport: BooleanWithMessage
  submitOrder: NewOrderResult
  subscribeToNewsletter: BooleanWithMessage
  updateAddress?: Maybe<Customer>
  updateInvoice?: Maybe<Customer>
  updateNewsletterSubscription?: Maybe<BooleanWithMessage>
  updatePassword: Scalars['Boolean']['output']
  updateProfile?: Maybe<Customer>
}

export type MutationAddToCartArgs = {
  cartId: Scalars['String']['input']
  qty: Scalars['Int']['input']
  sku: Scalars['String']['input']
}

export type MutationApplyCouponToCartArgs = {
  cartId: Scalars['String']['input']
  couponCode: Scalars['String']['input']
}

export type MutationCalculateShippingArgs = {
  cartId: Scalars['String']['input']
  input?: InputMaybe<ShippingDataInput>
}

export type MutationCollectTotalsArgs = {
  cartId: Scalars['String']['input']
  input?: InputMaybe<CalculateTotalsInput>
}

export type MutationDeleteAddressArgs = {
  addressId: Scalars['String']['input']
}

export type MutationFastOrderArgs = {
  data: FastOrderInput
}

export type MutationForgotPasswordArgs = {
  email: Scalars['String']['input']
}

export type MutationLoginCustomerArgs = {
  email: Scalars['String']['input']
  password: Scalars['String']['input']
}

export type MutationRegisterCustomerArgs = {
  data: CustomerRegistrationData
}

export type MutationRemoveFromCartArgs = {
  cartId: Scalars['String']['input']
  sku: Scalars['String']['input']
}

export type MutationResetPasswordArgs = {
  id: Scalars['String']['input']
  password: Scalars['String']['input']
  passwordConfirm: Scalars['String']['input']
  token: Scalars['String']['input']
}

export type MutationSendCarBuyoutRequestArgs = {
  data: CarBuyoutData
  files?: InputMaybe<Array<UploadFile>>
}

export type MutationSendContactMessageArgs = {
  input: ContactInput
}

export type MutationSendErrorReportArgs = {
  data: ErrorReportInput
}

export type MutationSubmitOrderArgs = {
  cartId: Scalars['String']['input']
  input: SubmitOrderInput
}

export type MutationSubscribeToNewsletterArgs = {
  email: Scalars['String']['input']
}

export type MutationUpdateAddressArgs = {
  address: AddressInput
}

export type MutationUpdateInvoiceArgs = {
  invoice: InvoiceInput
}

export type MutationUpdateNewsletterSubscriptionArgs = {
  isSubscribed: Scalars['Boolean']['input']
}

export type MutationUpdatePasswordArgs = {
  newPassword: Scalars['String']['input']
  oldPassword: Scalars['String']['input']
}

export type MutationUpdateProfileArgs = {
  firstname: Scalars['String']['input']
  lastname: Scalars['String']['input']
}

export type NewOrderResult = {
  __typename?: 'NewOrderResult'
  order?: Maybe<Order>
  orderNumber?: Maybe<Scalars['String']['output']>
  paymentMethod?: Maybe<PaymentMethod>
  redirect?: Maybe<OrderRedirect>
  shippingMethod?: Maybe<ShippingMethod>
  status?: Maybe<Scalars['String']['output']>
}

export type Newsletter = {
  __typename?: 'Newsletter'
  checkbox_agree_text: Scalars['String']['output']
  text: Scalars['String']['output']
}

export enum NoticeType {
  Bar = 'bar',
  Box = 'box',
}

export type Office = {
  __typename?: 'Office'
  address: Scalars['String']['output']
  addressEn: Scalars['String']['output']
  cityId: Scalars['Int']['output']
  code: Scalars['String']['output']
  id: Scalars['Int']['output']
  location: GeoLocation
  name: Scalars['String']['output']
  nameEn: Scalars['String']['output']
}

export type OpenGraph = {
  __typename?: 'OpenGraph'
  currency: Scalars['String']['output']
  description: Scalars['String']['output']
  image: Scalars['String']['output']
  ogType: OpenGraphType
  price: Scalars['String']['output']
  site_name: Scalars['String']['output']
  title: Scalars['String']['output']
  url: Scalars['String']['output']
}

export enum OpenGraphType {
  Article = 'article',
  Catalog = 'catalog',
  Product = 'product',
  Website = 'website',
}

export type Order = {
  __typename?: 'Order'
  address: Address
  createAt: Scalars['String']['output']
  incrementId: Scalars['String']['output']
  items?: Maybe<Array<CartItem>>
  note: Scalars['String']['output']
  paymentMethod: PaymentMethod
  shippingMethod: ShippingMethod
  status: OrderStatus
  totals: Array<Total>
}

export type OrderRedirect = {
  __typename?: 'OrderRedirect'
  data?: Maybe<Array<MapValue>>
  url: Scalars['String']['output']
}

export type OrderStatus = {
  __typename?: 'OrderStatus'
  code: Scalars['String']['output']
  label: Scalars['String']['output']
}

export type PageData =
  | CmsContent
  | CatalogPage
  | ProductPage
  | SearchPage
  | SplashPage

export type PageRedirect = {
  __typename?: 'PageRedirect'
  code: Scalars['Int']['output']
  url_path: Scalars['String']['output']
}

export type PaymentMethod = {
  __typename?: 'PaymentMethod'
  additional?: Maybe<Array<MapValue>>
  code: Scalars['String']['output']
  title: Scalars['String']['output']
}

export enum Position {
  BottomLeft = 'bottom_left',
  BottomRight = 'bottom_right',
  TopLeft = 'top_left',
  TopRight = 'top_right',
}

export type ProductAttribute = {
  __typename?: 'ProductAttribute'
  attribute_code: Scalars['String']['output']
  label: Scalars['String']['output']
  value: Scalars['String']['output']
}

export type ProductImage = {
  __typename?: 'ProductImage'
  label: Scalars['String']['output']
  position: Scalars['Int']['output']
  url: Scalars['String']['output']
}

export type ProductPage = {
  __typename?: 'ProductPage'
  product: MagentoProduct
  related: Array<MagentoProduct>
}

export enum ProductRelationType {
  Replacement = 'REPLACEMENT',
  Upsell = 'UPSELL',
}

export enum ProductStatus {
  Available = 'AVAILABLE',
  OutOfStock = 'OUT_OF_STOCK',
  Reserved = 'RESERVED',
}

export type Quarter = {
  __typename?: 'Quarter'
  id: Scalars['Int']['output']
  name: Scalars['String']['output']
  nameEn: Scalars['String']['output']
}

export type Query = {
  __typename?: 'Query'
  brands: Array<CarBrand>
  brandsCMS: BrandsContent
  carModels: Array<CarModel>
  carModifications: Array<CarModification>
  carsBuyout: Array<CarsModels>
  categoryTree: Array<MagentoCategory>
  getBlogCategories: Array<BlogCategory>
  getBlogCategory: BlogCategory
  getBlogPost: BlogPost
  getBlogPosts: MagentoBlog
  getCart: Cart
  getCmsContent: CmsContent
  getCustomer?: Maybe<Customer>
  getCustomerOrder?: Maybe<Order>
  getEmptyCart: Cart
  getFeaturedBlogPost: BlogPost
  getPageMetadata: StoreMetaData
  getPartners: Array<City>
  getProduct: MagentoProduct
  getProductCollection: Array<MagentoProduct>
  getRelated: Array<MagentoProduct>
  getShippingCities: Array<City>
  getShippingMethods: Array<ShippingMethod>
  getStockStatus: Array<Maybe<MagentoStockStatus>>
  mostViewedCategory: Array<MagentoCategory>
  pageData: MagentoPageData
  search: SearchResults
  searchPage: SearchPage
  staticContent: MagentoPageStaticContent
}

export type QueryCarModelsArgs = {
  brandId?: InputMaybe<Scalars['Int']['input']>
}

export type QueryCarModificationsArgs = {
  modelId?: InputMaybe<Scalars['Int']['input']>
}

export type QueryCategoryTreeArgs = {
  parentId?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetBlogCategoryArgs = {
  page?: InputMaybe<Scalars['Int']['input']>
  urlKey: Scalars['String']['input']
}

export type QueryGetBlogPostArgs = {
  identifier: Scalars['String']['input']
}

export type QueryGetBlogPostsArgs = {
  page: Scalars['Int']['input']
}

export type QueryGetCartArgs = {
  cartId: Scalars['String']['input']
}

export type QueryGetCmsContentArgs = {
  contentType: ContentType
  identifier: Scalars['String']['input']
}

export type QueryGetCustomerOrderArgs = {
  incId: Scalars['String']['input']
}

export type QueryGetPageMetadataArgs = {
  entityType?: InputMaybe<EntityType>
  query?: InputMaybe<Array<InputMaybe<QueryParam>>>
  url: Scalars['String']['input']
}

export type QueryGetProductArgs = {
  sku: Scalars['String']['input']
}

export type QueryGetProductCollectionArgs = {
  skus?: InputMaybe<Array<Scalars['String']['input']>>
}

export type QueryGetRelatedArgs = {
  relationType?: InputMaybe<ProductRelationType>
  sku: Scalars['String']['input']
}

export type QueryGetShippingCitiesArgs = {
  cityType: CityType
  filterCityId?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetStockStatusArgs = {
  skus?: InputMaybe<Array<Scalars['String']['input']>>
}

export type QueryPageDataArgs = {
  query?: InputMaybe<Array<InputMaybe<QueryParam>>>
  url: Scalars['String']['input']
}

export type QuerySearchArgs = {
  searchQuery: Scalars['String']['input']
}

export type QuerySearchPageArgs = {
  query?: InputMaybe<Array<InputMaybe<QueryParam>>>
  searchQuery: Scalars['String']['input']
}

export type QueryParam = {
  name: Scalars['String']['input']
  value: Scalars['String']['input']
}

export type SearchPage = {
  __typename?: 'SearchPage'
  applied_filters: Array<MagentoAppliedFilter>
  category: MagentoCategory
  filters: Array<MagentoFilter>
  page_info: MagentoPageInfo
  products: Array<MagentoProduct>
  title: Scalars['String']['output']
  total_count: Scalars['Int']['output']
}

export type SearchResults = {
  __typename?: 'SearchResults'
  categories: Array<MagentoCategory>
  products: Array<MagentoProduct>
  suggestions: Array<Scalars['String']['output']>
  total_count: Scalars['Int']['output']
}

export type SelectOption = {
  __typename?: 'SelectOption'
  label?: Maybe<Scalars['String']['output']>
  value: Scalars['String']['output']
}

export type Settings = {
  __typename?: 'Settings'
  name: Scalars['String']['output']
  value: Scalars['String']['output']
}

export type ShippingDataInput = {
  address: AddressData
  customer: CustomerData
  shippingMethod?: InputMaybe<ShippingMethodCode>
}

export type ShippingMethod = {
  __typename?: 'ShippingMethod'
  additional?: Maybe<Array<Maybe<MapValue>>>
  code: ShippingMethodCode
  name: Scalars['String']['output']
  total?: Maybe<ShippingMethodTotal>
}

export enum ShippingMethodCode {
  CarcoTransport = 'CARCO_TRANSPORT',
  EcontToDoor = 'ECONT_TO_DOOR',
  EcontToOffice = 'ECONT_TO_OFFICE',
  OwnTransport = 'OWN_TRANSPORT',
  PartnerPickup = 'PARTNER_PICKUP',
  SpeedyToDoor = 'SPEEDY_TO_DOOR',
  SpeedyToOffice = 'SPEEDY_TO_OFFICE',
}

export type ShippingMethodTotal = {
  __typename?: 'ShippingMethodTotal'
  label: Scalars['String']['output']
  price: Money
}

export type SocialLinks = {
  __typename?: 'SocialLinks'
  facebook?: Maybe<Scalars['String']['output']>
  instagram?: Maybe<Scalars['String']['output']>
  linkedin?: Maybe<Scalars['String']['output']>
  youtube?: Maybe<Scalars['String']['output']>
}

export type SplashPage = {
  __typename?: 'SplashPage'
  applied_filters: Array<MagentoAppliedFilter>
  category: MagentoCategory
  filters: Array<MagentoFilter>
  page_info: MagentoPageInfo
  products: Array<MagentoProduct>
}

export type StoreContactInfo = {
  __typename?: 'StoreContactInfo'
  address: Scalars['String']['output']
  email: Scalars['String']['output']
  socials: SocialLinks
  telephone: Scalars['String']['output']
}

export type StoreMetaData = {
  __typename?: 'StoreMetaData'
  canonical_url: Scalars['String']['output']
  description: Scalars['String']['output']
  entityType?: Maybe<EntityType>
  keywords: Scalars['String']['output']
  next_url: Scalars['String']['output']
  og?: Maybe<OpenGraph>
  prev_url: Scalars['String']['output']
  robots: Scalars['String']['output']
  title: Scalars['String']['output']
}

export type SubmitOrderInput = {
  invoice: InvoiceInput
  note: Scalars['String']['input']
  paymentMethod: Scalars['String']['input']
  promoCode: Scalars['String']['input']
  shipping: ShippingDataInput
}

export type Token = {
  __typename?: 'Token'
  expires: Scalars['String']['output']
  token: Scalars['String']['output']
}

export type Total = {
  __typename?: 'Total'
  amount: Money
  code: Scalars['String']['output']
  label: Scalars['String']['output']
}

export type UploadFile = {
  file: Scalars['Upload']['input']
  id: Scalars['Int']['input']
}

export type GetCurrentProductStatusQueryVariables = Exact<{
  skus: Array<Scalars['String']['input']> | Scalars['String']['input']
}>

export type GetCurrentProductStatusQuery = {
  __typename?: 'Query'
  getStockStatus: Array<{
    __typename?: 'MagentoStockStatus'
    sku: string
    status: ProductStatus
  } | null>
}

export type MakeFastOrderMutationVariables = Exact<{
  data: FastOrderInput
}>

export type MakeFastOrderMutation = {
  __typename?: 'Mutation'
  fastOrder: {
    __typename?: 'BooleanWithMessage'
    message: string
    value: boolean
  }
}

export type SendErrorReportMutationVariables = Exact<{
  data: ErrorReportInput
}>

export type SendErrorReportMutation = {
  __typename?: 'Mutation'
  sendErrorReport: {
    __typename?: 'BooleanWithMessage'
    message: string
    value: boolean
  }
}

export type BlogPostPageDataFragment = {
  __typename?: 'BlogPost'
  urlKey: string
  title: string
  content: string
  mainImageUrl: string
  publishedAt: string
}

export type GetBlogPostQueryVariables = Exact<{
  identifier: Scalars['String']['input']
}>

export type GetBlogPostQuery = {
  __typename?: 'Query'
  getBlogPost: {
    __typename?: 'BlogPost'
    urlKey: string
    title: string
    content: string
    mainImageUrl: string
    publishedAt: string
  }
}

export type PostPreviewFragment = {
  __typename?: 'BlogPost'
  urlKey: string
  title: string
  summary: string
  previewImageUrl: string
  publishedAt: string
}

export type MagentoBlogPageDataFragment = {
  __typename?: 'MagentoBlog'
  currentPage: number
  totalPages: number
  posts: Array<{
    __typename?: 'BlogPost'
    urlKey: string
    title: string
    summary: string
    previewImageUrl: string
    publishedAt: string
  }>
}

export type GetBlogPostsQueryVariables = Exact<{
  page: Scalars['Int']['input']
}>

export type GetBlogPostsQuery = {
  __typename?: 'Query'
  getBlogPosts: {
    __typename?: 'MagentoBlog'
    currentPage: number
    totalPages: number
    posts: Array<{
      __typename?: 'BlogPost'
      urlKey: string
      title: string
      summary: string
      previewImageUrl: string
      publishedAt: string
    }>
  }
}

export type GetFeaturedBlogPostQueryVariables = Exact<{ [key: string]: never }>

export type GetFeaturedBlogPostQuery = {
  __typename?: 'Query'
  getFeaturedBlogPost: {
    __typename?: 'BlogPost'
    urlKey: string
    title: string
    summary: string
    previewImageUrl: string
    publishedAt: string
  }
}

export type GetSearchResultsPageQueryVariables = Exact<{
  term: Scalars['String']['input']
  query?: InputMaybe<Array<InputMaybe<QueryParam>> | InputMaybe<QueryParam>>
}>

export type GetSearchResultsPageQuery = {
  __typename?: 'Query'
  searchPage: {
    __typename?: 'SearchPage'
    title: string
    total_count: number
    category: {
      __typename?: 'MagentoCategory'
      id: number
      name: string
      title: string
      url_path: string
      thumbnail: string
    }
    applied_filters: Array<{
      __typename?: 'MagentoAppliedFilter'
      attribute_code: string
      label: string
      value: string
      request_var: string
      request_value: string
    }>
    filters: Array<{
      __typename?: 'MagentoFilter'
      label: string
      attribute_code: string
      type: FilterRenderType
      position: number
      request_var: string
      options: Array<{
        __typename?: 'MagentoOption'
        label: string
        value: string
        id: string
      }>
    }>
    page_info: {
      __typename?: 'MagentoPageInfo'
      available_page_sizes: Array<number>
      sort_field: string
      sort_direction: string
      page_size: number
      products_count: number
      current_page: number
      total_pages: number
      available_sorts: Array<{
        __typename?: 'MagentoOption'
        label: string
        value: string
      }>
    }
    products: Array<{
      __typename?: 'MagentoProduct'
      id: number
      attribute_set_id: number
      name: string
      sku: string
      url_key: string
      price: number
      special_price: number
      tierBrand: string
      years: string
      image: string
      status: ProductStatus
      brand: {
        __typename?: 'CarBrand'
        value: string
        image_url: string
        url: string
      }
    }>
  }
}

export type CrateNewOrderMutationVariables = Exact<{
  cartId: Scalars['String']['input']
  order: SubmitOrderInput
}>

export type CrateNewOrderMutation = {
  __typename?: 'Mutation'
  submitOrder: {
    __typename?: 'NewOrderResult'
    orderNumber?: string | null
    status?: string | null
    paymentMethod?: {
      __typename?: 'PaymentMethod'
      code: string
      title: string
      additional?: Array<{
        __typename?: 'MapValue'
        key: string
        value: string
      }> | null
    } | null
    redirect?: {
      __typename?: 'OrderRedirect'
      url: string
      data?: Array<{
        __typename?: 'MapValue'
        key: string
        value: string
      }> | null
    } | null
    shippingMethod?: {
      __typename?: 'ShippingMethod'
      name: string
      code: ShippingMethodCode
      total?: {
        __typename?: 'ShippingMethodTotal'
        label: string
        price: { __typename?: 'Money'; currency: string; value: number }
      } | null
      additional?: Array<{
        __typename?: 'MapValue'
        key: string
        value: string
      } | null> | null
    } | null
    order?: {
      __typename?: 'Order'
      incrementId: string
      items?: Array<{
        __typename?: 'CartItem'
        qty: number
        product: { __typename?: 'MagentoProduct'; sku: string; name: string }
        totals?: {
          __typename?: 'CartItemTotals'
          base_price: {
            __typename?: 'Total'
            code: string
            label: string
            amount: { __typename?: 'Money'; currency: string; value: number }
          }
          row_total: {
            __typename?: 'Total'
            code: string
            label: string
            amount: { __typename?: 'Money'; currency: string; value: number }
          }
        } | null
      }> | null
      totals: Array<{
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }>
    } | null
  }
}

export type ShippingTotalFragment = {
  __typename?: 'ShippingMethodTotal'
  label: string
  price: { __typename?: 'Money'; currency: string; value: number }
}

export type MagentoShippingMethodFragment = {
  __typename?: 'ShippingMethod'
  name: string
  code: ShippingMethodCode
  total?: {
    __typename?: 'ShippingMethodTotal'
    label: string
    price: { __typename?: 'Money'; currency: string; value: number }
  } | null
  additional?: Array<{
    __typename?: 'MapValue'
    key: string
    value: string
  } | null> | null
}

export type GetShippingMethodsQueryVariables = Exact<{ [key: string]: never }>

export type GetShippingMethodsQuery = {
  __typename?: 'Query'
  getShippingMethods: Array<{
    __typename?: 'ShippingMethod'
    name: string
    code: ShippingMethodCode
    total?: {
      __typename?: 'ShippingMethodTotal'
      label: string
      price: { __typename?: 'Money'; currency: string; value: number }
    } | null
    additional?: Array<{
      __typename?: 'MapValue'
      key: string
      value: string
    } | null> | null
  }>
}

export type CalculateShippingMutationVariables = Exact<{
  cartId: Scalars['String']['input']
  input: ShippingDataInput
}>

export type CalculateShippingMutation = {
  __typename?: 'Mutation'
  calculateShipping: {
    __typename?: 'ShippingMethodTotal'
    label: string
    price: { __typename?: 'Money'; currency: string; value: number }
  }
}

export type ShippingCityFragment = {
  __typename?: 'City'
  id: number
  name: string
  nameEn: string
  postCode: string
}

export type ShippingQuarterFragment = {
  __typename?: 'Quarter'
  id: number
  name: string
  nameEn: string
}

export type ShippingOfficeFragment = {
  __typename?: 'Office'
  id: number
  code: string
  name: string
  nameEn: string
  address: string
  addressEn: string
}

export type GetCitiesQueryVariables = Exact<{
  cityType: CityType
}>

export type GetCitiesQuery = {
  __typename?: 'Query'
  getShippingCities: Array<{
    __typename?: 'City'
    id: number
    name: string
    nameEn: string
    postCode: string
  }>
}

export type GetEcontCityQueryVariables = Exact<{
  cityType: CityType
  filterCityId: Scalars['Int']['input']
}>

export type GetEcontCityQuery = {
  __typename?: 'Query'
  getShippingCities: Array<{
    __typename?: 'City'
    id: number
    name: string
    nameEn: string
    postCode: string
    offices: Array<{
      __typename?: 'Office'
      id: number
      code: string
      name: string
      nameEn: string
      address: string
      addressEn: string
    }>
    quarters: Array<{
      __typename?: 'Quarter'
      id: number
      name: string
      nameEn: string
    }>
  }>
}

export type CollectTotalsMutationVariables = Exact<{
  cartId: Scalars['String']['input']
  promoCode: Scalars['String']['input']
  address: AddressData
  customer: CustomerData
  shippingMethod?: InputMaybe<ShippingMethodCode>
}>

export type CollectTotalsMutation = {
  __typename?: 'Mutation'
  collectTotals: {
    __typename?: 'Cart'
    id: string
    customer: string
    couponCode?: string | null
    shippingMethod: ShippingMethodCode
    totals: {
      __typename?: 'CartTotals'
      grand_total: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      subtotal: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      shipping?: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      } | null
      discount?: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      } | null
    }
    items: Array<{
      __typename?: 'CartItem'
      id: string
      qty: number
      product: {
        __typename?: 'MagentoProduct'
        name: string
        sku: string
        image: string
        url_key: string
      }
      totals?: {
        __typename?: 'CartItemTotals'
        base_price: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        sale_price: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        discount: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        row_total: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
      } | null
    }>
  }
}

export type SendContactUsMutationVariables = Exact<{
  data: ContactInput
}>

export type SendContactUsMutation = {
  __typename?: 'Mutation'
  sendContactMessage: {
    __typename?: 'BooleanWithMessage'
    value: boolean
    message: string
  }
}

export type RequestAccountDeletionMutationVariables = Exact<{
  [key: string]: never
}>

export type RequestAccountDeletionMutation = {
  __typename?: 'Mutation'
  requestDeleteCustomer?: {
    __typename?: 'BooleanWithMessage'
    message: string
    value: boolean
  } | null
}

export type CustomerOrderFragment = {
  __typename?: 'Order'
  incrementId: string
  createAt: string
  note: string
  address: {
    __typename?: 'Address'
    addressId: string
    firstname: string
    lastname: string
    telephone: string
    city: string
    cityId?: string | null
    postCode: string
    office?: string | null
    officeId?: string | null
    street: string
    streetNumber?: string | null
    quarterId?: string | null
    note?: string | null
  }
  status: { __typename?: 'OrderStatus'; code: string; label: string }
  shippingMethod: {
    __typename?: 'ShippingMethod'
    code: ShippingMethodCode
    name: string
  }
  paymentMethod: {
    __typename?: 'PaymentMethod'
    code: string
    title: string
    additional?: Array<{
      __typename?: 'MapValue'
      key: string
      value: string
    }> | null
  }
  items?: Array<{
    __typename?: 'CartItem'
    id: string
    qty: number
    product: {
      __typename?: 'MagentoProduct'
      name: string
      sku: string
      image: string
    }
    totals?: {
      __typename?: 'CartItemTotals'
      base_price: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      discount: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      row_total: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      sale_price: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
    } | null
  }> | null
  totals: Array<{
    __typename?: 'Total'
    code: string
    label: string
    amount: { __typename?: 'Money'; currency: string; value: number }
  }>
}

export type GetCustomerAccountInformationQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  currentPage?: InputMaybe<Scalars['Int']['input']>
}>

export type GetCustomerAccountInformationQuery = {
  __typename?: 'Query'
  getCustomer?: {
    __typename?: 'Customer'
    id: any
    email: string
    firstname: string
    lastname: string
    telephone: string
    isSubscribed: boolean
    orders?: Array<{
      __typename?: 'Order'
      incrementId: string
      createAt: string
      note: string
      address: {
        __typename?: 'Address'
        addressId: string
        firstname: string
        lastname: string
        telephone: string
        city: string
        cityId?: string | null
        postCode: string
        office?: string | null
        officeId?: string | null
        street: string
        streetNumber?: string | null
        quarterId?: string | null
        note?: string | null
      }
      status: { __typename?: 'OrderStatus'; code: string; label: string }
      shippingMethod: {
        __typename?: 'ShippingMethod'
        code: ShippingMethodCode
        name: string
      }
      paymentMethod: {
        __typename?: 'PaymentMethod'
        code: string
        title: string
        additional?: Array<{
          __typename?: 'MapValue'
          key: string
          value: string
        }> | null
      }
      items?: Array<{
        __typename?: 'CartItem'
        id: string
        qty: number
        product: {
          __typename?: 'MagentoProduct'
          name: string
          sku: string
          image: string
        }
        totals?: {
          __typename?: 'CartItemTotals'
          base_price: {
            __typename?: 'Total'
            code: string
            label: string
            amount: { __typename?: 'Money'; currency: string; value: number }
          }
          discount: {
            __typename?: 'Total'
            code: string
            label: string
            amount: { __typename?: 'Money'; currency: string; value: number }
          }
          row_total: {
            __typename?: 'Total'
            code: string
            label: string
            amount: { __typename?: 'Money'; currency: string; value: number }
          }
          sale_price: {
            __typename?: 'Total'
            code: string
            label: string
            amount: { __typename?: 'Money'; currency: string; value: number }
          }
        } | null
      }> | null
      totals: Array<{
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }>
    }> | null
    invoice: {
      __typename?: 'MagentoInvoice'
      required: boolean
      type: MagentoInvoiceType
      company: string
      eik: string
      ddsRegistration: boolean
      ddsNumber: string
      mol: string
      address: string
      personName: string
      pin: string
    }
    addresses: Array<{
      __typename?: 'Address'
      addressId: string
      firstname: string
      lastname: string
      telephone: string
      city: string
      cityId?: string | null
      postCode: string
      office?: string | null
      officeId?: string | null
      street: string
      streetNumber?: string | null
      quarterId?: string | null
      note?: string | null
    }>
    authToken: { __typename?: 'Token'; token: string; expires: string }
  } | null
}

export type GetCustomerOrderQueryVariables = Exact<{
  incId: Scalars['String']['input']
}>

export type GetCustomerOrderQuery = {
  __typename?: 'Query'
  getCustomerOrder?: {
    __typename?: 'Order'
    incrementId: string
    createAt: string
    note: string
    address: {
      __typename?: 'Address'
      addressId: string
      firstname: string
      lastname: string
      telephone: string
      city: string
      cityId?: string | null
      postCode: string
      office?: string | null
      officeId?: string | null
      street: string
      streetNumber?: string | null
      quarterId?: string | null
      note?: string | null
    }
    status: { __typename?: 'OrderStatus'; code: string; label: string }
    shippingMethod: {
      __typename?: 'ShippingMethod'
      code: ShippingMethodCode
      name: string
    }
    paymentMethod: {
      __typename?: 'PaymentMethod'
      code: string
      title: string
      additional?: Array<{
        __typename?: 'MapValue'
        key: string
        value: string
      }> | null
    }
    items?: Array<{
      __typename?: 'CartItem'
      id: string
      qty: number
      product: {
        __typename?: 'MagentoProduct'
        name: string
        sku: string
        image: string
      }
      totals?: {
        __typename?: 'CartItemTotals'
        base_price: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        discount: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        row_total: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        sale_price: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
      } | null
    }> | null
    totals: Array<{
      __typename?: 'Total'
      code: string
      label: string
      amount: { __typename?: 'Money'; currency: string; value: number }
    }>
  } | null
}

export type GetStaticContentQueryVariables = Exact<{ [key: string]: never }>

export type GetStaticContentQuery = {
  __typename?: 'Query'
  staticContent: {
    __typename?: 'MagentoPageStaticContent'
    menu: {
      __typename?: 'MegaMenu'
      items: Array<{
        __typename?: 'MegaMenuItem'
        label: string
        url: string
        layout: MenuItemLayout
        image_url?: string | null
        items?: Array<{
          __typename?: 'MegaMenuItem'
          label: string
          url: string
          image_url?: string | null
          items?: Array<{
            __typename?: 'MegaMenuItem'
            label: string
            url: string
            image_url?: string | null
            promo?: {
              __typename?: 'MegaMenuPromo'
              text: string
              promo_percentage: string
              valid_until: string
            } | null
            items?: Array<{
              __typename?: 'MegaMenuItem'
              label: string
              url: string
              image_url?: string | null
            }> | null
          }> | null
        }> | null
      }>
    }
    footer: {
      __typename?: 'Footer'
      aboutText: string
      paymentMethods: Array<string>
      columns: Array<
        | {
            __typename: 'ContactColumn'
            title: string
            contact: {
              __typename?: 'StoreContactInfo'
              telephone: string
              email: string
              socials: {
                __typename?: 'SocialLinks'
                facebook?: string | null
                instagram?: string | null
                linkedin?: string | null
                youtube?: string | null
              }
            }
          }
        | {
            __typename: 'LinksColumn'
            title: string
            links: Array<{ __typename?: 'Link'; text: string; url: string }>
          }
      >
      newsletter: {
        __typename?: 'Newsletter'
        text: string
        checkbox_agree_text: string
      }
    }
    contact: {
      __typename?: 'StoreContactInfo'
      telephone: string
      email: string
      address: string
      socials: {
        __typename?: 'SocialLinks'
        facebook?: string | null
        instagram?: string | null
        linkedin?: string | null
        youtube?: string | null
      }
    }
    miscellaneous: {
      __typename?: 'HeadIncludes'
      meta: Array<{ __typename?: 'MetaTags'; content: string; name: string }>
      scripts: Array<{
        __typename?: 'JsScript'
        content: string
        async: boolean
        src: string
      }>
    }
    settings?: Array<{
      __typename?: 'Settings'
      value: string
      name: string
    }> | null
    cookieCompliance: {
      __typename?: 'CookieCompliance'
      gdprPopupContent: string
      gtagID: string
      fbPixelID: string
      clarityID: string
      cookieGroups: Array<{
        __typename?: 'CookieGroup'
        isEssential: boolean
        title: string
        text: string
        vendors: Array<string>
        grants?: Array<string> | null
        cookiePatterns?: Array<string> | null
      } | null>
    }
  }
}

export type GdprWidgetFragment = {
  __typename?: 'CookieCompliance'
  gdprPopupContent: string
  gtagID: string
  fbPixelID: string
  clarityID: string
  cookieGroups: Array<{
    __typename?: 'CookieGroup'
    isEssential: boolean
    title: string
    text: string
    vendors: Array<string>
    grants?: Array<string> | null
    cookiePatterns?: Array<string> | null
  } | null>
}

export type SubscribeNewsletterMutationVariables = Exact<{
  email: Scalars['String']['input']
}>

export type SubscribeNewsletterMutation = {
  __typename?: 'Mutation'
  subscribeToNewsletter: {
    __typename?: 'BooleanWithMessage'
    value: boolean
    message: string
  }
}

export type GetCarBrandsContentQueryVariables = Exact<{ [key: string]: never }>

export type GetCarBrandsContentQuery = {
  __typename?: 'Query'
  brandsCMS: {
    __typename?: 'BrandsContent'
    topDescription: string
    bottomDescription: string
  }
}

export type GetCarBrandsQueryVariables = Exact<{ [key: string]: never }>

export type GetCarBrandsQuery = {
  __typename?: 'Query'
  brands: Array<{
    __typename?: 'CarBrand'
    brand_id: number
    value: string
    url: string
    image_url: string
  }>
}

export type GetCarModelsQueryVariables = Exact<{
  brand: Scalars['Int']['input']
}>

export type GetCarModelsQuery = {
  __typename?: 'Query'
  carModels: Array<{
    __typename?: 'CarModel'
    brand_id: number
    model_id: number
    value: string
    products_count: number
    url_value: string
  }>
}

export type GetCarModificationsQueryVariables = Exact<{
  modelId: Scalars['Int']['input']
}>

export type GetCarModificationsQuery = {
  __typename?: 'Query'
  carModifications: Array<{
    __typename?: 'CarModification'
    model_id: number
    modification_id: number
    value: string
    url_value: string
  }>
}

export type GetCarsBuyoutDataQueryVariables = Exact<{ [key: string]: never }>

export type GetCarsBuyoutDataQuery = {
  __typename?: 'Query'
  carsBuyout: Array<{
    __typename?: 'CarsModels'
    brand: string
    models: Array<{ __typename?: 'CarModel'; model_id: number; value: string }>
  }>
}

export type TotalInfoFragment = {
  __typename?: 'Total'
  code: string
  label: string
  amount: { __typename?: 'Money'; currency: string; value: number }
}

export type MagentoCartItemFragment = {
  __typename?: 'CartItem'
  id: string
  qty: number
  product: {
    __typename?: 'MagentoProduct'
    name: string
    sku: string
    image: string
    url_key: string
  }
  totals?: {
    __typename?: 'CartItemTotals'
    base_price: {
      __typename?: 'Total'
      code: string
      label: string
      amount: { __typename?: 'Money'; currency: string; value: number }
    }
    sale_price: {
      __typename?: 'Total'
      code: string
      label: string
      amount: { __typename?: 'Money'; currency: string; value: number }
    }
    discount: {
      __typename?: 'Total'
      code: string
      label: string
      amount: { __typename?: 'Money'; currency: string; value: number }
    }
    row_total: {
      __typename?: 'Total'
      code: string
      label: string
      amount: { __typename?: 'Money'; currency: string; value: number }
    }
  } | null
}

export type MagentoCartFragment = {
  __typename?: 'Cart'
  id: string
  customer: string
  couponCode?: string | null
  shippingMethod: ShippingMethodCode
  totals: {
    __typename?: 'CartTotals'
    grand_total: {
      __typename?: 'Total'
      code: string
      label: string
      amount: { __typename?: 'Money'; currency: string; value: number }
    }
    subtotal: {
      __typename?: 'Total'
      code: string
      label: string
      amount: { __typename?: 'Money'; currency: string; value: number }
    }
    shipping?: {
      __typename?: 'Total'
      code: string
      label: string
      amount: { __typename?: 'Money'; currency: string; value: number }
    } | null
    discount?: {
      __typename?: 'Total'
      code: string
      label: string
      amount: { __typename?: 'Money'; currency: string; value: number }
    } | null
  }
  items: Array<{
    __typename?: 'CartItem'
    id: string
    qty: number
    product: {
      __typename?: 'MagentoProduct'
      name: string
      sku: string
      image: string
      url_key: string
    }
    totals?: {
      __typename?: 'CartItemTotals'
      base_price: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      sale_price: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      discount: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      row_total: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
    } | null
  }>
}

export type InitEmptyCartQueryVariables = Exact<{ [key: string]: never }>

export type InitEmptyCartQuery = {
  __typename?: 'Query'
  getEmptyCart: { __typename?: 'Cart'; id: string }
}

export type GetCartQueryVariables = Exact<{
  cartId: Scalars['String']['input']
}>

export type GetCartQuery = {
  __typename?: 'Query'
  getCart: {
    __typename?: 'Cart'
    id: string
    customer: string
    couponCode?: string | null
    shippingMethod: ShippingMethodCode
    totals: {
      __typename?: 'CartTotals'
      grand_total: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      subtotal: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      shipping?: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      } | null
      discount?: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      } | null
    }
    items: Array<{
      __typename?: 'CartItem'
      id: string
      qty: number
      product: {
        __typename?: 'MagentoProduct'
        name: string
        sku: string
        image: string
        url_key: string
      }
      totals?: {
        __typename?: 'CartItemTotals'
        base_price: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        sale_price: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        discount: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        row_total: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
      } | null
    }>
  }
}

export type AddItemToCartMutationVariables = Exact<{
  cartId: Scalars['String']['input']
  sku: Scalars['String']['input']
}>

export type AddItemToCartMutation = {
  __typename?: 'Mutation'
  addToCart: {
    __typename?: 'Cart'
    id: string
    customer: string
    couponCode?: string | null
    shippingMethod: ShippingMethodCode
    totals: {
      __typename?: 'CartTotals'
      grand_total: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      subtotal: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      shipping?: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      } | null
      discount?: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      } | null
    }
    items: Array<{
      __typename?: 'CartItem'
      id: string
      qty: number
      product: {
        __typename?: 'MagentoProduct'
        name: string
        sku: string
        image: string
        url_key: string
      }
      totals?: {
        __typename?: 'CartItemTotals'
        base_price: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        sale_price: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        discount: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        row_total: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
      } | null
    }>
  }
}

export type RemoveItemFromCartMutationVariables = Exact<{
  cartId: Scalars['String']['input']
  sku: Scalars['String']['input']
}>

export type RemoveItemFromCartMutation = {
  __typename?: 'Mutation'
  removeFromCart: {
    __typename?: 'Cart'
    id: string
    customer: string
    couponCode?: string | null
    shippingMethod: ShippingMethodCode
    totals: {
      __typename?: 'CartTotals'
      grand_total: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      subtotal: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      shipping?: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      } | null
      discount?: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      } | null
    }
    items: Array<{
      __typename?: 'CartItem'
      id: string
      qty: number
      product: {
        __typename?: 'MagentoProduct'
        name: string
        sku: string
        image: string
        url_key: string
      }
      totals?: {
        __typename?: 'CartItemTotals'
        base_price: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        sale_price: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        discount: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        row_total: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
      } | null
    }>
  }
}

export type ApplyCouponCodeMutationVariables = Exact<{
  cartId: Scalars['String']['input']
  coupon: Scalars['String']['input']
}>

export type ApplyCouponCodeMutation = {
  __typename?: 'Mutation'
  applyCouponToCart: {
    __typename?: 'Cart'
    id: string
    customer: string
    couponCode?: string | null
    shippingMethod: ShippingMethodCode
    totals: {
      __typename?: 'CartTotals'
      grand_total: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      subtotal: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      }
      shipping?: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      } | null
      discount?: {
        __typename?: 'Total'
        code: string
        label: string
        amount: { __typename?: 'Money'; currency: string; value: number }
      } | null
    }
    items: Array<{
      __typename?: 'CartItem'
      id: string
      qty: number
      product: {
        __typename?: 'MagentoProduct'
        name: string
        sku: string
        image: string
        url_key: string
      }
      totals?: {
        __typename?: 'CartItemTotals'
        base_price: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        sale_price: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        discount: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
        row_total: {
          __typename?: 'Total'
          code: string
          label: string
          amount: { __typename?: 'Money'; currency: string; value: number }
        }
      } | null
    }>
  }
}

export type CategoryPreviewFragment = {
  __typename?: 'MagentoCategory'
  id: number
  name: string
  url_path: string
  thumbnail: string
  products_count: number
  children: Array<{
    __typename?: 'MagentoCategory'
    id: number
    name: string
    url_path: string
    thumbnail: string
    products_count: number
  }>
}

export type GetCategoryTreeQueryVariables = Exact<{ [key: string]: never }>

export type GetCategoryTreeQuery = {
  __typename?: 'Query'
  categoryTree: Array<{
    __typename?: 'MagentoCategory'
    children: Array<{
      __typename?: 'MagentoCategory'
      id: number
      name: string
      url_path: string
      thumbnail: string
      products_count: number
      children: Array<{
        __typename?: 'MagentoCategory'
        id: number
        name: string
        url_path: string
        thumbnail: string
        products_count: number
      }>
    }>
  }>
}

export type GetMostViewCategoriesQueryVariables = Exact<{
  [key: string]: never
}>

export type GetMostViewCategoriesQuery = {
  __typename?: 'Query'
  mostViewedCategory: Array<{
    __typename?: 'MagentoCategory'
    id: number
    name: string
    url_path: string
    thumbnail: string
    products_count: number
    children: Array<{
      __typename?: 'MagentoCategory'
      id: number
      name: string
      url_path: string
      thumbnail: string
      products_count: number
    }>
  }>
}

export type CmsWidgetFragment = {
  __typename?: 'CMSContent'
  identifier: string
  rawContent: string
  title: string
}

export type GetCmsHtmlQueryVariables = Exact<{
  identity: Scalars['String']['input']
}>

export type GetCmsHtmlQuery = {
  __typename?: 'Query'
  getCmsContent: {
    __typename?: 'CMSContent'
    identifier: string
    rawContent: string
    title: string
  }
}

export type UpdateProfileMutationVariables = Exact<{
  firstname: Scalars['String']['input']
  lastname: Scalars['String']['input']
}>

export type UpdateProfileMutation = {
  __typename?: 'Mutation'
  updateProfile?: {
    __typename?: 'Customer'
    id: any
    email: string
    firstname: string
    lastname: string
    telephone: string
    isSubscribed: boolean
    authToken: { __typename?: 'Token'; token: string; expires: string }
  } | null
}

export type UpdateInvoiceMutationVariables = Exact<{
  invoice: InvoiceInput
}>

export type UpdateInvoiceMutation = {
  __typename?: 'Mutation'
  updateInvoice?: {
    __typename?: 'Customer'
    id: any
    email: string
    firstname: string
    lastname: string
    telephone: string
    isSubscribed: boolean
    invoice: {
      __typename?: 'MagentoInvoice'
      required: boolean
      type: MagentoInvoiceType
      company: string
      eik: string
      ddsRegistration: boolean
      ddsNumber: string
      mol: string
      address: string
      personName: string
      pin: string
    }
    addresses: Array<{
      __typename?: 'Address'
      addressId: string
      firstname: string
      lastname: string
      telephone: string
      city: string
      cityId?: string | null
      postCode: string
      office?: string | null
      officeId?: string | null
      street: string
      streetNumber?: string | null
      quarterId?: string | null
      note?: string | null
    }>
    authToken: { __typename?: 'Token'; token: string; expires: string }
  } | null
}

export type DeleteAddressMutationVariables = Exact<{
  addressId: Scalars['String']['input']
}>

export type DeleteAddressMutation = {
  __typename?: 'Mutation'
  deleteAddress?: {
    __typename?: 'Customer'
    id: any
    email: string
    firstname: string
    lastname: string
    telephone: string
    isSubscribed: boolean
    invoice: {
      __typename?: 'MagentoInvoice'
      required: boolean
      type: MagentoInvoiceType
      company: string
      eik: string
      ddsRegistration: boolean
      ddsNumber: string
      mol: string
      address: string
      personName: string
      pin: string
    }
    addresses: Array<{
      __typename?: 'Address'
      addressId: string
      firstname: string
      lastname: string
      telephone: string
      city: string
      cityId?: string | null
      postCode: string
      office?: string | null
      officeId?: string | null
      street: string
      streetNumber?: string | null
      quarterId?: string | null
      note?: string | null
    }>
    authToken: { __typename?: 'Token'; token: string; expires: string }
  } | null
}

export type UpdateAddressMutationVariables = Exact<{
  address: AddressInput
}>

export type UpdateAddressMutation = {
  __typename?: 'Mutation'
  updateAddress?: {
    __typename?: 'Customer'
    id: any
    email: string
    firstname: string
    lastname: string
    telephone: string
    isSubscribed: boolean
    invoice: {
      __typename?: 'MagentoInvoice'
      required: boolean
      type: MagentoInvoiceType
      company: string
      eik: string
      ddsRegistration: boolean
      ddsNumber: string
      mol: string
      address: string
      personName: string
      pin: string
    }
    addresses: Array<{
      __typename?: 'Address'
      addressId: string
      firstname: string
      lastname: string
      telephone: string
      city: string
      cityId?: string | null
      postCode: string
      office?: string | null
      officeId?: string | null
      street: string
      streetNumber?: string | null
      quarterId?: string | null
      note?: string | null
    }>
    authToken: { __typename?: 'Token'; token: string; expires: string }
  } | null
}

export type UpdatePasswordMutationVariables = Exact<{
  oldPassword: Scalars['String']['input']
  newPassword: Scalars['String']['input']
}>

export type UpdatePasswordMutation = {
  __typename?: 'Mutation'
  updatePassword: boolean
}

export type UpdateCustomerNewsletterStatusMutationVariables = Exact<{
  isSubscribed: Scalars['Boolean']['input']
}>

export type UpdateCustomerNewsletterStatusMutation = {
  __typename?: 'Mutation'
  updateNewsletterSubscription?: {
    __typename?: 'BooleanWithMessage'
    message: string
    value: boolean
  } | null
}

export type RegisterCustomerMutationVariables = Exact<{
  data: CustomerRegistrationData
}>

export type RegisterCustomerMutation = {
  __typename?: 'Mutation'
  registerCustomer: string
}

export type LoginCustomerMutationVariables = Exact<{
  email: Scalars['String']['input']
  password: Scalars['String']['input']
}>

export type LoginCustomerMutation = {
  __typename?: 'Mutation'
  loginCustomer: string
}

export type LogoutCustomerMutationVariables = Exact<{ [key: string]: never }>

export type LogoutCustomerMutation = {
  __typename?: 'Mutation'
  logoutCustomer: boolean
}

export type ForgotPasswordMutationVariables = Exact<{
  email: Scalars['String']['input']
}>

export type ForgotPasswordMutation = {
  __typename?: 'Mutation'
  forgotPassword: string
}

export type ResetPasswordMutationVariables = Exact<{
  id: Scalars['String']['input']
  resetToken: Scalars['String']['input']
  pass: Scalars['String']['input']
  confirm: Scalars['String']['input']
}>

export type ResetPasswordMutation = {
  __typename?: 'Mutation'
  resetPassword: boolean
}

export type CustomerAddressFragment = {
  __typename?: 'Address'
  addressId: string
  firstname: string
  lastname: string
  telephone: string
  city: string
  cityId?: string | null
  postCode: string
  office?: string | null
  officeId?: string | null
  street: string
  streetNumber?: string | null
  quarterId?: string | null
  note?: string | null
}

export type CustomerBasicInfoFragment = {
  __typename?: 'Customer'
  id: any
  email: string
  firstname: string
  lastname: string
  telephone: string
  isSubscribed: boolean
  authToken: { __typename?: 'Token'; token: string; expires: string }
}

export type CustomerInvoiceFragment = {
  __typename?: 'MagentoInvoice'
  required: boolean
  type: MagentoInvoiceType
  company: string
  eik: string
  ddsRegistration: boolean
  ddsNumber: string
  mol: string
  address: string
  personName: string
  pin: string
}

export type CustomerFullInfoFragment = {
  __typename?: 'Customer'
  id: any
  email: string
  firstname: string
  lastname: string
  telephone: string
  isSubscribed: boolean
  invoice: {
    __typename?: 'MagentoInvoice'
    required: boolean
    type: MagentoInvoiceType
    company: string
    eik: string
    ddsRegistration: boolean
    ddsNumber: string
    mol: string
    address: string
    personName: string
    pin: string
  }
  addresses: Array<{
    __typename?: 'Address'
    addressId: string
    firstname: string
    lastname: string
    telephone: string
    city: string
    cityId?: string | null
    postCode: string
    office?: string | null
    officeId?: string | null
    street: string
    streetNumber?: string | null
    quarterId?: string | null
    note?: string | null
  }>
  authToken: { __typename?: 'Token'; token: string; expires: string }
}

export type GetMagentoCustomerQueryVariables = Exact<{ [key: string]: never }>

export type GetMagentoCustomerQuery = {
  __typename?: 'Query'
  getCustomer?: {
    __typename?: 'Customer'
    id: any
    email: string
    firstname: string
    lastname: string
    telephone: string
    isSubscribed: boolean
    authToken: { __typename?: 'Token'; token: string; expires: string }
  } | null
}

export type GetMagentoCustomerWithAddressesQueryVariables = Exact<{
  [key: string]: never
}>

export type GetMagentoCustomerWithAddressesQuery = {
  __typename?: 'Query'
  getCustomer?: {
    __typename?: 'Customer'
    id: any
    email: string
    firstname: string
    lastname: string
    telephone: string
    isSubscribed: boolean
    invoice: {
      __typename?: 'MagentoInvoice'
      required: boolean
      type: MagentoInvoiceType
      company: string
      eik: string
      ddsRegistration: boolean
      ddsNumber: string
      mol: string
      address: string
      personName: string
      pin: string
    }
    addresses: Array<{
      __typename?: 'Address'
      addressId: string
      firstname: string
      lastname: string
      telephone: string
      city: string
      cityId?: string | null
      postCode: string
      office?: string | null
      officeId?: string | null
      street: string
      streetNumber?: string | null
      quarterId?: string | null
      note?: string | null
    }>
    authToken: { __typename?: 'Token'; token: string; expires: string }
  } | null
}

export type GetLastViewedQueryVariables = Exact<{
  skus: Array<Scalars['String']['input']> | Scalars['String']['input']
}>

export type GetLastViewedQuery = {
  __typename?: 'Query'
  getProductCollection: Array<{
    __typename?: 'MagentoProduct'
    id: number
    attribute_set_id: number
    name: string
    sku: string
    url_key: string
    price: number
    special_price: number
    tierBrand: string
    years: string
    image: string
    status: ProductStatus
    brand: {
      __typename?: 'CarBrand'
      value: string
      image_url: string
      url: string
    }
  }>
}

export type GetMetadataQueryVariables = Exact<{
  url: Scalars['String']['input']
  query?: InputMaybe<Array<InputMaybe<QueryParam>> | InputMaybe<QueryParam>>
  entityType?: InputMaybe<EntityType>
}>

export type GetMetadataQuery = {
  __typename?: 'Query'
  getPageMetadata: {
    __typename?: 'StoreMetaData'
    canonical_url: string
    next_url: string
    prev_url: string
    robots: string
    title: string
    description: string
    keywords: string
    og?: {
      __typename?: 'OpenGraph'
      ogType: OpenGraphType
      title: string
      description: string
      image: string
      url: string
      price: string
      currency: string
    } | null
  }
}

export type CatalogFilterFragment = {
  __typename?: 'MagentoFilter'
  label: string
  attribute_code: string
  type: FilterRenderType
  position: number
  request_var: string
  options: Array<{
    __typename?: 'MagentoOption'
    label: string
    value: string
    id: string
  }>
}

export type PageInfoFragment = {
  __typename?: 'MagentoPageInfo'
  available_page_sizes: Array<number>
  sort_field: string
  sort_direction: string
  page_size: number
  products_count: number
  current_page: number
  total_pages: number
  available_sorts: Array<{
    __typename?: 'MagentoOption'
    label: string
    value: string
  }>
}

export type AppliedFilterFragment = {
  __typename?: 'MagentoAppliedFilter'
  attribute_code: string
  label: string
  value: string
  request_var: string
  request_value: string
}

export type CatalogPageDataFragment = {
  __typename?: 'CatalogPage'
  category: {
    __typename?: 'MagentoCategory'
    id: number
    name: string
    title: string
    url_path: string
    thumbnail: string
    description: string
  }
  applied_filters: Array<{
    __typename?: 'MagentoAppliedFilter'
    attribute_code: string
    label: string
    value: string
    request_var: string
    request_value: string
  }>
  filters: Array<{
    __typename?: 'MagentoFilter'
    label: string
    attribute_code: string
    type: FilterRenderType
    position: number
    request_var: string
    options: Array<{
      __typename?: 'MagentoOption'
      label: string
      value: string
      id: string
    }>
  }>
  page_info: {
    __typename?: 'MagentoPageInfo'
    available_page_sizes: Array<number>
    sort_field: string
    sort_direction: string
    page_size: number
    products_count: number
    current_page: number
    total_pages: number
    available_sorts: Array<{
      __typename?: 'MagentoOption'
      label: string
      value: string
    }>
  }
  products: Array<{
    __typename?: 'MagentoProduct'
    id: number
    attribute_set_id: number
    name: string
    sku: string
    url_key: string
    price: number
    special_price: number
    tierBrand: string
    years: string
    image: string
    status: ProductStatus
    brand: {
      __typename?: 'CarBrand'
      value: string
      image_url: string
      url: string
    }
  }>
}

export type SplashPageDataFragment = {
  __typename?: 'SplashPage'
  category: {
    __typename?: 'MagentoCategory'
    id: number
    name: string
    url_path: string
    thumbnail: string
    description: string
  }
  applied_filters: Array<{
    __typename?: 'MagentoAppliedFilter'
    attribute_code: string
    label: string
    value: string
    request_var: string
    request_value: string
  }>
  filters: Array<{
    __typename?: 'MagentoFilter'
    label: string
    attribute_code: string
    type: FilterRenderType
    position: number
    request_var: string
    options: Array<{
      __typename?: 'MagentoOption'
      label: string
      value: string
      id: string
    }>
  }>
  page_info: {
    __typename?: 'MagentoPageInfo'
    available_page_sizes: Array<number>
    sort_field: string
    sort_direction: string
    page_size: number
    products_count: number
    current_page: number
    total_pages: number
    available_sorts: Array<{
      __typename?: 'MagentoOption'
      label: string
      value: string
    }>
  }
  products: Array<{
    __typename?: 'MagentoProduct'
    id: number
    attribute_set_id: number
    name: string
    sku: string
    url_key: string
    price: number
    special_price: number
    tierBrand: string
    years: string
    image: string
    status: ProductStatus
    brand: {
      __typename?: 'CarBrand'
      value: string
      image_url: string
      url: string
    }
  }>
}

export type ProductPageDataFragment = {
  __typename?: 'ProductPage'
  product: {
    __typename?: 'MagentoProduct'
    id: number
    attribute_set_id: number
    name: string
    sku: string
    url_key: string
    image: string
    price: number
    special_price: number
    special_price_to_date: string
    tierBrand: string
    description: string
    status: ProductStatus
    freeAfterXSeconds: number
    gallery: Array<{
      __typename?: 'ProductImage'
      label: string
      position: number
      url: string
    }>
    brand: {
      __typename?: 'CarBrand'
      value: string
      image_url: string
      url: string
    }
    model: { __typename?: 'CarModel'; value: string }
    modification: { __typename?: 'CarModification'; value: string }
    additional: Array<{
      __typename?: 'ProductAttribute'
      attribute_code: string
      label: string
      value: string
    }>
  }
}

export type CmsContentPageDataFragment = {
  __typename?: 'CMSContent'
  title: string
  rawContent: string
}

export type GetPageDataQueryVariables = Exact<{
  url: Scalars['String']['input']
  query?: InputMaybe<Array<InputMaybe<QueryParam>> | InputMaybe<QueryParam>>
}>

export type GetPageDataQuery = {
  __typename?: 'Query'
  pageData: {
    __typename?: 'MagentoPageData'
    breadcrumbs?: Array<{
      __typename?: 'Breadcrumb'
      url_path: string
      label: string
    }> | null
    page?:
      | { __typename: 'CMSContent'; title: string; rawContent: string }
      | {
          __typename: 'CatalogPage'
          category: {
            __typename?: 'MagentoCategory'
            id: number
            name: string
            title: string
            url_path: string
            thumbnail: string
            description: string
          }
          applied_filters: Array<{
            __typename?: 'MagentoAppliedFilter'
            attribute_code: string
            label: string
            value: string
            request_var: string
            request_value: string
          }>
          filters: Array<{
            __typename?: 'MagentoFilter'
            label: string
            attribute_code: string
            type: FilterRenderType
            position: number
            request_var: string
            options: Array<{
              __typename?: 'MagentoOption'
              label: string
              value: string
              id: string
            }>
          }>
          page_info: {
            __typename?: 'MagentoPageInfo'
            available_page_sizes: Array<number>
            sort_field: string
            sort_direction: string
            page_size: number
            products_count: number
            current_page: number
            total_pages: number
            available_sorts: Array<{
              __typename?: 'MagentoOption'
              label: string
              value: string
            }>
          }
          products: Array<{
            __typename?: 'MagentoProduct'
            id: number
            attribute_set_id: number
            name: string
            sku: string
            url_key: string
            price: number
            special_price: number
            tierBrand: string
            years: string
            image: string
            status: ProductStatus
            brand: {
              __typename?: 'CarBrand'
              value: string
              image_url: string
              url: string
            }
          }>
        }
      | {
          __typename: 'ProductPage'
          product: {
            __typename?: 'MagentoProduct'
            id: number
            attribute_set_id: number
            name: string
            sku: string
            url_key: string
            image: string
            price: number
            special_price: number
            special_price_to_date: string
            tierBrand: string
            description: string
            status: ProductStatus
            freeAfterXSeconds: number
            gallery: Array<{
              __typename?: 'ProductImage'
              label: string
              position: number
              url: string
            }>
            brand: {
              __typename?: 'CarBrand'
              value: string
              image_url: string
              url: string
            }
            model: { __typename?: 'CarModel'; value: string }
            modification: { __typename?: 'CarModification'; value: string }
            additional: Array<{
              __typename?: 'ProductAttribute'
              attribute_code: string
              label: string
              value: string
            }>
          }
        }
      | { __typename: 'SearchPage' }
      | {
          __typename: 'SplashPage'
          category: {
            __typename?: 'MagentoCategory'
            id: number
            name: string
            url_path: string
            thumbnail: string
            description: string
          }
          applied_filters: Array<{
            __typename?: 'MagentoAppliedFilter'
            attribute_code: string
            label: string
            value: string
            request_var: string
            request_value: string
          }>
          filters: Array<{
            __typename?: 'MagentoFilter'
            label: string
            attribute_code: string
            type: FilterRenderType
            position: number
            request_var: string
            options: Array<{
              __typename?: 'MagentoOption'
              label: string
              value: string
              id: string
            }>
          }>
          page_info: {
            __typename?: 'MagentoPageInfo'
            available_page_sizes: Array<number>
            sort_field: string
            sort_direction: string
            page_size: number
            products_count: number
            current_page: number
            total_pages: number
            available_sorts: Array<{
              __typename?: 'MagentoOption'
              label: string
              value: string
            }>
          }
          products: Array<{
            __typename?: 'MagentoProduct'
            id: number
            attribute_set_id: number
            name: string
            sku: string
            url_key: string
            price: number
            special_price: number
            tierBrand: string
            years: string
            image: string
            status: ProductStatus
            brand: {
              __typename?: 'CarBrand'
              value: string
              image_url: string
              url: string
            }
          }>
        }
      | null
    redirect?: {
      __typename?: 'PageRedirect'
      code: number
      url_path: string
    } | null
  }
}

export type GetPartnersQueryVariables = Exact<{ [key: string]: never }>

export type GetPartnersQuery = {
  __typename?: 'Query'
  getPartners: Array<{
    __typename?: 'City'
    name: string
    nameEn: string
    postCode: string
    offices: Array<{
      __typename?: 'Office'
      name: string
      nameEn: string
      address: string
      addressEn: string
      location: { __typename?: 'GeoLocation'; lat: number; lng: number }
    }>
  }>
}

export type ProductBrandFragment = {
  __typename?: 'CarBrand'
  value: string
  image_url: string
  url: string
}

export type ProductCardDataFragment = {
  __typename?: 'MagentoProduct'
  id: number
  attribute_set_id: number
  name: string
  sku: string
  url_key: string
  price: number
  special_price: number
  tierBrand: string
  years: string
  image: string
  status: ProductStatus
  brand: {
    __typename?: 'CarBrand'
    value: string
    image_url: string
    url: string
  }
}

export type RelatedProductCardFragment = {
  __typename?: 'MagentoProduct'
  id: number
  attribute_set_id: number
  name: string
  sku: string
  status: ProductStatus
  url_key: string
  price: number
  special_price: number
  tierBrand: string
  years: string
  image: string
  brand: {
    __typename?: 'CarBrand'
    value: string
    image_url: string
    url: string
  }
}

export type GetRelatedQueryVariables = Exact<{
  sku: Scalars['String']['input']
}>

export type GetRelatedQuery = {
  __typename?: 'Query'
  getRelated: Array<{
    __typename?: 'MagentoProduct'
    id: number
    attribute_set_id: number
    name: string
    sku: string
    status: ProductStatus
    url_key: string
    price: number
    special_price: number
    tierBrand: string
    years: string
    image: string
    brand: {
      __typename?: 'CarBrand'
      value: string
      image_url: string
      url: string
    }
  }>
}

export type ProductStatusFragment = {
  __typename?: 'MagentoProduct'
  status: ProductStatus
  price: number
  freeAfterXSeconds: number
  special_price: number
}

export type GetProductStatusQueryVariables = Exact<{
  sku: Scalars['String']['input']
}>

export type GetProductStatusQuery = {
  __typename?: 'Query'
  getProduct: {
    __typename?: 'MagentoProduct'
    status: ProductStatus
    price: number
    freeAfterXSeconds: number
    special_price: number
  }
}

export type SearchQueryVariables = Exact<{
  term: Scalars['String']['input']
}>

export type SearchQuery = {
  __typename?: 'Query'
  search: {
    __typename?: 'SearchResults'
    suggestions: Array<string>
    total_count: number
    products: Array<{
      __typename?: 'MagentoProduct'
      id: number
      attribute_set_id: number
      name: string
      sku: string
      url_key: string
      price: number
      special_price: number
      tierBrand: string
      years: string
      image: string
      status: ProductStatus
      brand: {
        __typename?: 'CarBrand'
        value: string
        image_url: string
        url: string
      }
    }>
    categories: Array<{
      __typename?: 'MagentoCategory'
      name: string
      url_path: string
    }>
  }
}

export const BlogPostPageDataFragmentDoc = gql`
  fragment BlogPostPageData on BlogPost {
    urlKey
    title
    content
    mainImageUrl
    publishedAt
  }
`
export const PostPreviewFragmentDoc = gql`
  fragment PostPreview on BlogPost {
    urlKey
    title
    summary
    previewImageUrl
    publishedAt
  }
`
export const MagentoBlogPageDataFragmentDoc = gql`
  fragment MagentoBlogPageData on MagentoBlog {
    currentPage
    totalPages
    posts {
      ...PostPreview
    }
  }
  ${PostPreviewFragmentDoc}
`
export const ShippingTotalFragmentDoc = gql`
  fragment ShippingTotal on ShippingMethodTotal {
    label
    price {
      currency
      value
    }
  }
`
export const MagentoShippingMethodFragmentDoc = gql`
  fragment MagentoShippingMethod on ShippingMethod {
    name
    code
    total {
      ...ShippingTotal
    }
    additional {
      key
      value
    }
  }
  ${ShippingTotalFragmentDoc}
`
export const ShippingCityFragmentDoc = gql`
  fragment ShippingCity on City {
    id
    name
    nameEn
    postCode
  }
`
export const ShippingQuarterFragmentDoc = gql`
  fragment ShippingQuarter on Quarter {
    id
    name
    nameEn
  }
`
export const ShippingOfficeFragmentDoc = gql`
  fragment ShippingOffice on Office {
    id
    code
    name
    nameEn
    address
    addressEn
  }
`
export const CustomerAddressFragmentDoc = gql`
  fragment CustomerAddress on Address {
    addressId
    firstname
    lastname
    telephone
    city
    cityId
    postCode
    office
    officeId
    street
    streetNumber
    quarterId
    note
  }
`
export const TotalInfoFragmentDoc = gql`
  fragment TotalInfo on Total {
    code
    label
    amount {
      currency
      value
    }
  }
`
export const CustomerOrderFragmentDoc = gql`
  fragment CustomerOrder on Order {
    incrementId
    createAt
    address {
      ...CustomerAddress
    }
    status {
      code
      label
    }
    note
    shippingMethod {
      code
      name
    }
    paymentMethod {
      code
      title
      additional {
        key
        value
      }
    }
    items {
      id
      qty
      product {
        name
        sku
        image
      }
      totals {
        base_price {
          ...TotalInfo
        }
        discount {
          ...TotalInfo
        }
        row_total {
          ...TotalInfo
        }
        sale_price {
          ...TotalInfo
        }
      }
    }
    totals {
      amount {
        currency
        value
      }
      code
      label
    }
  }
  ${CustomerAddressFragmentDoc}
  ${TotalInfoFragmentDoc}
`
export const GdprWidgetFragmentDoc = gql`
  fragment GDPRWidget on CookieCompliance {
    gdprPopupContent
    gtagID
    fbPixelID
    clarityID
    cookieGroups {
      isEssential
      title
      text
      vendors
      grants
      cookiePatterns
    }
  }
`
export const MagentoCartItemFragmentDoc = gql`
  fragment MagentoCartItem on CartItem {
    id
    product {
      name
      sku
      image
      url_key
    }
    qty
    totals {
      base_price {
        ...TotalInfo
      }
      sale_price {
        ...TotalInfo
      }
      discount {
        ...TotalInfo
      }
      row_total {
        ...TotalInfo
      }
    }
  }
  ${TotalInfoFragmentDoc}
`
export const MagentoCartFragmentDoc = gql`
  fragment MagentoCart on Cart {
    id
    customer
    couponCode
    totals {
      grand_total {
        ...TotalInfo
      }
      subtotal {
        ...TotalInfo
      }
      shipping {
        ...TotalInfo
      }
      discount {
        ...TotalInfo
      }
    }
    items {
      ...MagentoCartItem
    }
    shippingMethod
  }
  ${TotalInfoFragmentDoc}
  ${MagentoCartItemFragmentDoc}
`
export const CategoryPreviewFragmentDoc = gql`
  fragment CategoryPreview on MagentoCategory {
    id
    name
    url_path
    thumbnail
    products_count
    children {
      id
      name
      url_path
      thumbnail
      products_count
    }
  }
`
export const CmsWidgetFragmentDoc = gql`
  fragment CMSWidget on CMSContent {
    identifier
    rawContent
    title
  }
`
export const CustomerBasicInfoFragmentDoc = gql`
  fragment CustomerBasicInfo on Customer {
    id
    authToken {
      token
      expires
    }
    email
    firstname
    lastname
    telephone
    isSubscribed
  }
`
export const CustomerInvoiceFragmentDoc = gql`
  fragment CustomerInvoice on MagentoInvoice {
    required
    type
    company
    eik
    ddsRegistration
    ddsNumber
    mol
    address
    personName
    pin
  }
`
export const CustomerFullInfoFragmentDoc = gql`
  fragment CustomerFullInfo on Customer {
    ...CustomerBasicInfo
    invoice {
      ...CustomerInvoice
    }
    addresses {
      ...CustomerAddress
    }
  }
  ${CustomerBasicInfoFragmentDoc}
  ${CustomerInvoiceFragmentDoc}
  ${CustomerAddressFragmentDoc}
`
export const AppliedFilterFragmentDoc = gql`
  fragment AppliedFilter on MagentoAppliedFilter {
    attribute_code
    label
    value
    request_var
    request_value
  }
`
export const CatalogFilterFragmentDoc = gql`
  fragment CatalogFilter on MagentoFilter {
    label
    attribute_code
    type
    position
    request_var
    options {
      label
      value
      id
    }
  }
`
export const PageInfoFragmentDoc = gql`
  fragment PageInfo on MagentoPageInfo {
    available_sorts {
      label
      value
    }
    available_page_sizes
    sort_field
    sort_direction
    page_size
    products_count
    current_page
    total_pages
  }
`
export const ProductBrandFragmentDoc = gql`
  fragment ProductBrand on CarBrand {
    value
    image_url
    url
  }
`
export const ProductCardDataFragmentDoc = gql`
  fragment ProductCardData on MagentoProduct {
    id
    attribute_set_id
    name
    sku
    url_key
    price
    special_price
    brand {
      ...ProductBrand
    }
    tierBrand
    years
    image
    status
  }
  ${ProductBrandFragmentDoc}
`
export const CatalogPageDataFragmentDoc = gql`
  fragment CatalogPageData on CatalogPage {
    category {
      id
      name
      title
      url_path
      thumbnail
      description
    }
    applied_filters {
      ...AppliedFilter
    }
    filters {
      ...CatalogFilter
    }
    page_info {
      ...PageInfo
    }
    products {
      ...ProductCardData
    }
  }
  ${AppliedFilterFragmentDoc}
  ${CatalogFilterFragmentDoc}
  ${PageInfoFragmentDoc}
  ${ProductCardDataFragmentDoc}
`
export const SplashPageDataFragmentDoc = gql`
  fragment SplashPageData on SplashPage {
    category {
      id
      name
      url_path
      thumbnail
      description
    }
    applied_filters {
      ...AppliedFilter
    }
    filters {
      ...CatalogFilter
    }
    page_info {
      ...PageInfo
    }
    products {
      ...ProductCardData
    }
  }
  ${AppliedFilterFragmentDoc}
  ${CatalogFilterFragmentDoc}
  ${PageInfoFragmentDoc}
  ${ProductCardDataFragmentDoc}
`
export const ProductStatusFragmentDoc = gql`
  fragment ProductStatus on MagentoProduct {
    status
    price
    freeAfterXSeconds
    special_price
  }
`
export const ProductPageDataFragmentDoc = gql`
  fragment ProductPageData on ProductPage {
    product {
      id
      attribute_set_id
      name
      sku
      url_key
      image
      gallery {
        label
        position
        url
      }
      price
      special_price
      special_price_to_date
      ...ProductStatus
      brand {
        ...ProductBrand
      }
      model {
        value
      }
      modification {
        value
      }
      tierBrand
      additional {
        attribute_code
        label
        value
      }
      description
    }
  }
  ${ProductStatusFragmentDoc}
  ${ProductBrandFragmentDoc}
`
export const CmsContentPageDataFragmentDoc = gql`
  fragment CMSContentPageData on CMSContent {
    title
    rawContent
  }
`
export const RelatedProductCardFragmentDoc = gql`
  fragment RelatedProductCard on MagentoProduct {
    id
    attribute_set_id
    name
    sku
    status
    url_key
    price
    special_price
    tierBrand
    brand {
      ...ProductBrand
    }
    years
    image
  }
  ${ProductBrandFragmentDoc}
`
export const GetCurrentProductStatusDocument = gql`
  query GetCurrentProductStatus($skus: [String!]!) {
    getStockStatus(skus: $skus) {
      sku
      status
    }
  }
`
export const MakeFastOrderDocument = gql`
  mutation MakeFastOrder($data: FastOrderInput!) {
    fastOrder(data: $data) {
      message
      value
    }
  }
`
export const SendErrorReportDocument = gql`
  mutation SendErrorReport($data: ErrorReportInput!) {
    sendErrorReport(data: $data) {
      message
      value
    }
  }
`
export const GetBlogPostDocument = gql`
  query GetBlogPost($identifier: String!) {
    getBlogPost(identifier: $identifier) {
      ...BlogPostPageData
    }
  }
  ${BlogPostPageDataFragmentDoc}
`
export const GetBlogPostsDocument = gql`
  query GetBlogPosts($page: Int!) {
    getBlogPosts(page: $page) {
      ...MagentoBlogPageData
    }
  }
  ${MagentoBlogPageDataFragmentDoc}
`
export const GetFeaturedBlogPostDocument = gql`
  query GetFeaturedBlogPost {
    getFeaturedBlogPost {
      ...PostPreview
    }
  }
  ${PostPreviewFragmentDoc}
`
export const GetSearchResultsPageDocument = gql`
  query GetSearchResultsPage($term: String!, $query: [QueryParam]) {
    searchPage(searchQuery: $term, query: $query) {
      title
      total_count
      category {
        id
        name
        title
        url_path
        thumbnail
      }
      applied_filters {
        ...AppliedFilter
      }
      filters {
        ...CatalogFilter
      }
      page_info {
        ...PageInfo
      }
      products {
        ...ProductCardData
      }
    }
  }
  ${AppliedFilterFragmentDoc}
  ${CatalogFilterFragmentDoc}
  ${PageInfoFragmentDoc}
  ${ProductCardDataFragmentDoc}
`
export const CrateNewOrderDocument = gql`
  mutation CrateNewOrder($cartId: String!, $order: SubmitOrderInput!) {
    submitOrder(cartId: $cartId, input: $order) {
      orderNumber
      status
      paymentMethod {
        code
        title
        additional {
          key
          value
        }
      }
      redirect {
        data {
          key
          value
        }
        url
      }
      shippingMethod {
        name
        code
        total {
          ...ShippingTotal
        }
        additional {
          key
          value
        }
      }
      order {
        incrementId
        items {
          product {
            sku
            name
          }
          totals {
            base_price {
              ...TotalInfo
            }
            row_total {
              ...TotalInfo
            }
          }
          qty
        }
        totals {
          ...TotalInfo
        }
      }
    }
  }
  ${ShippingTotalFragmentDoc}
  ${TotalInfoFragmentDoc}
`
export const GetShippingMethodsDocument = gql`
  query GetShippingMethods {
    getShippingMethods {
      ...MagentoShippingMethod
    }
  }
  ${MagentoShippingMethodFragmentDoc}
`
export const CalculateShippingDocument = gql`
  mutation CalculateShipping($cartId: String!, $input: ShippingDataInput!) {
    calculateShipping(cartId: $cartId, input: $input) {
      ...ShippingTotal
    }
  }
  ${ShippingTotalFragmentDoc}
`
export const GetCitiesDocument = gql`
  query GetCities($cityType: CityType!) {
    getShippingCities(cityType: $cityType) {
      ...ShippingCity
    }
  }
  ${ShippingCityFragmentDoc}
`
export const GetEcontCityDocument = gql`
  query GetEcontCity($cityType: CityType!, $filterCityId: Int!) {
    getShippingCities(cityType: $cityType, filterCityId: $filterCityId) {
      ...ShippingCity
      offices {
        ...ShippingOffice
      }
      quarters {
        ...ShippingQuarter
      }
    }
  }
  ${ShippingCityFragmentDoc}
  ${ShippingOfficeFragmentDoc}
  ${ShippingQuarterFragmentDoc}
`
export const CollectTotalsDocument = gql`
  mutation CollectTotals(
    $cartId: String!
    $promoCode: String!
    $address: AddressData!
    $customer: CustomerData!
    $shippingMethod: ShippingMethodCode
  ) {
    collectTotals(
      cartId: $cartId
      input: {
        promoCode: $promoCode
        shipping: {
          shippingMethod: $shippingMethod
          address: $address
          customer: $customer
        }
      }
    ) {
      ...MagentoCart
    }
  }
  ${MagentoCartFragmentDoc}
`
export const SendContactUsDocument = gql`
  mutation SendContactUs($data: ContactInput!) {
    sendContactMessage(input: $data) {
      value
      message
    }
  }
`
export const RequestAccountDeletionDocument = gql`
  mutation RequestAccountDeletion {
    requestDeleteCustomer {
      message
      value
    }
  }
`
export const GetCustomerAccountInformationDocument = gql`
  query GetCustomerAccountInformation($limit: Int, $currentPage: Int) {
    getCustomer {
      ...CustomerFullInfo
      orders(limit: $limit, page: $currentPage) {
        ...CustomerOrder
      }
    }
  }
  ${CustomerFullInfoFragmentDoc}
  ${CustomerOrderFragmentDoc}
`
export const GetCustomerOrderDocument = gql`
  query GetCustomerOrder($incId: String!) {
    getCustomerOrder(incId: $incId) {
      ...CustomerOrder
    }
  }
  ${CustomerOrderFragmentDoc}
`
export const GetStaticContentDocument = gql`
  query GetStaticContent {
    staticContent {
      menu {
        items {
          label
          url
          layout
          image_url
          items {
            label
            url
            image_url
            items {
              label
              url
              image_url
              promo {
                text
                promo_percentage
                valid_until
              }
              items {
                label
                url
                image_url
              }
            }
          }
        }
      }
      footer {
        aboutText
        columns {
          __typename
          ... on LinksColumn {
            title
            links {
              text
              url
            }
          }
          ... on ContactColumn {
            title
            contact {
              telephone
              email
              socials {
                facebook
                instagram
                linkedin
                youtube
              }
            }
          }
        }
        newsletter {
          text
          checkbox_agree_text
        }
        paymentMethods
      }
      contact {
        telephone
        email
        address
        socials {
          facebook
          instagram
          linkedin
          youtube
        }
      }
      miscellaneous {
        meta {
          content
          name
        }
        scripts {
          content
          async
          src
        }
      }
      settings {
        value
        name
      }
      cookieCompliance {
        ...GDPRWidget
      }
    }
  }
  ${GdprWidgetFragmentDoc}
`
export const SubscribeNewsletterDocument = gql`
  mutation SubscribeNewsletter($email: String!) {
    subscribeToNewsletter(email: $email) {
      value
      message
    }
  }
`
export const GetCarBrandsContentDocument = gql`
  query GetCarBrandsContent {
    brandsCMS {
      topDescription
      bottomDescription
    }
  }
`
export const GetCarBrandsDocument = gql`
  query GetCarBrands {
    brands {
      brand_id
      value
      url
      image_url
    }
  }
`
export const GetCarModelsDocument = gql`
  query GetCarModels($brand: Int!) {
    carModels(brandId: $brand) {
      brand_id
      model_id
      value
      products_count
      url_value
    }
  }
`
export const GetCarModificationsDocument = gql`
  query GetCarModifications($modelId: Int!) {
    carModifications(modelId: $modelId) {
      model_id
      modification_id
      value
      url_value
    }
  }
`
export const GetCarsBuyoutDataDocument = gql`
  query GetCarsBuyoutData {
    carsBuyout {
      brand
      models {
        model_id
        value
      }
    }
  }
`
export const InitEmptyCartDocument = gql`
  query InitEmptyCart {
    getEmptyCart {
      id
    }
  }
`
export const GetCartDocument = gql`
  query GetCart($cartId: String!) {
    getCart(cartId: $cartId) {
      ...MagentoCart
    }
  }
  ${MagentoCartFragmentDoc}
`
export const AddItemToCartDocument = gql`
  mutation AddItemToCart($cartId: String!, $sku: String!) {
    addToCart(cartId: $cartId, sku: $sku, qty: 1) {
      ...MagentoCart
    }
  }
  ${MagentoCartFragmentDoc}
`
export const RemoveItemFromCartDocument = gql`
  mutation RemoveItemFromCart($cartId: String!, $sku: String!) {
    removeFromCart(cartId: $cartId, sku: $sku) {
      ...MagentoCart
    }
  }
  ${MagentoCartFragmentDoc}
`
export const ApplyCouponCodeDocument = gql`
  mutation ApplyCouponCode($cartId: String!, $coupon: String!) {
    applyCouponToCart(cartId: $cartId, couponCode: $coupon) {
      ...MagentoCart
    }
  }
  ${MagentoCartFragmentDoc}
`
export const GetCategoryTreeDocument = gql`
  query GetCategoryTree {
    categoryTree {
      children {
        ...CategoryPreview
      }
    }
  }
  ${CategoryPreviewFragmentDoc}
`
export const GetMostViewCategoriesDocument = gql`
  query GetMostViewCategories {
    mostViewedCategory {
      ...CategoryPreview
    }
  }
  ${CategoryPreviewFragmentDoc}
`
export const GetCmsHtmlDocument = gql`
  query GetCMSHtml($identity: String!) {
    getCmsContent(identifier: $identity, contentType: PAGE) {
      ...CMSWidget
    }
  }
  ${CmsWidgetFragmentDoc}
`
export const UpdateProfileDocument = gql`
  mutation UpdateProfile($firstname: String!, $lastname: String!) {
    updateProfile(firstname: $firstname, lastname: $lastname) {
      ...CustomerBasicInfo
    }
  }
  ${CustomerBasicInfoFragmentDoc}
`
export const UpdateInvoiceDocument = gql`
  mutation UpdateInvoice($invoice: InvoiceInput!) {
    updateInvoice(invoice: $invoice) {
      ...CustomerFullInfo
    }
  }
  ${CustomerFullInfoFragmentDoc}
`
export const DeleteAddressDocument = gql`
  mutation DeleteAddress($addressId: String!) {
    deleteAddress(addressId: $addressId) {
      ...CustomerFullInfo
    }
  }
  ${CustomerFullInfoFragmentDoc}
`
export const UpdateAddressDocument = gql`
  mutation UpdateAddress($address: AddressInput!) {
    updateAddress(address: $address) {
      ...CustomerFullInfo
    }
  }
  ${CustomerFullInfoFragmentDoc}
`
export const UpdatePasswordDocument = gql`
  mutation UpdatePassword($oldPassword: String!, $newPassword: String!) {
    updatePassword(oldPassword: $oldPassword, newPassword: $newPassword)
  }
`
export const UpdateCustomerNewsletterStatusDocument = gql`
  mutation UpdateCustomerNewsletterStatus($isSubscribed: Boolean!) {
    updateNewsletterSubscription(isSubscribed: $isSubscribed) {
      message
      value
    }
  }
`
export const RegisterCustomerDocument = gql`
  mutation RegisterCustomer($data: CustomerRegistrationData!) {
    registerCustomer(data: $data)
  }
`
export const LoginCustomerDocument = gql`
  mutation LoginCustomer($email: String!, $password: String!) {
    loginCustomer(email: $email, password: $password)
  }
`
export const LogoutCustomerDocument = gql`
  mutation LogoutCustomer {
    logoutCustomer
  }
`
export const ForgotPasswordDocument = gql`
  mutation ForgotPassword($email: String!) {
    forgotPassword(email: $email)
  }
`
export const ResetPasswordDocument = gql`
  mutation ResetPassword(
    $id: String!
    $resetToken: String!
    $pass: String!
    $confirm: String!
  ) {
    resetPassword(
      id: $id
      token: $resetToken
      password: $pass
      passwordConfirm: $confirm
    )
  }
`
export const GetMagentoCustomerDocument = gql`
  query GetMagentoCustomer {
    getCustomer {
      id
      authToken {
        token
        expires
      }
      email
      firstname
      lastname
      telephone
      isSubscribed
    }
  }
`
export const GetMagentoCustomerWithAddressesDocument = gql`
  query GetMagentoCustomerWithAddresses {
    getCustomer {
      ...CustomerFullInfo
    }
  }
  ${CustomerFullInfoFragmentDoc}
`
export const GetLastViewedDocument = gql`
  query GetLastViewed($skus: [String!]!) {
    getProductCollection(skus: $skus) {
      ...ProductCardData
    }
  }
  ${ProductCardDataFragmentDoc}
`
export const GetMetadataDocument = gql`
  query GetMetadata(
    $url: String!
    $query: [QueryParam]
    $entityType: EntityType
  ) {
    getPageMetadata(url: $url, query: $query, entityType: $entityType) {
      canonical_url
      next_url
      prev_url
      robots
      title
      description
      keywords
      og {
        ogType
        title
        description
        image
        url
        price
        currency
      }
    }
  }
`
export const GetPageDataDocument = gql`
  query GetPageData($url: String!, $query: [QueryParam]) {
    pageData(url: $url, query: $query) {
      breadcrumbs {
        url_path
        label
      }
      page {
        __typename
        ... on CatalogPage {
          ...CatalogPageData
        }
        ... on ProductPage {
          ...ProductPageData
        }
        ... on SplashPage {
          ...SplashPageData
        }
        ... on CMSContent {
          ...CMSContentPageData
        }
      }
      redirect {
        code
        url_path
      }
    }
  }
  ${CatalogPageDataFragmentDoc}
  ${ProductPageDataFragmentDoc}
  ${SplashPageDataFragmentDoc}
  ${CmsContentPageDataFragmentDoc}
`
export const GetPartnersDocument = gql`
  query GetPartners {
    getPartners {
      name
      nameEn
      postCode
      offices {
        name
        nameEn
        address
        addressEn
        location {
          lat
          lng
        }
      }
    }
  }
`
export const GetRelatedDocument = gql`
  query GetRelated($sku: String!) {
    getRelated(sku: $sku, relationType: UPSELL) {
      ...RelatedProductCard
    }
  }
  ${RelatedProductCardFragmentDoc}
`
export const GetProductStatusDocument = gql`
  query GetProductStatus($sku: String!) {
    getProduct(sku: $sku) {
      ...ProductStatus
    }
  }
  ${ProductStatusFragmentDoc}
`
export const SearchDocument = gql`
  query Search($term: String!) {
    search(searchQuery: $term) {
      products {
        ...ProductCardData
      }
      categories {
        name
        url_path
      }
      suggestions
      total_count
    }
  }
  ${ProductCardDataFragmentDoc}
`

export type SdkFunctionWrapper = <T>(
  action: (requestHeaders?: Record<string, string>) => Promise<T>,
  operationName: string,
  operationType?: string,
  variables?: any
) => Promise<T>

const defaultWrapper: SdkFunctionWrapper = (
  action,
  _operationName,
  _operationType,
  _variables
) => action()

export function getSdk(
  client: GraphQLClient,
  withWrapper: SdkFunctionWrapper = defaultWrapper
) {
  return {
    GetCurrentProductStatus(
      variables: GetCurrentProductStatusQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCurrentProductStatusQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCurrentProductStatusQuery>(
            GetCurrentProductStatusDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetCurrentProductStatus',
        'query',
        variables
      )
    },
    MakeFastOrder(
      variables: MakeFastOrderMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<MakeFastOrderMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<MakeFastOrderMutation>(
            MakeFastOrderDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'MakeFastOrder',
        'mutation',
        variables
      )
    },
    SendErrorReport(
      variables: SendErrorReportMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<SendErrorReportMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<SendErrorReportMutation>(
            SendErrorReportDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'SendErrorReport',
        'mutation',
        variables
      )
    },
    GetBlogPost(
      variables: GetBlogPostQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetBlogPostQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetBlogPostQuery>(GetBlogPostDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetBlogPost',
        'query',
        variables
      )
    },
    GetBlogPosts(
      variables: GetBlogPostsQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetBlogPostsQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetBlogPostsQuery>(GetBlogPostsDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetBlogPosts',
        'query',
        variables
      )
    },
    GetFeaturedBlogPost(
      variables?: GetFeaturedBlogPostQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetFeaturedBlogPostQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetFeaturedBlogPostQuery>(
            GetFeaturedBlogPostDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetFeaturedBlogPost',
        'query',
        variables
      )
    },
    GetSearchResultsPage(
      variables: GetSearchResultsPageQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetSearchResultsPageQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetSearchResultsPageQuery>(
            GetSearchResultsPageDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetSearchResultsPage',
        'query',
        variables
      )
    },
    CrateNewOrder(
      variables: CrateNewOrderMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CrateNewOrderMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CrateNewOrderMutation>(
            CrateNewOrderDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'CrateNewOrder',
        'mutation',
        variables
      )
    },
    GetShippingMethods(
      variables?: GetShippingMethodsQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetShippingMethodsQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetShippingMethodsQuery>(
            GetShippingMethodsDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetShippingMethods',
        'query',
        variables
      )
    },
    CalculateShipping(
      variables: CalculateShippingMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CalculateShippingMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CalculateShippingMutation>(
            CalculateShippingDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'CalculateShipping',
        'mutation',
        variables
      )
    },
    GetCities(
      variables: GetCitiesQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCitiesQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCitiesQuery>(GetCitiesDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetCities',
        'query',
        variables
      )
    },
    GetEcontCity(
      variables: GetEcontCityQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetEcontCityQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetEcontCityQuery>(GetEcontCityDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetEcontCity',
        'query',
        variables
      )
    },
    CollectTotals(
      variables: CollectTotalsMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CollectTotalsMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CollectTotalsMutation>(
            CollectTotalsDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'CollectTotals',
        'mutation',
        variables
      )
    },
    SendContactUs(
      variables: SendContactUsMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<SendContactUsMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<SendContactUsMutation>(
            SendContactUsDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'SendContactUs',
        'mutation',
        variables
      )
    },
    RequestAccountDeletion(
      variables?: RequestAccountDeletionMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<RequestAccountDeletionMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<RequestAccountDeletionMutation>(
            RequestAccountDeletionDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'RequestAccountDeletion',
        'mutation',
        variables
      )
    },
    GetCustomerAccountInformation(
      variables?: GetCustomerAccountInformationQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCustomerAccountInformationQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCustomerAccountInformationQuery>(
            GetCustomerAccountInformationDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetCustomerAccountInformation',
        'query',
        variables
      )
    },
    GetCustomerOrder(
      variables: GetCustomerOrderQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCustomerOrderQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCustomerOrderQuery>(
            GetCustomerOrderDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetCustomerOrder',
        'query',
        variables
      )
    },
    GetStaticContent(
      variables?: GetStaticContentQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetStaticContentQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetStaticContentQuery>(
            GetStaticContentDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetStaticContent',
        'query',
        variables
      )
    },
    SubscribeNewsletter(
      variables: SubscribeNewsletterMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<SubscribeNewsletterMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<SubscribeNewsletterMutation>(
            SubscribeNewsletterDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'SubscribeNewsletter',
        'mutation',
        variables
      )
    },
    GetCarBrandsContent(
      variables?: GetCarBrandsContentQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCarBrandsContentQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCarBrandsContentQuery>(
            GetCarBrandsContentDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetCarBrandsContent',
        'query',
        variables
      )
    },
    GetCarBrands(
      variables?: GetCarBrandsQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCarBrandsQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCarBrandsQuery>(GetCarBrandsDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetCarBrands',
        'query',
        variables
      )
    },
    GetCarModels(
      variables: GetCarModelsQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCarModelsQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCarModelsQuery>(GetCarModelsDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetCarModels',
        'query',
        variables
      )
    },
    GetCarModifications(
      variables: GetCarModificationsQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCarModificationsQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCarModificationsQuery>(
            GetCarModificationsDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetCarModifications',
        'query',
        variables
      )
    },
    GetCarsBuyoutData(
      variables?: GetCarsBuyoutDataQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCarsBuyoutDataQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCarsBuyoutDataQuery>(
            GetCarsBuyoutDataDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetCarsBuyoutData',
        'query',
        variables
      )
    },
    InitEmptyCart(
      variables?: InitEmptyCartQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<InitEmptyCartQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<InitEmptyCartQuery>(InitEmptyCartDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'InitEmptyCart',
        'query',
        variables
      )
    },
    GetCart(
      variables: GetCartQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCartQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCartQuery>(GetCartDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetCart',
        'query',
        variables
      )
    },
    AddItemToCart(
      variables: AddItemToCartMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<AddItemToCartMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<AddItemToCartMutation>(
            AddItemToCartDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'AddItemToCart',
        'mutation',
        variables
      )
    },
    RemoveItemFromCart(
      variables: RemoveItemFromCartMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<RemoveItemFromCartMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<RemoveItemFromCartMutation>(
            RemoveItemFromCartDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'RemoveItemFromCart',
        'mutation',
        variables
      )
    },
    ApplyCouponCode(
      variables: ApplyCouponCodeMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<ApplyCouponCodeMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<ApplyCouponCodeMutation>(
            ApplyCouponCodeDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'ApplyCouponCode',
        'mutation',
        variables
      )
    },
    GetCategoryTree(
      variables?: GetCategoryTreeQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCategoryTreeQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCategoryTreeQuery>(
            GetCategoryTreeDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetCategoryTree',
        'query',
        variables
      )
    },
    GetMostViewCategories(
      variables?: GetMostViewCategoriesQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetMostViewCategoriesQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetMostViewCategoriesQuery>(
            GetMostViewCategoriesDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetMostViewCategories',
        'query',
        variables
      )
    },
    GetCMSHtml(
      variables: GetCmsHtmlQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCmsHtmlQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCmsHtmlQuery>(GetCmsHtmlDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetCMSHtml',
        'query',
        variables
      )
    },
    UpdateProfile(
      variables: UpdateProfileMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<UpdateProfileMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<UpdateProfileMutation>(
            UpdateProfileDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'UpdateProfile',
        'mutation',
        variables
      )
    },
    UpdateInvoice(
      variables: UpdateInvoiceMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<UpdateInvoiceMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<UpdateInvoiceMutation>(
            UpdateInvoiceDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'UpdateInvoice',
        'mutation',
        variables
      )
    },
    DeleteAddress(
      variables: DeleteAddressMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<DeleteAddressMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<DeleteAddressMutation>(
            DeleteAddressDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'DeleteAddress',
        'mutation',
        variables
      )
    },
    UpdateAddress(
      variables: UpdateAddressMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<UpdateAddressMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<UpdateAddressMutation>(
            UpdateAddressDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'UpdateAddress',
        'mutation',
        variables
      )
    },
    UpdatePassword(
      variables: UpdatePasswordMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<UpdatePasswordMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<UpdatePasswordMutation>(
            UpdatePasswordDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'UpdatePassword',
        'mutation',
        variables
      )
    },
    UpdateCustomerNewsletterStatus(
      variables: UpdateCustomerNewsletterStatusMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<UpdateCustomerNewsletterStatusMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<UpdateCustomerNewsletterStatusMutation>(
            UpdateCustomerNewsletterStatusDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'UpdateCustomerNewsletterStatus',
        'mutation',
        variables
      )
    },
    RegisterCustomer(
      variables: RegisterCustomerMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<RegisterCustomerMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<RegisterCustomerMutation>(
            RegisterCustomerDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'RegisterCustomer',
        'mutation',
        variables
      )
    },
    LoginCustomer(
      variables: LoginCustomerMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<LoginCustomerMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<LoginCustomerMutation>(
            LoginCustomerDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'LoginCustomer',
        'mutation',
        variables
      )
    },
    LogoutCustomer(
      variables?: LogoutCustomerMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<LogoutCustomerMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<LogoutCustomerMutation>(
            LogoutCustomerDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'LogoutCustomer',
        'mutation',
        variables
      )
    },
    ForgotPassword(
      variables: ForgotPasswordMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<ForgotPasswordMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<ForgotPasswordMutation>(
            ForgotPasswordDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'ForgotPassword',
        'mutation',
        variables
      )
    },
    ResetPassword(
      variables: ResetPasswordMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<ResetPasswordMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<ResetPasswordMutation>(
            ResetPasswordDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'ResetPassword',
        'mutation',
        variables
      )
    },
    GetMagentoCustomer(
      variables?: GetMagentoCustomerQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetMagentoCustomerQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetMagentoCustomerQuery>(
            GetMagentoCustomerDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetMagentoCustomer',
        'query',
        variables
      )
    },
    GetMagentoCustomerWithAddresses(
      variables?: GetMagentoCustomerWithAddressesQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetMagentoCustomerWithAddressesQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetMagentoCustomerWithAddressesQuery>(
            GetMagentoCustomerWithAddressesDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetMagentoCustomerWithAddresses',
        'query',
        variables
      )
    },
    GetLastViewed(
      variables: GetLastViewedQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetLastViewedQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetLastViewedQuery>(GetLastViewedDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetLastViewed',
        'query',
        variables
      )
    },
    GetMetadata(
      variables: GetMetadataQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetMetadataQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetMetadataQuery>(GetMetadataDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetMetadata',
        'query',
        variables
      )
    },
    GetPageData(
      variables: GetPageDataQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetPageDataQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetPageDataQuery>(GetPageDataDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetPageData',
        'query',
        variables
      )
    },
    GetPartners(
      variables?: GetPartnersQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetPartnersQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetPartnersQuery>(GetPartnersDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetPartners',
        'query',
        variables
      )
    },
    GetRelated(
      variables: GetRelatedQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetRelatedQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetRelatedQuery>(GetRelatedDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetRelated',
        'query',
        variables
      )
    },
    GetProductStatus(
      variables: GetProductStatusQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetProductStatusQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetProductStatusQuery>(
            GetProductStatusDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetProductStatus',
        'query',
        variables
      )
    },
    Search(
      variables: SearchQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<SearchQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<SearchQuery>(SearchDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'Search',
        'query',
        variables
      )
    },
  }
}
export type Sdk = ReturnType<typeof getSdk>
