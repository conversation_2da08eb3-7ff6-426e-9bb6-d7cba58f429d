const CLIENT_REST_DOMAIN = process.env.NEXT_PUBLIC_ROOT_DOMAIN ?? 'https://carco.bg'
const SERVER_REST_DOMAIN = process.env.SERVER_SIDE_REST_DOMAIN ?? CLIENT_REST_DOMAIN

export const REST_DOMAIN = typeof window === 'undefined' ? SERVER_REST_DOMAIN : CLIENT_REST_DOMAIN

export async function apiPost<T, R>(
  route: string,
  data?: T,
  header?: { [key: string]: string }
): Promise<R> {
  route = route.replace(/^\/+/, '')
  const fullUrl = `${REST_DOMAIN}/${route}`
  const res = await fetch(fullUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
      'Authorization': '',
      ...(header ?? {}),
    },
    body: JSON.stringify(data),
  })

  return await res.json()
}

export interface ApiCallResult<T> {
  data: T
  error?: string
}
