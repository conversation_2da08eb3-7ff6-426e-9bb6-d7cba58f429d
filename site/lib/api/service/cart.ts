import {
  getCartIdToken,
  getCustomerAuthTokenCookie,
  saveCartIdToken,
} from '@lib/utils/cookie'
import { MagentoBackend } from '@lib/api/graphql'
import { Cart, MagentoCartFragment } from '@lib/generated/graphql'
import { IMagentoCart } from '@lib/model/Cart/MagentoCart'
import { AuthHeaderKey, RecaptchaHeaderKey } from '@atom/ReCaptchaV3Script'

export function isValidCartId(cartId: any) {
  return typeof cartId === 'string' && cartId.length > 8
}

export async function getValidCartToken(): Promise<string> {
  let _token = getCartIdToken()
  if (!isValidCartId(_token)) {
    let _tokenResponse = await MagentoBackend.InitEmptyCart()
    _token = _tokenResponse.getEmptyCart.id
    saveCartIdToken(_token)
  }

  return _token
}

export async function addToMagentoQuote(
  cartId: string,
  sku: string
): Promise<IMagentoCart | null> {
  if (!isValidCartId(cartId)) {
    return null
  }

  try {
    const response = await MagentoBackend.AddItemToCart(
      {
        cartId: cartId,
        sku: sku,
      },
      {
        [RecaptchaHeaderKey]: 'NON',
        [AuthHeaderKey]: getCustomerAuthTokenCookie(),
      }
    )

    if (response.addToCart) {
      return response.addToCart as Cart
    } else {
      console.error('response', response)
      throw new Error('No addToCart response')
    }
  } catch (e) {
    console.error(e)
    throw e
  }

  return null
}

export async function getMagentoCart(
  cartId: string,
  token?: string
): Promise<MagentoCartFragment | null> {
  if (!isValidCartId(cartId)) {
    return null
  }

  const _token = token ?? getCustomerAuthTokenCookie()

  const response = await MagentoBackend.GetCart(
    {
      cartId: cartId,
    },
    {
      [AuthHeaderKey]: _token,
    }
  )

  let cart = null
  if (response.getCart) {
    cart = response.getCart
  }

  return cart
}

export async function removeProductFromCart(
  cartId: string,
  sku: string
): Promise<IMagentoCart | null> {
  if (!isValidCartId(cartId)) {
    return null
  }

  let cart = null
  try {
    const response = await MagentoBackend.RemoveItemFromCart(
      {
        cartId,
        sku,
      },
      {
        [RecaptchaHeaderKey]: 'NON',
        [AuthHeaderKey]: getCustomerAuthTokenCookie(),
      }
    )

    if (response.removeFromCart) {
      cart = response.removeFromCart
    }
  } catch (e) {
    console.error(e)
    throw e
  }

  return cart
}

export async function applyCouponCode(
  cartId: string,
  couponCode: string
): Promise<IMagentoCart | null> {
  if (!isValidCartId(cartId)) {
    return null
  }

  try {
    const response = await MagentoBackend.ApplyCouponCode(
      {
        cartId,
        coupon: couponCode,
      },
      {
        [RecaptchaHeaderKey]: 'NON',
        [AuthHeaderKey]: getCustomerAuthTokenCookie(),
      }
    )

    if (response.applyCouponToCart) {
      return response.applyCouponToCart
    }

    throw new Error('Невалиден купон')
  } catch (e) {
    console.error(e)
    throw e
  }
}
