/** @type {import('next').NextConfig} */
// next.config.js
module.exports = {
  // reactStrictMode: false,
  productionBrowserSourceMaps: false,
  env: {
    NEXT_PUBLIC_ROOT_DOMAIN: process.env.NEXT_PUBLIC_ROOT_DOMAIN ?? 'https://carco.bg',
    NEXT_PUBLIC_IMAGE_DOMAIN: process.env.NEXT_PUBLIC_IMAGE_DOMAIN ?? 'https://carco.bg',
    NEXT_PUBLIC_FRONTEND_GRAPTHQL_ENDPOINT: process.env.NEXT_PUBLIC_FRONTEND_GRAPTHQL_ENDPOINT ?? "http://localhost:9420/graphql",
  },
  experimental: { appDir: true },
  images: {
    minimumCacheTTL: 86400,
    unoptimized: true, // Disable image optimization for development
    domains: [
      'localhost',
      'localhost:3000',
      'cdn2.carco.bg',
      'carco.bg',
      'server',
      'carco.localhost',
    ],
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '',
        pathname: '/media/**',
      },
      {
        protocol: 'http',
        hostname: 'server',
        port: '',
        pathname: '/media/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'carco.bg',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn2.carco.bg',
        port: '',
        pathname: '/**',
      },
    ],
  },
  typescript: {
    ignoreBuildErrors: false,
  },
  reactStrictMode: true,
}
