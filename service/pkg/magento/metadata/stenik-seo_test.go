package metadata

import (
	"github.com/stretchr/testify/assert"
	"testing"
	"text/template"
)

func TestTemplateCompile(t *testing.T) {
	temp := MagentoVariableTemplate{
		Template: "{{var product.name}}",
	}
	attrs, templateText := temp.CompileProduct()

	if templateText != "{{.name}}" {
		t.<PERSON>("Expected '{{.name}}', got '%s'", templateText)
	}

	if attrs["product.name"] != "name" {
		t.<PERSON><PERSON>("Expected 'name', got '%s'", attrs["product.name"])
	}

	temp.Template = "{{var product.name}} {{var product.price}}"
	attrs, templateText = temp.CompileProduct()

	if templateText != "{{.name}} {{.price}}" {
		t.<PERSON><PERSON><PERSON>("Expected '{{.name}} {{.price}}', got '%s'", templateText)
	}

	if attrs["product.name"] != "name" {
		t.<PERSON><PERSON><PERSON>("Expected 'name', got '%s'", attrs["product.name"])
	}

	if attrs["product.price"] != "price" {
		t.<PERSON>("Expected 'price', got '%s'", attrs["product.price"])
	}

	res := ParseTemplate(templateText, map[string]string{"name": "test", "price": "100"})
	if res != "test 100" {
		t.Errorf("Expected 'test 100', got '%s'", res)
	}
}

func TestTemplateCompileIfStatements(t *testing.T) {

	temp := MagentoVariableTemplate{
		Template: "{{if category.meta_description}}{{var category.meta_description}} {{var pager/page_string_dashed}}{{else}}TEST {{var category.name}} {{var pager/page_string_comma}}{{/if}}",
	}
	_, templateText := temp.CompileCategory()

	if templateText != "{{if .meta_description}}{{.meta_description}} {{.PagerStripDashed}}{{else}}TEST {{.name}} {{.PagerStripComma}}{{end}}" {
		t.Errorf(`Expected 
'{{if .meta_description}}{{.meta_description}} {{.PagerStripDashed}}{{else}}TEST {{.name}} {{.PagerStripComma}}{{end}}'
'%s'`, templateText)
	}

	variables := GetPagerVariables(2)
	params := map[string]string{
		"name":             "test-name",
		"meta_description": "test-description",
	}

	for k, v := range variables {
		params[k] = v
	}

	res := ParseTemplate(templateText, params)
	if res != "test-description - страница 2" {
		t.Errorf("Expected \n'test-description - страница 2'\n'%s'", res)
	}

	params = map[string]string{
		"name": "test-name",
	}
	for k, v := range variables {
		params[k] = v
	}

	res = ParseTemplate(templateText, params)
	if res != "TEST test-name , страница 2" {
		t.Errorf("Expected \n'TEST test-name, страница 2'\n'%s'", res)
	}
}

func TestTemplateCompileDepends(t *testing.T) {
	temp := MagentoVariableTemplate{
		Template: "{{depends category.meta_description}}{{var category.meta_description}} {{/depend}}",
	}
	_, templateText := temp.CompileCategory()

	assert.Equal(t, "{{if .meta_description}}{{.meta_description}} {{end}}", templateText)

	temp.Template =
		"{{depends category.meta_description}}{{var category.meta_description}} {{if category.test}}{{var category.test}} {{/if}} {{/depend}}{{if category.name}}{{var category.name}} {{/if}}"

	_, templateText = temp.CompileCategory()
	assert.Equal(t, "{{if .meta_description}}{{.meta_description}} {{if .test}}{{.test}} {{end}} {{end}}{{if .name}}{{.name}} {{end}}", templateText)

	_, err := template.New("test").Parse(templateText)
	assert.NoError(t, err)
}
