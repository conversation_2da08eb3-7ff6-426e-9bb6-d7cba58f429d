package backend

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"pfgbulgaria/carco-graphapi/graph/model"
	"pfgbulgaria/carco-graphapi/pkg/api"
	config2 "pfgbulgaria/carco-graphapi/pkg/config"
	"pfgbulgaria/carco-graphapi/pkg/utils"
	"time"
)

type MagentoEndpoint string

const (
	CalculateShipping MagentoEndpoint = "sales/calculateShipping"
	CollectTotals     MagentoEndpoint = "sales/collectTotals"
	ApplyCouponCode   MagentoEndpoint = "sales/applyCouponCode"
	CreateOrder       MagentoEndpoint = "sales/createNewOrder"

	GetCMSContent MagentoEndpoint = "content/getCmsContent"

	RegisterCustomer MagentoEndpoint = "customer_auth/registerCustomer"
	LoginCustomer    MagentoEndpoint = "customer_auth/login"
	ForgotPassword   MagentoEndpoint = "customer_auth/forgotPassword"
	ResetPassword    MagentoEndpoint = "customer_auth/resetPassword"

	CustomerUpdatePassword MagentoEndpoint = "customer_account/updatePassword"
	CustomerRequestForget  MagentoEndpoint = "customer_account/requestForget"

	NewsletterSubscribe   MagentoEndpoint = "newsletter/subscribe"
	NewsletterUnsubscribe MagentoEndpoint = "newsletter/unsubscribe"

	SendContactMessage       MagentoEndpoint = "contacts/sendMessage"
	SendCarBuyoutMessage     MagentoEndpoint = "contacts/sendCarBuyoutMessage"
	SendEmailTemplateMessage MagentoEndpoint = "email/sendTemplate"

	ProductFastOrder MagentoEndpoint = "product/fastOrder"
)

type ShippingAddressData struct {
	ShippingMethodCode string             `json:"shippingMethodCode"`
	Address            model.AddressData  `json:"address"`
	Customer           model.CustomerData `json:"customer"`
}

type MagentoInvoiceData struct {
	CustomerInvoice string `json:"customer_invoice"`
	InvoiceType     string `json:"customer_invoice_type"`
	// company
	CompanyName    string `json:"customer_invoice_company_name"`
	CompanyPIC     string `json:"customer_invoice_company_pic"`
	CompanyURN     string `json:"customer_invoice_company_urn"`
	CompanyVAT     string `json:"customer_invoice_company_vat"`
	CompanyAddress string `json:"customer_invoice_company_addr"`
	// personal
	PersonalName string `json:"customer_invoice_personal_name"`
	PersonalPIN  string `json:"customer_invoice_personal_pin"`
	PersonalAddr string `json:"customer_invoice_personal_addr"`
}

type GenericMagentoRequest interface{}

type MagentoOrderData struct {
	CartID            string              `json:"cartId"`
	Shipping          ShippingAddressData `json:"shipping"`
	Invoice           MagentoInvoiceData  `json:"invoice"`
	PromoCode         string              `json:"promoCode"`
	PaymentMethodCode string              `json:"paymentMethod"`
	CustomerNote      string              `json:"customerNote"`
}

type MagentoCmsContentData struct {
	Id   string `json:"id"`
	Type string `json:"type"`
}

func (e MagentoEndpoint) PostDataToMagento(v interface{}) (MagentoResponse, error) {
	res := make(MagentoResponse)

	b, err := json.Marshal(v)
	if err != nil {
		return res, errors.New("невалидни данни за " + string(e))
	}

	// Log request data for debugging
	utils.Notice(fmt.Sprintf("Sending request to %s: %s", string(e), string(b)))

	client, err := api.GetHttpClient()
	if err != nil {
		return res, err
	}

	backendRequest, err := http.NewRequest(
		http.MethodPost,
		e.GetUrl(),
		bytes.NewReader(b),
	)
	backendRequest.Header.Set("PFG-Auth-Service-Token", config2.GetConfig().Magento.AuthToken)
	backendRequest.Header.Set("X-PFG-SERVICE", "graphapi")
	backendRequest.Header.Set("Content-Type", "application/json")

	AddXDebugCookieToRequest(backendRequest)

	start := time.Now()
	backendResponse, err2 := client.Do(backendRequest)
	utils.LogTimeElapsed(start, "magentoAPI->"+string(e))

	if err2 != nil {
		return res, err2
	} else if backendResponse.StatusCode != http.StatusOK {
		if backendResponse.StatusCode >= http.StatusInternalServerError {
			utils.Notice(GetErrorMessageFromRawResponse(backendResponse.Body))

			return res, errors.New("възникна грешка при изпълнение на заявката")
		} else if backendResponse.StatusCode == http.StatusNotFound {
			return res, errors.New("Греше път: " + string(e))
		} else if backendResponse.StatusCode == http.StatusBadRequest {
			return res, errors.New(GetErrorMessageFromRawResponse(backendResponse.Body))
		}

		return res, fmt.Errorf("status: %d: Error: %s",
			backendResponse.StatusCode,
			GetErrorMessageFromRawResponse(backendResponse.Body),
		)
	}

	var body []byte
	if body, err = io.ReadAll(backendResponse.Body); err != nil {
		return res, err
	}

	if err != nil {
		utils.ErrorWarning(err)
		return res, err
	}

	if len(body) < 1 {
		return res, errors.New("празен бакенд респонс")
	}

	// Log raw response for debugging
	utils.Notice(fmt.Sprintf("Raw response from %s (status: %d): %s", string(e), backendResponse.StatusCode, string(body)))

	if err = json.Unmarshal(body, &res); err != nil {
		utils.ErrorWarning(fmt.Errorf("JSON unmarshal error for endpoint %s: %v. Raw response body: %s", string(e), err, string(body)))
		return res, errors.New("грешка в бакенд респонса")
	}

	return res, nil
}

func (e MagentoEndpoint) GetUrl() string {
	return fmt.Sprintf("%s/%s",
		config2.GetConfig().Magento.RestApi,
		e,
	)
}

func GetErrorMessageFromRawResponse(reader io.ReadCloser) string {
	var body []byte
	var err error
	if body, err = io.ReadAll(reader); err != nil {
		return err.Error()
	}

	var val map[string]interface{}
	json.Unmarshal(body, &val)

	// has error key
	if _, ok := val["error"]; ok {
		return val["error"].(string)
	}

	return "Unknown error: " + string(body)
}
