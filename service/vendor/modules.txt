# github.com/99designs/gqlgen v0.17.19
## explicit; go 1.16
github.com/99designs/gqlgen
github.com/99designs/gqlgen/api
github.com/99designs/gqlgen/codegen
github.com/99designs/gqlgen/codegen/config
github.com/99designs/gqlgen/codegen/templates
github.com/99designs/gqlgen/complexity
github.com/99designs/gqlgen/graphql
github.com/99designs/gqlgen/graphql/errcode
github.com/99designs/gqlgen/graphql/executor
github.com/99designs/gqlgen/graphql/handler
github.com/99designs/gqlgen/graphql/handler/extension
github.com/99designs/gqlgen/graphql/handler/lru
github.com/99designs/gqlgen/graphql/handler/transport
github.com/99designs/gqlgen/graphql/introspection
github.com/99designs/gqlgen/internal/code
github.com/99designs/gqlgen/internal/imports
github.com/99designs/gqlgen/internal/rewrite
github.com/99designs/gqlgen/plugin
github.com/99designs/gqlgen/plugin/federation
github.com/99designs/gqlgen/plugin/federation/fieldset
github.com/99designs/gqlgen/plugin/modelgen
github.com/99designs/gqlgen/plugin/resolvergen
github.com/99designs/gqlgen/plugin/servergen
# github.com/agnivade/levenshtein v1.1.1
## explicit; go 1.13
github.com/agnivade/levenshtein
# github.com/cespare/xxhash/v2 v2.1.2
## explicit; go 1.11
github.com/cespare/xxhash/v2
# github.com/cpuguy83/go-md2man/v2 v2.0.1
## explicit; go 1.11
github.com/cpuguy83/go-md2man/v2/md2man
# github.com/davecgh/go-spew v1.1.1
## explicit
github.com/davecgh/go-spew/spew
# github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f
## explicit
github.com/dgryski/go-rendezvous
# github.com/fatih/color v1.13.0
## explicit; go 1.13
github.com/fatih/color
# github.com/gin-contrib/cors v1.4.0
## explicit; go 1.13
github.com/gin-contrib/cors
# github.com/gin-contrib/sse v0.1.0
## explicit; go 1.12
github.com/gin-contrib/sse
# github.com/gin-gonic/gin v1.8.1
## explicit; go 1.18
github.com/gin-gonic/gin
github.com/gin-gonic/gin/binding
github.com/gin-gonic/gin/internal/bytesconv
github.com/gin-gonic/gin/internal/json
github.com/gin-gonic/gin/render
# github.com/go-playground/locales v0.14.0
## explicit; go 1.13
github.com/go-playground/locales
github.com/go-playground/locales/currency
# github.com/go-playground/universal-translator v0.18.0
## explicit; go 1.13
github.com/go-playground/universal-translator
# github.com/go-playground/validator/v10 v10.11.1
## explicit; go 1.13
github.com/go-playground/validator/v10
# github.com/go-redis/redis/v9 v9.0.0-beta.2
## explicit; go 1.17
github.com/go-redis/redis/v9
github.com/go-redis/redis/v9/internal
github.com/go-redis/redis/v9/internal/hashtag
github.com/go-redis/redis/v9/internal/hscan
github.com/go-redis/redis/v9/internal/pool
github.com/go-redis/redis/v9/internal/proto
github.com/go-redis/redis/v9/internal/rand
github.com/go-redis/redis/v9/internal/util
# github.com/go-sql-driver/mysql v1.6.0
## explicit; go 1.10
github.com/go-sql-driver/mysql
# github.com/goccy/go-json v0.9.7
## explicit; go 1.12
github.com/goccy/go-json
github.com/goccy/go-json/internal/decoder
github.com/goccy/go-json/internal/encoder
github.com/goccy/go-json/internal/encoder/vm
github.com/goccy/go-json/internal/encoder/vm_color
github.com/goccy/go-json/internal/encoder/vm_color_indent
github.com/goccy/go-json/internal/encoder/vm_indent
github.com/goccy/go-json/internal/errors
github.com/goccy/go-json/internal/runtime
# github.com/google/uuid v1.3.0
## explicit
github.com/google/uuid
# github.com/gorilla/websocket v1.5.0
## explicit; go 1.12
github.com/gorilla/websocket
# github.com/hashicorp/golang-lru v0.5.4
## explicit; go 1.12
github.com/hashicorp/golang-lru
github.com/hashicorp/golang-lru/simplelru
# github.com/jinzhu/inflection v1.0.0
## explicit
github.com/jinzhu/inflection
# github.com/jinzhu/now v1.1.5
## explicit; go 1.12
github.com/jinzhu/now
# github.com/json-iterator/go v1.1.12
## explicit; go 1.12
github.com/json-iterator/go
# github.com/leodido/go-urn v1.2.1
## explicit; go 1.13
github.com/leodido/go-urn
# github.com/mattn/go-colorable v0.1.12
## explicit; go 1.13
github.com/mattn/go-colorable
# github.com/mattn/go-isatty v0.0.14
## explicit; go 1.12
github.com/mattn/go-isatty
# github.com/mitchellh/mapstructure v1.3.1
## explicit; go 1.14
github.com/mitchellh/mapstructure
# github.com/modern-go/concurrent v0.0.0-20180228061459-e0a39a4cb421
## explicit
github.com/modern-go/concurrent
# github.com/modern-go/reflect2 v1.0.2
## explicit; go 1.12
github.com/modern-go/reflect2
# github.com/pelletier/go-toml/v2 v2.0.1
## explicit; go 1.16
github.com/pelletier/go-toml/v2
github.com/pelletier/go-toml/v2/internal/ast
github.com/pelletier/go-toml/v2/internal/danger
github.com/pelletier/go-toml/v2/internal/tracker
# github.com/pmezard/go-difflib v1.0.0
## explicit
github.com/pmezard/go-difflib/difflib
# github.com/russross/blackfriday/v2 v2.1.0
## explicit
github.com/russross/blackfriday/v2
# github.com/stretchr/testify v1.8.0
## explicit; go 1.13
github.com/stretchr/testify/assert
# github.com/ugorji/go/codec v1.2.7
## explicit; go 1.11
github.com/ugorji/go/codec
# github.com/urfave/cli/v2 v2.8.1
## explicit; go 1.18
github.com/urfave/cli/v2
# github.com/vektah/gqlparser/v2 v2.5.1
## explicit; go 1.16
github.com/vektah/gqlparser/v2
github.com/vektah/gqlparser/v2/ast
github.com/vektah/gqlparser/v2/gqlerror
github.com/vektah/gqlparser/v2/lexer
github.com/vektah/gqlparser/v2/parser
github.com/vektah/gqlparser/v2/validator
github.com/vektah/gqlparser/v2/validator/rules
# github.com/xrash/smetrics v0.0.0-20201216005158-039620a65673
## explicit
github.com/xrash/smetrics
# golang.org/x/crypto v0.0.0-20211215153901-e495a2d5b3d3
## explicit; go 1.17
golang.org/x/crypto/openpgp/errors
golang.org/x/crypto/sha3
# golang.org/x/mod v0.6.0-dev.0.20220106191415-9b9b3d81d5e3
## explicit; go 1.17
golang.org/x/mod/internal/lazyregexp
golang.org/x/mod/module
golang.org/x/mod/semver
# golang.org/x/net v0.0.0-20220425223048-2871e0cb64e4
## explicit; go 1.17
golang.org/x/net/html
golang.org/x/net/html/atom
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/h2c
golang.org/x/net/http2/hpack
golang.org/x/net/idna
# golang.org/x/sys v0.0.0-20220422013727-9388b58f7150
## explicit; go 1.17
golang.org/x/sys/cpu
golang.org/x/sys/execabs
golang.org/x/sys/internal/unsafeheader
golang.org/x/sys/unix
# golang.org/x/text v0.3.7
## explicit; go 1.17
golang.org/x/text/cases
golang.org/x/text/internal
golang.org/x/text/internal/language
golang.org/x/text/internal/language/compact
golang.org/x/text/internal/tag
golang.org/x/text/language
golang.org/x/text/secure/bidirule
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
# golang.org/x/tools v0.1.10
## explicit; go 1.17
golang.org/x/tools/go/ast/astutil
golang.org/x/tools/go/gcexportdata
golang.org/x/tools/go/internal/gcimporter
golang.org/x/tools/go/internal/packagesdriver
golang.org/x/tools/go/packages
golang.org/x/tools/imports
golang.org/x/tools/internal/event
golang.org/x/tools/internal/event/core
golang.org/x/tools/internal/event/keys
golang.org/x/tools/internal/event/label
golang.org/x/tools/internal/fastwalk
golang.org/x/tools/internal/gocommand
golang.org/x/tools/internal/gopathwalk
golang.org/x/tools/internal/imports
golang.org/x/tools/internal/packagesinternal
golang.org/x/tools/internal/typeparams
golang.org/x/tools/internal/typesinternal
# golang.org/x/xerrors v0.0.0-20200804184101-5ec99f83aff1
## explicit; go 1.11
golang.org/x/xerrors
golang.org/x/xerrors/internal
# google.golang.org/protobuf v1.28.0
## explicit; go 1.11
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/proto
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
# gopkg.in/yaml.v2 v2.4.0
## explicit; go 1.15
gopkg.in/yaml.v2
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# gorm.io/driver/mysql v1.3.6
## explicit; go 1.14
gorm.io/driver/mysql
# gorm.io/gorm v1.23.10
## explicit; go 1.16
gorm.io/gorm
gorm.io/gorm/callbacks
gorm.io/gorm/clause
gorm.io/gorm/logger
gorm.io/gorm/migrator
gorm.io/gorm/schema
gorm.io/gorm/utils
