// Copyright 2019 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by generate-protos. DO NOT EDIT.

package genid

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
)

const File_google_protobuf_empty_proto = "google/protobuf/empty.proto"

// Names for google.protobuf.Empty.
const (
	Empty_message_name     protoreflect.Name     = "Empty"
	Empty_message_fullname protoreflect.FullName = "google.protobuf.Empty"
)
