extend type Mutation {
  calculateShipping(
    cartId: String!
    input: ShippingDataInput
  ): ShippingMethodTotal!
  collectTotals(cartId: String!, input: CalculateTotalsInput): Cart!
  applyCouponToCart(cartId: String!, couponCode: String!): Cart!
  submitOrder(cartId: String!, input: SubmitOrderInput!): NewOrderResult!
}

type NewOrderResult {
  orderNumber: String
  status: String
  paymentMethod: PaymentMethod
  shippingMethod: ShippingMethod
  redirect: OrderRedirect
  order: Order
}

type OrderRedirect {
  url: String!
  data: [MapValue!]
  method: String! # GET or POST
  autoRedirect: Boolean! # Whether to auto-redirect or show form
}

type PaymentMethod {
  code: String!
  title: String!
  additional: [MapValue!]
  redirectRequired: Boolean! # Whether this payment method requires redirect
}

input ShippingDataInput {
  shippingMethod: ShippingMethodCode
  address: AddressData!
  customer: CustomerData!
}

input CalculateTotalsInput {
  promoCode: String
  shipping: ShippingDataInput
}

input CustomerData {
  email: String!
  phone: String!
  firstname: String!
  lastname: String!
}

input AddressData {
  cityId: String
  postCode: String
  officeId: String
  street: String
  streetNumber: String
  quarterId: String
  note: String
}

input SubmitOrderInput {
  promoCode: String!
  paymentMethod: String!
  shipping: ShippingDataInput!
  invoice: InvoiceInput!
  note: String!
}
