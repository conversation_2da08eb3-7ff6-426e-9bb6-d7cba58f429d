schema:
  - schema/**/*.gql

exec:
  filename: graph/generated/generated.go
  package: generated

model:
  filename: graph/model/generated_models.go
  package: model

# Where should the resolver implementations go?
resolver:
  layout: follow-schema
  dir: graph/resolver
  package: resolver

autobind:
  - "pfgbulgaria/carco-graphapi/graph/model"

models:
  Int64:
    model:
      - github.com/99designs/gqlgen/graphql.Int64
  ID:
    model:
      - github.com/99designs/gqlgen/graphql.ID
      - github.com/99designs/gqlgen/graphql.Int64
  Int:
    model:
      - github.com/99designs/gqlgen/graphql.Int
      - github.com/99designs/gqlgen/graphql.Int64
      - github.com/99designs/gqlgen/graphql.Int32
  ##################################
  # Catalog Models
  ##################################
  CatalogPage:
    fields:
      products:
        resolver: true
      page_info:
        resolver: true
      filters:
        resolver: true
  SplashPage:
    fields:
      products:
        resolver: true
      page_info:
        resolver: true
      filters:
        resolver: true
  ##################################
  # Product Models
  ##################################
  MagentoProduct:
    fields:
      brand:
        resolver: true
      status:
        resolver: true
      freeAfterXSeconds:
        resolver: true
      description:
        resolver: true
      additional:
        resolver: true
      gallery:
        resolver: true
  ##################################
  # Checkout Models
  ##################################
  Cart:
    fields:
      totals:
        resolver: true
      items:
        resolver: true
  City:
    fields:
      offices:
        resolver: true
      quarters:
        resolver: true
  ##################################
  # Customer Models
  ##################################
  Customer:
    fields:
      addresses:
        resolver: true
      orders:
        resolver: true
  ##################################
  # Customer Models
  ##################################
  BlogCategory:
    fields:
      posts:
        resolver: true
