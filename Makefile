.PHONY: help
.DEFAULT_GOAL := help

setup:
	@docker-compose build --no-cache graphapi

down:
	@docker-compose down

up:
	@docker-compose up -d graphapi

ssh:
	@make up
	@docker-compose exec graphapi bash

server:
	@make up
	@docker-compose exec graphapi go run server.go

help:
	@echo "Available targets:"
	@echo "  setup: Build the base image"
	@echo "  down: Stop and remove the containers"
	@echo "  up: Start the containers"
	@echo "  ssh: Start the containers and open a shell"
