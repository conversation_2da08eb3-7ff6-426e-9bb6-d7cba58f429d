.DEFAULT_GOAL := help

down:
	@docker-compose down

setup:
	@docker-compose build

up:
	@docker-compose up -d server

cache-clear:
	@docker-compose exec cache redis-cli flushall

import-db:
	@make up
	@docker-compose exec db bash /tmp/import/import-db.sh

ssh:
	@make up
	@docker-compose exec magento bash

server_ssh:
	@make up
	@docker-compose exec server sh

graphapi-ssh:
	@cd graphapi && make up && docker-compose exec graphapi bash

frontend-ssh:
	@cd frontend && make up && docker-compose exec frontend bash

logs:
	@docker-compose logs -f -t
