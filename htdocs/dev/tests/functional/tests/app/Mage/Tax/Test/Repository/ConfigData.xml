<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Mage\Core\Test\Repository\ConfigData">
        <dataset name="shipping_tax_class_taxable_goods">
            <field name="tax/classes/shipping_tax_class" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Taxable Goods</item>
                <item name="value" xsi:type="string">2</item>
            </field>
        </dataset>

        <dataset name="shipping_tax_class_taxable_goods_rollback">
            <field name="tax/classes/shipping_tax_class" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">None</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>

        <dataset name="tax_calculation_base_on_shipping_origin">
            <field name="tax/calculation/based_on" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Shipping Origin</item>
                <item name="value" xsi:type="string">origin</item>
            </field>
        </dataset>

        <dataset name="tax_calculation_base_on_shipping_origin_rollback">
            <field name="tax/calculation/based_on" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Shipping Address</item>
                <item name="value" xsi:type="string">shipping</item>
            </field>
        </dataset>

        <dataset name="cross_border_enabled_price_incl_tax">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Before Discount</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/display/type" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/display/shipping" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/cart_display/price" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/cart_display/subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/cart_display/shipping" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/cart_display/grandtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/cart_display/full_summary" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/enable" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/display_list" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_sales" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/apply_vat" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Not Taxed</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/include_in_subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>

        <dataset name="cross_border_enabled_price_excl_tax">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Before Discount</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/display/type" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/display/shipping" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/cart_display/price" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/cart_display/subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/cart_display/shipping" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/cart_display/grandtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/cart_display/full_summary" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/enable" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/display_list" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_sales" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/apply_vat" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Not Taxed</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/include_in_subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>

        <dataset name="default_tax_configuration">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Before Discount</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/display/type" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/display/shipping" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/cart_display/price" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/cart_display/subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/cart_display/shipping" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/cart_display/grandtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/cart_display/full_summary" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/sales_display/price" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/sales_display/subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/sales_display/shipping" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/sales_display/grandtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/sales_display/full_summary" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/sales_display/zero_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/enable" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/display_list" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_sales" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/apply_vat" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Not Taxed</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/include_in_subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>

        <dataset name="display_including_tax">
            <field name="tax/display/type" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/display/shipping" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/cart_display/price" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/cart_display/subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/cart_display/shipping" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/cart_display/grandtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/cart_display/full_summary" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/sales_display/price" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/sales_display/subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/sales_display/shipping" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/sales_display/grandtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
        </dataset>

        <dataset name="display_excluding_including_tax">
            <field name="tax/display/type" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including and Excluding Tax</item>
                <item name="value" xsi:type="string">3</item>
            </field>
            <field name="tax/display/shipping" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including and Excluding Tax</item>
                <item name="value" xsi:type="string">3</item>
            </field>
            <field name="tax/cart_display/price" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including and Excluding Tax</item>
                <item name="value" xsi:type="string">3</item>
            </field>
            <field name="tax/cart_display/subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including and Excluding Tax</item>
                <item name="value" xsi:type="string">3</item>
            </field>
            <field name="tax/cart_display/shipping" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including and Excluding Tax</item>
                <item name="value" xsi:type="string">3</item>
            </field>
            <field name="tax/cart_display/grandtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/cart_display/full_summary" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/sales_display/price" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including and Excluding Tax</item>
                <item name="value" xsi:type="string">3</item>
            </field>
            <field name="tax/sales_display/subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including and Excluding Tax</item>
                <item name="value" xsi:type="string">3</item>
            </field>
            <field name="tax/sales_display/shipping" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including and Excluding Tax</item>
                <item name="value" xsi:type="string">3</item>
            </field>
            <field name="tax/sales_display/grandtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
        </dataset>

        <dataset name="row_cat_incl_ship_excl_after_disc_on_excl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Row Total</item>
                <item name="value" xsi:type="string">ROW_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">After Discount</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>

        <dataset name="row_cat_excl_ship_incl_before_disc_on_incl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Row Total</item>
                <item name="value" xsi:type="string">ROW_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Before Discount</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>

        <dataset name="total_cat_excl_ship_incl_after_disc_on_excl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">After Discount</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>

        <dataset name="row_cat_incl_ship_excl_before_disc_on_incl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Row Total</item>
                <item name="value" xsi:type="string">ROW_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Before Discount</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>

        <dataset name="unit_cat_incl_ship_incl_before_disc_on_incl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string" />
                <item name="value" xsi:type="string">UNIT_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Before Discount</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>

        <dataset name="total_cat_excl_ship_incl_before_disc_on_incl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Before Discount</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>

        <dataset name="unit_cat_excl_ship_excl_after_disc_on_excl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Unit Price</item>
                <item name="value" xsi:type="string">UNIT_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">After Discount</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>

        <dataset name="total_cat_incl_ship_excl_before_disc_on_excl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Before Discount</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>

        <dataset name="total_cat_excl_ship_incl_after_disc_on_incl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">After Discount</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>

        <dataset name="unit_cat_excl_ship_incl_after_disc_on_excl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Unit Price</item>
                <item name="value" xsi:type="string">UNIT_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">After Discount</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>
    </repository>
</config>
