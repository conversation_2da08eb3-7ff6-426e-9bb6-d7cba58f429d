<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Tax\Test\TestCase\TaxCalculationTest" summary="TaxCalculationTest">
        <variation name="TaxCalculationTestVariation1" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" method="test">
            <data name="description" xsi:type="string">Simple product tier price with sales rule, customer tax equals store tax and catalog price including tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods, row_cat_incl_ship_excl_after_disc_on_excl, display_excluding_including_tax</data>
            <data name="product" xsi:type="string">catalogProductSimple::simple_with_tier_price</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups_no_coupon</data>
            <data name="taxRule" xsi:type="string">customer_equals_store_rate</data>
            <data name="customer/dataset" xsi:type="string">johndoe_unique</data>
            <data name="prices/category_price_excl_tax" xsi:type="string">277.14</data>
            <data name="prices/category_price_incl_tax" xsi:type="string">300.00</data>
            <data name="prices/product_view_price_excl_tax" xsi:type="string">277.14</data>
            <data name="prices/product_view_price_incl_tax" xsi:type="string">300.00</data>
            <data name="prices/cart_item_price_excl_tax" xsi:type="string">13.86</data>
            <data name="prices/cart_item_price_incl_tax" xsi:type="string">15.00</data>
            <data name="prices/cart_item_subtotal_excl_tax" xsi:type="string">41.57</data>
            <data name="prices/cart_item_subtotal_incl_tax" xsi:type="string">45.00</data>
            <data name="prices/subtotal_excl_tax" xsi:type="string">41.57</data>
            <data name="prices/subtotal_incl_tax" xsi:type="string">45.00</data>
            <data name="prices/discount" xsi:type="string">20.79</data>
            <data name="prices/shipping_excl_tax" xsi:type="string">15.00</data>
            <data name="prices/shipping_incl_tax" xsi:type="string">16.24</data>
            <data name="prices/tax" xsi:type="string">3.09</data>
            <data name="prices/grand_total_excl_tax" xsi:type="string">37.36</data>
            <data name="prices/grand_total_incl_tax" xsi:type="string">40.45</data>
            <data name="arguments/shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="arguments/shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="arguments/payment/method" xsi:type="string">checkmo</data>
            <data name="address/dataset" xsi:type="string">Estimate_US_address_NY</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
        </variation>
        <variation name="TaxCalculationTestVariation2" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" method="test">
            <data name="description" xsi:type="string">Simple product group price with sales rule, customer tax greater than store tax and catalog price excluding tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods, row_cat_excl_ship_incl_before_disc_on_incl, display_excluding_including_tax</data>
            <data name="product" xsi:type="string">catalogProductSimple::simple_with_group_price_and_category</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups_no_coupon</data>
            <data name="taxRule" xsi:type="string">customer_greater_store_rate</data>
            <data name="customer/dataset" xsi:type="string">johndoe_unique</data>
            <data name="prices/category_price_excl_tax" xsi:type="string">90.99</data>
            <data name="prices/category_price_incl_tax" xsi:type="string">98.61</data>
            <data name="prices/product_view_price_excl_tax" xsi:type="string">90.99</data>
            <data name="prices/product_view_price_incl_tax" xsi:type="string">98.61</data>
            <data name="prices/cart_item_price_excl_tax" xsi:type="string">90.99</data>
            <data name="prices/cart_item_price_incl_tax" xsi:type="string">98.61</data>
            <data name="prices/cart_item_subtotal_excl_tax" xsi:type="string">272.97</data>
            <data name="prices/cart_item_subtotal_incl_tax" xsi:type="string">295.83</data>
            <data name="prices/subtotal_excl_tax" xsi:type="string">272.97</data>
            <data name="prices/subtotal_incl_tax" xsi:type="string">295.83</data>
            <data name="prices/discount" xsi:type="string">147.92</data>
            <data name="prices/shipping_excl_tax" xsi:type="string">13.86</data>
            <data name="prices/shipping_incl_tax" xsi:type="string">15.02</data>
            <data name="prices/tax" xsi:type="string">24.02</data>
            <data name="prices/grand_total_excl_tax" xsi:type="string">138.91</data>
            <data name="prices/grand_total_incl_tax" xsi:type="string">162.93</data>
            <data name="arguments/shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="arguments/shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="arguments/payment/method" xsi:type="string">checkmo</data>
            <data name="address/dataset" xsi:type="string">Estimate_US_address_NY</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
        </variation>
        <variation name="TaxCalculationTestVariation3" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" method="test">
            <data name="description" xsi:type="string">Simple product group price with sales rule, customer tax less than store tax and catalog price excluding tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods, total_cat_excl_ship_incl_after_disc_on_excl, display_excluding_including_tax</data>
            <data name="product" xsi:type="string">catalogProductSimple::simple_with_group_price_and_category</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups_no_coupon</data>
            <data name="taxRule" xsi:type="string">customer_less_store_rate</data>
            <data name="customer/dataset" xsi:type="string">johndoe_unique</data>
            <data name="prices/category_price_excl_tax" xsi:type="string">90.99</data>
            <data name="prices/category_price_incl_tax" xsi:type="string">98.50</data>
            <data name="prices/product_view_price_excl_tax" xsi:type="string">90.99</data>
            <data name="prices/product_view_price_incl_tax" xsi:type="string">98.50</data>
            <data name="prices/cart_item_price_excl_tax" xsi:type="string">90.99</data>
            <data name="prices/cart_item_price_incl_tax" xsi:type="string">98.50</data>
            <data name="prices/cart_item_subtotal_excl_tax" xsi:type="string">272.97</data>
            <data name="prices/cart_item_subtotal_incl_tax" xsi:type="string">295.49</data>
            <data name="prices/subtotal_excl_tax" xsi:type="string">272.97</data>
            <data name="prices/subtotal_incl_tax" xsi:type="string">295.49</data>
            <data name="prices/discount" xsi:type="string">136.49</data>
            <data name="prices/shipping_excl_tax" xsi:type="string">13.84</data>
            <data name="prices/shipping_incl_tax" xsi:type="string">14.98</data>
            <data name="prices/tax" xsi:type="string">12.40</data>
            <data name="prices/grand_total_excl_tax" xsi:type="string">150.32</data>
            <data name="prices/grand_total_incl_tax" xsi:type="string">162.72</data>
            <data name="arguments/shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="arguments/shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="arguments/payment/method" xsi:type="string">checkmo</data>
            <data name="address/dataset" xsi:type="string">Estimate_US_address_NY</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
        </variation>
        <variation name="TaxCalculationTestVariation4" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" method="test">
            <data name="description" xsi:type="string">Simple product special price with sales rule, customer tax less than store tax and catalog price including tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods, row_cat_incl_ship_excl_before_disc_on_incl, display_excluding_including_tax</data>
            <data name="product" xsi:type="string">catalogProductSimple::product_with_special_price_and_category</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups_no_coupon</data>
            <data name="taxRule" xsi:type="string">customer_less_store_rate</data>
            <data name="customer/dataset" xsi:type="string">johndoe_unique</data>
            <data name="prices/category_price_excl_tax" xsi:type="string">83.05</data>
            <data name="prices/category_price_incl_tax" xsi:type="string">89.90</data>
            <data name="prices/product_view_price_excl_tax" xsi:type="string">83.05</data>
            <data name="prices/product_view_price_incl_tax" xsi:type="string">89.90</data>
            <data name="prices/cart_item_price_excl_tax" xsi:type="string">83.05</data>
            <data name="prices/cart_item_price_incl_tax" xsi:type="string">89.90</data>
            <data name="prices/cart_item_subtotal_excl_tax" xsi:type="string">249.15</data>
            <data name="prices/cart_item_subtotal_incl_tax" xsi:type="string">269.70</data>
            <data name="prices/subtotal_excl_tax" xsi:type="string">249.15</data>
            <data name="prices/subtotal_incl_tax" xsi:type="string">269.70</data>
            <data name="prices/discount" xsi:type="string">134.85</data>
            <data name="prices/shipping_excl_tax" xsi:type="string">15.00</data>
            <data name="prices/shipping_incl_tax" xsi:type="string">16.24</data>
            <data name="prices/tax" xsi:type="string">21.79</data>
            <data name="prices/grand_total_excl_tax" xsi:type="string">129.30</data>
            <data name="prices/grand_total_incl_tax" xsi:type="string">151.09</data>
            <data name="arguments/shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="arguments/shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="arguments/payment/method" xsi:type="string">checkmo</data>
            <data name="address/dataset" xsi:type="string">Estimate_US_address_NY</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
        </variation>
        <variation name="TaxCalculationTestVariation5" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" method="test">
            <data name="description" xsi:type="string">Simple product tier price with sales rule, customer tax less than store tax and catalog price including tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods, unit_cat_incl_ship_incl_before_disc_on_incl, display_excluding_including_tax</data>
            <data name="product" xsi:type="string">catalogProductSimple::simple_with_tier_price</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups_no_coupon</data>
            <data name="taxRule" xsi:type="string">customer_less_store_rate</data>
            <data name="customer/dataset" xsi:type="string">johndoe_unique</data>
            <data name="prices/category_price_excl_tax" xsi:type="string">276.81</data>
            <data name="prices/category_price_incl_tax" xsi:type="string">299.65</data>
            <data name="prices/product_view_price_excl_tax" xsi:type="string">276.81</data>
            <data name="prices/product_view_price_incl_tax" xsi:type="string">299.65</data>
            <data name="prices/cart_item_price_excl_tax" xsi:type="string">13.84</data>
            <data name="prices/cart_item_price_incl_tax" xsi:type="string">14.98</data>
            <data name="prices/cart_item_subtotal_excl_tax" xsi:type="string">41.52</data>
            <data name="prices/cart_item_subtotal_incl_tax" xsi:type="string">44.94</data>
            <data name="prices/subtotal_excl_tax" xsi:type="string">41.52</data>
            <data name="prices/subtotal_incl_tax" xsi:type="string">44.94</data>
            <data name="prices/discount" xsi:type="string">22.47</data>
            <data name="prices/shipping_excl_tax" xsi:type="string">13.84</data>
            <data name="prices/shipping_incl_tax" xsi:type="string">14.98</data>
            <data name="prices/tax" xsi:type="string">4.56</data>
            <data name="prices/grand_total_excl_tax" xsi:type="string">32.89</data>
            <data name="prices/grand_total_incl_tax" xsi:type="string">37.45</data>
            <data name="arguments/shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="arguments/shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="arguments/payment/method" xsi:type="string">checkmo</data>
            <data name="address/dataset" xsi:type="string">Estimate_US_address_NY</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
        </variation>
        <variation name="TaxCalculationTestVariation6" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" method="test">
            <data name="description" xsi:type="string">Simple product special price with sales rule, customer tax equals store tax and catalog price excluding tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods, total_cat_excl_ship_incl_before_disc_on_incl, display_excluding_including_tax</data>
            <data name="product" xsi:type="string">catalogProductSimple::product_with_special_price_and_category</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups_no_coupon</data>
            <data name="taxRule" xsi:type="string">customer_equals_store_rate</data>
            <data name="customer/dataset" xsi:type="string">johndoe_unique</data>
            <data name="prices/category_price_excl_tax" xsi:type="string">90.00</data>
            <data name="prices/category_price_incl_tax" xsi:type="string">97.43</data>
            <data name="prices/product_view_price_excl_tax" xsi:type="string">90.00</data>
            <data name="prices/product_view_price_incl_tax" xsi:type="string">97.43</data>
            <data name="prices/cart_item_price_excl_tax" xsi:type="string">90.00</data>
            <data name="prices/cart_item_price_incl_tax" xsi:type="string">97.43</data>
            <data name="prices/cart_item_subtotal_excl_tax" xsi:type="string">270.00</data>
            <data name="prices/cart_item_subtotal_incl_tax" xsi:type="string">292.28</data>
            <data name="prices/subtotal_excl_tax" xsi:type="string">270.00</data>
            <data name="prices/subtotal_incl_tax" xsi:type="string">292.28</data>
            <data name="prices/discount" xsi:type="string">146.14</data>
            <data name="prices/shipping_excl_tax" xsi:type="string">13.86</data>
            <data name="prices/shipping_incl_tax" xsi:type="string">15.00</data>
            <data name="prices/tax" xsi:type="string">23.42</data>
            <data name="prices/grand_total_excl_tax" xsi:type="string">137.72</data>
            <data name="prices/grand_total_incl_tax" xsi:type="string">161.14</data>
            <data name="arguments/shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="arguments/shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="arguments/payment/method" xsi:type="string">checkmo</data>
            <data name="address/dataset" xsi:type="string">Estimate_US_address_NY</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
        </variation>
        <variation name="TaxCalculationTestVariation7" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" method="test">
            <data name="description" xsi:type="string">Simple product group price with sales rule, customer tax equals store tax and catalog price excluding tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods, unit_cat_excl_ship_excl_after_disc_on_excl, display_excluding_including_tax</data>
            <data name="product" xsi:type="string">catalogProductSimple::simple_with_group_price_and_category</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups_no_coupon</data>
            <data name="taxRule" xsi:type="string">customer_equals_store_rate</data>
            <data name="customer/dataset" xsi:type="string">johndoe_unique</data>
            <data name="prices/category_price_excl_tax" xsi:type="string">90.99</data>
            <data name="prices/category_price_incl_tax" xsi:type="string">98.50</data>
            <data name="prices/product_view_price_excl_tax" xsi:type="string">90.99</data>
            <data name="prices/product_view_price_incl_tax" xsi:type="string">98.50</data>
            <data name="prices/cart_item_price_excl_tax" xsi:type="string">90.99</data>
            <data name="prices/cart_item_price_incl_tax" xsi:type="string">98.50</data>
            <data name="prices/cart_item_subtotal_excl_tax" xsi:type="string">272.97</data>
            <data name="prices/cart_item_subtotal_incl_tax" xsi:type="string">295.50</data>
            <data name="prices/subtotal_excl_tax" xsi:type="string">272.97</data>
            <data name="prices/subtotal_incl_tax" xsi:type="string">295.50</data>
            <data name="prices/discount" xsi:type="string">136.49</data>
            <data name="prices/shipping_excl_tax" xsi:type="string">15.00</data>
            <data name="prices/shipping_incl_tax" xsi:type="string">16.24</data>
            <data name="prices/tax" xsi:type="string">12.49</data>
            <data name="prices/grand_total_excl_tax" xsi:type="string">151.48</data>
            <data name="prices/grand_total_incl_tax" xsi:type="string">163.97</data>
            <data name="arguments/shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="arguments/shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="arguments/payment/method" xsi:type="string">checkmo</data>
            <data name="address/dataset" xsi:type="string">Estimate_US_address_NY</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
        </variation>
        <variation name="TaxCalculationTestVariation8" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" method="test">
            <data name="description" xsi:type="string">Simple product special price with sales rule, customer tax greater than store tax and catalog price including tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods, total_cat_incl_ship_excl_before_disc_on_excl, display_excluding_including_tax</data>
            <data name="product" xsi:type="string">catalogProductSimple::simple_with_group_price_and_category</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups_no_coupon</data>
            <data name="taxRule" xsi:type="string">customer_greater_store_rate</data>
            <data name="customer/dataset" xsi:type="string">johndoe_unique</data>
            <data name="prices/category_price_excl_tax" xsi:type="string">84.06</data>
            <data name="prices/category_price_incl_tax" xsi:type="string">91.10</data>
            <data name="prices/product_view_price_excl_tax" xsi:type="string">84.06</data>
            <data name="prices/product_view_price_incl_tax" xsi:type="string">91.10</data>
            <data name="prices/cart_item_price_excl_tax" xsi:type="string">84.06</data>
            <data name="prices/cart_item_price_incl_tax" xsi:type="string">91.10</data>
            <data name="prices/cart_item_subtotal_excl_tax" xsi:type="string">252.18</data>
            <data name="prices/cart_item_subtotal_incl_tax" xsi:type="string">273.30</data>
            <data name="prices/subtotal_excl_tax" xsi:type="string">252.18</data>
            <data name="prices/subtotal_incl_tax" xsi:type="string">273.30</data>
            <data name="prices/discount" xsi:type="string">126.09</data>
            <data name="prices/shipping_excl_tax" xsi:type="string">15.00</data>
            <data name="prices/shipping_incl_tax" xsi:type="string">16.26</data>
            <data name="prices/tax" xsi:type="string">22.38</data>
            <data name="prices/grand_total_excl_tax" xsi:type="string">141.09</data>
            <data name="prices/grand_total_incl_tax" xsi:type="string">163.47</data>
            <data name="arguments/shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="arguments/shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="arguments/payment/method" xsi:type="string">checkmo</data>
            <data name="address/dataset" xsi:type="string">Estimate_US_address_NY</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
        </variation>
        <variation name="TaxCalculationTestVariation9" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" method="test">
            <data name="description" xsi:type="string">Simple product tier price with sales rule, customer tax greater than store tax and catalog price excluding tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods, total_cat_excl_ship_incl_after_disc_on_incl, display_excluding_including_tax</data>
            <data name="product" xsi:type="string">catalogProductSimple::simple_with_tier_price</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups_no_coupon</data>
            <data name="taxRule" xsi:type="string">customer_greater_store_rate</data>
            <data name="customer/dataset" xsi:type="string">johndoe_unique</data>
            <data name="prices/category_price_excl_tax" xsi:type="string">300.00</data>
            <data name="prices/category_price_incl_tax" xsi:type="string">325.13</data>
            <data name="prices/product_view_price_excl_tax" xsi:type="string">300.00</data>
            <data name="prices/product_view_price_incl_tax" xsi:type="string">325.13</data>
            <data name="prices/cart_item_price_excl_tax" xsi:type="string">15.00</data>
            <data name="prices/cart_item_price_incl_tax" xsi:type="string">16.26</data>
            <data name="prices/cart_item_subtotal_excl_tax" xsi:type="string">45.00</data>
            <data name="prices/cart_item_subtotal_incl_tax" xsi:type="string">48.77</data>
            <data name="prices/subtotal_excl_tax" xsi:type="string">45.00</data>
            <data name="prices/subtotal_incl_tax" xsi:type="string">48.77</data>
            <data name="prices/discount" xsi:type="string">24.39</data>
            <data name="prices/shipping_excl_tax" xsi:type="string">13.86</data>
            <data name="prices/shipping_incl_tax" xsi:type="string">15.02</data>
            <data name="prices/tax" xsi:type="string">2.89</data>
            <data name="prices/grand_total_excl_tax" xsi:type="string">34.47</data>
            <data name="prices/grand_total_incl_tax" xsi:type="string">37.36</data>
            <data name="arguments/shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="arguments/shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="arguments/payment/method" xsi:type="string">checkmo</data>
            <data name="address/dataset" xsi:type="string">Estimate_US_address_NY</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
        </variation>
        <variation name="TaxCalculationTestVariation10" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" method="test">
            <data name="description" xsi:type="string">Simple product special price with sales rule, customer tax greater than store tax and catalog price excluding tax</data>
            <data name="configData" xsi:type="string">shipping_tax_class_taxable_goods, unit_cat_excl_ship_incl_after_disc_on_excl, display_excluding_including_tax</data>
            <data name="product" xsi:type="string">catalogProductSimple::product_with_special_price_and_category</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups_no_coupon</data>
            <data name="taxRule" xsi:type="string">customer_greater_store_rate</data>
            <data name="customer/dataset" xsi:type="string">johndoe_unique</data>
            <data name="prices/category_price_excl_tax" xsi:type="string">90.00</data>
            <data name="prices/category_price_incl_tax" xsi:type="string">97.54</data>
            <data name="prices/product_view_price_excl_tax" xsi:type="string">90.00</data>
            <data name="prices/product_view_price_incl_tax" xsi:type="string">97.54</data>
            <data name="prices/cart_item_price_excl_tax" xsi:type="string">90.00</data>
            <data name="prices/cart_item_price_incl_tax" xsi:type="string">97.54</data>
            <data name="prices/cart_item_subtotal_excl_tax" xsi:type="string">270.00</data>
            <data name="prices/cart_item_subtotal_incl_tax" xsi:type="string">292.62</data>
            <data name="prices/subtotal_excl_tax" xsi:type="string">270.00</data>
            <data name="prices/subtotal_incl_tax" xsi:type="string">292.62</data>
            <data name="prices/discount" xsi:type="string">135.00</data>
            <data name="prices/shipping_excl_tax" xsi:type="string">13.86</data>
            <data name="prices/shipping_incl_tax" xsi:type="string">15.02</data>
            <data name="prices/tax" xsi:type="string">12.47</data>
            <data name="prices/grand_total_excl_tax" xsi:type="string">148.86</data>
            <data name="prices/grand_total_incl_tax" xsi:type="string">161.33</data>
            <data name="arguments/shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="arguments/shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="arguments/payment/method" xsi:type="string">checkmo</data>
            <data name="address/dataset" xsi:type="string">Estimate_US_address_NY</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax" next="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxRuleIsAppliedToAllPricesExcludingIncludingTax"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertOrderTaxOnBackendExcludingIncludingTax" prev="Mage\Tax\Test\Constraint\AssertTaxCalculationAfterCheckoutExcludingIncludingTax"/>
        </variation>
    </testCase>
</config>
