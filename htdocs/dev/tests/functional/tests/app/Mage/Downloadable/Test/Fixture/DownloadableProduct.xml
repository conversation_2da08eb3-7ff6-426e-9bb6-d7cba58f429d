<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/fixture.xsd">
    <fixture name="downloadableProduct" module="Mage_Downloadable" type="eav" entity_type="catalog_product"
             product_type="downloadable" collection="Mage\Catalog\Model\Resource\Product\Collection" identifier="sku"
             repository_class="Mage\Downloadable\Test\Repository\DownloadableProduct"
             handler_interface="Mage\Downloadable\Test\Handler\DownloadableProductInterface"
             class="Mage\Downloadable\Test\Fixture\DownloadableProduct">
        <data_config>
            <item name="type_id" xsi:type="string">downloadable</item>
            <item name="create_url_params" xsi:type="array">
                <item name="type" xsi:type="string">downloadable</item>
                <item name="set" xsi:type="string">4</item>
            </item>
            <item name="input_prefix" xsi:type="string">product</item>
        </data_config>
        <field name="category_ids" is_required="0" group="categories"
               source="Mage\Catalog\Test\Fixture\CatalogProductSimple\CategoryIds"/>
        <field name="cost" is_required="0"/>
        <field name="created_at" is_required="1"/>
        <field name="custom_design" is_required="0"/>
        <field name="custom_design_from" is_required="0"/>
        <field name="custom_design_to" is_required="0"/>
        <field name="custom_layout_update" is_required="0"/>
        <field name="description" is_required="1" group="general"/>
        <field name="gallery" is_required="0"/>
        <field name="gift_message_available" is_required="0"/>
        <field name="group_price" is_required="0" group="prices"
               repository="Mage\Downloadable\Test\Repository\DownloadableProduct\GroupPriceOptions"
               />
        <field name="has_options" is_required="0"/>
        <field name="image" is_required="0"/>
        <field name="image_label" is_required="0"/>
        <field name="links_exist" is_required="0"/>
        <field name="links_purchased_separately" is_required="1"/>
        <field name="links_title" is_required="1"/>
        <field name="media_gallery" is_required="0"/>
        <field name="meta_description" is_required="0"/>
        <field name="meta_keyword" is_required="0"/>
        <field name="meta_title" is_required="0"/>
        <field name="minimal_price" is_required="0"/>
        <field name="msrp" is_required="0"/>
        <field name="msrp_display_actual_price_type" is_required="0"/>
        <field name="msrp_enabled" is_required="0"/>
        <field name="name" is_required="1" group="general"/>
        <field name="news_from_date" is_required="0"/>
        <field name="news_to_date" is_required="0"/>
        <field name="old_id" is_required="0"/>
        <field name="options_container" is_required="0"/>
        <field name="page_layout" is_required="0"/>
        <field name="price" is_required="1" group="prices"
               source="Mage\Catalog\Test\Fixture\CatalogProductSimple\Price"
               repository="Mage\Downloadable\Test\Repository\DownloadableProduct\Price"/>
        <field name="related_tgtr_position_behavior" is_required="0"/>
        <field name="related_tgtr_position_limit" is_required="0"/>
        <field name="required_options" is_required="0"/>
        <field name="samples_title" is_required="1"/>
        <field name="short_description" is_required="1" group="general"/>
        <field name="sku" is_required="1" group="general"/>
        <field name="small_image" is_required="0"/>
        <field name="small_image_label" is_required="0"/>
        <field name="special_from_date" is_required="0"/>
        <field name="special_price" is_required="0" group="prices"/>
        <field name="special_to_date" is_required="0"/>
        <field name="status" is_required="1" group="general"/>
        <field name="tax_class_id" is_required="1" group="prices"
               source="Mage\Catalog\Test\Fixture\CatalogProductSimple\TaxClass"/>
        <field name="thumbnail" is_required="0"/>
        <field name="thumbnail_label" is_required="0"/>
        <field name="tier_price" is_required="0" group="prices"
               repository="Mage\Catalog\Test\Repository\CatalogProductSimple\TierPriceOptions"/>
        <field name="updated_at" is_required="1"/>
        <field name="upsell_tgtr_position_behavior" is_required="0"/>
        <field name="upsell_tgtr_position_limit" is_required="0"/>
        <field name="url_key" is_required="0" group="general"/>
        <field name="url_path" is_required="0"/>
        <field name="visibility" is_required="1" group="general"/>
        <field name="custom_options" source="Mage\Catalog\Test\Fixture\CatalogProductSimple\CustomOptions"
               repository="Mage\Catalog\Test\Repository\CatalogProductSimple\CustomOptions"
               group="custom-options"/>
        <field name="id" group="null"/>
        <field name="website_ids" group="websites"
               source="Mage\Catalog\Test\Fixture\CatalogProductSimple\WebsiteIds"/>
        <field name="downloadable_links" group="downloadable_information"
               repository="Mage\Downloadable\Test\Repository\DownloadableProduct\Links"/>
        <field name="downloadable_sample" group="downloadable_information"
               repository="Mage\Downloadable\Test\Repository\DownloadableProduct\Samples"/>
        <field name="checkout_data"
               repository="Mage\Downloadable\Test\Repository\DownloadableProduct\CheckoutData"
               group="null"/>
        <field name="stock_data" group="inventory"/>
    </fixture>
</config>
