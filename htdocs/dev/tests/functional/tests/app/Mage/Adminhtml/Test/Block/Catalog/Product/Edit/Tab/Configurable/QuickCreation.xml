<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<mapping strict="0">
    <wrapper>simple_product</wrapper>
    <fields>
        <name_autogenerate>
            <input>checkbox</input>
        </name_autogenerate>
        <name />
        <sku_autogenerate>
            <input>checkbox</input>
        </sku_autogenerate>
        <sku />
        <weight />
        <status>
            <input>select</input>
        </status>
        <visibility>
            <input>select</input>
        </visibility>
        <attributes>
            <class>Mage\Adminhtml\Test\Block\Catalog\Product\Edit\Tab\Configurable\QuickCreation\AttributesElement</class>
            <selector>.form-list</selector>
        </attributes>
        <stock_data composite="1">
            <qty>
                <selector>[name="simple_product[stock_data][qty]"]</selector>
            </qty>
            <is_in_stock>
                <selector>[name="simple_product[stock_data][is_in_stock]"]</selector>
                <input>select</input>
            </is_in_stock>
        </stock_data>
    </fields>
</mapping>
