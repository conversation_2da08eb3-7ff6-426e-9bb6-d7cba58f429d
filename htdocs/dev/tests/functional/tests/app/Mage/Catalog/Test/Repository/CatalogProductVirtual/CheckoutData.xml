<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Mage\Catalog\Test\Repository\CatalogProductVirtual\CheckoutData">
        <dataset name="default">
            <field name="qty" xsi:type="string">3</field>
            <field name="cartItem" xsi:type="array">
                <item name="price" xsi:type="string">10</item>
                <item name="qty" xsi:type="string">3</item>
                <item name="subtotal" xsi:type="string">30</item>
            </field>
        </dataset>

        <dataset name="order_default">
            <field name="qty" xsi:type="string">1</field>
            <field name="cartItem" xsi:type="array">
                <item name="price" xsi:type="string">100.00</item>
                <item name="subtotal" xsi:type="string">100.00</item>
            </field>
        </dataset>

        <dataset name="with_one_custom_option">
            <field name="options" xsi:type="array">
                <item name="custom_options" xsi:type="array">
                    <item name="0" xsi:type="array">
                        <item name="title" xsi:type="string">attribute_key_0</item>
                        <item name="value" xsi:type="string">option_key_0</item>
                    </item>
                </item>
            </field>
            <field name="qty" xsi:type="string">2</field>
            <field name="cartItem" xsi:type="array">
                <item name="price" xsi:type="string">120.00</item>
                <item name="subtotal" xsi:type="string">240.00</item>
            </field>
        </dataset>
        
        <dataset name="with_special_price">
            <field name="qty" xsi:type="string">2</field>
            <field name="cartItem" xsi:type="array">
                <item name="price" xsi:type="string">10</item>
                <item name="qty" xsi:type="string">3</item>
                <item name="subtotal" xsi:type="string">20</item>
            </field>
        </dataset>

        <dataset name="with_tier_price">
            <field name="qty" xsi:type="string">4</field>
            <field name="cartItem" xsi:type="array">
                <item name="price" xsi:type="string">15</item>
                <item name="qty" xsi:type="string">4</item>
                <item name="subtotal" xsi:type="string">60</item>
            </field>
        </dataset>

        <dataset name="with_grouped_price">
            <field name="qty" xsi:type="string">2</field>
            <field name="cartItem" xsi:type="array">
                <item name="price" xsi:type="string">90.00</item>
                <item name="subtotal" xsi:type="string">180.00</item>
            </field>
        </dataset>
        
        <dataset name="three_simple_products">
            <field name="options" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="name" xsi:type="string">product_key_0</item>
                    <item name="qty" xsi:type="string">3</item>
                </item>
                <item name="1" xsi:type="array">
                    <item name="name" xsi:type="string">product_key_1</item>
                    <item name="qty" xsi:type="string">1</item>
                </item>
                <item name="2" xsi:type="array">
                    <item name="name" xsi:type="string">product_key_2</item>
                    <item name="qty" xsi:type="string">2</item>
                </item>
            </field>
            <field name="cartItem" xsi:type="array">
                <item name="price" xsi:type="array">
                    <item name="product_key_0" xsi:type="string">560</item>
                    <item name="product_key_1" xsi:type="string">40</item>
                    <item name="product_key_2" xsi:type="string">100</item>
                </item>
                <item name="qty" xsi:type="array">
                    <item name="product_key_0" xsi:type="string">3</item>
                    <item name="product_key_1" xsi:type="string">1</item>
                    <item name="product_key_2" xsi:type="string">2</item>
                </item>
                <item name="subtotal" xsi:type="array">
                    <item name="product_key_0" xsi:type="string">1680</item>
                    <item name="product_key_1" xsi:type="string">40</item>
                    <item name="product_key_2" xsi:type="string">200</item>
                </item>
            </field>
        </dataset>
    </repository>
</config>
