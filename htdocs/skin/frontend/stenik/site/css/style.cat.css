@charset "utf-8";
/* CSS Document
   Author: <PERSON><PERSON><PERSON> as developer @ www.stenikgroup.com
*/

/* cyrillic */
@font-face {
	font-family: 'Montserrat';
	font-style: italic;
	font-weight: 400;
	src: url('../fonts/Montserrat400ic.woff2') format('woff2');
	unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
	font-display: swap;
}
/* latin */
@font-face {
	font-family: 'Montserrat';
	font-style: italic;
	font-weight: 400;
	src: url('../fonts/Montserrat400i.woff2') format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
	font-display: swap;
}
/* cyrillic */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 300;
	src: url('../fonts/Montserrat300c.woff2') format('woff2');
	unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
	font-display: swap;
}
/* latin */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 300;
	src: url('../fonts/Montserrat300.woff2') format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
	font-display: swap;
}
/* cyrillic */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 400;
	src: url('../fonts/Montserrat400c.woff2') format('woff2');
	unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
	font-display: swap;
}
/* latin */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 400;
	src: url('../fonts/Montserrat400.woff2') format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
	font-display: swap;
}
/* cyrillic */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 500;
	src: url('../fonts/Montserrat500c.woff2') format('woff2');
	unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
	font-display: swap;
}
/* latin */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 500;
	src: url('../fonts/Montserrat500.woff2') format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
	font-display: swap;
}
/* cyrillic */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 600;
	src: url('../fonts/Montserrat600c.woff2') format('woff2');
	unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
	font-display: swap;
}
/* latin */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 600;
	src: url('../fonts/Montserrat600.woff2') format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
	font-display: swap;
}
/* cyrillic */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 700;
	src: url('../fonts/Montserrat700c.woff2') format('woff2');
	unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
	font-display: swap;
}
/* latin */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 700;
	src: url('../fonts/Montserrat700.woff2') format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
	font-display: swap;
}
/* cyrillic */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 800;
	src: url('../fonts/Montserrat800c.woff2') format('woff2');
	unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
	font-display: swap;
}
/* latin */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 800;
	src: url('../fonts/Montserrat800.woff2') format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
	font-display: swap;
}



/* Magento resets
*******************/

.clearer:after, .header-container:after, .header-container .top-container:after, .header:after, .header .quick-access:after, #nav:after, .main:after, .footer:after, .footer-container .bottom-container:after, .col-main:after, .col2-set:after, .col3-set:after, .col3-layout .product-options-bottom .price-box:after, .col4-set:after, .search-autocomplete li:after, .block .block-content:after, .block .actions:after, .block li.item:after, .block-poll li:after, .block-layered-nav .currently li:after, .page-title:after, .products-grid:after, .products-list li.item:after, .box-account .box-head:after, .dashboard .box .box-title:after, .box-reviews li.item:after, .box-tags li.item:after, .pager:after, .sorter:after, .ratings:after, .add-to-box:after, .add-to-cart:after, .product-essential:after, .product-collateral:after, .product-view .product-img-box .more-views ul:after, .product-view .box-tags .form-add:after, .product-view .product-shop .short-description:after, .product-view .box-description:after, .product-options .options-list li:after, .product-options-bottom:after, .product-review:after, .cart-collaterals:after, .cart .crosssell li.item:after, .opc .step-title:after, .checkout-progress:after, .multiple-checkout .place-order:after, .group-select li:after, .form-list li:after, .form-list .field:after, .buttons-set:after, .page-print .print-head:after, .advanced-search-summary:after, .gift-messages-form .item:after, .send-friend .form-list li p:after {
    clear: both;
    content: ".";
    display: block;
    font-size: 0;
    height: 0;
    line-height: 0;
    overflow: hidden;
}

/* Stenik default style helpers
*********************************/

ol { padding:0; margin:5px 0 10px 20px; }
ol li { padding:0; margin:0;  }
ul { margin: 0; padding: 0; }
ul li {	list-style: none; padding:0; margin:0; }
p {	padding:0; margin:0; }
iframe { border-width: 0px; }
a { outline:none; }
a:hover {	outline:none; }
a img { border:0; }
.clear { clear:both; }
.clearH { clear:both; height:10px; }
.clearH2 { clear:both; height:20px; }
.clearH3 { clear:both; height:30px; }
.clearH4 { clear:both; height:40px; }
.noBgr { background:none !important; }
.left {	float: left; }
.right { float: right; }
.padding5 { padding: 5px; }
.padding10 { padding: 10px; }
.padding15 { padding: 15px; }
.padding20 { padding: 20px; }
.padding30 { padding: 30px; }
.paddingT5 { padding-top: 5px; }
.paddingT10 { padding-top: 10px; }
.paddingT20 { padding-top: 20px; }
.paddingT30 { padding-top: 30px; }
.paddingT40 { padding-top: 40px; }
.paddingT50 { padding-top: 50px; }
.paddingT60 { padding-top: 60px; }
.paddingT70 { padding-top: 70px; }
.paddingT80 { padding-top: 80px; }
.paddingB5 { padding-bottom: 5px; }
.paddingB10 { padding-bottom: 10px; }
.paddingB20 { padding-bottom: 20px; }
.paddingB30 { padding-bottom: 30px; }
.paddingB40 { padding-bottom: 40px; }
.paddingB50 { padding-bottom: 50px; }
.paddingB60 { padding-bottom: 60px; }
.paddingB70 { padding-bottom: 70px; }
.paddingB80 { padding-bottom: 80px; }
.marginT5 { margin-top: 5px; }
.marginT10 { margin-top: 10px; }
.marginT20 { margin-top: 20px; }
.marginT30 { margin-top: 30px; }
.marginT40 { margin-top: 40px; }
.marginT50 { margin-top: 50px; }
.marginT60 { margin-top: 60px; }
.marginT70 { margin-top: 70px; }
.marginT80 { margin-top: 80px; }
.marginB5 { margin-bottom: 5px; }
.marginB10 { margin-bottom: 10px; }
.marginB20 { margin-bottom: 20px; }
.marginB30 { margin-bottom: 30px; }
.marginB40 { margin-bottom: 40px; }
.marginB50 { margin-bottom: 50px; }
.marginB60 { margin-bottom: 60px; }
.marginB70 { margin-bottom: 70px; }
.marginB80 { margin-bottom: 80px; }
.rounded5 { border-radius: 5px; }
.rounded10 { border-radius: 10px; }
.rounded15 { border-radius: 10px; }
.rounded20 { border-radius: 10px; }
.rounded100 { border-radius: 100%; }
.top { top: 0px; }
.top10 { top: 10px; }
.top20 { top: 20px; }
.top30 { top: 30px; }
.top40 { top: 40px; }
.bottom { bottom: 0px; }
.bottom10 { bottom: 10px; }
.bottom20 { bottom: 20px; }
.bottom30 { bottom: 30px; }
.bottom40 { bottom: 40px; }
.left { left: 0px; }
.left10 { left: 10px; }
.left20 { left: 20px; }
.left30 { left: 30px; }
.left40 { left: 40px; }
.right { right: 0px; }
.right10 { right: 10px; }
.right20 { right: 20px; }
.right30 { right: 30px; }
.right40 { right: 40px; }



/* Default body and wrapper settings
**************************************/

body { background: #FFF; color: #222; font-size: 14px; line-height: 20px; font-weight: normal; font-family: 'Montserrat', sans-serif; direction: ltr; }
body.disable-scroll { overflow: hidden; }



/* Main colors config
***********************/

.background-color1 { background-color: #222; }
.background-color2 { background-color: #f4f4f4; }
.checkout-color { background-color: #ce181e !important; }
.checkout-color:hover { background-color: #5b050e !important; }
a { color: #ce181e; text-decoration: none; }
a:hover { color: #9f9f9f; text-decoration: none; }
a:focus { text-decoration: none; }
p { color: #222; }
p a { color: #ce181e; }
p a:hover { color: #9f9f9f; }
h1 { color: #222; }
h2 { color: #222; }
h3 { color: #222; }
h4 { color: #222; }
h5 { color: #222; }
h6 { color: #222; }
img { max-width: 100%; }
.row-title { color: #222; }
a.row-title:hover { color: #ce181e; }



/* Typography styles
***********************/

p {	padding: 0px; margin: 0px 0px 15px 0px;	font-size: 14px; line-height: 20px;	font-weight: normal; text-decoration: none; }

h1 { padding: 0px; margin: -8px 0px 15px 0px; font-size: 36px; line-height: 46px; font-weight: bold; text-decoration: none; text-transform: uppercase; }
h2 { padding: 0px; margin: 20px 0px 15px 0px; font-size: 24px; line-height: 36px; font-weight: bold; text-decoration: none; text-transform: uppercase; }
h3 { padding: 0px; margin: 20px 0px 15px 0px; font-size: 20px; line-height: 30px; font-weight: bold; text-decoration: none; text-transform: uppercase; }
h4 { padding: 0px; margin: 20px 0px 15px 0px; font-size: 18px; line-height: 28px; font-weight: bold; text-decoration: none; }
h5 { padding: 0px; margin: 20px 0px 10px 0px; font-size: 16px; line-height: 28px; font-weight: bold; text-decoration: none; }
h6 { padding: 0px; margin: 20px 0px 10px 0px; font-size: 16px; line-height: 20px; font-weight: bold; text-decoration: none; }
.row-title { display: block; margin-bottom: 25px; text-transform: uppercase; font-size: 30px; text-align: center; line-height: 30px; font-weight: bold; text-decoration: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.replacement-products .row-title { display: block; margin-bottom: 0; text-transform: uppercase; font-size: 30px; text-align: center; line-height: 30px; font-weight: bold; text-decoration: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.widget-button { display: inline-block; margin-bottom: 40px; padding: 0 25px; line-height: 36px; border: 1px solid #ce181e; font-size: 12px; font-weight: 500; color: #ce181e; text-decoration: none; text-transform: uppercase; cursor: pointer; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.widget-button:hover { background: #ce181e; color: #FFF; }
.or { float: left; width: 100%; height: 16px; margin: 10px 0px 10px 0px; line-height: 16px; position: relative; color: #333; text-align: center; }
.or span { height: 16px; line-height: 16px; padding: 0px 10px 0px 10px; background: #fff; position: relative; z-index: 11; }
.or:after { content: ''; width: 100%; height: 1px; background: #f0f0f0; position: absolute; left: 0px; top: 9px; z-index: 10; }

blockquote { display: block; clear: both; margin: 0px; padding: 0px 20px 0px 70px; position: relative; }
blockquote:before { content: ''; width: 3px; height: 100%; background: #ce181e; position: absolute; left: 50px; top: 0px; z-index: 10; }
blockquote p { font-size: 16px; line-height: 24px; font-weight: 400; font-style: italic; }

table.stylized { width: 100%; height: auto; padding: 0px 0px 0px 0px; margin: 0px 0px 20px 0px; border-spacing: 0px; border-collapse: separate; border: none; }
table.stylized th { padding: 10px 30px; vertical-align: middle; background: #fff; color: #222; font-size: 14px; line-height: 15px; text-align: center; font-weight: bold; }
table.stylized td { padding: 10px 30px; vertical-align: top; background: #ededed; color: #222; font-size: 14px; line-height: 16px; text-align: left; font-weight: 500; }
table.stylized tr:nth-child(2n) td { background: #eae9e9; }


.text-page a { display: inline-table; font-size: 14px; line-height: 18px; font-weight: normal; text-decoration: none; border-bottom: 1px solid #ce181e; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.text-page a:hover { border-bottom: 1px solid #ce181e; }
.text-page ul { margin: 0px 0px 20px 0px; padding: 0px; line-height: 24px; }
.text-page ul li { padding: 0px 0px 0px 70px; position: relative; }
.text-page ul li:before { content: ''; width: 8px; height: 8px; background: #ce181e; border-radius: 100%; position: absolute; left: 50px; top: 8px; z-index: 10; }
.text-page ol { margin: 0px 0px 20px 0px; padding: 0px; line-height: 24px; list-style: none; }
.text-page ol li{ padding: 0px 0px 0px 70px; position: relative; counter-increment: step-counter; }
.text-page ol li:before{ content: counter(step-counter)'.'; color: #ce181e; position: absolute; left: 50px; top: 0px; z-index: 10; font-size: 14px; line-height: 24px; font-weight: 700; }
.text-page img { max-width: 100%; }
.text-page .button { border: none; padding: 14px 10px; min-width: 190px; font-weight: 600; margin: 10px 0; }
.text-page .button:hover { border: none; }





/* Forms and buttons elements base styles
******************************************/

label { display: block; margin: 0 0 4px; font-size: 14px; font-weight: bold; color: #222; }
label a { -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
label em { font-style: normal; color: #9c242e; }

input.input-text { width: 100%; height: 40px; padding: 5px 10px 5px 10px; margin: 0px 0px 8px 0px; border: 1px solid #ccc; background: #fff; font-size: 13px; color: #6f6f6f; text-decoration: none; font-weight: normal; font-family: 'Montserrat', Arial, sans-serif; outline: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
input.input-text:hover { box-shadow: 0px 0px 2px 2px #e5e5e5; }
input.input-text:focus { box-shadow: 0px 0px 2px 2px #c4c4c4; }
input.input-text.validation-failed { border-color: #da1515; }
input[type="file"] { width: 100%; height: 40px;padding: 0px 5px 0px 0px; margin: 0px 0px 8px 0px; border: 1px solid #ccc; box-sizing: content-box; background: #fff; font-size: 13px; color: #6f6f6f; text-decoration: none; font-weight: normal; font-family: 'Montserrat', Arial, sans-serif; outline: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
input[type="file"]:hover { box-shadow: 0px 0px 2px 2px #e5e5e5; }
input[type="file"]:focus { box-shadow: 0px 0px 2px 2px #c4c4c4; }
input[type="file"].validation-failed { border-color: #da1515; }
select { width: 100%; height: 40px; padding: 9px 25px 8px 10px; margin: 0px 0px 8px 0px; border: 1px solid #ccc; background: #fff url(../images/select-arrow.png) no-repeat right top; font-size: 13px; line-height: 15px; color: #6f6f6f; text-decoration: none; font-weight: normal; font-family: 'Montserrat', Arial, sans-serif; position: relative; -moz-outline: none; outline: none; -webkit-appearance: none; -moz-appearance: none; -o-appearance: none; appearance: none; text-overflow: ''; cursor: pointer; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
select:hover { box-shadow: 0px 0px 2px 2px #e5e5e5; }
select:focus { box-shadow: 0px 0px 2px 2px #c4c4c4; }
select.input-text.validation-failed { border-color: #da1515; }
select.multiselect { padding-right: 5px; background-image: none; }
select::-ms-expand { display: none; }
select:-moz-focusring { color: transparent; text-shadow: 0 0 0 #000; }
textarea { width: 100%; height: 140px; padding: 10px; margin: 0px 0px 8px 0px;  font-size: 13px; line-height: 16px; color: #6f6f6f; text-decoration: none; font-weight: normal; border: 1px solid #ccc; background: #fff; font-family: 'Montserrat', Arial, sans-serif; resize: vertical; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
textarea:hover { box-shadow: 0px 0px 2px 2px #e5e5e5; }
textarea:focus { box-shadow: 0px 0px 2px 2px #c4c4c4; }
textarea.validation-failed { border-color: #da1515; }

.button { float: left; min-width: 160px; padding: 10px 20px; margin: 0px; border: none; background: #000; color: #fff; font-size: 14px; font-weight: bold; text-align: center; text-decoration: none !important; text-transform: uppercase; outline: none; cursor: pointer; font-family: 'Montserrat', Arial, sans-serif; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.button:hover { color: #fff; background: #585858; }
.button.no-background { background: transparent; border: 1px solid #FFF; color: #FFF; }
.button.grey-background { background: #f0f0f0; border: 1px solid #cbcbcb; color: #222; }
.button.white-background { background: #fff; border: 1px solid #cbcbcb; color: #222; }
.button.no-background:hover { color: #222; background: #fff; }
.button.grey-background:hover { color: #222; background: #cbcbcb; }
.button.white-background:hover { color: #222; background: #cbcbcb; }
.button.right { float: right; }
.button .icon-svg.shopping-cart { width: 20px; height: 18px; margin: 1px 4px 0px 3px; fill: #fff; }
.button.has-forword-arrow { position: relative; }
.button.has-forword-arrow:before { content: ''; width: 13px; height: 2px; background: #fff; transform: rotate(45deg); position: absolute; right: 20px; top: 20px; z-index: 20; }
.button.has-forword-arrow:after { content: ''; width: 13px; height: 2px; background: #fff; transform: rotate(-45deg); position: absolute; right: 20px; top: 29px; z-index: 20; }



/* SVG Glob Sizes
*******************/

.icon-svg { float: left; padding: 0px; pointer-events: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.icon-svg.langchange { width: 16px; height: 15px; }
.icon-svg.phone { width: 15px; height: 15px; }
.icon-svg.delivery { width: 25px; height: 15px; }
.icon-svg.compare { width: 24px; height: 23px; }
.icon-svg.wishlist { width: 18px; height: 15px; }
.icon-svg.user { width: 27px; height: 24px; }
.icon-svg.search { width: 24px; height: 24px; }
.icon-svg.shopping-cart { width: 26px; height: 24px; }
.icon-svg.returns { width: 42px; height: 42px; }
.icon-svg.credits { width: 40px; height: 38px; }
.icon-svg.stenik { width: 20px; height: 20px; }
.icon-svg.arrow-up { width: 10px; height: 16px; }
.icon-svg.arrow-down { width: 10px; height: 16px; }
.icon-svg.check { width: 16px; height: 11px; }
.icon-svg.close { width: 22px; height: 20px; }
.icon-svg.facebook { width: 32px; height: 32px; }
.icon-svg.gplus { width: 32px; height: 32px; }
.icon-svg.twitter { width: 32px; height: 32px; }
.icon-svg.insta { width: 32px; height: 32px; }
.icon-svg.youtube { width: 32px; height: 32px; }
.icon-svg.linkedin { width: 32px; height: 32px; }
.icon-svg.back-arrow { width: 13px; height: 10px; }
.icon-svg.arrow-right-angled { width: 7px; height: 13px; }
.icon-svg.grid-view { width: 14px; height: 14px; }
.icon-svg.list-view { width: 17px; height: 14px; }
.icon-svg.share { width: 17px; height: 17px; }
.icon-svg.logout { width: 16px; height: 16px; }
.icon-svg.register { width: 27px; height: 27px; }
.icon-svg.subscribe { width: 16px; height: 14px; }
.icon-svg.in-stock-icon { width: 22px; height: 22px; }
.icon-svg.mail { width: 24px; height: 16px; }



/* Header styles
*******************/

header { background: #fff; box-shadow: 0 0 20px 3px rgba(206,24,30,0.2); position: relative; z-index: 1400; }
.cms-index-index header { width: 100%; position: absolute; top: 0; left: 0; z-index: 100; background: transparent; box-shadow: none;}

.top-line { margin: 0; }
.top-line .row { margin: 0; }

.info-box { float: left; width: auto; margin: 0px 30px 0px 0px; font-size: 13px; line-height: 18px; color: #222; text-decoration: none; }
.info-box span.text { float: left; padding: 0px; margin: 0px; color: #222; text-decoration: none; }
.info-box p { float: left; padding: 0px; margin: 0px; }
.info-box p strong { font-weight: 500; color: #000; }
.info-box.absolute-center { width: 500px; position: absolute; left: 50%; top: 9px; margin: 0px 0px 0px -250px; text-align: center; }
.info-box.absolute-center p { float: none; display: inline-table; padding: 0px; margin: 0px; }
.info-box.absolute-center span.text { float: none; display: inline-table; padding: 0px; margin: 0px; }
.info-box.absolute-center img { float: none; display: inline-table; padding: 0px; margin: 0px 4px -3px 0px; }
.info-box.absolute-center .icon-svg { float: none; display: inline-table; padding: 0px; margin: 0px 4px -3px 0px; }

.icon-link { float: right; padding: 25px 0 30px; cursor: pointer; margin-left: 18px; position: relative; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.icon-link:hover .icon-svg { fill: #ce181e; }
.icon-link .notification { display: none; width: 21px; height: 21px; line-height: 21px; padding: 0px; margin: 0px; border-radius: 100%; background: #ce181e; color: #fff; font-size: 12px; font-weight: bold; text-align: center; position: absolute; right: -14px; top: 12px; z-index: 5; }
.icon-link.has-items .notification { display: block; }

header .header-links.drop-down { float: right; margin: 0; position: relative; height: auto; }
header .header-links .user-button.open-item { display: block; padding: 22px 13px 30px 15px; position: relative; height: auto; cursor: pointer; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
header .header-links.drop-down .user-button:after { content: ""; width: 0; height: 0; position: absolute; top: 35px; right: 0; border-width: 6px 6px 0 6px; border-color: #000 transparent transparent transparent; border-style: solid solid none solid; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
header .header-links.drop-down .user-button:before{ display: none; }
header .header-links:hover .user-button:after { border-top-color: #ce181e; }
header .user-button .icon-svg { float: none; display: inline-block; vertical-align: top; }
header .header-links:hover .user-button .icon-svg { fill: #ce181e; }
header .header-links ul.dropdown-links { display: none; width: 200px; position: absolute; top: 100%; border: none; left: 50%; z-index: 30; margin-left: -100px; background: #FFF; padding: 10px 0; box-shadow: 0 0 20px 3px rgba(206,24,30,0.2); border-top: 3px solid #ce181e; }
header .header-links ul.dropdown-links li { background: none; }
header .header-links ul.dropdown-links li:hover { background: none; }
header .header-links ul.dropdown-links:before { content: ""; width: 0; height: 0; position: absolute; bottom: 103%; left: 50%; margin-left: -10px; border-width: 0 10px 10px 10px; border-color: transparent transparent #ce181e transparent; border-style: solid; }
header .header-links ul.dropdown-links li .clever-link,
header .header-links ul.dropdown-links li a { display: block; cursor: pointer; line-height: 36px; text-align: center; color: #222; font-weight: 600; font-size: 14px; padding: 0; text-transform: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
header .header-links ul.dropdown-links li .clever-link:hover,
header .header-links ul.dropdown-links li a:hover { color: #ce181e; background: none; }

.logo-wrapper { float: left; width: auto; margin: 14px 27px 0 0; position: relative; }
.logo-wrapper .logo { float: left; width: auto; }
.logo-wrapper .logo img { float: left; width: 175px; height: 50px; }

.search-form { float: right; width: 260px; height: 38px; padding: 0; margin: 18px 0 0; position: relative; top: 0; z-index: 5; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.search-form input.search-input { float: left; width: 100%; height: 38px; padding: 0 50px 0 20px; margin: 0; border: 1px solid #ddd; font-size: 14px; font-weight: 600; color: #999; font-family: 'Montserrat'; outline: none; position: relative; z-index: 5; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.search-form input.search-input:focus { box-shadow: 0px 0px 2px 2px #c4c4c4; }
.search-form .search-submit { float: right; width: 38px; height: 38px; padding: 0px; margin: 0px; border: none; background: none; cursor: pointer; outline: none; position: absolute; right: 0px; top: 0px; z-index: 6; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.search-form .search-submit:hover { opacity: 0.7; }
.search-form .search-submit .icon-svg.search { float: left; padding: 0px; margin: 1px 0px 0px 7px; }

header .searchautocomplete { float: right; width: 299px; height: 38px; padding: 0; margin: 18px 0 0; position: relative; top: 0; background: none; border-radius: 0; }
header .searchautocomplete .nav { float: left; width: 100%; height: 100%; background: none; }
header .searchautocomplete .nav .nav-input { float: left; width: 100%; margin: 0; padding: 0; }
header .searchautocomplete .nav .nav-input input { float: left; width: 100%; height: 38px; padding: 0 50px 0 20px; margin: 0; border: 1px solid #ddd; border-radius: 0; font-size: 14px; font-weight: 600; color: #666; font-family: 'Montserrat'; outline: none; position: relative; z-index: 5; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.cms-index-index header .searchautocomplete .nav .nav-input input { border: none; background: #ced2dc; }
header .searchautocomplete .nav-submit-button { float: right; width: 44px; height: 38px; padding: 0; margin: 0; border: none; background: none; cursor: pointer; outline: none; position: absolute; right: 0px; top: 0px; z-index: 15; }
header .searchautocomplete .nav-submit-button .button { float: right; width: 100%; height: 100%; min-width: auto; background: none; border-radius: 0; border: none; }
header .searchautocomplete .nav-submit-button .button:hover { background: none; }
header .searchautocomplete .nav-submit-button .button:hover .icon-svg { fill: #ce181e; }
header .searchautocomplete .searchautocomplete-placeholder { top: 38px; left: 0; width: 299px; border-radius: 0; border: 1px solid #ce181e; padding: 0; }
header .searchautocomplete .searchautocomplete-placeholder ul li { border: none; margin-bottom: 0; border-bottom: 1px solid #ce181e; }
.searchautocomplete .searchautocomplete-placeholder ul li a.name { font-size: 12px; }
header .searchautocomplete .searchautocomplete-placeholder ul li:hover { background: none; }
header .searchautocomplete .searchautocomplete-placeholder .all { padding: 0px; margin: 0; color: #fff; text-align: center; }
header .searchautocomplete .searchautocomplete-placeholder .all a{ background: #ce181e; color: #fff; padding: 10px; float: left; width: 100%; font-size: 12px; line-height: 16px; -webkit-transition: all 0.2s linear;-moz-transition: all 0.2s linear;-o-transition: all 0.2s linear;transition: all 0.2s linear; }
header .searchautocomplete .searchautocomplete-placeholder .all a:hover { background: #000; }
header .searchautocomplete .searchautocomplete-placeholder .all .icon-svg{ fill: #fff; margin: 2px 0 -2px 8px; float: none; display: inline-block; -webkit-transition: all 0.2s linear;-moz-transition: all 0.2s linear;-o-transition: all 0.2s linear;transition: all 0.2s linear; }
header .searchautocomplete .searchautocomplete-placeholder ul li .searchautocomlete-image{ margin-bottom: 30px; margin-right: 8px; }
header .searchautocomplete .searchautocomplete-placeholder ul li .price-box { float: left; }
header .searchautocomplete .searchautocomplete-placeholder ul li .price-box .old-price{ font-size: 16px; }
header .searchautocomplete .searchautocomplete-placeholder ul li .price-box .special-price{ font-size: 16px; }
header .searchautocomplete .searchautocomplete-placeholder ul li .price-box .regular-price{ font-size: 16px; }

.mini-cart-wrapper { float: right; }
.mini-cart { float: right; width: auto; margin: 0; position: relative; }
.mini-cart .mini-cart-open { float: right; width: auto; padding: 25px 0 30px 35px; margin: 0 0 0 15px; position: relative; cursor: pointer; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.mini-cart .open-title { float: left; clear: both; padding: 0 0 0 50px; margin: 0; color: #222; font-size: 13px; line-height: 18px; text-align: left; font-weight: normal; text-decoration: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.mini-cart .items-price { float: left; clear: both; margin: 0; color: #222; font-size: 14px; font-weight: 600; line-height: 24px; text-decoration: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.mini-cart .mini-cart-open .cart-qty { display: inline-block; width: 21px; height: 21px; line-height: 21px; padding: 0px; margin: 0px; border-radius: 100%; background: #ce181e; color: #fff; font-size: 12px; font-weight: bold; text-align: center; position: absolute; left: 15px; top: 12px; z-index: 15; }
.mini-cart .icon-svg.shopping-cart { padding: 0; margin: 0; fill: #000; position: absolute; left: 0; top: 25px; z-index: 10; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;}
.mini-cart .mini-cart-sub { display: none; float: left; width: 354px; height: auto; padding: 10px; background: #fff; border: 1px solid #222; position: absolute; right: 0; top: 55px; z-index: 500; }
.mini-cart .mini-cart-sub .top-title { float: left; width: 352px; height: auto; margin: -10px -10px 20px -10px; padding: 10px 0 11px 0; background: #222; color: #fff; font-size: 16px; font-weight: bold; text-align: center; text-transform: uppercase; }
.mini-cart .mini-cart-sub .mini-cart-item { float: left; width: 100%; margin: 0 0 10px 0; position: relative; }
.mini-cart .mini-cart-sub .mini-cart-item .item-image { float: left; width: 120px; height: 120px; margin: 0 20px 0 0; padding: 5px; border: 1px solid #e5e5e5; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.mini-cart .mini-cart-sub .mini-cart-item .item-image:hover { border-color: #979797; }
.mini-cart .mini-cart-sub .mini-cart-item .item-image img { float: left; width: 100%; height: auto; }
.mini-cart .mini-cart-sub .mini-cart-item .item-info { display: table; padding-right: 25px; }
.mini-cart .mini-cart-sub .mini-cart-item .remove-button { display: inline-block; width: 20px; height: 20px; position: absolute; top: 0; right: 0; text-align: center; line-height: 18px; color: #444; font-weight: 600; font-size: 14px; padding: 0px; border: 1px solid #e1e1e1; cursor: pointer; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;}
.mini-cart .mini-cart-sub .mini-cart-item .remove-button:hover{ border-color: #444; background: #444; color: #FFF; }
.mini-cart .mini-cart-sub .mini-cart-item .item-info .title { display: block; padding: 0; margin: 0 0 8px 0; font-size: 13px; line-height: 16px; color: #333; text-decoration: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.mini-cart .mini-cart-sub .mini-cart-item .item-info .title:hover { color: #ce181e; }
.mini-cart .mini-cart-sub .mini-cart-item .item-info .attribute { display: block; padding: 0; margin: 0; color: #8f8f8f; font-size: 13px; line-height: 16px; text-decoration: none; font-weight: normal; }
.mini-cart .mini-cart-sub .sub-total { display: block; clear: both; padding: 10px; margin: 0 0 5px 0; background: #e6e6e6; color: #313131; font-size: 17px; line-height: 19px; letter-spacing: 0.5px; text-transform: uppercase; text-decoration: none; font-weight: bold; text-align: center; }
.mini-cart .mini-cart-sub .delivery-price { clear: both; padding: 0 10px 0 0; margin: 0 0 8px 0; color: #222; font-size: 13px; line-height: 18px; text-decoration: none; font-weight: normal; text-align: left; }
.mini-cart .mini-cart-sub .delivery-price .icon-svg.delivery { margin: 5px 10px 5px 0; }
.mini-cart .mini-cart-sub .delivery-price.free-shipping { margin: 10px 0 15px 0; color: #7cb015; }
.mini-cart .mini-cart-sub .delivery-price.free-shipping .icon-svg.delivery { margin: 2px 10px 0 0; fill: #7cb015; }
.mini-cart .mini-cart-sub .half-width { display: inline-block; min-width: 50px; width: 48%; margin-left: 1%; margin-right: 1%; }

.mini-cart:hover .mini-cart-open .open-title { color: #ce181e; }
.mini-cart:hover .mini-cart-open .items-price { color: #ce181e; }
.mini-cart:hover .mini-cart-open .icon-svg.shopping-cart { fill: #ce181e; }
.mini-cart.open-mini-cart .open-title { color: #fff; }
.mini-cart.open-mini-cart .items-price { color: #fff; }
.mini-cart.open-mini-cart .icon-svg.shopping-cart { fill: #fff; }

.topMenu { float: left; }
.topMenu li { float: left; margin: 0 7px; }
.topMenu .clever-link,
.topMenu a { float: left; font-size: 14px; line-height: 14px; font-weight: bold; color: #222; padding: 33px 8px 32px; position: relative; text-transform: uppercase; cursor: pointer; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.topMenu .clever-link:hover,
.topMenu a:hover { color: #ce181e; }
.topMenu .clever-link:before,
.topMenu a:before { content: ""; width: 100%; height: 6px; background: #ce181e; position: absolute; top: 0; left: 0; transform: translateY(-6px); -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.topMenu .clever-link:hover:before,
.topMenu a:hover:before { transform: translateY(0); }

.navbar { float: left; }
.navbar-nav { float: left; width: 100%; }
.navbar-nav > li { float: left; -webkit-transition: background 0.2s linear; -moz-transition: background 0.2s linear; -o-transition: background 0.2s linear; transition: background 0.2s linear; }
.navbar-nav > li > a, .navbar-nav > li > .link { cursor: pointer; float: left; padding: 33px 10px 32px; position: relative; color: #222; font-size: 14px; line-height: 14px; font-weight: bold; text-decoration: none; text-transform: uppercase; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.navbar-nav > li.has-sub > a, .navbar-nav > li.has-sub > .link { padding-right: 20px; }
.navbar-nav > li.has-sub > a:after, .navbar-nav > li.has-sub > .link:after { content: ""; width: 0; height: 0; position: absolute; top: 37px; right: 0; border-width: 6px 6px 0 6px; border-color: #000 transparent transparent transparent; border-style: solid solid none solid; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.navbar-nav > li.has-sub > a:hover:after, .navbar-nav > li.has-sub > .link:hover:after { border-top-color: #ce181e; }
.navbar-nav > li:hover > a, .navbar-nav > li:hover > .link { color: #ce181e; }
.navbar-nav > li.active > a, .navbar-nav > li.active > .link { color: #ce181e !important; }
.navbar-nav > li.has-sub.open-sub { color: #ce181e; }
.navbar-nav > li.has-sub.open-sub > a { color: #ce181e; }
.navbar-nav > li.has-sub.open-sub > a:after { border-top-color: #ce181e; }
.navbar-nav > li.has-sub.open-sub > a:before { content: ""; width: 0; height: 0; position: absolute; bottom: 0; left: 50%; margin-left: -10px; border-width: 0 10px 10px 10px; border-color: transparent transparent #ce181e transparent; border-style: solid; }

.attributesplash-group-view .navbar-nav li.brands > a { color: #ce181e !important; }
.attributesplash-page-view .navbar-nav li.brands > a { color: #ce181e !important; }

.stenik-article-article-list .navbar-nav li.articles > a { color: #ce181e !important; }
.stenik-article-article-view .navbar-nav li.articles > a { color: #ce181e !important; }

.stenik-shop-shop-list .navbar-nav li.shops > a { color: #ce181e !important; }
.stenik-shop-shop-view .navbar-nav li.shops > a { color: #ce181e !important; }

.navbar-nav > li .level0 { display: none; }

.navbar-nav .sub-nav { display: none; width: 100%; height: auto; padding: 20px 0px 0px 0px; box-sizing: content-box; background: #FFF; position: absolute; left: 0px; top: 79px; z-index: 2000; border-top: 3px solid #ce181e; box-shadow: 0 10px 20px 3px rgba(206,24,30,0.2); }
.navbar-nav .sub-nav .sub-nav-col { float: left; width: 284px; height: auto; padding: 0px 10px 0px 10px; margin: 0px 8px 0px 8px; }
.navbar-nav .sub-nav .sub-nav-col ul { float: left; width: 100%; height: auto; padding: 0px; margin: 0px 0px 25px 0px; }
.navbar-nav .sub-nav .sub-nav-col ul li { float: left; width: 100%; height: auto; padding: 0px; margin: 0px; }
.navbar-nav .sub-nav .sub-nav-col ul li a.sub-cat-name { float: left; width: 100%; height: auto; padding: 0px; margin: 0px 0px 5px 0px; font-size: 16px; color: #333; text-decoration: none; font-weight: 700; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.navbar-nav .sub-nav .sub-nav-col ul li a.sub-cat-name:hover { color: #ce181e; }
.navbar-nav .sub-nav .sub-nav-col ul li ul.sub-sub-list { margin: 0px; }
.navbar-nav .sub-nav .sub-nav-col ul li ul.sub-sub-list li a { color: #333; margin: 0 0 5px 0; display: block; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.navbar-nav .sub-nav .sub-nav-col ul li ul.sub-sub-list li a:hover { color: #999; }
.navbar-nav .sub-nav .sub-nav-col.banner { float: right; }
.navbar-nav .sub-nav .sub-nav-col.banner p { float: left; width: 100%; height: auto; margin: 0px 0px 20px 0px; padding: 0px; }
.navbar-nav .sub-nav .sub-nav-col.banner img { float: left; max-width: 100%; padding: 0px; margin: 0px; }

.qtip.stenik-tooltip { background: #e9e9e9; border: 1px solid #cbcbcb; border-radius: 10px; }


/* Responsive Header styles
******************************/

.responsive-header { display: none; width: 100%; height: 48px; position: fixed; left: 0; top: 0; z-index: 2000; background: #FFF; box-shadow: 0 5px 10px 3px rgba(206,24,30,0.2); }
.responsive-header .header-phone { width: 38px; height: 38px; position: absolute; left: 55px; top: 5px; line-height: 38px; text-align: center; }
.responsive-header .header-phone svg { float: none; width: 22px; height: 22px; margin-top: -2px; vertical-align: middle; }
.responsive-header .responsive-logo { display: block; width: 148px; height: 46px; padding: 0; margin: 0px auto; position: relative; z-index: 5; text-align: center; }
.responsive-header .responsive-logo img { display: block; width: 129px; height: 40px; margin: 3px auto 0px auto; }
.responsive-header .responsive-logo .slogan { display: block; width: 100%; height: 15px; line-height: 15px; font-size: 11px; color: #818181; font-weight: 700; letter-spacing: 0.7px; text-align: center; text-transform: uppercase; position: absolute; left: 0px; bottom: -1px; z-index: 10; }
.responsive-header .responsive-mini-cart-wrapper { float: right; }
.responsive-header .responsive-cart { width: 48px; height: 48px; line-height: 48px; padding: 0px; margin: 0px; position: absolute; right: 10px; top: 0; z-index: 10; text-align: center;}
.responsive-header .responsive-cart svg { float: none; display: inline-block; margin: -3px 0 0 0; padding: 0; width: 29px; height: 29px; fill: #222; vertical-align: middle; }
.responsive-header .responsive-cart .notification { display: none; position: absolute; right: 3px; top: 3px; z-index: 10; width: 21px; height: 21px; line-height: 21px; background: #ce181e; color: #fff; border-radius: 100%; font-size: 12px; text-align: center; }
.responsive-header .responsive-cart.has-items .notification { display: block; }
.responsive-header .responsive-call { width: 48px; height: 48px; padding: 0px; margin: 0px; position: absolute; right: 68px; top: 0; z-index: 10; }
.responsive-header .responsive-call svg { width: 27px; height: 27px; margin: 4px; fill: #3f3f3f; }
.responsive-header .responsive-search-wrapper {  padding: 0px; }
.responsive-header .responsive-search-wrapper .open-responsive-search { float: left; position: absolute; top: 0; right: 68px; width: 48px; height: 48px; margin: 0; text-align: center; line-height: 48px; }
.responsive-header .responsive-search-wrapper .open-responsive-search .icon-svg.search { float: none; display: inline-block; vertical-align: middle; width: 23px; height: 23px; margin: -3px 0 0; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.responsive-header .responsive-search-wrapper.opened .open-responsive-search .icon-svg.search { fill: #999; }
.responsive-header .responsive-search-wrapper .search-form-wrapper { display: none; float: left; width: 100%; height: auto; margin: 0px; padding: 10px; background: #f1f1f1; border-bottom: 1px solid #d5d5d5; text-align: center; position: absolute; left: 0px; top: 48px; z-index: 2850; }
.responsive-header .responsive-search-wrapper .search-form-wrapper .search-form { width: 100%; margin: 0px; position: relative; left: auto; top: auto; }
.responsive-header .responsive-search-wrapper .searchautocomplete { width: 100%; }


.responsive-header .responsive-menu { width: 38px; height: 38px; padding: 0; margin: 0; position: absolute; left: 0; top: 5px; z-index: 2900; }
.responsive-header .responsive-menu .open-responsive-menu { float: left; width: 38px; height: 38px; padding: 0; margin: 0 0 0 5px; position: relative; z-index: 2900; -webkit-transition: background 0.2s linear; -moz-transition: background 0.2s linear; -o-transition: background 0.2s linear; transition: background 0.2s linear; }
.responsive-header .responsive-menu .open-responsive-menu:before { content: ''; opacity: 1; width: 32px; height: 32px; position: absolute; left: 3px; top: 3px; z-index: 10; background: url(../images/hambuger-menu.svg) no-repeat center center; background-size: 32px 32px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.responsive-header .responsive-menu .open-responsive-menu:after { content: ''; opacity: 0; width: 32px; height: 32px; padding: 0; margin: 0; position: absolute; left: -28px; top: 3px; z-index: 10; background: url(../images/close-menu.svg) no-repeat center center; background-size: 32px 32px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.responsive-header .responsive-menu .responsive-menu-sub { float: left; opacity: 0; width: 0; height: auto; padding: 0; margin: 0; overflow: hidden; background: #222; position: absolute; left: 0; top: 43px; z-index: 2500; visibility: hidden; -webkit-transition: all 0.2s linear; -moz-transition: opacity 0.2s linear; -o-transition: opacity 0.2s linear; transition: opacity 0.2s linear; }
.responsive-header .responsive-menu .tabs-links-content { float: left; width: 100%; }
.responsive-header .responsive-menu .tabs-links-content .responsive-tab-link { float: left; width: 50%; padding: 12px 10px 12px 10px; color: #fff; background: #3c3c3c; font-size: 18px; line-height: 22px; font-weight: bold; text-align: center; text-transform: uppercase; }
.responsive-header .responsive-menu .tabs-links-content .responsive-tab-link.opened { background: #222; }
.responsive-header .responsive-menu .responsive-menu-tab { float: left; width: 100%; height: auto; display: none; }
.responsive-header .responsive-menu .responsive-menu-tab.opened { display: block; }
.responsive-header .responsive-menu .responsive-menu-sub ul { float: left; width: 100%; height: auto; padding: 0; margin: 0; }
.responsive-header .responsive-menu .responsive-menu-sub ul li { float: left; width: 100%; height: auto; padding: 0; margin: 0; border-bottom: 1px solid #3c3c3c; }
.responsive-header .responsive-menu .responsive-menu-sub ul li .clever-link,
.responsive-header .responsive-menu .responsive-menu-sub ul li a { float: left; width: 100%; height: auto; padding: 10px 20px 12px 20px; margin: 0; font-size: 16px; line-height: 22px; color: #fff; text-decoration: none; text-align: left; font-weight: 600; cursor: pointer; }
.responsive-header .responsive-menu .responsive-menu-sub ul li.parent > a { position: relative; padding-right: 40px; }
.responsive-header .responsive-menu .responsive-menu-sub ul li.parent > a:before { content: ''; width: 20px; height: 4px; background: #fff; position: absolute; right: 15px; top: 21px; }
.responsive-header .responsive-menu .responsive-menu-sub ul li.parent > a:after { content: ''; width: 4px; height: 20px; background: #fff; position: absolute; right: 23px; top: 13px; }
.responsive-header .responsive-menu .responsive-menu-sub ul li.parent.open-sub > a:after { display: none; }
.responsive-header .responsive-menu .responsive-menu-sub ul li p { display: none; }
.responsive-header .responsive-menu .responsive-menu-sub ul li ul { display: none; background: #3c3c3c; padding: 0; margin: 0; }
.responsive-header .responsive-menu .responsive-menu-sub ul li ul li { border-bottom: 1px solid #676767; }
.responsive-header .responsive-menu .responsive-menu-sub ul li ul li a { width: 100%; padding: 10px 20px 12px 20px; font-size: 16px; line-height: 18px; font-weight: bold; text-transform: none; font-family: inherit; }
.responsive-header .responsive-menu .responsive-menu-sub ul li ul li ul { background: #676767; padding: 5px 0 5px 0; }
.responsive-header .responsive-menu .responsive-menu-sub ul li ul li ul li { border-bottom: none; }
.responsive-header .responsive-menu .responsive-menu-sub ul li ul li ul li a { padding: 10px 20px 10px 30px; font-size: 14px; line-height: 16px; font-weight: 500; }
.responsive-header .responsive-menu .responsive-menu-fade { float: left; width: 0; height: 0; padding: 0; margin: 0; opacity: 0; visibility: hidden; overflow: hidden; position: fixed; right: 0; top: 0; z-index: 2400; -webkit-transition: opacity 0.2s linear; -moz-transition: opacity 0.2s linear; -o-transition: opacity 0.2s linear; transition: opacity 0.2s linear; }
.responsive-header .responsive-menu .responsive-menu-fade:before { content: ''; float: left; width: 100%; height: 100%; background: #000; opacity: 0.7; }
.responsive-header .responsive-menu .icon-link { width: 100%; line-height: 25px; margin: 18px 0 18px 0; padding: 0 20px 0 20px; color: #fff; font-weight: 600; font-size: 16px; }
.responsive-header .responsive-menu .icon-link .icon-svg { fill: #fff; margin: 0 10px 0 0; }
.responsive-header .responsive-menu .icon-link .notification { left: auto; position: relative; display: none; width: 21px; height: 21px; line-height: 21px; font-weight: bold; background: #ce181e; top: 0; }
.responsive-header .responsive-menu .icon-link.has-items .notification { display: inline-block; }
.responsive-header .responsive-menu.opened .open-responsive-menu:before { opacity: 0; left: -38px; }
.responsive-header .responsive-menu.opened .open-responsive-menu:after { opacity: 1; left: 3px; }
.responsive-header .responsive-menu.opened .responsive-menu-sub { width: 400px !important; opacity: 1; visibility: visible; }
.responsive-header .responsive-menu.opened .responsive-menu-fade { width: 100%; height: 100%; opacity: 1; visibility: visible; }
.responsive-header .responsive-menu.opened .open-responsive-menu { background: #ccc; }
.responsive-header .drop-down.language { width: 35px; margin: 0; position: absolute; left: 48px; top: 9px; z-index: 2800; }
.responsive-header .drop-down.language .open-item { background: #e4e4e4; border: none; text-transform: uppercase; margin: 0; padding: 0; text-align: center; }
.responsive-header .drop-down.language .open-item:after { display: none; }
.responsive-header .drop-down.language .sub-options { padding: 5px 0 5px 0; }
.responsive-header .drop-down.language .sub-options li a { text-transform: uppercase; padding: 0; margin: 0; text-align: center; }



/* Main content styles
************************/
.cms-index-index #main { padding: 0; }
#main {	padding: 20px 0 20px 0; min-height: 55vh;}

.wide-area { position: relative; }
.wide-area.widgets.bottom { margin-bottom: 20px; }
.wide-area.widgets p { margin: 0; }
.wide-area.slider { margin-bottom: 20px; }
.wide-area.products { margin-bottom: 20px }
.wide-area.products .col-sm-3 { padding: 0; }
.wide-area.products .product-slider { margin-left: -10px; margin-right: -10px; }
.wide-area.news { margin-bottom: 20px }
.wide-area.accent-text { margin-bottom: 30px }
.wide-area.product-tabs { margin-bottom: 20px; }
.wide-area.related-products { margin-bottom: 20px; }
.wide-area.upsell-products { margin-bottom: 20px; }
.wide-area.crosssale-products { margin-bottom: 20px; }

.row-header .button { margin-top: -8px; }
.category-banner { margin-bottom: 20px; }
.category-banner img { max-width: 100%; }
.cms-index-noroute .text-page { text-align: center; }
.cms-index-noroute .text-page .button { float: none; display: table; margin: 0 auto; }
.bubble-layer-top { display: none; }
.stenik-ajaxAddToCart-result .ajax-cart-total .button.checkout-button { font-size: 13px; }
.parts-links { font-size: 0; }
.category-parts-links-title { display: block; text-align: center; margin-bottom: 20px; font-weight: 600; font-size: 20px; }
.parts-links-item { display: inline-block; vertical-align: top; width: 285px; margin: 0 20px 20px 0; padding: 15px 10px; background: #F1F1F1; font-weight: 500; font-size: 14px; line-height: 17px; text-align: center; text-decoration: underline; color: #CE191F; box-sizing: border-box; }
.parts-links-item:nth-of-type(3n) { margin-right: 0; }
.parts-links-item:after { content: ""; display: inline-block; vertical-align: middle; width: 6px; height: 10px; margin: 0 0 0 10px; background: url(../images/parts-link-arrow.svg) no-repeat center center; }

/* Widget box styles
**********************/

.widget-box { display: block; margin-bottom: 20px; position: relative; text-decoration: none; }
.widget-box > .widget-image { display: block; width: 100%; height: 100%; position: relative; }
.widget-box > .widget-image:after { content: ""; width: 100%; height: 100%; position: absolute; top: 0; left: 0; background: rgb(63,0,11); /* Old browsers */ background: -moz-linear-gradient(left, rgba(63,0,11,1) 0%, rgba(206,24,30,1) 100%); /* FF3.6-15 */ background: -webkit-linear-gradient(left, rgba(63,0,11,1) 0%,rgba(206,24,30,1) 100%); /* Chrome10-25,Safari5.1-6 */ background: linear-gradient(to right, rgba(63,0,11,1) 0%,rgba(206,24,30,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */ filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3f000b', endColorstr='#ce181e',GradientType=1 ); /* IE6-9 */ opacity: 0; pointer-events: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.widget-box:hover > .widget-image:after { opacity: 0.8; }
.widget-box img { max-width: 100%; height: auto; padding: 0; margin: 0; }
.widget-box.wide .button { position: absolute; top: 50%; margin-top: -20px; right: 20px; }
.widget-box .widget-info { width: 100%; height: auto; padding: 40px; margin: 0; position: absolute; left: 0; top: 0; bottom: 0; right: 0; z-index: 10; }
.widget-box .widget-info .title { display: block; margin: 0 0 10px 0; padding: 0; color: #fff; font-size: 36px; line-height: 40px; font-weight: bold; text-decoration: none; text-transform: uppercase; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.widget-box .widget-info .sub-title { display: block; margin: 0; padding: 0; color: #fff; font-size: 16px; line-height: 20px; font-weight: normal; text-decoration: none; }
.widget-box .widget-info .button { padding: 12px 20px; position: absolute; bottom: 40px; left: 40px; background: rgba(0, 0, 0, 0.4); }
.widget-box:hover .widget-info .button { background: rgba(255, 255, 255, 1); color: #ce181e; }
.widget-box .widget-info .button.left { float: left; left: auto; }
.widget-box .widget-info .button.right { float: right; right: auto; }
.widget-box.dark-colors .widget-info .title { color: #222; }
.widget-box.dark-colors .widget-info .title:hover { color: #515151; }
.widget-box.dark-colors .widget-info .sub-title { color: #222; }
.widget-box.dark-colors .widget-info .button { background: #222; color: #fff; }
.widget-box.dark-colors .widget-info .button:hover { background: #515151; }
.service-box { display: block; margin: 0 0 20px 0; }
a.service-box { border: none; }
a.service-box:hover { border: none; }
.service-box .widget-image { float: left; width: 100%; height: 64px; margin-bottom: 25px; }
.service-box .title { display: block; padding: 0; margin: 0 0 10px; font-size: 18px; line-height: 20px; font-weight: bold; text-transform: uppercase; }
.service-box .sub-title { display: block; font-size: 14px; color: #666; }
.parallax-widget-area { height: 600px; position: relative; overflow: hidden; }
.parallax-widget-area .container { position: relative; }
.parallax-container { width: 100%; height: 100%; }
a.parallax-image { display: block; height: 100%; }
a.parallax-image .parallax {  }
a.parallax-image .parallax img { width: 2000px; max-width: 2000px; height: 1623px; position: absolute; left: 50%; bottom: 0; min-width: 100%; min-height: 100%; transform: translate3d(-50%, 204px, 0); -webkit-transition: all 0s linear; -moz-transition: all 0s linear; -o-transition: all 0s linear; transition: all 0s linear; }
.parallax-info-box { width: 420px; position: absolute; top: 40px; right: 70px; z-index: 10; text-align: right; }
.parallax-info-box a.parallax-title { display: block; margin: 0 0 10px 0; padding: 0; font-size: 42px; line-height: 46px; color: #444; font-weight: bold; text-decoration: none; text-transform: uppercase; border: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.parallax-info-box a.parallax-title:hover { color: #666; border: none; }
.parallax-info-box a.parallax-title span { color: #666; }
.parallax-info-box .parallax-desc { display: block; font-size: 18px; line-height: 24px; font-weight: bold; color: #777; }
.homepage-categories { float: left; width: 100%; border: 1px solid #ebebeb; border-top: 3px solid #ce181e; }
.homepage-categories-title { display: block; padding: 13px 0; font-size: 20px; font-weight: 600; color: #FFF; text-transform: uppercase; text-align: center; background: #000; }
.homepage-categories .navbar { width: 100%; }
.homepage-categories .navbar-nav > li { width: 100%; }
.homepage-categories .navbar-nav > li > a { display: none; }
.homepage-categories .navbar-nav > li > .sub-nav { display: block !important; float: left; width: 100%; position: static; padding: 16px 0 20px; border-top: none; box-shadow: none; }
.homepage-categories .navbar-nav > li > .sub-nav .container { width: 100%; padding: 0; }
.homepage-categories .navbar-nav .sub-nav .sub-nav-col { margin: 0; padding: 0; }
.homepage-categories .navbar-nav .sub-nav .sub-nav-col ul { margin-bottom: 0; }
.homepage-categories .navbar-nav .sub-nav .sub-nav-col ul li { margin-bottom: 0; position: relative; }
.homepage-categories .navbar-nav .sub-nav .sub-nav-col ul li a.sub-cat-name { font-size: 14px; line-height: 18px; font-weight: 600; color: #222; padding: 8px 40px 8px 20px; margin-bottom: 0; background: url(../images/homepage-categories-arrow.svg) no-repeat right 18px top 12px; }
.homepage-categories .navbar-nav .sub-nav .sub-nav-col ul li a.sub-cat-name:hover { color: #ce181e; }
.homepage-categories .navbar-nav .sub-nav .sub-nav-col ul li ul { visibility: hidden; opacity: 0; position: absolute; right: -100%; top: 0; z-index: 500; background: #fff; padding: 5px 0; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }

.homepage-categories .navbar-nav .sub-nav .sub-nav-col ul li:hover > ul,
.homepage-categories .navbar-nav .sub-nav .sub-nav-col ul li ul:hover{ visibility: visible; opacity: 1;  }
.navbar-nav .sub-nav .sub-nav-col ul li ul.sub-sub-list li a{ padding: 5px 15px; }

.cars-widgets { padding: 70px 0; background: url(../images/widget-block-bckg.jpg) no-repeat center top; background-size: cover; }
.widgets h2 { margin: 0 0 25px; text-transform: uppercase; font-size: 30px; }
.widgets h2 ~ p > a { display: inline-block; margin-bottom: 60px; padding: 0 25px; line-height: 36px; border: 1px solid #ce181e; font-size: 12px; font-weight: 500; color: #ce181e; text-decoration: none; text-transform: uppercase; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.widgets h2 ~ p > a:hover { background: #ce181e; color: #FFF; }
.car-widget { display: block; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;  }
.car-widget:hover { box-shadow: 0 0 10px 2px rgba(0,0,0,0.3); }
.car-widget .widget-image { display: block; position: relative; }
.car-widget .widget-image:after { content: ""; width: 100%; height: 100%; position: absolute; top: 0; left: 0; background: rgb(63,0,11); /* Old browsers */ background: -moz-linear-gradient(left, rgba(63,0,11,1) 0%, rgba(206,24,30,1) 100%); /* FF3.6-15 */ background: -webkit-linear-gradient(left, rgba(63,0,11,1) 0%,rgba(206,24,30,1) 100%); /* Chrome10-25,Safari5.1-6 */ background: linear-gradient(to right, rgba(63,0,11,1) 0%,rgba(206,24,30,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */ filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3f000b', endColorstr='#ce181e',GradientType=1 ); /* IE6-9 */ opacity: 0; pointer-events: none; -webkit-transition: all 0.3s linear; -moz-transition: all 0.3s linear; -o-transition: all 0.3s linear; transition: all 0.3s linear;  }
.car-widget .widget-image:before { content: ""; width: 38px; height: 22px; position: absolute; top: 50%; left: 50%; z-index: 10; margin: -11px 0 0 -19px; opacity: 0; background: url(../images/view.svg) no-repeat center center; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.car-widget:hover .widget-image:after { opacity: 0.8; }
.car-widget:hover .widget-image:before { opacity: 1; }
.car-widget .widget-info { display: block; height: 90px; padding: 15px 18px; background: #FFF; border-bottom: 4px solid transparent; color: #222; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.car-widget:hover .widget-info { background: #000; border-bottom-color: #ce181e; color: #FFF; }
.car-widget .widget-info .title { display: block; font-size: 14px; font-weight: 600; }
.car-widget .widget-info .year { display: block; font-size: 14px; font-weight: 600; }
.services-widget { padding: 80px 0; background: #FFF; text-align: center; }
.services-widget h2 { margin-bottom: 70px; }

.search-area { margin: -405px 0 45px; }
.search-box { height: 300px; padding: 25px 30px; position: relative; box-shadow: 0 5px 20px 3px rgba(206,24,30,0.2); background: rgba(255,255,255,0.9); }
.suggestion-box { margin-top: 20px; padding: 15px 20px; position: relative; box-shadow: 0 5px 20px 3px rgba(206,24,30,0.2); background: rgba(255,255,255,0.9); }
.suggestion-box ul { display: flex; flex-direction: row; flex-wrap: wrap }
.suggestion-box ul li { margin: 5px 10px; }
.progress-bar { width: 100%; height: 4px; background: #b7b7b7; position: absolute; top: -4px; left: 0; }
.progress-item { float: left; width: 25%; height: 4px; }
.progress-item.active { background: #ce181e; }
.search-box .row-title { margin-bottom: 40px; }
.search-fields { float: left; width: 100%; margin-bottom: 40px; }
.search-field-box { float: left; width: 267px; margin-right: 30px; }
.search-field-box:last-of-type { margin-right: 0; }
.search-field-label { margin-bottom: 15px; line-height: 38px; padding-left: 8px; }
.search-field-icon { display: inline-block; max-width: 66px; vertical-align: middle; margin: 0 15px 0 0; }
.search-field-icon img { margin-top: -3px; }
.disabled-box .search-field-icon img { -webkit-filter: grayscale(100%); /* Safari 6.0 - 9.0 */ filter: grayscale(100%); }
.search-field-text { font-size: 16px; font-weight: bold; color: #222; text-transform: uppercase; }
#main .homepage-search-form .select2-container { height: 40px; margin-bottom: 8px; }
#main .homepage-search-form .select2-container .select2-selection { height: 40px; border-color: #707070; }
#main .homepage-search-form .disabled-box .select2-container .select2-selection { border-color: #d4d4d4; }
#main .homepage-search-form .select2-container .select2-selection .select2-selection__rendered { height: 40px; line-height: 40px; font-size: 14px; color: #222; }
#main .homepage-search-form .disabled-box .select2-container .select2-selection .select2-selection__rendered { color: #acacac; }
#main .homepage-search-form .select2-container .select2-selection .select2-selection__arrow { width: 41px; height: 38px; background: url(../images/select-arrow2.png) no-repeat center center; }
#main .homepage-search-form .disabled-box .select2-container .select2-selection .select2-selection__arrow { background: url(../images/select-arrow3.png) no-repeat center center; }
.search-box .button-container { float: left; width: 100%; text-align: center; }
.search-box .button-container .widget-button { min-width: 200px; height: 50px; margin-bottom: 0; line-height: 48px; font-size: 20px; font-weight: 500; }
.search-box .button-container .widget-button:disabled { color: #ccc; border-color: #e1e1e1; background: transparent; }
.search-box .button-container .widget-button .icon-svg { float: none; display: inline-block; vertical-align: middle; margin: -3px 10px 0 0; fill: #ce181e; }
.search-box .button-container .widget-button:hover .icon-svg { fill: #FFF; }
.search-box .button-container .widget-button:disabled .icon-svg { fill: #ccc; }

.buying-form-wrapper { float: left; width: 695px; margin-right: 65px; padding-bottom: 40px; position: relative; }
.buying-form-wrapper .button {  position: absolute; bottom: 0; }
.buying-form-wrapper .fieldset-agreements_set input { float: left; }
.buying-form-wrapper .fieldset-agreements_set label {
    display: inline-block;
    max-width: 85%;
    margin: -2px 0 0 8px;
    vertical-align: top;
    font-weight: 500;
}
.buying-form-wrapper .field-row-wrapper { margin-bottom: 20px; }
.buying-form-wrapper .required { color: #CE181E; }
.buying-form-wrapper ~ .text-page { float: left; width: 440px; }
.buying-form-wrapper ~ .text-page ul li { padding-left: 20px; }
.buying-form-wrapper ~ .text-page ul li:before { left: 0; }
.buying-form-wrapper ~ .text-page .col-xs-12 { padding: 0; }
.buying-form-wrapper .input-file { max-width: 330px; }
.buying-form-wrapper .input-text { height: 35px; margin: 0; }
.buying-form-wrapper .input-file { height: auto; padding: 8px; }
.buying-form-wrapper select { height: 35px; margin: 0; background-size: 48px 100%; }
.buying-form-wrapper textarea { height: 100px; margin: 0; }

.homepage-text-block { padding: 20px 0 50px; }



/* Labels styles
*******************/

.label { width: 80px; height: 40px; line-height: 40px; color: #fff; font-size: 16px; text-align: center; text-decoration: none; text-transform: uppercase; font-weight: bold; position: absolute; right: 0; top: 20px; z-index: 15; }
.label.promo { background: #ce181e; }
.label.new { background: #ce181e; left: 0; }
.gallery-box .label { width: 130px; font-size: 30px; z-index: 200; }
.gallery-box .label.promo { right: 30px; }


/* OWL Slider styles
**********************/

/*.owl-carousel.banner-slider { margin-bottom: 20px; }*/
/*.owl-carousel.banner-slider .widget-box { margin-bottom: 0; }*/
/*.owl-carousel.banner-slider .widget-box > a:after { width: 0; height: 0; }*/
/*.owl-carousel.banner-slider .widget-box .widget-info { width: 640px; }*/
/*.owl-carousel.banner-slider .widget-box .widget-info .title { margin-bottom: 45px; }*/
/*.owl-carousel.banner-slider .widget-box .widget-info .title:hover { color: #ce181e; }*/
/*.owl-carousel.banner-slider .widget-box .widget-info .button { position: static;}*/
/*.owl-carousel.banner-slider .widget-box img { min-height: 500px; }*/
/*.owl-carousel.banner-slider .owl-nav { width: 100%; height: 1px; padding: 0 10px 0 10px; position: absolute; left: 0; top: 50%; transform: translateY(-50%); }*/
/*.owl-carousel.banner-slider .owl-nav .owl-prev { float: left; width: 35px; height: 35px; background: url(../images/owl-arrow-prev.svg) no-repeat center center; margin-top: -12px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.owl-carousel.banner-slider .owl-nav .owl-next { float: right; width: 35px; height: 35px; background: url(../images/owl-arrow-next.svg) no-repeat center center; margin-top: -12px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.owl-carousel.banner-slider .owl-nav .owl-prev:hover { opacity: 0.6; }*/
/*.owl-carousel.banner-slider .owl-nav .owl-next:hover { opacity: 0.6; }*/
/*.owl-carousel.banner-slider .owl-dots { width: 100%; height: 1px; position: absolute; left: 0; bottom: 27px; text-align: center; }*/
/*.owl-carousel.banner-slider .owl-dots .owl-dot { display: inline-block; vertical-align: top; width: 10px; height: 10px; margin: 0 5px; background: #fff; border-radius: 100%; position: relative; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.owl-carousel.banner-slider .owl-dots .owl-dot:hover,*/
/*.owl-carousel.banner-slider .owl-dots .owl-dot.active { background: #ce181e; }*/

/*.owl-carousel.product-slider .owl-nav { width: 100%; height: 1px; padding: 0 10px 0 10px; position: absolute; left: 0; top: 50%; margin-top: -19px; }*/
/*.owl-carousel.product-slider .owl-nav .owl-prev { float: left; margin-left: -40px; width: 40px; height: 37px; background: url(../images/owl-arrow-prev2.svg) no-repeat left center; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.owl-carousel.product-slider .owl-nav .owl-next { float: right; margin-right: -40px; width: 40px; height: 37px; background: url(../images/owl-arrow-next2.svg) no-repeat right center; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.owl-carousel.product-slider .owl-nav .owl-prev:hover { opacity: 0.7; }*/
/*.owl-carousel.product-slider .owl-nav .owl-next:hover { opacity: 0.7; }*/
/*.owl-carousel.product-slider .owl-nav .owl-prev.disabled { background: #FFF !important; }*/
/*.owl-carousel.product-slider .owl-nav .owl-next.disabled { background: #FFF !important; }*/



/* Product box styles
***********************/
.homepage-products { padding: 40px 0; text-align: center; }
.product-widget-container { padding: 40px 0; }
.products-list.category-products { margin-bottom: 20px; }
.products-list .col-sm-4 { padding: 0; }
.products-list .col-sm-3 { padding: 0; }
.product-box { display: block; cursor: pointer; height: 450px; text-align: left; position: relative; margin: 10px 0 10px 0; padding: 0 10px; text-decoration: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.last-seen .product-box { height: auto; }
.last-seen .product-box .product-box-info { padding: 10px; }
.last-seen-products .row-title { margin-bottom: 0; }

.product-box .image-wrapper {
    display: block;
    clear: both;
    width: 100%;
    position: relative;
    border: 1px solid #ebebeb;
    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    -o-transition: all 0.2s linear;
    transition: all 0.2s linear;
}

 .catalog-category-view .product-box .image-wrapper {
    padding-top: 100%;
}

.product-box:hover .image-wrapper { border-color: #ce181e; }
.product-box .image-wrapper img { display: table; max-width: 100%; padding: 0; margin: 0; border: none; }

.catalog-category-view .product-box .image-wrapper img.first {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9;
}

.product-box .image-wrapper img.second { opacity: 0; position: absolute; left: 0; top: 0; z-index: 10; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.product-box:hover .image-wrapper.has-second-img img.second { opacity: 1; }
.product-box-info { display: block; padding: 18px; }
.product-title-sku { display: block; height: 87px; }
.product-box .title { display: block; max-height: 54px; margin-bottom: 5px; overflow: hidden; color: #222; font-size: 14px; font-weight: 600; line-height: 18px; text-decoration: none; border: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.product-box:hover .title { color: #ce181e; text-decoration: none; }
.product-box .sku { margin-bottom: 8px !important; font-size: 14px; color: #777; font-weight: 500; }
.product-box .price-box { margin: 0 0 10px; border: none; }
.product-box .actions { opacity: 0; display: block; width: 100%; position: absolute; left: 0; bottom: 0; z-index: 11; padding: 0; margin: 0; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.product-box .actions .button  { float: left; width: 100%; padding: 0 10px; line-height: 58px; font-size: 15px; font-weight: 600; color: #FFF; background: rgb(63,0,11); /* Old browsers */ background: -moz-linear-gradient(left, rgba(63,0,11,1) 0%, rgba(206,24,30,1) 100%); /* FF3.6-15 */ background: -webkit-linear-gradient(left, rgba(63,0,11,1) 0%,rgba(206,24,30,1) 100%); /* Chrome10-25,Safari5.1-6 */ background: linear-gradient(to right, rgba(63,0,11,1) 0%,rgba(206,24,30,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */ filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3f000b', endColorstr='#ce181e',GradientType=1 ); /* IE6-9 */ }
.product-box .actions .button .icon-svg { float: none; display: inline-block; width: 31px; height: 29px; vertical-align: middle; margin: -3px 5px 0 0; }
.product-box .icon-link { width: 40px; height: 40px; margin: 0 0 0 12px; border-radius: 5px; background-color: #ededed; border: 1px solid #e6e6e6; }
.product-box .icon-link .icon-svg.wishlist { margin-left: 10px; margin-top: 12px; }
.product-box .icon-link .icon-svg.compare { margin-left: 9px; margin-top: 12px; }
.product-box:focus { text-decoration: none; }
.product-box:hover .actions { opacity: 1; }
.compare-link { display: inline-block; z-index: 1; padding: 0 40px 0 33px; line-height: 22px; font-size: 14px; font-weight: 600; color: #222; position: relative; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.compare-link:hover,
.compare-link.added { color: #ce181e; }
.compare-link:before { content: ""; width: 22px; height: 22px; position: absolute; top: 0; left: 0; border: 1px solid #222; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.compare-link:hover:before,
.compare-link.added:before { border-color: #ce181e; }
.compare-link.added:after { content: ""; width: 20px; height: 20px; position: absolute; top: 2px; left: 1px; background: url(../images/tick.svg) no-repeat center center; }
.compare-link.added:hover:after{ content: 'x'; text-align: center; width: 20px; height: 20px; font-size: 17px; line-height: 17px; background: none; }
.list-mode .product-box { float: left; width: 100%; height: auto; border: none; box-shadow: 0 0 1px 0 #ababab; }
.list-mode .product-box:after { content: ''; width: 248px; height: 100%; position: absolute; right: 0; top: 0; z-index: 6; background: #f7f7f7; }
.list-mode .product-box .image-wrapper { float: left; width: 274px; height: 274px; margin: 0 24px 0 0; }
.list-mode .product-box .product-info { float: left; width: 320px; padding: 0; margin: 0; }
.col1-layout .list-mode .product-box .product-info { width: 600px; }
.list-mode .product-box .title { max-height: 80px; margin: 0 0 10px 0; font-size: 22px; line-height: 24px; }
.list-mode .product-box .attributes { margin: 0 0 10px 0; }
.list-mode .product-box .short-description { display: block; color: #333; }
.list-mode .product-box .price-actions-col { float: right; width: 248px; padding: 12px 20px 12px 20px; margin: 0 -10px 0 0; position: relative; z-index: 10; text-align: center; }
.list-mode .product-box .price-box { margin-top: -5px; margin-bottom: 10px; }
.list-mode .product-box .price-box .regular-price { width: 100%; text-align: center; font-size: 22px; color: #222; }
.list-mode .product-box .price-box .old-price { width: 100%; margin-bottom: 7px; text-align: center; font-size: 19px; color: #5f5f5f; }
.list-mode .product-box .price-box .special-price { width: 100%; text-align: center; font-size: 22px; color: #e7352b; }
.list-mode .product-box .actions { opacity: 1; position: relative; bottom: auto; left: auto; float: left; width: 100%; padding: 0; }
.list-mode .product-box .actions .button { width: 100%; margin-bottom: 10px; }
.list-mode .product-box .actions .button .wishlist { margin: 3px 2px 0 12px; }
.list-mode .product-box .actions .button .compare { margin: 4px 8px 0 -4px; }



/* Price box styles
*********************/

.price-box { padding: 0; margin: 10px 0 10px 0; }
.price-box .price-label { display: none; }
.price-box .old-price { display: inline-table; margin-bottom: 0; margin-right: 3px; font-size: 18px; line-height: 20px; color: #222; font-weight: bold; text-decoration: line-through; }
.price-box .special-price { display: inline-table; margin-bottom: 0px; font-size: 18px; line-height: 20px; color: #ce181e; font-weight: bold; }
.price-box .regular-price { display: inline-table; font-size: 18px; line-height: 20px; color: #222; font-weight: bold; }
.price-box .price-from { display: inline-table; }
.price-box .price-to { display: inline-table; }
.price-box .price-from .price { display: inline-table; font-size: 16px; line-height: 20px; color: #333; font-weight: bold; }
.price-box .price-from .price-label { display: inline-table; font-size: 14px; line-height: 16px; color: #333; font-weight: normal; }
.price-box .price-to .price { display: inline-table; font-size: 16px; line-height: 20px; color: #333; font-weight: bold; }
.price-box .price-to .price-label { display: inline-table; font-size: 14px; line-height: 16px; color: #333; font-weight: normal; }
.price-box .minimal-price { display: inline-table; font-size: 16px; line-height: 20px; color: #333; font-weight: bold; }
.price-box .minimal-price-link { display: inline-table; }
.price-box .minimal-price-link .price-label { display: inline-table; font-size: 14px; line-height: 16px; color: #333; font-weight: normal; }
.price-box .minimal-price-link .price { display: inline-table; font-size: 16px; line-height: 20px; font-weight: normal; }
.price-box > .price { display: table; font-size: 16px; line-height: 20px; color: #333; font-weight: bold; }
.price-box p { margin-bottom: 0px; }

.mini-cart .mini-cart-sub .mini-cart-item .item-info .price-box .old-price { font-size: 12px; line-height: 16px; }
.mini-cart .mini-cart-sub .mini-cart-item .item-info .price-box .special-price { font-size: 12px; line-height: 16px; }
.mini-cart .mini-cart-sub .mini-cart-item .item-info .price-box .regular-price { font-size: 12px; line-height: 16px; }



/* Brands styles
******************/

.brands-listing { position: relative; margin-bottom: 30px; }
.brands-listing:after { content: ''; width: 2px; height: 100%; background: #fff; position: absolute; right: 0px; top: 0px; z-index: 10; }
.brands-listing:before { content: ''; width: 100%; height: 2px; background: #fff; position: absolute; left: 0px; bottom: 0px; z-index: 10; }
.brands-listing.homepage-brands { padding-top: 30px; padding-bottom: 30px; margin-bottom: 30px; }
.brands-listing.homepage-brands .row-title { float: left; margin-bottom: 30px; }
.brands-listing .row { padding-left: 10px; padding-right: 10px; }
.brands-listing .row .col-sm-3 { padding: 0px; }
.brands-listing .row .col-sm-3 .col-sm-6 { padding: 0px; }
.brand-item { display: block; height: 148px; margin: 0px; background: #fff; border-right: 2px solid #f4f4f4; border-bottom: 2px solid #f4f4f4; text-align: center; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.brand-item img { display: block; max-width: 90%; max-height: 80%; margin: auto; padding: 0px; position: relative; top: 50%; transform: translateY(-50%); }
a.brand-item:active { border-right: 2px solid #f4f4f4; border-bottom: 2px solid #f4f4f4; }
a.brand-item:focus { border-right: 2px solid #f4f4f4; border-bottom: 2px solid #f4f4f4; }
a.brand-item:hover { opacity: 0.6; }
.brand-description { display: table; width: 100%; padding: 20px; margin-bottom: 20px; border-radius: 5px; }
.brand-description .brand-item { float: right; width: 120px; height: 120px; margin: 0px 0px 0px 30px; border: none; }


/* News styles
********************/

/*.news-box { display: block; margin: 0px 0px 20px 0px; }*/
/*.news-box .image-wrapper { display: block; margin: 0px 0px 10px 0px; }*/
/*.news-box .image-wrapper img { display: block; max-width: 100%; padding: 0px; margin: 0px; }*/
/*.news-box .data { display: block; margin: 0px 0px 5px 0px; font-size: 12px; line-height: 15px; font-weight: bold; color: #a4a4a4; }*/
/*.news-box .title { display: block; max-height: 62px; overflow: hidden; font-size: 16px; line-height: 20px; font-weight: bold; color: #333; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.news-box .title:hover { color: #ce181e; }*/
/*.news-box.listing-style { float: left; width: 100%; border: 1px solid #d0d0d0; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.news-box.listing-style:hover { background: #f1f1f1; }*/
/*.news-box.listing-style .image-wrapper { float: left; margin: 0px; }*/
/*.news-box.listing-style .title { margin-bottom: 10px; }*/
/*.news-box.listing-style .news-listing-info { float: right; width: 445px; padding: 20px; }*/
/*.news-box.listing-style .short-description { color: #222; }*/
/*.text-page a.news-inner { border: none; margin: 0px 0px 20px 0px; }*/
/*.news-data { display: block; margin: -10px 0px 15px 0px; font-size: 12px; line-height: 15px; font-weight: bold; color: #a4a4a4; }*/
/*.button.back-to-news .icon-svg.back-arrow { margin: 6px 10px 0px -5px; }*/




/* Аccent text content styles
*******************************/

.accent-text-content { padding-top: 25px; margin-top: 25px; border-top: 1px solid #ebebeb; }
.accent-text-content.category-description { margin-bottom: 20px; }
.accent-text-content h1 { font-size: 25px; line-height: 27px; }
.accent-text-content h2, .accent-text-content h3, .accent-text-content h4, .accent-text-content h5, .accent-text-content h6 { margin-top: 0px; }



/* Social icons styles
************************/

.social { display: inline-block; padding: 0px; margin: 0px 5px 5px 0px; border-radius: 100%; }
.social .icon-svg { fill: #333; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.social:hover .icon-svg { fill: #ce181e; }



/* Drop down items styles
***************************/

.block-layered-nav { float: left; width: 100%; background: #e4e4e4; padding: 7px 0px 7px 0px; }
.drop-down { float: left; width: auto; height: 35px; margin: 0px 45px 0px 0px; position: relative; }
.drop-down .open-item { display: block; width: 100%; height: 35px; line-height: 35px; margin: 0px; padding: 0 40px 0 10px; border: 1px solid #c9c9c9; font-size: 12px; color: #222; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.drop-down .open-item span { display: block; white-space: nowrap; overflow: hidden; }
.drop-down .open-item:after { content: ''; width: 0; height: 0; border-style: solid; border-width: 6px 6px 0px 6px; border-color: #000 transparent transparent transparent; top: 15px; right: 12px; position: absolute; cursor: pointer; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.drop-down .open-item:before { content: ''; width: 35px; height: 33px; position: absolute; top: 1px; right: 1px; border-left: 1px solid #c9c9c9; background: #FFF; }
.drop-down.open .open-item { border-color: #ce181e; }
.drop-down.open .open-item:after { border-color: #000 transparent transparent transparent; }
.drop-down .sub-options { display: none; float: left; width: 100%; height: auto; padding: 10px 10px 7px 10px; position: absolute; left: 0px; top: 34px; z-index: 1300; background: #FFF; border: 1px solid #ce181e; }
.drop-down.open-from-top .sub-options { top: auto !important; bottom: 34px !important; }
.drop-down .sub-options li { float: left; width: 100%; height: auto; }
.drop-down .sub-options li .clever-link,
.drop-down .sub-options li a { display: block; cursor: pointer; margin: 0px 0px 3px 0px; color: #222; position: relative; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.drop-down .sub-options li:hover .clever-link,
.drop-down .sub-options li:hover a { color: #ce181e; }
.drop-down .sub-options li .clever-link img,
.drop-down .sub-options li a img { float: left; padding: 0px; margin: 0px; }
.drop-down .sub-options li label { display: inline; color: #333; }
.drop-down .sub-options li label a { border: none; }
.drop-down.multiselect .sub-options li .clever-link,
.drop-down.multiselect .sub-options li a { display: block; margin-bottom: 10px; padding-left: 32px; line-height: 22px; font-size: 14px; font-weight: 600; color: #222; position: relative; cursor: pointer; }
.drop-down.multiselect .sub-options li .clever-link:before,
.drop-down.multiselect .sub-options li a:before { content: ''; width: 22px; height: 22px; background: #fff; border: 1px solid #ccc; position: absolute; left: 0; top: 0; z-index: 5; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.drop-down.multiselect .sub-options li:hover .clever-link:before,
.drop-down.multiselect .sub-options li:hover a:before { border-color: #ce181e; }
.drop-down.multiselect .sub-options li.active .clever-link:before,
.drop-down.multiselect .sub-options li.active a:before { background: url(../images/tick.svg) no-repeat center center; border-color: #ce181e; }
.drop-down .sub-options.has-img li { float: left; width: auto; margin: 4px 10px 6px 0px; }
.drop-down .sub-options.has-img li a { padding-left: 0px; margin: 0px; border: 1px solid #cecece; border-radius: 5px; overflow: hidden; }
.drop-down .sub-options.has-img li a:before { display: none; }
.drop-down .sub-options.has-img li:hover a { border-color: #555; opacity: 0.8; }
.drop-down .sub-options.has-img li.active a { border-color: #555; opacity: 1 !important; }
.drop-down .sub-options li .checkbox-filter { display: none; }

header .drop-down { height: 18px; line-height: 18px; padding: 0px; margin: 0px 18px 0px 0px; }
header .drop-down .open-item { float: left; width: auto; height: 22px; line-height: 22px; padding: 0px 12px 0px 0px; border: none; color: #222; text-transform: uppercase; text-decoration: none; font-size: 12px; font-weight: normal; letter-spacing: 0; font-family: 'Montserrat'; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
header .drop-down .open-item:after { top: 9px; right: 2px; border-color: #000 transparent transparent transparent; }
header .drop-down.open .open-item { background: none; }
header .drop-down.open .open-item:after { opacity: 0.5; }
header .drop-down .open-item:hover { color: #555; }
header .drop-down .open-item img { float: left; width: auto; height: auto; margin: 5px 5px 0px 0px; }
header .drop-down .open-item .icon-svg { float: left; margin-top: 3px; fill: #000; pointer-events: none; }
header .drop-down .sub-options { width: 40px; padding: 0px; background: #f0f0f0; box-shadow: none; left: -5px; top: 22px; border-radius: 0; }
header .drop-down .sub-options li { background: #f0f0f0; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
header .drop-down .sub-options li:hover { background: #d7d7d7; }
header .drop-down .sub-options li a { padding: 5px; margin: 0px; text-transform: uppercase; font-size: 12px; }
header .drop-down .sub-options li:hover a { color: #555; }
header .drop-down .sub-options a img { margin: 5px 5px 0px 0px; }
header .drop-down.language .sub-options { width: 55px; }
header .drop-down.language.no-flags img { display: none; }
header .drop-down.language.no-flags .sub-options { width: 27px; }

.sidebar  { padding-left: 0; }
.sidebar .block-layered-nav { background: none; }
.sidebar .drop-down { width: 100%; height: auto; margin: 0px 0px 15px 0px; padding: 0px 0px 10px 0px; border-bottom: 1px solid #e4e4e4; }
.sidebar .drop-down .open-item { height: auto; padding: 0px; margin-bottom: 15px; border: none; color: #222; font-weight: bold; font-size: 14px; line-height: 20px; text-transform: uppercase; }
.sidebar .drop-down .open-item:after { display: none; }
.sidebar .drop-down .open-item:before { display: none; }
.sidebar .drop-down .sub-options { display: block !important; width: 100%; height: auto; max-height: 300px; overflow-y: auto; padding: 0px; background: none; position: relative; top: auto; left: auto; box-shadow: none; border-radius: 0; border: none; }
.sidebar .drop-down .sub-options li:hover a { color: #ce181e; }
.sidebar .drop-down .sub-options li.active a { color: #ce181e !important; }
.sidebar .drop-down.has-img .sub-options { width: 105%; }
.sidebar .drop-down.has-img .open-item { margin-bottom: 10px; }
.sidebar .drop-down.categories { border-bottom: none; padding-bottom: 0; }
.sidebar .drop-down.categories .open-item { padding: 17px 20px; font-size: 14px; font-weight: bold; color: #FFF; background: #000; border-top: 3px solid #ce181e; border-radius: 0; margin-bottom: 0; }
.sidebar .drop-down.categories ul { padding: 10px 0; border: 1px solid #ebebeb; border-top: none; max-height: none; }
.sidebar .drop-down.categories ul a { font-size: 14px; line-height: 18px; font-weight: 600; color: #222; padding: 8px 40px 8px 20px; margin-bottom: 0; background: url(../images/homepage-categories-arrow.svg) no-repeat right 18px top 12px; }
.sidebar .drop-down.currency { display: none; }

.filters-wrapper { float: left; width: 100%; border: 1px solid #ebebeb; }
.filters-title { display: block; padding: 17px 20px; font-size: 14px; font-weight: bold; color: #FFF; background: #000; border-top: 3px solid #ce181e; border-radius: 0; margin-bottom: 0; text-transform: uppercase; }
.filters-content { padding: 20px; }

.filters-main-content .drop-down { margin: 0px 20px 0px 0px; }
.filters-main-content .drop-down .open-item:after { right: 6px; }
.filters-main-content .drop-down .open-item { padding: 0px 18px 0px 10px; border: none; color: #333; font-weight: bold; font-size: 16px; text-transform: uppercase; }
.filters-main-content .drop-down.open .open-item { background: #fff; color: #ce181e; }
.filters-main-content .drop-down .open-item:after { border-color: #000 transparent transparent transparent; }
.filters-main-content .drop-down .sub-options { width: 245px; top: 30px; background: #fff; border-radius: 0px 5px 5px 5px; box-shadow: 0px 2px 2px 0px #dedede; }
.filters-main-content .drop-down .sub-options li:hover a { color: #ce181e; }
.filters-main-content .drop-down .sub-options li.active a { color: #ce181e !important; }


/* Fitler price slider styles
*******************************/

div.layer-slider { float: left; width: 100%; height: auto; padding: 0px; margin: 0px; }
div.layer-slider .price-slider { height: 14px; margin: 4px 0px 10px 0px; }
div.layer-slider .price-limit { display: none; }
div.layer-slider .bg { height: 6px; background-color: #ebebeb; top: 4px; pointer-events: none; }
div.layer-slider .span { margin-left: 13px; height: 6px; top: 4px; background: #ce181e; pointer-events: none; }
div.layer-slider .handle { width: 14px; height: 14px; background: #ce181e; border-radius: 100%; }
div.layer-slider .left { float: left; }
div.layer-slider .right { float: right; }
div.layer-slider .price { color: #222; font-size: 16px; line-height: 16px; text-align: left; font-weight: 600; text-decoration: none; }
div.layer-slider .price-slider-inputs { float: left; height: auto; padding: 0px; margin: 5px 0px 5px 0px; }
div.layer-slider .price-slider-inputs input.input-text { float: left !important; width: 91px; height: 35px; margin: 0 8px 0 0; font-size: 12px; font-weight: 500; color: #222; }



/* Fitlers State styles
************************/

.state-content { float: left; width: 100%; padding: 15px 20px; margin: 0 0 30px; background: #ebebeb; border-top: 3px solid #ce181e; }
.state-content .state-title { float: left; width: 100%; height: auto; padding: 0; margin: 0 0 25px; border: none; color: #222; font-weight: bold; font-size: 18px; line-height: 20px; text-transform: uppercase; }
.state-content .state-filter { float: left; width: 100%; height: auto; padding: 0; margin: 0 0 15px; }
.state-content .state-filter .filter-label { float: left; width: 100%; height: auto; padding: 0; margin: 0 0 5px; font-size: 14px; font-weight: bold; color: #222; text-transform: uppercase; }
.state-content .state-filter .filter { float: left; width: auto; height: auto; padding: 0 29px 0 10px; margin: 0 8px 0 0; background: #fff; position: relative; font-size: 14px; line-height: 30px; font-weight: 600; color: #222; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.state-content .state-filter .filter:hover { color: #ce181e; }
.state-content .state-filter .filter:after { content: 'x'; position: absolute; right: 0; top: 4px; width: 18px; height: 18px; line-height: 18px; padding: 0; margin: 0; color: #999; font-weight: 600; font-size: 18px; text-decoration: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.state-content .state-filter .filter:hover:after { color: #ce181e; }
.state-content .remove-all-filters { padding: 0 20px 0 0; position: relative; font-size: 16px; font-weight: bold; color: #ce181e; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.state-content .remove-all-filters:after { content: 'x'; position: absolute; right: 0; top: 1px; width: 16px; height: 16px; line-height: 18px; padding: 0; margin: 0; color: #ce181e; font-weight: bold; font-size: 18px; text-decoration: none; text-align: center; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.state-content .remove-all-filters:hover { color: #fd5c53; }
.state-content .remove-all-filters:hover:after { color: #fd5c53; }


/* Breadcrumbs styles
************************/

.breadcrumbs {
	display: block;
	margin-bottom: 20px;
	width: 100%;
	overflow-x: auto;
	white-space: nowrap;
}

.breadcrumbs ul li { display: inline-block; padding-left: 30px; position: relative; color: #ce181e; font-size: 12px; line-height: 16px; font-weight: 600; }
.breadcrumbs ul li:before { content: ''; width: 7px; height: 13px; position: absolute; left: 10px; top: 1px; z-index: 5; background: url(../images/breadcrumbs-arrow.svg) no-repeat left top; }
.breadcrumbs ul li a { color: #777; font-size: 12px; line-height: 16px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.breadcrumbs ul li a:hover { color: #ce181e; }
.breadcrumbs ul li.home { padding-left: 0; }
.breadcrumbs ul li.home:before { display: none; }



/* Toolbar styles
*********************/

.toolbar { float: left; width: 100%; height: auto; clear: both; margin: 0 0 20px 0; }
.toolbar .sort-title { float: left; margin-right: 15px; line-height: 35px; font-size: 12px; color: #222; }
.toolbar .drop-down:first-of-type { min-width: 180px; margin-right: 10px; }
.toolbar .sorting-arrow { float: left; width: 30px; height: 30px; margin: 0 10px 0 -8px; padding: 0; text-align: center; cursor: pointer; }
.toolbar .sorting-arrow .icon-svg { float: none; display: block; margin: 7px auto 7px auto; fill: #999; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.toolbar .sorting-arrow:hover .icon-svg { fill: #ce181e; }
.toolbar .sorting-arrow.descending .icon-svg { -ms-transform: rotate(180deg); -webkit-transform: rotate(180deg); transform: rotate(180deg); }
.toolbar .view-mode { float: left; width: auto; height: 30px; margin: 0 10px 0 0; }
.toolbar .view-mode a { float: left; width: 30px; height: 30px; margin: 0 3px 0 0; border-radius: 5px; border: 1px solid #cbcbcb; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.toolbar .view-mode a:hover { border-color: #ce181e; }
.toolbar .view-mode a .icon-svg { float: none; display: block; margin: 7px auto 7px auto; opacity: 0.3; }
.toolbar .view-mode a.active .icon-svg { opacity: 1; }
.toolbar .show-only-available { float: left; width: auto; height: 30px; margin: 0 0 0 10px; }
.toolbar .show-only-available input.checkbox { float: left; width: auto; height: auto; padding: 0; margin: 9px 6px 0 0; }
.toolbar .show-only-available label { float: left; width: auto; height: 30px; line-height: 30px; padding: 0; margin: 0; }



/* Paging styles
*******************/

.pager { float: right; }
.pager a { float: left; width: auto; min-width: 35px; height: 35px; line-height: 35px; padding: 0 5px; margin: 0px 3px 0px 3px; background: #fff; border: 1px solid #fff; position: relative; font-size: 12px; color: #222222; font-weight: normal; text-align: center; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.pager a:hover { color: #ce181e; }
.pager a.selected { border-color: #c9c9c9; color: #ce181e; font-weight: 600; }
.pager a.prev { background: url(../images/paging-prev.svg) no-repeat center center; }
.pager a.prev.disable { display: none; }
.pager a.next { background: url(../images/paging-next.svg) no-repeat center center; margin-right: 0px; }
.pager a.next.disable { display: none; }
.pager span.sep { float: left; width: 35px; height: 35px; line-height: 35px; padding: 0px; margin: 0px 0px 0px 8px; color: #6f6f6f; font-weight: 500; text-align: center; }
.pager.news-pager { float: left; width: 100%; text-align: center; margin: 0px 0px 10px 0px; }
.pager.news-pager a { float: none; display: inline-block; }
.pager.news-pager a.prev { margin-bottom: -9px; }
.pager.news-pager a.next { margin-bottom: -9px; }
.pager.news-pager span.sep { float: none; display: inline-block; }


/* Product view gallery styles
*******************************/

/*.gallery-box { position: relative; overflow: hidden; padding-right: 30px; }*/
/*.gallery-box .gallery-main-images { border: 1px solid #EBEBEB; }*/
/*.gallery-box .gallery-thumbnails { width: 100%; height: auto; z-index: 30; outline: none; }*/
/*.gallery-box .gallery-thumbnails .thumb { float: left; width: 110px; height: auto; padding: 0; margin: 0px 10px 10px 0px; border: 1px solid #ebebeb; cursor: pointer; outline: none; }*/
/*.gallery-box .gallery-thumbnails .thumb.active { border-color: #ce181e; }*/
/*.gallery-box .gallery-thumbnails .thumb img { float: left; max-width: 100%; padding: 0; margin: 0; outline: none; }*/
/*.gallery-box .gallery-thumbnails .slick-arrow { width: 30px; height: 30px; padding: 0px; background: #fff; border: 1px solid #e4e4e4; position: absolute; z-index: 30; bottom: 0px; font-size: 1px; text-indent: -999px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.gallery-box .gallery-thumbnails .slick-arrow.slick-prev { left: 5px; }*/
/*.gallery-box .gallery-thumbnails .slick-arrow.slick-next { left: auto; right: 5px; }*/
/*.gallery-box .gallery-thumbnails .slick-arrow.slick-prev:before { content:''; width: 0; height: 0; margin: -2px -5px 0px 0px; border-style: solid; border-width: 0px 5px 6px 5px; border-color: transparent transparent #999 transparent; top: 50%; right: 50%; position: absolute; }*/
/*.gallery-box .gallery-thumbnails .slick-arrow.slick-next:before { content:''; width: 0; height: 0; margin: -2px -6px 0px 0px; border-style: solid; border-width: 6px 5px 0px 5px; border-color: #999 transparent transparent transparent; top: 50%; right: 50%; position: absolute; }*/
/*.gallery-box .gallery-thumbnails .slick-arrow:hover { background: #eaeaea; }*/
/*.gallery-box .gallery-main-images { padding: 0; position: relative; z-index: 25; }*/
/*.gallery-box .gallery-main-images .main-image img { max-width: 100%; }*/
/*.gallery-box .gallery-main-images.one-image { padding-left: 0px; }*/
/*.gallery-box ul.slick-dots { display: block; width: 100%; height: auto; padding: 0px; margin: 10px 0px 0px 0px; text-align: center; }*/
/*.gallery-box ul.slick-dots li { display: inline-block; margin: 0px 5px 0px 5px; }*/
/*.gallery-box ul.slick-dots li button { padding: 5px 15px; margin: 0px; border: none; background-color: #ce181e; color: #FFF; font-size: 12px; font-weight: 500; text-align: center; text-decoration: none !important; text-transform: uppercase; outline: none; cursor: pointer; font-family: 'Montserrat', sans-serif; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.gallery-box ul.slick-dots li.slick-active button { background: #3f000b; }*/


/* Product view info
**********************/

/*.catalog-product-view h1 { margin: 0px 0px 15px 0px; text-transform: none; font-size: 36px; font-weight: 600; line-height: 44px; }*/
/*.product-view-main { margin-bottom: 20px; }*/
/*.status-row { margin-bottom: 25px; border-top: 1px solid #ebebeb; border-bottom: 1px solid #ebebeb; padding: 12px 0; line-height: 22px; }*/
/*.status-row p.sku { float: left; margin: 0; font-size: 14px; font-weight: 600; color: #777; }*/
/*.status-row p.sku:after { content: "|"; color: #777; margin: 0 15px 0 12px; }*/
/*.status-row p.sku span { color: #ce181e; }*/
/*.availability { float: left; padding: 0; margin: 0; font-size: 14px; line-height: 22px; font-weight: 600; text-decoration: none; }*/
/*.availability:after { content: "|"; color: #777; margin: 0 13px 0 10px; }*/
/*.availability .icon-svg { float: left; }*/
/*.availability.in-stock { color: #7ca769; }*/
/*.availability.out-of-stock { color: #e7352b; }*/
/*.availability.in-stock .icon-svg.in-stock-icon { margin: 0 10px 0 0; fill: #7ca769; }*/
/*.availability.out-of-stock .icon-svg.close { width: 22px; height: 22px; margin: 0 10px 0 0; fill: #e7352b; }*/
/*.status-row .compare-link { padding-right: 0; }*/
/*.status-row .product-contacts-button { display: inline-block; vertical-align: middle; width: 20px; height: 20px; background: url(../images/contacts-phone.svg) repeat-x left top; }*/
/*.product-options { margin-bottom: 20px; padding: 0; }*/
/*.product-options .price-box { float: left; }*/
/*.product-options .price-box .old-price { font-size: 29px; line-height: 50px; padding-right: 5px; }*/
/*.product-options .price-box .special-price { font-size: 32px; line-height: 50px; }*/
/*.product-options .price-box .regular-price { font-size: 32px; line-height: 50px; }*/
/*.product-options .button { float: left; padding: 0 20px; margin-right: 10px; margin-top:10px; margin-left: 20px; line-height: 50px; font-size: 15px; font-weight: 600; color: #FFF;*/
/*	background: rgb(206,24,30); !* Old browsers *!*/
/*	background: -moz-linear-gradient(left, rgba(206,24,30,1) 0%, rgba(63,0,11,1) 100%); !* FF3.6-15 *!*/
/*	background: -webkit-linear-gradient(left, rgba(206,24,30,1) 0%,rgba(63,0,11,1) 100%); !* Chrome10-25,Safari5.1-6 *!*/
/*	background: linear-gradient(to right, rgba(206,24,30,1) 0%,rgba(63,0,11,1) 100%); !* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ *!*/
/*	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ce181e', endColorstr='#3f000b',GradientType=1 ); !* IE6-9 *!*/
/*}*/
/*.product-slider-replacements  .product-box .button { margin-left: 0; line-height: 50px; font-size: 15px; font-weight: 600; color: #FFF;}*/
/*.product-options .button.checkout-color.inactive { opacity: 0.5; cursor: default; }*/
/*.product-options .button .icon-svg { float: none; display: inline-block; width: 31px; height: 29px; vertical-align: middle; margin: -3px 5px 0 0; }*/
/*.product-options input.hidden-qty { width: 0px; height: 0px; opacity: 0; overflow: hidden; visibility: hidden; padding: 0px; margin: 0px; position: absolute; right: 0px; z-index: 0; bottom: 0px; }*/
/*.add-to-links { padding-left: 20px; }*/
/*.add-to-links .rating-box { float: left; margin: 0px; }*/
/*.add-to-links .link { float: left; width: 100%; margin: 10px 0px 10px 0px; color: #222; font-size: 13px; line-height: 18px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.add-to-links .link:hover { color: #ce181e; }*/
/*.add-to-links .link .icon-svg { float: left; margin: 3px 8px 0px 0px; }*/
/*.add-to-links .link.view-rating { width: auto; margin: 0px 0px 0px 10px; }*/
/*.add-to-links hr { clear: both; margin: 10px 0px 10px 0px; }*/
/*.info-cms-block { float: left; width: 100%; margin: 10px 0px 10px 0px; padding: 0px 0px 0px 28px; position: relative; font-size: 13px; line-height: 16px; }*/
/*.info-cms-block .icon-svg { float: left; position: absolute; left: -3px; top: 2px; z-index: 10; }*/
/*.info-cms-block p { margin-bottom: 0px; font-size: 13px; line-height: 16px; }*/
/*.info-cms-block a { color: #222; border-bottom: 1px solid #222; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.info-cms-block a:hover { color: #ce181e; border-bottom: 1px solid #9f9f9f; }*/
/*table.stylized.attributes td { width: 50%; }*/
/*table.stylized.attributes tr td:first-of-type { font-weight: bold; vertical-align: middle; }*/
/*table.stylized.attributes tr:nth-of-type(2n) td { background: #FFF; }*/
/*table.stylized.attributes .brand-logo-img { max-height: 40px; }*/
/*.reserved-cart,*/
/*.cr-product-timer { font-size: 14px; font-weight: 600; color: #ce181e; }*/
/*.reserved-cart { margin-bottom: 20px; }*/
/*.error-signal { color: #222; font-size: 14px; font-weight: 500; text-decoration: underline; }*/
/*.error-signal:before { content: "|"; display: inline-block; text-decoration: none; color: #777; margin: 0 10px; }*/
/*.error-signal:after { content: "|"; display: inline-block; color: #777; margin: 0 10px; text-decoration: none; }*/
/*.error-signal:hover { text-decoration: none; }*/
/*#errorsignal-form { padding: 30px 50px 95px 50px; position: relative;}*/
/*#errorsignal-form .form-title { display: block; margin-bottom: 25px; padding-bottom: 20px; font-size: 30px; font-weight: bold; text-align: center; border-bottom: 1px solid #EBEBEB; text-transform: uppercase; color: #222; }*/
/*#errorsignal-form .input-box { margin-bottom: 15px; float: left; width: 100%; }*/
/*#errorsignal-form .required { color: #CE181E; }*/
/*#errorsignal-form .fieldset-wrapper-agreements_set input { float: left; }*/
/*#errorsignal-form .fieldset-wrapper-agreements_set label { display: inline-block; max-width: 85%; font-weight: 500; margin: -2px 0 0 8px; vertical-align: top; }*/
/*#errorsignal-form #stenik_form_errorsignal_form_submit_row { margin: 0px; }*/
/*#errorsignal-form .button { height: 55px; padding: 0 20px; line-height: 55px; background: #222; position: absolute; bottom: 65px; }*/
/*#errorsignal-form input ~ label { display: inline-block; max-width: 85%; margin: -2px 0 0 8px; vertical-align: top; }*/
/*.we-recommend-categories { float: left; width: 100%; margin-bottom: 50px; border-top: 1px solid #EDEDED; border-bottom: 1px solid #EDEDED; padding: 20px 10px; box-sizing: border-box; }*/
/*.we-recommend-categories strong { display: block; margin-bottom: 10px; }*/
/*.we-recommend-categories ul li { float: left; margin: 10px 20px 10px 0; }*/
/*.we-recommend-categories ul li a { float: left; padding: 0 20px; line-height: 48px; font-size: 14px; font-weight: 500; text-decoration: none; text-transform: uppercase; color: #CE181E; border: 1px solid #CE181E; transition: all 0.2s; }*/
/*.we-recommend-categories ul li a:hover { background: #CE181E; color: #FFF; }*/

/* Custom options for product styles
*************************************/

/*.product-options-content { float: left; width: 100%; margin: 10px 0px 10px 0px; }*/
/*.product-options-content dl { float: left; width: 100%; padding: 0px; margin: 0px; }*/
/*.product-options-content dl dt { float: left; width: 100%; padding: 0px; margin: 0px; }*/
/*.product-options-content dl dt label { display: inline-block; }*/
/*.product-options-content dl dt span.price-notice { display: inline-block; font-style: italic; color: #999; font-size: 13px; }*/
/*.product-options-content dl dd { float: left; width: 100%; padding: 0px 0px 10px 0px; margin: 0px; }*/
/*.product-options-content dl dd ul.options-list { float: left; width: 100%; }*/
/*.product-options-content dl dd ul.options-list li { float: left; width: 100%; }*/
/*.product-options-content dl dd ul.options-list li input.radio { float: left; width: auto; margin: 3px 8px 0px 0px; }*/
/*.product-options-content dl dd ul.options-list li input.checkbox { float: left; width: auto; margin: 3px 8px 0px 0px; }*/
/*.product-options-content dl dd ul.options-list li .label { float: left; width: 90%; height: auto; line-height: 18px; padding: 0px; text-align: left; position: relative; left: auto; top: auto; right: auto; bottom: auto; font-weight: normal; }*/
/*.product-options-content dl dd p.no-margin { margin: 0px; padding: 0px 5px 0px 5px; font-size: 12px; color: #999; }*/
/*.product-options-content .qty-holder { float: left; width: 100%; height: auto; padding: 0px; margin: 0px; }*/
/*.product-options-content .qty-holder label { float: left; width: auto; height: auto; padding: 0px; margin: 0px 5px 0px 0px; }*/
/*.product-options-content .input-text.qty.qty-disabled { width: auto; min-width: 30px; height: 17px; padding: 0px; margin: 0px; background: none; border: none; box-shadow: none; }*/


/* Rating styles
******************/

/*.rating-box { display: block; width: 96px; height: 18px; padding: 0px; margin: 0px auto 20px auto; position: relative; background: url(../images/star.svg) repeat-x left top; }*/
/*.rating-box .rating { float: left; height: 18px; background: url(../images/star-yellow.svg) repeat-x left top; }*/
/*.customer-reviews { margin: 0px 0px 20px 0px; }*/
/*.reviews-listing { float: right; }*/
/*.customer-form label.auto-width { float: left; height: 18px; line-height: 18px; margin-right: 10px; }*/
/*.customer-form .star { float: left; width: 18px; height: 18px; margin: 0px; position: relative; cursor: pointer; }*/
/*.customer-form .star:before { content: ''; opacity: 1; width: 18px; height: 18px; position: absolute; left: 0px; top: 0px; background: url(../images/star.svg) no-repeat left top; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.customer-form .star:after { content: ''; opacity: 0; width: 18px; height: 18px; position: absolute; left: 0px; top: 0px; background: url(../images/star-yellow.svg) no-repeat left top; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.customer-form .star:hover:before { opacity: 0; }*/
/*.customer-form .star:hover:after { opacity: 1; }*/
/*.customer-form .star.star-rating-on:before { opacity: 0; }*/
/*.customer-form .star.star-rating-on:after { opacity: 1; }*/
/*.customer-form .star.star-rating-hover:before { opacity: 0; }*/
/*.customer-form .star.star-rating-hover:after { opacity: 1; }*/
/*.customer-form .star a { display: none; }*/
/*.customer-form .google-captcha-box .recaptcha { float: left; }*/
/*.customer-form #advice-validate-rating-validate_rating.validation-advice { float: left; width: 100%; height: auto; }*/

/*.review-item { padding: 30px 0px 20px 0px; border-bottom: 1px solid #dedede; }*/
/*.review-item .rating-box-title { margin: 0px 0px 10px 0px; }*/
/*.review-item .rating-box-title .rating-box { float: left; margin: 0px 12px 0px 0px; }*/
/*.review-item .author { font-weight: bold; }*/


/* Social Share
*****************/

.social-share { float: left; margin: 10px 0px 10px 0px; position: relative; cursor: pointer; color: #222; font-size: 13px; line-height: 18px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.social-share:hover { color: #ce181e; }
.social-share.open { color: #ce181e; }
.social-share .icon-svg { float: left; margin: 3px 8px 0px 0px; }
.social-share .sub-social-share { display: none; float: left; min-width: 120px; padding: 7px; background: #fff; box-shadow: 0px 0px 2px 0px #ccc; position: absolute; left: 0px; top: -40px; z-index: 10; }
.social-share .social { float: left; margin: 0px 5px 0px 5px;  border-radius: 100%; }
.social-share .social .icon-svg { float: left; margin: 0px; }
.social-share .title { float: left; margin: 3px 10px 1px 0px; color: #222; }
.social-share.wide-row { width: 100%; margin: 10px 0px 10px 0px; padding: 10px 0px 10px 0px; border-top: 1px solid #e9e9e9; border-bottom: 1px solid #e9e9e9; }


/* Tabs styles
***************/

.tabs { float: left; width: 100%; height: auto; margin: 0px 0px 20px 0px; }
.tabs.regular-tabs { }
.tabs .tabs-nav { float: left; width: 100%; height: auto; }
.tabs .tabs-nav ul { float: left; width: 100%; height: auto; }
.tabs .tabs-nav ul li { float: left; width: auto; height: auto; }
.tabs .tabs-nav ul li .tab-nav-item { float: left; padding: 15px 35px; color: #999; border: 1px solid #ededed; border-bottom: none; font-size: 20px; font-weight: bold; text-transform: uppercase; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.tabs .tabs-nav ul li .tab-nav-item:hover { background: #ededed; color: #222; }
.tabs .tabs-nav ul li .tab-nav-item.selected { color: #222; background: #ededed; position: relative; border-left: none; border-right: none; }
.tabs .tabs-nav ul li .tab-nav-item.selected:before { content: ""; width: 100%; height: 3px; position: absolute; top: 0; left: 0; background: #ce181e; }
.tabs .tabs-content { float: left; width: 100%; height: auto; padding: 35px; border-top: 1px solid #ededed; border-bottom: 1px solid #ededed; }
.tabs .tabs-content .tab-nav-item.responsive { display: none; float: left; width: 100%; height: auto; padding: 10px 10px 10px 10px; margin: 0px 0px 1px 0px; background: #dcdcdc; color: #393939; font-size: 16px; font-weight: bold; text-transform: uppercase; }
.tabs .tabs-content .tab-nav-item.responsive.selected { background: #222; color: #ededed; }
.tabs .tab { float: left; width: 100%; height: auto; padding: 0px; margin: 0px; }
.tabs .tab .text-page h6 { margin-top: 0px; margin-bottom: 15px; }
.tabs.accordion { float: left; width: 100%; height: auto; border-top: 1px solid #eee; }
.tabs.accordion .accordion-item { float: left; width: 100%; height: auto; padding: 0px; margin: 0px; border-bottom: 1px solid #eee; }
.tabs.accordion .tab-nav-item { float: left; width: 100%; height: auto; padding: 12px 10px 12px 10px; margin: 0px; color: #393939; font-size: 20px; font-weight: bold; text-transform: uppercase; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.tabs.accordion .tab-nav-item.selected { color: #ededed; background: #646464; }
.tabs.accordion .tab { padding: 10px 10px 0px 10px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.tabs.accordion .tab.selected { background: #f0f0f0; border-bottom-color: #b3b3b3; }


/* Shopping cart styles
*************************/

.checkout-cart-index .shopping-cart { margin: 0 0 40px; padding-right: 30px; }
.checkout-cart-index .shopping-cart
.shopping-cart-items { padding: 0 25px 15px; background: #FFF; box-shadow: 0 5px 20px 3px rgba(206,24,30,0.2); border-top: 3px solid #ce181e; }
.shopping-cart-items .cart-row { padding: 15px 0; background: #fff; }
.shopping-cart-items .cart-row.header-row { padding: 15px 0; margin-bottom: 15px; border: none; border-bottom: 1px solid #e1e1e1; }
.shopping-cart-items .cell { display: inline-block; min-width: 30px; color: #222; font-size: 13px; line-height: 16px; font-weight: normal; text-align: center; position: relative; vertical-align: middle; }
.shopping-cart-items .cart-row.header-row .cell { font-size: 14px; line-height: 18px; font-weight: bold; text-transform: uppercase; color: #000; }
.shopping-cart-items .cell.col1 { width: 35%; text-align: left; }
.shopping-cart-items .cell.col2 { width: 20%; }
.shopping-cart-items .cell.col3 { width: 12%; }
.shopping-cart-items .cell.col4 { width: 20%; }
.shopping-cart-items .cell.col5 { width: 10%; }
.shopping-cart-items .cart-row.header-row .cell.col1 { text-align: left; padding-left: 20px; }
.shopping-cart-items .cart-img-wrapper { display: inline-block; width: 110px; height: 110px; border: 1px solid #ebebeb; margin: 0px; position: absolute; left: 0px; top: 0px; z-index: 10; }
.shopping-cart-items .cart-img-wrapper img { max-width: 100%; height: auto; }
.shopping-cart-items .cart-info { width: 100%; min-height: 90px; padding-left: 125px; vertical-align: middle; }
.shopping-cart-items .cart-info .item-title { display: block; margin: 15px 0; font-size: 14px; font-weight: 600; line-height: 16px; color: #222; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.shopping-cart-items .cart-info .item-title:hover { color: #ce181e; }
.shopping-cart-items .cart-info .attributes { display: block; margin: 0px 0px 2px 0px; font-size: 13px; line-height: 15px; color: #8f8f8f; }
.shopping-cart-items .cart-info .item-msg.error { display: block; margin: 0px 0px 2px 0px; font-size: 13px; line-height: 16px; color: #de5b60; }
.shopping-cart-items .price-box { width: 100%; margin: 0px; text-align: center; }
.shopping-cart-items .price-box .old-price { display: block; margin-bottom: 3px; font-size: 16px; line-height: 16px; }
.shopping-cart-items .price-box .special-price { display: block; font-size: 16px; line-height: 16px; }
.shopping-cart-items .price-box .regular-price { width: 100%; font-size: 16px; line-height: 16px; }
.shopping-cart-items .item-remove { display: inline-block; width: 24px; height: 24px; text-align: center; line-height: 22px; color: #444; font-weight: 600; font-size: 16px; padding: 0px; border: 1px solid #e1e1e1; cursor: pointer; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.shopping-cart-items .item-remove:hover { border-color: #444; background: #444; color: #FFF; }
.shopping-cart .continue-shopping { padding: 0 0 0 18px; font-size: 14px; line-height: 14px; font-weight: 600; color: #000; text-transform: none; background: url(../images/continue-shopping.svg) no-repeat left center; }
.checkout-page-top .button { display: none; }
.cart-responsive-btn { display: none; }


/* Estimate shipping
**********************/

/*.estimate-shipping { float: right; width: 380px; height: auto; padding: 10px; margin: 0px; background: #f0f0f0; border: 1px solid #dde1e6; border-radius: 3px; }*/
/*.estimate-shipping .title { float: left; width: 100%; margin: 0px 0px 10px 0px; color: #000; font-size: 18px; line-height: 20px; }*/
/*.estimate-shipping dl.sp-methods { float: left; width: 100%; height: auto; padding: 0px; margin: 10px 0px 5px 0px; }*/
/*.estimate-shipping dl.sp-methods dt { margin: 0px 0px 5px 0px; }*/
/*.estimate-shipping dl.sp-methods dd { margin: 0px 0px 10px 10px; }*/
/*.estimate-shipping dl.sp-methods dd input.radio { float: left; margin: 4px 10px 0px 0px; }*/


/* Spinner styles
*******************/

/*.spinner-box { display: inline-table; margin: 0; width: 104px; height: 50px; border: 1px solid #e1e1e1; position: relative; }*/
/*.spinner-box .ui-spinner { display: block; position: relative; }*/
/*.spinner-box input.amount { display: inline-block; width: 42px; height: 48px; outline: none; border: none; border-left: 1px solid #e1e1e1; border-right: 1px solid #e1e1e1; text-align: center; font-size: 18px; color: #000; font-weight: 600; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.spinner-box .ui-spinner-button { position: absolute; top: 0px; z-index: 10; width: 30px; height: 48px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.spinner-box .ui-spinner-button:hover { background-color: #ce181e; }*/
/*.spinner-box .ui-spinner-button.ui-spinner-down { left: 0px; }*/
/*.spinner-box .ui-spinner-button.ui-spinner-up { right: 0px; }*/
/*.spinner-box .ui-spinner-button.ui-spinner-down:before { content: "-"; position: absolute; left: 0; top: 0; width: 100%; height: 100%; line-height: 48px; text-align: center; font-size: 18px; color: #000; font-weight: 600; }*/
/*.spinner-box .ui-spinner-button.ui-spinner-down:hover:before { color: #FFF; }*/
/*.spinner-box .ui-spinner-button.ui-spinner-up:before { content: "+"; position: absolute; left: 0; top: 0; width: 100%; height: 100%; line-height: 48px; text-align: center; font-size: 18px; color: #000; font-weight: 600; }*/
/*.spinner-box .ui-spinner-button.ui-spinner-up:hover:before { color: #FFF; }*/
/*.spinner-box .loader { width: 35px; height: 35px; padding: 0px; margin: 0px 0px 0px -17px; background: url(../images/preloader-28x28.gif) no-repeat center center; position: absolute; left: 50%; top: 55px; z-index: 20; }*/



/* Discount styles
*******************/

.discount { width: 100%; margin: 0; }
.discount .title { display: block; width: 100%; padding: 15px 5px 15px 18px; border-top: 3px solid #ce181e; color: #000; font-family: 13px; font-weight: 600; background: #ededed; cursor: pointer; }
.discount .title span { text-decoration: underline; }
.discount .title span:hover { text-decoration: none; }
.discount .discount-form { display: none; width: 100%; position: relative; padding: 15px 18px; border: 1px solid #ededed; }
.discount .discount-form.showed{ display: block; }
.discount .discount-form input.input-text { padding: 0px 45px 0px 10px; position: relative; z-index: 5; }
.discount .discount-form .discount-button { position: absolute; right: 19px; top: 16px; z-index: 10; width: 38px; height: 38px; background: #f0f0f0; border: none; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.discount .discount-form .discount-button:hover { background: #dbdbdb; }
.discount .discount-form .discount-button .icon-svg.check { width: 19px; height: 14px; margin: 0px 0px 0px 3px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.discount .discount-form .discount-button .icon-svg.close { width: 19px; height: 17px; margin: 0px 0px 0px 3px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.discount .discount-form .discount-button:hover .icon-svg.check { fill: #7cb015; }
.discount .discount-form .discount-button:hover .icon-svg.close { fill: #da1515; }
.discount.checkout-review-col-discount .title { padding: 10px 0px 10px 0px; font-size: 18px; line-height: 20px; cursor: pointer; }
.discount.checkout-review-col-discount .discount-form.checkout-fake-discount-form { display: none; }


/* Total table styles
***********************/

table.total-table { width: 100%; height: auto; padding: 0; margin: 0 0 20px; border-spacing: 0px; border-collapse: separate; border: none; }
table.total-table th { padding: 15px 18px; vertical-align: top; color: #000; font-size: 14px; line-height: 16px; text-align: left; font-weight: 600; }
table.total-table td { padding: 15px 18px; vertical-align: top; color: #000; font-size: 14px; line-height: 16px; text-align: right; font-weight: 600; }
table.total-table tfoot th { padding: 15px 18px; background: #ededed; font-size: 18px; line-height: 18px; font-weight: bold; color: #000; text-transform: uppercase; }
table.total-table tfoot td { padding: 15px 18px; background: #ededed; font-size: 18px; line-height: 18px; font-weight: bold; color: #000; }



/* Login and registration
**************************/

.registered-users { min-height: 380px; position: relative; margin-bottom: 30px; }
.registered-users:after { content: ''; width: 1px; height: 100%; background: #e9e9e9; position: absolute; right: 20px; top: 0px; z-index: 10; }
.registered-users h4 { margin-top: 0px; }
.registered-users .login-form { width: 60%; }
.registered-users .login-form .forgotpassword { cursor: pointer; color: #ce181e; border-bottom: 1px solid #ce181e; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.registered-users .login-form .forgotpassword:hover { color: #9f9f9f; }
.new-users { min-height: 380px; margin-bottom: 30px; }
.new-users h4 { margin-top: 0px; }
.account-create { margin-bottom: 30px; }
.account-create .social-login { width: 525px; }
.account-create .social-login .button { padding-left: 8px; padding-right: 8px; font-size: 13px; }
.account-create .registration-form { width: 75%; }
.checkbox-content label { float: left; width: auto; font-size: 13px; font-weight: 500; }
.checkbox-content label.long-label { width: 93%; }
.radio ~ label { font-size: 13px; font-weight: 500; }
.checkbox ~ label { font-size: 13px; font-weight: 500; }
.customer-form .checkbox-content label.long-label { width: 89%; }
.checkbox-content input.checkbox { float: left; margin: 4px 6px 0px 0px; }
.checkbox-content .validation-advice { width: 100%; clear: both; }
.gdpr-info { float: left; width: 100%; height: auto; box-sizing: border-box; padding: 0px 0px 0px 20px; }
.gdpr-info p { margin: 0px 0px 7px 0px; color: #666; font-size: 12px; line-height: 15px; }
.deleting-account-info { float: right; width: 230px; }
.terms-popup { float: left; width: 560px; height: 600px; padding: 10px; background: #fff; overflow-y: scroll; }



/* Social Login
*****************/

/*.social-login { width: 435px; }*/
/*.social-login .facebook-login { margin-right: 10px; background: #3b5791; line-height: 32px; padding: 8px 10px; }*/
/*.social-login .facebook-login:hover { background: #577cc7; }*/
/*.social-login .gplus-login { background: #e04b33; line-height: 32px; padding-left: 30px; padding: 8px 20px; }*/
/*.social-login .gplus-login:hover { background: #fc482a; }*/
/*.social-login .button .icon-svg { fill: #fff; margin: 0px 10px 0px 0px; }*/



/* Contacts page
******************/

/*.contacts-info { padding: 0 30px 0 0; }*/
/*.contacts-info h4 { margin: 0 0 15px; font-size: 30px; font-weight: bold; color: #222; }*/
/*.contacts-info p { padding-left: 30px; position: relative; }*/
/*.contacts-info p img { position: absolute; left: 0; top: 3px; }*/
/*.contacts-form { padding: 0 0 0 30px; }*/
/*.contacts-form h4 { margin: 0 0 15px; font-size: 30px; font-weight: bold; color: #222; }*/
/*.contacts-form .button .icon-svg { fill: #FFF; margin-top: 2px; }*/
/*.gmap-content { width: 100%; height: auto; position: relative; margin: 0px 0px 20px 0px; }*/
/*.gmap-content #map_container { width: 100%; height: 460px; }*/
/*.gmap-content img { max-width: 100%; }*/
/*.google-captcha-box { position: relative; }*/
/*.google-captcha-box .recaptcha { }*/
/*.center-form-action { }*/
/*.center-form-action .button { float: none; display: inline-block; }*/


/* Compare list styles
***********************/

/*.more-items-for-compare { float: right; margin: -62px 0px 0px 0px; }*/
/*.compare-table-wrapper { max-width: 100%; overflow-x: auto; }*/
/*table.compare-table { width: 100%; padding: 0px; margin: 30px 0px 30px 0px; border-collapse: separate; box-sizing: border-box; border-top: 1px solid #e9e9e9; border-left: 1px solid #e9e9e9; text-align: center; }*/
/*table.compare-table td { padding: 20px; background: #fff; font-size: 13px; line-height: 16px; color: #333; position: relative; border-bottom: 1px solid #e9e9e9; border-right: 1px solid #e9e9e9; }*/
/*table.compare-table th { min-width: 160px; padding: 20px; background: #f1f1f1; font-size: 16px; line-height: 20px; color: #333; text-align: left; position: relative; border-bottom: 1px solid #dcdcdc; border-right: 1px solid #e9e9e9; }*/
/*table.compare-table td .compare-img { display: block; width: 80%; height: auto; padding: 0px; margin: auto; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*table.compare-table td .compare-img img { max-width: 100%; }*/
/*table.compare-table td .title { display: block; width: 100%; padding: 0px 10px 0px 10px; margin: 0px; font-size: 13px; line-height: 16px; color: #333; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*table.compare-table td:hover .title { color: #ce181e; }*/
/*table.compare-table td .button { float: none; display: table; margin: auto; text-align: left; }*/
/*table.compare-table td .button .icon-svg.shopping-cart { margin: -2px 12px 0px 25px; }*/
/*table.compare-table td .item-remove { width: 26px; height: 24px; padding: 4px; opacity: 0.4; position: absolute; top: 10px; right: 10px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*table.compare-table td .item-remove .icon-svg.close { width: 18px; height: 16px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*table.compare-table td .item-remove:hover .icon-svg.close { fill: #e7352b; }*/
/*table.compare-table td .view-more { color: #333; border-bottom: 1px solid #333; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*table.compare-table td .view-more:hover { color: #ce181e; }*/
/*.sidebar .block-compare { display: none; }*/



/* Sidebar styles
********************/

.sidebar .line { display: block; clear: both; width: 100%; height: 1px; padding: 0px; margin: 0px 0px 10px; background: #e4e4e4; }
.sidebar .sidebar-responsive-wrapper { background: none; padding: 0px 7px 0px 7px; }
.sidebar .open-responsive-sidebar { display: none; }
.sidebar .open-responsive-sidebar:hover { background: #222; }
.sidebar .open-responsive-sidebar #filtersCount { color: #ce181e; }
.sidebar .sidebar-nav ul li a { display: block; padding: 10px 25px; color: #222; font-size: 14px; line-height: 20px; font-weight: bold; text-transform: uppercase; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.sidebar .sidebar-nav ul li a:hover { color: #ce181e; box-shadow: 0 0 20px 2px rgba(206,24,30,0.15); }
.sidebar .sidebar-nav ul li.active a { color: #ce181e; }
.sidebar div.layer-slider .price-slider-inputs {  }
.sidebar div.layer-slider .price-range .button.filter-button { min-width: auto; padding-top: 7px; padding-bottom: 7px; margin-top: 5px; margin-bottom: 5px; height: 35px; background: #ce181e url(../images/price-range.svg) no-repeat center center; }
.sidebar .cart-sidebar .button { width: 100%; padding: 15px 20px; }
.sidebar .cart-sidebar .checkout-top-btn .button { margin-top: 3px; margin-bottom: 0px; }
.sidebar .info-cms-block { padding: 24px 18px 24px 80px; width: 250px; float: none; margin:0 auto 10px auto; border-bottom: 1px solid #ededed; font-size: 12px; line-height: 16px; font-weight: 600; color: #000; }
.sidebar .info-cms-block.free-shipping { color: #7cb015; }
.sidebar .info-cms-block.free-shipping strong { }
.sidebar .info-cms-block .icon-svg { width: 60px; height: 34px; top: 30px; left: 18px; }
.sidebar .info-cms-block img { position: absolute; width: 60px; height: 34px; top: 24px; left: 0px; }
.sidebar .info-cms-block.free-shipping .icon-svg.delivery { fill: #7cb015; top: 16px; left: 0; }



/* Footer styles
*******************/

footer { padding: 60px 0 0; background: #f2f2f2 url(../images/footer-bckg.jpg) no-repeat center top; background-size: cover; }
footer .info-cols { margin-bottom: 30px; }
footer .payments { margin-bottom: 30px; }
footer .copy-rights .row { margin: 0; border-top: 1px solid #b7b7b7; font-size: 12px; line-height: 45px; }

.footer-col { text-align: left; }
.footer-col .title { margin: 0 0 25px; font-size: 16px; color: #222; line-height: 20px; font-weight: bold; text-decoration: none; text-transform: uppercase; }
.footer-col .mobile-link { display: none;}
.footer-col p { margin: 0 0 10px; font-size: 12px; font-weight: 600; line-height: 17px; color: #666; }
.footer-col p img { margin-right: 10px; }
.footer-col ul { margin: 0; padding: 0; }
.footer-col ul li { margin: 0 0 10px; padding: 0; }
.footer-col ul li .clever-link,
.footer-col ul li a { margin: 0; padding: 0; font-size: 12px; font-weight: 600; color: #666; cursor: pointer; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.footer-col ul li .clever-link:hover,
.footer-col ul li a:hover { color: #ce181e; }
.footer-col.newsletter-col p { margin-bottom: 20px; }
.footer-col .newsletter-form { padding: 0px; margin: 0px 10px 0px 0px; position: relative; }
.footer-col .newsletter-form input.input-newsletter { width: 100%; height: 40px; padding: 0 50px 0 10px; background: #fff; border: 1px solid #000; color: #666; font-size: 12px; font-weight: 500; text-decoration: none; font-family: 'Montserrat', sans-serif; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.footer-col .newsletter-form input.input-newsletter:focus { border-color: #ce181e; }
.footer-col .newsletter-form button.newsletter-button { width: 40px; height: 40px; text-align: center; line-height: 40px; padding: 0; border: none; font-size: 18px; color: #fff; text-decoration: none; font-weight: bold; background: #000; position: absolute; right: 0px; top: 0px; z-index: 10; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.footer-col .newsletter-form button.newsletter-button .icon-svg { float: none; display: inline-block; vertical-align: middle; margin-top: -3px; fill: #FFF; }
.footer-col .newsletter-form button.newsletter-button:hover { background: #ce181e; }
.footer-col .newsletter-form input.checkbox { float: left; margin: 10px 10px 0px 0px; width: 16px; height: 16px; }
.footer-col .newsletter-form .newsletter-terms { float: left; width: 100%; height: auto; padding: 5px; margin: 0px; }
.footer-col .newsletter-form .newsletter-terms label { color: #ccc; }
.footer-col .newsletter-form .newsletter-terms .gdpr-info { padding: 0px; }
.footer-col .newsletter-form ul.messages { margin: 0px; border-radius: 5px; position: absolute; left: 0px; top: 45px; z-index: 10; }
.footer-col .newsletter-form ul.messages li.error-msg { border-radius: 5px; }
.footer-col .newsletter-form ul.messages li.success-msg { border-radius: 5px; }
.footer-col .newsletter-form .newsletterLoader { display: none; width: 35px; height: 35px; padding: 0px; margin: 0px; background: url(../images/preloader-28x28.gif) no-repeat center center; position: absolute; right: 70px; top: 4px; z-index: 20;}

.payments-box { padding: 20px 15px 15px 15px; border: 1px solid #3a3a3a; color: #cdcdcd; }
.payments-box p { color: #bebebe; margin: 0px; padding: 0px; }
.payments-box p img { padding: 0px 10px 10px 10px; }

.copy { float: left; }
.copy a { color: #222; font-size: 12px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.copy a:hover { color: #29a8f0; }

.stenik-info { float: right; }
.stenik-info a { color: #222; font-size: 12px; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.stenik-info a .stenik-color { -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.stenik-info a .stenik-color:hover { color: #29a8f0; }
.stenik-info .icon-svg.stenik { float: none; display: inline-block; vertical-align: middle; margin: -3px 5px 0 0; fill: #000; }


/* Back to top styles
***********************/

.back-to-top { width: 40px; height: 40px; position: fixed; bottom: 34px; right: 120px; z-index: 1800; background: #222; -webkit-transition: background 0.2s linear; -moz-transition: background 0.2s linear; -o-transition: background 0.2s linear; transition: background 0.2s linear; }
.back-to-top:hover { background: #ce181e; }
.back-to-top .icon-svg.arrow-up { fill: #fff; stroke: #fff; width: 12px; height: 20px; margin: 9px 0px 0px 14px; }


/* Magento messages styles
****************************/

ul.messages { margin: 0px 0px 20px 0px; clear:both; }
ul.messages li.success-msg { padding: 12px 15px 12px 45px; background: #7cb015 url(../images/msgs-mark.svg) no-repeat 20px 16px; background-size: 16px 12px; color: #fff; font-weight: normal; }
ul.messages li.success-msg strong { font-weight: 500; }
ul.messages li.success-msg a { color: #fff; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
ul.messages li.error-msg { padding: 12px 15px 12px 45px; background: #e7352b url(../images/msgs-x.svg) no-repeat 20px 16px; background-size: 14px 14px; color: #fff; font-weight: normal; }
ul.messages li.error-msg strong { font-weight: 500; }
ul.messages li.error-msg a { color: #fff; text-decoration: underline; }
ul.messages li.notice-msg { padding: 12px 15px 12px 45px; background: #ebbc00 url(../images/notice.svg) no-repeat 18px 13px; background-size: 17px 19px; color: #fff; font-weight: normal; }
ul.messages li.notice-msg strong { font-weight: 500; }


/* Validation Advice styles
*****************************/

.validation-advice { margin: 0px 0px 10px 0px; color: #da1515; }
.newsletter-form .validation-advice { width: 100%; padding: 5px 10px 5px 10px; background: #fff; border-radius: 5px; position: absolute; left: 0px; top: 45px; z-index: 20; }



/* Search autocomplatestyles
******************************/

.search-autocomplete { padding: 10px; background: #f1f1f1; border: 1px solid #e5e5e5; border-radius: 5px; z-index: 2999; }
.search-autocomplete ul li { cursor: pointer; border-bottom: none !important; }
.search-autocomplete .amount { float: right; color: #ce181e; }


/* Default Magento Checkout styles
***********************************/

/*.opc { width: 100%; padding: 0px; margin: 0px 0px 10px 0px; list-style-type:none; }*/
/*.opc .section { margin: 0px 0px 20px 0px; }*/
/*.opc .step { padding: 15px 18px 15px 18px; border: 1px solid #f0f0f0; position: relative; }*/
/*.opc .step .col2-set .col-1 { float: left; width: 48.5%; }*/
/*.opc .step .col2-set .col-2 { float: right; width: 48.5%; }*/
/*.opc .section .step-title { padding: 0px; margin: 0px; background: #f0f0f0; text-align: left; }*/
/*.opc .section .step-title .number { float: left; width: 54px; margin: 0px 18px 0px 0px; padding: 15px 0px 15px 0px; font-size: 25px; line-height: 25px; color: #fff; text-decoration: none; font-weight: bold; text-align: center; background: #222;  }*/
/*.opc .section .step-title h2 { display: inline-block; margin: 0px; padding: 15px 0px 15px 0px; color: #333; font-size: 22px; line-height: 25px; text-decoration: none; font-weight: bold; }*/
/*.opc .section .step-title a { display: none; float: right; margin: 18px 18px 0px 0px; text-align: right; color: #787878; font-size: 14px; line-height: 14px; border-bottom: 1px solid #939393; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.opc .section .step-title a:hover { color: #ce181e; }*/
/*.opc .section.allow .step-title a { display: block; }*/
/*.opc .section.allow .step-title .number { background: #555; }*/
/*.opc .section.allow.active .step-title { background: #d7d7d7; }*/
/*.opc .section.allow.active .step-title .number { background: #ce181e; }*/
/*.opc .section.allow.active .step-title a { display: none; }*/
/*.opc .step h3 { margin: 0px 0px 15px 0px; font-size: 22px; line-height: 24px; }*/
/*.opc .step fieldset { padding: 0px; margin: 0px; border: none; }*/
/*.opc #checkout-step-login.step .text-page ul li { padding-left: 20px; }*/
/*.opc #checkout-step-login.step .text-page ul li:before { left: 2px; }*/
/*.opc #checkout-step-login.step .checkout-login-chooser .buttons-wrapper { margin-bottom: 20px; text-align: center; }*/
/*.opc #checkout-step-login.step .checkout-login-chooser .button { min-width: 220px; display: table; float: none; margin-left: auto; margin-right: auto; }*/
/*.opc #checkout-step-login.step a.forgotpassword { float: right; margin: 8px 0px 0px 0px; border-bottom: 1px solid #ce181e; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.opc .step ul.form-list ul li.fields { display: inline-block; width: 48%; margin: 0px 1.5% 0px 0px; vertical-align: top; }*/
/*.opc .step ul.form-list ul li.wide { width: 100%; margin: 0px }*/
/*.opc .step ul.form-list ul li.wide .field.half { display: inline-block; width: 48%; margin: 0px 1.5% 0px 0px; vertical-align: top; }*/
/*.opc .step input.radio { float: left; margin: 4px 8px 0px 0px; }*/
/*.opc .step input[type="radio"] { float: left; margin: 3px 8px 0px 0px; }*/
/*.opc .step input.checkbox { float: left; margin: 4px 8px 0px 0px; }*/
/*.opc .step .data-table td { height: 70px; }*/
/*.opc .step #checkout-review-table.data-table .item-title { max-width: 300px; text-align: left; }*/
/*.opc .step #checkout-review-table.data-table dl.item-options { max-width: 300px; text-align: left; }*/
/*.opc .step .buttons-set { padding: 20px 0px 0px 0px; border-top: 1px solid #f0f0f0; position: relative; }*/
/*.opc .step .buttons-set .button { float: right; min-width: 180px; background-color: #f27c21; }*/
/*.opc .step .buttons-set .button:hover { background-color: #ff9211; }*/
/*.opc .step .buttons-set .please-wait { width: 35px; height: 35px; padding: 0px; margin: 0px; background: url(../images/preloader-28x28.gif) no-repeat center center; position: absolute; right: 220px; top: 22px; z-index: 20; text-indent: -9999px; font-size: 1px; color: #fff; }*/
/*.opc .step .buttons-set .please-wait img { display: none; }*/
/*.opc .step .buttons-set .backLink { display: none; }*/
/*.opc .step .buttons-set .f-left { float: left; display: none; }*/
/*.opc .step dl.sp-methods { margin: 0px 0px 20px 0px; }*/
/*.opc .step dl.sp-methods dt { margin: 0px 0px 5px 0px; }*/
/*.opc .step dl.sp-methods dd { margin: 0px 0px 0px 18px; }*/
/*.opc .step dl.sp-methods dd#dd_method_paypal_express ul#payment_form_paypal_express { margin: 10px 0px 10px 0px; }*/
/*.opc .step #checkout-review-table-wrapper table.total-table { float: right; width: 326px; }*/
/*.opc .step #checkout-agreements { position: relative; }*/
/*.opc .step #checkout-agreements .order-comment { width: 480px; position: absolute; left: 0px; top: -105px; z-index: 10px; }*/
/*.opc .step #checkout-agreements .order-comment.gdpr-enable { top: -125px; }*/
/*.opc .step #checkout-agreements .order-comment textarea { height: 100px; }*/
/*.opc .step #checkout-agreements ol.checkout-agreements { float: right; width: 326px; padding: 0px; margin: 0px; list-style-type: none; }*/
/*.opc .step #checkout-agreements ol.checkout-agreements input.checkbox { float: left; margin: 3px 8px 0px 0px; }*/
/*.opc .step #checkout-review-submit .buttons-set .button { min-width: 326px; }*/
/*.opc .step #checkout-review-submit .buttons-set .please-wait { right: 350px; }*/
/*.opc-block-progress { margin: 65px 0px 0px 0px; }*/
/*.opc-block-progress .block-title { display: none; }*/
/*.opc-block-progress .block-content dl { margin: 0px; }*/
/*.opc-block-progress .block-content .progress-row { padding: 0px 0px 8px 0px; margin: 0px 0px 18px 0px; border-bottom: 1px solid #e4e4e4; }*/
/*.opc-block-progress .block-content dl dt { margin: 0px 0px 10px 0px; font-size: 18px; color: #aaa; font-weight: 500px; }*/
/*.opc-block-progress .block-content dl dt.complete { color: #333; }*/
/*.opc-block-progress .block-content dl dt .changelink { display: none; float: right; width: 19px; height: 14px; margin: 4px 0px 0px 0px; }*/
/*.opc-block-progress .block-content dl dt.complete .changelink { display: block; }*/
/*.opc-block-progress .block-content dl dt .changelink .separator { display: none; }*/
/*.opc-block-progress .block-content dl dt .changelink a { float: right; width: 19px; height: 14px; background: url(../images/checkout-progress-edit.svg) no-repeat center center; background-size: 19px 14px; text-indent: -9999px; font-size: 1px; color: #fff; opacity: 0.7; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.opc-block-progress .block-content dl dt .changelink:hover a { opacity: 0.4; }*/
/*.opc-block-progress .block-content dl dd { padding: 0px; margin: 0px 0px 10px 0px; }*/
/*.opc-block-progress .block-content dl dd address { font-style: normal; color: #333; }*/
/*.opc-block-progress .block-content dl dd p strong { font-weight: normal; }*/
/*.opc-block-progress .block-content dl dd table { font-size: 12px; line-height: 16px; }*/
/*.opc-block-progress .block-content dl dd table td { text-align: center; }*/
/*.opc-block-progress .block-content dl dd table td.label { width: auto; height: auto; position: relative; left: auto; top: auto; }*/



/* Magento Data Table styles
******************************/

/*.data-table { width: 100%; }*/
/*.data-table th { padding: 18px 10px 18px 10px; background: #f0f0f0; font-size: 20px; color: #393939; font-weight: bold; }*/
/*.data-table th.a-center { text-align: center; }*/
/*.data-table td { padding: 10px; border-bottom: 1px solid #dedede; font-size: 13px; line-height: 15px; color: #333; text-align: center; position: relative; }*/
/*.data-table td.a-center { text-align: center; }*/
/*.data-table td.a-right { text-align: center; }*/
/*.data-table td .cart-img-wrapper { float: left; width: 50px; height: 50px; border: 1px solid #ddd; margin: 0px; position: absolute; left: 0px; top: 10px; z-index: 10; }*/
/*.data-table td .cart-img-wrapper img { max-width: 100%; height: auto; }*/
/*.data-table td .item-title { display: block; margin: 0px 0px 5px 0px; padding: 0px 0px 0px 50px; font-size: 13px; line-height: 15px; color: #333; }*/
/*.data-table td h3 { display: block; margin: 0px 0px 5px 0px; padding: 0px 0px 0px 50px; font-size: 13px; font-weight: normal; line-height: 15px; color: #333; font-family: inherit; }*/
/*.data-table td a { -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.data-table td dl.item-options { padding: 0px 0px 0px 50px; font-size: 13px; line-height: 15px; color: #333; }*/
/*.data-table td dl.item-options dt { display: inline-block; margin: 0px; font-size: 12px; color: #777; }*/
/*.data-table td dl.item-options dd { display: inline-block; margin: 0px 10px 0px 0px; font-size: 12px; color: #999; }*/
/*.data-table td .description { margin: 0px 0px 10px 0px; }*/
/*.data-table td textarea { width: 100%; height: 90px; }*/
/*.data-table tfoot td.a-right { text-align: right; }*/
/*.data-table tfoot td.last.a-right { text-align: center; }*/



/* Customer account styles
***************************/

.my-account .col2-set .col-1 { float: left; width: 48.5%; }
.my-account .col2-set .col-2 { float: right; width: 48.5%; }
.my-account .box-head h2 { display: inline-block; }
.my-account .box-head a { float: right; margin-top: 28px; border-bottom: 1px solid #ce181e; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.my-account .box-title { margin: 0px 0px 10px 0px; border-bottom: 1px solid #e5e5e5; }
.my-account .box-title h3 { display: inline-block; }
.my-account .box-title a { float: right; margin-top: 28px; border-bottom: 1px solid #ce181e; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.my-account .box-content h4 { margin-top: 0px; }
.my-account ul.form-list li.fields { display: inline-block; width: 48%; margin: 0px 1.5% 0px 0px; vertical-align: top; }
.my-account ul.form-list li.wide { width: 100%; margin: 0px }
.my-account ul.form-list li.wide .field.half { display: inline-block; width: 48%; margin: 0px 1.5% 0px 0px; vertical-align: top; }
.my-account select { padding-top: 10px; padding-bottom: 9px; background-position: right 17px; }
.my-account input.radio { float: left; margin: 4px 8px 0px 0px; }
.my-account input[type="radio"] { float: left; margin: 4px 8px 0px 0px; }
.my-account input.checkbox { float: left; margin: 4px 8px 0px 0px; }
.my-account .buttons-set { margin: 20px 0px 0px 0px;  padding: 20px 0px 0px 0px; border-top: 1px solid #f0f0f0; }
.my-account .addresses-list ol { margin: 0px 0px 20px 0px;  padding: 0px; list-style-type: none; }
.my-account dl.order-info { padding: 0px; margin: 10px 0px 10px 0px; }
.my-account dl.order-info dd { padding: 0px; margin: 0px; }
.my-account dl.order-info ul#order-info-tabs { margin: 10px 0px 0px 0px; }
.my-account dl.order-info ul#order-info-tabs li { padding: 0px 0px 0px 40px; position: relative; }
.my-account dl.order-info ul#order-info-tabs li:before { content: ''; width: 6px; height: 6px; background: #000; border-radius: 100%; position: absolute; left: 24px; top: 8px; z-index: 10; }
.my-account .title-buttons a { -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.my-account #checkout-review-table.data-table td { text-align: left; }
.my-account #my-orders-table.data-table td h3 { padding-left: 0px; }
.my-account .order-items .order-comments { display: none; }
.my-account .buttons-set .back-link a { -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.my-account .my-wishlist .buttons-set .button.btn-share { margin-right: 20px; }
.my-account .my-wishlist .buttons-set .button.btn-update { float: right; }
.my-account #wishlist-table.data-table td h3 { padding-left: 0px; }
.my-account #wishlist-table.data-table td .product-image { display: block; width: 100px; border: 1px solid #ddd; padding: 5px; }
.my-account #wishlist-table.data-table td .product-image img { max-width: 100%; }
.my-account #wishlist-table.data-table td .price-box { width: 100%; text-align: center; }
.my-account #wishlist-table.data-table td .price-box .regular-price { width: 100%; text-align: center; }
.my-account #wishlist-table.data-table .add-to-cart-alt { display: table; margin: 0px 0px 5px 0px; }
.my-account #wishlist-table.data-table td input.input-text.qty { width: 40px; text-align: center; }
.my-account #wishlist-table.data-table td button.button.btn-cart.checkout-color { width: 100%; padding-left: 5px; padding-right: 5px; }
.my-account #wishlist-table.data-table td .item-remove { display: inline-block; width: 22px; height: 20px; padding: 0px; cursor: pointer; }
.my-account #wishlist-table.data-table td .item-remove .icon-svg { fill: #bbb; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.my-account #wishlist-table.data-table td .item-remove:hover .icon-svg { fill: #e7352b; }
.my-account .buttons-set .required { display: none; }
.customer-account-edit .my-account .back-link { display: none; }
.customer-address-index .my-account .title-buttons .button { margin-bottom: 20px; }
.customer-address-index .my-account .buttons-set { display: none; }
.sales-order-history .my-account .buttons-set { display: none; }
.customer-invoice-table td { padding: 5px 10px 5px 0; }
input.no-display { padding: 0px; margin: 0px; border: none; opacity: 0; visibility: hidden; position: absolute; right: 0px; top: 0px; outline: none; }


/* Sitemap styles
******************/

/*.sitemap-btn ul.links { padding: 0px; margin: 0px; background: none; }*/
/*.sitemap-btn ul.links li { padding: 0px; margin: 0px; background: none; }*/
/*.sitemap-btn ul.links li a { float: left; min-width: 160px; padding: 10px 20px; margin: 0px; border-radius: 5px; border: none; background-color: #222; color: #fff; font-size: 12px; font-weight: 500; text-align: center; text-decoration: none !important; text-transform: uppercase; outline: none; cursor: pointer; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.sitemap-btn ul.links li:before { display: none; }*/
/*ul.sitemap li { margin: 5px 0px 5px 0px; }*/
/*ul.sitemap li.level-0 { padding-left: 10px; }*/
/*ul.sitemap li.level-0:before { display: none; }*/
/*ul.sitemap li.level-0 a { border-bottom: none; text-transform: uppercase; font-weight: 500; font-size: 16px; }*/
/*ul.sitemap li.level-2 { padding-left: 50px; }*/
/*ul.sitemap li.level-2:before { display: none; }*/
/*ul.sitemap li.level-2 a { border-bottom: none; color: #333; }*/
/*.catalog-seo-sitemap-product ul.sitemap li { float: left; width: 25%; }*/
/*.catalog-seo-sitemap-product ul.sitemap li a { border-bottom: none; color: #333; }*/


/* Advanced Search styles
**************************/

.advanced-search ul.form-list li { float: left; width: 25%; padding: 0 1% 0 1%; }
.advanced-search ul.form-list li .input-range input.input-text { width: 40%; }
.catalogsearch-advanced-index .buttons-set { float: left; width: 100%; padding: 20px 0px 0px 0px; margin: 20px 0px 0px 0px; border-top: 1px solid #f0f0f0; }


/* Gender widget styles
*************************/

.gender-widget { display: table; margin: 5px 0px 10px 0px; }
.gender-widget .gender-label { float: left; width: auto; margin-right: 10px; line-height: 18px; }
.gender-widget label.radio-label { float: left; width: auto; margin-right: 10px; line-height: 18px; }
.gender-widget input.radio { float: left; width: auto; margin: 2px 4px 0px 0px; }


/* Stenik Shops styles
************************/

/*.shops-listing .toolbar { padding: 10px; margin: 0px; background: #f4f4f4; }*/
/*.shops-listing .toolbar .drop-down.chose-city { min-width: 30%; }*/
/*.shops-listing .toolbar .drop-down.chose-city .open-item { background: #fff; }*/
/*.shops-listing .toolbar .drop-down.chose-city .sub-options { background: #fff; border: 1px solid #e4e4e4; border-top: none; }*/
/*.shops-listing .shop-box { float: left; width: 100%; height: auto; padding: 10px; margin: 0px 0px 10px 0px; border-left: 1px solid #f4f4f4; border-bottom: 1px solid #f4f4f4; }*/
/*.shops-listing .shop-box .title { float: left; width: 100%; margin: 0px 0px 10px 0px; color: #000; font-size: 18px; line-height: 20px; }*/
/*.shops-listing .shop-box p { float: left; width: 100%; margin: 0px 0px 5px 0px; }*/
/*.shops-listing .shop-box p a { display: inline-table; margin-right: 10px; font-size: 14px; line-height: 18px; font-weight: normal; text-decoration: none; border-bottom: 1px solid #ce181e; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.shops-listing .shop-box p a:hover { border-bottom: 1px solid #ce181e; }*/
/*.shops-listing-google-map #gmap { float: left; width: 100%; height: 600px; position: relative; }*/
/*.shops-listing-wrapper .responsive-back-to-top { display: none; width: 100%; margin: 20px 0px 20px 0px; }*/
/*.shop-gallery-wrapper { float: left; width: 100%; height: auto; margin: 0px 0px 10px 0px; }*/
/*.shop-gallery { margin: 0px 0px 10px 0px; }*/
/*.shop-gallery .slick-slide img { max-width: 100%; }*/
/*.shop-gallery-thumbs { margin: 0px 0px 10px 0px; }*/
/*.shop-gallery-thumbs.hide-thumbs { display: none; }*/
/*.shop-gallery-thumbs .thumb { float: left; margin: 0px 2px 0px 2px; }*/
/*.shop-gallery-thumbs .thumb img { float: left; max-width: 100%; height: auto; margin: 0px; }*/
/*.shop-info h5 { margin-top: 0px; }*/
/*.share-and-backbtn { float: left; width: 100%; margin: 5px 0px 0px 0px; padding: 15px 0px 15px 0px; border-top: 1px solid #e4e4e4; border-bottom: 1px solid #e4e4e4; }*/
/*.share-and-backbtn .shop-share { float: left; }*/
/*.share-and-backbtn .shop-share .title { float: left; width: auto; height: 30px; line-height: 30px; margin: 0px 10px 0px 0px; }*/
/*.share-and-backbtn .shop-share .social { margin-bottom: 0px; }*/
/*.share-and-backbtn .shop-share .social .icon-svg { width: 30px; height: 30px; }*/
/*.share-and-backbtn .shop-back-link { float: right; margin: 5px 0px 0px 0px; font-size: 14px; line-height: 18px; font-weight: normal; text-decoration: none; border-bottom: 1px solid #ce181e; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.share-and-backbtn .shop-back-link:hover { border-bottom: 1px solid #ce181e; }*/


/* Stenik Unicredit and TBI Leasing Calculator styles
******************************************************/

/*.leasing-calculator .downpayment-content { float: left; width: 100%; height: auto; padding: 0px; position: relative; }*/
/*.leasing-calculator .downpayment-content label { margin: 0px 0px 8px 0px; }*/
/*.leasing-calculator .downpayment-content input.input-text.downpayment { float: left; width: 240px; margin: 0px 10px 0px 0px; }*/
/*.leasing-calculator .horizontal-scroll-wrapper { width: 100%; margin-bottom: 10px; overflow-x: auto; }*/
/*.leasing-calculator .horizontal-scroll-wrapper table.stylized.variants { margin-bottom: 10px; }*/
/*.leasing-calculator .recalc-loader { width: 35px; height: 35px; padding: 0px; margin: 0px; background: url(../images/preloader-28x28.gif) no-repeat center center; position: absolute; left: 445px; top: 43px; z-index: 20; }*/
/*.leasing-message { float: left; width: auto; margin: 14px 0px 0px 14px; }*/
/*#opc-payment .leasing-calculator { float: left; width: 100%; margin: 10px 0px 10px 0px; }*/
/*#opc-payment .leasing-calculator .downpayment-content { padding: 0px; }*/
/*#opc-payment .leasing-calculator .checkout-agreements { float: left; width: 100%; margin: 0px 0px 10px 0px; }*/
/*#opc-payment .leasing-calculator .checkout-agreements input { float: left; margin: 3px 8px 0px 0px; }*/
/*#opc-payment .leasing-calculator .checkout-agreements label { float: left; width: 85%; }*/
/*#opc-payment .leasing-calculator .horizontal-scroll-wrapper { width: 820px; }*/
/*table.stylized th.promo { background: #e51e26; color: #fff; position: relative; }*/
/*table.stylized th.promo span.promo-text { width: 100%; height: 14px; line-height: 14px; padding: 0px; margin: 0px; text-align: center; color: #fff; font-size: 11px; text-decoration: none; font-weight: normal; background: #e51e26; position: absolute; left: 0px; top: 2px; z-index: 10; }*/
/*.stenik-checkout .leasing-calculator .checkout-agreements { float: left; width: 100%; margin: 0px 0px 10px 0px; }*/
/*.stenik-checkout .leasing-calculator .checkout-agreements input { float: left; margin: 3px 8px 0px 0px; }*/
/*.stenik-checkout .leasing-calculator .checkout-agreements label { float: left; width: 85%; }*/

/*.leasing-tabs { margin-bottom: 20px; }*/
/*.leasing-tabs ul { float: left; width: 100%; height: auto; }*/
/*.leasing-tabs li { float: left; width: 100%; height: auto; }*/
/*.leasing-tabs li a.leasing-tabs-nav { float: left; width: 100%; height: auto; padding: 15px 10px 15px 10px; margin: 0px; background: #f7f5f2; border-bottom: 1px solid #d5cec8; position: relative; font-size: 15px; line-height: 17px; color: #424242; font-weight: normal; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.leasing-tabs li a.leasing-tabs-nav:hover { background: #f0ede8; }*/
/*.leasing-tabs li a.leasing-tabs-nav.selected { background: #d5cec8; }*/
/*.leasing-tabs li a.leasing-tabs-nav:after { content: '»'; width: 17px; height: 17px; line-height: 17px; color: #c2b7ad; font-size: 14px; font-weight: normal; position: absolute; right: 10px; top: 18px; z-index: 10;  -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.leasing-tabs li a.leasing-tabs-nav.selected:after { color: #333; }*/
/*.leasing-tabs li a.leasing-tabs-nav img { float: left; height: 25px; }*/



/* Stenik Custom Forms popup styles
*************************************/

.inquiry-popup-form { float: left; width: 580px; min-height: 200px; padding: 20px; background: #fff; }
.inquiry-popup-form .form-title { float: left; width: 100%; height: auto; padding: 0px; margin: 0px 0px 20px 0px; color: #333; font-size: 24px; line-height: 28px; font-weight: bold; text-decoration: none; }
.inquiry-popup-form .data-form-box { float: left; width: 144px; height: auto; margin: 0px 14px 0px 0px; }
.inquiry-popup-form .data-form-box.last { margin: 0px; }
.inquiry-popup-form .fieldset-wrapper-button_set label { display: none; }
.inquiry-popup-form .fieldset-wrapper-captcha_set { margin: 15px 0px 15px 0px; }


/* Pay Pal Express styles
**************************/

/*.paypal-shortcut { padding-bottom: 10px; }*/
/*.checkout-top-btn .paypal-shortcut { display: none; }*/
/*.paypal-shortcut .paypal-link { width: 100%; display: block; text-align: center; }*/
/*.paypal-checkout-review-shipping { margin: 0px; padding: 0px 0px 20px 0px; border-bottom: 1px solid #e4e4e4; }*/
/*.paypal-checkout-review-shipping h5 { margin-top: 0px; }*/
/*.paypal-checkout-review-shipping h5 .separator { font-weight: normal; }*/
/*.paypal-checkout-review-shipping h5 a { font-size: 14px; font-weight: normal; }*/
/*.paypal-checkout-review-shipping .shipping-address { margin: 0px 0px 0px -10px; padding: 10px; background: #f1f1f1; }*/
/*.paypal-checkout-review-shipping select#shipping_method { width: 90% !important; }*/
/*.paypal-checkout-review-items h3 { }*/
/*.paypal-checkout-review-items h3 .separator { font-weight: normal; }*/
/*.paypal-checkout-review-items h3 a { font-size: 16px; font-weight: normal; }*/
/*.paypal-total { float: right; width: 280px; }*/

/*.paypal-express-review #order_review_form { position: relative; z-index: 11; }*/

/*.paypal-express-review #details-reload .paypal-total { width: 330px; }*/

/*.paypal-express-review .comment-box { float: left; width: 60%; height: auto; padding: 0px; margin: 20px 0px 20px 0px; }*/
/*.paypal-express-review .comment-box h5 { float: left; width: 100%; height: auto; padding: 0px; margin: 0px 0px 10px 0px; font-size: 16px; line-height: 19px; }*/
/*.paypal-express-review .comment-box .textarea-box textarea { height: 100px; margin: 0px; }*/
/*.paypal-express-review ol.checkout-agreements { float: right; width: 330px; padding: 0px; margin: 0px 0px 10px 0px; list-style-type: none; position: relative; }*/
/*.paypal-express-review ol.checkout-agreements .agreement-content { display: none; }*/
/*.paypal-express-review ol.checkout-agreements input.checkbox { float: left; margin: 3px 6px 0px 0px; }*/
/*.paypal-express-review ol.checkout-agreements label { float: left; width: auto; margin: 0px; }*/

/*.paypal-express-review .edit-shopping-cart { float: left; margin: 15px 0px 0px 0px; color: #444; text-decoration: underline; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.paypal-express-review .edit-shopping-cart:hover { color: #888; }*/

/*.paypal-express-review .paypal-review-placeorderbtn-wrapper { float: right; width: 330px; }*/
/*.paypal-express-review .buttons-set .button.btn-checkout { float: right; width: 100%; background: #f27c21; }*/



/* Stenik Invoice Fileds styles
*********************************/

/*.invoice-fields-wrapper input.checkbox { float: left; margin: 4px 6px 0px 0px; }*/
/*.invoice-fields-wrapper .checkbox-content label { float: left; width: auto; }*/


/* Stenik Checkout Styles
****************************/

/*.stenik-checkout-top-login { float: left; height: auto; padding: 15px 20px; margin: 0 0 20px; background: #ededed; }*/
/*.stenik-checkout-top-login p { float: left; width: auto; padding: 0; margin: 0 10px 0 0; line-height: 44px; font-size: 13px; font-weight: 600; color: #000; }*/
/*.stenik-checkout-top-login .social-login { float: left; width: auto; height: 35px; padding: 0px; margin: 0px 10px 0px 0px; }*/
/*.stenik-checkout-top-login .social-login .facebook-login { padding: 6px 18px; line-height: 32px; }*/
/*.stenik-checkout-top-login .social-login .gplus-login { padding: 6px 18px; line-height: 32px; }*/
/*.stenik-checkout-top-login .button { min-width: 95px; padding: 12px 10px; }*/
/*.checkout-popup-login { width: 500px; height: 380px; padding: 20px; background: #fff; }*/
/*.checkout-popup-login .forgotpassword { float: right; margin: 7px 0px 0px 20px; line-height: 20px; color: #ce181e; border-bottom: 1px solid #ce181e; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }*/
/*.checkout-popup-login .forgotpassword:hover { color: #777; }*/

/*.stenik-checkout { position: relative; }*/
/*.stenik-checkout .step-title { float: left; width: 100%; margin: 0 0 20px; padding: 0; line-height: 50px; color: #000; font-size: 18px; font-weight: bold; }*/
/*.stenik-checkout .step-title .step-number { display: inline-block; width: 50px; height: 50px; border-radius: 100%; color: #FFF; text-align: center; margin-right: 7px;*/
/*	background: rgb(206,24,30); !* Old browsers *!*/
/*	background: -moz-linear-gradient(left, rgba(206,24,30,1) 0%, rgba(63,0,11,1) 100%); !* FF3.6-15 *!*/
/*	background: -webkit-linear-gradient(left, rgba(206,24,30,1) 0%,rgba(63,0,11,1) 100%); !* Chrome10-25,Safari5.1-6 *!*/
/*	background: linear-gradient(to right, rgba(206,24,30,1) 0%,rgba(63,0,11,1) 100%); !* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ *!*/
/*	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ce181e', endColorstr='#3f000b',GradientType=1 ); !* IE6-9 *!*/
/*}*/
/*.stenik-checkout .stenik-onepage-section { float: left; width: 100%; height: auto; padding: 0; margin: 0 0 10px; }*/
/*.stenik-checkout .fields .field { float: left; width: 50%; margin: 0; padding: 0 20px; }*/
/*.stenik-checkout .fields.wide { float: left; width: 100%; margin: 0; padding: 0 20px; }*/
/*.stenik-checkout .create-account-checkbox .input-checkbox { float: left; margin: 4px 6px 0 0; }*/
/*.stenik-checkout .create-account-checkbox .label-checkbox { float: left; margin: 0; font-size: 13px; font-weight: 500; }*/
/*.show-invoice-fileds .checkbox ~ label { font-size: 13px; font-weight: 500; }*/
/*.stenik-checkout .fields.check-box { margin-bottom: 10px; }*/
/*.stenik-checkout .fields.check-box input { float: left; margin: 4px 6px 0 0; }*/
/*.stenik-checkout .fields.check-box label { display: inline-block; font-size: 13px; font-weight: 500; }*/
/*.stenik-checkout .fields.speedyOfficeFields,*/
/*.stenik-checkout .fields.speedyNonOfficeFields { padding: 0; }*/
/*.stenik-checkout .fields.speedyNonOfficeFields .separator { float: left; width: 50%;padding: 0 20px; box-sizing: border-box; clear: left; text-align: center; margin-bottom: 10px; }*/
/*.stenik-checkout .fields.speedyNonOfficeFields .fields { float: left; width: 100%; }*/
/*.stenik-checkout .fields.speedyOfficeFields .field.wide,*/
/*.stenik-checkout .fields.speedyNonOfficeFields .field.wide { width: 100%; }*/

/*.stenik-checkout #billing-new-address-form .fields.control input.checkbox { float: left; margin: 3px 6px 0px 0px; }*/
/*.stenik-checkout #checkout-shipping-method-load dl.sp-methods { float: left; width: 100%; height: auto; padding: 0px 20px 0px 20px; margin: 0px; }*/
/*.stenik-checkout #checkout-shipping-method-load dl.sp-methods dt { display: none; float: left; width: 100%; height: auto; padding: 0px; margin: 0px 0px 10px 0px; font-weight: 700; }*/
/*.stenik-checkout #checkout-shipping-method-load dl.sp-methods dd { float: left; width: 100%; height: auto; padding: 0px; margin: 0px; }*/
/*.stenik-checkout #checkout-shipping-method-load dl.sp-methods dd ul { float: left; width: 100%; height: auto; padding: 0px; margin: 0px; }*/
/*.stenik-checkout #checkout-shipping-method-load dl.sp-methods dd ul li { float: left; width: 100%; height: auto; padding: 0px; margin: 0px 0px 8px 0px; }*/
/*.stenik-checkout #checkout-shipping-method-load dl.sp-methods dd input.radio { float: left; width: auto; height: auto; padding: 0px; margin: 4px 6px 0px 0px; }*/
/*.stenik-checkout #checkout-shipping-method-load dl.sp-methods dd input[type="radio"] { float: left; width: auto; height: auto; padding: 0px; margin: 4px 6px 0px 0px; }*/
/*.stenik-checkout #checkout-shipping-method-load dl.sp-methods dd label { float: left; width: auto; height: auto; padding: 0px; margin: 0px 0px 0px 0px; font-weight: 400; }*/
/*.stenik-checkout #checkout-shipping-method-load dl.sp-methods dd label .price { font-weight: 700; }*/
/*.stenik-checkout #onepage-checkout-shipping-method-additional-load .gift-messages { display: none; }*/
/*.stenik-checkout #onepage-checkout-shipping-method-additional-load .gift-messages-form { display: none; }*/
/*.stenik-checkout #stenik-onepage-section-payment_method dl.sp-methods { float: left; width: 100%; height: auto; padding: 0px 20px 0px 20px; margin: 0px; }*/
/*.stenik-checkout #stenik-onepage-section-payment_method dl.sp-methods dt { float: left; width: 100%; height: auto; padding: 0px; margin: 0px 0px 10px 0px; font-weight: 400; }*/
/*.stenik-checkout #stenik-onepage-section-payment_method dl.sp-methods dt input.paymentMethod { float: left; width: auto; height: auto; padding: 0px; margin: 4px 6px 0px 0px; }*/
/*.stenik-checkout #stenik-onepage-section-payment_method dl.sp-methods label { font-size: 13px; font-weight: 500; }*/
/*.stenik-checkout #stenik-onepage-section-payment_method dl.sp-methods dd { float: left; width: 100%; height: auto; padding: 0px 20px 0px 20px; margin: 0px 0px 20px 0px; }*/

/*.stenik-checkout .comment-box { float: left; width: 100%; height: auto; padding: 0px 20px 0px 20px; margin: 0px 0px 20px 0px; }*/
/*.stenik-checkout .comment-box h5 { float: left; width: 100%; height: auto; padding: 0px; margin: 0px 0px 10px 0px; font-size: 14px; line-height: 16px; }*/
/*.stenik-checkout .comment-box .textarea-box textarea { height: 100px; margin: 0px; }*/
/*.stenik-checkout ol.checkout-agreements { float: left; width: 100%; padding: 0px 20px 0px 20px; margin: 0px 0px 10px 0px; list-style-type: none; position: relative; }*/
/*.stenik-checkout ol.checkout-agreements li { float: left; width: 100%; height: auto; margin: 0px 0px 8px 0px; }*/
/*.stenik-checkout ol.checkout-agreements input.checkbox { float: left; margin: 3px 6px 0px 0px; }*/
/*.stenik-checkout ol.checkout-agreements label { float: left; width: 94%; margin: 0px; }*/
/*.stenik-checkout ol.checkout-agreements .agreement-content { float: left; width: 100%; height: auto; padding: 0px 0px 0px 20px; margin: 0px; }*/
/*.stenik-checkout ol.checkout-agreements .agreement-content p { margin: 0px 0px 6px 0px; color: #555; font-size: 12px; line-height: 14px; }*/
/*.stenik-checkout .checkbox-content { float: left; width: 100%; height: auto; margin: 0px 0px 10px 0px; padding: 0px 0px 0px 20px; }*/

/*.stenik-checkout .coupon-review-col { float: left; width: 370px; height: auto; margin: 0px 0px 10px 0px; position: absolute !important; top: -96px; right: -395px; }*/
/*.stenik-checkout .coupon-review-wrapper { float: left; width: 100%; height: auto; }*/
/*.stenik-checkout .coupon-review-wrapper.sticky-sidebar { position: fixed; width: 370px; top: 5px; right: 50%; z-index: 200; transform: translateX(162%); -webkit-transform: translateX(162%); }*/
/*.stenik-checkout .coupon-review-wrapper.sticky-sidebar.bottom-stop { position: absolute; top: auto; right: 0; bottom: 0; transform: translateX(0%); -webkit-transform: translateX(0%); }*/
/*.stenik-checkout .order-review { float: left; width: 100%; height: auto; }*/
/*.stenik-checkout .checkout-review-box { float: left; width: 100%; }*/
/*.stenik-checkout .checkout-review-box .review-title { display: block; cursor: pointer; position: relative; background: #ededed; margin: 0; padding: 13px 22px; font-size: 14px; font-weight: bold; color: #000; text-transform: uppercase; }*/
/*.stenik-checkout .checkout-review-box .review-title:before { content: ''; position: absolute; right: 32px; top: 23px; width: 2px; height: 16px; background: #000; }*/
/*.stenik-checkout .checkout-review-box .review-title:after { content: ''; position: absolute; right: 25px; top: 30px; width: 16px; height: 2px; background: #000;}*/
/*.stenik-checkout .checkout-review-box .review-title.opened:before { display: none; }*/
/*.stenik-checkout .checkout-review-box .review-title .items-qty { display: block; margin: 0; text-transform: none; }*/
/*.stenik-checkout .checkout-review-box .review-items-box { float: left; width: 100%; height: auto; max-height: 252px; overflow: auto; padding: 0; margin: 0; border-bottom: 1px solid #e1e1e1; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item { float: left; width: 100%; height: auto; min-height: 125px; padding: 0; margin: 0; background: #fff; border-bottom: none; position: relative; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item .cart-img-wrapper { float: left; width: 92px; height: 92px; padding: 0px; margin: 0px; border: 1px solid #dfdfdf; position: absolute; left: 16px; top: 16px; z-index: 10; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item .cart-img-wrapper img { float: left; width: 90px; height: 90px; padding: 0px; margin: 0px; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item .review-item-info { float: left; width: 100%; height: auto; padding: 13px 0px 0px 126px; margin: 0px; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item .item-title { float: left; width: 100%; height: 38px; overflow: hidden; margin: 0px 0px 5px 0px; color: #222; font-size: 15px; line-height: 18px; font-weight: 500; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item dl.item-options { float: left; width: 100%; height: auto; margin: 0px 0px 5px 0px; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item dl.item-options dt { float: left; width: auto; height: auto; margin: 0px 4px 0px 0px; padding: 0px; color: #8f8f8f; font-size: 13px; line-height: 15px; font-weight: 400; clear: left; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item dl.item-options dd { float: left; width: auto; height: auto; margin: 0px; padding: 0px; color: #8f8f8f; font-size: 13px; line-height: 15px; font-weight: 400; clear: right; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item .item-qty { float: left; width: auto; height: auto; margin: 3px 15px 0px 0px; font-size: 12px; font-weight: bold; line-height: 15px; color: #000; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item .cart-price { float: right; width: auto; height: auto; margin: 0px 10px 0px 0px; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item .cart-price .price { color: #222; font-size: 13px; line-height: 15px; font-weight: 400; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item .old-price { float: left; width: auto; height: auto; margin: 0px 10px 0px 0px; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item .old-price .cart-price { margin: 0px; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item .old-price .price { color: #777; font-size: 12px; line-height: 15px; font-weight: 400; text-decoration: line-through; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item .special-price { float: left; width: auto; height: auto; margin: 0px; }*/
/*.stenik-checkout .checkout-review-box .review-items-box .review-item .special-price .price { color: #e7352b; font-size: 12px; line-height: 15px; font-weight: 700; }*/
/*.stenik-checkout .checkout-review-box .total-table { width: 100%; border-collapse: collapse; margin: 0px; }*/
/*.stenik-checkout .checkout-review-box .total-table th { text-align: left; padding: 15px 10px 15px 25px; }*/
/*.stenik-checkout .checkout-review-box .total-table td { text-align: right; padding: 15px 25px 15px 10px; }*/
/*.stenik-checkout .checkout-review-box .total-table tfoot th { padding: 20px 10px 20px 25px; background: #ededed; }*/
/*.stenik-checkout .checkout-review-box .total-table tfoot td { padding: 20px 25px 20px 10px; background: #ededed; }*/

/*.stenik-onepage-section-overlay .loaderIcon { width: 28px; height: 28px; margin: -14px 0px 0px -14px; position: absolute; left: 50%; top: 50%; z-index: 9999; background: url(../images/preloader-28x28.gif) no-repeat center center; }*/
/*.stenik-checkout .coupon-review-col .stenik-onepage-section-overlay .loaderIcon { top: 65px; margin-top: 0px; }*/
/*.stenik-checkout #stenik-onepage-section-review .stenik-onepage-section-overlay .loaderIcon { !*top: 65px; margin-top: 0px;*! }*/

/*.stenik-onepage-section-overlay.stenik-checkout-overlay { display: none; width: 100%; height: 100%; background: rgba(255,255,255,0.5); position: absolute; top: 0px; left: 0px; z-index: 9999; }*/
/*.stenik-onepage-section-overlay.stenik-checkout-overlay .loaderIcon { position: fixed; }*/

/*.stenik-checkout .buttons-set { float: left; width: 100%; height: auto; padding: 0px 20px 0px 20px; margin: 0px 0px 40px 0px; }*/
/*.stenik-checkout .buttons-set .button.checkout-color { min-width: 240px; }*/

/*.must-login-checkout { position: relative; height: 500px; }*/
/*.login-first-msg { float: left; width: 100%; height: auto; padding: 20px 15px 20px 15px; margin: 10px 0px 20px 0px; background: #f5f5f5; color: #666; font-size: 16px; line-height: 18px; font-weight: normal; }*/
/*.social-login-checkout { float: left; width: 90%; }*/
/*.social-login-checkout .social-login { float: left; width: 100%; }*/
/*.social-login-checkout .social-login .facebook-login { padding-left: 12px; padding-right: 12px; }*/
/*.social-login-checkout .social-login .gplus-login { padding-left: 14px; padding-right: 14px; }*/
/*.stenik-checkout.must-login-checkout .coupon-review-col { top: 20px; }*/


/* Stenik Checkout Econt And Stenik Checkout Rapido Styles
*************************************************************/

/*.delivery-to-wrapper { float: left; width: 100%; height: auto; margin: 10px 0; }*/
/*.delivery-to-wrapper .step-sub-title { padding: 10px 0px 10px 0px; color: #222; font-size: 14px; line-height: 17px; font-weight: 700; }*/
/*.delivery-to-wrapper .step-sub-title em { font-style: normal; margin-right: 4px; }*/
/*.delivery-to-wrapper .standart-address-fileds-wrapper { margin: 0px -20px 0px -20px; }*/
/*.shipping-methods-group { float: left; width: 100%; height: auto; margin: 0px 0px 10px 0px; }*/
/*.shipping-methods-group .shippingMethod input.radio.prechoose { float: left; margin: 4px 6px 0px 0px; }*/
/*.delivery-to-wrapper #extensa_econt-form { padding: 10px 0px 10px 0px; margin: 0px -20px 0px -20px; }*/
/*.delivery-to-wrapper #extensa_econt-form p.comb-text { padding: 0px 20px 0px 20px; font-size: 12px; line-height: 18px; }*/
/*.delivery-to-wrapper #extensa_econt-form .extensa_econt_services { padding: 0px 20px 0px 20px; }*/
/*.delivery-to-wrapper #extensa_econt-form .extensa_econt_services p { margin-bottom: 5px; }*/
/*.delivery-to-wrapper #extensa_econt-form .officeLocator button.button { margin-top: 23px; }*/
/*.delivery-to-wrapper #extensa_rapido-form { padding: 10px 0px 10px 0px; margin: 0px -20px 0px -20px; }*/
/*.delivery-to-wrapper p.error-msg { padding: 0px 20px 0px 20px; color: #e7352b; }*/
/*.stenik-checkout .autocomplete { width: 100%; margin: 0px; padding: 0px; background: #fff; border: 1px solid #ccc; position: absolute; left: 0px; top: 38px; z-index: 10000; }*/
/*.stenik-checkout .autocomplete ul { margin: 0px; padding: 0px; }*/
/*.stenik-checkout .autocomplete ul li.selected { background: #ccc; }*/
/*.stenik-checkout .autocomplete ul li { padding: 6px 10px; cursor: pointer; text-align: left; color: #222; font-size: 14px; }*/
/*.stenik-checkout .please-wait { display: none; position: absolute; right: 5px; top: 5px; z-index: 20; }*/
/*.stenik-checkout .please-wait .v-middle { width: 28px; height: 28px; }*/
/*#main .select2-container { width: 100% !important; height: 40px; margin: 0px 0px 8px 0px; outline: none; }*/
/*#main .select2-container .select2-selection { height: 40px; border: 1px solid #ccc; outline: none; border-radius: 0; }*/
/*.delivery-to-wrapper .input-text { background: transparent; }*/
/*#main .select2-container .select2-selection { background: transparent; }*/
/*#main .select2-container .select2-selection .select2-selection__rendered { height: 40px; line-height: 40px; padding: 0px 70px 0px 10px; outline: none; font-size: 13px; color: #777; text-decoration: none; font-weight: 500; font-family: 'Montserrat', Arial, sans-serif; }*/
/*#main .select2-container .select2-selection .select2-selection__arrow { width: 48px; height: 38px; background: url(../images/select-arrow.png) no-repeat center center; }*/
/*#main .select2-container .select2-selection .select2-selection__arrow b { display: none; }*/
/*#main .select2-container--default .select2-results__option--highlighted { color: #333 !important; background: #ccc !important; }*/

/*.my-account .delivery-to-wrapper #extensa_econt-form { margin: 0px; }*/
/*.my-account .delivery-to-wrapper #extensa_rapido-form { margin: 0px; }*/
/*.my-account .delivery-to-wrapper #extensa_econt-form p.comb-text { padding: 0px 20px 0px 0px; }*/
/*.my-account .delivery-to-wrapper #extensa_econt-form .extensa_econt_services { padding: 0px 20px 0px 0px; }*/
/*.my-account p.error-msg { padding: 0px 20px 0px 0px; color: #e7352b; }*/
/*.my-account ul.form-list li.fields.econt-door-address-fields { width: 100%; margin: 0px; }*/
/*.my-account ul.form-list li.fields.econt-door-address-fields ul li.fields { width: 100%; margin: 0px; }*/
/*.my-account ul.form-list li.fields.econt-door-address-fields ul li.fields .field { display: inline-block; width: 48%; margin: 0px 1.5% 0px 0px; vertical-align: top; }*/
/*.my-account ul.form-list li.fields.econt-office-address-fields { width: 100%; margin: 0px; }*/
/*.my-account ul.form-list li.fields.econt-office-address-fields ul li.fields { width: 100%; margin: 0px; }*/
/*.my-account ul.form-list li.fields.econt-office-address-fields ul li.fields .field { display: inline-block; width: 48%; margin: 0px 1.5% 0px 0px; vertical-align: top; }*/
/*.my-account ul.form-list li#extensa_rapido-to_door { width: 100%; margin: 0px; }*/
/*.my-account ul.form-list li#extensa_rapido-to_door ul li.fields { width: 100%; margin: 0px; }*/
/*.my-account ul.form-list li#extensa_rapido-to_door ul li.fields.wide { width: 98%; margin: 0px; }*/
/*.my-account ul.form-list li#extensa_rapido-to_door ul li.fields .field { display: inline-block; width: 48%; margin: 0px 1.5% 0px 0px; vertical-align: top; }*/
/*.my-account .rapido-address-fields-content li.control .field { display: inline-block; width: 48%; margin: 0px 1.5% 0px 0px; vertical-align: top; }*/
/*.my-account .select2-container { width: 100% !important; height: 40px; margin: 0px 0px 8px 0px; outline: none; }*/
/*.my-account .select2-container .select2-selection { height: 40px; border: 1px solid #cbcbcb; border-radius: 5px; outline: none; }*/
/*.my-account .select2-container .select2-selection .select2-selection__rendered { height: 38px; line-height: 38px; padding: 0px 25px 0px 10px; outline: none; font-size: 13px; color: #6f6f6f; text-decoration: none; font-weight: normal; font-family: 'Montserrat', Arial, sans-serif; }*/
/*.my-account .select2-container .select2-selection .select2-selection__arrow { height: 38px; border-radius: 5px; background: url(../images/select-arrow.png) no-repeat center center; }*/
/*.my-account .select2-container .select2-selection .select2-selection__arrow b { display: none; }*/
/*.my-account  select#extensa_rapido:take_office { width: 48% }*/


/* Start Responsive queries
*****************************/
@media only screen and (max-width: 1500px) {
	.search-area { margin: -50px 0 20px; }
}
@media only screen and (max-width: 1220px) {
  header .searchautocomplete { width: 44px; }
  header .searchautocomplete .nav .nav-input input { display: none; position: absolute; top: 0; right: 0; }
  header .searchautocomplete.opened .nav .nav-input input { display: block; width: 300px; background: #FFF; }
  .navbar-nav > li > a { padding-left: 15px; padding-right: 15px; }
  .navbar-nav .sub-nav .sub-nav-col { width: 219px; }
  .search-form { width: 300px; margin-left: -114px; }
  .search-box { height: auto; }
  .search-box .row-title { margin-bottom: 20px; }
  .search-fields { margin-bottom: 0; text-align: center; }
  .search-field-box { float: none; display: inline-block; vertical-align: top; margin: 0 15px 20px; text-align: left; }
  .search-field-box:last-of-type { margin-right: 15px; }
  .homepage-categories .navbar-nav > li > .sub-nav { padding: 10px 0; }
  .homepage-categories .navbar-nav .sub-nav .sub-nav-col ul li a.sub-cat-name { font-size: 12px; line-height: 14px; padding: 5px 30px 5px 10px; }
  .homepage-text-block .row { margin: 0; }
  .widget-box .widget-info .title { font-size: 26px; line-height: 30px; }
  .owl-carousel.banner-slider .widget-box .widget-info .title { margin-bottom: 25px; }
  .owl-carousel.product-slider .owl-nav { top: -67px; margin-top: 0; }
  .upsell-products .owl-carousel.product-slider .owl-nav { top: -40px; }
  .owl-carousel.product-slider .owl-nav .owl-prev { margin-left: 0; }
  .owl-carousel.product-slider .owl-nav .owl-next { margin-right: 0; }
  .homepage-products { padding: 0; }
  .product-widget-container { padding: 20px 0; }
  .cars-widgets { padding: 30px 0; }
  .services-widget { padding: 30px 0 10px; }
  .services-widget h2 { margin-bottom: 20px; }
  .widget-button { margin-bottom: 0; }
  .widgets h2 { margin-bottom: 20px; }
  .widgets h2 ~ p > a { margin-bottom: 20px; }
  .parts-links-item { width: 32%; margin-right: 2%; }
  .product-box { height: 400px; }
  .product-box-info { padding: 10px 0 0; }
  .product-title-sku { height: auto; }
  .product-box .compare-link { font-size: 12px; line-height: 22px; }
  .product-box .compare-link::before { top: 0; }
  .compare-link.added::after { top: 3px; left: 1px; }
  .owl-carousel.banner-slider .widget-box img { min-height: 20px; }
  .product-box .actions .button { min-width: 140px; padding-left: 5px; padding-right: 5px; font-size: 11px; }
  .product-box .icon-link { width: 34px; margin-left: 2px; }
  .product-box .icon-link .icon-svg.wishlist { margin-left: 7px; }
  .product-box .icon-link .icon-svg.compare { margin-left: 7px; }
  .brand-item { height: 116px; }
  .service-box .title { padding-top: 15px; }
  .payments-box p img { padding: 0px 5px 10px 5px; }
  .pager { margin: 20px 0px 0px 0px; }
  .col1-layout .pager { margin: 0px; }
  .sidebar { padding-right: 10px; }
  .sidebar .drop-down.has-img .sub-options { width: 100%; }
  .list-mode .product-box .image-wrapper { width: 200px; height: 200px; margin-right: 10px; }
  .list-mode .product-box .product-info { width: 225px; }
  .list-mode .product-box .actions .button .compare { margin: 4px 6px 0px 10px; }
  .catalog-product-view .brand-logo { width: 65px; height: 65px; }
  .product-options .button.add-to-cart .icon-svg.shopping-cart { margin: 1px 2px 0px 2px; }
  .discount .title { font-size: 13px; }
  .sidebar .info-cms-block { margin-top: 0px; }
  table.total-table { margin-bottom: 10px; }
  table.total-table th { font-size: 13px; line-height: 15px; padding: 0px 10px 10px; }
  table.total-table td { font-size: 13px; line-height: 15px; padding: 0px 10px 10px; }
  table.total-table tfoot th { font-size: 15px; line-height: 17px; padding: 10px; }
  table.total-table tfoot td { font-size: 15px; line-height: 17px; padding: 10px; }
  .news-box.listing-style .image-wrapper { width: 360px; }
  .news-box.listing-style .news-listing-info { width: 338px; }
  table.compare-table th { padding: 10px; }
  table.compare-table td { padding: 10px; }
  .back-to-top { right: 100px; }
  #checkout-step-login .social-login { width: 100%; }
  #checkout-step-login .social-login .facebook-login { min-width: 150px; margin-right: 5px; padding-left: 10px; padding-right: 10px; }
  #checkout-step-login .social-login .gplus-login { min-width: 150px; padding-left: 10px; padding-right: 10px; }
  .opc .step #checkout-agreements .order-comment { width: 310px; top: -100px; }
  .paypal-express-review .order-comment { float: left; width: 310px; margin-top: -90px; }
  .data-table th { padding: 10px 5px; font-size: 18px; }
  .data-table td { padding: 10px 5px; }
  .my-account .box-title h3 { font-size: 30px; line-height: 30px; }
  #opc-payment .unicredit-leasing-calculator .horizontal-scroll-wrapper { width: 630px; }
  .stenik-checkout-top-login { width: 620px; }
  .stenik-checkout-top-login p { margin-right: 5px; }
  .stenik-checkout-top-login p:first-of-type { width: 100%; }
  .stenik-checkout-top-login .social-login { margin-right: 5px; }
  .stenik-checkout-top-login .social-login .facebook-login { padding-left: 10px; padding-right: 10px; }
  .stenik-checkout-top-login .social-login .gplus-login { padding-left: 10px; padding-right: 10px; }
  .stenik-checkout-top-login .button { min-width: 85px; padding-left: 10px; padding-right: 10px; }
  .stenik-checkout .coupon-review-col { width: 300px; right: -320px; }
  .stenik-checkout #stenik-onepage-section-review { /*width: 300px; right: -320px;*/ }
  .stenik-checkout .coupon-review-wrapper.sticky-sidebar { width: 300px; transform: translateX(160%); -webkit-transform: translateX(160%); }
  .stenik-checkout .checkout-review-box.sticky-sidebar { /*width: 300px; transform: translateX(160%); -webkit-transform: translateX(160%);*/ }
  footer { padding-top: 30px; background: #f2f2f2; }

  .buying-form-wrapper { width: 480px; margin-right: 20px; }

}

@media only screen and (min-width: 991px) {
    .cms-index-index .slider-wrapper {
        min-height: 500px;
    }
}
@media only screen and (max-width: 991px) {

  header { display: none; }
  .responsive-header { display: block; }
  .top-banner { margin-top: 50px; }
  #main { padding-top: 60px; }
  .homepage-top-area .col-md-3 { display: none; }
  .product-box .actions { display: none !important; }
  .brand-item { height: 88px; }
  .price-box .old-price { font-size: 13px; }
  .price-box .special-price { font-size: 14px; }
  .price-box .regular-price { font-size: 14px; }
  .price-box .price-from { font-size: 14px; }
  .price-box .price-to { font-size: 14px; }
  .news-box .title { max-height: 68px; font-size: 14px; line-height: 16px; }
  .accent-text .accent-text-content { padding: 20px; }
  .service-box { text-align: center; }
  .service-box .image-wrapper { float: none; display: table; margin: 0px auto 10px auto; }
  .service-box .title { width: 100%; font-size: 20px; line-height: 24px; padding-top: 0px; }
  .service-box .sub-title { width: 100%; text-align: center; }
  .toolbar .view-mode { float: right; margin-right: 0px; }
  .toolbar .show-only-available { clear: left; margin-top: 10px; }
  .catalog-product-view .breadcrumbs { display: block; }
  .pager { margin: 10px 0px 0px 0px; }
  .col1-layout .pager { margin: 10px 0px 0px 0px; }
  .parts-links { margin: 0 -1%; }
  .parts-links-item { width: 48%; margin: 0 1% 10px !important; }
  .list-mode .product-box:after { display: none; }
  .list-mode .product-box .image-wrapper { width: 240px; height: 240px; margin-right: 20px; }
  .list-mode .product-box .product-info { width: 255px; }
  .list-mode .product-box .actions { display: block !important; float: left; width: 45%; margin-top: 10px; }
  .list-mode .product-box .price-actions-col { width: 100%; margin: 0px; padding: 10px 20px 10px 20px; background: #f7f7f7; }
  .list-mode .product-box .price-actions-col .price-box { float: left; width: 360px; margin: 0px; }
  .list-mode .product-box .price-actions-col .rating-box { float: right; }
  .list-mode .product-box .price-box .regular-price { float: left; width: 100%; margin: 0px; text-align: left; }
  .list-mode .product-box .price-box .old-price { float: left; width: auto; margin: 0px 10px 0px 0px; }
  .list-mode .product-box .price-box .special-price { float: left; width: auto; margin: 0px; }
  .catalog-product-view h1 { font-size: 18px; line-height: 22px; }
  .gallery-box .gallery-main-images { padding: 0px; margin: 0px 0px 10px 0px; }
  .gallery-box .gallery-thumbnails { width: 100%; height: auto; padding: 0px; margin: 0px; position: relative; left: auto; top: auto; }
  .gallery-box .gallery-thumbnails .thumb { margin: 0px 5px 0px 0px; }
  .col-xs-10.sku-and-short-description { width: 75%; }
  .col-xs-2.brand-logo-wrapper { width: 25%; }
  .col-xs-6.product-options { width: 100%; }
  .col-xs-6.add-to-links { width: 100%; }
  .status-row { margin-bottom: 15px; }
  .status-row p.sku { margin-right: 10px; }
  .availability-wrapper { min-width: 130px; display: inline-block;position: relative; min-height: 22px;}
  .availability { margin-right: 10px; }
  .status-row .compare-link { max-width: 150px; font-size: 12px; line-height: 14px; }
  .product-options .button { margin-left: 0; }
  .tabs .tabs-nav ul li .tab-nav-item { padding: 10px; font-size: 15px; }
  .shopping-cart-items .cart-img-wrapper { width: 70px; height: auto; }
  .shopping-cart-items .cart-info { padding-left: 80px; }
  .shopping-cart-items .cart-info .item-title { margin-top: 0px; }
  .shopping-cart-items .cart-info .attributes { font-size: 12px; }
  .sidebar .cart-sidebar .button { padding-left: 10px; padding-right: 10px; }
  .social-login { width: 310px; }
  .social-login .facebook-login { min-width: 150px; padding: 8px 6px 8px 6px; margin-right: 5px; }
  .social-login .gplus-login { min-width: 150px; padding: 8px 6px 8px 6px; }
  .social-login .button .icon-svg { margin-right: 4px; }
  .account-create .social-login { width: 100%; }
  .account-create .social-login .facebook-login { margin: 0px 0px 10px 0px; }
  .account-create .registration-form { width: 100%; }
  .news-box.listing-style .image-wrapper { width: 270px; }
  .news-box.listing-style .news-listing-info { width: 263px; padding: 10px; }
  .catalog-product-compare-index h1 { font-size: 40px; line-height: 40px; }
  .catalog-product-compare-index .more-items-for-compare { margin-top: -55px; }
  table.compare-table td .button { min-width: 20px; padding-left: 10px; padding-right: 10px; }
  table.compare-table td .button .icon-svg.shopping-cart { margin: -1px 5px 0px 0px; }
  .footer-col .title { font-size: 18px; line-height: 21px; margin: 0px 0px 15px 0px; }
  .payments-box p img { margin: 0px 7px; }
  #checkout-step-login .social-login .facebook-login { width: 100%; margin-right: 0px; margin-bottom: 10px; padding-left: 10px; padding-right: 10px; }
  #checkout-step-login .social-login .gplus-login { width: 100%; padding-left: 10px; padding-right: 10px; }
  .opc .step #checkout-agreements .order-comment { width: 240px; }
  .paypal-express-review .comment-box { width: 45%; }
  .paypal-express-review .order-comment { width: 240px; }
  .opc .step #checkout-review-table-wrapper table.total-table { width: 240px; }
  .opc .step #checkout-agreements ol.checkout-agreements { width: 235px; }
  .opc-block-progress .block-content dl dt { font-size: 15px; }
  .opc-block-progress .block-content dl dd address { font-size: 12px; }
  .sidebar .sidebar-nav ul li a { font-size: 16px; }
  .my-account .box-title h3 { margin-bottom: 10px; font-size: 23px; line-height: 23px; }
  .my-account .box-title a { font-size: 13px; line-height: 15px; margin-top: 23px; }
  .my-account .box-content h4 { font-size: 20px; line-height: 20px; }
  .my-account .my-wishlist .buttons-set .button.btn-share { margin-bottom: 10px; }
  .my-account .my-wishlist .buttons-set .button.btn-add { float: right; margin-bottom: 10px; }
  #cboxContent #cboxClose { width: 30px; height: 30px; right: -15px; top: -15px; background-color: #ccc; }
  #opc-payment .unicredit-leasing-calculator .horizontal-scroll-wrapper { width: 480px; }
  .stenik-checkout-top-login { width: 100%; }
  .stenik-checkout .col-xs-8 { width: 100%; }
  .stenik-checkout.must-login-checkout .col-xs-4.new-users { width: 100%; min-height: 20px; }
  .stenik-checkout.must-login-checkout .login-first-msg { margin-bottom: 0px; }
  .stenik-checkout.must-login-checkout .col-xs-8.registered-users:after { display: none; }
  .stenik-checkout.must-login-checkout .social-login-checkout { width: 100%; }
  .stenik-checkout .coupon-review-col { position: relative !important; width: 100%; right: auto; top: auto; }
  .stenik-checkout #stenik-onepage-section-review { /*position: relative !important; width: 100%; right: auto; top: auto;*/ }
  .stenik-checkout.must-login-checkout .coupon-review-col { margin-bottom: 40px; }
  .stenik-checkout.must-login-checkout #stenik-onepage-section-review { /*margin-bottom: 40px;*/ }
  .stenik-checkout .checkout-review-box .review-items-box { display: none; }
  .stenik-checkout .checkout-review-box .review-items-box .review-item .cart-price { float: left; }
  .stenik-checkout .officeLocator button.button { display: none; }

  .buying-form-wrapper { width: 100%; margin: 0 0 20px; }
  .buying-form-wrapper ~ .text-page { width: 100%; }

}

@media only screen and (max-width: 767px) {

  body.scroll-disabled { position: fixed; left: 0; right: 0; }
  .button { min-width: 140px; padding: 10px; }
  .row-header .button { margin-top: -11px; }
  .widgets.three-in-row .row { margin-left: 0px; margin-right: 0px; }
  .widgets .col-xs-4 { padding-left: 1px; padding-right: 1px; }
  .widget-box.wide .button.right40 { right: 10px; }
  .owl-carousel.banner-slider .owl-nav { padding: 0px; }
  .owl-carousel.banner-slider .widget-box .widget-info { max-width: 100%; }
  .widget-box .widget-info { padding: 20px; }
  .widget-box .widget-info .button { left: 20px; bottom: 20px; }
  .car-widget { margin-bottom: 20px; }
  .product-box { height: 370px; }
  .product-box .compare-link { line-height: 24px; }
  .list-mode .product-box .image-wrapper { margin-right: 30px; }
  .list-mode .product-box .product-info { width: 290px; margin-bottom: 10px; }
  .list-mode .product-box .actions { width: 65%; }
  .brand-item { height: 100px; }
  .col2-left-layout .main-content { position: relative; padding-top: 60px; }
  .col2-right-layout .main-content { position: relative; padding-top: 60px; }
  .product-view-main .gallery-box-wrapper { position: relative; padding-top: 115%; }
  .product-view-main .gallery-box { position: absolute; left: 0; top: 0; width: 100%; height: auto;}
  .product-view-main .gallery-box .main-image { text-align: center; }
  .product-view-main .gallery-box .main-image img { margin: auto; }
  .sidebar { width: 300px; margin: 0px 0px 0px -150px; padding: 0px; position: absolute; left: 50%; top: 0px; z-index: 1000; }
  .checkout-cart-index .sidebar { width: 100%; margin: 0px; padding-left: 20px; padding-right: 20px; position: relative; left: auto; top: auto; }
  .checkout-cart-index .sidebar .cart-sidebar .checkout-top-btn button.button.btn-proceed-checkout { margin: -5px 0px 10px 0px; }
  .checkout-cart-index .sidebar .cart-sidebar .button { padding: 15px 10px; }
  .sidebar .open-responsive-sidebar { display: block; width: 100%; padding-top: 10px; padding-bottom: 10px; }
  .sidebar .sidebar-responsive-wrapper { display: none; width: 100%; padding: 3px 10px 0px 10px; position: absolute; left: 0px; top: 37px; z-index: 500; background: #222; border-top: none; border-radius: 0px 0px 5px 5px; }
  .sidebar .sidebar-responsive-wrapper .block-layered-nav { padding: 0px; border: none; }
  .sidebar .drop-down { padding: 10px 0px 0px 0px; margin: 0px 0px 10px 0px; border-top: 1px solid #434343; border-bottom: none; }
  .sidebar .drop-down .open-item { color: #fff; }
  .sidebar .drop-down .sub-options li .clever-link,
  .sidebar .drop-down .sub-options li a { color: #fff; }
  .sidebar .drop-down.multiselect .sub-options li .clever-link:before,
  .sidebar .drop-down.multiselect .sub-options li a::before { background-color: #FFF; }
  .sidebar .drop-down.categories { border-top: none;  }
  .sidebar .drop-down.categories .open-item { border-top: none; background: none; padding: 0; }
  .sidebar .drop-down.categories ul { border: none; }
  .sidebar .drop-down.categories ul a { padding: 8px 30px 8px 0; }
  .filters-wrapper { border: none; }
  .filters-title { display: none; }
  .filters-content { padding: 0; }
  div.layer-slider .price { color: #FFF; }
  .sidebar .drop-down.has-js-scroll .sub-options .jspContainer { width: 100% !important; overflow: visible !important; }
  .sidebar .drop-down.has-js-scroll .sub-options .jspContainer .jspPane { width: 100% !important; position: relative !important; left: auto !important; top: auto !important; }
  .sidebar .sidebar-nav { margin-bottom: 10px; }
  div.layer-slider .price-slider-inputs input.input-text { width: 112px; }
  .sidebar .sidebar-nav ul li a { padding-left: 10px; padding-right: 10px; color: #a6a6a6; }
  .sidebar .sidebar-nav ul li a:hover { background: inherit; }
  .sidebar .state-content { background: #a0a0a0; }
  .sidebar div.layer-slider .price-slider { display: none; }
  .sidebar div.layer-slider .price-slider-inputs { display: block; }
  .sidebar div.layer-slider .price-range .button.filter-button { padding: 0 20px; }
  .sidebar .price-slider-inputs .input-text { background: #ccc; color: #000; box-shadow: none !important; }
  .checkout-onepage-index .sidebar.col-right { display: none; }
  .checkout-onepage-index .col2-right-layout .main-content { padding-top: 0px; }
  .product-view-main .col-xs-5 { width: 100%; }
  .product-view-main .col-xs-7 { width: 100%; }
  .gallery-box { padding-right: 0; }
  .product-view-main .gallery-thumbnails { display: none; }
  .catalog-product-view h1 { text-align: center; }
  .product-options .button { width: 100%; }
  .product-tabs .tabs-nav { display: none; }
  .tabs .tabs-content { padding: 0; }
  .product-tabs .tabs-content .tab-nav-item.responsive { display: block; }
  .product-tabs .tabs-content .tab { padding: 10px; background: #f1f1f1; margin-bottom: 1px; }
  .inquiry-popup-form { width: 420px; }
  .checkout-cart-index .breadcrumbs { display: none; }
  .checkout-cart-index h1 { font-size: 25px; line-height: 27px; text-align: center; }
  .checkout-cart-index .shopping-cart { margin-bottom: 10px; padding-right: 10px; }
  .shopping-cart-items .cart-row.header-row .cell { font-size: 16px; line-height: 16px; }
  .shopping-cart-items .cell.col1 { width: 52%; }
  .shopping-cart-items .cell.col2 { display: none; }
  .shopping-cart-items .cell.col3 { width: 15%; }
  .shopping-cart-items .cell.col5 { width: 9%; }
  .shopping-cart-items .cart-row.header-row { padding: 5px 0px 5px 0px; }
  .shopping-cart-items .cart-row { padding: 10px 0px 10px 0px; }
  .shopping-cart-items .clearH2 { display: none; }
  .cart-responsive-btn { display: block; margin-bottom: 20px; padding: 15px 10px; }
  table.total-table th,
  table.total-table td { padding: 10px; }
  .continue-shopping { display: none; }
  .discount { margin-top: 0px; }
  .registered-users { float: left; width: 100%; min-height: 100px; }
  .registered-users:after { display: none; }
  .new-users { float: left; width: 100%; min-height: 100px; }
  .registered-users .login-form { width: 100%; }
  .customer-account-forgotpassword .main-content .col-xs-6 { width: 100%; }
  .social-login { width: 100%; }
  .news-box.listing-style .image-wrapper { width: 50%; }
  .news-box.listing-style .news-listing-info { width: 50%; padding: 2%; }
  .social-share.wide-row { padding: 10px; }
  .catalog-product-compare-index h1 { font-size: 30px; line-height: 30px; }
  .catalog-product-compare-index .more-items-for-compare { margin-top: -45px; padding-top: 5px; padding-bottom: 5px; }
  table.compare-table th { min-width: 30px; }
  .shops-listing-wrapper .shops-listing.col-xs-6 { width: 100%; }
  .shops-listing-wrapper .shops-listing .toolbar .drop-down.chose-city { width: 100%; }
  .shops-listing-wrapper .shops-listing-google-map.col-xs-6 { width: 100%; }
  .shops-listing-wrapper #gmap { height: 300px; }
  .shops-inner-wrapper .shop-info .col-xs-6 { width: 100%; }
  footer.image-background { background-image: none; }
  .info-cols .row { font-size: 0; }
  .footer-col { float: none; display: inline-block; margin-bottom: 20px; vertical-align: top; }
  .payments { margin-top: -30px; margin-bottom: 20px; }
  .payments-box p img { margin: 0px; }
  .copy-rights .col-xs-8 { width: 100%; text-align: center; margin: 0px; padding: 0px; }
  .copy-rights .col-xs-4 { width: 100%; text-align: center; margin: 0px; padding: 0px; }
  .copy-rights .copy { line-height: 36px; width: 100%; padding: 0px 10px 0px 10px; text-align: center; }
  .copy-rights .stenik-info { line-height: 36px; width: 100%; text-align: center; }
  .copy-rights .stenik-info .icon-svg.stenik { float: none; display: inline-block; margin-bottom: -5px; }
  .checkout-onepage-index h1 { font-size: 35px; line-height: 37px; text-align: center; }
  .opc #checkout-step-login.step .checkout-login-chooser .button { min-width: 20px; width: 100%; }
  .opc .step #checkout-agreements .order-comment { width: 40%; }
  .paypal-express-review .comment-box { width: 45%; margin-top: 10px; }
  .paypal-express-review .order-comment { width: 40%; }
  .paypal-express-review #details-reload .paypal-total { width: 240px; }
  .opc .step #checkout-review-table-wrapper table.total-table { width: 55%; }
  .opc .step #checkout-agreements ol.checkout-agreements { width: 55%; }
  .sales-order-view .my-account .box-title h2 { font-size: 30px; line-height: 30px; }
  .my-account .my-wishlist .buttons-set .button.btn-add { margin-bottom: 10px; margin-left: 10px; }
  .terms-popup { width: 420px; }
  .unicredit-leasing-calculator .downpayment-content input.input-text.downpayment { height: 38px; }
  .unicredit-leasing-calculator .horizontal-scroll-wrapper { margin-bottom: 20px; }
  #opc-payment .unicredit-leasing-calculator .horizontal-scroll-wrapper { width: 390px; }
  .stenik-ajaxAddToCart-result { width: 85%; left: 4%; top: 4%; margin: 0px; }
  .stenik-ajaxAddToCart-result .ajax-cart-item { padding: 15px 0px 15px 5px; }
  .stenik-ajaxAddToCart-result .cart-img-wrapper { width: 50px; height: 50px; }
  .stenik-ajaxAddToCart-result .item-info { width: 130px; }
  .stenik-ajaxAddToCart-result .ajax-cart-total .cart-summary-count { font-size: 12px; line-height: 15px; }
  .stenik-ajaxAddToCart-result .ajax-cart-total .itermediate-price { font-size: 12px; line-height: 15px; }
  .stenik-ajaxAddToCart-result .crosssale-products .product-box { height: 200px; }
  .stenik-ajaxAddToCart-result .crosssale-products .product-box .title { font-size: 13px; }

}

@media only screen and (max-width: 479px) {

  h1 { font-size: 30px; line-height: 32px; text-align: center; }
  h2 { font-size: 22px; line-height: 25px; }
  h3 { font-size: 22px; line-height: 22px; }
  h4 { font-size: 20px; line-height: 20px; }
  h5 { font-size: 18px; line-height: 18px; }
  h6 { font-size: 16px; line-height: 16px; }
  blockquote p { font-size: 14px; line-height: 20px; }
  .responsive-header .responsive-search-wrapper .open-responsive-search { right: 58px; }
  .button { width: 100%; }
  .social-login .facebook-login { margin-bottom: 10px; }
  .widgets h2,
  .row-title { font-size: 22px; line-height: 24px; }
  .homepage-text-block h1 { font-size: 24px; line-height: 26px; }
  .widgets .row { margin: 0; }
  .cms-index-index .brands-listing .row-title { font-size: 17px; }
  .responsive-header .responsive-menu.opened .responsive-menu-sub { width: 280px !important; }
  .responsive-header .responsive-menu.opened .responsive-menu-sub ul li.parent > a { background-position: 248px 11px; }
  .responsive-header .responsive-menu.opened .responsive-menu-sub ul li ul li.parent > a { background-position: 244px 10px; }
  .responsive-header .responsive-menu .responsive-menu-sub .search-form input.search-input { font-size: 11px; box-shadow: none; }
  .search-field-box { width: 100%; margin: 0 0 20px; }
  .widget-box .widget-info .title { margin-bottom: 0px; font-size: 20px; line-height: 24px; }
  .widget-box .button { display: none; }
  .slider .widget-box .button { display: block; }
  .widgets.marginB20 { margin-bottom: 10px; }
  .widgets.marginB30 { margin-bottom: 20px; }
  .slider.marginB20 { margin-bottom: 10px; }
  .services-widget .row { font-size: 0; }
  .services-widget .row > p { display: none; }
  .services-widget .col-xs-6 { display: inline-block; float: none; vertical-align: top; }
  .parts-links { margin: 0; }
  .parts-links-item { width: 100%; margin: 0 0 10px !important; }
  .products.marginB20 { margin-bottom: 10px; }
  .products-list .col-xs-3 { width: 50%; }
  .product-box { height: 370px; }
  .product-box .compare-link { line-height: 22px; padding-right: 40px; }
  .product-box .price-box { margin-bottom: 5px; }
  .product-box .sku { margin-bottom: 3px !important; }
  .list-mode .product-box { border: 1px solid #e9e9e9 !important; box-shadow: none; }
  .list-mode .product-box .image-wrapper { float: none; display: table; width: 180px; height: 180px; margin: 0px auto 0px auto; }
  .list-mode .product-box .product-info { width: 100%; text-align: center; }
  .list-mode .product-box .price-actions-col { padding: 15px 10px 10px 10px; }
  .list-mode .product-box .price-actions-col .price-box { width: 260px; }
  .news .col-xs-3 { width: 50%; }
  .service-box .title { font-size: 17px; line-height: 22px; }
  .catalog-category-view h1 { font-size: 24px; line-height: 28px; }
  .toolbar .drop-down { margin-right: 4px; }
  .toolbar .drop-down .open-item { font-size: 13px; }
  .toolbar .sorting-arrow { margin-right: 0px; }
  .toolbar .sort-block { float: left; width: 48%; }
  .toolbar .sort-block.second { float: right; }
  .toolbar .sort-block .drop-down { width: 100%; min-width: 0; max-width: 100%; }
  .toolbar .sort-block.first .drop-down { width: calc(100% - 30px); margin-right: 0; }
  .toolbar .sorting-arrow { margin-left: 0; }
  .pager { width: 100%; text-align: center; }
  .pager .pages { display: table; margin: auto; }
  .breadcrumbs ul li.product { display: none; }
  .gallery-box ul.slick-dots li { margin: 0px 4px; }
  .gallery-box ul.slick-dots li button { padding-left: 12px; padding-right: 12px; }
  .status-row .compare-link { width: auto; margin-top: 0; font-size: 14px; line-height: 22px; max-width: none; }
  .product-options .price-box .old-price { font-size: 20px; line-height: 30px; }
  .product-options .price-box .special-price { font-size: 22px; line-height: 30px; }
  .product-options .price-box .regular-price { font-size: 22px; line-height: 30px; }
  .tabs .tabs-nav ul li.tab-nav-item-wrapper { width: 100%; }
  .tabs .tabs-nav ul li.tab-nav-item-wrapper a.tab-nav-item { width: 100%; background: #f0f0f0; }
  .tabs .tabs-nav ul li.tab-nav-item-wrapper a.tab-nav-item.selected { background: #222; }
  .tabs .tabs-content .tab .col-xs-6 { width: 100%; }
  .products-list.last-seen .col-xs-2 { padding-left: 5px; padding-right: 5px; }
  .products-list.last-seen .col-xs-2 .product-box { padding: 0px; margin: 0px; }
  .inquiry-popup-form { width: 350px; padding-left: 10px; padding-right: 10px; }
  .error-signal { display: inline-block; margin-top: 5px; }
  #errorsignal-form { padding: 20px; }
  #errorsignal-form .form-title { margin-bottom: 20px; font-size: 24px; line-height: 28px; }
  .we-recommend-categories { margin-bottom: 30px; }
  .we-recommend-categories strong { text-align: center; }
  .we-recommend-categories ul li { width: 100%; margin: 0 0 10px; }
  .we-recommend-categories ul li a { width: 100%; line-height: 38px; text-align: center; }
  .shopping-cart-items .cell.col1 { width: 100%; margin-bottom: 10px; padding-bottom: 8px; }
  .shopping-cart-items .cell.col3 { width: 45%; }
  .shopping-cart-items .cell.col3 .spinner-box { float: left; margin: 0px; }
  .shopping-cart-items .cell.col4 { width: 40%; }
  .shopping-cart-items .cart-row.header-row .cell.col1 { width: 100%; text-align: center; padding: 0px 0px 3px 0px; margin-bottom: 0px; }
  .shopping-cart-items .header-row .col2 { display: none; }
  .shopping-cart-items .header-row .col3 { display: none; }
  .shopping-cart-items .header-row .col4 { display: none; }
  .shopping-cart-items .header-row .col5 { display: none; }
  .shopping-cart-items .cart-img-wrapper { width: 50px; }
  .shopping-cart-items .cart-info { min-height: 40px; padding-left: 57px; }
  .shopping-cart-items .cart-info .item-title { font-size: 12px; line-height: 14px; margin: 0px 0px 5px 0px; }
  .shopping-cart-items .cart-info .attributes { font-size: 11px; }
  blockquote { padding-left: 45px; }
  blockquote:before { left: 30px; }
  .account-create { width: 100%; }
  .account-create .social-login .facebook-login { margin-right: 10px; }
  .registration-banner { display: none; }
  .contacts-info { width: 100%; margin-bottom: 10px; padding-right: 0; }
  .contacts-info h4 { font-size: 20px; }
  .contacts-form { width: 100%; padding-left: 0; }
  .contacts-form h4 { font-size: 20px; }
  .center-form-action .button { width: 100%; }
  .center-form-action .button .icon-svg { float: none; display: inline-block; vertical-align: middle; margin: -3px 10px 0 0; }
  .gmap-content #map_container { height: 300px; }
  .news-box.listing-style .image-wrapper { width: 100%; }
  .news-box.listing-style .news-listing-info { width: 100%; padding: 10px; }
  .catalog-product-compare-index .more-items-for-compare { margin: 0px auto 0px auto; float: none; display: table; clear: both; }
  table.compare-table th { padding: 5px; font-size: 12px; }
  table.compare-table td { padding: 5px; }
  table.compare-table td .item-remove { right: 5px; top: 5px; }
  table.compare-table td .view-more { font-size: 12px; }
  .customer-form { width: 100%; }
  .reviews-listing { width: 100%; }
  footer { padding-top: 16px; }
  footer .payments { margin-bottom: 20px; }
  footer .info-cols { margin-bottom: 0; }
  footer .copy-rights .row { border-top: none; }
  .footer-col { width: 100%; padding-bottom: 6px; margin-bottom: 10px; border-bottom: 1px solid #474747; }
  .footer-col.open-with-click-on-responsive { margin-bottom: 15px; }
  .footer-col.open-with-click-on-responsive p.title { cursor: pointer; }
  .footer-col.open-with-click-on-responsive p.title:before { content: ''; width: 13px; height: 2px; background: #222; transform: rotate(45deg); position: absolute; right: 28px; top: 11px; z-index: 20; }
  .footer-col.open-with-click-on-responsive p.title:after { content: ''; width: 2px; height: 13px; background: #222; transform: rotate(45deg); position: absolute; right: 25px; top: 5px; z-index: 20; }
  .footer-col.open-with-click-on-responsive.opened p.title { color: #ce181e; }
  .footer-col.open-with-click-on-responsive.opened p.title:before { transform: rotate(-45deg); background: #ce181e; }
  .footer-col.open-with-click-on-responsive.opened p.title:after { transform: rotate(-45deg); background: #ce181e; }
  .footer-col.open-with-click-on-responsive ul { display: none; }
  .footer-col.contacts-col { text-align: center; }
  .footer-col .title { margin-bottom: 7px; }
  .footer-col .mobile-link { display: block; cursor: pointer; color: #222; font-size: 18px; margin: 15px 0; }
  .footer-col ul li { margin-bottom: 5px; }
  .footer-col p img { margin: 0 10px !important; }
  .footer-col .newsletter-form { margin: 0px 0px 10px 0px; }
  .footer-col .newsletter-form input.input-newsletter { padding: 0px 50px 0px 10px; font-size: 12px; }
  .footer-col .newsletter-form button.newsletter-button { width: 45px; }
  .payments-box { margin-top: 10px; padding: 0px; border: none; text-align: center; }
  .payments-box .col-xs-5 { width: 100%; margin-bottom: 10px; }
  .payments-box .col-xs-7 { width: 100%; }
  .payments-box p.right { float: none; }
  .payments-box p img { max-height: 40px; }
  .checkout-onepage-index h1 { margin-bottom: 5px; font-size: 30px; line-height: 30px; text-align: center; }
  .opc .section { margin-bottom: 10px; }
  .opc .section .step-title h2 { padding: 10px 0px 10px 0px; font-size: 20px; line-height: 20px; }
  .opc .section .step-title a { margin: 15px 10px 0px 0px; }
  .opc .section .step-title .number { margin-right: 10px; padding: 10px 0px 10px 0px; }
  .data-table { margin-left: 0px; }
  .data-table th { padding: 10px 5px 10px 5px; font-size: 16px; }
  .data-table td { padding: 10px 5px 10px 5px; }
  .data-table td .cart-img-wrapper { display: none; }
  .data-table td .item-title { padding-left: 5px; }
  .data-table td dl.item-options { padding-left: 5px; }
  .opc .step #review-buttons-container .button { width: 100%; }
  .opc .step #checkout-review-submit .buttons-set .please-wait { margin-right: -20px; right: 50%; top: -16px; }
  .opc .step #checkout-agreements .order-comment label { line-height: 14px; }
  .my-account .box-head h2 { font-size: 30px; line-height: 30px; }
  .my-account .box-head a { margin-top: 26px; }
  .my-account .box-title a { font-size: 12px; line-height: 14px; margin-bottom: 10px; }
  .my-account .data-table th { padding: 5px 3px; font-size: 15px; }
  .my-account .data-table td { padding: 5px 3px; font-size: 11px; line-height: 13px; }
  .my-account #wishlist-table.data-table td .product-image { width: 50px; height: 50px; }
  .my-account #wishlist-table.data-table td .description { display: none; }
  .my-account #wishlist-table.data-table td .price-box .old-price { font-size: 13px; line-height: 15px; }
  .my-account #wishlist-table.data-table td .price-box .special-price { font-size: 14px; line-height: 16px; }
  .my-account #wishlist-table.data-table td .price-box .regular-price { font-size: 14px; line-height: 16px; }
  .my-account #wishlist-table.data-table td textarea { padding: 5px; }
  .shops-inner-wrapper .col-xs-6.shop-info { width: 100%; }
  .shops-inner-wrapper .col-xs-6.shops-listing-google-map { width: 100%; }
  .shops-inner-wrapper .shops-listing-google-map #gmap { height: 300px; }
  .terms-popup { width: 330px; }
  .unicredit-leasing-calculator .downpayment-content input.input-text.downpayment { width: 100%; height: 38px; margin-bottom: 10px; }
  .unicredit-leasing-calculator .downpayment-content .button.recalc { width: 100%; }
  #opc-payment .unicredit-leasing-calculator .horizontal-scroll-wrapper { width: 280px; }
  .stenik-checkout-top-login { text-align: center; margin-bottom: 0px; }
  .stenik-checkout-top-login p { width: 100%; height: auto; margin: 0px 0px 10px 0px; text-align: center; line-height: 18px; }
  .stenik-checkout-top-login .social-login { width: 100%; height: auto; margin: 0px 0px 5px 0px; text-align: center; }
  .stenik-checkout-top-login .social-login .facebook-login { float: none; display: inline-table; width: 100%; margin-bottom: 10px; }
  .stenik-checkout-top-login .social-login .gplus-login { float: none; display: inline-table; width: 100%; }
  .stenik-checkout-top-login .button { float: none; display: inline-table; }
  .stenik-checkout .step-title { font-size: 15px; }
  .delivery-to-wrapper { padding: 15px 20px; }
  .stenik-checkout .fields .field { padding-left: 10px; padding-right: 10px; }
  .stenik-checkout .fields.wide { padding-left: 10px; padding-right: 10px; }
  .stenik-checkout .fields.speedyOfficeFields,
  .stenik-checkout .fields.speedyNonOfficeFields { padding: 0; }
  .stenik-checkout .fields.speedyNonOfficeFields .separator { width: 100%; }
  .stenik-checkout .buttons-set { padding-left: 10px; padding-right: 10px; margin-bottom: 20px; }
  .stenik-checkout .buttons-set button.button { width: 100%; padding: 15px 10px; }
  .paypal-express-review #details-reload .paypal-total { width: 100%; }
  .paypal-express-review .comment-box { width: 100%; }
  .paypal-express-review ol.checkout-agreements { float: left; width: 100%; }
  .paypal-express-review .paypal-review-placeorderbtn-wrapper { float: left; width: 100%; }
  .checkout-popup-login { width: 350px; height: 320px; padding: 10px; }
  .checkout-popup-login h4 { margin-top: 0px; }
  #stenik-ajaxaddtocart-result { overflow-y: scroll; }
  .stenik-ajaxAddToCart-result { width: 88%; left: 2%; top: 2%; margin: 0px; }
  .stenik-ajaxAddToCart-result .close-popup { width: 30px; height: 30px; right: -30px;  }
  .stenik-ajaxAddToCart-result .success-message { padding-right: 20px; font-size: 14px; line-height: 17px; }
  .stenik-ajaxAddToCart-result .ajax-cart-item { width: 100%; padding: 10px; border-bottom: 1px solid #f1eff0; }
  .stenik-ajaxAddToCart-result .ajax-cart-item:after { display: none; }
  .stenik-ajaxAddToCart-result .ajax-cart-item .cart-img-wrapper { width: 80px; height: 80px; }
  .stenik-ajaxAddToCart-result .ajax-cart-item .item-info { width: 220px; }
  .stenik-ajaxAddToCart-result .ajax-cart-total { float: left; width: 100%; padding: 10px 10px 15px 10px; }
  .stenik-ajaxAddToCart-result .crosssale-products .product-box { height: 245px; }
  .stenik-gdprcompliance-customer-dashboard .my-account .box-content .button { width: 100%; margin-bottom: 10px; }
  .stenik-gdprcompliance-customer-review .buttons-set .button.accept { width: 100%; margin-right: 0px; margin-bottom: 10px; }
  .stenik-gdprcompliance-customer-review .buttons-set .button.later { width: 100%; margin-bottom: 10px; }
  .stenik-gdprcompliance-customer-review .buttons-set .link-remove.right { float: left; }

}

@media only screen and (max-width: 380px) {

  .responsive-header .responsive-cart { right: 0; }
  .responsive-header .responsive-search-wrapper .open-responsive-search { right: 48px; }
  .cms-index-index .row-title { font-size: 15px; line-height: 20px;}
  .row-title { display: block; width: 100%; text-align: center; }
  .cms-index-index .brands-listing .row-title { width: 100%; text-align: center; margin: -20px 0px 30px 0px; font-size: 17px; }
  .cms-index-index .brands-listing .button.right { float: left; margin-left: 28%; margin-bottom: 10px; }
  .widgets.three-in-row .row { margin-left: 0px; margin-right: 0px; }
  .widgets.three-in-row .row .col-xs-4 { width: 100%; padding: 0px; }
  .widgets.three-in-row .widget-box { margin-bottom: 10px; }
  .widgets.three-in-row .widget-box .widget-info .title { font-size: 24px; line-height: 30px; margin-bottom: 10px; }
  .widgets.three-in-row .widget-box .widget-info .sub-title { display: block; margin-bottom: 10px; }
  .widgets.three-in-row .widget-box .button { display: block; margin-bottom: 10px; }
  .homepage-top-area .col-xs-6 { width: 100%; }
  .car-widget .widget-info { padding: 10px; }
  .car-widget .widget-info .title { font-size: 12px; }
  .product-box { height: 310px; }
  .brand-item { height: 73px; }
  .service-box .image-wrapper { width: 100px; height: 100px; }
  .service-box .image-wrapper .icon-svg.returns { margin: 28px 0px 0px 28px; }
  .service-box .image-wrapper .icon-svg.delivery { margin: 33px 0 0 23px; }
  .service-box .image-wrapper .icon-svg.credits { margin: 27px 0px 0px 30px; }
  .service-box .title { font-size: 14px; line-height: 20px; }
  .toolbar .view-mode { margin-top: 10px; }
  .toolbar .drop-down .open-item { font-size: 12px; }
  .filters-main-content .drop-down { margin-right: 10px; }
  .list-mode .product-box .price-actions-col .price-box { width: 158px; }
  .list-mode .product-box .price-actions-col .rating-box { margin: 0px 0px 0px 0px; }
  .list-mode .product-box .product-info { width: 100%; }
  .inquiry-popup-form { width: 290px; }
  .inquiry-popup-form .form-title { font-size: 18px; line-height: 22px; }
  .inquiry-popup-form .g-recaptcha { margin-left: -10px; }
  .customer-account-create .account-create .recaptcha { float: left; margin-left: -14px; }
  .customer-form .google-captcha-box .recaptcha { margin-left: -21px; }
  .social-login .facebook-login { min-width: 130px; }
  .social-login .gplus-login { min-width: 130px; }
  .checkbox-content label { font-size: 12px; max-width: 85%; }
  .customer-form .button { width: 100%; }
  .opc .section .step-title .number { width: 38px; margin-right: 5px; font-size: 20px; line-height: 20px; }
  .opc .section .step-title h2 { font-size: 16px; line-height: 20px; }
  .opc .section .step-title a { font-size: 12px; line-height: 12px; margin: 17px 5px 0px 0px; }
  .opc .step h3 { font-size: 16px; line-height: 20px; }
  .opc #checkout-step-login.step a.forgotpassword { font-size: 11px; line-height: 13px; }
  .opc .step ul.form-list ul li.fields { width: 100%; margin: 0px; }
  .opc .step #checkout-review-table-wrapper table.total-table { width: 100%; }
  .opc .step #checkout-agreements ol.checkout-agreements { width: 100%; }
  .opc .step #checkout-agreements .order-comment { position: relative; top: auto; left: auto; width: 100%; }
  .paypal-express-review .order-comment { margin: 0px; width: 100%; }
  .opc .step .data-table { margin-left: -6%; width: 109%; }
  .opc .step #checkout-review-submit .buttons-set .button { min-width: 262px; }
  .my-account .data-table th { padding: 5px 1px; font-size: 14px; }
  .my-account .data-table td { padding: 5px 1px; font-size: 10px; line-height: 12px; }
  .my-account ul.form-list li.fields { width: 100%; margin: 0px; }
  .my-account .data-table td h3 { font-size: 11px; }
  .my-account .my-wishlist .buttons-set .button.btn-share { float: none; width: 100%; display: block; margin: 0px 0px 10px 0px; }
  .my-account .my-wishlist .buttons-set .button.btn-add { float: none; width: 100%; display: block; margin: 0px 0px 10px 0px; }
  .my-account .my-wishlist .buttons-set .button.btn-update { float: none; width: 100%; display: block; margin: 0px 0px 10px 0px; }
  .terms-popup { width: 300px; }
  #cboxContent #cboxClose { right: -10px; top: -10px; }
  #opc-payment .unicredit-leasing-calculator .horizontal-scroll-wrapper { margin-left: -30px; }
  .checkout-onepage-index h1 { font-size: 21px; line-height: 25px; }
  .stenik-checkout .fields .field { width: 100%; }
  .stenik-checkout .checkout-review-box .review-items-box { padding-left: 10px; padding-right: 10px; }
  .stenik-checkout ol.checkout-agreements label { width: 236px; }
  .stenik-checkout li.fields.wide.create-account-checkbox label { width: 240px; }
  .paypal-express-review ol.checkout-agreements label { width: 250px; }
  .checkout-popup-login { width: 280px; padding: 5px; }
  .checkout-popup-login .forgotpassword { font-size: 12px; }
  .deleting-account-info { width: 100%; }
  .social-login-checkout .social-login .facebook-login { padding-left: 6px; padding-right: 6px; }
  .social-login-checkout .social-login .gplus-login { padding-left: 6px; padding-right: 6px; }
  .stenik-ajaxAddToCart-result .ajax-cart-item .cart-img-wrapper { width: 60px; height: 60px; }
  .stenik-ajaxAddToCart-result .ajax-cart-item .item-info { width: 185px; }

}

#fb-carco {
	position: fixed;
	bottom: 20px;
	right: 20px;
	z-index: 99999;
}

@media only screen and (min-width: 768px) { .sidebar .sidebar-responsive-wrapper { display: block !important; } }

.stretched-link::after {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1;
	pointer-events: auto;
	content: "";
	background-color: rgba(0,0,0,0);
}

.categories-wrapper {
	margin: 10px 0 10px 0;
}
.accordeon-wrapper {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
}
.accordeon {
	display: inline-block;
	background: #F1F1F1;
	width: 20%;
	cursor: pointer;
	font-weight: 500;
	color: #CE191F;
	position: relative;
	margin: 0 0 20px 0;
}
.accordeon-link {
	display: inline-block;
	width: 80%;
	background: #F1F1F1;
	margin: 0 0 20px 0;
	cursor: pointer;
	font-size: 14px;
	line-height: 16px;
	font-weight: 500;
	color: #CE191F;
	position: relative;
	padding: 15px 45px 15px 10px;
}
.accordion {
	display: block;
	margin-bottom: 2px;
	cursor: pointer;
	position: relative;
	padding-right: 45px;
}
.accordion + .home-accordion-panel {
	display: block;
	opacity: 0;
	height: 0;
}
.accordion + .home-accordion-panel.open {
	opacity: 1;
	height: auto;
}
.accordeon:before { content: ''; width: 13px; height: 2px; background: #ce181e; transform: rotate(45deg); position: absolute; right: 28px; top: 22px; z-index: 20; }
.accordeon:after { content: ''; width: 2px; height: 13px; background: #ce181e; transform: rotate(45deg); position: absolute; right: 25px; top: 16px; z-index: 20; }
.accordeon.open { color: #222; }
.accordeon.open:before { transform: rotate(-45deg); background: #222; }
.accordeon.open:after { transform: rotate(-45deg); background: #222; }
.accordion:before { content: ''; width: 13px; height: 2px; background: #222; transform: rotate(45deg); position: absolute; right: 28px; top: 11px; z-index: 20; }
.accordion:after { content: ''; width: 2px; height: 13px; background: #222; transform: rotate(45deg); position: absolute; right: 25px; top: 5px; z-index: 20; }
.accordion.open { color: #ce181e; }
.accordion.open:before { transform: rotate(-45deg); background: #ce181e; }
.accordion.open:after { transform: rotate(-45deg); background: #ce181e; }

.accordeon-text {
	display: block;
	padding: 5px 15px;
	background: white;
}
.before-footer-categories {
	max-width: 500px;
	margin: auto;
}

.category-child {
	display: block;
	font-size: 14px;
	line-height: 14px;
	transition: opacity 0.3s ease-out;
	opacity: 0;
	height: 0;
	overflow: hidden;
	width: 100%;
}
.category-child.opened-child {
	padding: 5px 15px;
	opacity: 1;
	height: auto;
	transition: opacity 0.3s ease-out;
}

.screenreader {
	border:0;
	clip: rect(1px, 1px, 1px, 1px);
	clip-path: inset(50%);
	height: 1px;
	margin: -1px;
	width: 1px;
	overflow: hidden;
	position: absolute !important;
	word-wrap:normal !important;
}
.breadcrumbs-home {
	vertical-align: bottom;
}

.text-center {
	text-align: center;
}

.clearfix {
	clear: both;
}
/* Blog styling */
.newsListing {
    width: 100%;
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
}

.newsBox {
	width: 33%;
    box-sizing: border-box;
	height: 300px;
	margin: 0;
	background: #FFF;
	overflow: hidden;
	border-radius: 10px;
}

@media only screen and (min-width: 0px) and (max-width: 600px) {
	.newsBox {
		width: 50%;
	}
}
.newsImg {
	overflow: hidden;
	text-align: center;
	display: block;
	float: left;
	width: 100%;
}
.newsImg img {
	width: auto;
	margin: 0 auto;
	text-align: center;
	max-height: 164px;
	-webkit-transition: all 0.2s linear;
	-moz-transition: all 0.2s linear;
	-o-transition: all 0.2s linear;
	transition: all 0.2s linear;
	display: block;
	overflow: hidden;
}
.newsImg:hover { opacity: 0.7; }
.newsBoxInfo { float: left; width: 100%; padding: 20px; box-sizing: border-box; }
.newsBoxTitle {
	float: left;
	width: 100%;
	margin-bottom: 15px;
	font-size: 16px;
	font-weight: 500;
	text-transform: uppercase;
	text-decoration: none;
	color: #ce181e;
	overflow: hidden;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.newsBoxTitle:hover { color: #ce181e; }
.newsBoxText  { float: left; width: 100%; max-height: 50px; margin-bottom: 10px; font-size: 14px; line-height: 16px; overflow: hidden;text-align:left; }
.readMoreLink {
	float: left;
	width: 100%;
	font-size: 14px;
	color: #ce181e;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.readMoreLink:hover { color: #ce181e; }
.newsContainer { float: left; width: 100%; padding: 20px; background: #FFF; border-radius: 0 0 10px 10px; box-sizing: border-box; }
.newsHeader { float: left; width: 100%; }

#fast-order-modal {
	padding: 20px;
}

#fast-order-modal .fast-order-button-wrapper {
	text-align: center;
	padding-bottom: 10px;
	padding-top: 10px;
}

#fast-order-modal .fast-order-button-wrapper button {
	float: none;
}

@media only screen and (max-width: 1220px) {
	#fast-order-opener {
		margin-top: 10px;
	}
}

@media only screen and (min-width: 1220px) {
	#fast-order-opener {
		margin-left: 20px;
		margin-top: 10px;
	}
}

.wide-area.top-banner {
	position: relative;
    width: 100%;
    padding-top: 32.5%;
}

.wide-area.top-banner img {
	position: absolute;
    left: 0;
    top: 0;
    width: 100%;
}

.clever-link {
	cursor: pointer;
}

.filter-wrapper {
	border-top: 4px solid #ce181e;
	margin-bottom: 20px;
}

.filter-wrapper .search-box {
	height: auto;
}

.search-fields {
	align-items: center;
	justify-content: center;
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 20px;
}

.search-fields .search-field-box {
	margin: 0 1% 25px 1%;
	width: calc(25% - 2%);
	max-width: 30%;
	align-items: center;
	justify-content: center;
	min-width: 200px;
    padding-top: 10px;
}
.search-fields .search-field-label {
	margin-bottom: 10px;
	line-height: 15px;
	padding-left: 8px;
	min-height: 30px;
}

.drop-down.zeron_wheel_d .sub-options li,
.drop-down.zeron_tire_h .sub-options li,
.drop-down.zeron_tire_w .sub-options li,
.drop-down.zeron_wheel_w .sub-options li,
.drop-down.zeron_wheel_bolt_d .sub-options li,
.drop-down.zeron_wheel_bolt_number .sub-options li
{
	width: 50%;
}

.hidden-sealable, .hidden-not-sealable {
	display: none;
}

.mini-cart {
	width: 123px;
}

.search-lazy {
	width: 250px;
	float: right;
}

.wide-area .right {
	width: 490px;
}

.login-lazy {
	width: 100px;
	float: right;
}
