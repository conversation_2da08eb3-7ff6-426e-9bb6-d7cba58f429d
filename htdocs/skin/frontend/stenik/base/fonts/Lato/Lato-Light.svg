<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20090914 at Fri Apr 21 08:34:04 2017
 By www-data
Copyright (c) 2010-2011 by tyPoland Lukasz Dziedzic with Reserved Font Name "Lato". Licensed under the SIL Open Font License, Version 1.1. 
</metadata>
<defs>
<font id="Lato-Light" horiz-adv-x="1160" >
  <font-face 
    font-family="Lato Light"
    font-weight="300"
    font-stretch="normal"
    units-per-em="2000"
    panose-1="2 15 3 2 2 2 4 3 2 3"
    ascent="1610"
    descent="-390"
    x-height="994.999"
    cap-height="1415"
    bbox="-201.999 -372 2170 1789"
    underline-thickness="68"
    underline-position="-132"
    unicode-range="U+000D-U+FB02"
  />
<missing-glyph horiz-adv-x="1025" 
d="M262 1134c14 13.333 29.4961 26.0039 46.4961 38.0039s35.5 22.667 55.5 32s41.833 16.833 65.5 22.5s49.5 8.5 77.5 8.5c36 0 69.5 -5.16699 100.5 -15.5s58 -25.333 81 -45s41 -43.334 54 -71.001s19.5 -58.834 19.5 -93.501c0 -36.667 -5.5 -68.167 -16.5 -94.5
s-24.667 -49.333 -41 -69s-34.166 -37 -53.499 -52l-54.5 -42.5c-17 -13.333 -31.667 -26.5 -44 -39.5s-19.166 -27.833 -20.499 -44.5l-10 -114h-65l-6 122c-1.33301 20.667 3.33398 38.834 14.001 54.501s24.167 30.5 40.5 44.5s34.333 28 54 42
s37.834 29.833 54.501 47.5s30.667 37.667 42 60s17 49.166 17 80.499c0 24 -4.66699 45.333 -14 64s-21.833 34.5 -37.5 47.5s-34 23 -55 30s-43.167 10.5 -66.5 10.5c-30.667 0 -57 -3.83301 -79 -11.5s-40.667 -16.334 -56 -26.001s-27.5 -18.334 -36.5 -26.001
s-15.833 -11.5 -20.5 -11.5c-9.33301 0 -16.333 3.66699 -21 11zM409.996 265.004c0 21.333 7.33301 39.666 22 54.999s33 23 55 23c21.333 0 39.333 -7.66699 54 -23s22 -33.666 22 -54.999s-7.33301 -39.666 -22 -54.999s-32.667 -23 -54 -23
c-22 0 -40.333 7.66699 -55 23s-22 33.666 -22 54.999zM46.9961 1415h931v-1415h-931v1415zM81.9961 36.0039h859v1343h-859v-1343z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="1066" 
d="M199 0l0.000976562 891.003l-140 9c-19.333 1.33301 -29 9.66602 -29 24.999v39h169v85c0 61.333 9 116.5 27 165.5s44.167 90.667 78.5 125s76.166 60.666 125.499 78.999s105.666 27.5 168.999 27.5c22 0 44.833 -1.83301 68.5 -5.5s43.5 -8.83398 59.5 -15.501l-4 -48
c-0.666992 -6.66699 -5.33398 -10 -14.001 -10c-7.33301 0 -18.166 1.16699 -32.499 3.5s-32.5 3.5 -54.5 3.5c-105.333 0 -186.666 -27 -243.999 -81s-86 -136.333 -86 -247v-82h583v-964h-96v893h-485v-893h-95z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="1132" 
d="M199 0l0.000976562 891.001l-140 9c-19.333 1.33301 -29 9.66602 -29 24.999v39h169v65c0 58.667 8.66699 113.167 26 163.5s42.666 93.833 75.999 130.5s74.833 65.334 124.5 86.001s106.5 31 170.5 31c22.667 0 46.167 -0.666992 70.5 -2s48 -3 71 -5
s44.5 -3.66699 64.5 -5s37 -2 51 -2h72v-1426h-95v1359c-36 1.33301 -73.833 3.33301 -113.5 6s-75.5 4 -107.5 4c-50 0 -94.667 -7.83301 -134 -23.5s-72.5 -38.334 -99.5 -68.001s-47.667 -65.5 -62 -107.5s-21.5 -89 -21.5 -141v-65h288v-71h-286v-893h-95z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1025" 
d="M262 1134c14 13.333 29.4961 26.0039 46.4961 38.0039s35.5 22.667 55.5 32s41.833 16.833 65.5 22.5s49.5 8.5 77.5 8.5c36 0 69.5 -5.16699 100.5 -15.5s58 -25.333 81 -45s41 -43.334 54 -71.001s19.5 -58.834 19.5 -93.501c0 -36.667 -5.5 -68.167 -16.5 -94.5
s-24.667 -49.333 -41 -69s-34.166 -37 -53.499 -52l-54.5 -42.5c-17 -13.333 -31.667 -26.5 -44 -39.5s-19.166 -27.833 -20.499 -44.5l-10 -114h-65l-6 122c-1.33301 20.667 3.33398 38.834 14.001 54.501s24.167 30.5 40.5 44.5s34.333 28 54 42
s37.834 29.833 54.501 47.5s30.667 37.667 42 60s17 49.166 17 80.499c0 24 -4.66699 45.333 -14 64s-21.833 34.5 -37.5 47.5s-34 23 -55 30s-43.167 10.5 -66.5 10.5c-30.667 0 -57 -3.83301 -79 -11.5s-40.667 -16.334 -56 -26.001s-27.5 -18.334 -36.5 -26.001
s-15.833 -11.5 -20.5 -11.5c-9.33301 0 -16.333 3.66699 -21 11zM409.996 265.004c0 21.333 7.33301 39.666 22 54.999s33 23 55 23c21.333 0 39.333 -7.66699 54 -23s22 -33.666 22 -54.999s-7.33301 -39.666 -22 -54.999s-32.667 -23 -54 -23
c-22 0 -40.333 7.66699 -55 23s-22 33.666 -22 54.999zM46.9961 1415h931v-1415h-931v1415zM81.9961 36.0039h859v1343h-859v-1343z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="0" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="386" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="645" 
d="M372 1415v-572c0 -30 -0.333008 -58.333 -1 -85s-1.5 -53.834 -2.5 -81.501s-2.5 -56.834 -4.5 -87.501s-4 -64.667 -6 -102h-65c-2 37.333 -4 71.333 -6 102s-3.5 59.834 -4.5 87.501s-1.83301 54.834 -2.5 81.501s-1 55 -1 85v572h93zM230 77
c0 12.667 2.33301 24.667 7 36s11.167 21.166 19.5 29.499s18 15 29 20s23.167 7.5 36.5 7.5c12.667 0 24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20s15 -18.166 20 -29.499s7.5 -23.333 7.5 -36c0 -13.333 -2.5 -25.5 -7.5 -36.5s-11.667 -20.667 -20 -29
s-18.166 -14.833 -29.499 -19.5s-23.333 -7 -36 -7c-26 0 -47.833 8.83301 -65.5 26.5s-26.5 39.5 -26.5 65.5z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="734" 
d="M263 1415l-0.00195312 -292l-9 -153c-1.33301 -11.333 -4.33301 -20.166 -9 -26.499s-12.667 -9.5 -24 -9.5c-9.33301 0 -16.666 3.16699 -21.999 9.5s-8.66602 15.166 -9.99902 26.499l-9 153v292h83zM552.998 1415l-0.00195312 -292l-9 -153
c-1.33301 -11.333 -4.33301 -20.166 -9 -26.499s-12.667 -9.5 -24 -9.5c-9.33301 0 -16.666 3.16699 -21.999 9.5s-8.66602 15.166 -9.99902 26.499l-9 153v292h83z" />
    <glyph glyph-name="numbersign" unicode="#" 
d="M757 443l-94.001 -443.001h-41c-9.33301 0 -17 2.83301 -23 8.5s-9 13.834 -9 24.501c0 4.66699 0.333008 8.33398 1 11.001l86 399h-298l-86 -403c-3.33301 -14.667 -9 -25 -17 -31s-17.667 -9 -29 -9h-42l94 443h-183c-9.33301 0 -16.666 2.16699 -21.999 6.5
s-8 12.166 -8 23.499v9.5c0 3 0.666992 7.83301 2 14.5l3 16h218l82 389h-253l6 38c2.66699 22 17.334 33 44.001 33h213l87 404c2.66699 12.667 8.16699 22.167 16.5 28.5s18.166 9.5 29.499 9.5h42l-95 -442h298l95 442h42c20.667 0 31 -10 31 -30
c0 -5.33301 -0.333008 -9.33301 -1 -12l-87 -400h225l-6 -38c-3.33301 -22 -18.333 -33 -45 -33h-184l-82 -389h225c20 0 30 -8.66699 30 -26l-4 -44h-261zM388.999 512.999h298l82 389h-298z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M542 -15c-85.333 4 -159.663 22.332 -222.996 54.999s-119 75.667 -167 129l27 39c8 10 17.333 15 28 15c6 0 13.5 -3.66699 22.5 -11l32.5 -27.5c12.667 -11 27.667 -23 45 -36s37.5 -25.333 60.5 -37s49.167 -21.834 78.5 -30.501s62.666 -14 99.999 -16l32 636
c-45.333 14 -89.5 29.667 -132.5 47s-81.333 39.5 -115 66.5s-60.834 60.333 -81.501 100s-31 89.5 -31 149.5c0 46 8.66699 90.5 26 133.5s42.833 81.333 76.5 115s75.167 60.834 124.5 81.501s105.666 32 168.999 34l9 168c0.666992 9.33301 3.5 17 8.5 23
s12.167 9 21.5 9h36l-11 -201c67.333 -4.66699 126.333 -19.5 177 -44.5s97 -58.5 139 -100.5l-22 -34c-6.66699 -11.333 -15.334 -17 -26.001 -17c-8 0 -18.5 5.5 -31.5 16.5s-30 23.167 -51 36.5s-47 26.166 -78 38.499s-68.167 20.166 -111.5 23.499l-29 -572l94.5 -31
c31.667 -10.667 62 -22.667 91 -36s55.833 -28.666 80.5 -45.999s46.167 -37.833 64.5 -61.5s32.666 -50.667 42.999 -81s15.5 -65.5 15.5 -105.5c0 -57.333 -9.66699 -111.333 -29 -162s-47.166 -95.167 -83.499 -133.5s-81 -69.166 -134 -92.499
s-113.5 -36.666 -181.5 -39.999l-11 -211c-0.666992 -8.66699 -3.5 -16 -8.5 -22s-11.833 -9 -20.5 -9h-36zM944.003 397.999c0 44 -8.33301 81.335 -25 112.002s-39 56.667 -67 78s-60.167 39.166 -96.5 53.499s-74.5 27.833 -114.5 40.5l-31 -617
c53.333 3.33301 100.833 14.166 142.5 32.499s76.667 42.333 105 72s49.833 64 64.5 103s22 80.833 22 125.5zM307.003 1072c0 -41.333 7.33398 -76.667 22.001 -106s34.667 -54.5 60 -75.5s54.5 -39 87.5 -54s68.167 -28.833 105.5 -41.5l28 552
c-50.667 -2.66699 -94.834 -12 -132.501 -28s-69.167 -36.5 -94.5 -61.5s-44.333 -53.5 -57 -85.5s-19 -65.333 -19 -100z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1534" 
d="M682 1075c0 -58 -8 -109.167 -24 -153.5s-37.667 -81.166 -65 -110.499s-59 -51.5 -95 -66.5s-74 -22.5 -114 -22.5c-41.333 0 -80.166 7.5 -116.499 22.5s-67.833 37.167 -94.5 66.5s-47.667 66.166 -63 110.499s-23 95.5 -23 153.5
c0 58.667 7.66699 110.334 23 155.001s36.333 81.834 63 111.501s58.167 52 94.5 67s75.166 22.5 116.499 22.5s80 -7.5 116 -22.5s67.5 -37.333 94.5 -67s48.333 -66.834 64 -111.501s23.5 -96.334 23.5 -155.001zM606 1075c0 51.333 -5.83301 95.333 -17.5 132
s-27.667 66.834 -48 90.501s-44 41 -71 52s-55.5 16.5 -85.5 16.5s-58.333 -5.5 -85 -16.5s-50 -28.333 -70 -52s-35.833 -53.834 -47.5 -90.501s-17.5 -80.667 -17.5 -132s5.83301 -95.166 17.5 -131.499s27.5 -66.166 47.5 -89.499s43.333 -40.5 70 -51.5
s55 -16.5 85 -16.5s58.5 5.5 85.5 16.5s50.667 28.167 71 51.5s36.333 53.166 48 89.499s17.5 80.166 17.5 131.499zM1219 1397c4 5.33301 8.83398 9.66699 14.501 13s13.167 5 22.5 5h67l-1016 -1397c-8.66699 -12 -20.334 -18 -35.001 -18h-68zM1446 336.001
c0 -58 -8 -109.167 -24 -153.5s-37.5 -81.166 -64.5 -110.499s-58.5 -51.5 -94.5 -66.5s-74 -22.5 -114 -22.5c-41.333 0 -80.166 7.5 -116.499 22.5s-67.833 37.167 -94.5 66.5s-47.667 66.166 -63 110.499s-23 95.5 -23 153.5c0 58.667 7.66699 110.334 23 155.001
s36.333 81.834 63 111.501s58.167 52 94.5 67s75.166 22.5 116.499 22.5s80 -7.5 116 -22.5s67.5 -37.333 94.5 -67s48.167 -66.834 63.5 -111.501s23 -96.334 23 -155.001zM1370 336.001c0 51.333 -5.83301 95.5 -17.5 132.5s-27.5 67.333 -47.5 91s-43.5 41 -70.5 52
s-55.5 16.5 -85.5 16.5s-58.5 -5.5 -85.5 -16.5s-50.5 -28.333 -70.5 -52s-35.833 -54 -47.5 -91s-17.5 -81.167 -17.5 -132.5s5.83301 -95.166 17.5 -131.499s27.5 -66.166 47.5 -89.499s43.5 -40.333 70.5 -51s55.5 -16 85.5 -16s58.5 5.33301 85.5 16
s50.5 27.667 70.5 51s35.833 53.166 47.5 89.499s17.5 80.166 17.5 131.499z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1383" 
d="M659 1431c44.667 0 86.9961 -7.66699 126.996 -23s74.833 -36 104.5 -62s53.334 -56.167 71.001 -90.5s27.167 -70.166 28.5 -107.499c-9.33301 -2 -17.666 -4 -24.999 -6c-15.333 -3.33301 -27.333 -5 -36 -5c-5.33301 0 -10.333 2 -15 6s-8 9.66699 -10 17
c-4.66699 17.333 -12.667 37.333 -24 60s-26.666 44 -45.999 64s-43.5 37 -72.5 51s-63.167 21 -102.5 21c-40 0 -76.333 -6.16699 -109 -18.5s-60.667 -29.666 -84 -51.999s-41.5 -48.833 -54.5 -79.5s-19.5 -64.334 -19.5 -101.001c0 -54.667 14 -107 42 -157
s71.333 -103.667 130 -161l446 -440c30 48 53.833 98 71.5 150s29.167 102.333 34.5 151c1.33301 8.66699 4 15.5 8 20.5s10 7.5 18 7.5h55c-1.33301 -62.667 -13.166 -127.167 -35.499 -193.5s-54.166 -129.833 -95.499 -190.5l298 -292h-89c-12 0 -21.833 1.33301 -29.5 4
s-16.5 8.66699 -26.5 18l-205 201c-30 -34.667 -63 -66.667 -99 -96s-75 -54.5 -117 -75.5s-86.833 -37.5 -134.5 -49.5s-97.834 -18 -150.501 -18c-50 0 -99.333 8.33301 -148 25s-92.167 41.167 -130.5 73.5s-69.333 72 -93 119s-35.5 100.5 -35.5 160.5
c0 48 8.66699 93.833 26 137.5s41 83.667 71 120s65.667 68.833 107 97.5s86 52 134 70c-47.333 53.333 -82.833 105.166 -106.5 155.499s-35.5 104.166 -35.5 161.499c0 46.667 8.5 89.834 25.5 129.501s41.167 74.167 72.5 103.5s68.833 52.333 112.5 69s92.5 25 146.5 25
zM202.996 368c0 -51.333 9.83691 -95.998 29.5039 -133.998s45.167 -69.833 76.5 -95.5s66.166 -44.834 104.499 -57.501s76.166 -19 113.499 -19c46.667 0 90.834 5.33301 132.501 16s80.667 25.5 117 44.5s69.666 41.5 99.999 67.5s58.166 54.667 83.499 86l-459 451
c-4 3.33301 -7.66699 7 -11 11c-44 -18.667 -83.833 -41 -119.5 -67s-65.834 -54.833 -90.501 -86.5s-43.667 -65.834 -57 -102.501s-20 -74.667 -20 -114z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="443" 
d="M263 1415l-0.00195312 -292l-9 -153c-1.33301 -11.333 -4.33301 -20.166 -9 -26.499s-12.667 -9.5 -24 -9.5c-9.33301 0 -16.666 3.16699 -21.999 9.5s-8.66602 15.166 -9.99902 26.499l-9 153v292h83z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="600" 
d="M253 630c0 -149.333 20.166 -292.502 60.499 -429.502s100.5 -269.167 180.5 -396.5c4.66699 -7.33301 7 -14.333 7 -21c0 -10 -4.66699 -17.333 -14 -22l-42 -26c-51.333 78.667 -94.5 154.834 -129.5 228.501s-63.167 147 -84.5 220s-36.833 146.167 -46.5 219.5
s-14.5 149 -14.5 227s4.83301 153.833 14.5 227.5s25.167 147 46.5 220s49.5 146.333 84.5 220s78.167 149.834 129.5 228.501l42 -26c8.66699 -4.66699 13 -11.667 13 -21c0 -6.66699 -2.33301 -13.667 -7 -21c-79.333 -128 -139.166 -260.5 -179.499 -397.5
s-60.5 -280.5 -60.5 -430.5z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="600" 
d="M346 630c0 150 -20.168 293.502 -60.501 430.502s-100.166 269.5 -179.499 397.5c-4.66699 7.33301 -7 14.333 -7 21c0 9.33301 4.33301 16.333 13 21l42 26c50.667 -78.667 93.667 -154.834 129 -228.501s63.666 -147 84.999 -220s36.833 -146.333 46.5 -220
s14.5 -149.5 14.5 -227.5s-4.83301 -153.667 -14.5 -227s-25.167 -146.5 -46.5 -219.5s-49.666 -146.333 -84.999 -220s-78.333 -149.834 -129 -228.501l-42 26c-9.33301 4.66699 -14 12 -14 22c0 6.66699 2.33301 13.667 7 21c80 127.333 140.167 259.5 180.5 396.5
s60.5 280.167 60.5 429.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="800" 
d="M372 897l-0.000976562 213c0 11.333 0.833008 21.333 2.5 30s5.16699 18.667 10.5 30c-7.33301 -10 -14.5 -17.667 -21.5 -23s-15.5 -11 -25.5 -17l-188 -109l-25 44l188 110c9.33301 5.33301 18.333 9.33301 27 12s19 5 31 7c-12.667 2 -23.167 4.66699 -31.5 8
s-17.166 7.66602 -26.499 12.999l-190 110l26 45l189 -110c10.667 -6.66699 19.167 -13.167 25.5 -19.5s14.166 -15.166 23.499 -26.499c-5.33301 12.667 -9.16602 23.834 -11.499 33.501s-3.5 20.167 -3.5 31.5v214h52v-213c0 -12 -0.833008 -22.833 -2.5 -32.5
s-5.16699 -20.5 -10.5 -32.5c8 11.333 15.333 20.166 22 26.499s15.334 12.5 26.001 18.5l188 109l26 -45l-188 -109c-10 -6 -19.333 -10.667 -28 -14s-18.667 -5.66602 -30 -6.99902c12 -2 22.167 -4.33301 30.5 -7s17.5 -6.66699 27.5 -12l189 -110l-25 -45l-190 110
c-10 6 -18.333 11.667 -25 17s-14.334 13.666 -23.001 24.999c4 -12 7.16699 -22.333 9.5 -31s3.5 -18.667 3.5 -30v-214h-52z" />
    <glyph glyph-name="plus" unicode="+" 
d="M617 1158v-451h431v-73h-431v-453h-78v453h-430v73h430v451h78z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="395" 
d="M110 85c0 23.333 8.00293 43.3301 24.0029 59.9971s37 25 63 25c28.667 0 50.834 -9.83301 66.501 -29.5s23.5 -45.5 23.5 -77.5c0 -27.333 -3.5 -55.166 -10.5 -83.499s-17.167 -56.166 -30.5 -83.499s-29.333 -53.666 -48 -78.999s-39.334 -48.666 -62.001 -69.999
l-15 15c-5.33301 5.33301 -8 11 -8 17c0 3.33301 1 6.66602 3 9.99902s4.33301 6.33301 7 9c6.66699 6.66699 15.167 16.5 25.5 29.5s20.666 28.333 30.999 46s19.666 37.167 27.999 58.5s13.833 44 16.5 68c-4 -1.33301 -8.5 -2.33301 -13.5 -3s-10.5 -1 -16.5 -1
c-24.667 0 -44.834 8.33301 -60.501 25s-23.5 38 -23.5 64z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="665" 
d="M100 634h465v-81h-465v81z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="395" 
d="M105 77c0 12.667 2.33301 24.667 7 36s11.167 21.166 19.5 29.499s18 15 29 20s23.167 7.5 36.5 7.5c12.667 0 24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20s15 -18.166 20 -29.499s7.5 -23.333 7.5 -36c0 -13.333 -2.5 -25.5 -7.5 -36.5s-11.667 -20.667 -20 -29
s-18.166 -14.833 -29.499 -19.5s-23.333 -7 -36 -7c-26 0 -47.833 8.83301 -65.5 26.5s-26.5 39.5 -26.5 65.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="713" 
d="M91 -45c-5.33301 -13.333 -13.333 -23.167 -24 -29.5s-21.667 -9.5 -33 -9.5h-39l622 1502c10 24.667 28 37 54 37h41z" />
    <glyph glyph-name="zero" unicode="0" 
d="M1087 708c0 -124 -13.333 -231.333 -40 -322s-63 -165.667 -109 -225s-99.833 -103.5 -161.5 -132.5s-127.5 -43.5 -197.5 -43.5s-135.667 14.5 -197 43.5s-115 73.167 -161 132.5s-82.167 134.333 -108.5 225s-39.5 198 -39.5 322s13.167 231.333 39.5 322
s62.5 165.667 108.5 225s99.667 103.5 161 132.5s127 43.5 197 43.5s135.833 -14.5 197.5 -43.5s115.5 -73.167 161.5 -132.5s82.333 -134.333 109 -225s40 -198 40 -322zM987 708c0 114.667 -11 212.667 -33 294s-51.5 147.833 -88.5 199.5s-80.333 89.5 -130 113.5
s-101.834 36 -156.501 36s-106.667 -12 -156 -36s-92.666 -61.833 -129.999 -113.5s-67 -118.167 -89 -199.5s-33 -179.333 -33 -294c0 -115.333 11 -213.5 33 -294.5s51.667 -147.333 89 -199s80.666 -89.334 129.999 -113.001s101.333 -35.5 156 -35.5
s106.834 11.833 156.501 35.5s93 61.334 130 113.001s66.5 118 88.5 199s33 179.167 33 294.5z" />
    <glyph glyph-name="one" unicode="1" 
d="M309 74h325v1148c0 22.667 1 46.667 3 72l-312 -277c-6.66699 -6 -14.667 -9 -24 -9c-11.333 0 -20 4 -26 12l-29 40l409 361h74v-1347h309v-74h-729v74z" />
    <glyph glyph-name="two" unicode="2" 
d="M602 1431c54 0 105.501 -7.83203 154.501 -23.499s92.167 -39.334 129.5 -71.001s67 -71.334 89 -119.001s33 -103.5 33 -167.5c0 -53.333 -8.16699 -102.833 -24.5 -148.5s-38.5 -89.5 -66.5 -131.5s-60.5 -82.833 -97.5 -122.5l-116.5 -121.5l-443 -452
c21.333 5.33301 43.5 9.33301 66.5 12s46.167 4 69.5 4h611c10.667 0 19.167 -3.16699 25.5 -9.5s9.5 -14.5 9.5 -24.5v-56h-913v34c0 7.33301 1.83301 14.833 5.5 22.5s8.16699 14.167 13.5 19.5l483 489c40 40.667 77 80 111 118s63.5 76.333 88.5 115
s44.667 78.334 59 119.001s21.5 84 21.5 130c0 52 -8.66699 97.167 -26 135.5s-40.5 69.833 -69.5 94.5s-62.333 43 -100 55s-77.167 18 -118.5 18c-44.667 0 -85.834 -6.83301 -123.501 -20.5s-71 -32.667 -100 -57s-53 -53.333 -72 -87s-32.167 -70.5 -39.5 -110.5
c-6.66699 -23.333 -20 -35 -40 -35c-1.33301 0 -3 0.166992 -5 0.5s-3.66699 0.5 -5 0.5l-48 8c8 60.667 24.5 114.667 49.5 162s56.5 87.333 94.5 120s82 57.5 132 74.5s104 25.5 162 25.5z" />
    <glyph glyph-name="three" unicode="3" 
d="M613 1431c54 0 105 -7.5 153 -22.5s90.167 -37.5 126.5 -67.5s65 -67.333 86 -112s31.5 -96.334 31.5 -155.001c0 -46 -7.16699 -87.5 -21.5 -124.5s-34 -69.5 -59 -97.5s-54.5 -51.5 -88.5 -70.5s-71 -33.5 -111 -43.5c104 -19.333 182.667 -59.5 236 -120.5
s80 -137.167 80 -228.5c0 -58 -11.667 -111.833 -35 -161.5s-55.333 -92.5 -96 -128.5s-88.667 -64.167 -144 -84.5s-115.333 -30.5 -180 -30.5c-80.667 0 -147.834 10.833 -201.501 32.5s-97.834 49.834 -132.501 84.501s-61.667 73.5 -81 116.5s-35 85.833 -47 128.5
l38 16c8 3.33301 15.667 5 23 5s14.333 -1.66699 21 -5s11.667 -9.66602 15 -18.999l7 -20c4 -12 9.66699 -26.833 17 -44.5s16.833 -36 28.5 -55s26.167 -38 43.5 -57s38.5 -36 63.5 -51s54.333 -27.167 88 -36.5s72.5 -14 116.5 -14c60 0 112.5 10 157.5 30
s82.667 45.5 113 76.5s53 65.667 68 104s22.5 75.5 22.5 111.5c0 43.333 -6.83301 83.666 -20.5 120.999s-36.5 69.5 -68.5 96.5s-74.167 48.333 -126.5 64s-116.833 23.5 -193.5 23.5v69c61.333 1.33301 115.333 9.5 162 24.5s85.667 35.5 117 61.5s55 57 71 93
s24 75.667 24 119c0 50 -8.33301 93.333 -25 130s-39 67 -67 91s-60.667 41.667 -98 53s-76.333 17 -117 17c-44.667 0 -85.667 -6.66699 -123 -20s-70.333 -32 -99 -56s-52.667 -52.833 -72 -86.5s-33.666 -71.167 -42.999 -112.5
c-5.33301 -23.333 -17.666 -35 -36.999 -35c-4.66699 0 -8.33398 0.333008 -11.001 1l-49 8c8.66699 60.667 25.334 114.667 50.001 162s56 87.333 94 120s82 57.5 132 74.5s104 25.5 162 25.5z" />
    <glyph glyph-name="four" unicode="4" 
d="M859 482h258.999v-52c0 -7.33301 -2.16699 -13.166 -6.5 -17.499s-11.166 -6.5 -20.499 -6.5h-232v-406h-86v406h-667c-10 0 -17.667 2.16699 -23 6.5s-9 10.166 -11 17.499l-9 47l711 940h85v-935zM772.999 1223c0 11.333 0.333008 23.333 1 36s2 25.667 4 39l-611 -816
h606v741z" />
    <glyph glyph-name="five" unicode="5" 
d="M966 1374c0 -13.333 -4.50488 -24.501 -13.5049 -33.501s-23.167 -13.5 -42.5 -13.5h-512l-85 -472c44.667 11.333 87 19.5 127 24.5s78.333 7.5 115 7.5c73.333 0 138 -10.5 194 -31.5s102.833 -50.333 140.5 -88s66.167 -82.5 85.5 -134.5s29 -108.667 29 -170
c0 -74.667 -13 -141.5 -39 -200.5s-61.167 -109.167 -105.5 -150.5s-96.166 -73 -155.499 -95s-122.333 -33 -189 -33c-39.333 0 -76.666 3.5 -111.999 10.5s-68.666 16.333 -99.999 28s-60 25.167 -86 40.5s-49 31 -69 47l28 39c6.66699 10 16.334 15 29.001 15
c8.66699 0 21.167 -5 37.5 -15s37.333 -21 63 -33s56 -23 91 -33s75.833 -15 122.5 -15c54.667 0 105.667 8.83301 153 26.5s88.5 43.167 123.5 76.5s62.333 74.5 82 123.5s29.5 104.5 29.5 166.5c0 50.667 -7.5 97.334 -22.5 140.001s-37.667 79.167 -68 109.5
s-68.666 54 -114.999 71s-100.833 25.5 -163.5 25.5c-38 0 -78.333 -3 -121 -9s-88 -15.667 -136 -29l-62 19l111 627h636v-41z" />
    <glyph glyph-name="six" unicode="6" 
d="M628 846c60.667 0 116.667 -10.002 168 -30.002s95.5 -48.167 132.5 -84.5s66 -80.333 87 -132s31.5 -109.5 31.5 -173.5s-11.5 -123 -34.5 -177s-55 -100.667 -96 -140s-90.167 -70 -147.5 -92s-120.333 -33 -189 -33c-66 0 -126.333 10.667 -181 32
s-101.5 52 -140.5 92s-69.167 88.833 -90.5 146.5s-32 122.834 -32 195.501c0 55.333 14 117.833 42 187.5s74 147.834 138 234.501l386 520c11.333 15.333 28.333 23 51 23h84l-422 -551l-57 -77c-17.333 -24 -33 -47.667 -47 -71c39.333 40.667 86 72.5 140 95.5
s113 34.5 177 34.5zM229 426.998c0 -52.667 8.16797 -101.335 24.501 -146.002s39.5 -83.167 69.5 -115.5s66.667 -57.5 110 -75.5s92 -27 146 -27c57.333 0 109 9 155 27s85.167 42.833 117.5 74.5s57.166 69.334 74.499 113.001s26 91.167 26 142.5
c0 54 -8.5 102.833 -25.5 146.5s-41 80.667 -72 111s-67.833 53.666 -110.5 69.999s-90 24.5 -142 24.5c-58.667 0 -111.167 -10.167 -157.5 -30.5s-85.5 -46.666 -117.5 -78.999s-56.333 -69.166 -73 -110.499s-25 -83 -25 -125z" />
    <glyph glyph-name="seven" unicode="7" 
d="M1071 1415l-0.000976562 -42.999c0 -11.333 -1.16699 -21.166 -3.5 -29.499s-5.16602 -15.5 -8.49902 -21.5l-649 -1285c-5.33301 -10.667 -12.5 -19.334 -21.5 -26.001s-20.833 -10 -35.5 -10h-65l653 1280c8.66699 17.333 19 33 31 47h-816
c-7.33301 0 -13.5 2.66699 -18.5 8s-7.5 11.666 -7.5 18.999v61h941z" />
    <glyph glyph-name="eight" unicode="8" 
d="M580 -16c-68 0 -130.166 9 -186.499 27s-104.666 44 -144.999 78s-71.833 75 -94.5 123s-34 102 -34 162c0 52 7.5 98.333 22.5 139s36.167 76 63.5 106s59.833 55 97.5 75s79.167 35 124.5 45c-41.333 12 -78 28.333 -110 49s-58.833 45.334 -80.5 74.001
s-38.167 60.5 -49.5 95.5s-17 72.5 -17 112.5c0 50 9.5 97 28.5 141s46.167 82.333 81.5 115s78.166 58.334 128.499 77.001s107.166 28 170.499 28c62.667 0 119.167 -9.33301 169.5 -28s93.333 -44.334 129 -77.001s63 -71 82 -115s28.5 -91 28.5 -141
c0 -40 -5.66699 -77.5 -17 -112.5s-28 -66.833 -50 -95.5s-49 -53.334 -81 -74.001s-68.667 -37 -110 -49c45.333 -10 86.833 -25 124.5 -45s70.167 -45 97.5 -75s48.666 -65.333 63.999 -106s23 -87 23 -139c0 -60 -11.333 -114 -34 -162s-54.167 -89 -94.5 -123
s-88.666 -60 -144.999 -78s-118.5 -27 -186.5 -27zM580.001 60c54 0 103.166 7.5 147.499 22.5s82.5 36.333 114.5 64s56.667 60.834 74 99.501s26 81.667 26 129c0 62 -11.333 113.667 -34 155s-51.667 74.333 -87 99s-74.333 42.167 -117 52.5s-84 15.5 -124 15.5
s-81.333 -5.16699 -124 -15.5s-81.667 -27.833 -117 -52.5s-64.333 -57.667 -87 -99s-34 -93 -34 -155c0 -47.333 8.66699 -90.333 26 -129s41.833 -71.834 73.5 -99.501s69.834 -49 114.501 -64s94 -22.5 148 -22.5zM580 774c54 0 100.832 8.16699 140.499 24.5
s72.5 38.333 98.5 66s45.167 59.167 57.5 94.5s18.5 72 18.5 110c0 40.667 -7 78.5 -21 113.5s-34.5 65.5 -61.5 91.5s-60 46.333 -99 61s-83.5 22 -133.5 22s-94.667 -7.33301 -134 -22s-72.5 -35 -99.5 -61s-47.5 -56.5 -61.5 -91.5s-21 -72.833 -21 -113.5
c0 -38 6.16699 -74.667 18.5 -110s31.5 -66.833 57.5 -94.5s58.833 -49.667 98.5 -66s86.834 -24.5 141.501 -24.5z" />
    <glyph glyph-name="nine" unicode="9" 
d="M573 602c-58 0 -111.501 9.49805 -160.501 28.498s-91.333 46.167 -127 81.5s-63.5 77.833 -83.5 127.5s-30 105.167 -30 166.5c0 60.667 11.167 116.834 33.5 168.501s53.5 96.5 93.5 134.5s87.5 67.833 142.5 89.5s115.167 32.5 180.5 32.5
c62.667 0 120.167 -10.667 172.5 -32s97.333 -51.5 135 -90.5s67 -85.667 88 -140s31.5 -114.833 31.5 -181.5c0 -36.667 -3.83301 -71.667 -11.5 -105s-19 -66.666 -34 -99.999s-33.167 -67.5 -54.5 -102.5s-45.666 -72.5 -72.999 -112.5l-371 -544
c-10 -15.333 -26.333 -23 -49 -23h-86l418 584l55.5 79.5c17 25 32.5 49.167 46.5 72.5c-38.667 -42.667 -85.5 -75.667 -140.5 -99s-113.833 -35 -176.5 -35zM956.999 1005c0 51.333 -8.33398 98.334 -25.001 141.001s-40 79.167 -70 109.5s-65.5 54 -106.5 71
s-85.833 25.5 -134.5 25.5c-52 0 -99.833 -8.33301 -143.5 -25s-81 -40 -112 -70s-55.167 -65.833 -72.5 -107.5s-26 -87.5 -26 -137.5c0 -52 8 -98.833 24 -140.5s38.5 -77 67.5 -106s64 -51.167 105 -66.5s86.5 -23 136.5 -23c56.667 0 107.167 9.66699 151.5 29
s81.833 44.666 112.5 75.999s53.834 66.5 69.501 105.5s23.5 78.5 23.5 118.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="475" 
d="M145 77c0 12.667 2.33301 24.667 7 36s11.167 21.166 19.5 29.499s18 15 29 20s23.167 7.5 36.5 7.5c12.667 0 24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20s15 -18.166 20 -29.499s7.5 -23.333 7.5 -36c0 -13.333 -2.5 -25.5 -7.5 -36.5s-11.667 -20.667 -20 -29
s-18.166 -14.833 -29.499 -19.5s-23.333 -7 -36 -7c-26 0 -47.833 8.83301 -65.5 26.5s-26.5 39.5 -26.5 65.5zM145 874c0 12.667 2.33301 24.667 7 36s11.167 21.166 19.5 29.499s18 15 29 20s23.167 7.5 36.5 7.5c12.667 0 24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20
s15 -18.166 20 -29.499s7.5 -23.333 7.5 -36c0 -13.333 -2.5 -25.5 -7.5 -36.5s-11.667 -20.667 -20 -29s-18.166 -14.833 -29.499 -19.5s-23.333 -7 -36 -7c-26 0 -47.833 8.83301 -65.5 26.5s-26.5 39.5 -26.5 65.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="475" 
d="M145 874c0 12.667 2.33301 24.667 7 36s11.167 21.166 19.5 29.499s18 15 29 20s23.167 7.5 36.5 7.5c12.667 0 24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20s15 -18.166 20 -29.499s7.5 -23.333 7.5 -36c0 -13.333 -2.5 -25.5 -7.5 -36.5s-11.667 -20.667 -20 -29
s-18.166 -14.833 -29.499 -19.5s-23.333 -7 -36 -7c-26 0 -47.833 8.83301 -65.5 26.5s-26.5 39.5 -26.5 65.5zM150 85c0 23.333 8.00293 43.3301 24.0029 59.9971s37 25 63 25c28.667 0 50.834 -9.83301 66.501 -29.5s23.5 -45.5 23.5 -77.5
c0 -27.333 -3.5 -55.166 -10.5 -83.499s-17.167 -56.166 -30.5 -83.499s-29.333 -53.666 -48 -78.999s-39.334 -48.666 -62.001 -69.999l-15 15c-5.33301 5.33301 -8 11 -8 17c0 3.33301 1 6.66602 3 9.99902s4.33301 6.33301 7 9c6.66699 6.66699 15.167 16.5 25.5 29.5
s20.666 28.333 30.999 46s19.666 37.167 27.999 58.5s13.833 44 16.5 68c-4 -1.33301 -8.5 -2.33301 -13.5 -3s-10.5 -1 -16.5 -1c-24.667 0 -44.834 8.33301 -60.501 25s-23.5 38 -23.5 64z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M160 692l750 384.996v-65c0 -7.33301 -2 -13.666 -6 -18.999s-11 -10.666 -21 -15.999l-553 -280c-22 -11.333 -46 -20 -72 -26c28 -6 52 -14.333 72 -25l553 -281c10 -5.33301 17 -10.666 21 -15.999s6 -11.666 6 -18.999v-65l-750 386v41z" />
    <glyph glyph-name="equal" unicode="=" 
d="M166 556h827v-74h-827v74zM166 869h827v-74h-827v74z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M251 265l-0.00292969 65.001c0 7.33301 2 13.833 6 19.5s10.667 10.834 20 15.501l554 281c20.667 10.667 44.334 19 71.001 25c-13.333 3.33301 -25.666 7.16602 -36.999 11.499s-22.666 9.16602 -33.999 14.499l-554 280c-17.333 9.33301 -26 21 -26 35v65l750 -385
v-41z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="741" 
d="M33 1292c18 18 38.332 35.3359 60.999 52.0029s47.5 31.5 74.5 44.5s56.5 23.333 88.5 31s66.667 11.5 104 11.5c46.667 0 90.667 -7 132 -21s77.5 -34 108.5 -60s55.667 -57.833 74 -95.5s27.5 -79.834 27.5 -126.501c0 -50.667 -7.83301 -94.334 -23.5 -131.001
s-35.5 -68.834 -59.5 -96.501s-50 -52 -78 -73l-78.5 -59c-24.333 -18.333 -45 -36.833 -62 -55.5s-26.167 -39.334 -27.5 -62.001l-12 -164h-65l-5 171v5c0 27.333 8 51.333 24 72s35.833 40.667 59.5 60s49.334 39 77.001 59s53.334 42.5 77.001 67.5
s43.5 53.833 59.5 86.5s24 71.334 24 116.001c0 36 -7 68.167 -21 96.5s-33 52.333 -57 72s-51.667 34.667 -83 45s-64 15.5 -98 15.5c-44 0 -82.167 -5.83301 -114.5 -17.5s-59.5 -24.667 -81.5 -39s-39.333 -27.333 -52 -39s-21.334 -17.5 -26.001 -17.5
c-10 0 -17.333 4 -22 12zM234.999 77.0029c0 12.667 2.33301 24.667 7 36s11.167 21.166 19.5 29.499s18 15 29 20s23.167 7.5 36.5 7.5c12.667 0 24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20s15 -18.166 20 -29.499s7.5 -23.333 7.5 -36
c0 -13.333 -2.5 -25.5 -7.5 -36.5s-11.667 -20.667 -20 -29s-18.166 -14.833 -29.499 -19.5s-23.333 -7 -36 -7c-26 0 -47.833 8.83301 -65.5 26.5s-26.5 39.5 -26.5 65.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1643" 
d="M1167 188c-58.667 0 -102.838 15.4951 -132.505 46.4951s-45.167 75.5 -46.5 133.5c-37.333 -62 -79.5 -106.833 -126.5 -134.5s-97.833 -41.5 -152.5 -41.5c-37.333 0 -69.666 6.5 -96.999 19.5s-50 30.833 -68 53.5s-31.5 49.334 -40.5 80.001s-13.5 63.667 -13.5 99
c0 56.667 10.833 113.5 32.5 170.5s53.334 108.333 95.001 154s92.834 82.834 153.501 111.501s129.667 43 207 43c35.333 0 68.5 -3 99.5 -9s58.833 -14.667 83.5 -26l-91 -347c-8 -30.667 -13.833 -58.167 -17.5 -82.5s-5.5 -46.166 -5.5 -65.499
c0 -29.333 3.66699 -53.166 11 -71.499s17.166 -32.833 29.499 -43.5s26.5 -17.834 42.5 -21.501s32.667 -5.5 50 -5.5c38 0 74.333 10 109 30s65.334 48.5 92.001 85.5s47.834 81.833 63.501 134.5s23.5 111.667 23.5 177c0 98.667 -16.167 184.834 -48.5 258.501
s-76.333 134.834 -132 183.501s-120.5 85 -194.5 109s-152.667 36 -236 36c-91.333 0 -177.833 -17.667 -259.5 -53s-153.334 -84.666 -215.001 -147.999s-110.667 -138.833 -147 -226.5s-54.5 -183.834 -54.5 -288.501c0 -118 18.833 -221.833 56.5 -311.5
s88.834 -164.667 153.501 -225s140.167 -106 226.5 -137s178.166 -46.5 275.499 -46.5c98.667 0 188.834 11.333 270.501 34s155.834 55.667 222.501 99c4.66699 3.33301 10.334 5 17.001 5c9.33301 0 16 -4.66699 20 -14l15 -37
c-74.667 -49.333 -156.834 -87.333 -246.501 -114s-189.167 -40 -298.5 -40c-110 0 -212.667 17.5 -308 52.5s-178.333 86 -249 153s-126.334 149.333 -167.001 247s-61 209.167 -61 334.5c0 111.333 19.667 214.666 59 309.999s93 178 161 248s147.333 124.833 238 164.5
s187.667 59.5 291 59.5c89.333 0 175 -13.833 257 -41.5s154.5 -69 217.5 -124s113.333 -123.167 151 -204.5s56.5 -175.666 56.5 -282.999c0 -72.667 -9.66699 -139 -29 -199s-45.833 -111.5 -79.5 -154.5s-73.167 -76.5 -118.5 -100.5s-93.666 -36 -144.999 -36z
M726.996 257.995c24 0 48.8301 4 74.4971 12s50.334 22.5 74.001 43.5s45.834 49.333 66.501 85s38 81.167 52 136.5l80 310c-16 4.66699 -32.5 8.16699 -49.5 10.5s-36.833 3.5 -59.5 3.5c-60 0 -114.5 -12.167 -163.5 -36.5s-90.667 -56.166 -125 -95.499
s-61 -83.5 -80 -132.5s-28.5 -98.5 -28.5 -148.5c0 -26.667 3.33301 -51.5 10 -74.5s16.667 -42.833 30 -59.5s29.833 -29.834 49.5 -39.501s42.834 -14.5 69.501 -14.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1289" 
d="M1275 0l-78.999 -0.000976562c-9.33301 0 -17 2.5 -23 7.5s-10.667 11.5 -14 19.5l-167 411h-695l-166 -411c-2.66699 -7.33301 -7.33398 -13.666 -14.001 -18.999s-14.667 -8 -24 -8h-78l579 1415h102zM328.001 513.999l634.004 0.000976562l-289 715
c-4.66699 11.333 -9.33398 24.166 -14.001 38.499s-9.33398 29.5 -14.001 45.5c-4.66699 -16 -9.33398 -31.167 -14.001 -45.5s-9.33398 -27.5 -14.001 -39.5z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1282" 
d="M210 0v1415h407c80.667 0 150.5 -8 209.5 -24s107.833 -39.5 146.5 -70.5s67.5 -68.833 86.5 -113.5s28.5 -95.334 28.5 -152.001c0 -37.333 -6.5 -73.333 -19.5 -108s-31.833 -66.667 -56.5 -96s-54.834 -54.833 -90.501 -76.5s-76.5 -38.167 -122.5 -49.5
c111.333 -16.667 197 -53.667 257 -111s90 -133 90 -227c0 -60 -10.667 -114 -32 -162s-52.333 -88.667 -93 -122s-90.667 -58.833 -150 -76.5s-126.666 -26.5 -201.999 -26.5h-459zM312 678.001v-596.001h355c124.667 0 218.667 26.833 282 80.5s95 129.167 95 226.5
c0 44.667 -8.5 84.834 -25.5 120.501s-41.5 66 -73.5 91s-71.333 44.167 -118 57.5s-99.667 20 -159 20h-356zM312 753l307.001 -0.000976562c65.333 0 121.166 8.33301 167.499 25s84.333 38.667 114 66s51.5 58.333 65.5 93s21 70.334 21 107.001
c0 95.333 -30.167 167.5 -90.5 216.5s-153.5 73.5 -279.5 73.5h-305v-581z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1407" 
d="M1238 238c6.66699 0 12.3301 -2.33203 16.9971 -6.99902l40 -43c-29.333 -31.333 -61.333 -59.5 -96 -84.5s-72.5 -46.333 -113.5 -64s-86 -31.334 -135 -41.001s-102.833 -14.5 -161.5 -14.5c-98 0 -187.833 17.167 -269.5 51.5s-151.667 83 -210 146
s-103.833 139.167 -136.5 228.5s-49 188.666 -49 297.999c0 107.333 16.833 205.333 50.5 294s81 164.834 142 228.501s134 113 219 148s178.833 52.5 281.5 52.5c51.333 0 98.333 -3.83301 141 -11.5s82.5 -18.667 119.5 -33s71.833 -32.166 104.5 -53.499
s64.667 -46 96 -74l-31 -45c-5.33301 -8 -13.666 -12 -24.999 -12c-6 0 -13.667 3.5 -23 10.5l-35.5 26c-14.333 10.333 -31.666 21.666 -51.999 33.999s-44.5 23.666 -72.5 33.999s-60.333 19 -97 26s-78.334 10.5 -125.001 10.5c-86 0 -165 -14.833 -237 -44.5
s-134 -72 -186 -127s-92.5 -121.667 -121.5 -200s-43.5 -166.166 -43.5 -263.499c0 -100 14.333 -189.333 43 -268s68.334 -145.167 119.001 -199.5s110.5 -96 179.5 -125s143.833 -43.5 224.5 -43.5c50.667 0 95.834 3.33301 135.501 10s76.167 16.667 109.5 30
s64.333 29.666 93 48.999s57 42 85 68c3.33301 2.66699 6.5 4.83398 9.5 6.50098s6.5 2.5 10.5 2.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1520" 
d="M1401 708c0 -109.333 -16.333 -207.669 -49 -295.002s-78.667 -161.666 -138 -222.999s-130.333 -108.333 -213 -141s-174.334 -49 -275.001 -49h-516v1415h516c100.667 0 192.334 -16.333 275.001 -49s153.667 -79.667 213 -141s105.333 -135.666 138 -222.999
s49 -185.333 49 -294zM1295 707.997c0 98.667 -13.667 186.67 -41 264.003s-65.833 142.666 -115.5 195.999s-109.5 94 -179.5 122s-147.667 42 -233 42h-413v-1249h413c85.333 0 163 14 233 42s129.833 68.667 179.5 122s88.167 118.666 115.5 195.999
s41 165.666 41 264.999z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1182" 
d="M1058 1415v-85h-745v-572h620v-83h-620v-590h745v-85h-848v1415h848z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1144" 
d="M1058 1415v-85h-745v-595h645v-85h-645v-650h-103v1415h848z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1497" 
d="M818 67c48.667 0 92.9971 2.50098 132.997 7.50098s77.167 12.5 111.5 22.5s67 22.167 98 36.5s62.167 30.5 93.5 48.5v381h-272c-7.33301 0 -13.5 2.33301 -18.5 7s-7.5 10 -7.5 16v56h391v-503c-35.333 -24.667 -72 -46.667 -110 -66s-78.667 -35.5 -122 -48.5
s-90 -23 -140 -30s-104.667 -10.5 -164 -10.5c-102 0 -195.167 17.167 -279.5 51.5s-156.666 83 -216.999 146s-107.166 139.167 -140.499 228.5s-50 188.666 -50 297.999s16.667 208.5 50 297.5s80.666 165 141.999 228s135.833 111.667 223.5 146
s185.834 51.5 294.501 51.5c54 0 103.833 -3.66699 149.5 -11s88.167 -18.166 127.5 -32.499s76 -32 110 -53s66.667 -45.167 98 -72.5l-27 -44c-6 -10 -14.333 -15 -25 -15c-6 0 -11.667 1.33301 -17 4c-10 4 -24.5 13.333 -43.5 28s-44.667 30 -77 46
s-72.833 30.333 -121.5 43s-107.667 19 -177 19c-92 0 -175 -14.667 -249 -44s-137.167 -71.333 -189.5 -126s-92.666 -121.334 -120.999 -200.001s-42.5 -167 -42.5 -265c0 -100 14.333 -189.667 43 -269s68.834 -146.5 120.501 -201.5s113.667 -97.167 186 -126.5
s152.166 -44 239.499 -44z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1512" 
d="M1302 0h-103v678h-886v-678h-103v1415h103v-660h886v660h103v-1415z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="598" 
d="M350 0h-103v1415h103v-1415z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="908" 
d="M698 472c0 -79.333 -9.83594 -149.331 -29.5029 -209.998s-47.667 -111.667 -84 -153s-80.666 -72.5 -132.999 -93.5s-111.166 -31.5 -176.499 -31.5c-30 0 -60.333 2.33301 -91 7s-62.334 12 -95.001 22c1.33301 9.33301 2.5 18.666 3.5 27.999
s1.83301 18.666 2.5 27.999c1.33301 6 3.83301 11.333 7.5 16s10.167 7 19.5 7c6 0 13.167 -1.16699 21.5 -3.5s18.333 -4.83301 30 -7.5s25 -5.16699 40 -7.5s32.5 -3.5 52.5 -3.5c50.667 0 96.334 8 137.001 24s75.334 40.5 104.001 73.5s50.5 74.667 65.5 125
s22.5 109.5 22.5 177.5v945h103v-943z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1320" 
d="M342 762l84 -0.00195312c12.667 0 23.667 0.5 33 1.5s17.833 3 25.5 6s14.834 6.83301 21.501 11.5s13.667 10.667 21 18l571 587c10.667 10.667 20.5 18.167 29.5 22.5s20.167 6.5 33.5 6.5h83l-618 -635c-12.667 -13.333 -23.834 -23.666 -33.501 -30.999
s-20.5 -13.333 -32.5 -18c14 -4 26.5 -10.167 37.5 -18.5s22.167 -19.166 33.5 -32.499l652 -680h-83c-17.333 0 -29.666 2.5 -36.999 7.5s-15 11.5 -23 19.5l-599 612c-7.33301 7.33301 -14 13.666 -20 18.999s-12.833 9.83301 -20.5 13.5s-16.834 6.33398 -27.501 8.00098
s-24 2.5 -40 2.5h-91v-682h-102v1417h102v-655z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="1023" 
d="M312 87h671v-87h-773v1415h102v-1328z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1818" 
d="M890 412c10 -18.667 18.6689 -38.665 26.002 -59.998c4 10.667 8.16699 21.167 12.5 31.5s9.16602 20.166 14.499 29.499l556 983c5.33301 8.66699 10.666 14 15.999 16s12.666 3 21.999 3h71v-1415h-90v1206c0 17.333 1 35.666 3 54.999l-557 -989
c-9.33301 -17.333 -22.666 -26 -39.999 -26h-16c-16.667 0 -30 8.66699 -40 26l-572 990c2 -19.333 3 -38 3 -56v-1206h-89v1415h70c9.33301 0 16.833 -1 22.5 -3s11.167 -7.33301 16.5 -16l571 -984v0z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1512" 
d="M260 1415c9.33301 0 16.666 -1.16699 21.999 -3.5s11 -7.5 17 -15.5l916 -1226c-2 20 -3 39.333 -3 58v1187h90v-1415h-49c-15.333 0 -27.666 6.66699 -36.999 20l-920 1228c2 -20 3 -39 3 -57v-1191h-89v1415h50v0z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="1595" 
d="M1472 708c0 -109.333 -16.333 -208.501 -49 -297.501s-78.667 -165 -138 -228s-130.333 -111.667 -213 -146s-174.334 -51.5 -275.001 -51.5c-99.333 0 -190.333 17.167 -273 51.5s-153.667 83 -213 146s-105.5 139 -138.5 228s-49.5 188.167 -49.5 297.5
c0 108.667 16.5 207.5 49.5 296.5s79.167 165 138.5 228s130.333 111.833 213 146.5s173.667 52 273 52c100.667 0 192.334 -17.167 275.001 -51.5s153.667 -83.166 213 -146.499s105.333 -139.5 138 -228.5s49 -187.833 49 -296.5zM1366 707.999
c0 98.667 -13.667 187.334 -41 266.001s-65.833 145.334 -115.5 200.001s-109.5 96.5 -179.5 125.5s-147.667 43.5 -233 43.5c-84 0 -160.833 -14.5 -230.5 -43.5s-129.667 -70.833 -180 -125.5s-89.333 -121.334 -117 -200.001s-41.5 -167.334 -41.5 -266.001
c0 -99.333 13.833 -188.166 41.5 -266.499s66.667 -144.833 117 -199.5s110.333 -96.334 180 -125.001s146.5 -43 230.5 -43c85.333 0 163 14.333 233 43s129.833 70.334 179.5 125.001s88.167 121.167 115.5 199.5s41 167.166 41 266.499z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1187" 
d="M342 570v-569.999h-102v1415h365c166 0 291.167 -36.333 375.5 -109s126.5 -176.334 126.5 -311.001c0 -62 -11.5 -119 -34.5 -171s-56.167 -96.833 -99.5 -134.5s-96 -67 -158 -88s-132 -31.5 -210 -31.5h-263zM342 652.001l263.002 -0.00195312
c62.667 0 118.667 8.66699 168 26s91.166 41.333 125.499 72s60.666 66.834 78.999 108.501s27.5 87.167 27.5 136.5c0 108.667 -33.5 192.334 -100.5 251.001s-166.833 88 -299.5 88h-263v-682z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="1595" 
d="M1472 708c0 -71.333 -7.16797 -138.502 -21.501 -201.502s-34.833 -121 -61.5 -174s-59.167 -100.5 -97.5 -142.5s-81.5 -78 -129.5 -108l368 -394h-86c-13.333 0 -25.333 1.83301 -36 5.5s-20 10.167 -28 19.5l-302 326c-42 -17.333 -86.333 -30.666 -133 -39.999
s-96 -14 -148 -14c-99.333 0 -190.333 17.167 -273 51.5s-153.667 83 -213 146s-105.5 139 -138.5 228s-49.5 188.167 -49.5 297.5c0 108.667 16.5 207.5 49.5 296.5s79.167 165 138.5 228s130.333 111.833 213 146.5s173.667 52 273 52
c100.667 0 192.334 -17.167 275.001 -51.5s153.667 -83.166 213 -146.499s105.333 -139.5 138 -228.5s49 -187.833 49 -296.5zM1366 707.998c0 98.667 -13.667 187.334 -41 266.001s-65.833 145.334 -115.5 200.001s-109.5 96.5 -179.5 125.5s-147.667 43.5 -233 43.5
c-84 0 -160.833 -14.5 -230.5 -43.5s-129.667 -70.833 -180 -125.5s-89.333 -121.334 -117 -200.001s-41.5 -167.334 -41.5 -266.001c0 -99.333 13.833 -188.166 41.5 -266.499s66.667 -144.833 117 -199.5s110.333 -96.334 180 -125.001s146.5 -43 230.5 -43
c85.333 0 163 14.333 233 43s129.833 70.334 179.5 125.001s88.167 121.167 115.5 199.5s41 167.166 41 266.499z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1262" 
d="M342 640l0.000976562 -639.997h-102v1415h361c163.333 0 285.333 -31.5 366 -94.5s121 -155.5 121 -277.5c0 -54 -9.33301 -103.333 -28 -148s-45.5 -83.834 -80.5 -117.501s-77.167 -61.334 -126.5 -83.001s-105 -35.834 -167 -42.501c16 -10 30.333 -23.333 43 -40
l487 -612h-89c-10.667 0 -19.834 2 -27.501 6s-15.167 10.667 -22.5 20l-453 574c-11.333 14.667 -23.5 25 -36.5 31s-33.167 9 -60.5 9h-185zM342.001 716.003h245.999c62.667 0 118.834 7.5 168.501 22.5s91.667 36.5 126 64.5s60.5 61.667 78.5 101s27 83.666 27 132.999
c0 100.667 -33 175.334 -99 224.001s-162 73 -288 73h-259v-618z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1045" 
d="M894 1243c-6 -11.333 -14.667 -16.998 -26 -16.998c-8.66699 0 -19.834 6.16699 -33.501 18.5s-32.167 26 -55.5 41s-52.5 28.833 -87.5 41.5s-78.167 19 -129.5 19s-96.5 -7.33301 -135.5 -22s-71.667 -34.667 -98 -60s-46.333 -54.666 -60 -87.999
s-20.5 -68.333 -20.5 -105c0 -48 10.167 -87.667 30.5 -119s47.166 -58 80.499 -80s71.166 -40.5 113.499 -55.5l130.5 -44.5c44.667 -14.667 88.167 -30.834 130.5 -48.501s80.166 -39.834 113.499 -66.501s60.166 -59.5 80.499 -98.5s30.5 -87.5 30.5 -145.5
c0 -59.333 -10.167 -115.166 -30.5 -167.499s-49.833 -97.833 -88.5 -136.5s-86 -69.167 -142 -91.5s-120 -33.5 -192 -33.5c-93.333 0 -173.666 16.5 -240.999 49.5s-126.333 78.167 -177 135.5l28 44c8 10 17.333 15 28 15c6 0 13.667 -4 23 -12l34 -29.5
c13.333 -11.667 29.333 -24.334 48 -38.001s40.334 -26.334 65.001 -38.001s53 -21.5 85 -29.5s68.333 -12 109 -12c56 0 106 8.5 150 25.5s81.167 40.167 111.5 69.5s53.5 64.166 69.5 104.499s24 83.5 24 129.5c0 50 -10.167 91.167 -30.5 123.5
s-47.166 59.333 -80.499 81s-71.166 39.834 -113.499 54.501s-85.833 29 -130.5 43s-88.167 29.667 -130.5 47s-80.166 39.5 -113.499 66.5s-60.166 60.667 -80.499 101s-30.5 90.833 -30.5 151.5c0 47.333 9 93 27 137s44.333 82.833 79 116.5s77.5 60.667 128.5 81
s109.167 30.5 174.5 30.5c73.333 0 139.166 -11.667 197.499 -35s111.5 -59 159.5 -107z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1169" 
d="M1134 1415v-87h-497v-1328h-102v1328h-500v87h1099z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1467" 
d="M733 73c68 0 128.831 11.833 182.498 35.5s99.167 56.5 136.5 98.5s65.833 91.5 85.5 148.5s29.5 118.5 29.5 184.5v875h102v-875c0 -78 -12.333 -150.833 -37 -218.5s-60.167 -126.667 -106.5 -177s-102.666 -89.833 -168.999 -118.5s-140.833 -43 -223.5 -43
s-157.167 14.333 -223.5 43s-122.666 68.167 -168.999 118.5s-81.833 109.333 -106.5 177s-37 140.5 -37 218.5v875h103v-874c0 -66 9.83301 -127.5 29.5 -184.5s48 -106.5 85 -148.5s82.333 -75 136 -99s114.5 -36 182.5 -36z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1289" 
d="M15 1415l81.002 0.00195312c9.33301 0 17 -2.5 23 -7.5s10.667 -11.5 14 -19.5l482 -1174c12.667 -30.667 23 -63.334 31 -98.001c7.33301 36 17 68.667 29 98l481 1174c3.33301 7.33301 8.16602 13.666 14.499 18.999s14.166 8 23.499 8h80l-584 -1415h-91z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1974" 
d="M17 1415l84.999 0.00292969c19.333 0 31.666 -9 36.999 -27l357 -1163c4 -13.333 7.5 -27.666 10.5 -42.999l9.5 -48l10 48c3.33301 15.333 7.33301 29.666 12 42.999l400 1163c2.66699 7.33301 7.33398 13.666 14.001 18.999s14.667 8 24 8h28
c9.33301 0 17 -2.5 23 -7.5s10.667 -11.5 14 -19.5l400 -1163c4.66699 -13.333 8.83398 -27.333 12.501 -42s7.16699 -30.334 10.5 -47.001c3.33301 16 6.33301 31.5 9 46.5s6 29.167 10 42.5l357 1163c2 7.33301 6.5 13.666 13.5 18.999s15.167 8 24.5 8h78l-445 -1415h-92
l-418 1228c-5.33301 16 -10 33 -14 51c-4 -18 -8.66699 -35 -14 -51l-419 -1228h-91z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1199" 
d="M528 726l-487.999 689.001h101c9.33301 0 16.166 -2 20.499 -6s8.16602 -8.66699 11.499 -14l430 -620c2 5.33301 4.16699 10.5 6.5 15.5s5.16602 10.167 8.49902 15.5l415 588c4 5.33301 8.33301 10.166 13 14.499s10.334 6.5 17.001 6.5h98l-489 -684l509 -731h-101
c-9.33301 -0 -16.666 2.83301 -21.999 8.5s-9.66602 11.167 -12.999 16.5l-446 652c-2.66699 -10 -7.33398 -19.667 -14.001 -29l-438 -623c-4.66699 -6 -10 -11.667 -16 -17s-13 -8 -21 -8h-93z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1199" 
d="M651 584v-584h-103v584l-523 831h89c9.33301 0 16.833 -2.16699 22.5 -6.5s11.167 -10.5 16.5 -18.5l405 -650c9.33301 -15.333 17.333 -30.166 24 -44.499s12.667 -28.833 18 -43.5c5.33301 14.667 11.333 29.167 18 43.5s14.667 29.166 24 44.499l404 650
c4 7.33301 9 13.333 15 18s13.667 7 23 7h90z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1266" 
d="M1163 1415v-35c0 -14 -4.66699 -27.667 -14 -41l-898 -1254h904v-85h-1040v37c0 12.667 4 24.667 12 36l900 1257h-886v85h1022z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="600" 
d="M175 -264v1781h318v-36c0 -8.66699 -2.83301 -15.667 -8.5 -21s-13.167 -8 -22.5 -8h-206v-1651h206c9.33301 0 16.833 -2.66699 22.5 -8s8.5 -12.333 8.5 -21v-36h-318z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="715" 
d="M-8 1455l41 0.000976562c26 0 44 -12.333 54 -37l622 -1502h-39c-11.333 0 -22.333 3.16699 -33 9.5s-18.667 16.166 -24 29.499z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="600" 
d="M107 -228c0 8.66699 2.66699 15.667 8 21s12.666 8 21.999 8h206v1651h-206c-9.33301 0 -16.666 2.66699 -21.999 8s-8 12.333 -8 21v36h317v-1781h-317v36z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M544 1415h59.998l341 -615h-68c-7.33301 0 -13.833 2.16699 -19.5 6.5s-10.167 9.83301 -13.5 16.5l-241 436c-6 11.333 -11.333 22 -16 32s-8.66699 20.333 -12 31c-3.33301 -10.667 -7.16602 -21.167 -11.499 -31.5s-9.5 -20.833 -15.5 -31.5l-240 -436
c-3.33301 -5.33301 -7.66602 -10.5 -12.999 -15.5s-12.333 -7.5 -21 -7.5h-71z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="788" 
d="M788 -205v-68h-788v68h788z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="585" 
d="M178 1431c16 0 28 -2.33301 36 -7s15.667 -13 23 -25l157 -238h-54c-8 0 -14.833 1.16699 -20.5 3.5s-11.167 6.83301 -16.5 13.5l-217 253h92z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="973" 
d="M797 0c-20 0 -32.332 9.33496 -36.999 28.002l-14 127c-27.333 -26.667 -54.5 -50.667 -81.5 -72s-55.167 -39.333 -84.5 -54s-61 -25.834 -95 -33.501s-71.333 -11.5 -112 -11.5c-34 0 -67 5 -99 15s-60.333 25.333 -85 46s-44.5 47.167 -59.5 79.5
s-22.5 71.166 -22.5 116.499c0 42 12 81 36 117s61.5 67.333 112.5 94s116.667 48 197 64s176.833 25.333 289.5 28v104c0 92 -19.833 162.833 -59.5 212.5s-98.5 74.5 -176.5 74.5c-48 0 -88.833 -6.66699 -122.5 -20s-62.167 -28 -85.5 -44s-42.333 -30.667 -57 -44
s-27 -20 -37 -20c-13.333 0 -23.333 6 -30 18l-17 29c52 52 106.667 91.333 164 118s122.333 40 195 40c53.333 0 100 -8.5 140 -25.5s73.167 -41.333 99.5 -73s46.166 -70 59.499 -115s20 -95.167 20 -150.5v-648h-38zM397.001 53.002
c38.667 0 74.166 4.16699 106.499 12.5s62.333 20 90 35s53.5 32.833 77.5 53.5s47.667 43 71 67v259c-94.667 -2.66699 -176.167 -9.83398 -244.5 -21.501s-124.666 -27.5 -168.999 -47.5s-77 -43.833 -98 -71.5s-31.5 -59.167 -31.5 -94.5
c0 -33.333 5.5 -62.166 16.5 -86.499s25.5 -44.333 43.5 -60s39 -27.167 63 -34.5s49 -11 75 -11z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1092" 
d="M172 0v1455h96v-634c44 58.667 95 105 153 139s123.333 51 196 51c122 0 217.167 -42.167 285.5 -126.5s102.5 -211.166 102.5 -380.499c0 -72.667 -9.5 -140.5 -28.5 -203.5s-47 -117.667 -84 -164s-82.333 -82.833 -136 -109.5s-115.5 -40 -185.5 -40
c-69.333 0 -129 13.5 -179 40.5s-93 66.833 -129 119.5l-6 -122c-2 -16.667 -11 -25 -27 -25h-58zM590 934.999c-66 0 -125.667 -16.999 -179 -50.999s-101 -81.333 -143 -142v-525c38 -57.333 80 -97.5 126 -120.5s99 -34.5 159 -34.5c58.667 0 110 10.667 154 32
s80.833 51.5 110.5 90.5s52 85.667 67 140s22.5 114.166 22.5 179.499c0 148 -27.333 256.833 -82 326.5s-133 104.5 -235 104.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="910" 
d="M817 862c-3.33301 -3.33301 -6.50488 -6.16504 -9.50488 -8.49805s-7.16699 -3.5 -12.5 -3.5c-6.66699 0 -15.834 4.5 -27.501 13.5s-27.167 18.833 -46.5 29.5s-43.5 20.5 -72.5 29.5s-64.167 13.5 -105.5 13.5c-57.333 0 -108.166 -10.167 -152.499 -30.5
s-81.666 -49.5 -111.999 -87.5s-53.5 -84 -69.5 -138s-24 -114.667 -24 -182c0 -70 8.16699 -132 24.5 -186s39.5 -99.5 69.5 -136.5s66.333 -65.167 109 -84.5s90 -29 142 -29c48 0 88.167 5.66699 120.5 17s59 23.666 80 36.999s37.667 25.666 50 36.999
s22.166 17 29.499 17c8.66699 0 15.334 -3.33301 20.001 -10l26 -32c-15.333 -20 -34.666 -38.667 -57.999 -56s-49.666 -32.333 -78.999 -45s-61.166 -22.5 -95.499 -29.5s-70.166 -10.5 -107.499 -10.5c-63.333 0 -121.166 11.5 -173.499 34.5s-97.333 56.333 -135 100
s-67 97.167 -88 160.5s-31.5 135.333 -31.5 216c0 76 10 145.333 30 208s49.167 116.667 87.5 162s85.333 80.5 141 105.5s119.5 37.5 191.5 37.5c64.667 0 122.167 -10.333 172.5 -31s94.166 -48.667 131.499 -84z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1092" 
d="M867 0c-16.667 0 -26.332 8.66699 -28.999 26l-10 157c-44 -60 -95.5 -107.667 -154.5 -143s-125.167 -53 -198.5 -53c-122 0 -217.167 42.333 -285.5 127s-102.5 211.667 -102.5 381c0 72.667 9.5 140.5 28.5 203.5s47 117.667 84 164s82.333 82.833 136 109.5
s115.5 40 185.5 40c67.333 0 125.666 -12.5 174.999 -37.5s92 -61.5 128 -109.5v590h96v-1455h-53zM502.002 64c66 0 125.667 16.999 179 50.999s101 81.333 143 142v525c-38.667 56.667 -81.167 96.667 -127.5 120s-98.833 35 -157.5 35s-110 -10.667 -154 -32
s-80.833 -51.5 -110.5 -90.5s-52 -85.667 -67 -140s-22.5 -114.166 -22.5 -179.499c0 -148 27.333 -256.833 82 -326.5s133 -104.5 235 -104.5z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="1022" 
d="M537 1011c56 0 107.829 -9.66992 155.496 -29.0029s89 -47.5 124 -84.5s62.333 -82.5 82 -136.5s29.5 -116 29.5 -186c0 -14.667 -2.16699 -24.667 -6.5 -30s-11.166 -8 -20.499 -8h-719v-19c0 -74.667 8.66699 -140.334 26 -197.001s42 -104.167 74 -142.5
s70.667 -67.166 116 -86.499s96 -29 152 -29c50 0 93.333 5.5 130 16.5s67.5 23.333 92.5 37s44.833 26 59.5 37s25.334 16.5 32.001 16.5c8.66699 0 15.334 -3.33301 20.001 -10l26 -32c-16 -20 -37.167 -38.667 -63.5 -56s-55.666 -32.166 -87.999 -44.499
s-67 -22.166 -104 -29.499s-74.167 -11 -111.5 -11c-68 0 -130 11.833 -186 35.5s-104 58.167 -144 103.5s-70.833 100.833 -92.5 166.5s-32.5 140.834 -32.5 225.501c0 71.333 10.167 137.166 30.5 197.499s49.666 112.333 87.999 156s85.333 77.834 141 102.501
s118.834 37 189.501 37zM537.996 939.997c-51.333 0 -97.332 -7.99805 -137.999 -23.998s-75.834 -39 -105.501 -69s-53.667 -66 -72 -108s-30.5 -89 -36.5 -141h656c0 53.333 -7.33301 101.166 -22 143.499s-35.334 78.166 -62.001 107.499s-58.667 51.833 -96 67.5
s-78.666 23.5 -123.999 23.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="644" 
d="M199 0l-0.000976562 891.001l-140 9c-19.333 1.33301 -29 9.66602 -29 24.999v39h169v126c0 56.667 7.33301 106.5 22 149.5s35.334 78.833 62.001 107.5s58.834 50.167 96.501 64.5s79.167 21.5 124.5 21.5c20.667 0 41.5 -1.83301 62.5 -5.5
s39.5 -8.83398 55.5 -15.501l-3 -46c-0.666992 -9.33301 -7 -14 -19 -14c-8 0 -18.833 1 -32.5 3s-30.5 3 -50.5 3c-32.667 0 -62.834 -4.83301 -90.501 -14.5s-51.5 -25.167 -71.5 -46.5s-35.5 -49.166 -46.5 -83.499s-16.5 -76.5 -16.5 -126.5v-123h323v-71h-321v-893h-95
z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="1009" 
d="M490 1012c43.333 0 83.499 -5.33008 120.499 -15.9971s70.167 -25.667 99.5 -45h255v-33c0 -16.667 -9.33301 -25.667 -28 -27l-151 -11c18.667 -25.333 33 -53.833 43 -85.5s15 -65.5 15 -101.5c0 -48 -8.66699 -91.5 -26 -130.5s-41.5 -72.333 -72.5 -100
s-68.167 -49.167 -111.5 -64.5s-91.333 -23 -144 -23c-57.333 0 -109.333 8.66699 -156 26c-28 -16 -50 -35 -66 -57s-24 -43.333 -24 -64c0 -28.667 10 -50.667 30 -66s46.5 -26.5 79.5 -33.5s70.667 -11.5 113 -13.5s85.333 -4.16699 129 -6.5s86.667 -6.5 129 -12.5
s80 -16.5 113 -31.5s59.5 -36 79.5 -63s30 -62.833 30 -107.5c0 -41.333 -10.333 -81 -31 -119s-50.5 -71.667 -89.5 -101s-86.167 -52.833 -141.5 -70.5s-117.333 -26.5 -186 -26.5c-70 0 -131.333 7.16699 -184 21.5s-96.834 33.666 -132.501 57.999
s-62.5 52.5 -80.5 84.5s-27 66 -27 102c0 52.667 17.333 98 52 136s82.334 67.333 143.001 88c-33.333 11.333 -59.833 27.666 -79.5 48.999s-29.5 51 -29.5 89c0 14 2.66699 28.667 8 44s13.333 30.5 24 45.5s23.5 29.167 38.5 42.5s32.5 25.333 52.5 36
c-47.333 27.333 -84.166 63.666 -110.499 108.999s-39.5 98.333 -39.5 159c0 48 8.5 91.5 25.5 130.5s41 72.5 72 100.5s68.5 49.667 112.5 65s92.667 23 146 23zM858.999 -66.9971c0 31.333 -8.16797 56.499 -24.501 75.499s-38.166 33.833 -65.499 44.5
s-58.833 18.167 -94.5 22.5s-73 7.5 -112 9.5l-117.5 6c-39.333 2 -76 6 -110 12c-25.333 -10 -48.833 -21.5 -70.5 -34.5s-40.167 -27.667 -55.5 -44s-27.333 -34.333 -36 -54s-13 -41.167 -13 -64.5c0 -29.333 7.5 -56.5 22.5 -81.5s37 -46.667 66 -65
s64.5 -32.833 106.5 -43.5s90.333 -16 145 -16c50.667 0 97.834 5.66699 141.501 17s81.667 27.333 114 48s57.666 45.334 75.999 74.001s27.5 60 27.5 94zM489.998 440.002c42.667 0 80.666 6.16699 113.999 18.5s61.333 29.5 84 51.5s39.834 48.333 51.501 79
s17.5 64.667 17.5 102s-6 71.5 -18 102.5s-29.5 57.5 -52.5 79.5s-51 39 -84 51s-70.5 18 -112.5 18s-79.667 -6 -113 -18s-61.5 -29 -84.5 -51s-40.5 -48.5 -52.5 -79.5s-18 -65.167 -18 -102.5s6 -71.333 18 -102s29.5 -57 52.5 -79s51.167 -39.167 84.5 -51.5
s71 -18.5 113 -18.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="1082" 
d="M162 0l-0.000976562 1455h95v-630c46 56.667 98.5 101.834 157.5 135.501s125.167 50.5 198.5 50.5c54.667 0 102.834 -8.66699 144.501 -26s76.167 -42.333 103.5 -75s48 -72 62 -118s21 -98 21 -156v-636h-95v636c0 93.333 -21.333 166.5 -64 219.5
s-108 79.5 -196 79.5c-65.333 0 -126 -16.833 -182 -50.5s-106 -79.834 -150 -138.501v-746h-95z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="475" 
d="M285 995v-995h-95v995h95zM327 1338c0 -12 -2.5 -23.167 -7.5 -33.5s-11.5 -19.5 -19.5 -27.5s-17.333 -14.333 -28 -19s-22 -7 -34 -7s-23.333 2.33301 -34 7s-20 11 -28 19s-14.333 17.167 -19 27.5s-7 21.5 -7 33.5s2.33301 23.5 7 34.5s11 20.5 19 28.5
s17.333 14.333 28 19s22 7 34 7s23.333 -2.33301 34 -7s20 -11 28 -19s14.5 -17.5 19.5 -28.5s7.5 -22.5 7.5 -34.5z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="471" 
d="M285 995l-0.00195312 -1122c0 -34.667 -5 -66.667 -15 -96s-24.833 -54.833 -44.5 -76.5s-44.334 -38.667 -74.001 -51s-64.5 -18.5 -104.5 -18.5c-19.333 0 -36.666 1.66699 -51.999 5s-30.333 8.66602 -45 15.999l5 48c0.666992 8 5.33398 12 14.001 12
c5.33301 0 12.833 -1.33301 22.5 -4s22.834 -4 39.501 -4c54 0 94 14.667 120 44s39 71 39 125v1122h95zM326.998 1338c0 -12 -2.5 -23.167 -7.5 -33.5s-11.5 -19.5 -19.5 -27.5s-17.333 -14.333 -28 -19s-22 -7 -34 -7s-23.333 2.33301 -34 7s-20 11 -28 19
s-14.333 17.167 -19 27.5s-7 21.5 -7 33.5s2.33301 23.5 7 34.5s11 20.5 19 28.5s17.333 14.333 28 19s22 7 34 7s23.333 -2.33301 34 -7s20 -11 28 -19s14.5 -17.5 19.5 -28.5s7.5 -22.5 7.5 -34.5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="984" 
d="M268 1455l-0.00195312 -887.001h46c9.33301 0 18.166 1.33301 26.499 4s17.5 9 27.5 19l399 379c7.33301 7.33301 14.833 13.333 22.5 18s17.5 7 29.5 7h84l-439 -417l-19 -18.5c-6 -5.66699 -12.667 -10.5 -20 -14.5c10.667 -5.33301 19.667 -11.666 27 -18.999l23 -24
l461 -502h-83c-9.33301 -0 -17.666 1.83301 -24.999 5.5s-14.666 9.5 -21.999 17.5l-418 448c-11.333 12 -21.666 20.167 -30.999 24.5s-24 6.5 -44 6.5h-45v-502h-96v1455h96z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="475" 
d="M285 1455v-1455h-95v1455h95z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1591" 
d="M162 0l0.00195312 994.996h52c17.333 0 27.333 -8.33301 30 -25l9 -144c19.333 27.333 40 52.333 62 75s45.5 42.167 70.5 58.5s51.667 29 80 38s58.166 13.5 89.499 13.5c72.667 0 130.667 -21.167 174 -63.5s72.666 -101.166 87.999 -176.499
c12 41.333 28.833 77 50.5 107s46.5 54.833 74.5 74.5s58.5 34.334 91.5 44.001s66.833 14.5 101.5 14.5c49.333 0 93.666 -8.16699 132.999 -24.5s72.833 -40.5 100.5 -72.5s48.834 -71.333 63.501 -118s22 -100 22 -160v-636h-96v636c0 97.333 -21 171.5 -63 222.5
s-102 76.5 -180 76.5c-34.667 0 -67.834 -6.33301 -99.501 -19s-59.667 -31.5 -84 -56.5s-43.666 -56.167 -57.999 -93.5s-21.5 -80.666 -21.5 -129.999v-636h-95v636c0 96.667 -19.333 170.667 -58 222s-95 77 -169 77c-54 0 -104 -16.167 -150 -48.5
s-87 -77.166 -123 -134.499v-752h-95z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="1082" 
d="M162 0v995.001h52c17.333 0 27.333 -8.33301 30 -25l9 -150c45.333 57.333 98.166 103.5 158.499 138.5s127.5 52.5 201.5 52.5c54.667 0 102.834 -8.66699 144.501 -26s76.167 -42.333 103.5 -75s48 -72 62 -118s21 -98 21 -156v-636h-95v636
c0 93.333 -21.333 166.5 -64 219.5s-108 79.5 -196 79.5c-65.333 0 -126 -16.833 -182 -50.5s-106 -79.834 -150 -138.501v-746h-95z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="1082" 
d="M541 1011c71.333 0 135.166 -12.167 191.499 -36.5s103.833 -58.833 142.5 -103.5s68.167 -98.5 88.5 -161.5s30.5 -133.5 30.5 -211.5s-10.167 -148.333 -30.5 -211s-49.833 -116.334 -88.5 -161.001s-86.167 -79 -142.5 -103s-120.166 -36 -191.499 -36
s-135.166 12 -191.499 36s-104 58.333 -143 103s-68.667 98.334 -89 161.001s-30.5 133 -30.5 211s10.167 148.5 30.5 211.5s50 116.833 89 161.5s86.667 79.167 143 103.5s120.166 36.5 191.499 36.5zM541 62c59.333 0 111.166 10.167 155.499 30.5s81.333 49.5 111 87.5
s51.834 83.833 66.501 137.5s22 113.834 22 180.501c0 66 -7.33301 126 -22 180s-36.834 100.167 -66.501 138.5s-66.667 67.833 -111 88.5s-96.166 31 -155.499 31s-111.166 -10.333 -155.499 -31s-81.333 -50.167 -111 -88.5s-52 -84.5 -67 -138.5s-22.5 -114 -22.5 -180
c0 -66.667 7.5 -126.834 22.5 -180.501s37.333 -99.5 67 -137.5s66.667 -67.167 111 -87.5s96.166 -30.5 155.499 -30.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1070" 
d="M162 -352l0.000976562 1347h52c8.66699 0 15.667 -1.83301 21 -5.5s8.33301 -10.167 9 -19.5l9 -154c44 60 95.5 107.667 154.5 143s125.167 53 198.5 53c122.667 0 218 -42.333 286 -127s102 -211.667 102 -381c0 -72.667 -9.5 -140.5 -28.5 -203.5
s-46.833 -117.667 -83.5 -164s-81.834 -82.833 -135.501 -109.5s-115.5 -40 -185.5 -40c-68 0 -126.667 12.333 -176 37s-92 61.334 -128 110.001v-486h-95zM579.001 934.999c-66 0 -125.832 -17.168 -179.499 -51.501s-101.167 -82.166 -142.5 -143.499v-522
c38 -57.333 80.167 -97.666 126.5 -120.999s99.166 -35 158.499 -35c58.667 0 110 10.667 154 32s80.833 51.5 110.5 90.5s52 85.667 67 140s22.5 114.166 22.5 179.499c0 148 -27.167 256.833 -81.5 326.5s-132.833 104.5 -235.5 104.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="1092" 
d="M920 995l-0.000976562 -1347h-96v529c-44 -58.667 -95.167 -105 -153.5 -139s-123.5 -51 -195.5 -51c-122 0 -217.167 42.333 -285.5 127s-102.5 211.667 -102.5 381c0 72.667 9.5 140.5 28.5 203.5s47 117.667 84 164s82.333 82.833 136 109.5s115.5 40 185.5 40
c68.667 0 128.167 -13 178.5 -39s93.5 -64.333 129.5 -115l9 112c0.666992 16.667 10.334 25 29.001 25h53zM501.999 64c66 0 125.666 16.999 178.999 50.999s101 81.333 143 142v526c-36 54.667 -77.667 94 -125 118s-100.666 36 -159.999 36
c-58.667 0 -110 -10.667 -154 -32s-80.833 -51.5 -110.5 -90.5s-52 -85.667 -67 -140s-22.5 -114.166 -22.5 -179.499c0 -148 27.333 -256.833 82 -326.5s133 -104.5 235 -104.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="794" 
d="M162 0l-0.000976562 994.999h50c11.333 0 19.5 -2.33301 24.5 -7s7.83301 -12.667 8.5 -24l8 -210c32 80.667 74.667 143.834 128 189.501s119 68.5 197 68.5c30 0 57.667 -3.16699 83 -9.5s49.333 -15.5 72 -27.5l-13 -66c-2 -10.667 -8.66699 -16 -20 -16
c-4 0 -9.66699 1.33301 -17 4s-16.5 5.66699 -27.5 9s-24.5 6.33301 -40.5 9s-34.333 4 -55 4c-75.333 0 -137.166 -23.333 -185.499 -70s-87.5 -114 -117.5 -202v-647h-95z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="860" 
d="M710 872c-4.66699 -9.33301 -12 -14.002 -22 -14.002c-7.33301 0 -16.833 4.16699 -28.5 12.5s-27 17.666 -46 27.999s-42.333 19.666 -70 27.999s-61.5 12.5 -101.5 12.5c-36 0 -68.833 -5.16699 -98.5 -15.5s-55 -24.166 -76 -41.499s-37.333 -37.5 -49 -60.5
s-17.5 -47.167 -17.5 -72.5c0 -31.333 8 -57.333 24 -78s37 -38.334 63 -53.001s55.5 -27.334 88.5 -38.001l101.5 -32c34.667 -10.667 68.5 -22.5 101.5 -35.5s62.5 -29.167 88.5 -48.5s47 -43 63 -71s24 -62 24 -102c0 -43.333 -7.83301 -83.666 -23.5 -120.999
s-38.334 -69.666 -68.001 -96.999s-66.167 -49 -109.5 -65s-92.666 -24 -147.999 -24c-70 0 -130.333 11.167 -181 33.5s-96 51.5 -136 87.5l23 34c3.33301 5.33301 7 9.33301 11 12s9.66699 4 17 4c8.66699 0 19.334 -5.33301 32.001 -16s29.167 -22.167 49.5 -34.5
s45.666 -23.833 75.999 -34.5s67.833 -16 112.5 -16c42 0 79 5.83301 111 17.5s58.667 27.5 80 47.5s37.5 43.5 48.5 70.5s16.5 55.5 16.5 85.5c0 33.333 -8 61 -24 83s-37 40.667 -63 56s-55.5 28.333 -88.5 39l-102 32c-35 10.667 -69 22.334 -102 35.001
s-62.5 28.667 -88.5 48s-47 42.833 -63 70.5s-24 62.167 -24 103.5c0 35.333 7.66699 69.333 23 102s37.166 61.334 65.499 86.001s62.833 44.334 103.5 59.001s86.334 22 137.001 22c60.667 0 114.5 -8.66699 161.5 -26s90.167 -44 129.5 -80z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="713" 
d="M434 -16c-67.333 0 -119.831 18.668 -157.498 56.001s-56.5 95 -56.5 173v680h-143c-7.33301 0 -13.333 2 -18 6s-7 9.66699 -7 17v37l170 12l24 356c0.666992 6 3.16699 11.333 7.5 16s10.166 7 17.499 7h45v-380h313v-71h-313v-675c0 -27.333 3.5 -50.833 10.5 -70.5
s16.667 -35.834 29 -48.501s26.833 -22 43.5 -28s34.667 -9 54 -9c24 0 44.667 3.5 62 10.5s32.333 14.667 45 23s23 16 31 23s14.333 10.5 19 10.5c5.33301 0 10.666 -3.33301 15.999 -10l26 -42c-25.333 -28 -57.5 -50.5 -96.5 -67.5s-79.5 -25.5 -121.5 -25.5z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="1082" 
d="M233 995l-0.000976562 -636c0 -93.333 21.333 -166.5 64 -219.5s107.667 -79.5 195 -79.5c64.667 0 125 16.5 181 49.5s106.333 78.833 151 137.5v748h96v-995h-53c-18.667 0 -28.334 8.66699 -29.001 26l-9 148c-46 -57.333 -99.167 -103.333 -159.5 -138
s-127.5 -52 -201.5 -52c-55.333 0 -103.666 8.66699 -144.999 26s-75.666 42.333 -102.999 75s-48 72 -62 118s-21 98 -21 156v636h96z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="974" 
d="M529 0l-84.001 0.000976562l-420 995h75c9.33301 0 16.833 -2.5 22.5 -7.5s9.83398 -10.5 12.501 -16.5l328 -785c6.66699 -15.333 11.667 -30 15 -44l10 -42l10.5 42.5c3.66699 14.333 8.5 28.833 14.5 43.5l330 785c3.33301 7.33301 7.83301 13.166 13.5 17.499
s12.5 6.5 20.5 6.5h72z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1474" 
d="M22 995l72.999 0.000976562c9.33301 0 17 -2.5 23 -7.5s10 -10.5 12 -16.5l251 -785c4.66699 -15.333 8.33398 -30 11.001 -44l8 -42c3.33301 14 7 28 11 42s8.66699 28.667 14 44l267 792c4.66699 14.667 14 22 28 22h39c14.667 0 24.334 -7.33301 29.001 -22l263 -792
c10 -30 18 -59 24 -87l8.5 42.5c3 14.333 7.16699 29.166 12.5 44.499l252 785c5.33301 16 16.666 24 33.999 24h70l-333 -995h-70c-10.667 0 -18.334 7 -23.001 21l-273 808c-3.33301 10 -6.33301 20 -9 30s-5 20 -7 30c-2 -10 -4.16699 -20 -6.5 -30
s-5.16602 -20 -8.49902 -30l-276 -808c-4 -14 -12 -21 -24 -21h-67z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="921" 
d="M395 509l-346.001 485.999h91c9.33301 0 16.166 -2 20.499 -6s8.16602 -8.66699 11.499 -14l292 -422c2.66699 11.333 8.33398 23.333 17.001 36l273 385c3.33301 6 7.5 11 12.5 15s10.833 6 17.5 6h88l-348 -481l362 -514h-91c-9.33301 0 -16.666 2.83301 -21.999 8.5
s-9.66602 11.167 -12.999 16.5l-302 440c-3.33301 -13.333 -8.33301 -24.666 -15 -33.999l-290 -408c-4.66699 -6 -9.5 -11.333 -14.5 -16s-11.167 -7 -18.5 -7h-85z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="974" 
d="M379 -320c-4 -9.33301 -9.16797 -16.999 -15.501 -22.999s-15.5 -9 -27.5 -9h-68l175 389l-421 958h79c10 0 17.833 -2.5 23.5 -7.5s9.83398 -10.5 12.501 -16.5l335 -774c4 -10 7.5 -20.167 10.5 -30.5s5.83301 -20.833 8.5 -31.5
c3.33301 10.667 6.83301 21.167 10.5 31.5s7.5 20.5 11.5 30.5l331 774c3.33301 7.33301 8 13.166 14 17.499s12.667 6.5 20 6.5h73z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="904" 
d="M826 955c0 -14 -4.66699 -26.666 -14 -37.999l-623 -842h612v-75h-727v38c0 11.333 5 23.666 15 36.999l625 845h-607v75h719v-40z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="600" 
d="M219 445c0 22 -3.66699 42.1631 -11 60.4961s-17.833 34.333 -31.5 48s-30.167 24.167 -49.5 31.5s-40.666 11 -63.999 11v61c23.333 0 44.666 3.66699 63.999 11s35.833 17.833 49.5 31.5s24.167 29.667 31.5 48s11 38.5 11 60.5c0 36.667 -3.16699 72.334 -9.5 107.001
s-13.166 69.334 -20.499 104.001s-14.166 69.5 -20.499 104.5s-9.5 71.167 -9.5 108.5c0 40.667 6.33301 78.334 19 113.001s31 64.834 55 90.501s53.5 45.667 88.5 60s74.5 21.5 118.5 21.5h55v-42c0 -7.33301 -2.83301 -13 -8.5 -17s-11.167 -6 -16.5 -6h-33
c-28.667 0 -54.834 -5 -78.501 -15s-44 -24.333 -61 -43s-30.333 -41.334 -40 -68.001s-14.5 -56.334 -14.5 -89.001c0 -38 3 -75 9 -111s12.667 -71.5 20 -106.5s14 -70 20 -105s9 -70.167 9 -105.5c0 -24.667 -4.16699 -47.334 -12.5 -68.001s-19.333 -38.667 -33 -54
s-29.334 -28.166 -47.001 -38.499s-36.167 -17.833 -55.5 -22.5c19.333 -4 37.833 -11.167 55.5 -21.5s33.334 -23.333 47.001 -39s24.667 -33.667 33 -54s12.5 -42.833 12.5 -67.5c0 -35.333 -3 -70.5 -9 -105.5s-12.667 -70.167 -20 -105.5s-14 -71 -20 -107
s-9 -72.667 -9 -110c0 -32.667 4.83301 -62.334 14.5 -89.001s23 -49.334 40 -68.001s37.333 -33 61 -43s49.834 -15 78.501 -15h33c5.33301 0 10.833 -2 16.5 -6s8.5 -9.66699 8.5 -17v-42h-55c-44 0 -83.5 7.16699 -118.5 21.5s-64.5 34.333 -88.5 60
s-42.333 55.834 -55 90.501s-19 72.334 -19 113.001c0 37.333 3.16699 73.5 9.5 108.5s13.166 69.833 20.499 104.5s14.166 69.334 20.499 104.001s9.5 70.334 9.5 107.001z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="600" 
d="M262 1517h75v-1869h-75v1869z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="600" 
d="M381 445c0 -36.667 3.16699 -72.3301 9.5 -106.997s13.166 -69.334 20.499 -104.001s14.166 -69.5 20.499 -104.5s9.5 -71.167 9.5 -108.5c0 -40.667 -6.33301 -78.334 -19 -113.001s-31 -64.834 -55 -90.501s-53.5 -45.667 -88.5 -60s-74.5 -21.5 -118.5 -21.5h-55v42
c0 7.33301 2.66699 13 8 17s11 6 17 6h33c28.667 0 54.834 5 78.501 15s44 24.333 61 43s30.333 41.334 40 68.001s14.5 56.334 14.5 89.001c0 37.333 -3 74 -9 110s-12.667 71.667 -20 107s-14 70.5 -20 105.5s-9 70.167 -9 105.5c0 24.667 4 47.167 12 67.5
s18.833 38.333 32.5 54s29.334 28.667 47.001 39s36.167 17.5 55.5 21.5c-19.333 4.66699 -37.833 12.167 -55.5 22.5s-33.334 23.166 -47.001 38.499s-24.5 33.333 -32.5 54s-12 43.334 -12 68.001c0 35.333 3 70.5 9 105.5s12.667 70 20 105s14 70.5 20 106.5s9 73 9 111
c0 32.667 -4.83301 62.334 -14.5 89.001s-23 49.334 -40 68.001s-37.333 33 -61 43s-49.834 15 -78.501 15h-33c-6 0 -11.667 2 -17 6s-8 9.66699 -8 17v42h55c44 0 83.5 -7.16699 118.5 -21.5s64.5 -34.333 88.5 -60s42.333 -55.834 55 -90.501s19 -72.334 19 -113.001
c0 -37.333 -3.16699 -73.5 -9.5 -108.5s-13.166 -69.833 -20.499 -104.5s-14.166 -69.334 -20.499 -104.001s-9.5 -70.334 -9.5 -107.001c0 -22 3.66699 -42.167 11 -60.5s17.833 -34.333 31.5 -48s30 -24.167 49 -31.5s40.167 -11 63.5 -11v-61
c-23.333 0 -44.5 -3.66699 -63.5 -11s-35.333 -17.833 -49 -31.5s-24.167 -29.667 -31.5 -48s-11 -38.5 -11 -60.5z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M779 567c24.667 0 47.1641 4.5 67.4971 13.5s37.5 21.5 51.5 37.5s24.833 35 32.5 57s11.5 46 11.5 72h78c0 -36.667 -5.33301 -70.667 -16 -102s-26 -58.666 -46 -81.999s-44.667 -41.666 -74 -54.999s-62.666 -20 -99.999 -20c-34 0 -68.833 6.5 -104.5 19.5
s-71 27.5 -106 43.5s-68.833 30.5 -101.5 43.5s-63.334 19.5 -92.001 19.5c-25.333 0 -47.833 -4.5 -67.5 -13.5s-36.5 -21.5 -50.5 -37.5s-24.833 -35 -32.5 -57s-11.834 -46 -12.501 -72h-77c0 36.667 5.33301 70.667 16 102s26 58.666 46 81.999s44.5 41.666 73.5 54.999
s62.167 20 99.5 20c34.667 0 69.834 -6.5 105.501 -19.5s70.834 -27.5 105.501 -43.5s68.334 -30.5 101.001 -43.5s63.334 -19.5 92.001 -19.5z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="386" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="645" 
d="M279 -352v550c0 30 0.333008 58.333 1 85s1.5 53.834 2.5 81.501s2.5 56.834 4.5 87.501s4 64.667 6 102h65c2 -37.333 4 -71.333 6 -102s3.5 -59.834 4.5 -87.501s1.83301 -54.834 2.5 -81.501s1 -55 1 -85v-550h-93zM230 919c0 26 8.83301 47.8301 26.5 65.4971
s39.5 26.5 65.5 26.5c12.667 0 24.667 -2.33301 36 -7s21.166 -11.167 29.499 -19.5s15 -18.166 20 -29.499s7.5 -23.333 7.5 -36c0 -13.333 -2.5 -25.666 -7.5 -36.999s-11.667 -21.166 -20 -29.499s-18.166 -15 -29.499 -20s-23.333 -7.5 -36 -7.5
c-26 0 -47.833 9.16699 -65.5 27.5s-26.5 40.5 -26.5 66.5z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M589 -11c-64 4 -122.334 18.3281 -175.001 42.9951s-98 58.667 -136 102s-67.333 95.5 -88 156.5s-31 130.167 -31 207.5c0 74.667 10.667 143.167 32 205.5s52.5 116 93.5 161s91.333 80.333 151 106s127.834 39.167 204.501 40.5l10 205
c0.666992 8.66699 3.5 16.167 8.5 22.5s12.167 9.5 21.5 9.5h36l-12 -240c58 -5.33301 109.5 -17.666 154.5 -36.999s85.167 -44.333 120.5 -75l-24 -33c-3.33301 -3.33301 -6.5 -6.16602 -9.5 -8.49902s-7.16699 -3.5 -12.5 -3.5c-6 0 -14.5 3.83301 -25.5 11.5
s-25.667 16.334 -44 26.001s-40.833 19 -67.5 28s-58.667 15.167 -96 18.5l-43 -874c50 1.33301 92 7.66602 126 18.999s62.167 23.5 84.5 36.5s40 24.833 53 35.5s23.167 16 30.5 16c5.33301 0 9.33301 -0.666992 12 -2s5.33398 -3.66602 8.00098 -6.99902l24 -31
c-15.333 -19.333 -34.5 -37.166 -57.5 -53.499s-49.167 -30.666 -78.5 -42.999s-61.333 -22.333 -96 -30s-71.334 -12.167 -110.001 -13.5l-10 -210c-0.666992 -8.66699 -3.5 -16 -8.5 -22s-12.167 -9 -21.5 -9h-36zM252.999 497.995
c0 -65.333 7.83105 -123.833 23.498 -175.5s38.334 -96 68.001 -133s65.334 -66.167 107.001 -87.5s88.5 -34.333 140.5 -39l44 873c-61.333 -2 -115.833 -13.5 -163.5 -34.5s-87.667 -50.333 -120 -88s-57 -83.167 -74 -136.5s-25.5 -113 -25.5 -179z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" 
d="M67 664c0 10 3.16895 18.332 9.50195 24.999s15.166 10 26.499 10h165v294c0 62 8.66699 119.667 26 173s43.166 99.5 77.499 138.5s77.333 69.667 129 92s111.5 33.5 179.5 33.5c50.667 0 95.334 -6.16699 134.001 -18.5s72.5 -29.166 101.5 -50.499
s53.833 -46.333 74.5 -75s38.334 -59 53.001 -91l-38 -22c-6 -3.33301 -13.333 -5 -22 -5c-11.333 0 -21.333 5.66699 -30 17c-13.333 20.667 -27.5 40.834 -42.5 60.501s-32.833 37.167 -53.5 52.5s-45.334 27.5 -74.001 36.5s-63 13.5 -103 13.5
c-52 0 -97.5 -8.66699 -136.5 -26s-71.833 -41.5 -98.5 -72.5s-46.667 -68.333 -60 -112s-20 -91.834 -20 -144.501v-294h479v-39c0 -7.33301 -2.83301 -13.833 -8.5 -19.5s-12.834 -8.5 -21.501 -8.5h-449v-304c0 -59.333 -12 -109.166 -36 -149.499
s-57 -74.833 -99 -103.5c14.667 2.66699 29.167 4.66699 43.5 6s29.166 2 44.499 2h787v-40c0 -10 -4 -19.667 -12 -29s-18.667 -14 -32 -14h-970v65c22.667 8.66699 44.667 19.667 66 33s40.333 29.666 57 48.999s29.834 42 39.501 68s14.5 56.333 14.5 91v326h-201v32z
" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M237 671c0 41.333 6.66699 80.166 20 116.499s32 69.5 56 99.5l-154 154l49 50l154 -154c30 24 63.333 43 100 57s76 21 118 21c41.333 0 80.166 -6.83301 116.499 -20.5s69.5 -32.5 99.5 -56.5l154 154l49 -50l-153 -153c24 -30 43 -63.333 57 -100s21 -76 21 -118
c0 -41.333 -6.83301 -80.166 -20.5 -116.499s-32.5 -69.5 -56.5 -99.5l154 -154l-50 -50l-154 154c-30 -24 -63.333 -42.833 -100 -56.5s-75.667 -20.5 -117 -20.5s-80.166 6.66699 -116.499 20s-69.5 32 -99.5 56l-155 -154l-49 50l154 153
c-24 30 -42.833 63.333 -56.5 100s-20.5 76 -20.5 118zM309 671c0 -37.333 7.16699 -72.333 21.5 -105s33.666 -61.167 57.999 -85.5s53 -43.666 86 -57.999s68.167 -21.5 105.5 -21.5s72.5 7.16699 105.5 21.5s61.833 33.666 86.5 57.999s44.167 52.833 58.5 85.5
s21.5 67.667 21.5 105s-7.16699 72.5 -21.5 105.5s-33.833 61.833 -58.5 86.5s-53.5 44.167 -86.5 58.5s-68.167 21.5 -105.5 21.5s-72.5 -7.16699 -105.5 -21.5s-61.667 -33.833 -86 -58.5s-43.666 -53.5 -57.999 -86.5s-21.5 -68.167 -21.5 -105.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" 
d="M161 616h346.003l-422 799h79c17.333 0 30.333 -8.33301 39 -25l346 -665l14.5 -37.5c4.33301 -11.667 8.16602 -23.167 11.499 -34.5c6 22 14.667 46 26 72l345 665c3.33301 7.33301 8.16602 13.333 14.499 18s14.166 7 23.499 7h80l-423 -799h347v-58h-366v-118h366
v-58h-366v-382h-95v382h-366v58h366v118h-366v58z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="600" 
d="M262 1517h75v-778h-75v778zM262 425h75v-777h-75v777z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1002" 
d="M808 1291c-4.66699 -8.66699 -12.0049 -13.0039 -22.0049 -13.0039c-7.33301 0 -16.833 4.16699 -28.5 12.5s-27 17.5 -46 27.5s-42.333 19.167 -70 27.5s-61.5 12.5 -101.5 12.5c-37.333 0 -71.166 -5.16699 -101.499 -15.5s-56.166 -24.333 -77.499 -42
s-37.833 -38.167 -49.5 -61.5s-17.5 -48 -17.5 -74c0 -30.667 8.33301 -57.334 25 -80.001s38.667 -43.167 66 -61.5s58.333 -35.166 93 -50.499l106.5 -47c36.333 -16 71.833 -33.167 106.5 -51.5s65.667 -39.166 93 -62.499s49.333 -50.5 66 -81.5s25 -67.167 25 -108.5
c0 -55.333 -14 -103.5 -42 -144.5s-69.333 -73.167 -124 -96.5c36 -25.333 65.167 -54.333 87.5 -87s33.5 -72 33.5 -118c0 -43.333 -7.83301 -83.666 -23.5 -120.999s-38.334 -69.666 -68.001 -96.999s-66.167 -49 -109.5 -65s-92.666 -24 -147.999 -24
c-70 0 -130.333 11.167 -181 33.5s-96 51.5 -136 87.5l23 34c3.33301 5.33301 7 9.33301 11 12s9.66699 4 17 4c8.66699 0 19.334 -5.33301 32.001 -16s29.334 -22.167 50.001 -34.5s46.334 -23.833 77.001 -34.5s68.667 -16 114 -16c41.333 0 78 5.66699 110 17
s59 27 81 47s38.667 43.5 50 70.5s17 56.167 17 87.5c0 34 -8.66699 63.667 -26 89s-40.333 47.833 -69 67.5s-61.167 37.5 -97.5 53.5l-111 48c-37.667 16 -74.667 32.667 -111 50s-68.833 37.5 -97.5 60.5s-51.667 49.333 -69 79s-26 64.834 -26 105.501
c0 51.333 15.167 97.5 45.5 138.5s79.833 73.833 148.5 98.5c-36.667 24.667 -66.5 53.5 -89.5 86.5s-34.5 74.167 -34.5 123.5c0 36 7.66699 70.167 23 102.5s37.166 61 65.499 86s62.833 44.833 103.5 59.5s86.334 22 137.001 22c60.667 0 114.5 -8.83301 161.5 -26.5
s90.167 -44.167 129.5 -79.5zM223.995 726.996c0 -27.333 6.50098 -51.833 19.501 -73.5s30.667 -41.334 53 -59.001s48.166 -34 77.499 -49s60 -29.5 92 -43.5l97.5 -42.5c33 -14.333 64.5 -29.833 94.5 -46.5c47.333 21.333 81.333 48 102 80s31 68.333 31 109
c0 30 -6 56.667 -18 80s-28.167 44.166 -48.5 62.499s-44 34.833 -71 49.5s-55.5 28.834 -85.5 42.501l-91.5 40c-31 13 -60.833 27.167 -89.5 42.5c-59.333 -25.333 -101.333 -53.666 -126 -84.999s-37 -67 -37 -107z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="585" 
d="M203 1284c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18.167 -18 -25.5s-16.166 -13.166 -26.499 -17.499s-20.833 -6.5 -31.5 -6.5s-20.834 2.16699 -30.501 6.5s-18.334 10.166 -26.001 17.499s-13.667 15.833 -18 25.5s-6.5 19.834 -6.5 30.501
c0 11.333 2.16699 22 6.5 32s10.333 18.833 18 26.5s16.334 13.667 26.001 18s19.834 6.5 30.501 6.5s21.167 -2.16699 31.5 -6.5s19.166 -10.333 26.499 -18s13.333 -16.5 18 -26.5s7 -20.667 7 -32zM546 1284c0 -10.667 -2.33301 -20.834 -7 -30.501
s-10.667 -18.167 -18 -25.5s-16.166 -13.166 -26.499 -17.499s-20.833 -6.5 -31.5 -6.5c-11.333 0 -21.833 2.16699 -31.5 6.5s-18.334 10.166 -26.001 17.499s-13.667 15.833 -18 25.5s-6.5 19.834 -6.5 30.501c0 11.333 2.16699 22 6.5 32s10.333 18.833 18 26.5
s16.334 13.667 26.001 18s20.167 6.5 31.5 6.5c10.667 0 21.167 -2.16699 31.5 -6.5s19.166 -10.333 26.499 -18s13.333 -16.5 18 -26.5s7 -20.667 7 -32z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1613" 
d="M1080 414c8 6.66699 15.6611 10.002 22.9941 10.002c3.33301 0 6.33301 -0.666992 9 -2s4.66699 -2.66602 6 -3.99902l31 -31c-39.333 -39.333 -85.666 -70.666 -138.999 -93.999s-118.666 -35 -195.999 -35c-64 0 -122.833 10.833 -176.5 32.5s-99.667 52.167 -138 91.5
s-68.166 86.666 -89.499 141.999s-32 117 -32 185c0 66.667 11.167 127.667 33.5 183s53.5 102.833 93.5 142.5s87.667 70.5 143 92.5s116 33 182 33c34.667 0 66.5 -2.5 95.5 -7.5s55.833 -12.333 80.5 -22s47.667 -21.334 69 -35.001s42.333 -29.5 63 -47.5l-24 -33
c-4.66699 -7.33301 -11.334 -11 -20.001 -11c-6.66699 0 -15.5 4.33301 -26.5 13s-26.167 18.167 -45.5 28.5s-44.166 19.833 -74.499 28.5s-68.166 13 -113.499 13c-55.333 0 -105.666 -8.83301 -150.999 -26.5s-84.166 -42.834 -116.499 -75.501
s-57.333 -72.334 -75 -119.001s-26.5 -99 -26.5 -157c0 -60 8.83301 -113.333 26.5 -160s42.167 -86.167 73.5 -118.5s68.5 -57 111.5 -74s90.167 -25.5 141.5 -25.5c56.667 0 105.5 6.83301 146.5 20.5s79.5 34.5 115.5 62.5zM84.9941 708.002
c0 66 8.66797 129.833 26.001 191.5s41.5 119.167 72.5 172.5s68.5 102 112.5 146s92.667 81.667 146 113s110.666 55.666 171.999 72.999s125.333 26 192 26s130.667 -8.66699 192 -26s118.833 -41.666 172.5 -72.999s102.5 -69 146.5 -113s81.5 -92.667 112.5 -146
s55.167 -110.833 72.5 -172.5s26 -125.5 26 -191.5c0 -66.667 -8.66699 -130.667 -26 -192s-41.5 -118.666 -72.5 -171.999s-68.5 -102 -112.5 -146s-92.833 -81.667 -146.5 -113s-111.167 -55.666 -172.5 -72.999s-125.333 -26 -192 -26c-66 0 -129.833 8.66699 -191.5 26
s-119.167 41.666 -172.5 72.999s-102 69 -146 113s-81.5 92.667 -112.5 146s-55.167 110.666 -72.5 171.999s-26 125.333 -26 192zM143.995 708.002c0 -92.667 17.167 -179.5 51.5 -260.5s81.5 -151.667 141.5 -212s130.167 -107.833 210.5 -142.5s166.5 -52 258.5 -52
s178.5 17.333 259.5 52s151.5 82.167 211.5 142.5s107.333 131 142 212s52 167.833 52 260.5s-17.333 179.5 -52 260.5s-82 151.833 -142 212.5s-130.5 108.5 -211.5 143.5s-167.5 52.5 -259.5 52.5s-178.167 -17.5 -258.5 -52.5s-150.5 -82.833 -210.5 -143.5
s-107.167 -131.5 -141.5 -212.5s-51.5 -167.833 -51.5 -260.5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="671" 
d="M565 839h-32.001c-7.33301 0 -12.666 1.33301 -15.999 4s-6.66602 7.66699 -9.99902 15l-10 68c-16.667 -15.333 -33.167 -29 -49.5 -41s-33.333 -22 -51 -30s-36.667 -14.167 -57 -18.5s-42.5 -6.5 -66.5 -6.5c-22 0 -43 3 -63 9s-37.667 15.167 -53 27.5
s-27.666 28 -36.999 47s-14 41.5 -14 67.5c0 24 7 46.833 21 68.5s36.5 40.667 67.5 57s71.167 29.666 120.5 39.999s109 16.166 179 17.499v51c0 50.667 -11.333 89.167 -34 115.5s-58.334 39.5 -107.001 39.5c-28.667 0 -52.834 -3.66699 -72.501 -11
s-36.167 -15.166 -49.5 -23.499s-24.5 -16.166 -33.5 -23.499s-17.167 -11 -24.5 -11c-10 0 -17.333 4.33301 -22 13l-12 23c32 32 65.667 55.5 101 70.5s75.333 22.5 120 22.5c67.333 0 118.333 -19.5 153 -58.5s52 -91.167 52 -156.5v-376zM291.999 883
c22 0 42.334 2.33496 61.001 7.00195s36.167 11.167 52.5 19.5s31.666 18.166 45.999 29.499s28.5 23.666 42.5 36.999v138c-112 -3.33301 -193.167 -16.333 -243.5 -39s-75.5 -52.667 -75.5 -90c0 -18 3.16699 -33.5 9.5 -46.5s15 -23.667 26 -32s23.5 -14.333 37.5 -18
s28.667 -5.5 44 -5.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="874" 
d="M155 513v12.999l241 378l30 -15c10.667 -6.66699 16 -15 16 -25c0 -8 -2.33301 -15.667 -7 -23l-183 -293c-8.66699 -13.333 -16 -23 -22 -29c6.66699 -5.33301 14 -14.666 22 -27.999l183 -294c4.66699 -7.33301 7 -15 7 -23c0 -10 -5.33301 -18.333 -16 -25l-30 -15z
M410 512.999v12.999l241 378l30 -15c10.667 -6.66699 16 -15 16 -25c0 -8 -2.33301 -15.667 -7 -23l-183 -293c-8.66699 -13.333 -16 -23 -22 -29c6.66699 -5.33301 14 -14.666 22 -27.999l183 -294c4.66699 -7.33301 7 -15 7 -23c0 -10 -5.33301 -18.333 -16 -25l-30 -15z
" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M164 707h829v-368h-82v295h-747v73z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="665" 
d="M100 634h465v-81h-465v81z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="1613" 
d="M85 708c0 66 8.66797 129.833 26.001 191.5s41.5 119.167 72.5 172.5s68.5 102 112.5 146s92.667 81.667 146 113s110.666 55.666 171.999 72.999s125.333 26 192 26s130.667 -8.66699 192 -26s118.833 -41.666 172.5 -72.999s102.5 -69 146.5 -113
s81.5 -92.667 112.5 -146s55.167 -110.833 72.5 -172.5s26 -125.5 26 -191.5c0 -66.667 -8.66699 -130.667 -26 -192s-41.5 -118.666 -72.5 -171.999s-68.5 -102 -112.5 -146s-92.833 -81.667 -146.5 -113s-111.167 -55.666 -172.5 -72.999s-125.333 -26 -192 -26
c-66 0 -129.833 8.66699 -191.5 26s-119.167 41.666 -172.5 72.999s-102 69 -146 113s-81.5 92.667 -112.5 146s-55.167 110.666 -72.5 171.999s-26 125.333 -26 192zM144.001 708c0 -92.667 17.167 -179.5 51.5 -260.5s81.5 -151.667 141.5 -212
s130.167 -107.833 210.5 -142.5s166.5 -52 258.5 -52s178.5 17.333 259.5 52s151.5 82.167 211.5 142.5s107.333 131 142 212s52 167.833 52 260.5s-17.333 179.5 -52 260.5s-82 151.833 -142 212.5s-130.5 108.5 -211.5 143.5s-167.5 52.5 -259.5 52.5
s-178.167 -17.5 -258.5 -52.5s-150.5 -82.833 -210.5 -143.5s-107.167 -131.5 -141.5 -212.5s-51.5 -167.833 -51.5 -260.5zM624.001 657l-0.000976562 -390.998h-85v885h247c102 0 179.167 -19.167 231.5 -57.5s78.5 -96.833 78.5 -175.5
c0 -66.667 -21.5 -121.334 -64.5 -164.001s-103.5 -69.334 -181.5 -80.001c8 -4.66699 15.167 -10.167 21.5 -16.5s12.5 -14.166 18.5 -23.499l285 -368h-80c-6.66699 0 -12.5 1 -17.5 3s-9.5 6.33301 -13.5 13l-270 351c-5.33301 7.33301 -12.166 13.166 -20.499 17.499
s-21.5 6.5 -39.5 6.5h-110zM624 721.002h147.999c80.667 0 140.834 16.167 180.501 48.5s59.5 78.833 59.5 139.5c0 61.333 -18 106 -54 134s-93.333 42 -172 42h-162v-364z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="585" 
d="M20 1317h545v-65h-545v65z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="786" 
d="M91 1134c0 41.333 7.83301 80.166 23.5 116.499s37 68 64 95s58.833 48.167 95.5 63.5s76 23 118 23s81.333 -7.66699 118 -23s68.5 -36.5 95.5 -63.5s48.333 -58.667 64 -95s23.5 -75.166 23.5 -116.499s-7.83301 -80 -23.5 -116s-37 -67.5 -64 -94.5
s-58.833 -48.333 -95.5 -64s-76 -23.5 -118 -23.5s-81.333 7.83301 -118 23.5s-68.5 37 -95.5 64s-48.333 58.5 -64 94.5s-23.5 74.667 -23.5 116zM162 1134c0 -32 6 -62 18 -90s28.333 -52.333 49 -73s45 -37 73 -49s58 -18 90 -18s62 6 90 18s52.167 28.333 72.5 49
s36.5 45 48.5 73s18 58 18 90s-6 62 -18 90s-28.167 52.5 -48.5 73.5s-44.5 37.5 -72.5 49.5s-58 18 -90 18s-62 -6 -90 -18s-52.333 -28.5 -73 -49.5s-37 -45.5 -49 -73.5s-18 -58 -18 -90z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M617 1153v-406h431v-73h-431v-402h-78v402h-430v73h430v406h78zM109 153h939v-73h-939v73z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="662" 
d="M342 1623c29.333 0 57.002 -4.33105 83.002 -12.998s48.5 -21.167 67.5 -37.5s34 -36.666 45 -60.999s16.5 -52.5 16.5 -84.5c0 -26.667 -4.16699 -51.5 -12.5 -74.5s-19.5 -45 -33.5 -66s-30.333 -41.333 -49 -61l-58 -59.5l-207 -210
c12.667 3.33301 25.5 5.83301 38.5 7.5s26.167 2.5 39.5 2.5h275c16 0 24 -8 24 -24v-42h-474v24c0 5.33301 1 10.666 3 15.999s5.66699 10.333 11 15l235 237c18.667 18.667 36.167 37.334 52.5 56.001s30.666 37.5 42.999 56.5s22 38.5 29 58.5s10.5 40.667 10.5 62
c0 23.333 -3.83301 43.666 -11.5 60.999s-18 31.666 -31 42.999s-28.167 19.833 -45.5 25.5s-35.333 8.5 -54 8.5c-41.333 0 -74 -11.833 -98 -35.5s-42.667 -53.834 -56 -90.501c-4.66699 -12.667 -13.667 -19 -27 -19c-1.33301 0 -4 0.166992 -8 0.5s-7 0.833008 -9 1.5
l-30 5c9.33301 64 34.666 113 75.999 147s93 51 155 51z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="662" 
d="M348 1623c29.333 0 56.665 -4.16895 81.998 -12.502s47.166 -20.333 65.499 -36s32.833 -34.667 43.5 -57s16 -47.833 16 -76.5c0 -44 -12 -80.5 -36 -109.5s-56 -49.833 -96 -62.5c48.667 -11.333 85.834 -31 111.501 -59s38.5 -64 38.5 -108
c0 -32 -6.16699 -60.833 -18.5 -86.5s-29.166 -47.667 -50.499 -66s-46.333 -32.5 -75 -42.5s-59 -15 -91 -15c-40 0 -73.5 5 -100.5 15s-49.667 23.5 -68 40.5s-33 36.667 -44 59s-20.167 45.5 -27.5 69.5l30 13c2.66699 1.33301 5.16699 2.33301 7.5 3
s5.16602 1 8.49902 1c5.33301 0 10.333 -1.33301 15 -4s8 -6.66699 10 -12l3 -8c2.66699 -8 7.16699 -18.667 13.5 -32s15.5 -26.166 27.5 -38.499s27.833 -23.166 47.5 -32.499s44.5 -14 74.5 -14c27.333 0 51.333 4.5 72 13.5s37.834 20.5 51.501 34.5s24 29.667 31 47
s10.5 34.333 10.5 51c0 22 -3.5 41.667 -10.5 59s-18.167 32.166 -33.5 44.499s-35.333 21.833 -60 28.5s-54.667 10 -90 10v49c60.667 1.33301 105.834 14.5 135.501 39.5s44.5 58.5 44.5 100.5c0 22.667 -3.66699 42.334 -11 59.001s-17.5 30.5 -30.5 41.5
s-28 19.167 -45 24.5s-35.167 8 -54.5 8c-42 0 -75.167 -10.833 -99.5 -32.5s-42.5 -52.167 -54.5 -91.5c-4 -13.333 -12 -20 -24 -20c-4.66699 0 -12 1 -22 3l-25 4c9.33301 64 34.5 113 75.5 147s91.833 51 152.5 51z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="585" 
d="M517 1431l-216 -253c-5.33301 -6.66699 -10.833 -11.167 -16.5 -13.5s-12.5 -3.5 -20.5 -3.5h-57l157 238c7.33301 12 15 20.333 23 25s20 7 36 7h94z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="1082" 
d="M233 995l0.00195312 -635.998c0 -93.333 21.333 -166.5 64 -219.5s107.667 -79.5 195 -79.5c64.667 0 125 16.5 181 49.5s106.333 78.833 151 137.5v748h96v-995h-53c-18.667 0 -28.334 8.66699 -29.001 26l-9 148c-46.667 -58.667 -98.334 -103.334 -155.001 -134.001
s-119 -46 -187 -46c-65.333 0 -120 12.833 -164 38.5s-78.333 62.5 -103 110.5c3.33301 -28.667 5.5 -57.667 6.5 -87s1.5 -56 1.5 -80v-328h-48c-13.333 0 -23.833 3.66699 -31.5 11s-11.5 17.666 -11.5 30.999v1305h96z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1264" 
d="M1220 1415v-82h-225v-1523h-85v1523h-328v-1523h-86v889c-68.667 0 -130.334 9.33301 -185.001 28s-101 44.334 -139 77.001s-67.167 71.167 -87.5 115.5s-30.5 92.166 -30.5 143.499c0 53.333 10.167 101.666 30.5 144.999s49.5 80.333 87.5 111
s84.333 54.334 139 71.001s116.334 25 185.001 25h724z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="532" 
d="M165 592c0 14 2.66699 27.333 8 40s12.5 23.667 21.5 33s19.5 16.666 31.5 21.999s24.667 8 38 8c14 0 27.333 -2.66699 40 -8s23.667 -12.666 33 -21.999s16.666 -20.333 21.999 -33s8 -26 8 -40c0 -13.333 -2.66699 -26.166 -8 -38.499s-12.666 -23 -21.999 -32
s-20.333 -16.167 -33 -21.5s-26 -8 -40 -8c-13.333 0 -26 2.66699 -38 8s-22.5 12.5 -31.5 21.5s-16.167 19.667 -21.5 32s-8 25.166 -8 38.499z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="585" 
d="M158 -273c3.33301 0 7.83496 -1.83398 13.502 -5.50098s13 -7.66699 22 -12s20.167 -8.33301 33.5 -12s29.666 -5.5 48.999 -5.5c34 0 60.5 7.33301 79.5 22s28.5 34.667 28.5 60c0 16.667 -4.33301 30.5 -13 41.5s-21 20.167 -37 27.5s-34.833 13 -56.5 17
s-45.5 7.66699 -71.5 11l42 134h61l-30 -96c59.333 -10.667 104.5 -26 135.5 -46s46.5 -49.667 46.5 -89c0 -20 -4.5 -38 -13.5 -54s-21.667 -29.5 -38 -40.5s-35.833 -19.5 -58.5 -25.5s-47.334 -9 -74.001 -9c-27.333 0 -54 3.83301 -80 11.5s-47.667 17.834 -65 30.501
l10 28c4 8 9.33301 12 16 12z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="662" 
d="M187 949h157.999v540l4 37l-144 -126c-5.33301 -4 -10.666 -6 -15.999 -6c-7.33301 0 -13.333 3 -18 9l-22 31l211 184h58v-669h147v-49h-378v49z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="747" 
d="M375 1429c44 0 83.667 -7 119 -21s65.333 -34.167 90 -60.5s43.5 -58 56.5 -95s19.5 -78.5 19.5 -124.5s-6.5 -87.5 -19.5 -124.5s-31.833 -68.667 -56.5 -95s-54.667 -46.5 -90 -60.5s-75 -21 -119 -21c-45.333 0 -85.666 7 -120.999 21s-65.5 34.167 -90.5 60.5
s-44 58 -57 95s-19.5 78.5 -19.5 124.5s6.5 87.5 19.5 124.5s32 68.667 57 95s55.167 46.5 90.5 60.5s75.666 21 120.999 21zM375 885c69.333 0 121.5 21.5 156.5 64.5s52.5 102.5 52.5 178.5c0 75.333 -17.5 134.666 -52.5 177.999s-87.167 65 -156.5 65
c-71.333 0 -124.5 -21.667 -159.5 -65s-52.5 -102.666 -52.5 -177.999c0 -76 17.5 -135.5 52.5 -178.5s88.167 -64.5 159.5 -64.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="874" 
d="M223 134l-30 15.0039c-11.333 6 -17 14.667 -17 26c0 7.33301 2.33301 14.666 7 21.999l183 294c4 7.33301 7.83301 13.166 11.5 17.499s7.16699 7.83301 10.5 10.5c-3.33301 3.33301 -6.83301 7.16602 -10.5 11.499s-7.5 10.166 -11.5 17.499l-183 293
c-4.66699 7.33301 -7 15 -7 23c0 10.667 5.66699 19 17 25l30 15l241 -378v-13zM478 134.004l-30 15.0039c-11.333 6 -17 14.667 -17 26c0 7.33301 2.33301 14.666 7 21.999l183 294c4 7.33301 7.83301 13.166 11.5 17.499s7.16699 7.83301 10.5 10.5
c-3.33301 3.33301 -6.83301 7.16602 -10.5 11.499s-7.5 10.166 -11.5 17.499l-183 293c-4.66699 7.33301 -7 15 -7 23c0 10.667 5.66699 19 17 25l30 15l241 -378v-13z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1424" 
d="M1267 247h125.999v-36c0 -10.667 -5.66699 -16 -17 -16h-109v-195h-63v195h-325c-15.333 0 -23.666 5.33301 -24.999 16l-6 32l352 472h67v-468zM171.999 750h157.999v540l4 37l-144 -126c-5.33301 -4 -10.666 -6 -15.999 -6c-7.33301 0 -13.333 3 -18 9l-22 31l211 184
h58v-669h147v-49h-378v49zM1204 582c0 15.333 1 32.667 3 52l-289 -387h286v335zM407.998 31.001c-7.33301 -12 -15.499 -20.167 -24.499 -24.5s-19.167 -6.5 -30.5 -6.5h-40l800 1380c6.66699 11.333 14.167 20 22.5 26s19.166 9 32.499 9h42z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1424" 
d="M1126 723c29.333 0 57.001 -4.33398 83.001 -13.001s48.5 -21.167 67.5 -37.5s34 -36.666 45 -60.999s16.5 -52.5 16.5 -84.5c0 -26.667 -4.16699 -51.5 -12.5 -74.5s-19.5 -45 -33.5 -66s-30.333 -41.333 -49 -61l-58 -59.5l-207 -210
c12.667 3.33301 25.5 5.83301 38.5 7.5s26.167 2.5 39.5 2.5h275c16 0 24 -8 24 -24v-42h-474v24c0 5.33301 1 10.666 3 15.999s5.66699 10.333 11 15l235 237c18.667 18.667 36.167 37.334 52.5 56.001s30.666 37.5 42.999 56.5s22 38.5 29 58.5s10.5 40.667 10.5 62
c0 23.333 -3.83301 43.666 -11.5 60.999s-18 31.666 -31 42.999s-28.167 19.833 -45.5 25.5s-35.333 8.5 -54 8.5c-41.333 0 -74.166 -10.667 -98.499 -32s-42.833 -52.666 -55.5 -93.999c-4 -12.667 -13 -19 -27 -19c-1.33301 0 -5.33301 0.333008 -12 1
s-10.667 1.33398 -12 2.00098l-23 4c9.33301 64 34.666 113 75.999 147s93 51 155 51zM172.001 749.999h157.999v540l4 37l-144 -126c-5.33301 -4 -10.666 -6 -15.999 -6c-7.33301 0 -13.333 3 -18 9l-22 31l211 184h58v-669h147v-49h-378v49zM361 30.999
c-7.33301 -12 -15.499 -20.167 -24.499 -24.5s-19.167 -6.5 -30.5 -6.5h-40l800 1380c6.66699 11.333 14.167 20 22.5 26s19.166 9 32.499 9h42z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1428" 
d="M1267 247h125.999v-36c0 -10.667 -5.66699 -16 -17 -16h-109v-195h-63v195h-325c-15.333 0 -23.666 5.33301 -24.999 16l-6 32l352 472h67v-468zM332.999 1424c29.333 0 56.665 -4.16992 81.998 -12.5029s47.166 -20.333 65.499 -36s32.833 -34.667 43.5 -57
s16 -47.833 16 -76.5c0 -44 -12 -80.5 -36 -109.5s-56 -49.833 -96 -62.5c48.667 -11.333 85.834 -31 111.501 -59s38.5 -64 38.5 -108c0 -32 -6.16699 -60.833 -18.5 -86.5s-29.166 -47.667 -50.499 -66s-46.333 -32.5 -75 -42.5s-59 -15 -91 -15c-40 0 -73.5 5 -100.5 15
s-49.667 23.5 -68 40.5s-33 36.667 -44 59s-20.167 45.5 -27.5 69.5l30 13c2.66699 1.33301 5.16699 2.33301 7.5 3s5.16602 1 8.49902 1c5.33301 0 10.333 -1.33301 15 -4s8 -6.66699 10 -12l3 -8c2.66699 -8 7.16699 -18.667 13.5 -32s15.5 -26.166 27.5 -38.499
s27.833 -23.166 47.5 -32.499s44.5 -14 74.5 -14c27.333 0 51.333 4.5 72 13.5s37.834 20.5 51.501 34.5s24 29.667 31 47s10.5 34.333 10.5 51c0 22 -3.5 41.667 -10.5 59s-18.167 32.166 -33.5 44.499s-35.333 21.833 -60 28.5s-54.667 10 -90 10v49
c60.667 1.33301 105.834 14.5 135.501 39.5s44.5 58.5 44.5 100.5c0 22.667 -3.66699 42.334 -11 59.001s-17.5 30.5 -30.5 41.5s-28 19.167 -45 24.5s-35.167 8 -54.5 8c-41.333 0 -74.333 -11.833 -99 -35.5s-43 -53.167 -55 -88.5c-4.66699 -13.333 -12.667 -20 -24 -20
h-3c-1.33301 0 -3.5 0.333008 -6.5 1s-7.5 1.33398 -13.5 2.00098s-14 2 -24 4c9.33301 64 34.5 113 75.5 147s91.833 51 152.5 51zM1204 581.997c0 15.333 1 32.667 3 52l-289 -387h286v335zM415.998 30.998c-7.33301 -12 -15.499 -20.167 -24.499 -24.5
s-19.167 -6.5 -30.5 -6.5h-40l800 1380c6.66699 11.333 14.167 20 22.5 26s19.166 9 32.499 9h42z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="741" 
d="M712 -224c-18 -18 -38.333 -35.335 -61 -52.002s-47.5 -31.5 -74.5 -44.5s-56.5 -23.333 -88.5 -31s-66.667 -11.5 -104 -11.5c-46 0 -89.833 6.83301 -131.5 20.5s-78 33.334 -109 59.001s-55.667 57 -74 94s-27.5 79.167 -27.5 126.5c0 50.667 8 93.834 24 129.501
s35.833 66.334 59.5 92.001s49.5 48 77.5 67l79 53.5c24.667 16.667 45.5 33.667 62.5 51s26.167 37.666 27.5 60.999l11 163h65l6 -170v-5c0 -28 -8 -52 -24 -72s-35.833 -38.667 -59.5 -56s-49.5 -34.833 -77.5 -52.5s-53.833 -38 -77.5 -61s-43.5 -50 -59.5 -81
s-24 -68.5 -24 -112.5c0 -36 7 -68.167 21 -96.5s33 -52.333 57 -72s51.667 -34.667 83 -45s64 -15.5 98 -15.5c46 0 84.833 5.83301 116.5 17.5s58.167 24.667 79.5 39s38 27.333 50 39s21.333 17.5 28 17.5c6 0 10.5 -1.16699 13.5 -3.5s5.83301 -5.5 8.5 -9.5z
M325 917.998c0 12.667 2.33301 24.667 7 36s11.167 21.166 19.5 29.499s18 15 29 20s23.167 7.5 36.5 7.5c12.667 0 24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20s15 -18.166 20 -29.499s7.5 -23.333 7.5 -36c0 -13.333 -2.5 -25.5 -7.5 -36.5s-11.667 -20.667 -20 -29
s-18.166 -14.833 -29.499 -19.5s-23.333 -7 -36 -7c-26 0 -47.833 8.83301 -65.5 26.5s-26.5 39.5 -26.5 65.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1289" 
d="M1275 0l-78.999 -0.000976562c-9.33301 0 -17 2.5 -23 7.5s-10.667 11.5 -14 19.5l-167 411h-695l-166 -411c-2.66699 -7.33301 -7.33398 -13.666 -14.001 -18.999s-14.667 -8 -24 -8h-78l579 1415h102zM328.001 513.999l634.004 0.000976562l-289 715
c-4.66699 11.333 -9.33398 24.166 -14.001 38.499s-9.33398 29.5 -14.001 45.5c-4.66699 -16 -9.33398 -31.167 -14.001 -45.5s-9.33398 -27.5 -14.001 -39.5zM463.005 1770c16 0 27.833 -1.49902 35.5 -4.49902s16.167 -8.83301 25.5 -17.5l228 -206h-71
c-8 0 -14.667 0.666992 -20 2s-11.333 4.66602 -18 9.99902l-293 216h113z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1289" 
d="M1275 0l-78.999 -0.000976562c-9.33301 0 -17 2.5 -23 7.5s-10.667 11.5 -14 19.5l-167 411h-695l-166 -411c-2.66699 -7.33301 -7.33398 -13.666 -14.001 -18.999s-14.667 -8 -24 -8h-78l579 1415h102zM328.001 513.999l634.004 0.000976562l-289 715
c-4.66699 11.333 -9.33398 24.166 -14.001 38.499s-9.33398 29.5 -14.001 45.5c-4.66699 -16 -9.33398 -31.167 -14.001 -45.5s-9.33398 -27.5 -14.001 -39.5zM933.005 1770l-291.999 -215.999c-6.66699 -4.66699 -12.834 -7.83398 -18.501 -9.50098s-12.5 -2.5 -20.5 -2.5
h-70l227 206c9.33301 8.66699 18 14.5 26 17.5s20 4.5 36 4.5h112z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1289" 
d="M1275 0l-78.999 -0.000976562c-9.33301 0 -17 2.5 -23 7.5s-10.667 11.5 -14 19.5l-167 411h-695l-166 -411c-2.66699 -7.33301 -7.33398 -13.666 -14.001 -18.999s-14.667 -8 -24 -8h-78l579 1415h102zM328.001 513.999l634.004 0.000976562l-289 715
c-4.66699 11.333 -9.33398 24.166 -14.001 38.499s-9.33398 29.5 -14.001 45.5c-4.66699 -16 -9.33398 -31.167 -14.001 -45.5s-9.33398 -27.5 -14.001 -39.5zM924.005 1542h-73.0029c-5.33301 0 -11.166 0.833008 -17.499 2.5s-11.833 4.16699 -16.5 7.5l-156 123
c-5.33301 4 -9 7 -11 9l-5 5c-1.33301 -1.33301 -3.16602 -3 -5.49902 -5s-6.16602 -5 -11.499 -9l-157 -123c-4.66699 -3.33301 -10 -5.83301 -16 -7.5s-11.667 -2.5 -17 -2.5h-74l235 199h90z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1289" 
d="M1275 0l-78.999 -0.000976562c-9.33301 0 -17 2.5 -23 7.5s-10.667 11.5 -14 19.5l-167 411h-695l-166 -411c-2.66699 -7.33301 -7.33398 -13.666 -14.001 -18.999s-14.667 -8 -24 -8h-78l579 1415h102zM328.001 513.999l634.004 0.000976562l-289 715
c-4.66699 11.333 -9.33398 24.166 -14.001 38.499s-9.33398 29.5 -14.001 45.5c-4.66699 -16 -9.33398 -31.167 -14.001 -45.5s-9.33398 -27.5 -14.001 -39.5zM764.005 1616c28 0 49.332 9.16699 63.999 27.5s22.334 41.833 23.001 70.5h54c0 -23.333 -3 -44.833 -9 -64.5
s-14.667 -37 -26 -52s-25.833 -26.667 -43.5 -35s-37.834 -12.5 -60.501 -12.5c-22 0 -43 5.33301 -63 16s-39.333 22.167 -58 34.5l-54.5 34.5c-17.667 10.667 -35.5 16 -53.5 16c-28 0 -49.167 -9.33301 -63.5 -28s-22.166 -42 -23.499 -70h-56
c0 22.667 3.16699 44 9.5 64s15.5 37.5 27.5 52.5s26.667 26.667 44 35s37.333 12.5 60 12.5s44 -5.33301 64 -16s39.333 -22.167 58 -34.5l54 -34.5c17.333 -10.667 35 -16 53 -16z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1289" 
d="M1275 0l-78.999 -0.000976562c-9.33301 0 -17 2.5 -23 7.5s-10.667 11.5 -14 19.5l-167 411h-695l-166 -411c-2.66699 -7.33301 -7.33398 -13.666 -14.001 -18.999s-14.667 -8 -24 -8h-78l579 1415h102zM328.001 513.999l634.004 0.000976562l-289 715
c-4.66699 11.333 -9.33398 24.166 -14.001 38.499s-9.33398 29.5 -14.001 45.5c-4.66699 -16 -9.33398 -31.167 -14.001 -45.5s-9.33398 -27.5 -14.001 -39.5zM517.005 1665c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18 -18 -25s-16 -12.667 -26 -17
s-20.333 -6.5 -31 -6.5s-20.834 2.16699 -30.501 6.5s-18.167 10 -25.5 17s-13.166 15.333 -17.499 25s-6.5 19.834 -6.5 30.501s2.16699 21 6.5 31s10.166 18.833 17.499 26.5s15.833 13.667 25.5 18s19.834 6.5 30.501 6.5s21 -2.16699 31 -6.5s18.667 -10.333 26 -18
s13.333 -16.5 18 -26.5s7 -20.333 7 -31zM933.005 1665c0 -10.667 -2.16797 -20.834 -6.50098 -30.501s-10.333 -18 -18 -25s-16.334 -12.667 -26.001 -17s-19.834 -6.5 -30.501 -6.5c-11.333 0 -21.833 2.16699 -31.5 6.5s-18.167 10 -25.5 17s-13.166 15.333 -17.499 25
s-6.5 19.834 -6.5 30.501c0 22 8 41.167 24 57.5s35 24.5 57 24.5c10.667 0 20.834 -2.16699 30.501 -6.5s18.334 -10.333 26.001 -18s13.667 -16.5 18 -26.5s6.5 -20.333 6.5 -31z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1289" 
d="M1275 0l-78.999 -0.000976562c-9.33301 0 -17 2.5 -23 7.5s-10.667 11.5 -14 19.5l-167 411h-695l-166 -411c-2.66699 -7.33301 -7.33398 -13.666 -14.001 -18.999s-14.667 -8 -24 -8h-78l579 1415h102zM328.001 513.999l634.004 0.000976562l-289 715
c-4.66699 11.333 -9.33398 24.166 -14.001 38.499s-9.33398 29.5 -14.001 45.5c-4.66699 -16 -9.33398 -31.167 -14.001 -45.5s-9.33398 -27.5 -14.001 -39.5zM477.005 1630c0 22.667 4.5 43.834 13.5 63.501s21 36.5 36 50.5s32.667 25 53 33s41.833 12 64.5 12
s44.334 -4 65.001 -12s38.5 -19 53.5 -33s27 -30.833 36 -50.5s13.5 -40.834 13.5 -63.501c0 -23.333 -4.5 -44.666 -13.5 -63.999s-21 -36 -36 -50s-32.833 -24.833 -53.5 -32.5s-42.334 -11.5 -65.001 -11.5s-44.167 3.83301 -64.5 11.5s-38 18.5 -53 32.5
s-27 30.667 -36 50s-13.5 40.666 -13.5 63.999zM530.005 1630c0 -33.333 10.667 -60.666 32 -81.999s49 -32 83 -32c33.333 0 60.666 10.667 81.999 32s32 48.666 32 81.999s-10.667 60.666 -32 81.999s-48.666 32 -81.999 32c-34 0 -61.667 -10.667 -83 -32
s-32 -48.666 -32 -81.999z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1847" 
d="M763 1415h960v-85h-815l72 -572h618v-83h-607l75 -590h657v-85h-742l-56 438h-596l-227 -413c-8.66699 -16.667 -22.667 -25 -42 -25h-79zM371 514l544 -0.000976562l-105 823c-5.33301 -17.333 -11.333 -33.666 -18 -48.999l-21 -46z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1407" 
d="M656 -273c3.33301 0 7.83105 -1.83398 13.498 -5.50098s13 -7.66699 22 -12s20.167 -8.33301 33.5 -12s29.666 -5.5 48.999 -5.5c34 0 60.5 7.33301 79.5 22s28.5 34.667 28.5 60c0 16.667 -4.33301 30.5 -13 41.5s-21 20.167 -37 27.5s-34.833 13 -56.5 17
s-45.5 7.66699 -71.5 11l36 115c-91.333 5.33301 -174.833 26.333 -250.5 63s-140.5 86.334 -194.5 149.001s-96 137 -126 223s-45 181.667 -45 287c0 107.333 16.833 205.333 50.5 294s81 164.834 142 228.501s134 113 219 148s178.833 52.5 281.5 52.5
c51.333 0 98.333 -3.83301 141 -11.5s82.5 -18.667 119.5 -33s71.833 -32.166 104.5 -53.499s64.667 -46 96 -74l-31 -45c-5.33301 -8 -13.666 -12 -24.999 -12c-6 0 -13.667 3.5 -23 10.5l-35.5 26c-14.333 10.333 -31.666 21.666 -51.999 33.999
s-44.5 23.666 -72.5 33.999s-60.333 19 -97 26s-78.334 10.5 -125.001 10.5c-86 0 -165 -14.833 -237 -44.5s-134 -72 -186 -127s-92.5 -121.667 -121.5 -200s-43.5 -166.166 -43.5 -263.499c0 -100 14.333 -189.333 43 -268s68.334 -145.167 119.001 -199.5
s110.5 -96 179.5 -125s143.833 -43.5 224.5 -43.5c50.667 0 95.834 3.33301 135.501 10s76.167 16.667 109.5 30s64.333 29.666 93 48.999s57 42 85 68c3.33301 2.66699 6.5 4.83398 9.5 6.50098s6.5 2.5 10.5 2.5c6.66699 0 12.334 -2.33301 17.001 -7l40 -43
c-29.333 -30.667 -60.833 -58.334 -94.5 -83.001s-70.667 -45.834 -111 -63.501s-84.5 -31.5 -132.5 -41.5s-100.333 -15.333 -157 -16l-23 -75c59.333 -10.667 104.5 -26 135.5 -46s46.5 -49.667 46.5 -89c0 -20 -4.5 -38 -13.5 -54s-21.667 -29.5 -38 -40.5
s-35.833 -19.5 -58.5 -25.5s-47.334 -9 -74.001 -9c-27.333 0 -54 3.83301 -80 11.5s-47.667 17.834 -65 30.501l10 28c4 8 9.33301 12 16 12z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1182" 
d="M1058 1415v-85h-745v-572h620v-83h-620v-590h745v-85h-848v1415h848zM462 1770c16 0 27.833 -1.49902 35.5 -4.49902s16.167 -8.83301 25.5 -17.5l228 -206h-71c-8 0 -14.667 0.666992 -20 2s-11.333 4.66602 -18 9.99902l-293 216h113z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1182" 
d="M1058 1415v-85h-745v-572h620v-83h-620v-590h745v-85h-848v1415h848zM932 1770l-291.999 -215.999c-6.66699 -4.66699 -12.834 -7.83398 -18.501 -9.50098s-12.5 -2.5 -20.5 -2.5h-70l227 206c9.33301 8.66699 18 14.5 26 17.5s20 4.5 36 4.5h112z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1182" 
d="M1058 1415v-85h-745v-572h620v-83h-620v-590h745v-85h-848v1415h848zM923 1542h-73.0029c-5.33301 0 -11.166 0.833008 -17.499 2.5s-11.833 4.16699 -16.5 7.5l-156 123c-5.33301 4 -9 7 -11 9l-5 5c-1.33301 -1.33301 -3.16602 -3 -5.49902 -5s-6.16602 -5 -11.499 -9
l-157 -123c-4.66699 -3.33301 -10 -5.83301 -16 -7.5s-11.667 -2.5 -17 -2.5h-74l235 199h90z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1182" 
d="M1058 1415v-85h-745v-572h620v-83h-620v-590h745v-85h-848v1415h848zM516 1665c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18 -18 -25s-16 -12.667 -26 -17s-20.333 -6.5 -31 -6.5s-20.834 2.16699 -30.501 6.5s-18.167 10 -25.5 17s-13.166 15.333 -17.499 25
s-6.5 19.834 -6.5 30.501s2.16699 21 6.5 31s10.166 18.833 17.499 26.5s15.833 13.667 25.5 18s19.834 6.5 30.501 6.5s21 -2.16699 31 -6.5s18.667 -10.333 26 -18s13.333 -16.5 18 -26.5s7 -20.333 7 -31zM932 1665c0 -10.667 -2.16797 -20.834 -6.50098 -30.501
s-10.333 -18 -18 -25s-16.334 -12.667 -26.001 -17s-19.834 -6.5 -30.501 -6.5c-11.333 0 -21.833 2.16699 -31.5 6.5s-18.167 10 -25.5 17s-13.166 15.333 -17.499 25s-6.5 19.834 -6.5 30.501c0 22 8 41.167 24 57.5s35 24.5 57 24.5
c10.667 0 20.834 -2.16699 30.501 -6.5s18.334 -10.333 26.001 -18s13.667 -16.5 18 -26.5s6.5 -20.333 6.5 -31z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="598" 
d="M350 0h-103v1415h103v-1415zM120 1770c16 0 27.833 -1.49902 35.5 -4.49902s16.167 -8.83301 25.5 -17.5l228 -206h-71c-8 0 -14.667 0.666992 -20 2s-11.333 4.66602 -18 9.99902l-293 216h113z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="598" 
d="M350 0h-103v1415h103v-1415zM590 1770l-291.999 -215.999c-6.66699 -4.66699 -12.834 -7.83398 -18.501 -9.50098s-12.5 -2.5 -20.5 -2.5h-70l227 206c9.33301 8.66699 18 14.5 26 17.5s20 4.5 36 4.5h112z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="598" 
d="M350 0h-103v1415h103v-1415zM581 1542h-73.0029c-5.33301 0 -11.166 0.833008 -17.499 2.5s-11.833 4.16699 -16.5 7.5l-156 123c-5.33301 4 -9 7 -11 9l-5 5c-1.33301 -1.33301 -3.16602 -3 -5.49902 -5s-6.16602 -5 -11.499 -9l-157 -123
c-4.66699 -3.33301 -10 -5.83301 -16 -7.5s-11.667 -2.5 -17 -2.5h-74l235 199h90z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="598" 
d="M350 0h-103v1415h103v-1415zM174 1665c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18 -18 -25s-16 -12.667 -26 -17s-20.333 -6.5 -31 -6.5s-20.834 2.16699 -30.501 6.5s-18.167 10 -25.5 17s-13.166 15.333 -17.499 25s-6.5 19.834 -6.5 30.501
s2.16699 21 6.5 31s10.166 18.833 17.499 26.5s15.833 13.667 25.5 18s19.834 6.5 30.501 6.5s21 -2.16699 31 -6.5s18.667 -10.333 26 -18s13.333 -16.5 18 -26.5s7 -20.333 7 -31zM590 1665c0 -10.667 -2.16797 -20.834 -6.50098 -30.501s-10.333 -18 -18 -25
s-16.334 -12.667 -26.001 -17s-19.834 -6.5 -30.501 -6.5c-11.333 0 -21.833 2.16699 -31.5 6.5s-18.167 10 -25.5 17s-13.166 15.333 -17.499 25s-6.5 19.834 -6.5 30.501c0 22 8 41.167 24 57.5s35 24.5 57 24.5c10.667 0 20.834 -2.16699 30.501 -6.5
s18.334 -10.333 26.001 -18s13.667 -16.5 18 -26.5s6.5 -20.333 6.5 -31z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1575" 
d="M49 749l216 -0.00292969v666h516c100.667 0 192.334 -16.333 275.001 -49s153.667 -79.667 213 -141s105.333 -135.666 138 -222.999s49 -185.333 49 -294c0 -109.333 -16.333 -207.666 -49 -294.999s-78.667 -161.666 -138 -222.999s-130.333 -108.333 -213 -141
s-174.334 -49 -275.001 -49h-516v685h-216v64zM1350 707.997c0 98.667 -13.667 186.67 -41 264.003s-65.833 142.666 -115.5 195.999s-109.5 94 -179.5 122s-147.667 42 -233 42h-413v-583h404v-64h-404v-602h413c85.333 0 163 14 233 42s129.833 68.667 179.5 122
s88.167 118.666 115.5 195.999s41 165.666 41 264.999z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1512" 
d="M260 1415c9.33301 0 16.666 -1.16699 21.999 -3.5s11 -7.5 17 -15.5l916 -1226c-2 20 -3 39.333 -3 58v1187h90v-1415h-49c-15.333 0 -27.666 6.66699 -36.999 20l-920 1228c2 -20 3 -39 3 -57v-1191h-89v1415h50v0zM912 1616c28 0 49.332 9.16699 63.999 27.5
s22.334 41.833 23.001 70.5h54c0 -23.333 -3 -44.833 -9 -64.5s-14.667 -37 -26 -52s-25.833 -26.667 -43.5 -35s-37.834 -12.5 -60.501 -12.5c-22 0 -43 5.33301 -63 16s-39.333 22.167 -58 34.5l-54.5 34.5c-17.667 10.667 -35.5 16 -53.5 16
c-28 0 -49.167 -9.33301 -63.5 -28s-22.166 -42 -23.499 -70h-56c0 22.667 3.16699 44 9.5 64s15.5 37.5 27.5 52.5s26.667 26.667 44 35s37.333 12.5 60 12.5s44 -5.33301 64 -16s39.333 -22.167 58 -34.5l54 -34.5c17.333 -10.667 35 -16 53 -16z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="1595" 
d="M1472 708c0 -109.333 -16.333 -208.501 -49 -297.501s-78.667 -165 -138 -228s-130.333 -111.667 -213 -146s-174.334 -51.5 -275.001 -51.5c-99.333 0 -190.333 17.167 -273 51.5s-153.667 83 -213 146s-105.5 139 -138.5 228s-49.5 188.167 -49.5 297.5
c0 108.667 16.5 207.5 49.5 296.5s79.167 165 138.5 228s130.333 111.833 213 146.5s173.667 52 273 52c100.667 0 192.334 -17.167 275.001 -51.5s153.667 -83.166 213 -146.499s105.333 -139.5 138 -228.5s49 -187.833 49 -296.5zM1366 707.999
c0 98.667 -13.667 187.334 -41 266.001s-65.833 145.334 -115.5 200.001s-109.5 96.5 -179.5 125.5s-147.667 43.5 -233 43.5c-84 0 -160.833 -14.5 -230.5 -43.5s-129.667 -70.833 -180 -125.5s-89.333 -121.334 -117 -200.001s-41.5 -167.334 -41.5 -266.001
c0 -99.333 13.833 -188.166 41.5 -266.499s66.667 -144.833 117 -199.5s110.333 -96.334 180 -125.001s146.5 -43 230.5 -43c85.333 0 163 14.333 233 43s129.833 70.334 179.5 125.001s88.167 121.167 115.5 199.5s41 167.166 41 266.499zM618 1770
c16 0 27.833 -1.49902 35.5 -4.49902s16.167 -8.83301 25.5 -17.5l228 -206h-71c-8 0 -14.667 0.666992 -20 2s-11.333 4.66602 -18 9.99902l-293 216h113z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="1595" 
d="M1472 708c0 -109.333 -16.333 -208.501 -49 -297.501s-78.667 -165 -138 -228s-130.333 -111.667 -213 -146s-174.334 -51.5 -275.001 -51.5c-99.333 0 -190.333 17.167 -273 51.5s-153.667 83 -213 146s-105.5 139 -138.5 228s-49.5 188.167 -49.5 297.5
c0 108.667 16.5 207.5 49.5 296.5s79.167 165 138.5 228s130.333 111.833 213 146.5s173.667 52 273 52c100.667 0 192.334 -17.167 275.001 -51.5s153.667 -83.166 213 -146.499s105.333 -139.5 138 -228.5s49 -187.833 49 -296.5zM1366 707.999
c0 98.667 -13.667 187.334 -41 266.001s-65.833 145.334 -115.5 200.001s-109.5 96.5 -179.5 125.5s-147.667 43.5 -233 43.5c-84 0 -160.833 -14.5 -230.5 -43.5s-129.667 -70.833 -180 -125.5s-89.333 -121.334 -117 -200.001s-41.5 -167.334 -41.5 -266.001
c0 -99.333 13.833 -188.166 41.5 -266.499s66.667 -144.833 117 -199.5s110.333 -96.334 180 -125.001s146.5 -43 230.5 -43c85.333 0 163 14.333 233 43s129.833 70.334 179.5 125.001s88.167 121.167 115.5 199.5s41 167.166 41 266.499zM1088 1770l-291.999 -215.999
c-6.66699 -4.66699 -12.834 -7.83398 -18.501 -9.50098s-12.5 -2.5 -20.5 -2.5h-70l227 206c9.33301 8.66699 18 14.5 26 17.5s20 4.5 36 4.5h112z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="1595" 
d="M1472 708c0 -109.333 -16.333 -208.501 -49 -297.501s-78.667 -165 -138 -228s-130.333 -111.667 -213 -146s-174.334 -51.5 -275.001 -51.5c-99.333 0 -190.333 17.167 -273 51.5s-153.667 83 -213 146s-105.5 139 -138.5 228s-49.5 188.167 -49.5 297.5
c0 108.667 16.5 207.5 49.5 296.5s79.167 165 138.5 228s130.333 111.833 213 146.5s173.667 52 273 52c100.667 0 192.334 -17.167 275.001 -51.5s153.667 -83.166 213 -146.499s105.333 -139.5 138 -228.5s49 -187.833 49 -296.5zM1366 707.999
c0 98.667 -13.667 187.334 -41 266.001s-65.833 145.334 -115.5 200.001s-109.5 96.5 -179.5 125.5s-147.667 43.5 -233 43.5c-84 0 -160.833 -14.5 -230.5 -43.5s-129.667 -70.833 -180 -125.5s-89.333 -121.334 -117 -200.001s-41.5 -167.334 -41.5 -266.001
c0 -99.333 13.833 -188.166 41.5 -266.499s66.667 -144.833 117 -199.5s110.333 -96.334 180 -125.001s146.5 -43 230.5 -43c85.333 0 163 14.333 233 43s129.833 70.334 179.5 125.001s88.167 121.167 115.5 199.5s41 167.166 41 266.499zM1079 1542h-73.0029
c-5.33301 0 -11.166 0.833008 -17.499 2.5s-11.833 4.16699 -16.5 7.5l-156 123c-5.33301 4 -9 7 -11 9l-5 5c-1.33301 -1.33301 -3.16602 -3 -5.49902 -5s-6.16602 -5 -11.499 -9l-157 -123c-4.66699 -3.33301 -10 -5.83301 -16 -7.5s-11.667 -2.5 -17 -2.5h-74l235 199h90
z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="1595" 
d="M1472 708c0 -109.333 -16.333 -208.501 -49 -297.501s-78.667 -165 -138 -228s-130.333 -111.667 -213 -146s-174.334 -51.5 -275.001 -51.5c-99.333 0 -190.333 17.167 -273 51.5s-153.667 83 -213 146s-105.5 139 -138.5 228s-49.5 188.167 -49.5 297.5
c0 108.667 16.5 207.5 49.5 296.5s79.167 165 138.5 228s130.333 111.833 213 146.5s173.667 52 273 52c100.667 0 192.334 -17.167 275.001 -51.5s153.667 -83.166 213 -146.499s105.333 -139.5 138 -228.5s49 -187.833 49 -296.5zM1366 707.999
c0 98.667 -13.667 187.334 -41 266.001s-65.833 145.334 -115.5 200.001s-109.5 96.5 -179.5 125.5s-147.667 43.5 -233 43.5c-84 0 -160.833 -14.5 -230.5 -43.5s-129.667 -70.833 -180 -125.5s-89.333 -121.334 -117 -200.001s-41.5 -167.334 -41.5 -266.001
c0 -99.333 13.833 -188.166 41.5 -266.499s66.667 -144.833 117 -199.5s110.333 -96.334 180 -125.001s146.5 -43 230.5 -43c85.333 0 163 14.333 233 43s129.833 70.334 179.5 125.001s88.167 121.167 115.5 199.5s41 167.166 41 266.499zM919 1616
c28 0 49.332 9.16699 63.999 27.5s22.334 41.833 23.001 70.5h54c0 -23.333 -3 -44.833 -9 -64.5s-14.667 -37 -26 -52s-25.833 -26.667 -43.5 -35s-37.834 -12.5 -60.501 -12.5c-22 0 -43 5.33301 -63 16s-39.333 22.167 -58 34.5l-54.5 34.5
c-17.667 10.667 -35.5 16 -53.5 16c-28 0 -49.167 -9.33301 -63.5 -28s-22.166 -42 -23.499 -70h-56c0 22.667 3.16699 44 9.5 64s15.5 37.5 27.5 52.5s26.667 26.667 44 35s37.333 12.5 60 12.5s44 -5.33301 64 -16s39.333 -22.167 58 -34.5l54 -34.5
c17.333 -10.667 35 -16 53 -16z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="1595" 
d="M1472 708c0 -109.333 -16.333 -208.501 -49 -297.501s-78.667 -165 -138 -228s-130.333 -111.667 -213 -146s-174.334 -51.5 -275.001 -51.5c-99.333 0 -190.333 17.167 -273 51.5s-153.667 83 -213 146s-105.5 139 -138.5 228s-49.5 188.167 -49.5 297.5
c0 108.667 16.5 207.5 49.5 296.5s79.167 165 138.5 228s130.333 111.833 213 146.5s173.667 52 273 52c100.667 0 192.334 -17.167 275.001 -51.5s153.667 -83.166 213 -146.499s105.333 -139.5 138 -228.5s49 -187.833 49 -296.5zM1366 707.999
c0 98.667 -13.667 187.334 -41 266.001s-65.833 145.334 -115.5 200.001s-109.5 96.5 -179.5 125.5s-147.667 43.5 -233 43.5c-84 0 -160.833 -14.5 -230.5 -43.5s-129.667 -70.833 -180 -125.5s-89.333 -121.334 -117 -200.001s-41.5 -167.334 -41.5 -266.001
c0 -99.333 13.833 -188.166 41.5 -266.499s66.667 -144.833 117 -199.5s110.333 -96.334 180 -125.001s146.5 -43 230.5 -43c85.333 0 163 14.333 233 43s129.833 70.334 179.5 125.001s88.167 121.167 115.5 199.5s41 167.166 41 266.499zM672 1665
c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18 -18 -25s-16 -12.667 -26 -17s-20.333 -6.5 -31 -6.5s-20.834 2.16699 -30.501 6.5s-18.167 10 -25.5 17s-13.166 15.333 -17.499 25s-6.5 19.834 -6.5 30.501s2.16699 21 6.5 31s10.166 18.833 17.499 26.5
s15.833 13.667 25.5 18s19.834 6.5 30.501 6.5s21 -2.16699 31 -6.5s18.667 -10.333 26 -18s13.333 -16.5 18 -26.5s7 -20.333 7 -31zM1088 1665c0 -10.667 -2.16797 -20.834 -6.50098 -30.501s-10.333 -18 -18 -25s-16.334 -12.667 -26.001 -17s-19.834 -6.5 -30.501 -6.5
c-11.333 0 -21.833 2.16699 -31.5 6.5s-18.167 10 -25.5 17s-13.166 15.333 -17.499 25s-6.5 19.834 -6.5 30.501c0 22 8 41.167 24 57.5s35 24.5 57 24.5c10.667 0 20.834 -2.16699 30.501 -6.5s18.334 -10.333 26.001 -18s13.667 -16.5 18 -26.5s6.5 -20.333 6.5 -31z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M998 1036l-367 -366l372 -372l-51 -52l-373 372l-374 -374l-52 52l373 374l-368 368l53 54l368 -369l366 367z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1595" 
d="M1472 708c0 -109.333 -16.3301 -208.501 -48.9971 -297.501s-78.667 -165 -138 -228s-130.333 -111.667 -213 -146s-174.334 -51.5 -275.001 -51.5c-76.667 0 -148.167 10.167 -214.5 30.5s-126.5 49.5 -180.5 87.5l-131 -176
c-8.66699 -11.333 -18.334 -19.5 -29.001 -24.5s-21.667 -7.5 -33 -7.5h-44l185 249c-72 64 -127.833 143.5 -167.5 238.5s-59.5 203.5 -59.5 325.5c0 108.667 16.5 207.5 49.5 296.5s79.167 165 138.5 228s130.333 111.833 213 146.5s173.667 52 273 52
c80.667 0 155.667 -11.167 225 -33.5s131.666 -54.5 186.999 -96.5l111 149c8 10.667 15 18.334 21 23.001s15.667 7 29 7h55l-165 -222c67.333 -64 119.5 -142.333 156.5 -235s55.5 -197.667 55.5 -315zM228.003 707.999c0 -106.667 15.668 -200.83 47.001 -282.497
s75.333 -149.834 132 -204.501l746 1005c-47.333 38 -100.833 67 -160.5 87s-124.834 30 -195.501 30c-84 0 -160.833 -14.5 -230.5 -43.5s-129.667 -70.833 -180 -125.5s-89.333 -121.334 -117 -200.001s-41.5 -167.334 -41.5 -266.001zM1366 708.003
c0 101.333 -14.333 191.667 -43 271s-69 146.333 -121 201l-745 -1002c46 -34 97.5 -59.833 154.5 -77.5s118.833 -26.5 185.5 -26.5c85.333 0 163 14.333 233 43s129.833 70.334 179.5 125.001s88.167 121.167 115.5 199.5s41 167.166 41 266.499z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1467" 
d="M733 73c68 0 128.831 11.833 182.498 35.5s99.167 56.5 136.5 98.5s65.833 91.5 85.5 148.5s29.5 118.5 29.5 184.5v875h102v-875c0 -78 -12.333 -150.833 -37 -218.5s-60.167 -126.667 -106.5 -177s-102.666 -89.833 -168.999 -118.5s-140.833 -43 -223.5 -43
s-157.167 14.333 -223.5 43s-122.666 68.167 -168.999 118.5s-81.833 109.333 -106.5 177s-37 140.5 -37 218.5v875h103v-874c0 -66 9.83301 -127.5 29.5 -184.5s48 -106.5 85 -148.5s82.333 -75 136 -99s114.5 -36 182.5 -36zM551.998 1770
c16 0 27.833 -1.49902 35.5 -4.49902s16.167 -8.83301 25.5 -17.5l228 -206h-71c-8 0 -14.667 0.666992 -20 2s-11.333 4.66602 -18 9.99902l-293 216h113z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1467" 
d="M733 73c68 0 128.831 11.833 182.498 35.5s99.167 56.5 136.5 98.5s65.833 91.5 85.5 148.5s29.5 118.5 29.5 184.5v875h102v-875c0 -78 -12.333 -150.833 -37 -218.5s-60.167 -126.667 -106.5 -177s-102.666 -89.833 -168.999 -118.5s-140.833 -43 -223.5 -43
s-157.167 14.333 -223.5 43s-122.666 68.167 -168.999 118.5s-81.833 109.333 -106.5 177s-37 140.5 -37 218.5v875h103v-874c0 -66 9.83301 -127.5 29.5 -184.5s48 -106.5 85 -148.5s82.333 -75 136 -99s114.5 -36 182.5 -36zM1022 1770l-291.999 -215.999
c-6.66699 -4.66699 -12.834 -7.83398 -18.501 -9.50098s-12.5 -2.5 -20.5 -2.5h-70l227 206c9.33301 8.66699 18 14.5 26 17.5s20 4.5 36 4.5h112z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1467" 
d="M733 73c68 0 128.831 11.833 182.498 35.5s99.167 56.5 136.5 98.5s65.833 91.5 85.5 148.5s29.5 118.5 29.5 184.5v875h102v-875c0 -78 -12.333 -150.833 -37 -218.5s-60.167 -126.667 -106.5 -177s-102.666 -89.833 -168.999 -118.5s-140.833 -43 -223.5 -43
s-157.167 14.333 -223.5 43s-122.666 68.167 -168.999 118.5s-81.833 109.333 -106.5 177s-37 140.5 -37 218.5v875h103v-874c0 -66 9.83301 -127.5 29.5 -184.5s48 -106.5 85 -148.5s82.333 -75 136 -99s114.5 -36 182.5 -36zM1013 1542h-73.0029
c-5.33301 0 -11.166 0.833008 -17.499 2.5s-11.833 4.16699 -16.5 7.5l-156 123c-5.33301 4 -9 7 -11 9l-5 5c-1.33301 -1.33301 -3.16602 -3 -5.49902 -5s-6.16602 -5 -11.499 -9l-157 -123c-4.66699 -3.33301 -10 -5.83301 -16 -7.5s-11.667 -2.5 -17 -2.5h-74l235 199h90
z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1467" 
d="M733 73c68 0 128.831 11.833 182.498 35.5s99.167 56.5 136.5 98.5s65.833 91.5 85.5 148.5s29.5 118.5 29.5 184.5v875h102v-875c0 -78 -12.333 -150.833 -37 -218.5s-60.167 -126.667 -106.5 -177s-102.666 -89.833 -168.999 -118.5s-140.833 -43 -223.5 -43
s-157.167 14.333 -223.5 43s-122.666 68.167 -168.999 118.5s-81.833 109.333 -106.5 177s-37 140.5 -37 218.5v875h103v-874c0 -66 9.83301 -127.5 29.5 -184.5s48 -106.5 85 -148.5s82.333 -75 136 -99s114.5 -36 182.5 -36zM605.998 1665
c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18 -18 -25s-16 -12.667 -26 -17s-20.333 -6.5 -31 -6.5s-20.834 2.16699 -30.501 6.5s-18.167 10 -25.5 17s-13.166 15.333 -17.499 25s-6.5 19.834 -6.5 30.501s2.16699 21 6.5 31s10.166 18.833 17.499 26.5
s15.833 13.667 25.5 18s19.834 6.5 30.501 6.5s21 -2.16699 31 -6.5s18.667 -10.333 26 -18s13.333 -16.5 18 -26.5s7 -20.333 7 -31zM1022 1665c0 -10.667 -2.16797 -20.834 -6.50098 -30.501s-10.333 -18 -18 -25s-16.334 -12.667 -26.001 -17s-19.834 -6.5 -30.501 -6.5
c-11.333 0 -21.833 2.16699 -31.5 6.5s-18.167 10 -25.5 17s-13.166 15.333 -17.499 25s-6.5 19.834 -6.5 30.501c0 22 8 41.167 24 57.5s35 24.5 57 24.5c10.667 0 20.834 -2.16699 30.501 -6.5s18.334 -10.333 26.001 -18s13.667 -16.5 18 -26.5s6.5 -20.333 6.5 -31z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1199" 
d="M651 584v-584h-103v584l-523 831h89c9.33301 0 16.833 -2.16699 22.5 -6.5s11.167 -10.5 16.5 -18.5l405 -650c9.33301 -15.333 17.333 -30.166 24 -44.499s12.667 -28.833 18 -43.5c5.33301 14.667 11.333 29.167 18 43.5s14.667 29.166 24 44.499l404 650
c4 7.33301 9 13.333 15 18s13.667 7 23 7h90zM889 1770l-291.999 -215.999c-6.66699 -4.66699 -12.834 -7.83398 -18.501 -9.50098s-12.5 -2.5 -20.5 -2.5h-70l227 206c9.33301 8.66699 18 14.5 26 17.5s20 4.5 36 4.5h112z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1187" 
d="M342 286v-285.999h-102v1415h102v-284h263c166 0 291.167 -36.333 375.5 -109s126.5 -176.334 126.5 -311.001c0 -62 -11.5 -119 -34.5 -171s-56.167 -96.833 -99.5 -134.5s-96 -67 -158 -88s-132 -31.5 -210 -31.5h-263zM342 368.001l263.002 -0.000976562
c62.667 0 118.667 8.66699 168 26s91.166 41.333 125.499 72s60.666 66.834 78.999 108.501s27.5 87.167 27.5 136.5c0 108.667 -33.5 192.167 -100.5 250.5s-166.833 87.5 -299.5 87.5h-263v-681z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1158" 
d="M641 1433c56.667 0 107.003 -8.49902 151.003 -25.499s81 -39 111 -66s52.667 -57.167 68 -90.5s23 -66.666 23 -99.999c0 -41.333 -7.83301 -77.333 -23.5 -108s-35.334 -57.834 -59.001 -81.501s-49.167 -44.834 -76.5 -63.501s-52.833 -37.167 -76.5 -55.5
s-43.334 -37.166 -59.001 -56.499s-23.5 -41.333 -23.5 -66c0 -24 5.5 -44.167 16.5 -60.5s25.667 -30.5 44 -42.5s39 -23 62 -33l71 -31.5c24.333 -11 48 -23.333 71 -37s43.667 -30.5 62 -50.5s33 -43.833 44 -71.5s16.5 -61.167 16.5 -100.5
c0 -45.333 -8.33301 -87 -25 -125s-40 -70.667 -70 -98s-66 -48.5 -108 -63.5s-88.667 -22.5 -140 -22.5c-67.333 0 -125.166 11.167 -173.499 33.5s-92.5 51.5 -132.5 87.5l22 34c3.33301 5.33301 7.16602 9.33301 11.499 12s10.166 4 17.499 4
c8.66699 0 19.167 -5.33301 31.5 -16s28.5 -22.167 48.5 -34.5s44.833 -23.833 74.5 -34.5s65.5 -16 107.5 -16c39.333 0 74.333 5.83301 105 17.5s56.5 27.667 77.5 48s37 44.166 48 71.499s16.5 56.666 16.5 87.999c0 44.667 -9.83301 80.5 -29.5 107.5
s-44.167 49.167 -73.5 66.5s-61 32.333 -95 45s-65.667 27.167 -95 43.5s-53.833 37 -73.5 62s-29.5 58.5 -29.5 100.5c0 32.667 8 61 24 85s36.167 46.333 60.5 67s50.5 40.667 78.5 60s54.167 40.333 78.5 63s44.5 47.834 60.5 75.501s24 60.167 24 97.5
c0 20.667 -4.66699 43.167 -14 67.5s-24.5 47 -45.5 68s-48.5 38.5 -82.5 52.5s-75.333 21 -124 21c-47.333 0 -91.833 -7.16699 -133.5 -21.5s-78.167 -36.166 -109.5 -65.499s-56 -66.833 -74 -112.5s-27 -99.834 -27 -162.501v-999h-95v1001
c0 67.333 11 127.833 33 181.5s52.5 99 91.5 136s85.667 65.333 140 85s113.5 29.5 177.5 29.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="973" 
d="M797 0c-20 0 -32.332 9.33496 -36.999 28.002l-14 127c-27.333 -26.667 -54.5 -50.667 -81.5 -72s-55.167 -39.333 -84.5 -54s-61 -25.834 -95 -33.501s-71.333 -11.5 -112 -11.5c-34 0 -67 5 -99 15s-60.333 25.333 -85 46s-44.5 47.167 -59.5 79.5
s-22.5 71.166 -22.5 116.499c0 42 12 81 36 117s61.5 67.333 112.5 94s116.667 48 197 64s176.833 25.333 289.5 28v104c0 92 -19.833 162.833 -59.5 212.5s-98.5 74.5 -176.5 74.5c-48 0 -88.833 -6.66699 -122.5 -20s-62.167 -28 -85.5 -44s-42.333 -30.667 -57 -44
s-27 -20 -37 -20c-13.333 0 -23.333 6 -30 18l-17 29c52 52 106.667 91.333 164 118s122.333 40 195 40c53.333 0 100 -8.5 140 -25.5s73.167 -41.333 99.5 -73s46.166 -70 59.499 -115s20 -95.167 20 -150.5v-648h-38zM397.001 53.002
c38.667 0 74.166 4.16699 106.499 12.5s62.333 20 90 35s53.5 32.833 77.5 53.5s47.667 43 71 67v259c-94.667 -2.66699 -176.167 -9.83398 -244.5 -21.501s-124.666 -27.5 -168.999 -47.5s-77 -43.833 -98 -71.5s-31.5 -59.167 -31.5 -94.5
c0 -33.333 5.5 -62.166 16.5 -86.499s25.5 -44.333 43.5 -60s39 -27.167 63 -34.5s49 -11 75 -11zM392 1431c16 0 28 -2.33301 36 -7s15.667 -13 23 -25l157 -238h-54c-8 0 -14.833 1.16699 -20.5 3.5s-11.167 6.83301 -16.5 13.5l-217 253h92z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="973" 
d="M797 0c-20 0 -32.332 9.33496 -36.999 28.002l-14 127c-27.333 -26.667 -54.5 -50.667 -81.5 -72s-55.167 -39.333 -84.5 -54s-61 -25.834 -95 -33.501s-71.333 -11.5 -112 -11.5c-34 0 -67 5 -99 15s-60.333 25.333 -85 46s-44.5 47.167 -59.5 79.5
s-22.5 71.166 -22.5 116.499c0 42 12 81 36 117s61.5 67.333 112.5 94s116.667 48 197 64s176.833 25.333 289.5 28v104c0 92 -19.833 162.833 -59.5 212.5s-98.5 74.5 -176.5 74.5c-48 0 -88.833 -6.66699 -122.5 -20s-62.167 -28 -85.5 -44s-42.333 -30.667 -57 -44
s-27 -20 -37 -20c-13.333 0 -23.333 6 -30 18l-17 29c52 52 106.667 91.333 164 118s122.333 40 195 40c53.333 0 100 -8.5 140 -25.5s73.167 -41.333 99.5 -73s46.166 -70 59.499 -115s20 -95.167 20 -150.5v-648h-38zM397.001 53.002
c38.667 0 74.166 4.16699 106.499 12.5s62.333 20 90 35s53.5 32.833 77.5 53.5s47.667 43 71 67v259c-94.667 -2.66699 -176.167 -9.83398 -244.5 -21.501s-124.666 -27.5 -168.999 -47.5s-77 -43.833 -98 -71.5s-31.5 -59.167 -31.5 -94.5
c0 -33.333 5.5 -62.166 16.5 -86.499s25.5 -44.333 43.5 -60s39 -27.167 63 -34.5s49 -11 75 -11zM731 1431l-216 -253c-5.33301 -6.66699 -10.833 -11.167 -16.5 -13.5s-12.5 -3.5 -20.5 -3.5h-57l157 238c7.33301 12 15 20.333 23 25s20 7 36 7h94z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="973" 
d="M797 0c-20 0 -32.332 9.33496 -36.999 28.002l-14 127c-27.333 -26.667 -54.5 -50.667 -81.5 -72s-55.167 -39.333 -84.5 -54s-61 -25.834 -95 -33.501s-71.333 -11.5 -112 -11.5c-34 0 -67 5 -99 15s-60.333 25.333 -85 46s-44.5 47.167 -59.5 79.5
s-22.5 71.166 -22.5 116.499c0 42 12 81 36 117s61.5 67.333 112.5 94s116.667 48 197 64s176.833 25.333 289.5 28v104c0 92 -19.833 162.833 -59.5 212.5s-98.5 74.5 -176.5 74.5c-48 0 -88.833 -6.66699 -122.5 -20s-62.167 -28 -85.5 -44s-42.333 -30.667 -57 -44
s-27 -20 -37 -20c-13.333 0 -23.333 6 -30 18l-17 29c52 52 106.667 91.333 164 118s122.333 40 195 40c53.333 0 100 -8.5 140 -25.5s73.167 -41.333 99.5 -73s46.166 -70 59.499 -115s20 -95.167 20 -150.5v-648h-38zM397.001 53.002
c38.667 0 74.166 4.16699 106.499 12.5s62.333 20 90 35s53.5 32.833 77.5 53.5s47.667 43 71 67v259c-94.667 -2.66699 -176.167 -9.83398 -244.5 -21.501s-124.666 -27.5 -168.999 -47.5s-77 -43.833 -98 -71.5s-31.5 -59.167 -31.5 -94.5
c0 -33.333 5.5 -62.166 16.5 -86.499s25.5 -44.333 43.5 -60s39 -27.167 63 -34.5s49 -11 75 -11zM766 1169l-65 -0.000976562c-10.667 0 -19.667 4 -27 12l-156 163c-4 2.66699 -7.66699 6.33398 -11 11.001c-1.33301 -2.66699 -3 -4.83398 -5 -6.50098
s-3.66699 -3.16699 -5 -4.5l-157 -163c-3.33301 -3.33301 -7.33301 -6.16602 -12 -8.49902s-9.66699 -3.5 -15 -3.5h-67l218 246h85z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="973" 
d="M797 0c-20 0 -32.332 9.33496 -36.999 28.002l-14 127c-27.333 -26.667 -54.5 -50.667 -81.5 -72s-55.167 -39.333 -84.5 -54s-61 -25.834 -95 -33.501s-71.333 -11.5 -112 -11.5c-34 0 -67 5 -99 15s-60.333 25.333 -85 46s-44.5 47.167 -59.5 79.5
s-22.5 71.166 -22.5 116.499c0 42 12 81 36 117s61.5 67.333 112.5 94s116.667 48 197 64s176.833 25.333 289.5 28v104c0 92 -19.833 162.833 -59.5 212.5s-98.5 74.5 -176.5 74.5c-48 0 -88.833 -6.66699 -122.5 -20s-62.167 -28 -85.5 -44s-42.333 -30.667 -57 -44
s-27 -20 -37 -20c-13.333 0 -23.333 6 -30 18l-17 29c52 52 106.667 91.333 164 118s122.333 40 195 40c53.333 0 100 -8.5 140 -25.5s73.167 -41.333 99.5 -73s46.166 -70 59.499 -115s20 -95.167 20 -150.5v-648h-38zM397.001 53.002
c38.667 0 74.166 4.16699 106.499 12.5s62.333 20 90 35s53.5 32.833 77.5 53.5s47.667 43 71 67v259c-94.667 -2.66699 -176.167 -9.83398 -244.5 -21.501s-124.666 -27.5 -168.999 -47.5s-77 -43.833 -98 -71.5s-31.5 -59.167 -31.5 -94.5
c0 -33.333 5.5 -62.166 16.5 -86.499s25.5 -44.333 43.5 -60s39 -27.167 63 -34.5s49 -11 75 -11zM621 1280c28.667 0 50.3281 9.16797 64.9951 27.501s22.334 43.166 23.001 74.499h59c0 -24 -3.16699 -46.5 -9.5 -67.5s-15.666 -39.167 -27.999 -54.5
s-27.5 -27.333 -45.5 -36s-38.333 -13 -61 -13c-22 0 -42.833 5.5 -62.5 16.5s-38.5 23 -56.5 36s-35.5 25 -52.5 36s-34.167 16.5 -51.5 16.5c-28 0 -49.333 -9.5 -64 -28.5s-22.667 -43.5 -24 -73.5h-61c0 24 3.33301 46.5 10 67.5s16.334 39.167 29.001 54.5
s28 27.333 46 36s38.333 13 61 13s43.834 -5.5 63.501 -16.5s38.334 -23 56.001 -36s35 -25 52 -36s34.167 -16.5 51.5 -16.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="973" 
d="M797 0c-20 0 -32.332 9.33496 -36.999 28.002l-14 127c-27.333 -26.667 -54.5 -50.667 -81.5 -72s-55.167 -39.333 -84.5 -54s-61 -25.834 -95 -33.501s-71.333 -11.5 -112 -11.5c-34 0 -67 5 -99 15s-60.333 25.333 -85 46s-44.5 47.167 -59.5 79.5
s-22.5 71.166 -22.5 116.499c0 42 12 81 36 117s61.5 67.333 112.5 94s116.667 48 197 64s176.833 25.333 289.5 28v104c0 92 -19.833 162.833 -59.5 212.5s-98.5 74.5 -176.5 74.5c-48 0 -88.833 -6.66699 -122.5 -20s-62.167 -28 -85.5 -44s-42.333 -30.667 -57 -44
s-27 -20 -37 -20c-13.333 0 -23.333 6 -30 18l-17 29c52 52 106.667 91.333 164 118s122.333 40 195 40c53.333 0 100 -8.5 140 -25.5s73.167 -41.333 99.5 -73s46.166 -70 59.499 -115s20 -95.167 20 -150.5v-648h-38zM397.001 53.002
c38.667 0 74.166 4.16699 106.499 12.5s62.333 20 90 35s53.5 32.833 77.5 53.5s47.667 43 71 67v259c-94.667 -2.66699 -176.167 -9.83398 -244.5 -21.501s-124.666 -27.5 -168.999 -47.5s-77 -43.833 -98 -71.5s-31.5 -59.167 -31.5 -94.5
c0 -33.333 5.5 -62.166 16.5 -86.499s25.5 -44.333 43.5 -60s39 -27.167 63 -34.5s49 -11 75 -11zM417 1284c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18.167 -18 -25.5s-16.166 -13.166 -26.499 -17.499s-20.833 -6.5 -31.5 -6.5s-20.834 2.16699 -30.501 6.5
s-18.334 10.166 -26.001 17.499s-13.667 15.833 -18 25.5s-6.5 19.834 -6.5 30.501c0 11.333 2.16699 22 6.5 32s10.333 18.833 18 26.5s16.334 13.667 26.001 18s19.834 6.5 30.501 6.5s21.167 -2.16699 31.5 -6.5s19.166 -10.333 26.499 -18s13.333 -16.5 18 -26.5
s7 -20.667 7 -32zM760 1284c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18.167 -18 -25.5s-16.166 -13.166 -26.499 -17.499s-20.833 -6.5 -31.5 -6.5c-11.333 0 -21.833 2.16699 -31.5 6.5s-18.334 10.166 -26.001 17.499s-13.667 15.833 -18 25.5
s-6.5 19.834 -6.5 30.501c0 11.333 2.16699 22 6.5 32s10.333 18.833 18 26.5s16.334 13.667 26.001 18s20.167 6.5 31.5 6.5c10.667 0 21.167 -2.16699 31.5 -6.5s19.166 -10.333 26.499 -18s13.333 -16.5 18 -26.5s7 -20.667 7 -32z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="973" 
d="M797 0c-20 0 -32.332 9.33496 -36.999 28.002l-14 127c-27.333 -26.667 -54.5 -50.667 -81.5 -72s-55.167 -39.333 -84.5 -54s-61 -25.834 -95 -33.501s-71.333 -11.5 -112 -11.5c-34 0 -67 5 -99 15s-60.333 25.333 -85 46s-44.5 47.167 -59.5 79.5
s-22.5 71.166 -22.5 116.499c0 42 12 81 36 117s61.5 67.333 112.5 94s116.667 48 197 64s176.833 25.333 289.5 28v104c0 92 -19.833 162.833 -59.5 212.5s-98.5 74.5 -176.5 74.5c-48 0 -88.833 -6.66699 -122.5 -20s-62.167 -28 -85.5 -44s-42.333 -30.667 -57 -44
s-27 -20 -37 -20c-13.333 0 -23.333 6 -30 18l-17 29c52 52 106.667 91.333 164 118s122.333 40 195 40c53.333 0 100 -8.5 140 -25.5s73.167 -41.333 99.5 -73s46.166 -70 59.499 -115s20 -95.167 20 -150.5v-648h-38zM397.001 53.002
c38.667 0 74.166 4.16699 106.499 12.5s62.333 20 90 35s53.5 32.833 77.5 53.5s47.667 43 71 67v259c-94.667 -2.66699 -176.167 -9.83398 -244.5 -21.501s-124.666 -27.5 -168.999 -47.5s-77 -43.833 -98 -71.5s-31.5 -59.167 -31.5 -94.5
c0 -33.333 5.5 -62.166 16.5 -86.499s25.5 -44.333 43.5 -60s39 -27.167 63 -34.5s49 -11 75 -11zM334 1302c0 24 4.5 46 13.5 66s21.333 37.167 37 51.5s34 25.666 55 33.999s43.167 12.5 66.5 12.5s45.5 -4.16699 66.5 -12.5s39.333 -19.666 55 -33.999
s28.167 -31.5 37.5 -51.5s14 -42 14 -66s-4.66699 -45.833 -14 -65.5s-21.833 -36.834 -37.5 -51.501s-34 -26 -55 -34s-43.167 -12 -66.5 -12s-45.5 4 -66.5 12s-39.333 19.333 -55 34s-28 31.834 -37 51.501s-13.5 41.5 -13.5 65.5zM392 1302
c0 -33.333 10.667 -60.666 32 -81.999s49 -32 83 -32c33.333 0 60.666 10.667 81.999 32s32 48.666 32 81.999s-10.667 60.666 -32 81.999s-48.666 32 -81.999 32c-34 0 -61.667 -10.667 -83 -32s-32 -48.666 -32 -81.999z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1609" 
d="M1159 1011c50 0 96.667 -10.0039 140 -30.0039s81 -49.167 113 -87.5s57.167 -85.833 75.5 -142.5s27.5 -122 27.5 -196c0 -14.667 -1.66699 -24.667 -5 -30s-9.66602 -8 -18.999 -8h-660c0 -74.667 8 -140.334 24 -197.001s38.667 -104 68 -142
s64.666 -66.667 105.999 -86s87.333 -29 138 -29c47.333 0 87.666 5.5 120.999 16.5s61.333 23 84 36s40.334 25 53.001 36s22.334 16.5 29.001 16.5c9.33301 0 16.666 -3.33301 21.999 -10l24 -30c-15.333 -20 -35.333 -38.667 -60 -56s-52 -32.166 -82 -44.499
s-61.833 -22.166 -95.5 -29.499s-67.834 -11 -102.501 -11c-87.333 0 -162.5 23 -225.5 69s-110.167 114.667 -141.5 206c-13.333 -51.333 -34.166 -94.666 -62.499 -129.999s-60.5 -64 -96.5 -86s-74.167 -37.833 -114.5 -47.5s-78.833 -14.5 -115.5 -14.5
c-42 0 -81 5.16699 -117 15.5s-67.333 26.5 -94 48.5s-47.667 50.333 -63 85s-23 76.334 -23 125.001c0 42.667 12 82.834 36 120.501s61.5 70.834 112.5 99.501s116.667 52 197 70s176.833 28.333 289.5 31v69c0 92 -19.833 163.167 -59.5 213.5s-98.5 75.5 -176.5 75.5
c-48 0 -88.833 -6.83301 -122.5 -20.5s-62.167 -28.5 -85.5 -44.5s-42.333 -30.833 -57 -44.5s-27 -20.5 -37 -20.5c-7.33301 0 -13.333 1.66699 -18 5s-8.66699 7.66602 -12 12.999l-17 29c51.333 52 104.833 91.333 160.5 118s119.167 40 190.5 40
c84 0 150 -21.5 198 -64.5s79.667 -101.833 95 -176.5c31.333 74 77 132.5 137 175.5s133.667 64.5 221 64.5zM742 514.996c-94.667 -3.33301 -176.168 -11.667 -244.501 -25s-124.666 -31 -168.999 -53s-77 -47.833 -98 -77.5s-31.5 -62.167 -31.5 -97.5
c0 -37.333 5.5 -69.166 16.5 -95.499s26.5 -47.833 46.5 -64.5s43.667 -29 71 -37s57.333 -12 90 -12c44 0 85.333 7.33301 124 22s72.5 36.167 101.5 64.5s51.833 63.166 68.5 104.499s25 88.333 25 141v130zM1157 939.995c-48 0 -91.168 -8.66699 -129.501 -26
s-71.166 -41.666 -98.499 -72.999s-49.166 -69.333 -65.499 -114s-26.5 -94.334 -30.5 -149.001h598c0 56.667 -6.66699 107.5 -20 152.5s-32 83 -56 114s-52.833 54.667 -86.5 71s-70.834 24.5 -111.501 24.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="910" 
d="M393 -273c3.33301 0 7.83594 -1.83301 13.5029 -5.5s13 -7.66699 22 -12s20.167 -8.33301 33.5 -12s29.666 -5.5 48.999 -5.5c34 0 60.5 7.33301 79.5 22s28.5 34.667 28.5 60c0 16.667 -4.33301 30.5 -13 41.5s-21 20.167 -37 27.5s-34.833 13 -56.5 17
s-45.5 7.66699 -71.5 11l37 118c-58 4 -111 18.5 -159 43.5s-89.167 59.167 -123.5 102.5s-60.833 95.5 -79.5 156.5s-28 129.833 -28 206.5c0 76 10 145.333 30 208s49.167 116.667 87.5 162s85.333 80.5 141 105.5s119.5 37.5 191.5 37.5
c64.667 0 122.167 -10.333 172.5 -31s94.166 -48.667 131.499 -84l-25 -34c-3.33301 -3.33301 -6.5 -6.16602 -9.5 -8.49902s-7.16699 -3.5 -12.5 -3.5c-6.66699 0 -15.834 4.5 -27.501 13.5s-27.167 18.833 -46.5 29.5s-43.5 20.5 -72.5 29.5s-64.167 13.5 -105.5 13.5
c-57.333 0 -108.166 -10.167 -152.499 -30.5s-81.666 -49.5 -111.999 -87.5s-53.5 -84 -69.5 -138s-24 -114.667 -24 -182c0 -70 8.16699 -132 24.5 -186s39.5 -99.5 69.5 -136.5s66.333 -65.167 109 -84.5s90 -29 142 -29c48 0 88.167 5.66699 120.5 17
s59 23.666 80 36.999s37.667 25.666 50 36.999s22.166 17 29.499 17c8.66699 0 15.334 -3.33301 20.001 -10l26 -32c-30 -38.667 -72.833 -71 -128.5 -97s-118.834 -40.333 -189.501 -43l-24 -79c59.333 -10.667 104.5 -26 135.5 -46s46.5 -49.667 46.5 -89
c0 -20 -4.5 -38 -13.5 -54s-21.667 -29.5 -38 -40.5s-35.833 -19.5 -58.5 -25.5s-47.334 -9 -74.001 -9c-27.333 0 -54 3.83301 -80 11.5s-47.667 17.834 -65 30.501l10 28c4 8 9.33301 12 16 12z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="1022" 
d="M537 1011c56 0 107.829 -9.66992 155.496 -29.0029s89 -47.5 124 -84.5s62.333 -82.5 82 -136.5s29.5 -116 29.5 -186c0 -14.667 -2.16699 -24.667 -6.5 -30s-11.166 -8 -20.499 -8h-719v-19c0 -74.667 8.66699 -140.334 26 -197.001s42 -104.167 74 -142.5
s70.667 -67.166 116 -86.499s96 -29 152 -29c50 0 93.333 5.5 130 16.5s67.5 23.333 92.5 37s44.833 26 59.5 37s25.334 16.5 32.001 16.5c8.66699 0 15.334 -3.33301 20.001 -10l26 -32c-16 -20 -37.167 -38.667 -63.5 -56s-55.666 -32.166 -87.999 -44.499
s-67 -22.166 -104 -29.499s-74.167 -11 -111.5 -11c-68 0 -130 11.833 -186 35.5s-104 58.167 -144 103.5s-70.833 100.833 -92.5 166.5s-32.5 140.834 -32.5 225.501c0 71.333 10.167 137.166 30.5 197.499s49.666 112.333 87.999 156s85.333 77.834 141 102.501
s118.834 37 189.501 37zM537.996 939.997c-51.333 0 -97.332 -7.99805 -137.999 -23.998s-75.834 -39 -105.501 -69s-53.667 -66 -72 -108s-30.5 -89 -36.5 -141h656c0 53.333 -7.33301 101.166 -22 143.499s-35.334 78.166 -62.001 107.499s-58.667 51.833 -96 67.5
s-78.666 23.5 -123.999 23.5zM422.997 1431c16 0 28 -2.33301 36 -7s15.667 -13 23 -25l157 -238h-54c-8 0 -14.833 1.16699 -20.5 3.5s-11.167 6.83301 -16.5 13.5l-217 253h92z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="1022" 
d="M537 1011c56 0 107.829 -9.66992 155.496 -29.0029s89 -47.5 124 -84.5s62.333 -82.5 82 -136.5s29.5 -116 29.5 -186c0 -14.667 -2.16699 -24.667 -6.5 -30s-11.166 -8 -20.499 -8h-719v-19c0 -74.667 8.66699 -140.334 26 -197.001s42 -104.167 74 -142.5
s70.667 -67.166 116 -86.499s96 -29 152 -29c50 0 93.333 5.5 130 16.5s67.5 23.333 92.5 37s44.833 26 59.5 37s25.334 16.5 32.001 16.5c8.66699 0 15.334 -3.33301 20.001 -10l26 -32c-16 -20 -37.167 -38.667 -63.5 -56s-55.666 -32.166 -87.999 -44.499
s-67 -22.166 -104 -29.499s-74.167 -11 -111.5 -11c-68 0 -130 11.833 -186 35.5s-104 58.167 -144 103.5s-70.833 100.833 -92.5 166.5s-32.5 140.834 -32.5 225.501c0 71.333 10.167 137.166 30.5 197.499s49.666 112.333 87.999 156s85.333 77.834 141 102.501
s118.834 37 189.501 37zM537.996 939.997c-51.333 0 -97.332 -7.99805 -137.999 -23.998s-75.834 -39 -105.501 -69s-53.667 -66 -72 -108s-30.5 -89 -36.5 -141h656c0 53.333 -7.33301 101.166 -22 143.499s-35.334 78.166 -62.001 107.499s-58.667 51.833 -96 67.5
s-78.666 23.5 -123.999 23.5zM761.997 1431l-216 -253c-5.33301 -6.66699 -10.833 -11.167 -16.5 -13.5s-12.5 -3.5 -20.5 -3.5h-57l157 238c7.33301 12 15 20.333 23 25s20 7 36 7h94z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="1022" 
d="M537 1011c56 0 107.829 -9.66992 155.496 -29.0029s89 -47.5 124 -84.5s62.333 -82.5 82 -136.5s29.5 -116 29.5 -186c0 -14.667 -2.16699 -24.667 -6.5 -30s-11.166 -8 -20.499 -8h-719v-19c0 -74.667 8.66699 -140.334 26 -197.001s42 -104.167 74 -142.5
s70.667 -67.166 116 -86.499s96 -29 152 -29c50 0 93.333 5.5 130 16.5s67.5 23.333 92.5 37s44.833 26 59.5 37s25.334 16.5 32.001 16.5c8.66699 0 15.334 -3.33301 20.001 -10l26 -32c-16 -20 -37.167 -38.667 -63.5 -56s-55.666 -32.166 -87.999 -44.499
s-67 -22.166 -104 -29.499s-74.167 -11 -111.5 -11c-68 0 -130 11.833 -186 35.5s-104 58.167 -144 103.5s-70.833 100.833 -92.5 166.5s-32.5 140.834 -32.5 225.501c0 71.333 10.167 137.166 30.5 197.499s49.666 112.333 87.999 156s85.333 77.834 141 102.501
s118.834 37 189.501 37zM537.996 939.997c-51.333 0 -97.332 -7.99805 -137.999 -23.998s-75.834 -39 -105.501 -69s-53.667 -66 -72 -108s-30.5 -89 -36.5 -141h656c0 53.333 -7.33301 101.166 -22 143.499s-35.334 78.166 -62.001 107.499s-58.667 51.833 -96 67.5
s-78.666 23.5 -123.999 23.5zM796.997 1169l-65 -0.000976562c-10.667 0 -19.667 4 -27 12l-156 163c-4 2.66699 -7.66699 6.33398 -11 11.001c-1.33301 -2.66699 -3 -4.83398 -5 -6.50098s-3.66699 -3.16699 -5 -4.5l-157 -163
c-3.33301 -3.33301 -7.33301 -6.16602 -12 -8.49902s-9.66699 -3.5 -15 -3.5h-67l218 246h85z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="1022" 
d="M537 1011c56 0 107.829 -9.66992 155.496 -29.0029s89 -47.5 124 -84.5s62.333 -82.5 82 -136.5s29.5 -116 29.5 -186c0 -14.667 -2.16699 -24.667 -6.5 -30s-11.166 -8 -20.499 -8h-719v-19c0 -74.667 8.66699 -140.334 26 -197.001s42 -104.167 74 -142.5
s70.667 -67.166 116 -86.499s96 -29 152 -29c50 0 93.333 5.5 130 16.5s67.5 23.333 92.5 37s44.833 26 59.5 37s25.334 16.5 32.001 16.5c8.66699 0 15.334 -3.33301 20.001 -10l26 -32c-16 -20 -37.167 -38.667 -63.5 -56s-55.666 -32.166 -87.999 -44.499
s-67 -22.166 -104 -29.499s-74.167 -11 -111.5 -11c-68 0 -130 11.833 -186 35.5s-104 58.167 -144 103.5s-70.833 100.833 -92.5 166.5s-32.5 140.834 -32.5 225.501c0 71.333 10.167 137.166 30.5 197.499s49.666 112.333 87.999 156s85.333 77.834 141 102.501
s118.834 37 189.501 37zM537.996 939.997c-51.333 0 -97.332 -7.99805 -137.999 -23.998s-75.834 -39 -105.501 -69s-53.667 -66 -72 -108s-30.5 -89 -36.5 -141h656c0 53.333 -7.33301 101.166 -22 143.499s-35.334 78.166 -62.001 107.499s-58.667 51.833 -96 67.5
s-78.666 23.5 -123.999 23.5zM447.997 1284c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18.167 -18 -25.5s-16.166 -13.166 -26.499 -17.499s-20.833 -6.5 -31.5 -6.5s-20.834 2.16699 -30.501 6.5s-18.334 10.166 -26.001 17.499s-13.667 15.833 -18 25.5
s-6.5 19.834 -6.5 30.501c0 11.333 2.16699 22 6.5 32s10.333 18.833 18 26.5s16.334 13.667 26.001 18s19.834 6.5 30.501 6.5s21.167 -2.16699 31.5 -6.5s19.166 -10.333 26.499 -18s13.333 -16.5 18 -26.5s7 -20.667 7 -32zM790.997 1284
c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18.167 -18 -25.5s-16.166 -13.166 -26.499 -17.499s-20.833 -6.5 -31.5 -6.5c-11.333 0 -21.833 2.16699 -31.5 6.5s-18.334 10.166 -26.001 17.499s-13.667 15.833 -18 25.5s-6.5 19.834 -6.5 30.501
c0 11.333 2.16699 22 6.5 32s10.333 18.833 18 26.5s16.334 13.667 26.001 18s20.167 6.5 31.5 6.5c10.667 0 21.167 -2.16699 31.5 -6.5s19.166 -10.333 26.499 -18s13.333 -16.5 18 -26.5s7 -20.667 7 -32z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="475" 
d="M285 995v-995h-95v995h95zM126 1431c16 0 28 -2.33301 36 -7s15.667 -13 23 -25l157 -238h-54c-8 0 -14.833 1.16699 -20.5 3.5s-11.167 6.83301 -16.5 13.5l-217 253h92z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="475" 
d="M285 995v-995h-95v995h95zM465 1431l-216 -253c-5.33301 -6.66699 -10.833 -11.167 -16.5 -13.5s-12.5 -3.5 -20.5 -3.5h-57l157 238c7.33301 12 15 20.333 23 25s20 7 36 7h94z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="475" 
d="M285 995v-995h-95v995h95zM500 1169l-65 -0.000976562c-10.667 0 -19.667 4 -27 12l-156 163c-4 2.66699 -7.66699 6.33398 -11 11.001c-1.33301 -2.66699 -3 -4.83398 -5 -6.50098s-3.66699 -3.16699 -5 -4.5l-157 -163
c-3.33301 -3.33301 -7.33301 -6.16602 -12 -8.49902s-9.66699 -3.5 -15 -3.5h-67l218 246h85z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="475" 
d="M285 995v-995h-95v995h95zM151 1284c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18.167 -18 -25.5s-16.166 -13.166 -26.499 -17.499s-20.833 -6.5 -31.5 -6.5s-20.834 2.16699 -30.501 6.5s-18.334 10.166 -26.001 17.499s-13.667 15.833 -18 25.5
s-6.5 19.834 -6.5 30.501c0 11.333 2.16699 22 6.5 32s10.333 18.833 18 26.5s16.334 13.667 26.001 18s19.834 6.5 30.501 6.5s21.167 -2.16699 31.5 -6.5s19.166 -10.333 26.499 -18s13.333 -16.5 18 -26.5s7 -20.667 7 -32zM494 1284
c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18.167 -18 -25.5s-16.166 -13.166 -26.499 -17.499s-20.833 -6.5 -31.5 -6.5c-11.333 0 -21.833 2.16699 -31.5 6.5s-18.334 10.166 -26.001 17.499s-13.667 15.833 -18 25.5s-6.5 19.834 -6.5 30.501
c0 11.333 2.16699 22 6.5 32s10.333 18.833 18 26.5s16.334 13.667 26.001 18s20.167 6.5 31.5 6.5c10.667 0 21.167 -2.16699 31.5 -6.5s19.166 -10.333 26.499 -18s13.333 -16.5 18 -26.5s7 -20.667 7 -32z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1072" 
d="M454 1070c-4.66699 5.33301 -7 11.334 -7 18.001s4 13.667 12 21l126 109c-40 22 -82.167 40.833 -126.5 56.5s-91.166 28.5 -140.499 38.5c-6.66699 1.33301 -12.667 4.5 -18 9.5s-8 12.167 -8 21.5c0 2 0.833008 6.5 2.5 13.5s3.16699 11.833 4.5 14.5l6 20
c60 -9.33301 119.167 -24 177.5 -44s113.5 -46 165.5 -78l153 135l18 -29c3.33301 -5.33301 5 -10.666 5 -15.999c0 -7.33301 -4.33301 -15 -13 -23l-114 -100c42 -31.333 80.5 -67.333 115.5 -108s65.333 -87.334 91 -140.001s45.5 -111.334 59.5 -176.001s21 -136 21 -214
c0 -92 -9.5 -175.5 -28.5 -250.5s-47.5 -139.333 -85.5 -193s-85.667 -95 -143 -124s-124.333 -43.5 -201 -43.5c-60.667 0 -117.5 10.833 -170.5 32.5s-99.333 52.667 -139 93s-70.834 89.333 -93.501 147s-34 122.5 -34 194.5c0 64 9.66699 124.333 29 181
s47.5 106.167 84.5 148.5s82.5 75.666 136.5 99.999s115.333 36.5 184 36.5c35.333 0 70.833 -4.5 106.5 -13.5s70 -23 103 -42s63.833 -43.167 92.5 -72.5s53.667 -64 75 -104c-10 120.667 -38.333 221.334 -85 302.001s-106.667 146 -180 196l-164 -145zM528 63.001
c58.667 0 110.329 11.5 154.996 34.5s82.334 56 113.001 99s54.334 95.5 71.001 157.5s26 131.667 28 209c-10 34 -24.833 68 -44.5 102s-44.167 64.333 -73.5 91s-63.666 48.334 -102.999 65.001s-84.333 25 -135 25c-58.667 0 -110.167 -10 -154.5 -30
s-81.333 -47.667 -111 -83s-52 -76.833 -67 -124.5s-22.5 -98.834 -22.5 -153.501c0 -62 9.16699 -117.333 27.5 -166s43.333 -89.667 75 -123s68.334 -58.833 110.001 -76.5s85.5 -26.5 131.5 -26.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="1082" 
d="M162 0v995.001h52c17.333 0 27.333 -8.33301 30 -25l9 -150c45.333 57.333 98.166 103.5 158.499 138.5s127.5 52.5 201.5 52.5c54.667 0 102.834 -8.66699 144.501 -26s76.167 -42.333 103.5 -75s48 -72 62 -118s21 -98 21 -156v-636h-95v636
c0 93.333 -21.333 166.5 -64 219.5s-108 79.5 -196 79.5c-65.333 0 -126 -16.833 -182 -50.5s-106 -79.834 -150 -138.501v-746h-95zM664 1280c28.667 0 50.3281 9.16797 64.9951 27.501s22.334 43.166 23.001 74.499h59c0 -24 -3.16699 -46.5 -9.5 -67.5
s-15.666 -39.167 -27.999 -54.5s-27.5 -27.333 -45.5 -36s-38.333 -13 -61 -13c-22 0 -42.833 5.5 -62.5 16.5s-38.5 23 -56.5 36s-35.5 25 -52.5 36s-34.167 16.5 -51.5 16.5c-28 0 -49.333 -9.5 -64 -28.5s-22.667 -43.5 -24 -73.5h-61c0 24 3.33301 46.5 10 67.5
s16.334 39.167 29.001 54.5s28 27.333 46 36s38.333 13 61 13s43.834 -5.5 63.501 -16.5s38.334 -23 56.001 -36s35 -25 52 -36s34.167 -16.5 51.5 -16.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="1082" 
d="M541 1011c71.333 0 135.166 -12.167 191.499 -36.5s103.833 -58.833 142.5 -103.5s68.167 -98.5 88.5 -161.5s30.5 -133.5 30.5 -211.5s-10.167 -148.333 -30.5 -211s-49.833 -116.334 -88.5 -161.001s-86.167 -79 -142.5 -103s-120.166 -36 -191.499 -36
s-135.166 12 -191.499 36s-104 58.333 -143 103s-68.667 98.334 -89 161.001s-30.5 133 -30.5 211s10.167 148.5 30.5 211.5s50 116.833 89 161.5s86.667 79.167 143 103.5s120.166 36.5 191.499 36.5zM541 62c59.333 0 111.166 10.167 155.499 30.5s81.333 49.5 111 87.5
s51.834 83.833 66.501 137.5s22 113.834 22 180.501c0 66 -7.33301 126 -22 180s-36.834 100.167 -66.501 138.5s-66.667 67.833 -111 88.5s-96.166 31 -155.499 31s-111.166 -10.333 -155.499 -31s-81.333 -50.167 -111 -88.5s-52 -84.5 -67 -138.5s-22.5 -114 -22.5 -180
c0 -66.667 7.5 -126.834 22.5 -180.501s37.333 -99.5 67 -137.5s66.667 -67.167 111 -87.5s96.166 -30.5 155.499 -30.5zM428 1431c16 0 28 -2.33301 36 -7s15.667 -13 23 -25l157 -238h-54c-8 0 -14.833 1.16699 -20.5 3.5s-11.167 6.83301 -16.5 13.5l-217 253h92z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="1082" 
d="M541 1011c71.333 0 135.166 -12.167 191.499 -36.5s103.833 -58.833 142.5 -103.5s68.167 -98.5 88.5 -161.5s30.5 -133.5 30.5 -211.5s-10.167 -148.333 -30.5 -211s-49.833 -116.334 -88.5 -161.001s-86.167 -79 -142.5 -103s-120.166 -36 -191.499 -36
s-135.166 12 -191.499 36s-104 58.333 -143 103s-68.667 98.334 -89 161.001s-30.5 133 -30.5 211s10.167 148.5 30.5 211.5s50 116.833 89 161.5s86.667 79.167 143 103.5s120.166 36.5 191.499 36.5zM541 62c59.333 0 111.166 10.167 155.499 30.5s81.333 49.5 111 87.5
s51.834 83.833 66.501 137.5s22 113.834 22 180.501c0 66 -7.33301 126 -22 180s-36.834 100.167 -66.501 138.5s-66.667 67.833 -111 88.5s-96.166 31 -155.499 31s-111.166 -10.333 -155.499 -31s-81.333 -50.167 -111 -88.5s-52 -84.5 -67 -138.5s-22.5 -114 -22.5 -180
c0 -66.667 7.5 -126.834 22.5 -180.501s37.333 -99.5 67 -137.5s66.667 -67.167 111 -87.5s96.166 -30.5 155.499 -30.5zM767 1431l-216 -253c-5.33301 -6.66699 -10.833 -11.167 -16.5 -13.5s-12.5 -3.5 -20.5 -3.5h-57l157 238c7.33301 12 15 20.333 23 25s20 7 36 7h94z
" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="1082" 
d="M541 1011c71.333 0 135.166 -12.167 191.499 -36.5s103.833 -58.833 142.5 -103.5s68.167 -98.5 88.5 -161.5s30.5 -133.5 30.5 -211.5s-10.167 -148.333 -30.5 -211s-49.833 -116.334 -88.5 -161.001s-86.167 -79 -142.5 -103s-120.166 -36 -191.499 -36
s-135.166 12 -191.499 36s-104 58.333 -143 103s-68.667 98.334 -89 161.001s-30.5 133 -30.5 211s10.167 148.5 30.5 211.5s50 116.833 89 161.5s86.667 79.167 143 103.5s120.166 36.5 191.499 36.5zM541 62c59.333 0 111.166 10.167 155.499 30.5s81.333 49.5 111 87.5
s51.834 83.833 66.501 137.5s22 113.834 22 180.501c0 66 -7.33301 126 -22 180s-36.834 100.167 -66.501 138.5s-66.667 67.833 -111 88.5s-96.166 31 -155.499 31s-111.166 -10.333 -155.499 -31s-81.333 -50.167 -111 -88.5s-52 -84.5 -67 -138.5s-22.5 -114 -22.5 -180
c0 -66.667 7.5 -126.834 22.5 -180.501s37.333 -99.5 67 -137.5s66.667 -67.167 111 -87.5s96.166 -30.5 155.499 -30.5zM802 1169l-65 -0.000976562c-10.667 0 -19.667 4 -27 12l-156 163c-4 2.66699 -7.66699 6.33398 -11 11.001
c-1.33301 -2.66699 -3 -4.83398 -5 -6.50098s-3.66699 -3.16699 -5 -4.5l-157 -163c-3.33301 -3.33301 -7.33301 -6.16602 -12 -8.49902s-9.66699 -3.5 -15 -3.5h-67l218 246h85z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="1082" 
d="M541 1011c71.333 0 135.166 -12.167 191.499 -36.5s103.833 -58.833 142.5 -103.5s68.167 -98.5 88.5 -161.5s30.5 -133.5 30.5 -211.5s-10.167 -148.333 -30.5 -211s-49.833 -116.334 -88.5 -161.001s-86.167 -79 -142.5 -103s-120.166 -36 -191.499 -36
s-135.166 12 -191.499 36s-104 58.333 -143 103s-68.667 98.334 -89 161.001s-30.5 133 -30.5 211s10.167 148.5 30.5 211.5s50 116.833 89 161.5s86.667 79.167 143 103.5s120.166 36.5 191.499 36.5zM541 62c59.333 0 111.166 10.167 155.499 30.5s81.333 49.5 111 87.5
s51.834 83.833 66.501 137.5s22 113.834 22 180.501c0 66 -7.33301 126 -22 180s-36.834 100.167 -66.501 138.5s-66.667 67.833 -111 88.5s-96.166 31 -155.499 31s-111.166 -10.333 -155.499 -31s-81.333 -50.167 -111 -88.5s-52 -84.5 -67 -138.5s-22.5 -114 -22.5 -180
c0 -66.667 7.5 -126.834 22.5 -180.501s37.333 -99.5 67 -137.5s66.667 -67.167 111 -87.5s96.166 -30.5 155.499 -30.5zM657 1280c28.667 0 50.3281 9.16797 64.9951 27.501s22.334 43.166 23.001 74.499h59c0 -24 -3.16699 -46.5 -9.5 -67.5
s-15.666 -39.167 -27.999 -54.5s-27.5 -27.333 -45.5 -36s-38.333 -13 -61 -13c-22 0 -42.833 5.5 -62.5 16.5s-38.5 23 -56.5 36s-35.5 25 -52.5 36s-34.167 16.5 -51.5 16.5c-28 0 -49.333 -9.5 -64 -28.5s-22.667 -43.5 -24 -73.5h-61c0 24 3.33301 46.5 10 67.5
s16.334 39.167 29.001 54.5s28 27.333 46 36s38.333 13 61 13s43.834 -5.5 63.501 -16.5s38.334 -23 56.001 -36s35 -25 52 -36s34.167 -16.5 51.5 -16.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="1082" 
d="M541 1011c71.333 0 135.166 -12.167 191.499 -36.5s103.833 -58.833 142.5 -103.5s68.167 -98.5 88.5 -161.5s30.5 -133.5 30.5 -211.5s-10.167 -148.333 -30.5 -211s-49.833 -116.334 -88.5 -161.001s-86.167 -79 -142.5 -103s-120.166 -36 -191.499 -36
s-135.166 12 -191.499 36s-104 58.333 -143 103s-68.667 98.334 -89 161.001s-30.5 133 -30.5 211s10.167 148.5 30.5 211.5s50 116.833 89 161.5s86.667 79.167 143 103.5s120.166 36.5 191.499 36.5zM541 62c59.333 0 111.166 10.167 155.499 30.5s81.333 49.5 111 87.5
s51.834 83.833 66.501 137.5s22 113.834 22 180.501c0 66 -7.33301 126 -22 180s-36.834 100.167 -66.501 138.5s-66.667 67.833 -111 88.5s-96.166 31 -155.499 31s-111.166 -10.333 -155.499 -31s-81.333 -50.167 -111 -88.5s-52 -84.5 -67 -138.5s-22.5 -114 -22.5 -180
c0 -66.667 7.5 -126.834 22.5 -180.501s37.333 -99.5 67 -137.5s66.667 -67.167 111 -87.5s96.166 -30.5 155.499 -30.5zM453 1284c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18.167 -18 -25.5s-16.166 -13.166 -26.499 -17.499s-20.833 -6.5 -31.5 -6.5
s-20.834 2.16699 -30.501 6.5s-18.334 10.166 -26.001 17.499s-13.667 15.833 -18 25.5s-6.5 19.834 -6.5 30.501c0 11.333 2.16699 22 6.5 32s10.333 18.833 18 26.5s16.334 13.667 26.001 18s19.834 6.5 30.501 6.5s21.167 -2.16699 31.5 -6.5s19.166 -10.333 26.499 -18
s13.333 -16.5 18 -26.5s7 -20.667 7 -32zM796 1284c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18.167 -18 -25.5s-16.166 -13.166 -26.499 -17.499s-20.833 -6.5 -31.5 -6.5c-11.333 0 -21.833 2.16699 -31.5 6.5s-18.334 10.166 -26.001 17.499
s-13.667 15.833 -18 25.5s-6.5 19.834 -6.5 30.501c0 11.333 2.16699 22 6.5 32s10.333 18.833 18 26.5s16.334 13.667 26.001 18s20.167 6.5 31.5 6.5c10.667 0 21.167 -2.16699 31.5 -6.5s19.166 -10.333 26.499 -18s13.333 -16.5 18 -26.5s7 -20.667 7 -32z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M109 707h939v-73h-939v73zM486 1025c0 26 8.83301 48.168 26.5 66.501s39.5 27.5 65.5 27.5c12.667 0 24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20s15 -18.333 20 -30s7.5 -23.834 7.5 -36.501c0 -13.333 -2.5 -25.5 -7.5 -36.5s-11.667 -20.667 -20 -29
s-18.166 -14.833 -29.499 -19.5s-23.333 -7 -36 -7c-26 0 -47.833 8.83301 -65.5 26.5s-26.5 39.5 -26.5 65.5zM486 313.001c0 26 8.83301 48.168 26.5 66.501s39.5 27.5 65.5 27.5c12.667 0 24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20s15 -18.333 20 -30
s7.5 -23.834 7.5 -36.501c0 -13.333 -2.5 -25.5 -7.5 -36.5s-11.667 -20.667 -20 -29s-18.166 -14.833 -29.499 -19.5s-23.333 -7 -36 -7c-26 0 -47.833 8.83301 -65.5 26.5s-26.5 39.5 -26.5 65.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="1082" 
d="M872 873c40 -44.667 70.3311 -98.666 90.998 -161.999s31 -134.333 31 -213c0 -78 -10.167 -148.333 -30.5 -211s-49.833 -116.334 -88.5 -161.001s-86.167 -79 -142.5 -103s-120.166 -36 -191.499 -36c-107.333 0 -197 26.333 -269 79l-81 -109
c-8.66699 -11.333 -18.5 -19.5 -29.5 -24.5s-22.167 -7.5 -33.5 -7.5h-38l135 181c-45.333 44.667 -79.666 100 -102.999 166s-35 141.333 -35 226c0 78 10.167 148.5 30.5 211.5s50 116.833 89 161.5s86.667 79.167 143 103.5s120.166 36.5 191.499 36.5
c58.667 0 111.834 -8.16699 159.501 -24.5s89.834 -39.5 126.501 -69.5l87 116c7.33301 10 14.166 17.333 20.499 22s15.833 7 28.5 7h50zM179.998 498.001c0 -69.333 7.83203 -131.334 23.499 -186.001s39.5 -101 71.5 -139l504 679c-30 28 -64.667 49.333 -104 64
s-84 22 -134 22c-59.333 0 -111.5 -10.5 -156.5 -31.5s-82.667 -50.667 -113 -89s-53.166 -84.666 -68.499 -138.999s-23 -114.5 -23 -180.5zM540.997 60c59.333 0 111.5 10.333 156.5 31s82.5 50 112.5 88s52.667 84 68 138s23 114.333 23 181
c0 127.333 -26.667 229.333 -80 306l-502 -675c58 -46 132 -69 222 -69z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="1082" 
d="M233 995l-0.000976562 -636c0 -93.333 21.333 -166.5 64 -219.5s107.667 -79.5 195 -79.5c64.667 0 125 16.5 181 49.5s106.333 78.833 151 137.5v748h96v-995h-53c-18.667 0 -28.334 8.66699 -29.001 26l-9 148c-46 -57.333 -99.167 -103.333 -159.5 -138
s-127.5 -52 -201.5 -52c-55.333 0 -103.666 8.66699 -144.999 26s-75.666 42.333 -102.999 75s-48 72 -62 118s-21 98 -21 156v636h96zM420.999 1431c16 0 28 -2.33301 36 -7s15.667 -13 23 -25l157 -238h-54c-8 0 -14.833 1.16699 -20.5 3.5s-11.167 6.83301 -16.5 13.5
l-217 253h92z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="1082" 
d="M233 995l-0.000976562 -636c0 -93.333 21.333 -166.5 64 -219.5s107.667 -79.5 195 -79.5c64.667 0 125 16.5 181 49.5s106.333 78.833 151 137.5v748h96v-995h-53c-18.667 0 -28.334 8.66699 -29.001 26l-9 148c-46 -57.333 -99.167 -103.333 -159.5 -138
s-127.5 -52 -201.5 -52c-55.333 0 -103.666 8.66699 -144.999 26s-75.666 42.333 -102.999 75s-48 72 -62 118s-21 98 -21 156v636h96zM759.999 1431l-216 -253c-5.33301 -6.66699 -10.833 -11.167 -16.5 -13.5s-12.5 -3.5 -20.5 -3.5h-57l157 238
c7.33301 12 15 20.333 23 25s20 7 36 7h94z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="1082" 
d="M233 995l-0.000976562 -636c0 -93.333 21.333 -166.5 64 -219.5s107.667 -79.5 195 -79.5c64.667 0 125 16.5 181 49.5s106.333 78.833 151 137.5v748h96v-995h-53c-18.667 0 -28.334 8.66699 -29.001 26l-9 148c-46 -57.333 -99.167 -103.333 -159.5 -138
s-127.5 -52 -201.5 -52c-55.333 0 -103.666 8.66699 -144.999 26s-75.666 42.333 -102.999 75s-48 72 -62 118s-21 98 -21 156v636h96zM794.999 1169l-65 -0.000976562c-10.667 0 -19.667 4 -27 12l-156 163c-4 2.66699 -7.66699 6.33398 -11 11.001
c-1.33301 -2.66699 -3 -4.83398 -5 -6.50098s-3.66699 -3.16699 -5 -4.5l-157 -163c-3.33301 -3.33301 -7.33301 -6.16602 -12 -8.49902s-9.66699 -3.5 -15 -3.5h-67l218 246h85z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="1082" 
d="M233 995l-0.000976562 -636c0 -93.333 21.333 -166.5 64 -219.5s107.667 -79.5 195 -79.5c64.667 0 125 16.5 181 49.5s106.333 78.833 151 137.5v748h96v-995h-53c-18.667 0 -28.334 8.66699 -29.001 26l-9 148c-46 -57.333 -99.167 -103.333 -159.5 -138
s-127.5 -52 -201.5 -52c-55.333 0 -103.666 8.66699 -144.999 26s-75.666 42.333 -102.999 75s-48 72 -62 118s-21 98 -21 156v636h96zM445.999 1284c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18.167 -18 -25.5s-16.166 -13.166 -26.499 -17.499
s-20.833 -6.5 -31.5 -6.5s-20.834 2.16699 -30.501 6.5s-18.334 10.166 -26.001 17.499s-13.667 15.833 -18 25.5s-6.5 19.834 -6.5 30.501c0 11.333 2.16699 22 6.5 32s10.333 18.833 18 26.5s16.334 13.667 26.001 18s19.834 6.5 30.501 6.5s21.167 -2.16699 31.5 -6.5
s19.166 -10.333 26.499 -18s13.333 -16.5 18 -26.5s7 -20.667 7 -32zM788.999 1284c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18.167 -18 -25.5s-16.166 -13.166 -26.499 -17.499s-20.833 -6.5 -31.5 -6.5c-11.333 0 -21.833 2.16699 -31.5 6.5
s-18.334 10.166 -26.001 17.499s-13.667 15.833 -18 25.5s-6.5 19.834 -6.5 30.501c0 11.333 2.16699 22 6.5 32s10.333 18.833 18 26.5s16.334 13.667 26.001 18s20.167 6.5 31.5 6.5c10.667 0 21.167 -2.16699 31.5 -6.5s19.166 -10.333 26.499 -18s13.333 -16.5 18 -26.5
s7 -20.667 7 -32z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="974" 
d="M379 -320c-4 -9.33301 -9.16797 -16.999 -15.501 -22.999s-15.5 -9 -27.5 -9h-68l175 389l-421 958h79c10 0 17.833 -2.5 23.5 -7.5s9.83398 -10.5 12.501 -16.5l335 -774c4 -10 7.5 -20.167 10.5 -30.5s5.83301 -20.833 8.5 -31.5
c3.33301 10.667 6.83301 21.167 10.5 31.5s7.5 20.5 11.5 30.5l331 774c3.33301 7.33301 8 13.166 14 17.499s12.667 6.5 20 6.5h73zM736.999 1431l-216 -253c-5.33301 -6.66699 -10.833 -11.167 -16.5 -13.5s-12.5 -3.5 -20.5 -3.5h-57l157 238c7.33301 12 15 20.333 23 25
s20 7 36 7h94z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1070" 
d="M162 -352l0.000976562 1807h95v-635c44 58.667 95 105.167 153 139.5s123.333 51.5 196 51.5c122.667 0 218 -42.167 286 -126.5s102 -211.166 102 -380.499c0 -72.667 -9.5 -140.5 -28.5 -203.5s-46.833 -117.667 -83.5 -164s-81.834 -82.833 -135.501 -109.5
s-115.5 -40 -185.5 -40c-68 0 -126.667 13 -176 39s-92 64.333 -128 115v-493h-95zM579.001 934.999c-66 0 -125.832 -17.168 -179.499 -51.501s-101.167 -82.166 -142.5 -143.499v-522c38 -57.333 80.167 -97.666 126.5 -120.999s99.166 -35 158.499 -35
c58.667 0 110 10.667 154 32s80.833 51.5 110.5 90.5s52 85.667 67 140s22.5 114.166 22.5 179.499c0 148 -27.167 256.833 -81.5 326.5s-132.833 104.5 -235.5 104.5z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="974" 
d="M379 -320c-4 -9.33301 -9.16797 -16.999 -15.501 -22.999s-15.5 -9 -27.5 -9h-68l175 389l-421 958h79c10 0 17.833 -2.5 23.5 -7.5s9.83398 -10.5 12.501 -16.5l335 -774c4 -10 7.5 -20.167 10.5 -30.5s5.83301 -20.833 8.5 -31.5
c3.33301 10.667 6.83301 21.167 10.5 31.5s7.5 20.5 11.5 30.5l331 774c3.33301 7.33301 8 13.166 14 17.499s12.667 6.5 20 6.5h73zM422.999 1284c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18.167 -18 -25.5s-16.166 -13.166 -26.499 -17.499
s-20.833 -6.5 -31.5 -6.5s-20.834 2.16699 -30.501 6.5s-18.334 10.166 -26.001 17.499s-13.667 15.833 -18 25.5s-6.5 19.834 -6.5 30.501c0 11.333 2.16699 22 6.5 32s10.333 18.833 18 26.5s16.334 13.667 26.001 18s19.834 6.5 30.501 6.5s21.167 -2.16699 31.5 -6.5
s19.166 -10.333 26.499 -18s13.333 -16.5 18 -26.5s7 -20.667 7 -32zM765.999 1284c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18.167 -18 -25.5s-16.166 -13.166 -26.499 -17.499s-20.833 -6.5 -31.5 -6.5c-11.333 0 -21.833 2.16699 -31.5 6.5
s-18.334 10.166 -26.001 17.499s-13.667 15.833 -18 25.5s-6.5 19.834 -6.5 30.501c0 11.333 2.16699 22 6.5 32s10.333 18.833 18 26.5s16.334 13.667 26.001 18s20.167 6.5 31.5 6.5c10.667 0 21.167 -2.16699 31.5 -6.5s19.166 -10.333 26.499 -18s13.333 -16.5 18 -26.5
s7 -20.667 7 -32z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1289" 
d="M1350 -243c6.66699 0 11 -2.66699 13 -8l16 -34c-18.667 -16 -42 -28.833 -70 -38.5s-57.667 -14.5 -89 -14.5c-50.667 0 -91.667 12.5 -123 37.5s-47 59.5 -47 103.5c0 20.667 3.83301 40.5 11.5 59.5s18 36.833 31 53.5s27.667 32 44 46s33.166 26.667 50.499 38
c-13.333 2.66699 -22.666 11.667 -27.999 27l-167 411h-695l-166 -411c-2.66699 -7.33301 -7.33398 -13.666 -14.001 -18.999s-14.667 -8 -24 -8h-78l579 1415h102l579 -1415c-8 0 -20.333 -4.16699 -37 -12.5s-33.5 -20.333 -50.5 -36s-31.833 -34.334 -44.5 -56.001
s-19 -45.5 -19 -71.5c0 -32 10.667 -56.833 32 -74.5s48.666 -26.5 81.999 -26.5c18.667 0 34.334 1.83301 47.001 5.5s23.334 7.5 32.001 11.5s15.5 7.83301 20.5 11.5s9.16699 5.5 12.5 5.5zM328 514l634.004 0.000976562l-289 715
c-4.66699 11.333 -9.33398 24.166 -14.001 38.499s-9.33398 29.5 -14.001 45.5c-4.66699 -16 -9.33398 -31.167 -14.001 -45.5s-9.33398 -27.5 -14.001 -39.5z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="973" 
d="M928 -243c6.66699 0 10.998 -2.66406 12.998 -7.99707l16 -34c-18.667 -16 -42 -28.833 -70 -38.5s-57.667 -14.5 -89 -14.5c-50.667 0 -91.667 12.5 -123 37.5s-47 59.5 -47 103.5c0 21.333 4.16699 41.833 12.5 61.5s19.333 38 33 55s29.167 32.667 46.5 47
s35 27.166 53 38.499c-6.66699 6 -11 13.667 -13 23l-14 127c-27.333 -26.667 -54.5 -50.667 -81.5 -72s-55.167 -39.333 -84.5 -54s-61 -25.834 -95 -33.501s-71.333 -11.5 -112 -11.5c-34 0 -67 5 -99 15s-60.333 25.333 -85 46s-44.5 47.167 -59.5 79.5
s-22.5 71.166 -22.5 116.499c0 42 12 81 36 117s61.5 67.333 112.5 94s116.667 48 197 64s176.833 25.333 289.5 28v104c0 92 -19.833 162.833 -59.5 212.5s-98.5 74.5 -176.5 74.5c-48 0 -88.833 -6.66699 -122.5 -20s-62.167 -28 -85.5 -44s-42.333 -30.667 -57 -44
s-27 -20 -37 -20c-7.33301 0 -13.333 1.66699 -18 5s-8.66699 7.66602 -12 12.999l-17 29c52 52 106.667 91.333 164 118s122.333 40 195 40c53.333 0 100 -8.5 140 -25.5s73.167 -41.333 99.5 -73s46.166 -70 59.499 -115s20 -95.167 20 -150.5v-648
c-13.333 -7.33301 -27.666 -16.666 -42.999 -27.999s-29.666 -24.5 -42.999 -39.5s-24.5 -31.667 -33.5 -50s-13.5 -37.833 -13.5 -58.5c0 -32 10.667 -56.833 32 -74.5s48.666 -26.5 81.999 -26.5c18.667 0 34.334 1.83301 47.001 5.5s23.334 7.5 32.001 11.5
s15.5 7.83301 20.5 11.5s9.16699 5.5 12.5 5.5zM396.998 53.0029c38.667 0 74.166 4.16699 106.499 12.5s62.333 20 90 35s53.5 32.833 77.5 53.5s47.667 43 71 67v259c-94.667 -2.66699 -176.167 -9.83398 -244.5 -21.501s-124.666 -27.5 -168.999 -47.5
s-77 -43.833 -98 -71.5s-31.5 -59.167 -31.5 -94.5c0 -33.333 5.5 -62.166 16.5 -86.499s25.5 -44.333 43.5 -60s39 -27.167 63 -34.5s49 -11 75 -11z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1407" 
d="M1238 238c6.66699 0 12.3301 -2.33203 16.9971 -6.99902l40 -43c-29.333 -31.333 -61.333 -59.5 -96 -84.5s-72.5 -46.333 -113.5 -64s-86 -31.334 -135 -41.001s-102.833 -14.5 -161.5 -14.5c-98 0 -187.833 17.167 -269.5 51.5s-151.667 83 -210 146
s-103.833 139.167 -136.5 228.5s-49 188.666 -49 297.999c0 107.333 16.833 205.333 50.5 294s81 164.834 142 228.501s134 113 219 148s178.833 52.5 281.5 52.5c51.333 0 98.333 -3.83301 141 -11.5s82.5 -18.667 119.5 -33s71.833 -32.166 104.5 -53.499
s64.667 -46 96 -74l-31 -45c-5.33301 -8 -13.666 -12 -24.999 -12c-6 0 -13.667 3.5 -23 10.5l-35.5 26c-14.333 10.333 -31.666 21.666 -51.999 33.999s-44.5 23.666 -72.5 33.999s-60.333 19 -97 26s-78.334 10.5 -125.001 10.5c-86 0 -165 -14.833 -237 -44.5
s-134 -72 -186 -127s-92.5 -121.667 -121.5 -200s-43.5 -166.166 -43.5 -263.499c0 -100 14.333 -189.333 43 -268s68.334 -145.167 119.001 -199.5s110.5 -96 179.5 -125s143.833 -43.5 224.5 -43.5c50.667 0 95.834 3.33301 135.501 10s76.167 16.667 109.5 30
s64.333 29.666 93 48.999s57 42 85 68c3.33301 2.66699 6.5 4.83398 9.5 6.50098s6.5 2.5 10.5 2.5zM1109 1770l-291.999 -215.999c-6.66699 -4.66699 -12.834 -7.83398 -18.501 -9.50098s-12.5 -2.5 -20.5 -2.5h-70l227 206c9.33301 8.66699 18 14.5 26 17.5s20 4.5 36 4.5
h112z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="910" 
d="M817 862c-3.33301 -3.33301 -6.50488 -6.16504 -9.50488 -8.49805s-7.16699 -3.5 -12.5 -3.5c-6.66699 0 -15.834 4.5 -27.501 13.5s-27.167 18.833 -46.5 29.5s-43.5 20.5 -72.5 29.5s-64.167 13.5 -105.5 13.5c-57.333 0 -108.166 -10.167 -152.499 -30.5
s-81.666 -49.5 -111.999 -87.5s-53.5 -84 -69.5 -138s-24 -114.667 -24 -182c0 -70 8.16699 -132 24.5 -186s39.5 -99.5 69.5 -136.5s66.333 -65.167 109 -84.5s90 -29 142 -29c48 0 88.167 5.66699 120.5 17s59 23.666 80 36.999s37.667 25.666 50 36.999
s22.166 17 29.499 17c8.66699 0 15.334 -3.33301 20.001 -10l26 -32c-15.333 -20 -34.666 -38.667 -57.999 -56s-49.666 -32.333 -78.999 -45s-61.166 -22.5 -95.499 -29.5s-70.166 -10.5 -107.499 -10.5c-63.333 0 -121.166 11.5 -173.499 34.5s-97.333 56.333 -135 100
s-67 97.167 -88 160.5s-31.5 135.333 -31.5 216c0 76 10 145.333 30 208s49.167 116.667 87.5 162s85.333 80.5 141 105.5s119.5 37.5 191.5 37.5c64.667 0 122.167 -10.333 172.5 -31s94.166 -48.667 131.499 -84zM764.995 1431l-216 -253
c-5.33301 -6.66699 -10.833 -11.167 -16.5 -13.5s-12.5 -3.5 -20.5 -3.5h-57l157 238c7.33301 12 15 20.333 23 25s20 7 36 7h94z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="1224" 
d="M1060 -243c6.66699 0 10.9971 -2.66895 12.9971 -8.00195l16 -34c-18.667 -16 -42 -28.833 -70 -38.5s-57.667 -14.5 -89 -14.5c-50.667 0 -91.667 12.5 -123 37.5s-47 59.5 -47 103.5c0 20.667 3.83301 40.334 11.5 59.001s17.834 36.334 30.501 53.001
s27.167 32.167 43.5 46.5s33.166 27.166 50.499 38.499h-686v1415h848v-85h-745v-572h620v-83h-620v-590h745v-85h-91c-13.333 -7.33301 -27.666 -16.666 -42.999 -27.999s-29.666 -24.5 -42.999 -39.5s-24.5 -31.667 -33.5 -50s-13.5 -37.833 -13.5 -58.5
c0 -32 10.667 -56.833 32 -74.5s48.666 -26.5 81.999 -26.5c18.667 0 34.334 1.83301 47.001 5.5s23.334 7.5 32.001 11.5s15.5 7.83301 20.5 11.5s9.16699 5.5 12.5 5.5z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="1022" 
d="M745 -243c6.66699 0 10.9961 -2.66895 12.9961 -8.00195l16 -34c-18.667 -16 -42 -28.833 -70 -38.5s-57.667 -14.5 -89 -14.5c-50.667 0 -91.667 12.5 -123 37.5s-47 59.5 -47 103.5c0 18.667 3.16699 36.667 9.5 54s15 33.833 26 49.5s23.667 30.334 38 44.001
s29.166 26.167 44.499 37.5c-3.33301 0 -6.66602 -0.166992 -9.99902 -0.5s-6.66602 -0.5 -9.99902 -0.5c-68 0 -130 11.833 -186 35.5s-104 58.167 -144 103.5s-70.833 100.833 -92.5 166.5s-32.5 140.834 -32.5 225.501c0 71.333 10.167 137.166 30.5 197.499
s49.666 112.333 87.999 156s85.333 77.834 141 102.501s118.834 37 189.501 37c56 0 107.833 -9.66699 155.5 -29s89 -47.5 124 -84.5s62.333 -82.5 82 -136.5s29.5 -116 29.5 -186c0 -14.667 -2.16699 -24.667 -6.5 -30s-11.166 -8 -20.499 -8h-719v-19
c0 -74.667 8.66699 -140.334 26 -197.001s42 -104.167 74 -142.5s70.667 -67.166 116 -86.499s96 -29 152 -29c50 0 93.333 5.5 130 16.5s67.5 23.333 92.5 37s44.833 26 59.5 37s25.334 16.5 32.001 16.5c8.66699 0 15.334 -3.33301 20.001 -10l26 -32
c-12.667 -15.333 -28.167 -30 -46.5 -44s-39 -26.667 -62 -38s-47.833 -21.166 -74.5 -29.499s-54 -14.833 -82 -19.5c-13.333 -8 -27.333 -17.667 -42 -29s-28.334 -24.333 -41.001 -39s-23 -30.834 -31 -48.501s-12 -36.5 -12 -56.5c0 -32 10.667 -56.833 32 -74.5
s48.666 -26.5 81.999 -26.5c18.667 0 34.334 1.83301 47.001 5.5s23.334 7.5 32.001 11.5s15.5 7.83301 20.5 11.5s9.16699 5.5 12.5 5.5zM537.996 939.998c-51.333 0 -97.332 -7.99805 -137.999 -23.998s-75.834 -39 -105.501 -69s-53.667 -66 -72 -108
s-30.5 -89 -36.5 -141h656c0 53.333 -7.33301 101.166 -22 143.499s-35.334 78.166 -62.001 107.499s-58.667 51.833 -96 67.5s-78.666 23.5 -123.999 23.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="475" 
d="M285 995v-995h-95v995h95z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="1066" 
d="M355 722l417 216.999v-63c0 -7.33301 -1.33301 -13.333 -4 -18s-7.66699 -9 -15 -13l-398 -212v-546h671v-87h-773v587l-195 -101v66c0 12.667 6 22.334 18 29.001l177 93v741h102v-693z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="600" 
d="M347 1455v-593l196 89v-50c0 -7.33301 -1.33301 -13.5 -4 -18.5s-7.66699 -9.16699 -15 -12.5l-177 -84v-786h-95v750l-195 -90v52c0 14 6 23.667 18 29l177 84v630h95z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1512" 
d="M260 1415c9.33301 0 16.666 -1.16699 21.999 -3.5s11 -7.5 17 -15.5l916 -1226c-2 20 -3 39.333 -3 58v1187h90v-1415h-49c-15.333 0 -27.666 6.66699 -36.999 20l-920 1228c2 -20 3 -39 3 -57v-1191h-89v1415h50v0zM1081 1770l-291.999 -215.999
c-6.66699 -4.66699 -12.834 -7.83398 -18.501 -9.50098s-12.5 -2.5 -20.5 -2.5h-70l227 206c9.33301 8.66699 18 14.5 26 17.5s20 4.5 36 4.5h112z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="1082" 
d="M162 0v995.001h52c17.333 0 27.333 -8.33301 30 -25l9 -150c45.333 57.333 98.166 103.5 158.499 138.5s127.5 52.5 201.5 52.5c54.667 0 102.834 -8.66699 144.501 -26s76.167 -42.333 103.5 -75s48 -72 62 -118s21 -98 21 -156v-636h-95v636
c0 93.333 -21.333 166.5 -64 219.5s-108 79.5 -196 79.5c-65.333 0 -126 -16.833 -182 -50.5s-106 -79.834 -150 -138.501v-746h-95zM776 1431l-216 -253c-5.33301 -6.66699 -10.833 -11.167 -16.5 -13.5s-12.5 -3.5 -20.5 -3.5h-57l157 238c7.33301 12 15 20.333 23 25
s20 7 36 7h94z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="2227" 
d="M2103 1330l-745 0.000976562v-572h620v-83h-620v-590h745v-85h-837v334c-24.667 -54.667 -54.834 -103.5 -90.501 -146.5s-76.167 -79.333 -121.5 -109s-95.166 -52.334 -149.499 -68.001s-112.5 -23.5 -174.5 -23.5c-90 0 -172.167 17 -246.5 51
s-138.166 82.5 -191.499 145.5s-94.833 138.833 -124.5 227.5s-44.5 187.667 -44.5 297c0 108.667 14.833 207.5 44.5 296.5s71.167 165 124.5 228s117.166 111.833 191.499 146.5s156.5 52 246.5 52c62 0 120.167 -8 174.5 -24s104.166 -39 149.499 -69
s85.833 -66.5 121.5 -109.5s65.834 -91.833 90.501 -146.5v333h837v-85zM1255 708.001c0 98.667 -12.333 187.334 -37 266.001s-59.5 145.5 -104.5 200.5s-99 97.167 -162 126.5s-132.833 44 -209.5 44c-76 0 -145.667 -14.667 -209 -44s-117.5 -71.5 -162.5 -126.5
s-80 -121.833 -105 -200.5s-37.5 -167.334 -37.5 -266.001c0 -99.333 12.5 -188.333 37.5 -267s60 -145.167 105 -199.5s99.167 -96 162.5 -125s133 -43.5 209 -43.5c76.667 0 146.5 14.5 209.5 43.5s117 70.667 162 125s79.833 120.833 104.5 199.5s37 167.667 37 267z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1717" 
d="M1266 1011c50 0 96.834 -10.001 140.501 -30.001s81.5 -49.167 113.5 -87.5s57.167 -85.833 75.5 -142.5s27.5 -122 27.5 -196c0 -14.667 -1.83301 -24.667 -5.5 -30s-10.167 -8 -19.5 -8h-660c0 -74.667 8 -140.334 24 -197.001s38.833 -104 68.5 -142
s65.167 -66.667 106.5 -86s87.333 -29 138 -29c44.667 0 83.5 5.5 116.5 16.5s61 23.333 84 37s41.333 26 55 37s24.167 16.5 31.5 16.5c8 0 14.333 -3.33301 19 -10l26 -32c-15.333 -20 -35.333 -38.667 -60 -56s-52 -32.166 -82 -44.499s-61.833 -22.166 -95.5 -29.499
s-67.834 -11 -102.501 -11c-90 0 -167.333 24.833 -232 74.5s-111.667 123.167 -141 220.5c-28 -92.667 -74.667 -165 -140 -217s-147.333 -78 -246 -78c-65.333 0 -124 12 -176 36s-96.167 58.333 -132.5 103s-64.166 98.334 -83.499 161.001s-29 133 -29 211
s9.66699 148.5 29 211.5s47.166 116.833 83.499 161.5s80.833 79.167 133.5 103.5s112 36.5 178 36.5c96 0 176.333 -25.667 241 -77s111 -122 139 -212c13.333 44 31.5 83.667 54.5 119s50.167 65.666 81.5 90.999s67 44.833 107 58.5s84 20.5 132 20.5zM516.001 61.999
c54.667 0 102.5 10.167 143.5 30.5s75.167 49.5 102.5 87.5s47.833 83.833 61.5 137.5s20.5 113.834 20.5 180.501c0 66 -6.83301 126 -20.5 180s-34.167 100.167 -61.5 138.5s-61.5 67.833 -102.5 88.5s-88.833 31 -143.5 31c-55.333 0 -103.666 -10.333 -144.999 -31
s-75.833 -50.167 -103.5 -88.5s-48.334 -84.5 -62.001 -138.5s-20.5 -114 -20.5 -180c0 -66.667 6.83301 -126.834 20.5 -180.501s34.334 -99.5 62.001 -137.5s62.167 -67.167 103.5 -87.5s89.666 -30.5 144.999 -30.5zM1264 939.999c-48 0 -91.1689 -8.66699 -129.502 -26
s-71.166 -41.666 -98.499 -72.999s-49.166 -69.333 -65.499 -114s-26.5 -94.334 -30.5 -149.001h599c0 56.667 -6.66699 107.5 -20 152.5s-32.166 83 -56.499 114s-53.333 54.667 -87 71s-70.834 24.5 -111.501 24.5z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1082" 
d="M894 1243c-6 -11.333 -14.667 -16.998 -26 -16.998c-8.66699 0 -19.834 6.16699 -33.501 18.5s-32.167 26 -55.5 41s-52.5 28.833 -87.5 41.5s-78.167 19 -129.5 19s-96.5 -7.33301 -135.5 -22s-71.667 -34.667 -98 -60s-46.333 -54.666 -60 -87.999
s-20.5 -68.333 -20.5 -105c0 -48 10.167 -87.667 30.5 -119s47.166 -58 80.499 -80s71.166 -40.5 113.499 -55.5l130.5 -44.5c44.667 -14.667 88.167 -30.834 130.5 -48.501s80.166 -39.834 113.499 -66.501s60.166 -59.5 80.499 -98.5s30.5 -87.5 30.5 -145.5
c0 -59.333 -10.167 -115.166 -30.5 -167.499s-49.833 -97.833 -88.5 -136.5s-86 -69.167 -142 -91.5s-120 -33.5 -192 -33.5c-93.333 0 -173.666 16.5 -240.999 49.5s-126.333 78.167 -177 135.5l28 44c8 10 17.333 15 28 15c6 0 13.667 -4 23 -12l34 -29.5
c13.333 -11.667 29.333 -24.334 48 -38.001s40.334 -26.334 65.001 -38.001s53 -21.5 85 -29.5s68.333 -12 109 -12c56 0 106 8.5 150 25.5s81.167 40.167 111.5 69.5s53.5 64.166 69.5 104.499s24 83.5 24 129.5c0 50 -10.167 91.167 -30.5 123.5
s-47.166 59.333 -80.499 81s-71.166 39.834 -113.499 54.501s-85.833 29 -130.5 43s-88.167 29.667 -130.5 47s-80.166 39.5 -113.499 66.5s-60.166 60.667 -80.499 101s-30.5 90.833 -30.5 151.5c0 47.333 9 93 27 137s44.333 82.833 79 116.5s77.5 60.667 128.5 81
s109.167 30.5 174.5 30.5c73.333 0 139.166 -11.667 197.499 -35s111.5 -59 159.5 -107zM853 1770l-291.999 -215.999c-6.66699 -4.66699 -12.834 -7.83398 -18.501 -9.50098s-12.5 -2.5 -20.5 -2.5h-70l227 206c9.33301 8.66699 18 14.5 26 17.5s20 4.5 36 4.5h112z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="860" 
d="M710 872c-4.66699 -9.33301 -12 -14.002 -22 -14.002c-7.33301 0 -16.833 4.16699 -28.5 12.5s-27 17.666 -46 27.999s-42.333 19.666 -70 27.999s-61.5 12.5 -101.5 12.5c-36 0 -68.833 -5.16699 -98.5 -15.5s-55 -24.166 -76 -41.499s-37.333 -37.5 -49 -60.5
s-17.5 -47.167 -17.5 -72.5c0 -31.333 8 -57.333 24 -78s37 -38.334 63 -53.001s55.5 -27.334 88.5 -38.001l101.5 -32c34.667 -10.667 68.5 -22.5 101.5 -35.5s62.5 -29.167 88.5 -48.5s47 -43 63 -71s24 -62 24 -102c0 -43.333 -7.83301 -83.666 -23.5 -120.999
s-38.334 -69.666 -68.001 -96.999s-66.167 -49 -109.5 -65s-92.666 -24 -147.999 -24c-70 0 -130.333 11.167 -181 33.5s-96 51.5 -136 87.5l23 34c3.33301 5.33301 7 9.33301 11 12s9.66699 4 17 4c8.66699 0 19.334 -5.33301 32.001 -16s29.167 -22.167 49.5 -34.5
s45.666 -23.833 75.999 -34.5s67.833 -16 112.5 -16c42 0 79 5.83301 111 17.5s58.667 27.5 80 47.5s37.5 43.5 48.5 70.5s16.5 55.5 16.5 85.5c0 33.333 -8 61 -24 83s-37 40.667 -63 56s-55.5 28.333 -88.5 39l-102 32c-35 10.667 -69 22.334 -102 35.001
s-62.5 28.667 -88.5 48s-47 42.833 -63 70.5s-24 62.167 -24 103.5c0 35.333 7.66699 69.333 23 102s37.166 61.334 65.499 86.001s62.833 44.334 103.5 59.001s86.334 22 137.001 22c60.667 0 114.5 -8.66699 161.5 -26s90.167 -44 129.5 -80zM705 1431l-216 -253
c-5.33301 -6.66699 -10.833 -11.167 -16.5 -13.5s-12.5 -3.5 -20.5 -3.5h-57l157 238c7.33301 12 15 20.333 23 25s20 7 36 7h94z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1045" 
d="M894 1243c-6 -11.333 -14.667 -16.998 -26 -16.998c-8.66699 0 -19.834 6.16699 -33.501 18.5s-32.167 26 -55.5 41s-52.5 28.833 -87.5 41.5s-78.167 19 -129.5 19s-96.5 -7.33301 -135.5 -22s-71.667 -34.667 -98 -60s-46.333 -54.666 -60 -87.999
s-20.5 -68.333 -20.5 -105c0 -48 10.167 -87.667 30.5 -119s47.166 -58 80.499 -80s71.166 -40.5 113.499 -55.5l130.5 -44.5c44.667 -14.667 88.167 -30.834 130.5 -48.501s80.166 -39.834 113.499 -66.501s60.166 -59.5 80.499 -98.5s30.5 -87.5 30.5 -145.5
c0 -59.333 -10.167 -115.166 -30.5 -167.499s-49.833 -97.833 -88.5 -136.5s-86 -69.167 -142 -91.5s-120 -33.5 -192 -33.5c-93.333 0 -173.666 16.5 -240.999 49.5s-126.333 78.167 -177 135.5l28 44c8 10 17.333 15 28 15c6 0 13.667 -4 23 -12l34 -29.5
c13.333 -11.667 29.333 -24.334 48 -38.001s40.334 -26.334 65.001 -38.001s53 -21.5 85 -29.5s68.333 -12 109 -12c56 0 106 8.5 150 25.5s81.167 40.167 111.5 69.5s53.5 64.166 69.5 104.499s24 83.5 24 129.5c0 50 -10.167 91.167 -30.5 123.5
s-47.166 59.333 -80.499 81s-71.166 39.834 -113.499 54.501s-85.833 29 -130.5 43s-88.167 29.667 -130.5 47s-80.166 39.5 -113.499 66.5s-60.166 60.667 -80.499 101s-30.5 90.833 -30.5 151.5c0 47.333 9 93 27 137s44.333 82.833 79 116.5s77.5 60.667 128.5 81
s109.167 30.5 174.5 30.5c73.333 0 139.166 -11.667 197.499 -35s111.5 -59 159.5 -107zM293 1741l74.001 -0.000976562c5.33301 0 11 -0.833008 17 -2.5s11.333 -4.16699 16 -7.5l157 -123c5.33301 -4 9 -7 11 -9l5 -5c1.33301 1.33301 3 2.83301 5 4.5
s6 4.83398 12 9.50098l156 123c4.66699 3.33301 10.167 5.83301 16.5 7.5s12.166 2.5 17.499 2.5h73l-235 -199h-90z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="860" 
d="M710 872c-4.66699 -9.33301 -12 -14.002 -22 -14.002c-7.33301 0 -16.833 4.16699 -28.5 12.5s-27 17.666 -46 27.999s-42.333 19.666 -70 27.999s-61.5 12.5 -101.5 12.5c-36 0 -68.833 -5.16699 -98.5 -15.5s-55 -24.166 -76 -41.499s-37.333 -37.5 -49 -60.5
s-17.5 -47.167 -17.5 -72.5c0 -31.333 8 -57.333 24 -78s37 -38.334 63 -53.001s55.5 -27.334 88.5 -38.001l101.5 -32c34.667 -10.667 68.5 -22.5 101.5 -35.5s62.5 -29.167 88.5 -48.5s47 -43 63 -71s24 -62 24 -102c0 -43.333 -7.83301 -83.666 -23.5 -120.999
s-38.334 -69.666 -68.001 -96.999s-66.167 -49 -109.5 -65s-92.666 -24 -147.999 -24c-70 0 -130.333 11.167 -181 33.5s-96 51.5 -136 87.5l23 34c3.33301 5.33301 7 9.33301 11 12s9.66699 4 17 4c8.66699 0 19.334 -5.33301 32.001 -16s29.167 -22.167 49.5 -34.5
s45.666 -23.833 75.999 -34.5s67.833 -16 112.5 -16c42 0 79 5.83301 111 17.5s58.667 27.5 80 47.5s37.5 43.5 48.5 70.5s16.5 55.5 16.5 85.5c0 33.333 -8 61 -24 83s-37 40.667 -63 56s-55.5 28.333 -88.5 39l-102 32c-35 10.667 -69 22.334 -102 35.001
s-62.5 28.667 -88.5 48s-47 42.833 -63 70.5s-24 62.167 -24 103.5c0 35.333 7.66699 69.333 23 102s37.166 61.334 65.499 86.001s62.833 44.334 103.5 59.001s86.334 22 137.001 22c60.667 0 114.5 -8.66699 161.5 -26s90.167 -44 129.5 -80zM195 1415h67
c10.667 0 19.667 -3.66699 27 -11l156 -163l11 -13l11 13l156 163c3.33301 4 7.5 6.83301 12.5 8.5s9.83301 2.5 14.5 2.5h65l-217 -246h-85z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1199" 
d="M651 584v-584h-103v584l-523 831h89c9.33301 0 16.833 -2.16699 22.5 -6.5s11.167 -10.5 16.5 -18.5l405 -650c9.33301 -15.333 17.333 -30.166 24 -44.499s12.667 -28.833 18 -43.5c5.33301 14.667 11.333 29.167 18 43.5s14.667 29.166 24 44.499l404 650
c4 7.33301 9 13.333 15 18s13.667 7 23 7h90zM473 1665c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18 -18 -25s-16 -12.667 -26 -17s-20.333 -6.5 -31 -6.5s-20.834 2.16699 -30.501 6.5s-18.167 10 -25.5 17s-13.166 15.333 -17.499 25s-6.5 19.834 -6.5 30.501
s2.16699 21 6.5 31s10.166 18.833 17.499 26.5s15.833 13.667 25.5 18s19.834 6.5 30.501 6.5s21 -2.16699 31 -6.5s18.667 -10.333 26 -18s13.333 -16.5 18 -26.5s7 -20.333 7 -31zM889 1665c0 -10.667 -2.16797 -20.834 -6.50098 -30.501s-10.333 -18 -18 -25
s-16.334 -12.667 -26.001 -17s-19.834 -6.5 -30.501 -6.5c-11.333 0 -21.833 2.16699 -31.5 6.5s-18.167 10 -25.5 17s-13.166 15.333 -17.499 25s-6.5 19.834 -6.5 30.501c0 22 8 41.167 24 57.5s35 24.5 57 24.5c10.667 0 20.834 -2.16699 30.501 -6.5
s18.334 -10.333 26.001 -18s13.667 -16.5 18 -26.5s6.5 -20.333 6.5 -31z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="1266" 
d="M1163 1415v-35c0 -14 -4.66699 -27.667 -14 -41l-898 -1254h904v-85h-1040v37c0 12.667 4 24.667 12 36l900 1257h-886v85h1022zM946 1770l-291.999 -215.999c-6.66699 -4.66699 -12.834 -7.83398 -18.501 -9.50098s-12.5 -2.5 -20.5 -2.5h-70l227 206
c9.33301 8.66699 18 14.5 26 17.5s20 4.5 36 4.5h112z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="904" 
d="M826 955c0 -14 -4.66699 -26.666 -14 -37.999l-623 -842h612v-75h-727v38c0 11.333 5 23.666 15 36.999l625 845h-607v75h719v-40zM698 1431l-216 -253c-5.33301 -6.66699 -10.833 -11.167 -16.5 -13.5s-12.5 -3.5 -20.5 -3.5h-57l157 238c7.33301 12 15 20.333 23 25
s20 7 36 7h94z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="1266" 
d="M1163 1415v-35c0 -14 -4.66699 -27.667 -14 -41l-898 -1254h904v-85h-1040v37c0 12.667 4 24.667 12 36l900 1257h-886v85h1022zM742 1677c0 -11.333 -2.5 -22.166 -7.5 -32.499s-11.5 -19.333 -19.5 -27s-17.333 -13.834 -28 -18.501s-21.667 -7 -33 -7
s-22.166 2.33301 -32.499 7s-19.333 10.834 -27 18.501s-13.834 16.667 -18.501 27s-7 21.166 -7 32.499c0 12 2.33301 23.167 7 33.5s10.834 19.5 18.501 27.5s16.667 14.333 27 19s21.166 7 32.499 7s22.333 -2.33301 33 -7s20 -11 28 -19s14.5 -17.167 19.5 -27.5
s7.5 -21.5 7.5 -33.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="904" 
d="M826 955c0 -14 -4.66699 -26.666 -14 -37.999l-623 -842h612v-75h-727v38c0 11.333 5 23.666 15 36.999l625 845h-607v75h719v-40zM562 1350c0 -12 -2.5 -23.167 -7.5 -33.5s-11.5 -19.5 -19.5 -27.5s-17.333 -14.333 -28 -19s-22 -7 -34 -7s-23.333 2.33301 -34 7
s-20 11 -28 19s-14.333 17.167 -19 27.5s-7 21.5 -7 33.5s2.33301 23.5 7 34.5s11 20.5 19 28.5s17.333 14.333 28 19s22 7 34 7s23.333 -2.33301 34 -7s20 -11 28 -19s14.5 -17.5 19.5 -28.5s7.5 -22.5 7.5 -34.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="1266" 
d="M1163 1415v-35c0 -14 -4.66699 -27.667 -14 -41l-898 -1254h904v-85h-1040v37c0 12.667 4 24.667 12 36l900 1257h-886v85h1022zM375 1741l74.001 -0.000976562c5.33301 0 11 -0.833008 17 -2.5s11.333 -4.16699 16 -7.5l157 -123c5.33301 -4 9 -7 11 -9l5 -5
c1.33301 1.33301 3 2.83301 5 4.5s6 4.83398 12 9.50098l156 123c4.66699 3.33301 10.167 5.83301 16.5 7.5s12.166 2.5 17.499 2.5h73l-235 -199h-90z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="904" 
d="M826 955c0 -14 -4.66699 -26.666 -14 -37.999l-623 -842h612v-75h-727v38c0 11.333 5 23.666 15 36.999l625 845h-607v75h719v-40zM213 1415h67c10.667 0 19.667 -3.66699 27 -11l156 -163l11 -13l11 13l156 163c3.33301 4 7.5 6.83301 12.5 8.5s9.83301 2.5 14.5 2.5h65
l-217 -246h-85z" />
    <glyph glyph-name="florin" unicode="&#x192;" 
d="M635 797l-96 -783.998c-8 -65.333 -23.333 -121.333 -46 -168s-52 -85.334 -88 -116.001s-78.167 -53.167 -126.5 -67.5s-101.833 -21.5 -160.5 -21.5v48c0 21.333 12 32 36 32c34 0.666992 67 6.16699 99 16.5s61 27.166 87 50.499s48.167 53.666 66.5 90.999
s30.833 83.333 37.5 138l95 778l-192 10c-17.333 1.33301 -26 9.66602 -26 24.999v39h227l24 191c15.333 128 59 222.333 131 283s168.667 91 290 91v-51c0 -20 -12 -30 -36 -30c-35.333 0 -69 -5.16699 -101 -15.5s-61 -27.166 -87 -50.499s-48.167 -53.666 -66.5 -90.999
s-30.833 -83.333 -37.5 -138l-25 -189h357v-71h-362z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="585" 
d="M552 1169l-65 -0.000976562c-10.667 0 -19.667 4 -27 12l-156 163c-4 2.66699 -7.66699 6.33398 -11 11.001c-1.33301 -2.66699 -3 -4.83398 -5 -6.50098s-3.66699 -3.16699 -5 -4.5l-157 -163c-3.33301 -3.33301 -7.33301 -6.16602 -12 -8.49902s-9.66699 -3.5 -15 -3.5
h-67l218 246h85z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="585" 
d="M32 1415h67c10.667 0 19.667 -3.66699 27 -11l156 -163l11 -13l11 13l156 163c3.33301 4 7.5 6.83301 12.5 8.5s9.83301 2.5 14.5 2.5h65l-217 -246h-85z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="585" 
d="M20 1317h545v-65h-545v65z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="585" 
d="M292 1168c-44 0 -81.1689 6.5 -111.502 19.5s-55 30.833 -74 53.5s-32.667 49 -41 79s-12.5 61.667 -12.5 95h69c0 -24.667 2.83301 -48 8.5 -70s15.167 -41.333 28.5 -58s30.833 -29.834 52.5 -39.501s48.5 -14.5 80.5 -14.5s58.833 4.83301 80.5 14.5
s39.334 22.834 53.001 39.501s23.334 36 29.001 58s8.5 45.333 8.5 70h69c0 -33.333 -4.33301 -65 -13 -95s-22.5 -56.333 -41.5 -79s-43.667 -40.5 -74 -53.5s-67.5 -19.5 -111.5 -19.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="585" 
d="M381 1350c0 -12 -2.5 -23.167 -7.5 -33.5s-11.5 -19.5 -19.5 -27.5s-17.333 -14.333 -28 -19s-22 -7 -34 -7s-23.333 2.33301 -34 7s-20 11 -28 19s-14.333 17.167 -19 27.5s-7 21.5 -7 33.5s2.33301 23.5 7 34.5s11 20.5 19 28.5s17.333 14.333 28 19s22 7 34 7
s23.333 -2.33301 34 -7s20 -11 28 -19s14.5 -17.5 19.5 -28.5s7.5 -22.5 7.5 -34.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="585" 
d="M120 1302c0 24 4.5 46 13.5 66s21.333 37.167 37 51.5s34 25.666 55 33.999s43.167 12.5 66.5 12.5s45.5 -4.16699 66.5 -12.5s39.333 -19.666 55 -33.999s28.167 -31.5 37.5 -51.5s14 -42 14 -66s-4.66699 -45.833 -14 -65.5s-21.833 -36.834 -37.5 -51.501
s-34 -26 -55 -34s-43.167 -12 -66.5 -12s-45.5 4 -66.5 12s-39.333 19.333 -55 34s-28 31.834 -37 51.501s-13.5 41.5 -13.5 65.5zM178 1302c0 -33.333 10.667 -60.666 32 -81.999s49 -32 83 -32c33.333 0 60.666 10.667 81.999 32s32 48.666 32 81.999
s-10.667 60.666 -32 81.999s-48.666 32 -81.999 32c-34 0 -61.667 -10.667 -83 -32s-32 -48.666 -32 -81.999z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="585" 
d="M438 -243c6.66699 0 10.9971 -2.66797 12.9971 -8.00098l16 -34c-18.667 -16 -42 -28.833 -70 -38.5s-57.667 -14.5 -89 -14.5c-50.667 0 -91.667 12.5 -123 37.5s-47 59.5 -47 103.5c0 22.667 4.66699 44.167 14 64.5s21.5 39.5 36.5 57.5s31.833 34.333 50.5 49
s37.667 27.667 57 39l49 -13c-13.333 -7.33301 -27.666 -16.666 -42.999 -27.999s-29.666 -24.5 -42.999 -39.5s-24.5 -31.667 -33.5 -50s-13.5 -37.833 -13.5 -58.5c0 -32 10.667 -56.833 32 -74.5s48.666 -26.5 81.999 -26.5c18.667 0 34.334 1.83301 47.001 5.5
s23.334 7.5 32.001 11.5s15.5 7.83301 20.5 11.5s9.16699 5.5 12.5 5.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="585" 
d="M407 1280c28.667 0 50.3281 9.16797 64.9951 27.501s22.334 43.166 23.001 74.499h59c0 -24 -3.16699 -46.5 -9.5 -67.5s-15.666 -39.167 -27.999 -54.5s-27.5 -27.333 -45.5 -36s-38.333 -13 -61 -13c-22 0 -42.833 5.5 -62.5 16.5s-38.5 23 -56.5 36s-35.5 25 -52.5 36
s-34.167 16.5 -51.5 16.5c-28 0 -49.333 -9.5 -64 -28.5s-22.667 -43.5 -24 -73.5h-61c0 24 3.33301 46.5 10 67.5s16.334 39.167 29.001 54.5s28 27.333 46 36s38.333 13 61 13s43.834 -5.5 63.501 -16.5s38.334 -23 56.001 -36s35 -25 52 -36s34.167 -16.5 51.5 -16.5z
" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="585" 
d="M401 1431l-198 -253c-5.33301 -6.66699 -10.833 -11.167 -16.5 -13.5s-12.5 -3.5 -20.5 -3.5h-42l152 238c7.33301 11.333 15 19.5 23 24.5s20 7.5 36 7.5h66zM673 1431l-231 -253c-5.33301 -6.66699 -10.833 -11.167 -16.5 -13.5s-12.5 -3.5 -20.5 -3.5h-47l182 238
c8.66699 11.333 16.667 19.5 24 24.5s19 7.5 35 7.5h74z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="1174" 
d="M1139 995l0.000976562 -36.001c0 -9.33301 -3.16699 -17.666 -9.5 -24.999s-15.5 -11 -27.5 -11h-172v-923h-95v923h-468v-707c0 -73.333 -18.167 -129.333 -54.5 -168s-89.5 -58 -159.5 -58c-20.667 0 -39.667 1.5 -57 4.5s-34 9.16699 -50 18.5l5 39
c3.33301 9.33301 9.33301 14 18 14c6 0 13.667 -0.833008 23 -2.5s21.666 -2.5 36.999 -2.5c95.333 0 143 52 143 156v706h-202v34c0 8.66699 3.5 17.167 10.5 25.5s17.167 12.5 30.5 12.5h1028z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="1082" 
d="M147 614h787v-71h-787v71z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1591" 
d="M147 614h1297v-71h-1297v71z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="395" 
d="M171 1057c-14 25.333 -24.333 50.499 -31 75.499s-10 50.167 -10 75.5c0 52 12.667 102 38 150s58.333 91.667 99 131l27 -18c5.33301 -4 8 -8.66699 8 -14c0 -4.66699 -2 -9 -6 -13c-25.333 -34 -46.333 -67.667 -63 -101s-25 -68 -25 -104
c0 -39.333 11.667 -79.333 35 -120c3.33301 -5.33301 5 -10.666 5 -15.999c0 -9.33301 -5 -16 -15 -20z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="395" 
d="M240 1477c14.667 -25.333 25.333 -50.5 32 -75.5s10 -50.167 10 -75.5c0 -52 -12.667 -102 -38 -150s-58.333 -91.667 -99 -131l-28 18c-4.66699 4 -7 8.66699 -7 14s2 10 6 14c25.333 33.333 46.333 66.833 63 100.5s25 68.5 25 104.5
c0 38.667 -11.667 78.334 -35 119.001c-3.33301 5.33301 -5 10.666 -5 15.999c0 9.33301 5 16 15 20z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="395" 
d="M240 176c14.667 -25.333 25.333 -50.5 32 -75.5s10 -50.167 10 -75.5c0 -52 -12.667 -102 -38 -150s-58.333 -91.667 -99 -131l-28 18c-4.66699 4 -7 8.66699 -7 14s2 10 6 14c25.333 33.333 46.333 66.833 63 100.5s25 68.5 25 104.5
c0 38.667 -11.667 78.334 -35 119.001c-3.33301 5.33301 -5 10.666 -5 15.999c0 9.33301 5 16 15 20z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="665" 
d="M171 1057c-14 25.333 -24.333 50.499 -31 75.499s-10 50.167 -10 75.5c0 52 12.667 102 38 150s58.333 91.667 99 131l27 -18c5.33301 -4 8 -8.66699 8 -14c0 -4.66699 -2 -9 -6 -13c-25.333 -34 -46.333 -67.667 -63 -101s-25 -68 -25 -104
c0 -39.333 11.667 -79.333 35 -120c3.33301 -5.33301 5 -10.666 5 -15.999c0 -9.33301 -5 -16 -15 -20zM440 1057c-14 25.333 -24.333 50.499 -31 75.499s-10 50.167 -10 75.5c0 52 12.667 102 38 150s58.333 91.667 99 131l27 -18c5.33301 -4 8 -8.66699 8 -14
c0 -4.66699 -2 -9 -6 -13c-25.333 -34 -46.333 -67.667 -63 -101s-25 -68 -25 -104c0 -39.333 11.667 -79.333 35 -120c3.33301 -5.33301 5 -10.666 5 -15.999c0 -9.33301 -5 -16 -15 -20z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="665" 
d="M240 1477c14.667 -25.333 25.333 -50.5 32 -75.5s10 -50.167 10 -75.5c0 -52 -12.667 -102 -38 -150s-58.333 -91.667 -99 -131l-28 18c-4.66699 4 -7 8.66699 -7 14s2 10 6 14c25.333 33.333 46.333 66.833 63 100.5s25 68.5 25 104.5
c0 38.667 -11.667 78.334 -35 119.001c-3.33301 5.33301 -5 10.666 -5 15.999c0 9.33301 5 16 15 20zM508.999 1477c14.667 -25.333 25.333 -50.5 32 -75.5s10 -50.167 10 -75.5c0 -52 -12.667 -102 -38 -150s-58.333 -91.667 -99 -131l-28 18c-4.66699 4 -7 8.66699 -7 14
s2 10 6 14c25.333 33.333 46.333 66.833 63 100.5s25 68.5 25 104.5c0 38.667 -11.667 78.334 -35 119.001c-3.33301 5.33301 -5 10.666 -5 15.999c0 9.33301 5 16 15 20z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="665" 
d="M240 176c14.667 -25.333 25.333 -50.5 32 -75.5s10 -50.167 10 -75.5c0 -52 -12.667 -102 -38 -150s-58.333 -91.667 -99 -131l-28 18c-4.66699 4 -7 8.66699 -7 14s2 10 6 14c25.333 33.333 46.333 66.833 63 100.5s25 68.5 25 104.5
c0 38.667 -11.667 78.334 -35 119.001c-3.33301 5.33301 -5 10.666 -5 15.999c0 9.33301 5 16 15 20zM508.999 176c14.667 -25.333 25.333 -50.5 32 -75.5s10 -50.167 10 -75.5c0 -52 -12.667 -102 -38 -150s-58.333 -91.667 -99 -131l-28 18c-4.66699 4 -7 8.66699 -7 14
s2 10 6 14c25.333 33.333 46.333 66.833 63 100.5s25 68.5 25 104.5c0 38.667 -11.667 78.334 -35 119.001c-3.33301 5.33301 -5 10.666 -5 15.999c0 9.33301 5 16 15 20z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" 
d="M160 924c0 11.333 3.83301 21.666 11.5 30.999s19.167 14 34.5 14c51.333 -1.33301 106 -4.5 164 -9.5s114.667 -8.5 170 -10.5l-13 483c15.333 8.66699 32.333 13 51 13c18 0 34.667 -4.33301 50 -13l-13 -483c54.667 2 111 5.5 169 10.5s113 8.16699 165 9.5
c15.333 0 27 -4.66699 35 -14s12 -19.666 12 -30.999v-32h-381v-495l13 -746c-15.333 -8.66699 -32 -13 -50 -13c-18.667 0 -35.667 4.33301 -51 13l13 746v495h-380v32z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" 
d="M160 190h380v702h-380v32c0 11.333 3.83301 21.666 11.5 30.999s19.167 14 34.5 14c51.333 -1.33301 106 -4.5 164 -9.5s114.667 -8.5 170 -10.5l-13 483c15.333 8.66699 32.333 13 51 13c18 0 34.667 -4.33301 50 -13l-13 -483c54.667 2 111 5.5 169 10.5
s113 8.16699 165 9.5c15.333 0 27 -4.66699 35 -14s12 -19.666 12 -30.999v-32h-381v-702h381v-32c0 -11.333 -4 -21.5 -12 -30.5s-19.667 -13.5 -35 -13.5c-52 1.33301 -107 4.5 -165 9.5s-114.333 8.5 -169 10.5l13 -483c-15.333 -8.66699 -32 -13 -50 -13
c-18.667 0 -35.667 4.33301 -51 13l13 483c-55.333 -2 -112 -5.5 -170 -10.5s-112.667 -8.16699 -164 -9.5c-15.333 0 -26.833 4.5 -34.5 13.5s-11.5 19.167 -11.5 30.5v32z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" 
d="M286 596c0 40.667 7.66699 79 23 115s36.333 67.5 63 94.5s57.667 48.167 93 63.5s73 23 113 23c40.667 0 79 -7.66699 115 -23s67.333 -36.5 94 -63.5s47.667 -58.5 63 -94.5s23 -74.333 23 -115c0 -40 -7.66699 -77.5 -23 -112.5s-36.333 -65.833 -63 -92.5
s-58 -47.667 -94 -63s-74.333 -23 -115 -23c-40 0 -77.667 7.66699 -113 23s-66.333 36.333 -93 63s-47.667 57.5 -63 92.5s-23 72.5 -23 112.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1376" 
d="M105 77c0 12.667 2.33301 24.667 7 36s11.167 21.166 19.5 29.499s18 15 29 20s23.167 7.5 36.5 7.5c12.667 0 24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20s15 -18.166 20 -29.499s7.5 -23.333 7.5 -36c0 -13.333 -2.5 -25.5 -7.5 -36.5s-11.667 -20.667 -20 -29
s-18.166 -14.833 -29.499 -19.5s-23.333 -7 -36 -7c-26 0 -47.833 8.83301 -65.5 26.5s-26.5 39.5 -26.5 65.5zM1086 77c0 12.667 2.33301 24.667 7 36s11.167 21.166 19.5 29.499s18 15 29 20s23.167 7.5 36.5 7.5c12.667 0 24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20
s15 -18.166 20 -29.499s7.5 -23.333 7.5 -36c0 -13.333 -2.5 -25.5 -7.5 -36.5s-11.667 -20.667 -20 -29s-18.166 -14.833 -29.499 -19.5s-23.333 -7 -36 -7c-26 0 -47.833 8.83301 -65.5 26.5s-26.5 39.5 -26.5 65.5zM595 77c0 12.667 2.33301 24.667 7 36
s11.167 21.166 19.5 29.499s18 15 29 20s23.167 7.5 36.5 7.5c12.667 0 24.667 -2.5 36 -7.5s21.166 -11.667 29.499 -20s15 -18.166 20 -29.499s7.5 -23.333 7.5 -36c0 -13.333 -2.5 -25.5 -7.5 -36.5s-11.667 -20.667 -20 -29s-18.166 -14.833 -29.499 -19.5
s-23.333 -7 -36 -7c-26 0 -47.833 8.83301 -65.5 26.5s-26.5 39.5 -26.5 65.5z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="2258" 
d="M682 1075c0 -58 -8 -109.167 -24 -153.5s-37.667 -81.166 -65 -110.499s-59 -51.5 -95 -66.5s-74 -22.5 -114 -22.5c-41.333 0 -80.166 7.5 -116.499 22.5s-67.833 37.167 -94.5 66.5s-47.667 66.166 -63 110.499s-23 95.5 -23 153.5
c0 58.667 7.66699 110.334 23 155.001s36.333 81.834 63 111.501s58.167 52 94.5 67s75.166 22.5 116.499 22.5s80 -7.5 116 -22.5s67.5 -37.333 94.5 -67s48.333 -66.834 64 -111.501s23.5 -96.334 23.5 -155.001zM606 1075c0 51.333 -5.83301 95.333 -17.5 132
s-27.667 66.834 -48 90.501s-44 41 -71 52s-55.5 16.5 -85.5 16.5s-58.333 -5.5 -85 -16.5s-50 -28.333 -70 -52s-35.833 -53.834 -47.5 -90.501s-17.5 -80.667 -17.5 -132s5.83301 -95.166 17.5 -131.499s27.5 -66.166 47.5 -89.499s43.333 -40.5 70 -51.5
s55 -16.5 85 -16.5s58.5 5.5 85.5 16.5s50.667 28.167 71 51.5s36.333 53.166 48 89.499s17.5 80.166 17.5 131.499zM1219 1397c4 5.33301 8.83398 9.66699 14.501 13s13.167 5 22.5 5h67l-1016 -1397c-8.66699 -12 -20.334 -18 -35.001 -18h-68zM1446 336.001
c0 -58 -8 -109.167 -24 -153.5s-37.5 -81.166 -64.5 -110.499s-58.5 -51.5 -94.5 -66.5s-74 -22.5 -114 -22.5c-41.333 0 -80.166 7.5 -116.499 22.5s-67.833 37.167 -94.5 66.5s-47.667 66.166 -63 110.499s-23 95.5 -23 153.5c0 58.667 7.66699 110.334 23 155.001
s36.333 81.834 63 111.501s58.167 52 94.5 67s75.166 22.5 116.499 22.5s80 -7.5 116 -22.5s67.5 -37.333 94.5 -67s48.167 -66.834 63.5 -111.501s23 -96.334 23 -155.001zM1370 336.001c0 51.333 -5.83301 95.5 -17.5 132.5s-27.5 67.333 -47.5 91s-43.5 41 -70.5 52
s-55.5 16.5 -85.5 16.5s-58.5 -5.5 -85.5 -16.5s-50.5 -28.333 -70.5 -52s-35.833 -54 -47.5 -91s-17.5 -81.167 -17.5 -132.5s5.83301 -95.166 17.5 -131.499s27.5 -66.166 47.5 -89.499s43.5 -40.333 70.5 -51s55.5 -16 85.5 -16s58.5 5.33301 85.5 16
s50.5 27.667 70.5 51s35.833 53.166 47.5 89.499s17.5 80.166 17.5 131.499zM2170 336.001c0 -58 -8 -109.167 -24 -153.5s-37.5 -81.166 -64.5 -110.499s-58.5 -51.5 -94.5 -66.5s-74 -22.5 -114 -22.5c-41.333 0 -80.166 7.5 -116.499 22.5s-67.833 37.167 -94.5 66.5
s-47.667 66.166 -63 110.499s-23 95.5 -23 153.5c0 58.667 7.66699 110.334 23 155.001s36.333 81.834 63 111.501s58.167 52 94.5 67s75.166 22.5 116.499 22.5s80 -7.5 116 -22.5s67.5 -37.333 94.5 -67s48.167 -66.834 63.5 -111.501s23 -96.334 23 -155.001z
M2094 336.001c0 51.333 -5.83301 95.5 -17.5 132.5s-27.5 67.333 -47.5 91s-43.5 41 -70.5 52s-55.5 16.5 -85.5 16.5s-58.5 -5.5 -85.5 -16.5s-50.5 -28.333 -70.5 -52s-35.833 -54 -47.5 -91s-17.5 -81.167 -17.5 -132.5s5.83301 -95.166 17.5 -131.499
s27.5 -66.166 47.5 -89.499s43.5 -40.333 70.5 -51s55.5 -16 85.5 -16s58.5 5.33301 85.5 16s50.5 27.667 70.5 51s35.833 53.166 47.5 89.499s17.5 80.166 17.5 131.499z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="619" 
d="M155 513v12.999l241 378l30 -15c10.667 -6.66699 16 -15 16 -25c0 -8 -2.33301 -15.667 -7 -23l-183 -293c-8.66699 -13.333 -16 -23 -22 -29c6.66699 -5.33301 14 -14.666 22 -27.999l183 -294c4.66699 -7.33301 7 -15 7 -23c0 -10 -5.33301 -18.333 -16 -25l-30 -15z
" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="619" 
d="M223 134l-30 15.0039c-11.333 6 -17 14.667 -17 26c0 7.33301 2.33301 14.666 7 21.999l183 294c4 7.33301 7.83301 13.166 11.5 17.499s7.16699 7.83301 10.5 10.5c-3.33301 3.33301 -6.83301 7.16602 -10.5 11.499s-7.5 10.166 -11.5 17.499l-183 293
c-4.66699 7.33301 -7 15 -7 23c0 10.667 5.66699 19 17 25l30 15l241 -378v-13z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="497" 
d="M-107 31c-7.33301 -12 -15.499 -20.167 -24.499 -24.5s-19.167 -6.5 -30.5 -6.5h-40l800 1380c6.66699 11.333 14.167 20 22.5 26s19.166 9 32.499 9h42z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" 
d="M31 863l163.997 0.00488281c10.667 88 31 167 61 237s68 129.333 114 178s99.333 86 160 112s127.667 39 201 39c42.667 0 82.167 -3.83301 118.5 -11.5s70 -18.834 101 -33.501s60 -32.834 87 -54.501s52.833 -46.834 77.5 -75.501l-31 -35
c-2.66699 -3.33301 -5.66699 -6.16602 -9 -8.49902s-7.66602 -3.5 -12.999 -3.5s-11.666 3.5 -18.999 10.5l-28 25.5c-11.333 10 -25.166 21.167 -41.499 33.5s-35.666 23.5 -57.999 33.5s-48.333 18.5 -78 25.5s-63.834 10.5 -102.501 10.5c-60 0 -115 -10.5 -165 -31.5
s-93.833 -51.833 -131.5 -92.5s-68.834 -91 -93.501 -151s-42 -129.333 -52 -208h602v-30c0 -7.33301 -2.5 -13.833 -7.5 -19.5s-13.167 -8.5 -24.5 -8.5h-576c-2 -31.333 -3 -63.666 -3 -96.999c0 -14 0.166992 -27.5 0.5 -40.5s0.833008 -26.167 1.5 -39.5h517v-30
c0 -8 -2.66699 -14.833 -8 -20.5s-13 -8.5 -23 -8.5h-481c8.66699 -82.667 25 -155.334 49 -218.001s54.667 -115 92 -157s81 -73.667 131 -95s105 -32 165 -32c41.333 0 77.833 4 109.5 12s59.5 18.167 83.5 30.5s44.667 25.666 62 39.999s32 27.666 44 39.999l30.5 30.5
c8.33301 8 15.5 12 21.5 12c6.66699 0 13 -3 19 -9l37 -35c-24 -30.667 -50 -58.5 -78 -83.5s-59 -46.333 -93 -64s-71 -31.334 -111 -41.001s-83.333 -14.5 -130 -14.5c-75.333 0 -143.666 13 -204.999 39s-114.5 64 -159.5 114s-81.5 111.167 -109.5 183.5
s-46.667 154.833 -56 247.5h-162v59h157c-0.666992 13.333 -1.16699 26.5 -1.5 39.5s-0.5 26.5 -0.5 40.5c0 32.667 1 65 3 97h-158v58z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1427" 
d="M979 1010c3.33301 10.667 7.66504 20.333 12.998 29l213 364c3.33301 5.33301 6.5 8.66602 9.5 9.99902s7.83301 2 14.5 2h57v-575h-62v443l5 44l-219 -380c-5.33301 -10.667 -13.666 -16 -24.999 -16h-11c-11.333 0 -19.666 5.33301 -24.999 16l-223 378l5 -42v-443h-63
v575h57c6.66699 0 11.5 -0.666992 14.5 -2s6.83301 -4.66602 11.5 -9.99902l217 -364zM542.999 1415v-59h-199v-516h-71v516h-201v59h471z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="1510" 
d="M867 0l-0.00488281 363c64 13.333 122.333 33.166 175 59.499s98 59.333 136 99s67.333 86.334 88 140.001s31 114.834 31 183.501c0 84 -14.333 157.333 -43 220s-67.667 115 -117 157s-106.833 73.5 -172.5 94.5s-135.5 31.5 -209.5 31.5s-143.833 -10.5 -209.5 -31.5
s-123.167 -52.5 -172.5 -94.5s-88.333 -94.333 -117 -157s-43 -136 -43 -220c0 -68.667 10.333 -129.834 31 -183.501s49.834 -100.334 87.501 -140.001s82.834 -72.667 135.501 -99s111 -46.166 175 -59.499v-363h-501c-11.333 -0 -20.333 3.33301 -27 10
s-10 15.334 -10 26.001v52h457v222c-65.333 14.667 -125.666 37.334 -180.999 68.001s-103.166 68.667 -143.499 114s-71.833 98 -94.5 158s-34 126.333 -34 199c0 91.333 16.833 173 50.5 245s79.834 133 138.501 183s127.167 88.167 205.5 114.5s162.5 39.5 252.5 39.5
s174.167 -13.167 252.5 -39.5s146.666 -64.5 204.999 -114.5s104.333 -111 138 -183s50.5 -153.667 50.5 -245c0 -72.667 -11.333 -139 -34 -199s-54 -112.667 -94 -158s-87.667 -83.333 -143 -114s-115.666 -53.334 -180.999 -68.001v-222h456v-52
c0 -10.667 -3.33301 -19.334 -10 -26.001s-15.667 -10 -27 -10h-501z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" 
d="M401 1341c24 16 47.166 29.832 69.499 41.499s44.666 21.167 66.999 28.5s45.333 12.833 69 16.5s48.834 5.5 75.501 5.5c58 0 109.833 -12 155.5 -36s84.334 -58.667 116.001 -104s55.834 -100.833 72.501 -166.5s25 -140.5 25 -224.5
c0 -130 -11.833 -250.667 -35.5 -362s-59.167 -208 -106.5 -290s-106.333 -146.167 -177 -192.5s-152.667 -69.5 -246 -69.5c-54 0 -103.167 9 -147.5 27s-82.5 44 -114.5 78s-56.833 75.167 -74.5 123.5s-26.5 102.833 -26.5 163.5c0 76 12.167 147.5 36.5 214.5
s58.5 125.5 102.5 175.5s96.833 89.333 158.5 118s130.167 43 205.5 43c35.333 0 69.5 -4.5 102.5 -13.5s63.833 -23 92.5 -42s54.334 -43 77.001 -72s41.334 -63.167 56.001 -102.5c2 19.333 3.66699 38.833 5 58.5s2.16602 38.334 2.49902 56.001s0.666016 34 0.999023 49
s0.5 27.833 0.5 38.5c0 145.333 -25 257 -75 335s-122.333 117 -217 117c-32.667 0 -61.5 -3.5 -86.5 -10.5s-46.833 -14.5 -65.5 -22.5s-34.167 -15.5 -46.5 -22.5s-21.833 -10.5 -28.5 -10.5c-4.66699 0 -8.83398 1.16699 -12.501 3.5s-7.16699 6.83301 -10.5 13.5z
M497.999 63.999c54.667 0 105.502 11.8301 152.502 35.4971s89 57.667 126 102s68.667 98.333 95 162s46.166 135.834 59.499 216.501c-6.66699 34 -17.167 67.5 -31.5 100.5s-33.333 62.5 -57 88.5s-52.334 46.833 -86.001 62.5s-73.5 23.5 -119.5 23.5
c-65.333 0 -123.666 -12.167 -174.999 -36.5s-94.833 -57.666 -130.5 -99.999s-63 -92.333 -82 -150s-28.5 -119.834 -28.5 -186.501c0 -50 6.66699 -94.667 20 -134s32.166 -72.666 56.499 -99.999s53.5 -48.166 87.5 -62.499s71.667 -21.5 113 -21.5z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="1304" 
d="M606 1415h92l589 -1415h-1270zM156 83l992 0.000976562l-470 1146c-9.33301 22 -18 47.333 -26 76c-4 -14.667 -8.16699 -28.334 -12.5 -41.001s-8.83301 -24.667 -13.5 -36z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="1364" 
d="M1286 1415v-82h-210v-1685h-98v1685h-593v-1685h-98v1685h-210v82h1209z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="1364" 
d="M87 1415l1189 -0.000976562v-82h-1040l645 -788v-26l-645 -789h1040v-82h-1189v36c0 12 3.66699 22.667 11 32l673 817l-672 814c-8 8.66699 -12 19.334 -12 32.001v36z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M164 707h829v-73h-829v73z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="1123" 
d="M309 690l-230 0.000976562c-10.667 0 -19.834 3.16699 -27.501 9.5s-11.5 16.833 -11.5 31.5v30h314c10 0 17.833 -2.5 23.5 -7.5s9.83398 -10.833 12.501 -17.5l207 -539c9.33301 -30.667 16 -59.667 20 -87c4 24.667 9.33301 50 16 76l488 1509
c2.66699 7.33301 7 13.166 13 17.499s13 6.5 21 6.5h58l-558 -1719h-79z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="1364" 
d="M1006 282c-36.667 0 -70.334 6.66602 -101.001 19.999s-59 31 -85 53s-50.167 47.5 -72.5 76.5s-43.833 59.167 -64.5 90.5c-20.667 -31.333 -42.167 -61.333 -64.5 -90s-46.5 -54.167 -72.5 -76.5s-54.167 -40.166 -84.5 -53.499s-63.833 -20 -100.5 -20
c-38 0 -74.333 7.16699 -109 21.5s-65.167 34.833 -91.5 61.5s-47.333 59.167 -63 97.5s-23.5 81.166 -23.5 128.499s7.83301 90 23.5 128s36.667 70.333 63 97s56.833 47.167 91.5 61.5s71 21.5 109 21.5c36.667 0 70.167 -6.66699 100.5 -20s58.5 -31.166 84.5 -53.499
s50.167 -48 72.5 -77s43.833 -58.833 64.5 -89.5c20.667 30.667 42.167 60.5 64.5 89.5s46.5 54.667 72.5 77s54.333 40.166 85 53.499s64.334 20 101.001 20c38 0 74.333 -7.16699 109 -21.5s65.167 -34.833 91.5 -61.5s47.333 -59 63 -97s23.5 -80.667 23.5 -128
s-7.83301 -90.166 -23.5 -128.499s-36.667 -70.833 -63 -97.5s-56.833 -47.167 -91.5 -61.5s-71 -21.5 -109 -21.5zM365 359.999c29.333 0 56.5 6.33203 81.5 18.999s48.5 29.667 70.5 51s42.833 45.833 62.5 73.5l59.5 86.5l-59.5 87c-19.667 28 -40.5 52.667 -62.5 74
s-45.5 38.333 -70.5 51s-52.167 19 -81.5 19s-56.833 -5.16699 -82.5 -15.5s-48.167 -25.166 -67.5 -44.499s-34.5 -43.333 -45.5 -72s-16.5 -61.334 -16.5 -98.001c0 -37.333 5.5 -70.333 16.5 -99s26.167 -52.667 45.5 -72s41.833 -34.166 67.5 -44.499
s53.167 -15.5 82.5 -15.5zM1003 359.998c28.667 0 55.834 5.16797 81.501 15.501s48 25.166 67 44.499s34 43.333 45 72s16.5 61.667 16.5 99c0 36.667 -5.5 69.334 -16.5 98.001s-26 52.667 -45 72s-41.333 34.166 -67 44.499s-52.834 15.5 -81.501 15.5
c-30 0 -57.5 -6.33301 -82.5 -19s-48.5 -29.667 -70.5 -51s-42.833 -46 -62.5 -74l-59.5 -87l59.5 -86.5c19.667 -27.667 40.5 -52.167 62.5 -73.5s45.5 -38.333 70.5 -51s52.5 -19 82.5 -19z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="770" 
d="M392 1086c6.66699 58 19.6641 108.67 38.9971 152.003s43.333 79.166 72 107.499s61.5 49.666 98.5 63.999s76.833 21.5 119.5 21.5c26 0 48.5 -1.83301 67.5 -5.5s37.167 -9.83398 54.5 -18.501l-5 -43c-2 -6.66699 -5.16699 -10.834 -9.5 -12.501
s-11.166 -2.5 -20.499 -2.5c-7.33301 0 -16 0.166992 -26 0.5s-22.333 0.5 -37 0.5c-34.667 0 -66.667 -5.33301 -96 -16s-55.166 -27.334 -77.499 -50.001s-41 -51.167 -56 -85.5s-25.5 -75.5 -31.5 -123.5l-132 -1062c-8 -66 -22.5 -122.5 -43.5 -169.5
s-47 -85.5 -78 -115.5s-66.5 -52 -106.5 -66s-83 -21 -129 -21c-22 0 -43.833 1.5 -65.5 4.5s-40.5 9.16699 -56.5 18.5l7 39c2 6 4.66699 10 8 12s8.33301 3 15 3c7.33301 0 16.666 -0.666992 27.999 -2s26.333 -2 45 -2c40 0 76 5.33301 108 16s59.833 27.834 83.5 51.501
s43.334 54.334 59.001 92.001s26.834 83.5 33.501 137.5z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M781 810c34.667 0 66.665 7.50098 95.998 22.501s51 31.833 65 50.5l19 -58c-19.333 -28.667 -45.666 -50.834 -78.999 -66.501s-68.666 -23.5 -105.999 -23.5c-34.667 0 -68.834 6 -102.501 18s-67 25.333 -100 40s-65.5 28 -97.5 40s-63.333 18 -94 18
c-36.667 0 -69.5 -7.5 -98.5 -22.5s-50.5 -32.167 -64.5 -51.5l-21 56c20 30 46.5 53 79.5 69s69.5 24 109.5 24c34.667 0 69 -6 103 -18s67.5 -25.333 100.5 -40s65.333 -28 97 -40s62.834 -18 93.501 -18zM780.998 494.001c34.667 0 66.665 7.5 95.998 22.5
s51 31.833 65 50.5l19 -58c-19.333 -28.667 -45.666 -50.667 -78.999 -66s-68.666 -23 -105.999 -23c-34.667 0 -68.834 6 -102.501 18s-67 25.333 -100 40s-65.5 28 -97.5 40s-63.333 18 -94 18c-36.667 0 -69.5 -7.5 -98.5 -22.5s-50.5 -32.167 -64.5 -51.5l-21 56
c20 30 46.5 52.833 79.5 68.5s69.5 23.5 109.5 23.5c34.667 0 69 -6 103 -18s67.5 -25.333 100.5 -40s65.333 -28 97 -40s62.834 -18 93.501 -18z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M166 869h476l117 267h71l-116 -267h279v-74h-312l-104 -239h416v-74h-448l-118 -271h-72l118 271h-307v74h340l104 239h-444v74z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M160 729l749.999 373.999v-62c0 -7.33301 -2.16699 -14 -6.5 -20s-12.166 -11.667 -23.499 -17l-550 -271c-11.333 -5.33301 -23.5 -9.83301 -36.5 -13.5s-26.5 -7.16699 -40.5 -10.5c14 -3.33301 27.5 -7 40.5 -11s25.167 -8.66699 36.5 -14l549 -272
c11.333 -6 19.333 -11.667 24 -17s7 -11.666 7 -18.999v-63l-750 376v40zM159.999 152.999h750v-73h-750v73z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M1000 729v-40l-750 -376v63c0 7.33301 2.16699 13.833 6.5 19.5s12.166 11.167 23.499 16.5l550 272c11.333 5.33301 23.333 10 36 14s26 7.66699 40 11c-14 3.33301 -27.333 6.83301 -40 10.5s-24.667 8.16699 -36 13.5l-550 271c-11.333 5.33301 -19.166 11 -23.499 17
s-6.5 12.667 -6.5 20v62zM1000 80h-750v73h750v-73z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M145 678l402 832h66l402 -832l-402 -832h-66zM227 678l332 -684.999c5.33301 -10 9.5 -19.833 12.5 -29.5s5.5 -19.167 7.5 -28.5c2.66699 9.33301 5.5 18.666 8.5 27.999s7.16699 19.333 12.5 30l334 685l-334 685c-5.33301 10.667 -9.5 20.667 -12.5 30
s-5.83301 18.666 -8.5 27.999c-2.66699 -9.33301 -5.66699 -18.666 -9 -27.999l-11 -30z" />
    <glyph glyph-name="uni2669" unicode="&#x2669;" horiz-adv-x="0" 
d="M-2 1455h4v-1807h-4v1807z" />
    <glyph glyph-name="undercommaaccent" horiz-adv-x="585" 
d="M314 -91c10 0 16.499 -2.5 19.499 -7.5s4.5 -11.5 4.5 -19.5c0 -7.33301 -1 -16.666 -3 -27.999s-5.33301 -26 -10 -44s-10.667 -39.833 -18 -65.5l-27 -92.5c-3.33301 -8.66699 -7.66602 -14.834 -12.999 -18.501s-13.333 -5.5 -24 -5.5h-32l48 281h55z" />
    <glyph glyph-name="grave.case" horiz-adv-x="585" 
d="M116 1770c16 0 27.833 -1.49902 35.5 -4.49902s16.167 -8.83301 25.5 -17.5l228 -206h-71c-8 0 -14.667 0.666992 -20 2s-11.333 4.66602 -18 9.99902l-293 216h113z" />
    <glyph glyph-name="dieresis.case" horiz-adv-x="585" 
d="M165 1665c0 -10.667 -2.33301 -20.834 -7 -30.501s-10.667 -18 -18 -25s-16 -12.667 -26 -17s-20.333 -6.5 -31 -6.5s-20.834 2.16699 -30.501 6.5s-18.167 10 -25.5 17s-13.166 15.333 -17.499 25s-6.5 19.834 -6.5 30.501s2.16699 21 6.5 31s10.166 18.833 17.499 26.5
s15.833 13.667 25.5 18s19.834 6.5 30.501 6.5s21 -2.16699 31 -6.5s18.667 -10.333 26 -18s13.333 -16.5 18 -26.5s7 -20.333 7 -31zM581 1665c0 -10.667 -2.16797 -20.834 -6.50098 -30.501s-10.333 -18 -18 -25s-16.334 -12.667 -26.001 -17s-19.834 -6.5 -30.501 -6.5
c-11.333 0 -21.833 2.16699 -31.5 6.5s-18.167 10 -25.5 17s-13.166 15.333 -17.499 25s-6.5 19.834 -6.5 30.501c0 22 8 41.167 24 57.5s35 24.5 57 24.5c10.667 0 20.834 -2.16699 30.501 -6.5s18.334 -10.333 26.001 -18s13.667 -16.5 18 -26.5s6.5 -20.333 6.5 -31z" />
    <glyph glyph-name="macron.case" horiz-adv-x="585" 
d="M85 1649h415v-60h-415v60z" />
    <glyph glyph-name="acute.case" horiz-adv-x="585" 
d="M586 1770l-291.999 -215.999c-6.66699 -4.66699 -12.834 -7.83398 -18.501 -9.50098s-12.5 -2.5 -20.5 -2.5h-70l227 206c9.33301 8.66699 18 14.5 26 17.5s20 4.5 36 4.5h112z" />
    <glyph glyph-name="circumflex.case" horiz-adv-x="585" 
d="M572 1542h-73.0029c-5.33301 0 -11.166 0.833008 -17.499 2.5s-11.833 4.16699 -16.5 7.5l-156 123c-5.33301 4 -9 7 -11 9l-5 5c-1.33301 -1.33301 -3.16602 -3 -5.49902 -5s-6.16602 -5 -11.499 -9l-157 -123c-4.66699 -3.33301 -10 -5.83301 -16 -7.5
s-11.667 -2.5 -17 -2.5h-74l235 199h90z" />
    <glyph glyph-name="caron.case" horiz-adv-x="585" 
d="M12 1741l74.001 -0.000976562c5.33301 0 11 -0.833008 17 -2.5s11.333 -4.16699 16 -7.5l157 -123c5.33301 -4 9 -7 11 -9l5 -5c1.33301 1.33301 3 2.83301 5 4.5s6 4.83398 12 9.50098l156 123c4.66699 3.33301 10.167 5.83301 16.5 7.5s12.166 2.5 17.499 2.5h73
l-235 -199h-90z" />
    <glyph glyph-name="breve.case" horiz-adv-x="585" 
d="M292 1527c-82.667 0 -143.667 18.667 -183 56s-59 90 -59 158h64c0 -47.333 13.667 -84.5 41 -111.5s73 -40.5 137 -40.5s109.667 13.5 137 40.5s41 64.167 41 111.5h64c0 -65.333 -19.667 -117.333 -59 -156s-100.333 -58 -183 -58z" />
    <glyph glyph-name="dotaccent.case" horiz-adv-x="585" 
d="M379 1677c0 -11.333 -2.5 -22.166 -7.5 -32.499s-11.5 -19.333 -19.5 -27s-17.333 -13.834 -28 -18.501s-21.667 -7 -33 -7s-22.166 2.33301 -32.499 7s-19.333 10.834 -27 18.501s-13.834 16.667 -18.501 27s-7 21.166 -7 32.499c0 12 2.33301 23.167 7 33.5
s10.834 19.5 18.501 27.5s16.667 14.333 27 19s21.166 7 32.499 7s22.333 -2.33301 33 -7s20 -11 28 -19s14.5 -17.167 19.5 -27.5s7.5 -21.5 7.5 -33.5z" />
    <glyph glyph-name="ring.case" horiz-adv-x="585" 
d="M125 1630c0 22.667 4.5 43.834 13.5 63.501s21 36.5 36 50.5s32.667 25 53 33s41.833 12 64.5 12s44.334 -4 65.001 -12s38.5 -19 53.5 -33s27 -30.833 36 -50.5s13.5 -40.834 13.5 -63.501c0 -23.333 -4.5 -44.666 -13.5 -63.999s-21 -36 -36 -50
s-32.833 -24.833 -53.5 -32.5s-42.334 -11.5 -65.001 -11.5s-44.167 3.83301 -64.5 11.5s-38 18.5 -53 32.5s-27 30.667 -36 50s-13.5 40.666 -13.5 63.999zM178 1630c0 -33.333 10.667 -60.666 32 -81.999s49 -32 83 -32c33.333 0 60.666 10.667 81.999 32
s32 48.666 32 81.999s-10.667 60.666 -32 81.999s-48.666 32 -81.999 32c-34 0 -61.667 -10.667 -83 -32s-32 -48.666 -32 -81.999z" />
    <glyph glyph-name="tilde.case" horiz-adv-x="585" 
d="M412 1616c28 0 49.332 9.16699 63.999 27.5s22.334 41.833 23.001 70.5h54c0 -23.333 -3 -44.833 -9 -64.5s-14.667 -37 -26 -52s-25.833 -26.667 -43.5 -35s-37.834 -12.5 -60.501 -12.5c-22 0 -43 5.33301 -63 16s-39.333 22.167 -58 34.5l-54.5 34.5
c-17.667 10.667 -35.5 16 -53.5 16c-28 0 -49.167 -9.33301 -63.5 -28s-22.166 -42 -23.499 -70h-56c0 22.667 3.16699 44 9.5 64s15.5 37.5 27.5 52.5s26.667 26.667 44 35s37.333 12.5 60 12.5s44 -5.33301 64 -16s39.333 -22.167 58 -34.5l54 -34.5
c17.333 -10.667 35 -16 53 -16z" />
    <glyph glyph-name="hungarumlaut.case" horiz-adv-x="585" 
d="M388 1765l-198 -206c-5.33301 -6 -10.833 -10.333 -16.5 -13s-12.5 -4 -20.5 -4h-47l153 191c8.66699 10.667 17.5 18.667 26.5 24s21.5 8 37.5 8h65zM692 1765l-230 -206c-7.33301 -5.33301 -13.833 -9.5 -19.5 -12.5s-12.5 -4.5 -20.5 -4.5h-52l181 191
c10 10 19.5 17.833 28.5 23.5s21.5 8.5 37.5 8.5h75z" />
    <glyph glyph-name="caron.salt" horiz-adv-x="585" 
d="M341 1457c10 0 16.499 -2.49902 19.499 -7.49902s4.5 -11.5 4.5 -19.5c0 -7.33301 -1.16699 -16.833 -3.5 -28.5s-6 -27.334 -11 -47.001s-11.833 -43.667 -20.5 -72l-31 -102.5c-3.33301 -9.33301 -7.66602 -15.833 -12.999 -19.5s-13.333 -5.5 -24 -5.5h-32l51 302h60z
" />
    <hkern u1="&#x22;" u2="&#x2206;" k="170" />
    <hkern u1="&#x22;" u2="&#x203a;" k="190" />
    <hkern u1="&#x22;" u2="&#x2039;" k="190" />
    <hkern u1="&#x22;" u2="&#x2022;" k="190" />
    <hkern u1="&#x22;" u2="&#x201e;" k="245" />
    <hkern u1="&#x22;" u2="&#x201a;" k="245" />
    <hkern u1="&#x22;" u2="&#x2014;" k="190" />
    <hkern u1="&#x22;" u2="&#x2013;" k="190" />
    <hkern u1="&#x22;" u2="&#x178;" k="-25" />
    <hkern u1="&#x22;" u2="&#x153;" k="85" />
    <hkern u1="&#x22;" u2="&#x152;" k="48" />
    <hkern u1="&#x22;" u2="&#x119;" k="85" />
    <hkern u1="&#x22;" u2="&#x107;" k="85" />
    <hkern u1="&#x22;" u2="&#x106;" k="48" />
    <hkern u1="&#x22;" u2="&#x105;" k="62" />
    <hkern u1="&#x22;" u2="&#x104;" k="170" />
    <hkern u1="&#x22;" u2="&#xf8;" k="85" />
    <hkern u1="&#x22;" u2="&#xf6;" k="85" />
    <hkern u1="&#x22;" u2="&#xf5;" k="85" />
    <hkern u1="&#x22;" u2="&#xf4;" k="85" />
    <hkern u1="&#x22;" u2="&#xf3;" k="85" />
    <hkern u1="&#x22;" u2="&#xf2;" k="85" />
    <hkern u1="&#x22;" u2="&#xf0;" k="85" />
    <hkern u1="&#x22;" u2="&#xeb;" k="85" />
    <hkern u1="&#x22;" u2="&#xea;" k="85" />
    <hkern u1="&#x22;" u2="&#xe9;" k="85" />
    <hkern u1="&#x22;" u2="&#xe8;" k="85" />
    <hkern u1="&#x22;" u2="&#xe7;" k="85" />
    <hkern u1="&#x22;" u2="&#xe6;" k="62" />
    <hkern u1="&#x22;" u2="&#xe5;" k="62" />
    <hkern u1="&#x22;" u2="&#xe4;" k="62" />
    <hkern u1="&#x22;" u2="&#xe3;" k="62" />
    <hkern u1="&#x22;" u2="&#xe2;" k="62" />
    <hkern u1="&#x22;" u2="&#xe1;" k="62" />
    <hkern u1="&#x22;" u2="&#xe0;" k="62" />
    <hkern u1="&#x22;" u2="&#xdd;" k="-25" />
    <hkern u1="&#x22;" u2="&#xd8;" k="48" />
    <hkern u1="&#x22;" u2="&#xd6;" k="48" />
    <hkern u1="&#x22;" u2="&#xd5;" k="48" />
    <hkern u1="&#x22;" u2="&#xd4;" k="48" />
    <hkern u1="&#x22;" u2="&#xd3;" k="48" />
    <hkern u1="&#x22;" u2="&#xd2;" k="48" />
    <hkern u1="&#x22;" u2="&#xc7;" k="48" />
    <hkern u1="&#x22;" u2="&#xc6;" k="170" />
    <hkern u1="&#x22;" u2="&#xc5;" k="170" />
    <hkern u1="&#x22;" u2="&#xc4;" k="170" />
    <hkern u1="&#x22;" u2="&#xc3;" k="170" />
    <hkern u1="&#x22;" u2="&#xc2;" k="170" />
    <hkern u1="&#x22;" u2="&#xc1;" k="170" />
    <hkern u1="&#x22;" u2="&#xc0;" k="170" />
    <hkern u1="&#x22;" u2="&#xbb;" k="190" />
    <hkern u1="&#x22;" u2="&#xb7;" k="190" />
    <hkern u1="&#x22;" u2="&#xad;" k="190" />
    <hkern u1="&#x22;" u2="&#xab;" k="190" />
    <hkern u1="&#x22;" u2="q" k="85" />
    <hkern u1="&#x22;" u2="o" k="85" />
    <hkern u1="&#x22;" u2="e" k="85" />
    <hkern u1="&#x22;" u2="d" k="85" />
    <hkern u1="&#x22;" u2="c" k="85" />
    <hkern u1="&#x22;" u2="a" k="62" />
    <hkern u1="&#x22;" u2="\" k="-55" />
    <hkern u1="&#x22;" u2="Y" k="-25" />
    <hkern u1="&#x22;" u2="W" k="-55" />
    <hkern u1="&#x22;" u2="V" k="-55" />
    <hkern u1="&#x22;" u2="Q" k="48" />
    <hkern u1="&#x22;" u2="O" k="48" />
    <hkern u1="&#x22;" u2="G" k="48" />
    <hkern u1="&#x22;" u2="C" k="48" />
    <hkern u1="&#x22;" u2="A" k="170" />
    <hkern u1="&#x22;" u2="&#x40;" k="48" />
    <hkern u1="&#x22;" u2="&#x2f;" k="170" />
    <hkern u1="&#x22;" u2="&#x2e;" k="245" />
    <hkern u1="&#x22;" u2="&#x2d;" k="190" />
    <hkern u1="&#x22;" u2="&#x2c;" k="245" />
    <hkern u1="&#x22;" u2="&#x26;" k="170" />
    <hkern u1="&#x27;" u2="&#x2206;" k="170" />
    <hkern u1="&#x27;" u2="&#x203a;" k="190" />
    <hkern u1="&#x27;" u2="&#x2039;" k="190" />
    <hkern u1="&#x27;" u2="&#x2022;" k="190" />
    <hkern u1="&#x27;" u2="&#x201e;" k="245" />
    <hkern u1="&#x27;" u2="&#x201a;" k="245" />
    <hkern u1="&#x27;" u2="&#x2014;" k="190" />
    <hkern u1="&#x27;" u2="&#x2013;" k="190" />
    <hkern u1="&#x27;" u2="&#x178;" k="-25" />
    <hkern u1="&#x27;" u2="&#x153;" k="85" />
    <hkern u1="&#x27;" u2="&#x152;" k="48" />
    <hkern u1="&#x27;" u2="&#x119;" k="85" />
    <hkern u1="&#x27;" u2="&#x107;" k="85" />
    <hkern u1="&#x27;" u2="&#x106;" k="48" />
    <hkern u1="&#x27;" u2="&#x105;" k="62" />
    <hkern u1="&#x27;" u2="&#x104;" k="170" />
    <hkern u1="&#x27;" u2="&#xf8;" k="85" />
    <hkern u1="&#x27;" u2="&#xf6;" k="85" />
    <hkern u1="&#x27;" u2="&#xf5;" k="85" />
    <hkern u1="&#x27;" u2="&#xf4;" k="85" />
    <hkern u1="&#x27;" u2="&#xf3;" k="85" />
    <hkern u1="&#x27;" u2="&#xf2;" k="85" />
    <hkern u1="&#x27;" u2="&#xf0;" k="85" />
    <hkern u1="&#x27;" u2="&#xeb;" k="85" />
    <hkern u1="&#x27;" u2="&#xea;" k="85" />
    <hkern u1="&#x27;" u2="&#xe9;" k="85" />
    <hkern u1="&#x27;" u2="&#xe8;" k="85" />
    <hkern u1="&#x27;" u2="&#xe7;" k="85" />
    <hkern u1="&#x27;" u2="&#xe6;" k="62" />
    <hkern u1="&#x27;" u2="&#xe5;" k="62" />
    <hkern u1="&#x27;" u2="&#xe4;" k="62" />
    <hkern u1="&#x27;" u2="&#xe3;" k="62" />
    <hkern u1="&#x27;" u2="&#xe2;" k="62" />
    <hkern u1="&#x27;" u2="&#xe1;" k="62" />
    <hkern u1="&#x27;" u2="&#xe0;" k="62" />
    <hkern u1="&#x27;" u2="&#xdd;" k="-25" />
    <hkern u1="&#x27;" u2="&#xd8;" k="48" />
    <hkern u1="&#x27;" u2="&#xd6;" k="48" />
    <hkern u1="&#x27;" u2="&#xd5;" k="48" />
    <hkern u1="&#x27;" u2="&#xd4;" k="48" />
    <hkern u1="&#x27;" u2="&#xd3;" k="48" />
    <hkern u1="&#x27;" u2="&#xd2;" k="48" />
    <hkern u1="&#x27;" u2="&#xc7;" k="48" />
    <hkern u1="&#x27;" u2="&#xc6;" k="170" />
    <hkern u1="&#x27;" u2="&#xc5;" k="170" />
    <hkern u1="&#x27;" u2="&#xc4;" k="170" />
    <hkern u1="&#x27;" u2="&#xc3;" k="170" />
    <hkern u1="&#x27;" u2="&#xc2;" k="170" />
    <hkern u1="&#x27;" u2="&#xc1;" k="170" />
    <hkern u1="&#x27;" u2="&#xc0;" k="170" />
    <hkern u1="&#x27;" u2="&#xbb;" k="190" />
    <hkern u1="&#x27;" u2="&#xb7;" k="190" />
    <hkern u1="&#x27;" u2="&#xad;" k="190" />
    <hkern u1="&#x27;" u2="&#xab;" k="190" />
    <hkern u1="&#x27;" u2="q" k="85" />
    <hkern u1="&#x27;" u2="o" k="85" />
    <hkern u1="&#x27;" u2="e" k="85" />
    <hkern u1="&#x27;" u2="d" k="85" />
    <hkern u1="&#x27;" u2="c" k="85" />
    <hkern u1="&#x27;" u2="a" k="62" />
    <hkern u1="&#x27;" u2="\" k="-55" />
    <hkern u1="&#x27;" u2="Y" k="-25" />
    <hkern u1="&#x27;" u2="W" k="-55" />
    <hkern u1="&#x27;" u2="V" k="-55" />
    <hkern u1="&#x27;" u2="Q" k="48" />
    <hkern u1="&#x27;" u2="O" k="48" />
    <hkern u1="&#x27;" u2="G" k="48" />
    <hkern u1="&#x27;" u2="C" k="48" />
    <hkern u1="&#x27;" u2="A" k="170" />
    <hkern u1="&#x27;" u2="&#x40;" k="48" />
    <hkern u1="&#x27;" u2="&#x2f;" k="170" />
    <hkern u1="&#x27;" u2="&#x2e;" k="245" />
    <hkern u1="&#x27;" u2="&#x2d;" k="190" />
    <hkern u1="&#x27;" u2="&#x2c;" k="245" />
    <hkern u1="&#x27;" u2="&#x26;" k="170" />
    <hkern u1="&#x28;" u2="&#x153;" k="25" />
    <hkern u1="&#x28;" u2="&#x152;" k="40" />
    <hkern u1="&#x28;" u2="&#x119;" k="25" />
    <hkern u1="&#x28;" u2="&#x107;" k="25" />
    <hkern u1="&#x28;" u2="&#x106;" k="40" />
    <hkern u1="&#x28;" u2="&#xf8;" k="25" />
    <hkern u1="&#x28;" u2="&#xf6;" k="25" />
    <hkern u1="&#x28;" u2="&#xf5;" k="25" />
    <hkern u1="&#x28;" u2="&#xf4;" k="25" />
    <hkern u1="&#x28;" u2="&#xf3;" k="25" />
    <hkern u1="&#x28;" u2="&#xf2;" k="25" />
    <hkern u1="&#x28;" u2="&#xf0;" k="25" />
    <hkern u1="&#x28;" u2="&#xeb;" k="25" />
    <hkern u1="&#x28;" u2="&#xea;" k="25" />
    <hkern u1="&#x28;" u2="&#xe9;" k="25" />
    <hkern u1="&#x28;" u2="&#xe8;" k="25" />
    <hkern u1="&#x28;" u2="&#xe7;" k="25" />
    <hkern u1="&#x28;" u2="&#xd8;" k="40" />
    <hkern u1="&#x28;" u2="&#xd6;" k="40" />
    <hkern u1="&#x28;" u2="&#xd5;" k="40" />
    <hkern u1="&#x28;" u2="&#xd4;" k="40" />
    <hkern u1="&#x28;" u2="&#xd3;" k="40" />
    <hkern u1="&#x28;" u2="&#xd2;" k="40" />
    <hkern u1="&#x28;" u2="&#xc7;" k="40" />
    <hkern u1="&#x28;" u2="q" k="25" />
    <hkern u1="&#x28;" u2="o" k="25" />
    <hkern u1="&#x28;" u2="e" k="25" />
    <hkern u1="&#x28;" u2="d" k="25" />
    <hkern u1="&#x28;" u2="c" k="25" />
    <hkern u1="&#x28;" u2="Q" k="40" />
    <hkern u1="&#x28;" u2="O" k="40" />
    <hkern u1="&#x28;" u2="G" k="40" />
    <hkern u1="&#x28;" u2="C" k="40" />
    <hkern u1="&#x28;" u2="&#x40;" k="40" />
    <hkern u1="&#x2a;" u2="&#x2206;" k="170" />
    <hkern u1="&#x2a;" u2="&#x203a;" k="190" />
    <hkern u1="&#x2a;" u2="&#x2039;" k="190" />
    <hkern u1="&#x2a;" u2="&#x2022;" k="190" />
    <hkern u1="&#x2a;" u2="&#x201e;" k="245" />
    <hkern u1="&#x2a;" u2="&#x201a;" k="245" />
    <hkern u1="&#x2a;" u2="&#x2014;" k="190" />
    <hkern u1="&#x2a;" u2="&#x2013;" k="190" />
    <hkern u1="&#x2a;" u2="&#x178;" k="-25" />
    <hkern u1="&#x2a;" u2="&#x153;" k="85" />
    <hkern u1="&#x2a;" u2="&#x152;" k="48" />
    <hkern u1="&#x2a;" u2="&#x119;" k="85" />
    <hkern u1="&#x2a;" u2="&#x107;" k="85" />
    <hkern u1="&#x2a;" u2="&#x106;" k="48" />
    <hkern u1="&#x2a;" u2="&#x105;" k="62" />
    <hkern u1="&#x2a;" u2="&#x104;" k="170" />
    <hkern u1="&#x2a;" u2="&#xf8;" k="85" />
    <hkern u1="&#x2a;" u2="&#xf6;" k="85" />
    <hkern u1="&#x2a;" u2="&#xf5;" k="85" />
    <hkern u1="&#x2a;" u2="&#xf4;" k="85" />
    <hkern u1="&#x2a;" u2="&#xf3;" k="85" />
    <hkern u1="&#x2a;" u2="&#xf2;" k="85" />
    <hkern u1="&#x2a;" u2="&#xf0;" k="85" />
    <hkern u1="&#x2a;" u2="&#xeb;" k="85" />
    <hkern u1="&#x2a;" u2="&#xea;" k="85" />
    <hkern u1="&#x2a;" u2="&#xe9;" k="85" />
    <hkern u1="&#x2a;" u2="&#xe8;" k="85" />
    <hkern u1="&#x2a;" u2="&#xe7;" k="85" />
    <hkern u1="&#x2a;" u2="&#xe6;" k="62" />
    <hkern u1="&#x2a;" u2="&#xe5;" k="62" />
    <hkern u1="&#x2a;" u2="&#xe4;" k="62" />
    <hkern u1="&#x2a;" u2="&#xe3;" k="62" />
    <hkern u1="&#x2a;" u2="&#xe2;" k="62" />
    <hkern u1="&#x2a;" u2="&#xe1;" k="62" />
    <hkern u1="&#x2a;" u2="&#xe0;" k="62" />
    <hkern u1="&#x2a;" u2="&#xdd;" k="-25" />
    <hkern u1="&#x2a;" u2="&#xd8;" k="48" />
    <hkern u1="&#x2a;" u2="&#xd6;" k="48" />
    <hkern u1="&#x2a;" u2="&#xd5;" k="48" />
    <hkern u1="&#x2a;" u2="&#xd4;" k="48" />
    <hkern u1="&#x2a;" u2="&#xd3;" k="48" />
    <hkern u1="&#x2a;" u2="&#xd2;" k="48" />
    <hkern u1="&#x2a;" u2="&#xc7;" k="48" />
    <hkern u1="&#x2a;" u2="&#xc6;" k="170" />
    <hkern u1="&#x2a;" u2="&#xc5;" k="170" />
    <hkern u1="&#x2a;" u2="&#xc4;" k="170" />
    <hkern u1="&#x2a;" u2="&#xc3;" k="170" />
    <hkern u1="&#x2a;" u2="&#xc2;" k="170" />
    <hkern u1="&#x2a;" u2="&#xc1;" k="170" />
    <hkern u1="&#x2a;" u2="&#xc0;" k="170" />
    <hkern u1="&#x2a;" u2="&#xbb;" k="190" />
    <hkern u1="&#x2a;" u2="&#xb7;" k="190" />
    <hkern u1="&#x2a;" u2="&#xad;" k="190" />
    <hkern u1="&#x2a;" u2="&#xab;" k="190" />
    <hkern u1="&#x2a;" u2="q" k="85" />
    <hkern u1="&#x2a;" u2="o" k="85" />
    <hkern u1="&#x2a;" u2="e" k="85" />
    <hkern u1="&#x2a;" u2="d" k="85" />
    <hkern u1="&#x2a;" u2="c" k="85" />
    <hkern u1="&#x2a;" u2="a" k="62" />
    <hkern u1="&#x2a;" u2="\" k="-55" />
    <hkern u1="&#x2a;" u2="Y" k="-25" />
    <hkern u1="&#x2a;" u2="W" k="-55" />
    <hkern u1="&#x2a;" u2="V" k="-55" />
    <hkern u1="&#x2a;" u2="Q" k="48" />
    <hkern u1="&#x2a;" u2="O" k="48" />
    <hkern u1="&#x2a;" u2="G" k="48" />
    <hkern u1="&#x2a;" u2="C" k="48" />
    <hkern u1="&#x2a;" u2="A" k="170" />
    <hkern u1="&#x2a;" u2="&#x40;" k="48" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="170" />
    <hkern u1="&#x2a;" u2="&#x2e;" k="245" />
    <hkern u1="&#x2a;" u2="&#x2d;" k="190" />
    <hkern u1="&#x2a;" u2="&#x2c;" k="245" />
    <hkern u1="&#x2a;" u2="&#x26;" k="170" />
    <hkern u1="&#x2c;" u2="&#x2122;" k="245" />
    <hkern u1="&#x2c;" u2="&#x203a;" k="138" />
    <hkern u1="&#x2c;" u2="&#x2039;" k="138" />
    <hkern u1="&#x2c;" u2="&#x2022;" k="138" />
    <hkern u1="&#x2c;" u2="&#x201d;" k="245" />
    <hkern u1="&#x2c;" u2="&#x201c;" k="245" />
    <hkern u1="&#x2c;" u2="&#x2019;" k="245" />
    <hkern u1="&#x2c;" u2="&#x2018;" k="245" />
    <hkern u1="&#x2c;" u2="&#x2014;" k="138" />
    <hkern u1="&#x2c;" u2="&#x2013;" k="138" />
    <hkern u1="&#x2c;" u2="&#x178;" k="135" />
    <hkern u1="&#x2c;" u2="&#x152;" k="58" />
    <hkern u1="&#x2c;" u2="&#x106;" k="58" />
    <hkern u1="&#x2c;" u2="&#xdd;" k="135" />
    <hkern u1="&#x2c;" u2="&#xd8;" k="58" />
    <hkern u1="&#x2c;" u2="&#xd6;" k="58" />
    <hkern u1="&#x2c;" u2="&#xd5;" k="58" />
    <hkern u1="&#x2c;" u2="&#xd4;" k="58" />
    <hkern u1="&#x2c;" u2="&#xd3;" k="58" />
    <hkern u1="&#x2c;" u2="&#xd2;" k="58" />
    <hkern u1="&#x2c;" u2="&#xc7;" k="58" />
    <hkern u1="&#x2c;" u2="&#xbb;" k="138" />
    <hkern u1="&#x2c;" u2="&#xba;" k="245" />
    <hkern u1="&#x2c;" u2="&#xb7;" k="138" />
    <hkern u1="&#x2c;" u2="&#xb0;" k="245" />
    <hkern u1="&#x2c;" u2="&#xad;" k="138" />
    <hkern u1="&#x2c;" u2="&#xab;" k="138" />
    <hkern u1="&#x2c;" u2="&#xaa;" k="245" />
    <hkern u1="&#x2c;" u2="y" k="125" />
    <hkern u1="&#x2c;" u2="w" k="50" />
    <hkern u1="&#x2c;" u2="v" k="125" />
    <hkern u1="&#x2c;" u2="\" k="180" />
    <hkern u1="&#x2c;" u2="Y" k="135" />
    <hkern u1="&#x2c;" u2="W" k="110" />
    <hkern u1="&#x2c;" u2="V" k="180" />
    <hkern u1="&#x2c;" u2="T" k="180" />
    <hkern u1="&#x2c;" u2="Q" k="58" />
    <hkern u1="&#x2c;" u2="O" k="58" />
    <hkern u1="&#x2c;" u2="G" k="58" />
    <hkern u1="&#x2c;" u2="C" k="58" />
    <hkern u1="&#x2c;" u2="&#x40;" k="58" />
    <hkern u1="&#x2c;" u2="&#x2d;" k="138" />
    <hkern u1="&#x2c;" u2="&#x2a;" k="245" />
    <hkern u1="&#x2c;" u2="&#x27;" k="245" />
    <hkern u1="&#x2c;" u2="&#x22;" k="245" />
    <hkern u1="&#x2d;" u2="&#x2206;" k="35" />
    <hkern u1="&#x2d;" u2="&#x2122;" k="190" />
    <hkern u1="&#x2d;" u2="&#x201e;" k="138" />
    <hkern u1="&#x2d;" u2="&#x201d;" k="190" />
    <hkern u1="&#x2d;" u2="&#x201c;" k="190" />
    <hkern u1="&#x2d;" u2="&#x201a;" k="138" />
    <hkern u1="&#x2d;" u2="&#x2019;" k="190" />
    <hkern u1="&#x2d;" u2="&#x2018;" k="190" />
    <hkern u1="&#x2d;" u2="&#x17d;" k="43" />
    <hkern u1="&#x2d;" u2="&#x17b;" k="43" />
    <hkern u1="&#x2d;" u2="&#x179;" k="43" />
    <hkern u1="&#x2d;" u2="&#x178;" k="160" />
    <hkern u1="&#x2d;" u2="&#x104;" k="35" />
    <hkern u1="&#x2d;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2d;" u2="&#xc6;" k="35" />
    <hkern u1="&#x2d;" u2="&#xc5;" k="35" />
    <hkern u1="&#x2d;" u2="&#xc4;" k="35" />
    <hkern u1="&#x2d;" u2="&#xc3;" k="35" />
    <hkern u1="&#x2d;" u2="&#xc2;" k="35" />
    <hkern u1="&#x2d;" u2="&#xc1;" k="35" />
    <hkern u1="&#x2d;" u2="&#xc0;" k="35" />
    <hkern u1="&#x2d;" u2="&#xba;" k="190" />
    <hkern u1="&#x2d;" u2="&#xb0;" k="190" />
    <hkern u1="&#x2d;" u2="&#xaa;" k="190" />
    <hkern u1="&#x2d;" u2="\" k="105" />
    <hkern u1="&#x2d;" u2="Z" k="43" />
    <hkern u1="&#x2d;" u2="Y" k="160" />
    <hkern u1="&#x2d;" u2="X" k="55" />
    <hkern u1="&#x2d;" u2="W" k="25" />
    <hkern u1="&#x2d;" u2="V" k="105" />
    <hkern u1="&#x2d;" u2="T" k="180" />
    <hkern u1="&#x2d;" u2="A" k="35" />
    <hkern u1="&#x2d;" u2="&#x2f;" k="35" />
    <hkern u1="&#x2d;" u2="&#x2e;" k="138" />
    <hkern u1="&#x2d;" u2="&#x2c;" k="138" />
    <hkern u1="&#x2d;" u2="&#x2a;" k="190" />
    <hkern u1="&#x2d;" u2="&#x27;" k="190" />
    <hkern u1="&#x2d;" u2="&#x26;" k="35" />
    <hkern u1="&#x2d;" u2="&#x22;" k="190" />
    <hkern u1="&#x2e;" u2="&#x2122;" k="245" />
    <hkern u1="&#x2e;" u2="&#x203a;" k="138" />
    <hkern u1="&#x2e;" u2="&#x2039;" k="138" />
    <hkern u1="&#x2e;" u2="&#x2022;" k="138" />
    <hkern u1="&#x2e;" u2="&#x201d;" k="245" />
    <hkern u1="&#x2e;" u2="&#x201c;" k="245" />
    <hkern u1="&#x2e;" u2="&#x2019;" k="245" />
    <hkern u1="&#x2e;" u2="&#x2018;" k="245" />
    <hkern u1="&#x2e;" u2="&#x2014;" k="138" />
    <hkern u1="&#x2e;" u2="&#x2013;" k="138" />
    <hkern u1="&#x2e;" u2="&#x178;" k="135" />
    <hkern u1="&#x2e;" u2="&#x152;" k="58" />
    <hkern u1="&#x2e;" u2="&#x106;" k="58" />
    <hkern u1="&#x2e;" u2="&#xdd;" k="135" />
    <hkern u1="&#x2e;" u2="&#xd8;" k="58" />
    <hkern u1="&#x2e;" u2="&#xd6;" k="58" />
    <hkern u1="&#x2e;" u2="&#xd5;" k="58" />
    <hkern u1="&#x2e;" u2="&#xd4;" k="58" />
    <hkern u1="&#x2e;" u2="&#xd3;" k="58" />
    <hkern u1="&#x2e;" u2="&#xd2;" k="58" />
    <hkern u1="&#x2e;" u2="&#xc7;" k="58" />
    <hkern u1="&#x2e;" u2="&#xbb;" k="138" />
    <hkern u1="&#x2e;" u2="&#xba;" k="245" />
    <hkern u1="&#x2e;" u2="&#xb7;" k="138" />
    <hkern u1="&#x2e;" u2="&#xb0;" k="245" />
    <hkern u1="&#x2e;" u2="&#xad;" k="138" />
    <hkern u1="&#x2e;" u2="&#xab;" k="138" />
    <hkern u1="&#x2e;" u2="&#xaa;" k="245" />
    <hkern u1="&#x2e;" u2="y" k="125" />
    <hkern u1="&#x2e;" u2="w" k="50" />
    <hkern u1="&#x2e;" u2="v" k="125" />
    <hkern u1="&#x2e;" u2="\" k="180" />
    <hkern u1="&#x2e;" u2="Y" k="135" />
    <hkern u1="&#x2e;" u2="W" k="110" />
    <hkern u1="&#x2e;" u2="V" k="180" />
    <hkern u1="&#x2e;" u2="T" k="180" />
    <hkern u1="&#x2e;" u2="Q" k="58" />
    <hkern u1="&#x2e;" u2="O" k="58" />
    <hkern u1="&#x2e;" u2="G" k="58" />
    <hkern u1="&#x2e;" u2="C" k="58" />
    <hkern u1="&#x2e;" u2="&#x40;" k="58" />
    <hkern u1="&#x2e;" u2="&#x2d;" k="138" />
    <hkern u1="&#x2e;" u2="&#x2a;" k="245" />
    <hkern u1="&#x2e;" u2="&#x27;" k="245" />
    <hkern u1="&#x2e;" u2="&#x22;" k="245" />
    <hkern u1="&#x2f;" u2="&#x2206;" k="95" />
    <hkern u1="&#x2f;" u2="&#x2122;" k="-55" />
    <hkern u1="&#x2f;" u2="&#x203a;" k="105" />
    <hkern u1="&#x2f;" u2="&#x2039;" k="105" />
    <hkern u1="&#x2f;" u2="&#x2022;" k="105" />
    <hkern u1="&#x2f;" u2="&#x201e;" k="185" />
    <hkern u1="&#x2f;" u2="&#x201d;" k="-55" />
    <hkern u1="&#x2f;" u2="&#x201c;" k="-55" />
    <hkern u1="&#x2f;" u2="&#x201a;" k="185" />
    <hkern u1="&#x2f;" u2="&#x2019;" k="-55" />
    <hkern u1="&#x2f;" u2="&#x2018;" k="-55" />
    <hkern u1="&#x2f;" u2="&#x2014;" k="105" />
    <hkern u1="&#x2f;" u2="&#x2013;" k="105" />
    <hkern u1="&#x2f;" u2="&#x153;" k="108" />
    <hkern u1="&#x2f;" u2="&#x152;" k="45" />
    <hkern u1="&#x2f;" u2="&#x144;" k="73" />
    <hkern u1="&#x2f;" u2="&#x119;" k="108" />
    <hkern u1="&#x2f;" u2="&#x107;" k="108" />
    <hkern u1="&#x2f;" u2="&#x106;" k="45" />
    <hkern u1="&#x2f;" u2="&#x105;" k="108" />
    <hkern u1="&#x2f;" u2="&#x104;" k="95" />
    <hkern u1="&#x2f;" u2="&#xfc;" k="73" />
    <hkern u1="&#x2f;" u2="&#xfb;" k="73" />
    <hkern u1="&#x2f;" u2="&#xfa;" k="73" />
    <hkern u1="&#x2f;" u2="&#xf9;" k="73" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="108" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="108" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="108" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="108" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="108" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="108" />
    <hkern u1="&#x2f;" u2="&#xf1;" k="73" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="108" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="108" />
    <hkern u1="&#x2f;" u2="&#xea;" k="108" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="108" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="108" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="108" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="108" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="108" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="108" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="108" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="108" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="108" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="108" />
    <hkern u1="&#x2f;" u2="&#xd8;" k="45" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="45" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="45" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="45" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="45" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="45" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="45" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="95" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="95" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="95" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="95" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="95" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="95" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="95" />
    <hkern u1="&#x2f;" u2="&#xbb;" k="105" />
    <hkern u1="&#x2f;" u2="&#xba;" k="-55" />
    <hkern u1="&#x2f;" u2="&#xb9;" k="-70" />
    <hkern u1="&#x2f;" u2="&#xb7;" k="105" />
    <hkern u1="&#x2f;" u2="&#xb5;" k="73" />
    <hkern u1="&#x2f;" u2="&#xb3;" k="-70" />
    <hkern u1="&#x2f;" u2="&#xb2;" k="-70" />
    <hkern u1="&#x2f;" u2="&#xb0;" k="-55" />
    <hkern u1="&#x2f;" u2="&#xad;" k="105" />
    <hkern u1="&#x2f;" u2="&#xab;" k="105" />
    <hkern u1="&#x2f;" u2="&#xaa;" k="-55" />
    <hkern u1="&#x2f;" u2="z" k="70" />
    <hkern u1="&#x2f;" u2="y" k="33" />
    <hkern u1="&#x2f;" u2="x" k="35" />
    <hkern u1="&#x2f;" u2="v" k="33" />
    <hkern u1="&#x2f;" u2="u" k="73" />
    <hkern u1="&#x2f;" u2="t" k="35" />
    <hkern u1="&#x2f;" u2="s" k="103" />
    <hkern u1="&#x2f;" u2="r" k="73" />
    <hkern u1="&#x2f;" u2="q" k="108" />
    <hkern u1="&#x2f;" u2="p" k="73" />
    <hkern u1="&#x2f;" u2="o" k="108" />
    <hkern u1="&#x2f;" u2="n" k="73" />
    <hkern u1="&#x2f;" u2="m" k="73" />
    <hkern u1="&#x2f;" u2="g" k="133" />
    <hkern u1="&#x2f;" u2="f" k="30" />
    <hkern u1="&#x2f;" u2="e" k="108" />
    <hkern u1="&#x2f;" u2="d" k="108" />
    <hkern u1="&#x2f;" u2="c" k="108" />
    <hkern u1="&#x2f;" u2="a" k="108" />
    <hkern u1="&#x2f;" u2="Q" k="45" />
    <hkern u1="&#x2f;" u2="O" k="45" />
    <hkern u1="&#x2f;" u2="J" k="145" />
    <hkern u1="&#x2f;" u2="G" k="45" />
    <hkern u1="&#x2f;" u2="C" k="45" />
    <hkern u1="&#x2f;" u2="A" k="95" />
    <hkern u1="&#x2f;" u2="&#x40;" k="45" />
    <hkern u1="&#x2f;" u2="&#x3f;" k="-60" />
    <hkern u1="&#x2f;" u2="&#x3b;" k="73" />
    <hkern u1="&#x2f;" u2="&#x3a;" k="73" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="95" />
    <hkern u1="&#x2f;" u2="&#x2e;" k="185" />
    <hkern u1="&#x2f;" u2="&#x2d;" k="105" />
    <hkern u1="&#x2f;" u2="&#x2c;" k="185" />
    <hkern u1="&#x2f;" u2="&#x2a;" k="-55" />
    <hkern u1="&#x2f;" u2="&#x27;" k="-55" />
    <hkern u1="&#x2f;" u2="&#x26;" k="95" />
    <hkern u1="&#x2f;" u2="&#x22;" k="-55" />
    <hkern u1="&#x40;" u2="&#x2206;" k="30" />
    <hkern u1="&#x40;" u2="&#x2122;" k="48" />
    <hkern u1="&#x40;" u2="&#x201e;" k="58" />
    <hkern u1="&#x40;" u2="&#x201d;" k="48" />
    <hkern u1="&#x40;" u2="&#x201c;" k="48" />
    <hkern u1="&#x40;" u2="&#x201a;" k="58" />
    <hkern u1="&#x40;" u2="&#x2019;" k="48" />
    <hkern u1="&#x40;" u2="&#x2018;" k="48" />
    <hkern u1="&#x40;" u2="&#x17d;" k="75" />
    <hkern u1="&#x40;" u2="&#x17b;" k="75" />
    <hkern u1="&#x40;" u2="&#x179;" k="75" />
    <hkern u1="&#x40;" u2="&#x178;" k="80" />
    <hkern u1="&#x40;" u2="&#x104;" k="30" />
    <hkern u1="&#x40;" u2="&#xdd;" k="80" />
    <hkern u1="&#x40;" u2="&#xc6;" k="30" />
    <hkern u1="&#x40;" u2="&#xc5;" k="30" />
    <hkern u1="&#x40;" u2="&#xc4;" k="30" />
    <hkern u1="&#x40;" u2="&#xc3;" k="30" />
    <hkern u1="&#x40;" u2="&#xc2;" k="30" />
    <hkern u1="&#x40;" u2="&#xc1;" k="30" />
    <hkern u1="&#x40;" u2="&#xc0;" k="30" />
    <hkern u1="&#x40;" u2="&#xba;" k="48" />
    <hkern u1="&#x40;" u2="&#xb0;" k="48" />
    <hkern u1="&#x40;" u2="&#xaa;" k="48" />
    <hkern u1="&#x40;" u2="&#x7d;" k="40" />
    <hkern u1="&#x40;" u2="]" k="40" />
    <hkern u1="&#x40;" u2="\" k="45" />
    <hkern u1="&#x40;" u2="Z" k="75" />
    <hkern u1="&#x40;" u2="Y" k="80" />
    <hkern u1="&#x40;" u2="X" k="30" />
    <hkern u1="&#x40;" u2="V" k="45" />
    <hkern u1="&#x40;" u2="T" k="120" />
    <hkern u1="&#x40;" u2="A" k="30" />
    <hkern u1="&#x40;" u2="&#x2f;" k="30" />
    <hkern u1="&#x40;" u2="&#x2e;" k="58" />
    <hkern u1="&#x40;" u2="&#x2c;" k="58" />
    <hkern u1="&#x40;" u2="&#x2a;" k="48" />
    <hkern u1="&#x40;" u2="&#x29;" k="40" />
    <hkern u1="&#x40;" u2="&#x27;" k="48" />
    <hkern u1="&#x40;" u2="&#x26;" k="30" />
    <hkern u1="&#x40;" u2="&#x22;" k="48" />
    <hkern u1="A" u2="&#x2122;" k="170" />
    <hkern u1="A" u2="&#x203a;" k="35" />
    <hkern u1="A" u2="&#x2039;" k="35" />
    <hkern u1="A" u2="&#x2022;" k="35" />
    <hkern u1="A" u2="&#x201d;" k="170" />
    <hkern u1="A" u2="&#x201c;" k="170" />
    <hkern u1="A" u2="&#x2019;" k="170" />
    <hkern u1="A" u2="&#x2018;" k="170" />
    <hkern u1="A" u2="&#x2014;" k="35" />
    <hkern u1="A" u2="&#x2013;" k="35" />
    <hkern u1="A" u2="&#x178;" k="140" />
    <hkern u1="A" u2="&#x152;" k="30" />
    <hkern u1="A" u2="&#x106;" k="30" />
    <hkern u1="A" u2="&#xdd;" k="140" />
    <hkern u1="A" u2="&#xdc;" k="58" />
    <hkern u1="A" u2="&#xdb;" k="58" />
    <hkern u1="A" u2="&#xda;" k="58" />
    <hkern u1="A" u2="&#xd9;" k="58" />
    <hkern u1="A" u2="&#xd8;" k="30" />
    <hkern u1="A" u2="&#xd6;" k="30" />
    <hkern u1="A" u2="&#xd5;" k="30" />
    <hkern u1="A" u2="&#xd4;" k="30" />
    <hkern u1="A" u2="&#xd3;" k="30" />
    <hkern u1="A" u2="&#xd2;" k="30" />
    <hkern u1="A" u2="&#xc7;" k="30" />
    <hkern u1="A" u2="&#xbb;" k="35" />
    <hkern u1="A" u2="&#xba;" k="170" />
    <hkern u1="A" u2="&#xb9;" k="160" />
    <hkern u1="A" u2="&#xb7;" k="35" />
    <hkern u1="A" u2="&#xb3;" k="160" />
    <hkern u1="A" u2="&#xb2;" k="160" />
    <hkern u1="A" u2="&#xb0;" k="170" />
    <hkern u1="A" u2="&#xad;" k="35" />
    <hkern u1="A" u2="&#xab;" k="35" />
    <hkern u1="A" u2="&#xaa;" k="170" />
    <hkern u1="A" u2="y" k="70" />
    <hkern u1="A" u2="v" k="70" />
    <hkern u1="A" u2="\" k="95" />
    <hkern u1="A" u2="Y" k="140" />
    <hkern u1="A" u2="W" k="60" />
    <hkern u1="A" u2="V" k="95" />
    <hkern u1="A" u2="U" k="58" />
    <hkern u1="A" u2="T" k="115" />
    <hkern u1="A" u2="Q" k="30" />
    <hkern u1="A" u2="O" k="30" />
    <hkern u1="A" u2="J" k="-45" />
    <hkern u1="A" u2="G" k="30" />
    <hkern u1="A" u2="C" k="30" />
    <hkern u1="A" u2="&#x40;" k="30" />
    <hkern u1="A" u2="&#x3f;" k="48" />
    <hkern u1="A" u2="&#x2d;" k="35" />
    <hkern u1="A" u2="&#x2a;" k="170" />
    <hkern u1="A" u2="&#x27;" k="170" />
    <hkern u1="A" u2="&#x22;" k="170" />
    <hkern u1="C" u2="&#x203a;" k="155" />
    <hkern u1="C" u2="&#x2039;" k="155" />
    <hkern u1="C" u2="&#x2022;" k="155" />
    <hkern u1="C" u2="&#x2014;" k="155" />
    <hkern u1="C" u2="&#x2013;" k="155" />
    <hkern u1="C" u2="&#xbb;" k="155" />
    <hkern u1="C" u2="&#xb7;" k="155" />
    <hkern u1="C" u2="&#xad;" k="155" />
    <hkern u1="C" u2="&#xab;" k="155" />
    <hkern u1="C" u2="&#x2d;" k="155" />
    <hkern u1="D" u2="&#x2206;" k="30" />
    <hkern u1="D" u2="&#x2122;" k="48" />
    <hkern u1="D" u2="&#x201e;" k="58" />
    <hkern u1="D" u2="&#x201d;" k="48" />
    <hkern u1="D" u2="&#x201c;" k="48" />
    <hkern u1="D" u2="&#x201a;" k="58" />
    <hkern u1="D" u2="&#x2019;" k="48" />
    <hkern u1="D" u2="&#x2018;" k="48" />
    <hkern u1="D" u2="&#x17d;" k="75" />
    <hkern u1="D" u2="&#x17b;" k="75" />
    <hkern u1="D" u2="&#x179;" k="75" />
    <hkern u1="D" u2="&#x178;" k="80" />
    <hkern u1="D" u2="&#x104;" k="30" />
    <hkern u1="D" u2="&#xdd;" k="80" />
    <hkern u1="D" u2="&#xc6;" k="30" />
    <hkern u1="D" u2="&#xc5;" k="30" />
    <hkern u1="D" u2="&#xc4;" k="30" />
    <hkern u1="D" u2="&#xc3;" k="30" />
    <hkern u1="D" u2="&#xc2;" k="30" />
    <hkern u1="D" u2="&#xc1;" k="30" />
    <hkern u1="D" u2="&#xc0;" k="30" />
    <hkern u1="D" u2="&#xba;" k="48" />
    <hkern u1="D" u2="&#xb0;" k="48" />
    <hkern u1="D" u2="&#xaa;" k="48" />
    <hkern u1="D" u2="&#x7d;" k="40" />
    <hkern u1="D" u2="]" k="40" />
    <hkern u1="D" u2="\" k="45" />
    <hkern u1="D" u2="Z" k="75" />
    <hkern u1="D" u2="Y" k="80" />
    <hkern u1="D" u2="X" k="30" />
    <hkern u1="D" u2="V" k="45" />
    <hkern u1="D" u2="T" k="120" />
    <hkern u1="D" u2="A" k="30" />
    <hkern u1="D" u2="&#x2f;" k="30" />
    <hkern u1="D" u2="&#x2e;" k="58" />
    <hkern u1="D" u2="&#x2c;" k="58" />
    <hkern u1="D" u2="&#x2a;" k="48" />
    <hkern u1="D" u2="&#x29;" k="40" />
    <hkern u1="D" u2="&#x27;" k="48" />
    <hkern u1="D" u2="&#x26;" k="30" />
    <hkern u1="D" u2="&#x22;" k="48" />
    <hkern u1="F" u2="&#x2206;" k="115" />
    <hkern u1="F" u2="&#x201e;" k="180" />
    <hkern u1="F" u2="&#x201a;" k="180" />
    <hkern u1="F" u2="&#x153;" k="75" />
    <hkern u1="F" u2="&#x144;" k="60" />
    <hkern u1="F" u2="&#x119;" k="75" />
    <hkern u1="F" u2="&#x107;" k="75" />
    <hkern u1="F" u2="&#x104;" k="115" />
    <hkern u1="F" u2="&#xfc;" k="60" />
    <hkern u1="F" u2="&#xfb;" k="60" />
    <hkern u1="F" u2="&#xfa;" k="60" />
    <hkern u1="F" u2="&#xf9;" k="60" />
    <hkern u1="F" u2="&#xf8;" k="75" />
    <hkern u1="F" u2="&#xf6;" k="75" />
    <hkern u1="F" u2="&#xf5;" k="75" />
    <hkern u1="F" u2="&#xf4;" k="75" />
    <hkern u1="F" u2="&#xf3;" k="75" />
    <hkern u1="F" u2="&#xf2;" k="75" />
    <hkern u1="F" u2="&#xf1;" k="60" />
    <hkern u1="F" u2="&#xf0;" k="75" />
    <hkern u1="F" u2="&#xeb;" k="75" />
    <hkern u1="F" u2="&#xea;" k="75" />
    <hkern u1="F" u2="&#xe9;" k="75" />
    <hkern u1="F" u2="&#xe8;" k="75" />
    <hkern u1="F" u2="&#xe7;" k="75" />
    <hkern u1="F" u2="&#xc6;" k="115" />
    <hkern u1="F" u2="&#xc5;" k="115" />
    <hkern u1="F" u2="&#xc4;" k="115" />
    <hkern u1="F" u2="&#xc3;" k="115" />
    <hkern u1="F" u2="&#xc2;" k="115" />
    <hkern u1="F" u2="&#xc1;" k="115" />
    <hkern u1="F" u2="&#xc0;" k="115" />
    <hkern u1="F" u2="&#xb5;" k="60" />
    <hkern u1="F" u2="u" k="60" />
    <hkern u1="F" u2="r" k="60" />
    <hkern u1="F" u2="q" k="75" />
    <hkern u1="F" u2="p" k="60" />
    <hkern u1="F" u2="o" k="75" />
    <hkern u1="F" u2="n" k="60" />
    <hkern u1="F" u2="m" k="60" />
    <hkern u1="F" u2="e" k="75" />
    <hkern u1="F" u2="d" k="75" />
    <hkern u1="F" u2="c" k="75" />
    <hkern u1="F" u2="J" k="210" />
    <hkern u1="F" u2="A" k="115" />
    <hkern u1="F" u2="&#x3f;" k="-30" />
    <hkern u1="F" u2="&#x3b;" k="60" />
    <hkern u1="F" u2="&#x3a;" k="60" />
    <hkern u1="F" u2="&#x2f;" k="115" />
    <hkern u1="F" u2="&#x2e;" k="180" />
    <hkern u1="F" u2="&#x2c;" k="180" />
    <hkern u1="F" u2="&#x26;" k="115" />
    <hkern u1="J" u2="&#x2206;" k="58" />
    <hkern u1="J" u2="&#x201e;" k="50" />
    <hkern u1="J" u2="&#x201a;" k="50" />
    <hkern u1="J" u2="&#x104;" k="58" />
    <hkern u1="J" u2="&#xc6;" k="58" />
    <hkern u1="J" u2="&#xc5;" k="58" />
    <hkern u1="J" u2="&#xc4;" k="58" />
    <hkern u1="J" u2="&#xc3;" k="58" />
    <hkern u1="J" u2="&#xc2;" k="58" />
    <hkern u1="J" u2="&#xc1;" k="58" />
    <hkern u1="J" u2="&#xc0;" k="58" />
    <hkern u1="J" u2="A" k="58" />
    <hkern u1="J" u2="&#x2f;" k="58" />
    <hkern u1="J" u2="&#x2e;" k="50" />
    <hkern u1="J" u2="&#x2c;" k="50" />
    <hkern u1="J" u2="&#x26;" k="58" />
    <hkern u1="K" u2="&#x203a;" k="55" />
    <hkern u1="K" u2="&#x2039;" k="55" />
    <hkern u1="K" u2="&#x2022;" k="55" />
    <hkern u1="K" u2="&#x2014;" k="55" />
    <hkern u1="K" u2="&#x2013;" k="55" />
    <hkern u1="K" u2="&#x153;" k="28" />
    <hkern u1="K" u2="&#x152;" k="30" />
    <hkern u1="K" u2="&#x119;" k="28" />
    <hkern u1="K" u2="&#x107;" k="28" />
    <hkern u1="K" u2="&#x106;" k="30" />
    <hkern u1="K" u2="&#xf8;" k="28" />
    <hkern u1="K" u2="&#xf6;" k="28" />
    <hkern u1="K" u2="&#xf5;" k="28" />
    <hkern u1="K" u2="&#xf4;" k="28" />
    <hkern u1="K" u2="&#xf3;" k="28" />
    <hkern u1="K" u2="&#xf2;" k="28" />
    <hkern u1="K" u2="&#xf0;" k="28" />
    <hkern u1="K" u2="&#xeb;" k="28" />
    <hkern u1="K" u2="&#xea;" k="28" />
    <hkern u1="K" u2="&#xe9;" k="28" />
    <hkern u1="K" u2="&#xe8;" k="28" />
    <hkern u1="K" u2="&#xe7;" k="28" />
    <hkern u1="K" u2="&#xd8;" k="30" />
    <hkern u1="K" u2="&#xd6;" k="30" />
    <hkern u1="K" u2="&#xd5;" k="30" />
    <hkern u1="K" u2="&#xd4;" k="30" />
    <hkern u1="K" u2="&#xd3;" k="30" />
    <hkern u1="K" u2="&#xd2;" k="30" />
    <hkern u1="K" u2="&#xc7;" k="30" />
    <hkern u1="K" u2="&#xbb;" k="55" />
    <hkern u1="K" u2="&#xb7;" k="55" />
    <hkern u1="K" u2="&#xad;" k="55" />
    <hkern u1="K" u2="&#xab;" k="55" />
    <hkern u1="K" u2="y" k="58" />
    <hkern u1="K" u2="w" k="58" />
    <hkern u1="K" u2="v" k="58" />
    <hkern u1="K" u2="t" k="70" />
    <hkern u1="K" u2="q" k="28" />
    <hkern u1="K" u2="o" k="28" />
    <hkern u1="K" u2="f" k="45" />
    <hkern u1="K" u2="e" k="28" />
    <hkern u1="K" u2="d" k="28" />
    <hkern u1="K" u2="c" k="28" />
    <hkern u1="K" u2="Q" k="30" />
    <hkern u1="K" u2="O" k="30" />
    <hkern u1="K" u2="G" k="30" />
    <hkern u1="K" u2="C" k="30" />
    <hkern u1="K" u2="&#x40;" k="30" />
    <hkern u1="K" u2="&#x2d;" k="55" />
    <hkern u1="L" u2="&#x2122;" k="295" />
    <hkern u1="L" u2="&#x203a;" k="220" />
    <hkern u1="L" u2="&#x2039;" k="220" />
    <hkern u1="L" u2="&#x2022;" k="220" />
    <hkern u1="L" u2="&#x201e;" k="-52" />
    <hkern u1="L" u2="&#x201d;" k="295" />
    <hkern u1="L" u2="&#x201c;" k="295" />
    <hkern u1="L" u2="&#x201a;" k="-52" />
    <hkern u1="L" u2="&#x2019;" k="295" />
    <hkern u1="L" u2="&#x2018;" k="295" />
    <hkern u1="L" u2="&#x2014;" k="220" />
    <hkern u1="L" u2="&#x2013;" k="220" />
    <hkern u1="L" u2="&#x178;" k="195" />
    <hkern u1="L" u2="&#x153;" k="33" />
    <hkern u1="L" u2="&#x152;" k="80" />
    <hkern u1="L" u2="&#x119;" k="33" />
    <hkern u1="L" u2="&#x107;" k="33" />
    <hkern u1="L" u2="&#x106;" k="80" />
    <hkern u1="L" u2="&#xf8;" k="33" />
    <hkern u1="L" u2="&#xf6;" k="33" />
    <hkern u1="L" u2="&#xf5;" k="33" />
    <hkern u1="L" u2="&#xf4;" k="33" />
    <hkern u1="L" u2="&#xf3;" k="33" />
    <hkern u1="L" u2="&#xf2;" k="33" />
    <hkern u1="L" u2="&#xf0;" k="33" />
    <hkern u1="L" u2="&#xeb;" k="33" />
    <hkern u1="L" u2="&#xea;" k="33" />
    <hkern u1="L" u2="&#xe9;" k="33" />
    <hkern u1="L" u2="&#xe8;" k="33" />
    <hkern u1="L" u2="&#xe7;" k="33" />
    <hkern u1="L" u2="&#xdd;" k="195" />
    <hkern u1="L" u2="&#xd8;" k="80" />
    <hkern u1="L" u2="&#xd6;" k="80" />
    <hkern u1="L" u2="&#xd5;" k="80" />
    <hkern u1="L" u2="&#xd4;" k="80" />
    <hkern u1="L" u2="&#xd3;" k="80" />
    <hkern u1="L" u2="&#xd2;" k="80" />
    <hkern u1="L" u2="&#xc7;" k="80" />
    <hkern u1="L" u2="&#xbb;" k="220" />
    <hkern u1="L" u2="&#xba;" k="295" />
    <hkern u1="L" u2="&#xb9;" k="190" />
    <hkern u1="L" u2="&#xb7;" k="220" />
    <hkern u1="L" u2="&#xb3;" k="190" />
    <hkern u1="L" u2="&#xb2;" k="190" />
    <hkern u1="L" u2="&#xb0;" k="295" />
    <hkern u1="L" u2="&#xad;" k="220" />
    <hkern u1="L" u2="&#xab;" k="220" />
    <hkern u1="L" u2="&#xaa;" k="295" />
    <hkern u1="L" u2="y" k="93" />
    <hkern u1="L" u2="w" k="80" />
    <hkern u1="L" u2="v" k="93" />
    <hkern u1="L" u2="q" k="33" />
    <hkern u1="L" u2="o" k="33" />
    <hkern u1="L" u2="e" k="33" />
    <hkern u1="L" u2="d" k="33" />
    <hkern u1="L" u2="c" k="33" />
    <hkern u1="L" u2="\" k="170" />
    <hkern u1="L" u2="Y" k="195" />
    <hkern u1="L" u2="W" k="135" />
    <hkern u1="L" u2="V" k="170" />
    <hkern u1="L" u2="T" k="165" />
    <hkern u1="L" u2="Q" k="80" />
    <hkern u1="L" u2="O" k="80" />
    <hkern u1="L" u2="G" k="80" />
    <hkern u1="L" u2="C" k="80" />
    <hkern u1="L" u2="&#x40;" k="80" />
    <hkern u1="L" u2="&#x3f;" k="50" />
    <hkern u1="L" u2="&#x2e;" k="-52" />
    <hkern u1="L" u2="&#x2d;" k="220" />
    <hkern u1="L" u2="&#x2c;" k="-52" />
    <hkern u1="L" u2="&#x2a;" k="295" />
    <hkern u1="L" u2="&#x27;" k="295" />
    <hkern u1="L" u2="&#x22;" k="295" />
    <hkern u1="O" u2="&#x2206;" k="30" />
    <hkern u1="O" u2="&#x2122;" k="48" />
    <hkern u1="O" u2="&#x201e;" k="58" />
    <hkern u1="O" u2="&#x201d;" k="48" />
    <hkern u1="O" u2="&#x201c;" k="48" />
    <hkern u1="O" u2="&#x201a;" k="58" />
    <hkern u1="O" u2="&#x2019;" k="48" />
    <hkern u1="O" u2="&#x2018;" k="48" />
    <hkern u1="O" u2="&#x17d;" k="75" />
    <hkern u1="O" u2="&#x17b;" k="75" />
    <hkern u1="O" u2="&#x179;" k="75" />
    <hkern u1="O" u2="&#x178;" k="80" />
    <hkern u1="O" u2="&#x104;" k="30" />
    <hkern u1="O" u2="&#xdd;" k="80" />
    <hkern u1="O" u2="&#xc6;" k="30" />
    <hkern u1="O" u2="&#xc5;" k="30" />
    <hkern u1="O" u2="&#xc4;" k="30" />
    <hkern u1="O" u2="&#xc3;" k="30" />
    <hkern u1="O" u2="&#xc2;" k="30" />
    <hkern u1="O" u2="&#xc1;" k="30" />
    <hkern u1="O" u2="&#xc0;" k="30" />
    <hkern u1="O" u2="&#xba;" k="48" />
    <hkern u1="O" u2="&#xb0;" k="48" />
    <hkern u1="O" u2="&#xaa;" k="48" />
    <hkern u1="O" u2="&#x7d;" k="40" />
    <hkern u1="O" u2="]" k="40" />
    <hkern u1="O" u2="\" k="45" />
    <hkern u1="O" u2="Z" k="75" />
    <hkern u1="O" u2="Y" k="80" />
    <hkern u1="O" u2="X" k="30" />
    <hkern u1="O" u2="V" k="45" />
    <hkern u1="O" u2="T" k="120" />
    <hkern u1="O" u2="A" k="30" />
    <hkern u1="O" u2="&#x2f;" k="30" />
    <hkern u1="O" u2="&#x2e;" k="58" />
    <hkern u1="O" u2="&#x2c;" k="58" />
    <hkern u1="O" u2="&#x2a;" k="48" />
    <hkern u1="O" u2="&#x29;" k="40" />
    <hkern u1="O" u2="&#x27;" k="48" />
    <hkern u1="O" u2="&#x26;" k="30" />
    <hkern u1="O" u2="&#x22;" k="48" />
    <hkern u1="P" u2="&#x2206;" k="118" />
    <hkern u1="P" u2="&#x201e;" k="228" />
    <hkern u1="P" u2="&#x201a;" k="228" />
    <hkern u1="P" u2="&#x153;" k="30" />
    <hkern u1="P" u2="&#x119;" k="30" />
    <hkern u1="P" u2="&#x107;" k="30" />
    <hkern u1="P" u2="&#x105;" k="50" />
    <hkern u1="P" u2="&#x104;" k="118" />
    <hkern u1="P" u2="&#xf8;" k="30" />
    <hkern u1="P" u2="&#xf6;" k="30" />
    <hkern u1="P" u2="&#xf5;" k="30" />
    <hkern u1="P" u2="&#xf4;" k="30" />
    <hkern u1="P" u2="&#xf3;" k="30" />
    <hkern u1="P" u2="&#xf2;" k="30" />
    <hkern u1="P" u2="&#xf0;" k="30" />
    <hkern u1="P" u2="&#xeb;" k="30" />
    <hkern u1="P" u2="&#xea;" k="30" />
    <hkern u1="P" u2="&#xe9;" k="30" />
    <hkern u1="P" u2="&#xe8;" k="30" />
    <hkern u1="P" u2="&#xe7;" k="30" />
    <hkern u1="P" u2="&#xe6;" k="50" />
    <hkern u1="P" u2="&#xe5;" k="50" />
    <hkern u1="P" u2="&#xe4;" k="50" />
    <hkern u1="P" u2="&#xe3;" k="50" />
    <hkern u1="P" u2="&#xe2;" k="50" />
    <hkern u1="P" u2="&#xe1;" k="50" />
    <hkern u1="P" u2="&#xe0;" k="50" />
    <hkern u1="P" u2="&#xc6;" k="118" />
    <hkern u1="P" u2="&#xc5;" k="118" />
    <hkern u1="P" u2="&#xc4;" k="118" />
    <hkern u1="P" u2="&#xc3;" k="118" />
    <hkern u1="P" u2="&#xc2;" k="118" />
    <hkern u1="P" u2="&#xc1;" k="118" />
    <hkern u1="P" u2="&#xc0;" k="118" />
    <hkern u1="P" u2="q" k="30" />
    <hkern u1="P" u2="o" k="30" />
    <hkern u1="P" u2="e" k="30" />
    <hkern u1="P" u2="d" k="30" />
    <hkern u1="P" u2="c" k="30" />
    <hkern u1="P" u2="a" k="50" />
    <hkern u1="P" u2="J" k="170" />
    <hkern u1="P" u2="A" k="118" />
    <hkern u1="P" u2="&#x2f;" k="118" />
    <hkern u1="P" u2="&#x2e;" k="228" />
    <hkern u1="P" u2="&#x2c;" k="228" />
    <hkern u1="P" u2="&#x26;" k="118" />
    <hkern u1="Q" u2="&#x2206;" k="30" />
    <hkern u1="Q" u2="&#x2122;" k="48" />
    <hkern u1="Q" u2="&#x201e;" k="58" />
    <hkern u1="Q" u2="&#x201d;" k="48" />
    <hkern u1="Q" u2="&#x201c;" k="48" />
    <hkern u1="Q" u2="&#x201a;" k="58" />
    <hkern u1="Q" u2="&#x2019;" k="48" />
    <hkern u1="Q" u2="&#x2018;" k="48" />
    <hkern u1="Q" u2="&#x17d;" k="75" />
    <hkern u1="Q" u2="&#x17b;" k="75" />
    <hkern u1="Q" u2="&#x179;" k="75" />
    <hkern u1="Q" u2="&#x178;" k="80" />
    <hkern u1="Q" u2="&#x104;" k="30" />
    <hkern u1="Q" u2="&#xdd;" k="80" />
    <hkern u1="Q" u2="&#xc6;" k="30" />
    <hkern u1="Q" u2="&#xc5;" k="30" />
    <hkern u1="Q" u2="&#xc4;" k="30" />
    <hkern u1="Q" u2="&#xc3;" k="30" />
    <hkern u1="Q" u2="&#xc2;" k="30" />
    <hkern u1="Q" u2="&#xc1;" k="30" />
    <hkern u1="Q" u2="&#xc0;" k="30" />
    <hkern u1="Q" u2="&#xba;" k="48" />
    <hkern u1="Q" u2="&#xb0;" k="48" />
    <hkern u1="Q" u2="&#xaa;" k="48" />
    <hkern u1="Q" u2="&#x7d;" k="40" />
    <hkern u1="Q" u2="]" k="40" />
    <hkern u1="Q" u2="\" k="45" />
    <hkern u1="Q" u2="Z" k="75" />
    <hkern u1="Q" u2="Y" k="80" />
    <hkern u1="Q" u2="X" k="30" />
    <hkern u1="Q" u2="V" k="45" />
    <hkern u1="Q" u2="T" k="120" />
    <hkern u1="Q" u2="A" k="30" />
    <hkern u1="Q" u2="&#x2f;" k="30" />
    <hkern u1="Q" u2="&#x2e;" k="58" />
    <hkern u1="Q" u2="&#x2c;" k="58" />
    <hkern u1="Q" u2="&#x2a;" k="48" />
    <hkern u1="Q" u2="&#x29;" k="40" />
    <hkern u1="Q" u2="&#x27;" k="48" />
    <hkern u1="Q" u2="&#x26;" k="30" />
    <hkern u1="Q" u2="&#x22;" k="48" />
    <hkern u1="R" u2="&#x152;" k="43" />
    <hkern u1="R" u2="&#x106;" k="43" />
    <hkern u1="R" u2="&#xdc;" k="30" />
    <hkern u1="R" u2="&#xdb;" k="30" />
    <hkern u1="R" u2="&#xda;" k="30" />
    <hkern u1="R" u2="&#xd9;" k="30" />
    <hkern u1="R" u2="&#xd8;" k="43" />
    <hkern u1="R" u2="&#xd6;" k="43" />
    <hkern u1="R" u2="&#xd5;" k="43" />
    <hkern u1="R" u2="&#xd4;" k="43" />
    <hkern u1="R" u2="&#xd3;" k="43" />
    <hkern u1="R" u2="&#xd2;" k="43" />
    <hkern u1="R" u2="&#xc7;" k="43" />
    <hkern u1="R" u2="U" k="30" />
    <hkern u1="R" u2="T" k="45" />
    <hkern u1="R" u2="Q" k="43" />
    <hkern u1="R" u2="O" k="43" />
    <hkern u1="R" u2="G" k="43" />
    <hkern u1="R" u2="C" k="43" />
    <hkern u1="R" u2="&#x40;" k="43" />
    <hkern u1="T" u2="&#x2206;" k="115" />
    <hkern u1="T" u2="&#x203a;" k="180" />
    <hkern u1="T" u2="&#x2039;" k="180" />
    <hkern u1="T" u2="&#x2022;" k="180" />
    <hkern u1="T" u2="&#x201e;" k="180" />
    <hkern u1="T" u2="&#x201a;" k="180" />
    <hkern u1="T" u2="&#x2014;" k="180" />
    <hkern u1="T" u2="&#x2013;" k="180" />
    <hkern u1="T" u2="&#x153;" k="215" />
    <hkern u1="T" u2="&#x152;" k="120" />
    <hkern u1="T" u2="&#x144;" k="160" />
    <hkern u1="T" u2="&#x119;" k="215" />
    <hkern u1="T" u2="&#x107;" k="215" />
    <hkern u1="T" u2="&#x106;" k="120" />
    <hkern u1="T" u2="&#x105;" k="255" />
    <hkern u1="T" u2="&#x104;" k="115" />
    <hkern u1="T" u2="&#xfc;" k="160" />
    <hkern u1="T" u2="&#xfb;" k="160" />
    <hkern u1="T" u2="&#xfa;" k="160" />
    <hkern u1="T" u2="&#xf9;" k="160" />
    <hkern u1="T" u2="&#xf8;" k="215" />
    <hkern u1="T" u2="&#xf6;" k="215" />
    <hkern u1="T" u2="&#xf5;" k="215" />
    <hkern u1="T" u2="&#xf4;" k="215" />
    <hkern u1="T" u2="&#xf3;" k="215" />
    <hkern u1="T" u2="&#xf2;" k="215" />
    <hkern u1="T" u2="&#xf1;" k="160" />
    <hkern u1="T" u2="&#xf0;" k="215" />
    <hkern u1="T" u2="&#xeb;" k="215" />
    <hkern u1="T" u2="&#xea;" k="215" />
    <hkern u1="T" u2="&#xe9;" k="215" />
    <hkern u1="T" u2="&#xe8;" k="215" />
    <hkern u1="T" u2="&#xe7;" k="215" />
    <hkern u1="T" u2="&#xe6;" k="255" />
    <hkern u1="T" u2="&#xe5;" k="255" />
    <hkern u1="T" u2="&#xe4;" k="255" />
    <hkern u1="T" u2="&#xe3;" k="255" />
    <hkern u1="T" u2="&#xe2;" k="255" />
    <hkern u1="T" u2="&#xe1;" k="255" />
    <hkern u1="T" u2="&#xe0;" k="255" />
    <hkern u1="T" u2="&#xd8;" k="120" />
    <hkern u1="T" u2="&#xd6;" k="120" />
    <hkern u1="T" u2="&#xd5;" k="120" />
    <hkern u1="T" u2="&#xd4;" k="120" />
    <hkern u1="T" u2="&#xd3;" k="120" />
    <hkern u1="T" u2="&#xd2;" k="120" />
    <hkern u1="T" u2="&#xc7;" k="120" />
    <hkern u1="T" u2="&#xc6;" k="115" />
    <hkern u1="T" u2="&#xc5;" k="115" />
    <hkern u1="T" u2="&#xc4;" k="115" />
    <hkern u1="T" u2="&#xc3;" k="115" />
    <hkern u1="T" u2="&#xc2;" k="115" />
    <hkern u1="T" u2="&#xc1;" k="115" />
    <hkern u1="T" u2="&#xc0;" k="115" />
    <hkern u1="T" u2="&#xbb;" k="180" />
    <hkern u1="T" u2="&#xb7;" k="180" />
    <hkern u1="T" u2="&#xb5;" k="160" />
    <hkern u1="T" u2="&#xad;" k="180" />
    <hkern u1="T" u2="&#xab;" k="180" />
    <hkern u1="T" u2="z" k="120" />
    <hkern u1="T" u2="y" k="180" />
    <hkern u1="T" u2="x" k="153" />
    <hkern u1="T" u2="w" k="140" />
    <hkern u1="T" u2="v" k="180" />
    <hkern u1="T" u2="u" k="160" />
    <hkern u1="T" u2="s" k="188" />
    <hkern u1="T" u2="r" k="160" />
    <hkern u1="T" u2="q" k="215" />
    <hkern u1="T" u2="p" k="160" />
    <hkern u1="T" u2="o" k="215" />
    <hkern u1="T" u2="n" k="160" />
    <hkern u1="T" u2="m" k="160" />
    <hkern u1="T" u2="g" k="194" />
    <hkern u1="T" u2="e" k="215" />
    <hkern u1="T" u2="d" k="215" />
    <hkern u1="T" u2="c" k="215" />
    <hkern u1="T" u2="a" k="255" />
    <hkern u1="T" u2="Q" k="120" />
    <hkern u1="T" u2="O" k="120" />
    <hkern u1="T" u2="J" k="200" />
    <hkern u1="T" u2="G" k="120" />
    <hkern u1="T" u2="C" k="120" />
    <hkern u1="T" u2="A" k="115" />
    <hkern u1="T" u2="&#x40;" k="120" />
    <hkern u1="T" u2="&#x3b;" k="160" />
    <hkern u1="T" u2="&#x3a;" k="160" />
    <hkern u1="T" u2="&#x2f;" k="115" />
    <hkern u1="T" u2="&#x2e;" k="180" />
    <hkern u1="T" u2="&#x2d;" k="180" />
    <hkern u1="T" u2="&#x2c;" k="180" />
    <hkern u1="T" u2="&#x26;" k="115" />
    <hkern u1="U" u2="&#x2206;" k="58" />
    <hkern u1="U" u2="&#x201e;" k="50" />
    <hkern u1="U" u2="&#x201a;" k="50" />
    <hkern u1="U" u2="&#x104;" k="58" />
    <hkern u1="U" u2="&#xc6;" k="58" />
    <hkern u1="U" u2="&#xc5;" k="58" />
    <hkern u1="U" u2="&#xc4;" k="58" />
    <hkern u1="U" u2="&#xc3;" k="58" />
    <hkern u1="U" u2="&#xc2;" k="58" />
    <hkern u1="U" u2="&#xc1;" k="58" />
    <hkern u1="U" u2="&#xc0;" k="58" />
    <hkern u1="U" u2="A" k="58" />
    <hkern u1="U" u2="&#x2f;" k="58" />
    <hkern u1="U" u2="&#x2e;" k="50" />
    <hkern u1="U" u2="&#x2c;" k="50" />
    <hkern u1="U" u2="&#x26;" k="58" />
    <hkern u1="V" u2="&#x2206;" k="95" />
    <hkern u1="V" u2="&#x2122;" k="-55" />
    <hkern u1="V" u2="&#x203a;" k="105" />
    <hkern u1="V" u2="&#x2039;" k="105" />
    <hkern u1="V" u2="&#x2022;" k="105" />
    <hkern u1="V" u2="&#x201e;" k="185" />
    <hkern u1="V" u2="&#x201d;" k="-55" />
    <hkern u1="V" u2="&#x201c;" k="-55" />
    <hkern u1="V" u2="&#x201a;" k="185" />
    <hkern u1="V" u2="&#x2019;" k="-55" />
    <hkern u1="V" u2="&#x2018;" k="-55" />
    <hkern u1="V" u2="&#x2014;" k="105" />
    <hkern u1="V" u2="&#x2013;" k="105" />
    <hkern u1="V" u2="&#x153;" k="108" />
    <hkern u1="V" u2="&#x152;" k="45" />
    <hkern u1="V" u2="&#x144;" k="73" />
    <hkern u1="V" u2="&#x119;" k="108" />
    <hkern u1="V" u2="&#x107;" k="108" />
    <hkern u1="V" u2="&#x106;" k="45" />
    <hkern u1="V" u2="&#x105;" k="108" />
    <hkern u1="V" u2="&#x104;" k="95" />
    <hkern u1="V" u2="&#xfc;" k="73" />
    <hkern u1="V" u2="&#xfb;" k="73" />
    <hkern u1="V" u2="&#xfa;" k="73" />
    <hkern u1="V" u2="&#xf9;" k="73" />
    <hkern u1="V" u2="&#xf8;" k="108" />
    <hkern u1="V" u2="&#xf6;" k="108" />
    <hkern u1="V" u2="&#xf5;" k="108" />
    <hkern u1="V" u2="&#xf4;" k="108" />
    <hkern u1="V" u2="&#xf3;" k="108" />
    <hkern u1="V" u2="&#xf2;" k="108" />
    <hkern u1="V" u2="&#xf1;" k="73" />
    <hkern u1="V" u2="&#xf0;" k="108" />
    <hkern u1="V" u2="&#xeb;" k="108" />
    <hkern u1="V" u2="&#xea;" k="108" />
    <hkern u1="V" u2="&#xe9;" k="108" />
    <hkern u1="V" u2="&#xe8;" k="108" />
    <hkern u1="V" u2="&#xe7;" k="108" />
    <hkern u1="V" u2="&#xe6;" k="108" />
    <hkern u1="V" u2="&#xe5;" k="108" />
    <hkern u1="V" u2="&#xe4;" k="108" />
    <hkern u1="V" u2="&#xe3;" k="108" />
    <hkern u1="V" u2="&#xe2;" k="108" />
    <hkern u1="V" u2="&#xe1;" k="108" />
    <hkern u1="V" u2="&#xe0;" k="108" />
    <hkern u1="V" u2="&#xd8;" k="45" />
    <hkern u1="V" u2="&#xd6;" k="45" />
    <hkern u1="V" u2="&#xd5;" k="45" />
    <hkern u1="V" u2="&#xd4;" k="45" />
    <hkern u1="V" u2="&#xd3;" k="45" />
    <hkern u1="V" u2="&#xd2;" k="45" />
    <hkern u1="V" u2="&#xc7;" k="45" />
    <hkern u1="V" u2="&#xc6;" k="95" />
    <hkern u1="V" u2="&#xc5;" k="95" />
    <hkern u1="V" u2="&#xc4;" k="95" />
    <hkern u1="V" u2="&#xc3;" k="95" />
    <hkern u1="V" u2="&#xc2;" k="95" />
    <hkern u1="V" u2="&#xc1;" k="95" />
    <hkern u1="V" u2="&#xc0;" k="95" />
    <hkern u1="V" u2="&#xbb;" k="105" />
    <hkern u1="V" u2="&#xba;" k="-55" />
    <hkern u1="V" u2="&#xb9;" k="-70" />
    <hkern u1="V" u2="&#xb7;" k="105" />
    <hkern u1="V" u2="&#xb5;" k="73" />
    <hkern u1="V" u2="&#xb3;" k="-70" />
    <hkern u1="V" u2="&#xb2;" k="-70" />
    <hkern u1="V" u2="&#xb0;" k="-55" />
    <hkern u1="V" u2="&#xad;" k="105" />
    <hkern u1="V" u2="&#xab;" k="105" />
    <hkern u1="V" u2="&#xaa;" k="-55" />
    <hkern u1="V" u2="z" k="70" />
    <hkern u1="V" u2="y" k="33" />
    <hkern u1="V" u2="x" k="35" />
    <hkern u1="V" u2="v" k="33" />
    <hkern u1="V" u2="u" k="73" />
    <hkern u1="V" u2="t" k="35" />
    <hkern u1="V" u2="s" k="103" />
    <hkern u1="V" u2="r" k="73" />
    <hkern u1="V" u2="q" k="108" />
    <hkern u1="V" u2="p" k="73" />
    <hkern u1="V" u2="o" k="108" />
    <hkern u1="V" u2="n" k="73" />
    <hkern u1="V" u2="m" k="73" />
    <hkern u1="V" u2="g" k="133" />
    <hkern u1="V" u2="f" k="30" />
    <hkern u1="V" u2="e" k="108" />
    <hkern u1="V" u2="d" k="108" />
    <hkern u1="V" u2="c" k="108" />
    <hkern u1="V" u2="a" k="108" />
    <hkern u1="V" u2="Q" k="45" />
    <hkern u1="V" u2="O" k="45" />
    <hkern u1="V" u2="J" k="145" />
    <hkern u1="V" u2="G" k="45" />
    <hkern u1="V" u2="C" k="45" />
    <hkern u1="V" u2="A" k="95" />
    <hkern u1="V" u2="&#x40;" k="45" />
    <hkern u1="V" u2="&#x3f;" k="-60" />
    <hkern u1="V" u2="&#x3b;" k="73" />
    <hkern u1="V" u2="&#x3a;" k="73" />
    <hkern u1="V" u2="&#x2f;" k="95" />
    <hkern u1="V" u2="&#x2e;" k="185" />
    <hkern u1="V" u2="&#x2d;" k="105" />
    <hkern u1="V" u2="&#x2c;" k="185" />
    <hkern u1="V" u2="&#x2a;" k="-55" />
    <hkern u1="V" u2="&#x27;" k="-55" />
    <hkern u1="V" u2="&#x26;" k="95" />
    <hkern u1="V" u2="&#x22;" k="-55" />
    <hkern u1="W" u2="&#x2206;" k="65" />
    <hkern u1="W" u2="&#x2122;" k="-55" />
    <hkern u1="W" u2="&#x203a;" k="25" />
    <hkern u1="W" u2="&#x2039;" k="25" />
    <hkern u1="W" u2="&#x2022;" k="25" />
    <hkern u1="W" u2="&#x201e;" k="110" />
    <hkern u1="W" u2="&#x201d;" k="-55" />
    <hkern u1="W" u2="&#x201c;" k="-55" />
    <hkern u1="W" u2="&#x201a;" k="110" />
    <hkern u1="W" u2="&#x2019;" k="-55" />
    <hkern u1="W" u2="&#x2018;" k="-55" />
    <hkern u1="W" u2="&#x2014;" k="25" />
    <hkern u1="W" u2="&#x2013;" k="25" />
    <hkern u1="W" u2="&#x153;" k="20" />
    <hkern u1="W" u2="&#x119;" k="20" />
    <hkern u1="W" u2="&#x107;" k="20" />
    <hkern u1="W" u2="&#x105;" k="73" />
    <hkern u1="W" u2="&#x104;" k="65" />
    <hkern u1="W" u2="&#xf8;" k="20" />
    <hkern u1="W" u2="&#xf6;" k="20" />
    <hkern u1="W" u2="&#xf5;" k="20" />
    <hkern u1="W" u2="&#xf4;" k="20" />
    <hkern u1="W" u2="&#xf3;" k="20" />
    <hkern u1="W" u2="&#xf2;" k="20" />
    <hkern u1="W" u2="&#xf0;" k="20" />
    <hkern u1="W" u2="&#xeb;" k="20" />
    <hkern u1="W" u2="&#xea;" k="20" />
    <hkern u1="W" u2="&#xe9;" k="20" />
    <hkern u1="W" u2="&#xe8;" k="20" />
    <hkern u1="W" u2="&#xe7;" k="20" />
    <hkern u1="W" u2="&#xe6;" k="73" />
    <hkern u1="W" u2="&#xe5;" k="73" />
    <hkern u1="W" u2="&#xe4;" k="73" />
    <hkern u1="W" u2="&#xe3;" k="73" />
    <hkern u1="W" u2="&#xe2;" k="73" />
    <hkern u1="W" u2="&#xe1;" k="73" />
    <hkern u1="W" u2="&#xe0;" k="73" />
    <hkern u1="W" u2="&#xc6;" k="65" />
    <hkern u1="W" u2="&#xc5;" k="65" />
    <hkern u1="W" u2="&#xc4;" k="65" />
    <hkern u1="W" u2="&#xc3;" k="65" />
    <hkern u1="W" u2="&#xc2;" k="65" />
    <hkern u1="W" u2="&#xc1;" k="65" />
    <hkern u1="W" u2="&#xc0;" k="65" />
    <hkern u1="W" u2="&#xbb;" k="25" />
    <hkern u1="W" u2="&#xba;" k="-55" />
    <hkern u1="W" u2="&#xb9;" k="-55" />
    <hkern u1="W" u2="&#xb7;" k="25" />
    <hkern u1="W" u2="&#xb3;" k="-55" />
    <hkern u1="W" u2="&#xb2;" k="-55" />
    <hkern u1="W" u2="&#xb0;" k="-55" />
    <hkern u1="W" u2="&#xad;" k="25" />
    <hkern u1="W" u2="&#xab;" k="25" />
    <hkern u1="W" u2="&#xaa;" k="-55" />
    <hkern u1="W" u2="s" k="38" />
    <hkern u1="W" u2="q" k="20" />
    <hkern u1="W" u2="o" k="20" />
    <hkern u1="W" u2="g" k="99" />
    <hkern u1="W" u2="e" k="20" />
    <hkern u1="W" u2="d" k="20" />
    <hkern u1="W" u2="c" k="20" />
    <hkern u1="W" u2="a" k="73" />
    <hkern u1="W" u2="J" k="90" />
    <hkern u1="W" u2="A" k="65" />
    <hkern u1="W" u2="&#x3f;" k="-37" />
    <hkern u1="W" u2="&#x2f;" k="65" />
    <hkern u1="W" u2="&#x2e;" k="110" />
    <hkern u1="W" u2="&#x2d;" k="25" />
    <hkern u1="W" u2="&#x2c;" k="110" />
    <hkern u1="W" u2="&#x2a;" k="-55" />
    <hkern u1="W" u2="&#x27;" k="-55" />
    <hkern u1="W" u2="&#x26;" k="65" />
    <hkern u1="W" u2="&#x22;" k="-55" />
    <hkern u1="X" u2="&#x203a;" k="55" />
    <hkern u1="X" u2="&#x2039;" k="55" />
    <hkern u1="X" u2="&#x2022;" k="55" />
    <hkern u1="X" u2="&#x2014;" k="55" />
    <hkern u1="X" u2="&#x2013;" k="55" />
    <hkern u1="X" u2="&#x153;" k="28" />
    <hkern u1="X" u2="&#x152;" k="30" />
    <hkern u1="X" u2="&#x119;" k="28" />
    <hkern u1="X" u2="&#x107;" k="28" />
    <hkern u1="X" u2="&#x106;" k="30" />
    <hkern u1="X" u2="&#xf8;" k="28" />
    <hkern u1="X" u2="&#xf6;" k="28" />
    <hkern u1="X" u2="&#xf5;" k="28" />
    <hkern u1="X" u2="&#xf4;" k="28" />
    <hkern u1="X" u2="&#xf3;" k="28" />
    <hkern u1="X" u2="&#xf2;" k="28" />
    <hkern u1="X" u2="&#xf0;" k="28" />
    <hkern u1="X" u2="&#xeb;" k="28" />
    <hkern u1="X" u2="&#xea;" k="28" />
    <hkern u1="X" u2="&#xe9;" k="28" />
    <hkern u1="X" u2="&#xe8;" k="28" />
    <hkern u1="X" u2="&#xe7;" k="28" />
    <hkern u1="X" u2="&#xd8;" k="30" />
    <hkern u1="X" u2="&#xd6;" k="30" />
    <hkern u1="X" u2="&#xd5;" k="30" />
    <hkern u1="X" u2="&#xd4;" k="30" />
    <hkern u1="X" u2="&#xd3;" k="30" />
    <hkern u1="X" u2="&#xd2;" k="30" />
    <hkern u1="X" u2="&#xc7;" k="30" />
    <hkern u1="X" u2="&#xbb;" k="55" />
    <hkern u1="X" u2="&#xb7;" k="55" />
    <hkern u1="X" u2="&#xad;" k="55" />
    <hkern u1="X" u2="&#xab;" k="55" />
    <hkern u1="X" u2="y" k="58" />
    <hkern u1="X" u2="w" k="58" />
    <hkern u1="X" u2="v" k="58" />
    <hkern u1="X" u2="t" k="70" />
    <hkern u1="X" u2="q" k="28" />
    <hkern u1="X" u2="o" k="28" />
    <hkern u1="X" u2="f" k="45" />
    <hkern u1="X" u2="e" k="28" />
    <hkern u1="X" u2="d" k="28" />
    <hkern u1="X" u2="c" k="28" />
    <hkern u1="X" u2="Q" k="30" />
    <hkern u1="X" u2="O" k="30" />
    <hkern u1="X" u2="G" k="30" />
    <hkern u1="X" u2="C" k="30" />
    <hkern u1="X" u2="&#x40;" k="30" />
    <hkern u1="X" u2="&#x2d;" k="55" />
    <hkern u1="Y" u2="&#x2206;" k="140" />
    <hkern u1="Y" u2="&#x2122;" k="-25" />
    <hkern u1="Y" u2="&#x203a;" k="160" />
    <hkern u1="Y" u2="&#x2039;" k="160" />
    <hkern u1="Y" u2="&#x2022;" k="160" />
    <hkern u1="Y" u2="&#x201e;" k="135" />
    <hkern u1="Y" u2="&#x201d;" k="-25" />
    <hkern u1="Y" u2="&#x201c;" k="-25" />
    <hkern u1="Y" u2="&#x201a;" k="135" />
    <hkern u1="Y" u2="&#x2019;" k="-25" />
    <hkern u1="Y" u2="&#x2018;" k="-25" />
    <hkern u1="Y" u2="&#x2014;" k="160" />
    <hkern u1="Y" u2="&#x2013;" k="160" />
    <hkern u1="Y" u2="&#x153;" k="160" />
    <hkern u1="Y" u2="&#x152;" k="80" />
    <hkern u1="Y" u2="&#x144;" k="110" />
    <hkern u1="Y" u2="&#x119;" k="160" />
    <hkern u1="Y" u2="&#x107;" k="160" />
    <hkern u1="Y" u2="&#x106;" k="80" />
    <hkern u1="Y" u2="&#x105;" k="108" />
    <hkern u1="Y" u2="&#x104;" k="140" />
    <hkern u1="Y" u2="&#xfc;" k="110" />
    <hkern u1="Y" u2="&#xfb;" k="110" />
    <hkern u1="Y" u2="&#xfa;" k="110" />
    <hkern u1="Y" u2="&#xf9;" k="110" />
    <hkern u1="Y" u2="&#xf8;" k="160" />
    <hkern u1="Y" u2="&#xf6;" k="160" />
    <hkern u1="Y" u2="&#xf5;" k="160" />
    <hkern u1="Y" u2="&#xf4;" k="160" />
    <hkern u1="Y" u2="&#xf3;" k="160" />
    <hkern u1="Y" u2="&#xf2;" k="160" />
    <hkern u1="Y" u2="&#xf1;" k="110" />
    <hkern u1="Y" u2="&#xf0;" k="160" />
    <hkern u1="Y" u2="&#xeb;" k="160" />
    <hkern u1="Y" u2="&#xea;" k="160" />
    <hkern u1="Y" u2="&#xe9;" k="160" />
    <hkern u1="Y" u2="&#xe8;" k="160" />
    <hkern u1="Y" u2="&#xe7;" k="160" />
    <hkern u1="Y" u2="&#xe6;" k="108" />
    <hkern u1="Y" u2="&#xe5;" k="108" />
    <hkern u1="Y" u2="&#xe4;" k="108" />
    <hkern u1="Y" u2="&#xe3;" k="108" />
    <hkern u1="Y" u2="&#xe2;" k="108" />
    <hkern u1="Y" u2="&#xe1;" k="108" />
    <hkern u1="Y" u2="&#xe0;" k="108" />
    <hkern u1="Y" u2="&#xd8;" k="80" />
    <hkern u1="Y" u2="&#xd6;" k="80" />
    <hkern u1="Y" u2="&#xd5;" k="80" />
    <hkern u1="Y" u2="&#xd4;" k="80" />
    <hkern u1="Y" u2="&#xd3;" k="80" />
    <hkern u1="Y" u2="&#xd2;" k="80" />
    <hkern u1="Y" u2="&#xc7;" k="80" />
    <hkern u1="Y" u2="&#xc6;" k="140" />
    <hkern u1="Y" u2="&#xc5;" k="140" />
    <hkern u1="Y" u2="&#xc4;" k="140" />
    <hkern u1="Y" u2="&#xc3;" k="140" />
    <hkern u1="Y" u2="&#xc2;" k="140" />
    <hkern u1="Y" u2="&#xc1;" k="140" />
    <hkern u1="Y" u2="&#xc0;" k="140" />
    <hkern u1="Y" u2="&#xbb;" k="160" />
    <hkern u1="Y" u2="&#xba;" k="-25" />
    <hkern u1="Y" u2="&#xb9;" k="-45" />
    <hkern u1="Y" u2="&#xb7;" k="160" />
    <hkern u1="Y" u2="&#xb5;" k="110" />
    <hkern u1="Y" u2="&#xb3;" k="-45" />
    <hkern u1="Y" u2="&#xb2;" k="-45" />
    <hkern u1="Y" u2="&#xb0;" k="-25" />
    <hkern u1="Y" u2="&#xad;" k="160" />
    <hkern u1="Y" u2="&#xab;" k="160" />
    <hkern u1="Y" u2="&#xaa;" k="-25" />
    <hkern u1="Y" u2="y" k="100" />
    <hkern u1="Y" u2="x" k="125" />
    <hkern u1="Y" u2="w" k="85" />
    <hkern u1="Y" u2="v" k="100" />
    <hkern u1="Y" u2="u" k="110" />
    <hkern u1="Y" u2="s" k="113" />
    <hkern u1="Y" u2="r" k="110" />
    <hkern u1="Y" u2="q" k="160" />
    <hkern u1="Y" u2="p" k="110" />
    <hkern u1="Y" u2="o" k="160" />
    <hkern u1="Y" u2="n" k="110" />
    <hkern u1="Y" u2="m" k="110" />
    <hkern u1="Y" u2="g" k="165" />
    <hkern u1="Y" u2="e" k="160" />
    <hkern u1="Y" u2="d" k="160" />
    <hkern u1="Y" u2="c" k="160" />
    <hkern u1="Y" u2="a" k="108" />
    <hkern u1="Y" u2="Q" k="80" />
    <hkern u1="Y" u2="O" k="80" />
    <hkern u1="Y" u2="J" k="200" />
    <hkern u1="Y" u2="G" k="80" />
    <hkern u1="Y" u2="C" k="80" />
    <hkern u1="Y" u2="A" k="140" />
    <hkern u1="Y" u2="&#x40;" k="80" />
    <hkern u1="Y" u2="&#x3f;" k="-37" />
    <hkern u1="Y" u2="&#x3b;" k="110" />
    <hkern u1="Y" u2="&#x3a;" k="110" />
    <hkern u1="Y" u2="&#x2f;" k="140" />
    <hkern u1="Y" u2="&#x2e;" k="135" />
    <hkern u1="Y" u2="&#x2d;" k="160" />
    <hkern u1="Y" u2="&#x2c;" k="135" />
    <hkern u1="Y" u2="&#x2a;" k="-25" />
    <hkern u1="Y" u2="&#x27;" k="-25" />
    <hkern u1="Y" u2="&#x26;" k="140" />
    <hkern u1="Y" u2="&#x22;" k="-25" />
    <hkern u1="Z" u2="&#x203a;" k="75" />
    <hkern u1="Z" u2="&#x2039;" k="75" />
    <hkern u1="Z" u2="&#x2022;" k="75" />
    <hkern u1="Z" u2="&#x2014;" k="75" />
    <hkern u1="Z" u2="&#x2013;" k="75" />
    <hkern u1="Z" u2="&#x153;" k="50" />
    <hkern u1="Z" u2="&#x152;" k="70" />
    <hkern u1="Z" u2="&#x119;" k="50" />
    <hkern u1="Z" u2="&#x107;" k="50" />
    <hkern u1="Z" u2="&#x106;" k="70" />
    <hkern u1="Z" u2="&#xf8;" k="50" />
    <hkern u1="Z" u2="&#xf6;" k="50" />
    <hkern u1="Z" u2="&#xf5;" k="50" />
    <hkern u1="Z" u2="&#xf4;" k="50" />
    <hkern u1="Z" u2="&#xf3;" k="50" />
    <hkern u1="Z" u2="&#xf2;" k="50" />
    <hkern u1="Z" u2="&#xf0;" k="50" />
    <hkern u1="Z" u2="&#xeb;" k="50" />
    <hkern u1="Z" u2="&#xea;" k="50" />
    <hkern u1="Z" u2="&#xe9;" k="50" />
    <hkern u1="Z" u2="&#xe8;" k="50" />
    <hkern u1="Z" u2="&#xe7;" k="50" />
    <hkern u1="Z" u2="&#xd8;" k="70" />
    <hkern u1="Z" u2="&#xd6;" k="70" />
    <hkern u1="Z" u2="&#xd5;" k="70" />
    <hkern u1="Z" u2="&#xd4;" k="70" />
    <hkern u1="Z" u2="&#xd3;" k="70" />
    <hkern u1="Z" u2="&#xd2;" k="70" />
    <hkern u1="Z" u2="&#xc7;" k="70" />
    <hkern u1="Z" u2="&#xbb;" k="75" />
    <hkern u1="Z" u2="&#xb7;" k="75" />
    <hkern u1="Z" u2="&#xad;" k="75" />
    <hkern u1="Z" u2="&#xab;" k="75" />
    <hkern u1="Z" u2="y" k="45" />
    <hkern u1="Z" u2="v" k="45" />
    <hkern u1="Z" u2="s" k="40" />
    <hkern u1="Z" u2="q" k="50" />
    <hkern u1="Z" u2="o" k="50" />
    <hkern u1="Z" u2="e" k="50" />
    <hkern u1="Z" u2="d" k="50" />
    <hkern u1="Z" u2="c" k="50" />
    <hkern u1="Z" u2="Q" k="70" />
    <hkern u1="Z" u2="O" k="70" />
    <hkern u1="Z" u2="G" k="70" />
    <hkern u1="Z" u2="C" k="70" />
    <hkern u1="Z" u2="&#x40;" k="70" />
    <hkern u1="Z" u2="&#x3f;" k="-37" />
    <hkern u1="Z" u2="&#x2d;" k="75" />
    <hkern u1="[" u2="&#x153;" k="25" />
    <hkern u1="[" u2="&#x152;" k="40" />
    <hkern u1="[" u2="&#x119;" k="25" />
    <hkern u1="[" u2="&#x107;" k="25" />
    <hkern u1="[" u2="&#x106;" k="40" />
    <hkern u1="[" u2="&#xf8;" k="25" />
    <hkern u1="[" u2="&#xf6;" k="25" />
    <hkern u1="[" u2="&#xf5;" k="25" />
    <hkern u1="[" u2="&#xf4;" k="25" />
    <hkern u1="[" u2="&#xf3;" k="25" />
    <hkern u1="[" u2="&#xf2;" k="25" />
    <hkern u1="[" u2="&#xf0;" k="25" />
    <hkern u1="[" u2="&#xeb;" k="25" />
    <hkern u1="[" u2="&#xea;" k="25" />
    <hkern u1="[" u2="&#xe9;" k="25" />
    <hkern u1="[" u2="&#xe8;" k="25" />
    <hkern u1="[" u2="&#xe7;" k="25" />
    <hkern u1="[" u2="&#xd8;" k="40" />
    <hkern u1="[" u2="&#xd6;" k="40" />
    <hkern u1="[" u2="&#xd5;" k="40" />
    <hkern u1="[" u2="&#xd4;" k="40" />
    <hkern u1="[" u2="&#xd3;" k="40" />
    <hkern u1="[" u2="&#xd2;" k="40" />
    <hkern u1="[" u2="&#xc7;" k="40" />
    <hkern u1="[" u2="q" k="25" />
    <hkern u1="[" u2="o" k="25" />
    <hkern u1="[" u2="e" k="25" />
    <hkern u1="[" u2="d" k="25" />
    <hkern u1="[" u2="c" k="25" />
    <hkern u1="[" u2="Q" k="40" />
    <hkern u1="[" u2="O" k="40" />
    <hkern u1="[" u2="G" k="40" />
    <hkern u1="[" u2="C" k="40" />
    <hkern u1="[" u2="&#x40;" k="40" />
    <hkern u1="\" u2="&#x2122;" k="170" />
    <hkern u1="\" u2="&#x203a;" k="35" />
    <hkern u1="\" u2="&#x2039;" k="35" />
    <hkern u1="\" u2="&#x2022;" k="35" />
    <hkern u1="\" u2="&#x201d;" k="170" />
    <hkern u1="\" u2="&#x201c;" k="170" />
    <hkern u1="\" u2="&#x2019;" k="170" />
    <hkern u1="\" u2="&#x2018;" k="170" />
    <hkern u1="\" u2="&#x2014;" k="35" />
    <hkern u1="\" u2="&#x2013;" k="35" />
    <hkern u1="\" u2="&#x178;" k="140" />
    <hkern u1="\" u2="&#x152;" k="30" />
    <hkern u1="\" u2="&#x106;" k="30" />
    <hkern u1="\" u2="&#xdd;" k="140" />
    <hkern u1="\" u2="&#xdc;" k="58" />
    <hkern u1="\" u2="&#xdb;" k="58" />
    <hkern u1="\" u2="&#xda;" k="58" />
    <hkern u1="\" u2="&#xd9;" k="58" />
    <hkern u1="\" u2="&#xd8;" k="30" />
    <hkern u1="\" u2="&#xd6;" k="30" />
    <hkern u1="\" u2="&#xd5;" k="30" />
    <hkern u1="\" u2="&#xd4;" k="30" />
    <hkern u1="\" u2="&#xd3;" k="30" />
    <hkern u1="\" u2="&#xd2;" k="30" />
    <hkern u1="\" u2="&#xc7;" k="30" />
    <hkern u1="\" u2="&#xbb;" k="35" />
    <hkern u1="\" u2="&#xba;" k="170" />
    <hkern u1="\" u2="&#xb9;" k="160" />
    <hkern u1="\" u2="&#xb7;" k="35" />
    <hkern u1="\" u2="&#xb3;" k="160" />
    <hkern u1="\" u2="&#xb2;" k="160" />
    <hkern u1="\" u2="&#xb0;" k="170" />
    <hkern u1="\" u2="&#xad;" k="35" />
    <hkern u1="\" u2="&#xab;" k="35" />
    <hkern u1="\" u2="&#xaa;" k="170" />
    <hkern u1="\" u2="y" k="70" />
    <hkern u1="\" u2="v" k="70" />
    <hkern u1="\" u2="\" k="95" />
    <hkern u1="\" u2="Y" k="140" />
    <hkern u1="\" u2="W" k="60" />
    <hkern u1="\" u2="V" k="95" />
    <hkern u1="\" u2="U" k="58" />
    <hkern u1="\" u2="T" k="115" />
    <hkern u1="\" u2="Q" k="30" />
    <hkern u1="\" u2="O" k="30" />
    <hkern u1="\" u2="J" k="-45" />
    <hkern u1="\" u2="G" k="30" />
    <hkern u1="\" u2="C" k="30" />
    <hkern u1="\" u2="&#x40;" k="30" />
    <hkern u1="\" u2="&#x3f;" k="48" />
    <hkern u1="\" u2="&#x2d;" k="35" />
    <hkern u1="\" u2="&#x2a;" k="170" />
    <hkern u1="\" u2="&#x27;" k="170" />
    <hkern u1="\" u2="&#x22;" k="170" />
    <hkern u1="a" u2="&#x2122;" k="65" />
    <hkern u1="a" u2="&#x201d;" k="65" />
    <hkern u1="a" u2="&#x201c;" k="65" />
    <hkern u1="a" u2="&#x2019;" k="65" />
    <hkern u1="a" u2="&#x2018;" k="65" />
    <hkern u1="a" u2="&#xba;" k="65" />
    <hkern u1="a" u2="&#xb9;" k="65" />
    <hkern u1="a" u2="&#xb3;" k="65" />
    <hkern u1="a" u2="&#xb2;" k="65" />
    <hkern u1="a" u2="&#xb0;" k="65" />
    <hkern u1="a" u2="&#xaa;" k="65" />
    <hkern u1="a" u2="y" k="25" />
    <hkern u1="a" u2="w" k="13" />
    <hkern u1="a" u2="v" k="25" />
    <hkern u1="a" u2="&#x2a;" k="65" />
    <hkern u1="a" u2="&#x27;" k="65" />
    <hkern u1="a" u2="&#x22;" k="65" />
    <hkern u1="b" u2="&#x2122;" k="85" />
    <hkern u1="b" u2="&#x201d;" k="85" />
    <hkern u1="b" u2="&#x201c;" k="85" />
    <hkern u1="b" u2="&#x2019;" k="85" />
    <hkern u1="b" u2="&#x2018;" k="85" />
    <hkern u1="b" u2="&#xba;" k="85" />
    <hkern u1="b" u2="&#xb0;" k="85" />
    <hkern u1="b" u2="&#xaa;" k="85" />
    <hkern u1="b" u2="&#x7d;" k="25" />
    <hkern u1="b" u2="y" k="18" />
    <hkern u1="b" u2="x" k="60" />
    <hkern u1="b" u2="v" k="18" />
    <hkern u1="b" u2="]" k="25" />
    <hkern u1="b" u2="\" k="108" />
    <hkern u1="b" u2="W" k="20" />
    <hkern u1="b" u2="V" k="108" />
    <hkern u1="b" u2="&#x2a;" k="85" />
    <hkern u1="b" u2="&#x29;" k="25" />
    <hkern u1="b" u2="&#x27;" k="85" />
    <hkern u1="b" u2="&#x22;" k="85" />
    <hkern u1="e" u2="&#x2122;" k="85" />
    <hkern u1="e" u2="&#x201d;" k="85" />
    <hkern u1="e" u2="&#x201c;" k="85" />
    <hkern u1="e" u2="&#x2019;" k="85" />
    <hkern u1="e" u2="&#x2018;" k="85" />
    <hkern u1="e" u2="&#xba;" k="85" />
    <hkern u1="e" u2="&#xb0;" k="85" />
    <hkern u1="e" u2="&#xaa;" k="85" />
    <hkern u1="e" u2="&#x7d;" k="25" />
    <hkern u1="e" u2="y" k="18" />
    <hkern u1="e" u2="x" k="60" />
    <hkern u1="e" u2="v" k="18" />
    <hkern u1="e" u2="]" k="25" />
    <hkern u1="e" u2="\" k="108" />
    <hkern u1="e" u2="W" k="20" />
    <hkern u1="e" u2="V" k="108" />
    <hkern u1="e" u2="&#x2a;" k="85" />
    <hkern u1="e" u2="&#x29;" k="25" />
    <hkern u1="e" u2="&#x27;" k="85" />
    <hkern u1="e" u2="&#x22;" k="85" />
    <hkern u1="f" u2="&#x2122;" k="-75" />
    <hkern u1="f" u2="&#x201e;" k="135" />
    <hkern u1="f" u2="&#x201d;" k="-75" />
    <hkern u1="f" u2="&#x201c;" k="-75" />
    <hkern u1="f" u2="&#x201a;" k="135" />
    <hkern u1="f" u2="&#x2019;" k="-75" />
    <hkern u1="f" u2="&#x2018;" k="-75" />
    <hkern u1="f" u2="&#xba;" k="-75" />
    <hkern u1="f" u2="&#xb9;" k="-100" />
    <hkern u1="f" u2="&#xb3;" k="-100" />
    <hkern u1="f" u2="&#xb2;" k="-100" />
    <hkern u1="f" u2="&#xb0;" k="-75" />
    <hkern u1="f" u2="&#xaa;" k="-75" />
    <hkern u1="f" u2="&#x2e;" k="135" />
    <hkern u1="f" u2="&#x2c;" k="135" />
    <hkern u1="f" u2="&#x2a;" k="-75" />
    <hkern u1="f" u2="&#x27;" k="-75" />
    <hkern u1="f" u2="&#x22;" k="-75" />
    <hkern u1="h" u2="&#x2122;" k="65" />
    <hkern u1="h" u2="&#x201d;" k="65" />
    <hkern u1="h" u2="&#x201c;" k="65" />
    <hkern u1="h" u2="&#x2019;" k="65" />
    <hkern u1="h" u2="&#x2018;" k="65" />
    <hkern u1="h" u2="&#xba;" k="65" />
    <hkern u1="h" u2="&#xb9;" k="65" />
    <hkern u1="h" u2="&#xb3;" k="65" />
    <hkern u1="h" u2="&#xb2;" k="65" />
    <hkern u1="h" u2="&#xb0;" k="65" />
    <hkern u1="h" u2="&#xaa;" k="65" />
    <hkern u1="h" u2="y" k="25" />
    <hkern u1="h" u2="w" k="13" />
    <hkern u1="h" u2="v" k="25" />
    <hkern u1="h" u2="&#x2a;" k="65" />
    <hkern u1="h" u2="&#x27;" k="65" />
    <hkern u1="h" u2="&#x22;" k="65" />
    <hkern u1="k" u2="&#x153;" k="60" />
    <hkern u1="k" u2="&#x119;" k="60" />
    <hkern u1="k" u2="&#x107;" k="60" />
    <hkern u1="k" u2="&#xf8;" k="60" />
    <hkern u1="k" u2="&#xf6;" k="60" />
    <hkern u1="k" u2="&#xf5;" k="60" />
    <hkern u1="k" u2="&#xf4;" k="60" />
    <hkern u1="k" u2="&#xf3;" k="60" />
    <hkern u1="k" u2="&#xf2;" k="60" />
    <hkern u1="k" u2="&#xf0;" k="60" />
    <hkern u1="k" u2="&#xeb;" k="60" />
    <hkern u1="k" u2="&#xea;" k="60" />
    <hkern u1="k" u2="&#xe9;" k="60" />
    <hkern u1="k" u2="&#xe8;" k="60" />
    <hkern u1="k" u2="&#xe7;" k="60" />
    <hkern u1="k" u2="q" k="60" />
    <hkern u1="k" u2="o" k="60" />
    <hkern u1="k" u2="e" k="60" />
    <hkern u1="k" u2="d" k="60" />
    <hkern u1="k" u2="c" k="60" />
    <hkern u1="m" u2="&#x2122;" k="65" />
    <hkern u1="m" u2="&#x201d;" k="65" />
    <hkern u1="m" u2="&#x201c;" k="65" />
    <hkern u1="m" u2="&#x2019;" k="65" />
    <hkern u1="m" u2="&#x2018;" k="65" />
    <hkern u1="m" u2="&#xba;" k="65" />
    <hkern u1="m" u2="&#xb9;" k="65" />
    <hkern u1="m" u2="&#xb3;" k="65" />
    <hkern u1="m" u2="&#xb2;" k="65" />
    <hkern u1="m" u2="&#xb0;" k="65" />
    <hkern u1="m" u2="&#xaa;" k="65" />
    <hkern u1="m" u2="y" k="25" />
    <hkern u1="m" u2="w" k="13" />
    <hkern u1="m" u2="v" k="25" />
    <hkern u1="m" u2="&#x2a;" k="65" />
    <hkern u1="m" u2="&#x27;" k="65" />
    <hkern u1="m" u2="&#x22;" k="65" />
    <hkern u1="n" u2="&#x2122;" k="65" />
    <hkern u1="n" u2="&#x201d;" k="65" />
    <hkern u1="n" u2="&#x201c;" k="65" />
    <hkern u1="n" u2="&#x2019;" k="65" />
    <hkern u1="n" u2="&#x2018;" k="65" />
    <hkern u1="n" u2="&#xba;" k="65" />
    <hkern u1="n" u2="&#xb9;" k="65" />
    <hkern u1="n" u2="&#xb3;" k="65" />
    <hkern u1="n" u2="&#xb2;" k="65" />
    <hkern u1="n" u2="&#xb0;" k="65" />
    <hkern u1="n" u2="&#xaa;" k="65" />
    <hkern u1="n" u2="y" k="25" />
    <hkern u1="n" u2="w" k="13" />
    <hkern u1="n" u2="v" k="25" />
    <hkern u1="n" u2="&#x2a;" k="65" />
    <hkern u1="n" u2="&#x27;" k="65" />
    <hkern u1="n" u2="&#x22;" k="65" />
    <hkern u1="o" u2="&#x2122;" k="85" />
    <hkern u1="o" u2="&#x201d;" k="85" />
    <hkern u1="o" u2="&#x201c;" k="85" />
    <hkern u1="o" u2="&#x2019;" k="85" />
    <hkern u1="o" u2="&#x2018;" k="85" />
    <hkern u1="o" u2="&#xba;" k="85" />
    <hkern u1="o" u2="&#xb0;" k="85" />
    <hkern u1="o" u2="&#xaa;" k="85" />
    <hkern u1="o" u2="&#x7d;" k="25" />
    <hkern u1="o" u2="y" k="18" />
    <hkern u1="o" u2="x" k="60" />
    <hkern u1="o" u2="v" k="18" />
    <hkern u1="o" u2="]" k="25" />
    <hkern u1="o" u2="\" k="108" />
    <hkern u1="o" u2="W" k="20" />
    <hkern u1="o" u2="V" k="108" />
    <hkern u1="o" u2="&#x2a;" k="85" />
    <hkern u1="o" u2="&#x29;" k="25" />
    <hkern u1="o" u2="&#x27;" k="85" />
    <hkern u1="o" u2="&#x22;" k="85" />
    <hkern u1="p" u2="&#x2122;" k="85" />
    <hkern u1="p" u2="&#x201d;" k="85" />
    <hkern u1="p" u2="&#x201c;" k="85" />
    <hkern u1="p" u2="&#x2019;" k="85" />
    <hkern u1="p" u2="&#x2018;" k="85" />
    <hkern u1="p" u2="&#xba;" k="85" />
    <hkern u1="p" u2="&#xb0;" k="85" />
    <hkern u1="p" u2="&#xaa;" k="85" />
    <hkern u1="p" u2="&#x7d;" k="25" />
    <hkern u1="p" u2="y" k="18" />
    <hkern u1="p" u2="x" k="60" />
    <hkern u1="p" u2="v" k="18" />
    <hkern u1="p" u2="]" k="25" />
    <hkern u1="p" u2="\" k="108" />
    <hkern u1="p" u2="W" k="20" />
    <hkern u1="p" u2="V" k="108" />
    <hkern u1="p" u2="&#x2a;" k="85" />
    <hkern u1="p" u2="&#x29;" k="25" />
    <hkern u1="p" u2="&#x27;" k="85" />
    <hkern u1="p" u2="&#x22;" k="85" />
    <hkern u1="r" u2="&#x201e;" k="125" />
    <hkern u1="r" u2="&#x201a;" k="125" />
    <hkern u1="r" u2="&#x105;" k="50" />
    <hkern u1="r" u2="&#xe6;" k="50" />
    <hkern u1="r" u2="&#xe5;" k="50" />
    <hkern u1="r" u2="&#xe4;" k="50" />
    <hkern u1="r" u2="&#xe3;" k="50" />
    <hkern u1="r" u2="&#xe2;" k="50" />
    <hkern u1="r" u2="&#xe1;" k="50" />
    <hkern u1="r" u2="&#xe0;" k="50" />
    <hkern u1="r" u2="a" k="50" />
    <hkern u1="r" u2="&#x2e;" k="125" />
    <hkern u1="r" u2="&#x2c;" k="125" />
    <hkern u1="v" u2="&#x2206;" k="70" />
    <hkern u1="v" u2="&#x201e;" k="125" />
    <hkern u1="v" u2="&#x201a;" k="125" />
    <hkern u1="v" u2="&#x153;" k="18" />
    <hkern u1="v" u2="&#x119;" k="18" />
    <hkern u1="v" u2="&#x107;" k="18" />
    <hkern u1="v" u2="&#x104;" k="70" />
    <hkern u1="v" u2="&#xf8;" k="18" />
    <hkern u1="v" u2="&#xf6;" k="18" />
    <hkern u1="v" u2="&#xf5;" k="18" />
    <hkern u1="v" u2="&#xf4;" k="18" />
    <hkern u1="v" u2="&#xf3;" k="18" />
    <hkern u1="v" u2="&#xf2;" k="18" />
    <hkern u1="v" u2="&#xf0;" k="18" />
    <hkern u1="v" u2="&#xeb;" k="18" />
    <hkern u1="v" u2="&#xea;" k="18" />
    <hkern u1="v" u2="&#xe9;" k="18" />
    <hkern u1="v" u2="&#xe8;" k="18" />
    <hkern u1="v" u2="&#xe7;" k="18" />
    <hkern u1="v" u2="&#xc6;" k="70" />
    <hkern u1="v" u2="&#xc5;" k="70" />
    <hkern u1="v" u2="&#xc4;" k="70" />
    <hkern u1="v" u2="&#xc3;" k="70" />
    <hkern u1="v" u2="&#xc2;" k="70" />
    <hkern u1="v" u2="&#xc1;" k="70" />
    <hkern u1="v" u2="&#xc0;" k="70" />
    <hkern u1="v" u2="q" k="18" />
    <hkern u1="v" u2="o" k="18" />
    <hkern u1="v" u2="e" k="18" />
    <hkern u1="v" u2="d" k="18" />
    <hkern u1="v" u2="c" k="18" />
    <hkern u1="v" u2="A" k="70" />
    <hkern u1="v" u2="&#x2f;" k="70" />
    <hkern u1="v" u2="&#x2e;" k="125" />
    <hkern u1="v" u2="&#x2c;" k="125" />
    <hkern u1="v" u2="&#x26;" k="70" />
    <hkern u1="w" u2="&#x201e;" k="50" />
    <hkern u1="w" u2="&#x201a;" k="50" />
    <hkern u1="w" u2="&#x2e;" k="50" />
    <hkern u1="w" u2="&#x2c;" k="50" />
    <hkern u1="x" u2="&#x153;" k="60" />
    <hkern u1="x" u2="&#x119;" k="60" />
    <hkern u1="x" u2="&#x107;" k="60" />
    <hkern u1="x" u2="&#xf8;" k="60" />
    <hkern u1="x" u2="&#xf6;" k="60" />
    <hkern u1="x" u2="&#xf5;" k="60" />
    <hkern u1="x" u2="&#xf4;" k="60" />
    <hkern u1="x" u2="&#xf3;" k="60" />
    <hkern u1="x" u2="&#xf2;" k="60" />
    <hkern u1="x" u2="&#xf0;" k="60" />
    <hkern u1="x" u2="&#xeb;" k="60" />
    <hkern u1="x" u2="&#xea;" k="60" />
    <hkern u1="x" u2="&#xe9;" k="60" />
    <hkern u1="x" u2="&#xe8;" k="60" />
    <hkern u1="x" u2="&#xe7;" k="60" />
    <hkern u1="x" u2="q" k="60" />
    <hkern u1="x" u2="o" k="60" />
    <hkern u1="x" u2="e" k="60" />
    <hkern u1="x" u2="d" k="60" />
    <hkern u1="x" u2="c" k="60" />
    <hkern u1="y" u2="&#x2206;" k="70" />
    <hkern u1="y" u2="&#x201e;" k="125" />
    <hkern u1="y" u2="&#x201a;" k="125" />
    <hkern u1="y" u2="&#x153;" k="18" />
    <hkern u1="y" u2="&#x119;" k="18" />
    <hkern u1="y" u2="&#x107;" k="18" />
    <hkern u1="y" u2="&#x104;" k="70" />
    <hkern u1="y" u2="&#xf8;" k="18" />
    <hkern u1="y" u2="&#xf6;" k="18" />
    <hkern u1="y" u2="&#xf5;" k="18" />
    <hkern u1="y" u2="&#xf4;" k="18" />
    <hkern u1="y" u2="&#xf3;" k="18" />
    <hkern u1="y" u2="&#xf2;" k="18" />
    <hkern u1="y" u2="&#xf0;" k="18" />
    <hkern u1="y" u2="&#xeb;" k="18" />
    <hkern u1="y" u2="&#xea;" k="18" />
    <hkern u1="y" u2="&#xe9;" k="18" />
    <hkern u1="y" u2="&#xe8;" k="18" />
    <hkern u1="y" u2="&#xe7;" k="18" />
    <hkern u1="y" u2="&#xc6;" k="70" />
    <hkern u1="y" u2="&#xc5;" k="70" />
    <hkern u1="y" u2="&#xc4;" k="70" />
    <hkern u1="y" u2="&#xc3;" k="70" />
    <hkern u1="y" u2="&#xc2;" k="70" />
    <hkern u1="y" u2="&#xc1;" k="70" />
    <hkern u1="y" u2="&#xc0;" k="70" />
    <hkern u1="y" u2="q" k="18" />
    <hkern u1="y" u2="o" k="18" />
    <hkern u1="y" u2="e" k="18" />
    <hkern u1="y" u2="d" k="18" />
    <hkern u1="y" u2="c" k="18" />
    <hkern u1="y" u2="A" k="70" />
    <hkern u1="y" u2="&#x2f;" k="70" />
    <hkern u1="y" u2="&#x2e;" k="125" />
    <hkern u1="y" u2="&#x2c;" k="125" />
    <hkern u1="y" u2="&#x26;" k="70" />
    <hkern u1="&#x7b;" u2="&#x153;" k="25" />
    <hkern u1="&#x7b;" u2="&#x152;" k="40" />
    <hkern u1="&#x7b;" u2="&#x119;" k="25" />
    <hkern u1="&#x7b;" u2="&#x107;" k="25" />
    <hkern u1="&#x7b;" u2="&#x106;" k="40" />
    <hkern u1="&#x7b;" u2="&#xf8;" k="25" />
    <hkern u1="&#x7b;" u2="&#xf6;" k="25" />
    <hkern u1="&#x7b;" u2="&#xf5;" k="25" />
    <hkern u1="&#x7b;" u2="&#xf4;" k="25" />
    <hkern u1="&#x7b;" u2="&#xf3;" k="25" />
    <hkern u1="&#x7b;" u2="&#xf2;" k="25" />
    <hkern u1="&#x7b;" u2="&#xf0;" k="25" />
    <hkern u1="&#x7b;" u2="&#xeb;" k="25" />
    <hkern u1="&#x7b;" u2="&#xea;" k="25" />
    <hkern u1="&#x7b;" u2="&#xe9;" k="25" />
    <hkern u1="&#x7b;" u2="&#xe8;" k="25" />
    <hkern u1="&#x7b;" u2="&#xe7;" k="25" />
    <hkern u1="&#x7b;" u2="&#xd8;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd6;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd5;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd4;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd3;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd2;" k="40" />
    <hkern u1="&#x7b;" u2="&#xc7;" k="40" />
    <hkern u1="&#x7b;" u2="q" k="25" />
    <hkern u1="&#x7b;" u2="o" k="25" />
    <hkern u1="&#x7b;" u2="e" k="25" />
    <hkern u1="&#x7b;" u2="d" k="25" />
    <hkern u1="&#x7b;" u2="c" k="25" />
    <hkern u1="&#x7b;" u2="Q" k="40" />
    <hkern u1="&#x7b;" u2="O" k="40" />
    <hkern u1="&#x7b;" u2="G" k="40" />
    <hkern u1="&#x7b;" u2="C" k="40" />
    <hkern u1="&#x7b;" u2="&#x40;" k="40" />
    <hkern u1="&#xaa;" u2="&#x2206;" k="170" />
    <hkern u1="&#xaa;" u2="&#x203a;" k="190" />
    <hkern u1="&#xaa;" u2="&#x2039;" k="190" />
    <hkern u1="&#xaa;" u2="&#x2022;" k="190" />
    <hkern u1="&#xaa;" u2="&#x201e;" k="245" />
    <hkern u1="&#xaa;" u2="&#x201a;" k="245" />
    <hkern u1="&#xaa;" u2="&#x2014;" k="190" />
    <hkern u1="&#xaa;" u2="&#x2013;" k="190" />
    <hkern u1="&#xaa;" u2="&#x178;" k="-25" />
    <hkern u1="&#xaa;" u2="&#x153;" k="85" />
    <hkern u1="&#xaa;" u2="&#x152;" k="48" />
    <hkern u1="&#xaa;" u2="&#x119;" k="85" />
    <hkern u1="&#xaa;" u2="&#x107;" k="85" />
    <hkern u1="&#xaa;" u2="&#x106;" k="48" />
    <hkern u1="&#xaa;" u2="&#x105;" k="62" />
    <hkern u1="&#xaa;" u2="&#x104;" k="170" />
    <hkern u1="&#xaa;" u2="&#xf8;" k="85" />
    <hkern u1="&#xaa;" u2="&#xf6;" k="85" />
    <hkern u1="&#xaa;" u2="&#xf5;" k="85" />
    <hkern u1="&#xaa;" u2="&#xf4;" k="85" />
    <hkern u1="&#xaa;" u2="&#xf3;" k="85" />
    <hkern u1="&#xaa;" u2="&#xf2;" k="85" />
    <hkern u1="&#xaa;" u2="&#xf0;" k="85" />
    <hkern u1="&#xaa;" u2="&#xeb;" k="85" />
    <hkern u1="&#xaa;" u2="&#xea;" k="85" />
    <hkern u1="&#xaa;" u2="&#xe9;" k="85" />
    <hkern u1="&#xaa;" u2="&#xe8;" k="85" />
    <hkern u1="&#xaa;" u2="&#xe7;" k="85" />
    <hkern u1="&#xaa;" u2="&#xe6;" k="62" />
    <hkern u1="&#xaa;" u2="&#xe5;" k="62" />
    <hkern u1="&#xaa;" u2="&#xe4;" k="62" />
    <hkern u1="&#xaa;" u2="&#xe3;" k="62" />
    <hkern u1="&#xaa;" u2="&#xe2;" k="62" />
    <hkern u1="&#xaa;" u2="&#xe1;" k="62" />
    <hkern u1="&#xaa;" u2="&#xe0;" k="62" />
    <hkern u1="&#xaa;" u2="&#xdd;" k="-25" />
    <hkern u1="&#xaa;" u2="&#xd8;" k="48" />
    <hkern u1="&#xaa;" u2="&#xd6;" k="48" />
    <hkern u1="&#xaa;" u2="&#xd5;" k="48" />
    <hkern u1="&#xaa;" u2="&#xd4;" k="48" />
    <hkern u1="&#xaa;" u2="&#xd3;" k="48" />
    <hkern u1="&#xaa;" u2="&#xd2;" k="48" />
    <hkern u1="&#xaa;" u2="&#xc7;" k="48" />
    <hkern u1="&#xaa;" u2="&#xc6;" k="170" />
    <hkern u1="&#xaa;" u2="&#xc5;" k="170" />
    <hkern u1="&#xaa;" u2="&#xc4;" k="170" />
    <hkern u1="&#xaa;" u2="&#xc3;" k="170" />
    <hkern u1="&#xaa;" u2="&#xc2;" k="170" />
    <hkern u1="&#xaa;" u2="&#xc1;" k="170" />
    <hkern u1="&#xaa;" u2="&#xc0;" k="170" />
    <hkern u1="&#xaa;" u2="&#xbb;" k="190" />
    <hkern u1="&#xaa;" u2="&#xb7;" k="190" />
    <hkern u1="&#xaa;" u2="&#xad;" k="190" />
    <hkern u1="&#xaa;" u2="&#xab;" k="190" />
    <hkern u1="&#xaa;" u2="q" k="85" />
    <hkern u1="&#xaa;" u2="o" k="85" />
    <hkern u1="&#xaa;" u2="e" k="85" />
    <hkern u1="&#xaa;" u2="d" k="85" />
    <hkern u1="&#xaa;" u2="c" k="85" />
    <hkern u1="&#xaa;" u2="a" k="62" />
    <hkern u1="&#xaa;" u2="\" k="-55" />
    <hkern u1="&#xaa;" u2="Y" k="-25" />
    <hkern u1="&#xaa;" u2="W" k="-55" />
    <hkern u1="&#xaa;" u2="V" k="-55" />
    <hkern u1="&#xaa;" u2="Q" k="48" />
    <hkern u1="&#xaa;" u2="O" k="48" />
    <hkern u1="&#xaa;" u2="G" k="48" />
    <hkern u1="&#xaa;" u2="C" k="48" />
    <hkern u1="&#xaa;" u2="A" k="170" />
    <hkern u1="&#xaa;" u2="&#x40;" k="48" />
    <hkern u1="&#xaa;" u2="&#x2f;" k="170" />
    <hkern u1="&#xaa;" u2="&#x2e;" k="245" />
    <hkern u1="&#xaa;" u2="&#x2d;" k="190" />
    <hkern u1="&#xaa;" u2="&#x2c;" k="245" />
    <hkern u1="&#xaa;" u2="&#x26;" k="170" />
    <hkern u1="&#xab;" u2="&#x2206;" k="35" />
    <hkern u1="&#xab;" u2="&#x2122;" k="190" />
    <hkern u1="&#xab;" u2="&#x201e;" k="138" />
    <hkern u1="&#xab;" u2="&#x201d;" k="190" />
    <hkern u1="&#xab;" u2="&#x201c;" k="190" />
    <hkern u1="&#xab;" u2="&#x201a;" k="138" />
    <hkern u1="&#xab;" u2="&#x2019;" k="190" />
    <hkern u1="&#xab;" u2="&#x2018;" k="190" />
    <hkern u1="&#xab;" u2="&#x17d;" k="43" />
    <hkern u1="&#xab;" u2="&#x17b;" k="43" />
    <hkern u1="&#xab;" u2="&#x179;" k="43" />
    <hkern u1="&#xab;" u2="&#x178;" k="160" />
    <hkern u1="&#xab;" u2="&#x104;" k="35" />
    <hkern u1="&#xab;" u2="&#xdd;" k="160" />
    <hkern u1="&#xab;" u2="&#xc6;" k="35" />
    <hkern u1="&#xab;" u2="&#xc5;" k="35" />
    <hkern u1="&#xab;" u2="&#xc4;" k="35" />
    <hkern u1="&#xab;" u2="&#xc3;" k="35" />
    <hkern u1="&#xab;" u2="&#xc2;" k="35" />
    <hkern u1="&#xab;" u2="&#xc1;" k="35" />
    <hkern u1="&#xab;" u2="&#xc0;" k="35" />
    <hkern u1="&#xab;" u2="&#xba;" k="190" />
    <hkern u1="&#xab;" u2="&#xb0;" k="190" />
    <hkern u1="&#xab;" u2="&#xaa;" k="190" />
    <hkern u1="&#xab;" u2="\" k="105" />
    <hkern u1="&#xab;" u2="Z" k="43" />
    <hkern u1="&#xab;" u2="Y" k="160" />
    <hkern u1="&#xab;" u2="X" k="55" />
    <hkern u1="&#xab;" u2="W" k="25" />
    <hkern u1="&#xab;" u2="V" k="105" />
    <hkern u1="&#xab;" u2="T" k="180" />
    <hkern u1="&#xab;" u2="A" k="35" />
    <hkern u1="&#xab;" u2="&#x2f;" k="35" />
    <hkern u1="&#xab;" u2="&#x2e;" k="138" />
    <hkern u1="&#xab;" u2="&#x2c;" k="138" />
    <hkern u1="&#xab;" u2="&#x2a;" k="190" />
    <hkern u1="&#xab;" u2="&#x27;" k="190" />
    <hkern u1="&#xab;" u2="&#x26;" k="35" />
    <hkern u1="&#xab;" u2="&#x22;" k="190" />
    <hkern u1="&#xad;" u2="&#x2206;" k="35" />
    <hkern u1="&#xad;" u2="&#x2122;" k="190" />
    <hkern u1="&#xad;" u2="&#x201e;" k="138" />
    <hkern u1="&#xad;" u2="&#x201d;" k="190" />
    <hkern u1="&#xad;" u2="&#x201c;" k="190" />
    <hkern u1="&#xad;" u2="&#x201a;" k="138" />
    <hkern u1="&#xad;" u2="&#x2019;" k="190" />
    <hkern u1="&#xad;" u2="&#x2018;" k="190" />
    <hkern u1="&#xad;" u2="&#x17d;" k="43" />
    <hkern u1="&#xad;" u2="&#x17b;" k="43" />
    <hkern u1="&#xad;" u2="&#x179;" k="43" />
    <hkern u1="&#xad;" u2="&#x178;" k="160" />
    <hkern u1="&#xad;" u2="&#x104;" k="35" />
    <hkern u1="&#xad;" u2="&#xdd;" k="160" />
    <hkern u1="&#xad;" u2="&#xc6;" k="35" />
    <hkern u1="&#xad;" u2="&#xc5;" k="35" />
    <hkern u1="&#xad;" u2="&#xc4;" k="35" />
    <hkern u1="&#xad;" u2="&#xc3;" k="35" />
    <hkern u1="&#xad;" u2="&#xc2;" k="35" />
    <hkern u1="&#xad;" u2="&#xc1;" k="35" />
    <hkern u1="&#xad;" u2="&#xc0;" k="35" />
    <hkern u1="&#xad;" u2="&#xba;" k="190" />
    <hkern u1="&#xad;" u2="&#xb0;" k="190" />
    <hkern u1="&#xad;" u2="&#xaa;" k="190" />
    <hkern u1="&#xad;" u2="\" k="105" />
    <hkern u1="&#xad;" u2="Z" k="43" />
    <hkern u1="&#xad;" u2="Y" k="160" />
    <hkern u1="&#xad;" u2="X" k="55" />
    <hkern u1="&#xad;" u2="W" k="25" />
    <hkern u1="&#xad;" u2="V" k="105" />
    <hkern u1="&#xad;" u2="T" k="180" />
    <hkern u1="&#xad;" u2="A" k="35" />
    <hkern u1="&#xad;" u2="&#x2f;" k="35" />
    <hkern u1="&#xad;" u2="&#x2e;" k="138" />
    <hkern u1="&#xad;" u2="&#x2c;" k="138" />
    <hkern u1="&#xad;" u2="&#x2a;" k="190" />
    <hkern u1="&#xad;" u2="&#x27;" k="190" />
    <hkern u1="&#xad;" u2="&#x26;" k="35" />
    <hkern u1="&#xad;" u2="&#x22;" k="190" />
    <hkern u1="&#xae;" u2="&#x2206;" k="30" />
    <hkern u1="&#xae;" u2="&#x2122;" k="48" />
    <hkern u1="&#xae;" u2="&#x201e;" k="58" />
    <hkern u1="&#xae;" u2="&#x201d;" k="48" />
    <hkern u1="&#xae;" u2="&#x201c;" k="48" />
    <hkern u1="&#xae;" u2="&#x201a;" k="58" />
    <hkern u1="&#xae;" u2="&#x2019;" k="48" />
    <hkern u1="&#xae;" u2="&#x2018;" k="48" />
    <hkern u1="&#xae;" u2="&#x17d;" k="75" />
    <hkern u1="&#xae;" u2="&#x17b;" k="75" />
    <hkern u1="&#xae;" u2="&#x179;" k="75" />
    <hkern u1="&#xae;" u2="&#x178;" k="80" />
    <hkern u1="&#xae;" u2="&#x104;" k="30" />
    <hkern u1="&#xae;" u2="&#xdd;" k="80" />
    <hkern u1="&#xae;" u2="&#xc6;" k="30" />
    <hkern u1="&#xae;" u2="&#xc5;" k="30" />
    <hkern u1="&#xae;" u2="&#xc4;" k="30" />
    <hkern u1="&#xae;" u2="&#xc3;" k="30" />
    <hkern u1="&#xae;" u2="&#xc2;" k="30" />
    <hkern u1="&#xae;" u2="&#xc1;" k="30" />
    <hkern u1="&#xae;" u2="&#xc0;" k="30" />
    <hkern u1="&#xae;" u2="&#xba;" k="48" />
    <hkern u1="&#xae;" u2="&#xb0;" k="48" />
    <hkern u1="&#xae;" u2="&#xaa;" k="48" />
    <hkern u1="&#xae;" u2="&#x7d;" k="40" />
    <hkern u1="&#xae;" u2="]" k="40" />
    <hkern u1="&#xae;" u2="\" k="45" />
    <hkern u1="&#xae;" u2="Z" k="75" />
    <hkern u1="&#xae;" u2="Y" k="80" />
    <hkern u1="&#xae;" u2="X" k="30" />
    <hkern u1="&#xae;" u2="V" k="45" />
    <hkern u1="&#xae;" u2="T" k="120" />
    <hkern u1="&#xae;" u2="A" k="30" />
    <hkern u1="&#xae;" u2="&#x2f;" k="30" />
    <hkern u1="&#xae;" u2="&#x2e;" k="58" />
    <hkern u1="&#xae;" u2="&#x2c;" k="58" />
    <hkern u1="&#xae;" u2="&#x2a;" k="48" />
    <hkern u1="&#xae;" u2="&#x29;" k="40" />
    <hkern u1="&#xae;" u2="&#x27;" k="48" />
    <hkern u1="&#xae;" u2="&#x26;" k="30" />
    <hkern u1="&#xae;" u2="&#x22;" k="48" />
    <hkern u1="&#xb0;" u2="&#x2206;" k="170" />
    <hkern u1="&#xb0;" u2="&#x203a;" k="190" />
    <hkern u1="&#xb0;" u2="&#x2039;" k="190" />
    <hkern u1="&#xb0;" u2="&#x2022;" k="190" />
    <hkern u1="&#xb0;" u2="&#x201e;" k="245" />
    <hkern u1="&#xb0;" u2="&#x201a;" k="245" />
    <hkern u1="&#xb0;" u2="&#x2014;" k="190" />
    <hkern u1="&#xb0;" u2="&#x2013;" k="190" />
    <hkern u1="&#xb0;" u2="&#x178;" k="-25" />
    <hkern u1="&#xb0;" u2="&#x153;" k="85" />
    <hkern u1="&#xb0;" u2="&#x152;" k="48" />
    <hkern u1="&#xb0;" u2="&#x119;" k="85" />
    <hkern u1="&#xb0;" u2="&#x107;" k="85" />
    <hkern u1="&#xb0;" u2="&#x106;" k="48" />
    <hkern u1="&#xb0;" u2="&#x105;" k="62" />
    <hkern u1="&#xb0;" u2="&#x104;" k="170" />
    <hkern u1="&#xb0;" u2="&#xf8;" k="85" />
    <hkern u1="&#xb0;" u2="&#xf6;" k="85" />
    <hkern u1="&#xb0;" u2="&#xf5;" k="85" />
    <hkern u1="&#xb0;" u2="&#xf4;" k="85" />
    <hkern u1="&#xb0;" u2="&#xf3;" k="85" />
    <hkern u1="&#xb0;" u2="&#xf2;" k="85" />
    <hkern u1="&#xb0;" u2="&#xf0;" k="85" />
    <hkern u1="&#xb0;" u2="&#xeb;" k="85" />
    <hkern u1="&#xb0;" u2="&#xea;" k="85" />
    <hkern u1="&#xb0;" u2="&#xe9;" k="85" />
    <hkern u1="&#xb0;" u2="&#xe8;" k="85" />
    <hkern u1="&#xb0;" u2="&#xe7;" k="85" />
    <hkern u1="&#xb0;" u2="&#xe6;" k="62" />
    <hkern u1="&#xb0;" u2="&#xe5;" k="62" />
    <hkern u1="&#xb0;" u2="&#xe4;" k="62" />
    <hkern u1="&#xb0;" u2="&#xe3;" k="62" />
    <hkern u1="&#xb0;" u2="&#xe2;" k="62" />
    <hkern u1="&#xb0;" u2="&#xe1;" k="62" />
    <hkern u1="&#xb0;" u2="&#xe0;" k="62" />
    <hkern u1="&#xb0;" u2="&#xdd;" k="-25" />
    <hkern u1="&#xb0;" u2="&#xd8;" k="48" />
    <hkern u1="&#xb0;" u2="&#xd6;" k="48" />
    <hkern u1="&#xb0;" u2="&#xd5;" k="48" />
    <hkern u1="&#xb0;" u2="&#xd4;" k="48" />
    <hkern u1="&#xb0;" u2="&#xd3;" k="48" />
    <hkern u1="&#xb0;" u2="&#xd2;" k="48" />
    <hkern u1="&#xb0;" u2="&#xc7;" k="48" />
    <hkern u1="&#xb0;" u2="&#xc6;" k="170" />
    <hkern u1="&#xb0;" u2="&#xc5;" k="170" />
    <hkern u1="&#xb0;" u2="&#xc4;" k="170" />
    <hkern u1="&#xb0;" u2="&#xc3;" k="170" />
    <hkern u1="&#xb0;" u2="&#xc2;" k="170" />
    <hkern u1="&#xb0;" u2="&#xc1;" k="170" />
    <hkern u1="&#xb0;" u2="&#xc0;" k="170" />
    <hkern u1="&#xb0;" u2="&#xbb;" k="190" />
    <hkern u1="&#xb0;" u2="&#xb7;" k="190" />
    <hkern u1="&#xb0;" u2="&#xad;" k="190" />
    <hkern u1="&#xb0;" u2="&#xab;" k="190" />
    <hkern u1="&#xb0;" u2="q" k="85" />
    <hkern u1="&#xb0;" u2="o" k="85" />
    <hkern u1="&#xb0;" u2="e" k="85" />
    <hkern u1="&#xb0;" u2="d" k="85" />
    <hkern u1="&#xb0;" u2="c" k="85" />
    <hkern u1="&#xb0;" u2="a" k="62" />
    <hkern u1="&#xb0;" u2="\" k="-55" />
    <hkern u1="&#xb0;" u2="Y" k="-25" />
    <hkern u1="&#xb0;" u2="W" k="-55" />
    <hkern u1="&#xb0;" u2="V" k="-55" />
    <hkern u1="&#xb0;" u2="Q" k="48" />
    <hkern u1="&#xb0;" u2="O" k="48" />
    <hkern u1="&#xb0;" u2="G" k="48" />
    <hkern u1="&#xb0;" u2="C" k="48" />
    <hkern u1="&#xb0;" u2="A" k="170" />
    <hkern u1="&#xb0;" u2="&#x40;" k="48" />
    <hkern u1="&#xb0;" u2="&#x2f;" k="170" />
    <hkern u1="&#xb0;" u2="&#x2e;" k="245" />
    <hkern u1="&#xb0;" u2="&#x2d;" k="190" />
    <hkern u1="&#xb0;" u2="&#x2c;" k="245" />
    <hkern u1="&#xb0;" u2="&#x26;" k="170" />
    <hkern u1="&#xb2;" u2="&#x2206;" k="160" />
    <hkern u1="&#xb2;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb2;" u2="&#x104;" k="160" />
    <hkern u1="&#xb2;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb2;" u2="&#xc6;" k="160" />
    <hkern u1="&#xb2;" u2="&#xc5;" k="160" />
    <hkern u1="&#xb2;" u2="&#xc4;" k="160" />
    <hkern u1="&#xb2;" u2="&#xc3;" k="160" />
    <hkern u1="&#xb2;" u2="&#xc2;" k="160" />
    <hkern u1="&#xb2;" u2="&#xc1;" k="160" />
    <hkern u1="&#xb2;" u2="&#xc0;" k="160" />
    <hkern u1="&#xb2;" u2="\" k="-70" />
    <hkern u1="&#xb2;" u2="Y" k="-40" />
    <hkern u1="&#xb2;" u2="W" k="-70" />
    <hkern u1="&#xb2;" u2="V" k="-70" />
    <hkern u1="&#xb2;" u2="A" k="160" />
    <hkern u1="&#xb2;" u2="&#x2f;" k="160" />
    <hkern u1="&#xb2;" u2="&#x26;" k="160" />
    <hkern u1="&#xb3;" u2="&#x2206;" k="160" />
    <hkern u1="&#xb3;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb3;" u2="&#x104;" k="160" />
    <hkern u1="&#xb3;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb3;" u2="&#xc6;" k="160" />
    <hkern u1="&#xb3;" u2="&#xc5;" k="160" />
    <hkern u1="&#xb3;" u2="&#xc4;" k="160" />
    <hkern u1="&#xb3;" u2="&#xc3;" k="160" />
    <hkern u1="&#xb3;" u2="&#xc2;" k="160" />
    <hkern u1="&#xb3;" u2="&#xc1;" k="160" />
    <hkern u1="&#xb3;" u2="&#xc0;" k="160" />
    <hkern u1="&#xb3;" u2="\" k="-70" />
    <hkern u1="&#xb3;" u2="Y" k="-40" />
    <hkern u1="&#xb3;" u2="W" k="-70" />
    <hkern u1="&#xb3;" u2="V" k="-70" />
    <hkern u1="&#xb3;" u2="A" k="160" />
    <hkern u1="&#xb3;" u2="&#x2f;" k="160" />
    <hkern u1="&#xb3;" u2="&#x26;" k="160" />
    <hkern u1="&#xb7;" u2="&#x2206;" k="35" />
    <hkern u1="&#xb7;" u2="&#x2122;" k="190" />
    <hkern u1="&#xb7;" u2="&#x201e;" k="138" />
    <hkern u1="&#xb7;" u2="&#x201d;" k="190" />
    <hkern u1="&#xb7;" u2="&#x201c;" k="190" />
    <hkern u1="&#xb7;" u2="&#x201a;" k="138" />
    <hkern u1="&#xb7;" u2="&#x2019;" k="190" />
    <hkern u1="&#xb7;" u2="&#x2018;" k="190" />
    <hkern u1="&#xb7;" u2="&#x17d;" k="43" />
    <hkern u1="&#xb7;" u2="&#x17b;" k="43" />
    <hkern u1="&#xb7;" u2="&#x179;" k="43" />
    <hkern u1="&#xb7;" u2="&#x178;" k="160" />
    <hkern u1="&#xb7;" u2="&#x104;" k="35" />
    <hkern u1="&#xb7;" u2="&#xdd;" k="160" />
    <hkern u1="&#xb7;" u2="&#xc6;" k="35" />
    <hkern u1="&#xb7;" u2="&#xc5;" k="35" />
    <hkern u1="&#xb7;" u2="&#xc4;" k="35" />
    <hkern u1="&#xb7;" u2="&#xc3;" k="35" />
    <hkern u1="&#xb7;" u2="&#xc2;" k="35" />
    <hkern u1="&#xb7;" u2="&#xc1;" k="35" />
    <hkern u1="&#xb7;" u2="&#xc0;" k="35" />
    <hkern u1="&#xb7;" u2="&#xba;" k="190" />
    <hkern u1="&#xb7;" u2="&#xb0;" k="190" />
    <hkern u1="&#xb7;" u2="&#xaa;" k="190" />
    <hkern u1="&#xb7;" u2="\" k="105" />
    <hkern u1="&#xb7;" u2="Z" k="43" />
    <hkern u1="&#xb7;" u2="Y" k="160" />
    <hkern u1="&#xb7;" u2="X" k="55" />
    <hkern u1="&#xb7;" u2="W" k="25" />
    <hkern u1="&#xb7;" u2="V" k="105" />
    <hkern u1="&#xb7;" u2="T" k="180" />
    <hkern u1="&#xb7;" u2="A" k="35" />
    <hkern u1="&#xb7;" u2="&#x2f;" k="35" />
    <hkern u1="&#xb7;" u2="&#x2e;" k="138" />
    <hkern u1="&#xb7;" u2="&#x2c;" k="138" />
    <hkern u1="&#xb7;" u2="&#x2a;" k="190" />
    <hkern u1="&#xb7;" u2="&#x27;" k="190" />
    <hkern u1="&#xb7;" u2="&#x26;" k="35" />
    <hkern u1="&#xb7;" u2="&#x22;" k="190" />
    <hkern u1="&#xb9;" u2="&#x2206;" k="160" />
    <hkern u1="&#xb9;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb9;" u2="&#x104;" k="160" />
    <hkern u1="&#xb9;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb9;" u2="&#xc6;" k="160" />
    <hkern u1="&#xb9;" u2="&#xc5;" k="160" />
    <hkern u1="&#xb9;" u2="&#xc4;" k="160" />
    <hkern u1="&#xb9;" u2="&#xc3;" k="160" />
    <hkern u1="&#xb9;" u2="&#xc2;" k="160" />
    <hkern u1="&#xb9;" u2="&#xc1;" k="160" />
    <hkern u1="&#xb9;" u2="&#xc0;" k="160" />
    <hkern u1="&#xb9;" u2="\" k="-70" />
    <hkern u1="&#xb9;" u2="Y" k="-40" />
    <hkern u1="&#xb9;" u2="W" k="-70" />
    <hkern u1="&#xb9;" u2="V" k="-70" />
    <hkern u1="&#xb9;" u2="A" k="160" />
    <hkern u1="&#xb9;" u2="&#x2f;" k="160" />
    <hkern u1="&#xb9;" u2="&#x26;" k="160" />
    <hkern u1="&#xba;" u2="&#x2206;" k="170" />
    <hkern u1="&#xba;" u2="&#x203a;" k="190" />
    <hkern u1="&#xba;" u2="&#x2039;" k="190" />
    <hkern u1="&#xba;" u2="&#x2022;" k="190" />
    <hkern u1="&#xba;" u2="&#x201e;" k="245" />
    <hkern u1="&#xba;" u2="&#x201a;" k="245" />
    <hkern u1="&#xba;" u2="&#x2014;" k="190" />
    <hkern u1="&#xba;" u2="&#x2013;" k="190" />
    <hkern u1="&#xba;" u2="&#x178;" k="-25" />
    <hkern u1="&#xba;" u2="&#x153;" k="85" />
    <hkern u1="&#xba;" u2="&#x152;" k="48" />
    <hkern u1="&#xba;" u2="&#x119;" k="85" />
    <hkern u1="&#xba;" u2="&#x107;" k="85" />
    <hkern u1="&#xba;" u2="&#x106;" k="48" />
    <hkern u1="&#xba;" u2="&#x105;" k="62" />
    <hkern u1="&#xba;" u2="&#x104;" k="170" />
    <hkern u1="&#xba;" u2="&#xf8;" k="85" />
    <hkern u1="&#xba;" u2="&#xf6;" k="85" />
    <hkern u1="&#xba;" u2="&#xf5;" k="85" />
    <hkern u1="&#xba;" u2="&#xf4;" k="85" />
    <hkern u1="&#xba;" u2="&#xf3;" k="85" />
    <hkern u1="&#xba;" u2="&#xf2;" k="85" />
    <hkern u1="&#xba;" u2="&#xf0;" k="85" />
    <hkern u1="&#xba;" u2="&#xeb;" k="85" />
    <hkern u1="&#xba;" u2="&#xea;" k="85" />
    <hkern u1="&#xba;" u2="&#xe9;" k="85" />
    <hkern u1="&#xba;" u2="&#xe8;" k="85" />
    <hkern u1="&#xba;" u2="&#xe7;" k="85" />
    <hkern u1="&#xba;" u2="&#xe6;" k="62" />
    <hkern u1="&#xba;" u2="&#xe5;" k="62" />
    <hkern u1="&#xba;" u2="&#xe4;" k="62" />
    <hkern u1="&#xba;" u2="&#xe3;" k="62" />
    <hkern u1="&#xba;" u2="&#xe2;" k="62" />
    <hkern u1="&#xba;" u2="&#xe1;" k="62" />
    <hkern u1="&#xba;" u2="&#xe0;" k="62" />
    <hkern u1="&#xba;" u2="&#xdd;" k="-25" />
    <hkern u1="&#xba;" u2="&#xd8;" k="48" />
    <hkern u1="&#xba;" u2="&#xd6;" k="48" />
    <hkern u1="&#xba;" u2="&#xd5;" k="48" />
    <hkern u1="&#xba;" u2="&#xd4;" k="48" />
    <hkern u1="&#xba;" u2="&#xd3;" k="48" />
    <hkern u1="&#xba;" u2="&#xd2;" k="48" />
    <hkern u1="&#xba;" u2="&#xc7;" k="48" />
    <hkern u1="&#xba;" u2="&#xc6;" k="170" />
    <hkern u1="&#xba;" u2="&#xc5;" k="170" />
    <hkern u1="&#xba;" u2="&#xc4;" k="170" />
    <hkern u1="&#xba;" u2="&#xc3;" k="170" />
    <hkern u1="&#xba;" u2="&#xc2;" k="170" />
    <hkern u1="&#xba;" u2="&#xc1;" k="170" />
    <hkern u1="&#xba;" u2="&#xc0;" k="170" />
    <hkern u1="&#xba;" u2="&#xbb;" k="190" />
    <hkern u1="&#xba;" u2="&#xb7;" k="190" />
    <hkern u1="&#xba;" u2="&#xad;" k="190" />
    <hkern u1="&#xba;" u2="&#xab;" k="190" />
    <hkern u1="&#xba;" u2="q" k="85" />
    <hkern u1="&#xba;" u2="o" k="85" />
    <hkern u1="&#xba;" u2="e" k="85" />
    <hkern u1="&#xba;" u2="d" k="85" />
    <hkern u1="&#xba;" u2="c" k="85" />
    <hkern u1="&#xba;" u2="a" k="62" />
    <hkern u1="&#xba;" u2="\" k="-55" />
    <hkern u1="&#xba;" u2="Y" k="-25" />
    <hkern u1="&#xba;" u2="W" k="-55" />
    <hkern u1="&#xba;" u2="V" k="-55" />
    <hkern u1="&#xba;" u2="Q" k="48" />
    <hkern u1="&#xba;" u2="O" k="48" />
    <hkern u1="&#xba;" u2="G" k="48" />
    <hkern u1="&#xba;" u2="C" k="48" />
    <hkern u1="&#xba;" u2="A" k="170" />
    <hkern u1="&#xba;" u2="&#x40;" k="48" />
    <hkern u1="&#xba;" u2="&#x2f;" k="170" />
    <hkern u1="&#xba;" u2="&#x2e;" k="245" />
    <hkern u1="&#xba;" u2="&#x2d;" k="190" />
    <hkern u1="&#xba;" u2="&#x2c;" k="245" />
    <hkern u1="&#xba;" u2="&#x26;" k="170" />
    <hkern u1="&#xbb;" u2="&#x2206;" k="35" />
    <hkern u1="&#xbb;" u2="&#x2122;" k="190" />
    <hkern u1="&#xbb;" u2="&#x201e;" k="138" />
    <hkern u1="&#xbb;" u2="&#x201d;" k="190" />
    <hkern u1="&#xbb;" u2="&#x201c;" k="190" />
    <hkern u1="&#xbb;" u2="&#x201a;" k="138" />
    <hkern u1="&#xbb;" u2="&#x2019;" k="190" />
    <hkern u1="&#xbb;" u2="&#x2018;" k="190" />
    <hkern u1="&#xbb;" u2="&#x17d;" k="43" />
    <hkern u1="&#xbb;" u2="&#x17b;" k="43" />
    <hkern u1="&#xbb;" u2="&#x179;" k="43" />
    <hkern u1="&#xbb;" u2="&#x178;" k="160" />
    <hkern u1="&#xbb;" u2="&#x104;" k="35" />
    <hkern u1="&#xbb;" u2="&#xdd;" k="160" />
    <hkern u1="&#xbb;" u2="&#xc6;" k="35" />
    <hkern u1="&#xbb;" u2="&#xc5;" k="35" />
    <hkern u1="&#xbb;" u2="&#xc4;" k="35" />
    <hkern u1="&#xbb;" u2="&#xc3;" k="35" />
    <hkern u1="&#xbb;" u2="&#xc2;" k="35" />
    <hkern u1="&#xbb;" u2="&#xc1;" k="35" />
    <hkern u1="&#xbb;" u2="&#xc0;" k="35" />
    <hkern u1="&#xbb;" u2="&#xba;" k="190" />
    <hkern u1="&#xbb;" u2="&#xb0;" k="190" />
    <hkern u1="&#xbb;" u2="&#xaa;" k="190" />
    <hkern u1="&#xbb;" u2="\" k="105" />
    <hkern u1="&#xbb;" u2="Z" k="43" />
    <hkern u1="&#xbb;" u2="Y" k="160" />
    <hkern u1="&#xbb;" u2="X" k="55" />
    <hkern u1="&#xbb;" u2="W" k="25" />
    <hkern u1="&#xbb;" u2="V" k="105" />
    <hkern u1="&#xbb;" u2="T" k="180" />
    <hkern u1="&#xbb;" u2="A" k="35" />
    <hkern u1="&#xbb;" u2="&#x2f;" k="35" />
    <hkern u1="&#xbb;" u2="&#x2e;" k="138" />
    <hkern u1="&#xbb;" u2="&#x2c;" k="138" />
    <hkern u1="&#xbb;" u2="&#x2a;" k="190" />
    <hkern u1="&#xbb;" u2="&#x27;" k="190" />
    <hkern u1="&#xbb;" u2="&#x26;" k="35" />
    <hkern u1="&#xbb;" u2="&#x22;" k="190" />
    <hkern u1="&#xc0;" u2="&#x2122;" k="170" />
    <hkern u1="&#xc0;" u2="&#x203a;" k="35" />
    <hkern u1="&#xc0;" u2="&#x2039;" k="35" />
    <hkern u1="&#xc0;" u2="&#x2022;" k="35" />
    <hkern u1="&#xc0;" u2="&#x201d;" k="170" />
    <hkern u1="&#xc0;" u2="&#x201c;" k="170" />
    <hkern u1="&#xc0;" u2="&#x2019;" k="170" />
    <hkern u1="&#xc0;" u2="&#x2018;" k="170" />
    <hkern u1="&#xc0;" u2="&#x2014;" k="35" />
    <hkern u1="&#xc0;" u2="&#x2013;" k="35" />
    <hkern u1="&#xc0;" u2="&#x178;" k="140" />
    <hkern u1="&#xc0;" u2="&#x152;" k="30" />
    <hkern u1="&#xc0;" u2="&#x106;" k="30" />
    <hkern u1="&#xc0;" u2="&#xdd;" k="140" />
    <hkern u1="&#xc0;" u2="&#xdc;" k="58" />
    <hkern u1="&#xc0;" u2="&#xdb;" k="58" />
    <hkern u1="&#xc0;" u2="&#xda;" k="58" />
    <hkern u1="&#xc0;" u2="&#xd9;" k="58" />
    <hkern u1="&#xc0;" u2="&#xd8;" k="30" />
    <hkern u1="&#xc0;" u2="&#xd6;" k="30" />
    <hkern u1="&#xc0;" u2="&#xd5;" k="30" />
    <hkern u1="&#xc0;" u2="&#xd4;" k="30" />
    <hkern u1="&#xc0;" u2="&#xd3;" k="30" />
    <hkern u1="&#xc0;" u2="&#xd2;" k="30" />
    <hkern u1="&#xc0;" u2="&#xc7;" k="30" />
    <hkern u1="&#xc0;" u2="&#xbb;" k="35" />
    <hkern u1="&#xc0;" u2="&#xba;" k="170" />
    <hkern u1="&#xc0;" u2="&#xb9;" k="160" />
    <hkern u1="&#xc0;" u2="&#xb7;" k="35" />
    <hkern u1="&#xc0;" u2="&#xb3;" k="160" />
    <hkern u1="&#xc0;" u2="&#xb2;" k="160" />
    <hkern u1="&#xc0;" u2="&#xb0;" k="170" />
    <hkern u1="&#xc0;" u2="&#xad;" k="35" />
    <hkern u1="&#xc0;" u2="&#xab;" k="35" />
    <hkern u1="&#xc0;" u2="&#xaa;" k="170" />
    <hkern u1="&#xc0;" u2="y" k="70" />
    <hkern u1="&#xc0;" u2="v" k="70" />
    <hkern u1="&#xc0;" u2="\" k="95" />
    <hkern u1="&#xc0;" u2="Y" k="140" />
    <hkern u1="&#xc0;" u2="W" k="60" />
    <hkern u1="&#xc0;" u2="V" k="95" />
    <hkern u1="&#xc0;" u2="U" k="58" />
    <hkern u1="&#xc0;" u2="T" k="115" />
    <hkern u1="&#xc0;" u2="Q" k="30" />
    <hkern u1="&#xc0;" u2="O" k="30" />
    <hkern u1="&#xc0;" u2="J" k="-45" />
    <hkern u1="&#xc0;" u2="G" k="30" />
    <hkern u1="&#xc0;" u2="C" k="30" />
    <hkern u1="&#xc0;" u2="&#x40;" k="30" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="48" />
    <hkern u1="&#xc0;" u2="&#x2d;" k="35" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="170" />
    <hkern u1="&#xc0;" u2="&#x27;" k="170" />
    <hkern u1="&#xc0;" u2="&#x22;" k="170" />
    <hkern u1="&#xc1;" u2="&#x2122;" k="170" />
    <hkern u1="&#xc1;" u2="&#x203a;" k="35" />
    <hkern u1="&#xc1;" u2="&#x2039;" k="35" />
    <hkern u1="&#xc1;" u2="&#x2022;" k="35" />
    <hkern u1="&#xc1;" u2="&#x201d;" k="170" />
    <hkern u1="&#xc1;" u2="&#x201c;" k="170" />
    <hkern u1="&#xc1;" u2="&#x2019;" k="170" />
    <hkern u1="&#xc1;" u2="&#x2018;" k="170" />
    <hkern u1="&#xc1;" u2="&#x2014;" k="35" />
    <hkern u1="&#xc1;" u2="&#x2013;" k="35" />
    <hkern u1="&#xc1;" u2="&#x178;" k="140" />
    <hkern u1="&#xc1;" u2="&#x152;" k="30" />
    <hkern u1="&#xc1;" u2="&#x106;" k="30" />
    <hkern u1="&#xc1;" u2="&#xdd;" k="140" />
    <hkern u1="&#xc1;" u2="&#xdc;" k="58" />
    <hkern u1="&#xc1;" u2="&#xdb;" k="58" />
    <hkern u1="&#xc1;" u2="&#xda;" k="58" />
    <hkern u1="&#xc1;" u2="&#xd9;" k="58" />
    <hkern u1="&#xc1;" u2="&#xd8;" k="30" />
    <hkern u1="&#xc1;" u2="&#xd6;" k="30" />
    <hkern u1="&#xc1;" u2="&#xd5;" k="30" />
    <hkern u1="&#xc1;" u2="&#xd4;" k="30" />
    <hkern u1="&#xc1;" u2="&#xd3;" k="30" />
    <hkern u1="&#xc1;" u2="&#xd2;" k="30" />
    <hkern u1="&#xc1;" u2="&#xc7;" k="30" />
    <hkern u1="&#xc1;" u2="&#xbb;" k="35" />
    <hkern u1="&#xc1;" u2="&#xba;" k="170" />
    <hkern u1="&#xc1;" u2="&#xb9;" k="160" />
    <hkern u1="&#xc1;" u2="&#xb7;" k="35" />
    <hkern u1="&#xc1;" u2="&#xb3;" k="160" />
    <hkern u1="&#xc1;" u2="&#xb2;" k="160" />
    <hkern u1="&#xc1;" u2="&#xb0;" k="170" />
    <hkern u1="&#xc1;" u2="&#xad;" k="35" />
    <hkern u1="&#xc1;" u2="&#xab;" k="35" />
    <hkern u1="&#xc1;" u2="&#xaa;" k="170" />
    <hkern u1="&#xc1;" u2="y" k="70" />
    <hkern u1="&#xc1;" u2="v" k="70" />
    <hkern u1="&#xc1;" u2="\" k="95" />
    <hkern u1="&#xc1;" u2="Y" k="140" />
    <hkern u1="&#xc1;" u2="W" k="60" />
    <hkern u1="&#xc1;" u2="V" k="95" />
    <hkern u1="&#xc1;" u2="U" k="58" />
    <hkern u1="&#xc1;" u2="T" k="115" />
    <hkern u1="&#xc1;" u2="Q" k="30" />
    <hkern u1="&#xc1;" u2="O" k="30" />
    <hkern u1="&#xc1;" u2="J" k="-45" />
    <hkern u1="&#xc1;" u2="G" k="30" />
    <hkern u1="&#xc1;" u2="C" k="30" />
    <hkern u1="&#xc1;" u2="&#x40;" k="30" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="48" />
    <hkern u1="&#xc1;" u2="&#x2d;" k="35" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="170" />
    <hkern u1="&#xc1;" u2="&#x27;" k="170" />
    <hkern u1="&#xc1;" u2="&#x22;" k="170" />
    <hkern u1="&#xc2;" u2="&#x2122;" k="170" />
    <hkern u1="&#xc2;" u2="&#x203a;" k="35" />
    <hkern u1="&#xc2;" u2="&#x2039;" k="35" />
    <hkern u1="&#xc2;" u2="&#x2022;" k="35" />
    <hkern u1="&#xc2;" u2="&#x201d;" k="170" />
    <hkern u1="&#xc2;" u2="&#x201c;" k="170" />
    <hkern u1="&#xc2;" u2="&#x2019;" k="170" />
    <hkern u1="&#xc2;" u2="&#x2018;" k="170" />
    <hkern u1="&#xc2;" u2="&#x2014;" k="35" />
    <hkern u1="&#xc2;" u2="&#x2013;" k="35" />
    <hkern u1="&#xc2;" u2="&#x178;" k="140" />
    <hkern u1="&#xc2;" u2="&#x152;" k="30" />
    <hkern u1="&#xc2;" u2="&#x106;" k="30" />
    <hkern u1="&#xc2;" u2="&#xdd;" k="140" />
    <hkern u1="&#xc2;" u2="&#xdc;" k="58" />
    <hkern u1="&#xc2;" u2="&#xdb;" k="58" />
    <hkern u1="&#xc2;" u2="&#xda;" k="58" />
    <hkern u1="&#xc2;" u2="&#xd9;" k="58" />
    <hkern u1="&#xc2;" u2="&#xd8;" k="30" />
    <hkern u1="&#xc2;" u2="&#xd6;" k="30" />
    <hkern u1="&#xc2;" u2="&#xd5;" k="30" />
    <hkern u1="&#xc2;" u2="&#xd4;" k="30" />
    <hkern u1="&#xc2;" u2="&#xd3;" k="30" />
    <hkern u1="&#xc2;" u2="&#xd2;" k="30" />
    <hkern u1="&#xc2;" u2="&#xc7;" k="30" />
    <hkern u1="&#xc2;" u2="&#xbb;" k="35" />
    <hkern u1="&#xc2;" u2="&#xba;" k="170" />
    <hkern u1="&#xc2;" u2="&#xb9;" k="160" />
    <hkern u1="&#xc2;" u2="&#xb7;" k="35" />
    <hkern u1="&#xc2;" u2="&#xb3;" k="160" />
    <hkern u1="&#xc2;" u2="&#xb2;" k="160" />
    <hkern u1="&#xc2;" u2="&#xb0;" k="170" />
    <hkern u1="&#xc2;" u2="&#xad;" k="35" />
    <hkern u1="&#xc2;" u2="&#xab;" k="35" />
    <hkern u1="&#xc2;" u2="&#xaa;" k="170" />
    <hkern u1="&#xc2;" u2="y" k="70" />
    <hkern u1="&#xc2;" u2="v" k="70" />
    <hkern u1="&#xc2;" u2="\" k="95" />
    <hkern u1="&#xc2;" u2="Y" k="140" />
    <hkern u1="&#xc2;" u2="W" k="60" />
    <hkern u1="&#xc2;" u2="V" k="95" />
    <hkern u1="&#xc2;" u2="U" k="58" />
    <hkern u1="&#xc2;" u2="T" k="115" />
    <hkern u1="&#xc2;" u2="Q" k="30" />
    <hkern u1="&#xc2;" u2="O" k="30" />
    <hkern u1="&#xc2;" u2="J" k="-45" />
    <hkern u1="&#xc2;" u2="G" k="30" />
    <hkern u1="&#xc2;" u2="C" k="30" />
    <hkern u1="&#xc2;" u2="&#x40;" k="30" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="48" />
    <hkern u1="&#xc2;" u2="&#x2d;" k="35" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="170" />
    <hkern u1="&#xc2;" u2="&#x27;" k="170" />
    <hkern u1="&#xc2;" u2="&#x22;" k="170" />
    <hkern u1="&#xc3;" u2="&#x2122;" k="170" />
    <hkern u1="&#xc3;" u2="&#x203a;" k="35" />
    <hkern u1="&#xc3;" u2="&#x2039;" k="35" />
    <hkern u1="&#xc3;" u2="&#x2022;" k="35" />
    <hkern u1="&#xc3;" u2="&#x201d;" k="170" />
    <hkern u1="&#xc3;" u2="&#x201c;" k="170" />
    <hkern u1="&#xc3;" u2="&#x2019;" k="170" />
    <hkern u1="&#xc3;" u2="&#x2018;" k="170" />
    <hkern u1="&#xc3;" u2="&#x2014;" k="35" />
    <hkern u1="&#xc3;" u2="&#x2013;" k="35" />
    <hkern u1="&#xc3;" u2="&#x178;" k="140" />
    <hkern u1="&#xc3;" u2="&#x152;" k="30" />
    <hkern u1="&#xc3;" u2="&#x106;" k="30" />
    <hkern u1="&#xc3;" u2="&#xdd;" k="140" />
    <hkern u1="&#xc3;" u2="&#xdc;" k="58" />
    <hkern u1="&#xc3;" u2="&#xdb;" k="58" />
    <hkern u1="&#xc3;" u2="&#xda;" k="58" />
    <hkern u1="&#xc3;" u2="&#xd9;" k="58" />
    <hkern u1="&#xc3;" u2="&#xd8;" k="30" />
    <hkern u1="&#xc3;" u2="&#xd6;" k="30" />
    <hkern u1="&#xc3;" u2="&#xd5;" k="30" />
    <hkern u1="&#xc3;" u2="&#xd4;" k="30" />
    <hkern u1="&#xc3;" u2="&#xd3;" k="30" />
    <hkern u1="&#xc3;" u2="&#xd2;" k="30" />
    <hkern u1="&#xc3;" u2="&#xc7;" k="30" />
    <hkern u1="&#xc3;" u2="&#xbb;" k="35" />
    <hkern u1="&#xc3;" u2="&#xba;" k="170" />
    <hkern u1="&#xc3;" u2="&#xb9;" k="160" />
    <hkern u1="&#xc3;" u2="&#xb7;" k="35" />
    <hkern u1="&#xc3;" u2="&#xb3;" k="160" />
    <hkern u1="&#xc3;" u2="&#xb2;" k="160" />
    <hkern u1="&#xc3;" u2="&#xb0;" k="170" />
    <hkern u1="&#xc3;" u2="&#xad;" k="35" />
    <hkern u1="&#xc3;" u2="&#xab;" k="35" />
    <hkern u1="&#xc3;" u2="&#xaa;" k="170" />
    <hkern u1="&#xc3;" u2="y" k="70" />
    <hkern u1="&#xc3;" u2="v" k="70" />
    <hkern u1="&#xc3;" u2="\" k="95" />
    <hkern u1="&#xc3;" u2="Y" k="140" />
    <hkern u1="&#xc3;" u2="W" k="60" />
    <hkern u1="&#xc3;" u2="V" k="95" />
    <hkern u1="&#xc3;" u2="U" k="58" />
    <hkern u1="&#xc3;" u2="T" k="115" />
    <hkern u1="&#xc3;" u2="Q" k="30" />
    <hkern u1="&#xc3;" u2="O" k="30" />
    <hkern u1="&#xc3;" u2="J" k="-45" />
    <hkern u1="&#xc3;" u2="G" k="30" />
    <hkern u1="&#xc3;" u2="C" k="30" />
    <hkern u1="&#xc3;" u2="&#x40;" k="30" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="48" />
    <hkern u1="&#xc3;" u2="&#x2d;" k="35" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="170" />
    <hkern u1="&#xc3;" u2="&#x27;" k="170" />
    <hkern u1="&#xc3;" u2="&#x22;" k="170" />
    <hkern u1="&#xc4;" u2="&#x2122;" k="170" />
    <hkern u1="&#xc4;" u2="&#x203a;" k="35" />
    <hkern u1="&#xc4;" u2="&#x2039;" k="35" />
    <hkern u1="&#xc4;" u2="&#x2022;" k="35" />
    <hkern u1="&#xc4;" u2="&#x201d;" k="170" />
    <hkern u1="&#xc4;" u2="&#x201c;" k="170" />
    <hkern u1="&#xc4;" u2="&#x2019;" k="170" />
    <hkern u1="&#xc4;" u2="&#x2018;" k="170" />
    <hkern u1="&#xc4;" u2="&#x2014;" k="35" />
    <hkern u1="&#xc4;" u2="&#x2013;" k="35" />
    <hkern u1="&#xc4;" u2="&#x178;" k="140" />
    <hkern u1="&#xc4;" u2="&#x152;" k="30" />
    <hkern u1="&#xc4;" u2="&#x106;" k="30" />
    <hkern u1="&#xc4;" u2="&#xdd;" k="140" />
    <hkern u1="&#xc4;" u2="&#xdc;" k="58" />
    <hkern u1="&#xc4;" u2="&#xdb;" k="58" />
    <hkern u1="&#xc4;" u2="&#xda;" k="58" />
    <hkern u1="&#xc4;" u2="&#xd9;" k="58" />
    <hkern u1="&#xc4;" u2="&#xd8;" k="30" />
    <hkern u1="&#xc4;" u2="&#xd6;" k="30" />
    <hkern u1="&#xc4;" u2="&#xd5;" k="30" />
    <hkern u1="&#xc4;" u2="&#xd4;" k="30" />
    <hkern u1="&#xc4;" u2="&#xd3;" k="30" />
    <hkern u1="&#xc4;" u2="&#xd2;" k="30" />
    <hkern u1="&#xc4;" u2="&#xc7;" k="30" />
    <hkern u1="&#xc4;" u2="&#xbb;" k="35" />
    <hkern u1="&#xc4;" u2="&#xba;" k="170" />
    <hkern u1="&#xc4;" u2="&#xb9;" k="160" />
    <hkern u1="&#xc4;" u2="&#xb7;" k="35" />
    <hkern u1="&#xc4;" u2="&#xb3;" k="160" />
    <hkern u1="&#xc4;" u2="&#xb2;" k="160" />
    <hkern u1="&#xc4;" u2="&#xb0;" k="170" />
    <hkern u1="&#xc4;" u2="&#xad;" k="35" />
    <hkern u1="&#xc4;" u2="&#xab;" k="35" />
    <hkern u1="&#xc4;" u2="&#xaa;" k="170" />
    <hkern u1="&#xc4;" u2="y" k="70" />
    <hkern u1="&#xc4;" u2="v" k="70" />
    <hkern u1="&#xc4;" u2="\" k="95" />
    <hkern u1="&#xc4;" u2="Y" k="140" />
    <hkern u1="&#xc4;" u2="W" k="60" />
    <hkern u1="&#xc4;" u2="V" k="95" />
    <hkern u1="&#xc4;" u2="U" k="58" />
    <hkern u1="&#xc4;" u2="T" k="115" />
    <hkern u1="&#xc4;" u2="Q" k="30" />
    <hkern u1="&#xc4;" u2="O" k="30" />
    <hkern u1="&#xc4;" u2="J" k="-45" />
    <hkern u1="&#xc4;" u2="G" k="30" />
    <hkern u1="&#xc4;" u2="C" k="30" />
    <hkern u1="&#xc4;" u2="&#x40;" k="30" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="48" />
    <hkern u1="&#xc4;" u2="&#x2d;" k="35" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="170" />
    <hkern u1="&#xc4;" u2="&#x27;" k="170" />
    <hkern u1="&#xc4;" u2="&#x22;" k="170" />
    <hkern u1="&#xc5;" u2="&#x2122;" k="170" />
    <hkern u1="&#xc5;" u2="&#x203a;" k="35" />
    <hkern u1="&#xc5;" u2="&#x2039;" k="35" />
    <hkern u1="&#xc5;" u2="&#x2022;" k="35" />
    <hkern u1="&#xc5;" u2="&#x201d;" k="170" />
    <hkern u1="&#xc5;" u2="&#x201c;" k="170" />
    <hkern u1="&#xc5;" u2="&#x2019;" k="170" />
    <hkern u1="&#xc5;" u2="&#x2018;" k="170" />
    <hkern u1="&#xc5;" u2="&#x2014;" k="35" />
    <hkern u1="&#xc5;" u2="&#x2013;" k="35" />
    <hkern u1="&#xc5;" u2="&#x178;" k="140" />
    <hkern u1="&#xc5;" u2="&#x152;" k="30" />
    <hkern u1="&#xc5;" u2="&#x106;" k="30" />
    <hkern u1="&#xc5;" u2="&#xdd;" k="140" />
    <hkern u1="&#xc5;" u2="&#xdc;" k="58" />
    <hkern u1="&#xc5;" u2="&#xdb;" k="58" />
    <hkern u1="&#xc5;" u2="&#xda;" k="58" />
    <hkern u1="&#xc5;" u2="&#xd9;" k="58" />
    <hkern u1="&#xc5;" u2="&#xd8;" k="30" />
    <hkern u1="&#xc5;" u2="&#xd6;" k="30" />
    <hkern u1="&#xc5;" u2="&#xd5;" k="30" />
    <hkern u1="&#xc5;" u2="&#xd4;" k="30" />
    <hkern u1="&#xc5;" u2="&#xd3;" k="30" />
    <hkern u1="&#xc5;" u2="&#xd2;" k="30" />
    <hkern u1="&#xc5;" u2="&#xc7;" k="30" />
    <hkern u1="&#xc5;" u2="&#xbb;" k="35" />
    <hkern u1="&#xc5;" u2="&#xba;" k="170" />
    <hkern u1="&#xc5;" u2="&#xb9;" k="160" />
    <hkern u1="&#xc5;" u2="&#xb7;" k="35" />
    <hkern u1="&#xc5;" u2="&#xb3;" k="160" />
    <hkern u1="&#xc5;" u2="&#xb2;" k="160" />
    <hkern u1="&#xc5;" u2="&#xb0;" k="170" />
    <hkern u1="&#xc5;" u2="&#xad;" k="35" />
    <hkern u1="&#xc5;" u2="&#xab;" k="35" />
    <hkern u1="&#xc5;" u2="&#xaa;" k="170" />
    <hkern u1="&#xc5;" u2="y" k="70" />
    <hkern u1="&#xc5;" u2="v" k="70" />
    <hkern u1="&#xc5;" u2="\" k="95" />
    <hkern u1="&#xc5;" u2="Y" k="140" />
    <hkern u1="&#xc5;" u2="W" k="60" />
    <hkern u1="&#xc5;" u2="V" k="95" />
    <hkern u1="&#xc5;" u2="U" k="58" />
    <hkern u1="&#xc5;" u2="T" k="115" />
    <hkern u1="&#xc5;" u2="Q" k="30" />
    <hkern u1="&#xc5;" u2="O" k="30" />
    <hkern u1="&#xc5;" u2="J" k="-45" />
    <hkern u1="&#xc5;" u2="G" k="30" />
    <hkern u1="&#xc5;" u2="C" k="30" />
    <hkern u1="&#xc5;" u2="&#x40;" k="30" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="48" />
    <hkern u1="&#xc5;" u2="&#x2d;" k="35" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="170" />
    <hkern u1="&#xc5;" u2="&#x27;" k="170" />
    <hkern u1="&#xc5;" u2="&#x22;" k="170" />
    <hkern u1="&#xc7;" u2="&#x203a;" k="155" />
    <hkern u1="&#xc7;" u2="&#x2039;" k="155" />
    <hkern u1="&#xc7;" u2="&#x2022;" k="155" />
    <hkern u1="&#xc7;" u2="&#x2014;" k="155" />
    <hkern u1="&#xc7;" u2="&#x2013;" k="155" />
    <hkern u1="&#xc7;" u2="&#xbb;" k="155" />
    <hkern u1="&#xc7;" u2="&#xb7;" k="155" />
    <hkern u1="&#xc7;" u2="&#xad;" k="155" />
    <hkern u1="&#xc7;" u2="&#xab;" k="155" />
    <hkern u1="&#xc7;" u2="&#x2d;" k="155" />
    <hkern u1="&#xd0;" u2="&#x2206;" k="30" />
    <hkern u1="&#xd0;" u2="&#x2122;" k="48" />
    <hkern u1="&#xd0;" u2="&#x201e;" k="58" />
    <hkern u1="&#xd0;" u2="&#x201d;" k="48" />
    <hkern u1="&#xd0;" u2="&#x201c;" k="48" />
    <hkern u1="&#xd0;" u2="&#x201a;" k="58" />
    <hkern u1="&#xd0;" u2="&#x2019;" k="48" />
    <hkern u1="&#xd0;" u2="&#x2018;" k="48" />
    <hkern u1="&#xd0;" u2="&#x17d;" k="75" />
    <hkern u1="&#xd0;" u2="&#x17b;" k="75" />
    <hkern u1="&#xd0;" u2="&#x179;" k="75" />
    <hkern u1="&#xd0;" u2="&#x178;" k="80" />
    <hkern u1="&#xd0;" u2="&#x104;" k="30" />
    <hkern u1="&#xd0;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd0;" u2="&#xc6;" k="30" />
    <hkern u1="&#xd0;" u2="&#xc5;" k="30" />
    <hkern u1="&#xd0;" u2="&#xc4;" k="30" />
    <hkern u1="&#xd0;" u2="&#xc3;" k="30" />
    <hkern u1="&#xd0;" u2="&#xc2;" k="30" />
    <hkern u1="&#xd0;" u2="&#xc1;" k="30" />
    <hkern u1="&#xd0;" u2="&#xc0;" k="30" />
    <hkern u1="&#xd0;" u2="&#xba;" k="48" />
    <hkern u1="&#xd0;" u2="&#xb0;" k="48" />
    <hkern u1="&#xd0;" u2="&#xaa;" k="48" />
    <hkern u1="&#xd0;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd0;" u2="]" k="40" />
    <hkern u1="&#xd0;" u2="\" k="45" />
    <hkern u1="&#xd0;" u2="Z" k="75" />
    <hkern u1="&#xd0;" u2="Y" k="80" />
    <hkern u1="&#xd0;" u2="X" k="30" />
    <hkern u1="&#xd0;" u2="V" k="45" />
    <hkern u1="&#xd0;" u2="T" k="120" />
    <hkern u1="&#xd0;" u2="A" k="30" />
    <hkern u1="&#xd0;" u2="&#x2f;" k="30" />
    <hkern u1="&#xd0;" u2="&#x2e;" k="58" />
    <hkern u1="&#xd0;" u2="&#x2c;" k="58" />
    <hkern u1="&#xd0;" u2="&#x2a;" k="48" />
    <hkern u1="&#xd0;" u2="&#x29;" k="40" />
    <hkern u1="&#xd0;" u2="&#x27;" k="48" />
    <hkern u1="&#xd0;" u2="&#x26;" k="30" />
    <hkern u1="&#xd0;" u2="&#x22;" k="48" />
    <hkern u1="&#xd2;" u2="&#x2206;" k="30" />
    <hkern u1="&#xd2;" u2="&#x2122;" k="48" />
    <hkern u1="&#xd2;" u2="&#x201e;" k="58" />
    <hkern u1="&#xd2;" u2="&#x201d;" k="48" />
    <hkern u1="&#xd2;" u2="&#x201c;" k="48" />
    <hkern u1="&#xd2;" u2="&#x201a;" k="58" />
    <hkern u1="&#xd2;" u2="&#x2019;" k="48" />
    <hkern u1="&#xd2;" u2="&#x2018;" k="48" />
    <hkern u1="&#xd2;" u2="&#x17d;" k="75" />
    <hkern u1="&#xd2;" u2="&#x17b;" k="75" />
    <hkern u1="&#xd2;" u2="&#x179;" k="75" />
    <hkern u1="&#xd2;" u2="&#x178;" k="80" />
    <hkern u1="&#xd2;" u2="&#x104;" k="30" />
    <hkern u1="&#xd2;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd2;" u2="&#xc6;" k="30" />
    <hkern u1="&#xd2;" u2="&#xc5;" k="30" />
    <hkern u1="&#xd2;" u2="&#xc4;" k="30" />
    <hkern u1="&#xd2;" u2="&#xc3;" k="30" />
    <hkern u1="&#xd2;" u2="&#xc2;" k="30" />
    <hkern u1="&#xd2;" u2="&#xc1;" k="30" />
    <hkern u1="&#xd2;" u2="&#xc0;" k="30" />
    <hkern u1="&#xd2;" u2="&#xba;" k="48" />
    <hkern u1="&#xd2;" u2="&#xb0;" k="48" />
    <hkern u1="&#xd2;" u2="&#xaa;" k="48" />
    <hkern u1="&#xd2;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd2;" u2="]" k="40" />
    <hkern u1="&#xd2;" u2="\" k="45" />
    <hkern u1="&#xd2;" u2="Z" k="75" />
    <hkern u1="&#xd2;" u2="Y" k="80" />
    <hkern u1="&#xd2;" u2="X" k="30" />
    <hkern u1="&#xd2;" u2="V" k="45" />
    <hkern u1="&#xd2;" u2="T" k="120" />
    <hkern u1="&#xd2;" u2="A" k="30" />
    <hkern u1="&#xd2;" u2="&#x2f;" k="30" />
    <hkern u1="&#xd2;" u2="&#x2e;" k="58" />
    <hkern u1="&#xd2;" u2="&#x2c;" k="58" />
    <hkern u1="&#xd2;" u2="&#x2a;" k="48" />
    <hkern u1="&#xd2;" u2="&#x29;" k="40" />
    <hkern u1="&#xd2;" u2="&#x27;" k="48" />
    <hkern u1="&#xd2;" u2="&#x26;" k="30" />
    <hkern u1="&#xd2;" u2="&#x22;" k="48" />
    <hkern u1="&#xd3;" u2="&#x2206;" k="30" />
    <hkern u1="&#xd3;" u2="&#x2122;" k="48" />
    <hkern u1="&#xd3;" u2="&#x201e;" k="58" />
    <hkern u1="&#xd3;" u2="&#x201d;" k="48" />
    <hkern u1="&#xd3;" u2="&#x201c;" k="48" />
    <hkern u1="&#xd3;" u2="&#x201a;" k="58" />
    <hkern u1="&#xd3;" u2="&#x2019;" k="48" />
    <hkern u1="&#xd3;" u2="&#x2018;" k="48" />
    <hkern u1="&#xd3;" u2="&#x17d;" k="75" />
    <hkern u1="&#xd3;" u2="&#x17b;" k="75" />
    <hkern u1="&#xd3;" u2="&#x179;" k="75" />
    <hkern u1="&#xd3;" u2="&#x178;" k="80" />
    <hkern u1="&#xd3;" u2="&#x104;" k="30" />
    <hkern u1="&#xd3;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd3;" u2="&#xc6;" k="30" />
    <hkern u1="&#xd3;" u2="&#xc5;" k="30" />
    <hkern u1="&#xd3;" u2="&#xc4;" k="30" />
    <hkern u1="&#xd3;" u2="&#xc3;" k="30" />
    <hkern u1="&#xd3;" u2="&#xc2;" k="30" />
    <hkern u1="&#xd3;" u2="&#xc1;" k="30" />
    <hkern u1="&#xd3;" u2="&#xc0;" k="30" />
    <hkern u1="&#xd3;" u2="&#xba;" k="48" />
    <hkern u1="&#xd3;" u2="&#xb0;" k="48" />
    <hkern u1="&#xd3;" u2="&#xaa;" k="48" />
    <hkern u1="&#xd3;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd3;" u2="]" k="40" />
    <hkern u1="&#xd3;" u2="\" k="45" />
    <hkern u1="&#xd3;" u2="Z" k="75" />
    <hkern u1="&#xd3;" u2="Y" k="80" />
    <hkern u1="&#xd3;" u2="X" k="30" />
    <hkern u1="&#xd3;" u2="V" k="45" />
    <hkern u1="&#xd3;" u2="T" k="120" />
    <hkern u1="&#xd3;" u2="A" k="30" />
    <hkern u1="&#xd3;" u2="&#x2f;" k="30" />
    <hkern u1="&#xd3;" u2="&#x2e;" k="58" />
    <hkern u1="&#xd3;" u2="&#x2c;" k="58" />
    <hkern u1="&#xd3;" u2="&#x2a;" k="48" />
    <hkern u1="&#xd3;" u2="&#x29;" k="40" />
    <hkern u1="&#xd3;" u2="&#x27;" k="48" />
    <hkern u1="&#xd3;" u2="&#x26;" k="30" />
    <hkern u1="&#xd3;" u2="&#x22;" k="48" />
    <hkern u1="&#xd4;" u2="&#x2206;" k="30" />
    <hkern u1="&#xd4;" u2="&#x2122;" k="48" />
    <hkern u1="&#xd4;" u2="&#x201e;" k="58" />
    <hkern u1="&#xd4;" u2="&#x201d;" k="48" />
    <hkern u1="&#xd4;" u2="&#x201c;" k="48" />
    <hkern u1="&#xd4;" u2="&#x201a;" k="58" />
    <hkern u1="&#xd4;" u2="&#x2019;" k="48" />
    <hkern u1="&#xd4;" u2="&#x2018;" k="48" />
    <hkern u1="&#xd4;" u2="&#x17d;" k="75" />
    <hkern u1="&#xd4;" u2="&#x17b;" k="75" />
    <hkern u1="&#xd4;" u2="&#x179;" k="75" />
    <hkern u1="&#xd4;" u2="&#x178;" k="80" />
    <hkern u1="&#xd4;" u2="&#x104;" k="30" />
    <hkern u1="&#xd4;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd4;" u2="&#xc6;" k="30" />
    <hkern u1="&#xd4;" u2="&#xc5;" k="30" />
    <hkern u1="&#xd4;" u2="&#xc4;" k="30" />
    <hkern u1="&#xd4;" u2="&#xc3;" k="30" />
    <hkern u1="&#xd4;" u2="&#xc2;" k="30" />
    <hkern u1="&#xd4;" u2="&#xc1;" k="30" />
    <hkern u1="&#xd4;" u2="&#xc0;" k="30" />
    <hkern u1="&#xd4;" u2="&#xba;" k="48" />
    <hkern u1="&#xd4;" u2="&#xb0;" k="48" />
    <hkern u1="&#xd4;" u2="&#xaa;" k="48" />
    <hkern u1="&#xd4;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd4;" u2="]" k="40" />
    <hkern u1="&#xd4;" u2="\" k="45" />
    <hkern u1="&#xd4;" u2="Z" k="75" />
    <hkern u1="&#xd4;" u2="Y" k="80" />
    <hkern u1="&#xd4;" u2="X" k="30" />
    <hkern u1="&#xd4;" u2="V" k="45" />
    <hkern u1="&#xd4;" u2="T" k="120" />
    <hkern u1="&#xd4;" u2="A" k="30" />
    <hkern u1="&#xd4;" u2="&#x2f;" k="30" />
    <hkern u1="&#xd4;" u2="&#x2e;" k="58" />
    <hkern u1="&#xd4;" u2="&#x2c;" k="58" />
    <hkern u1="&#xd4;" u2="&#x2a;" k="48" />
    <hkern u1="&#xd4;" u2="&#x29;" k="40" />
    <hkern u1="&#xd4;" u2="&#x27;" k="48" />
    <hkern u1="&#xd4;" u2="&#x26;" k="30" />
    <hkern u1="&#xd4;" u2="&#x22;" k="48" />
    <hkern u1="&#xd5;" u2="&#x2206;" k="30" />
    <hkern u1="&#xd5;" u2="&#x2122;" k="48" />
    <hkern u1="&#xd5;" u2="&#x201e;" k="58" />
    <hkern u1="&#xd5;" u2="&#x201d;" k="48" />
    <hkern u1="&#xd5;" u2="&#x201c;" k="48" />
    <hkern u1="&#xd5;" u2="&#x201a;" k="58" />
    <hkern u1="&#xd5;" u2="&#x2019;" k="48" />
    <hkern u1="&#xd5;" u2="&#x2018;" k="48" />
    <hkern u1="&#xd5;" u2="&#x17d;" k="75" />
    <hkern u1="&#xd5;" u2="&#x17b;" k="75" />
    <hkern u1="&#xd5;" u2="&#x179;" k="75" />
    <hkern u1="&#xd5;" u2="&#x178;" k="80" />
    <hkern u1="&#xd5;" u2="&#x104;" k="30" />
    <hkern u1="&#xd5;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd5;" u2="&#xc6;" k="30" />
    <hkern u1="&#xd5;" u2="&#xc5;" k="30" />
    <hkern u1="&#xd5;" u2="&#xc4;" k="30" />
    <hkern u1="&#xd5;" u2="&#xc3;" k="30" />
    <hkern u1="&#xd5;" u2="&#xc2;" k="30" />
    <hkern u1="&#xd5;" u2="&#xc1;" k="30" />
    <hkern u1="&#xd5;" u2="&#xc0;" k="30" />
    <hkern u1="&#xd5;" u2="&#xba;" k="48" />
    <hkern u1="&#xd5;" u2="&#xb0;" k="48" />
    <hkern u1="&#xd5;" u2="&#xaa;" k="48" />
    <hkern u1="&#xd5;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd5;" u2="]" k="40" />
    <hkern u1="&#xd5;" u2="\" k="45" />
    <hkern u1="&#xd5;" u2="Z" k="75" />
    <hkern u1="&#xd5;" u2="Y" k="80" />
    <hkern u1="&#xd5;" u2="X" k="30" />
    <hkern u1="&#xd5;" u2="V" k="45" />
    <hkern u1="&#xd5;" u2="T" k="120" />
    <hkern u1="&#xd5;" u2="A" k="30" />
    <hkern u1="&#xd5;" u2="&#x2f;" k="30" />
    <hkern u1="&#xd5;" u2="&#x2e;" k="58" />
    <hkern u1="&#xd5;" u2="&#x2c;" k="58" />
    <hkern u1="&#xd5;" u2="&#x2a;" k="48" />
    <hkern u1="&#xd5;" u2="&#x29;" k="40" />
    <hkern u1="&#xd5;" u2="&#x27;" k="48" />
    <hkern u1="&#xd5;" u2="&#x26;" k="30" />
    <hkern u1="&#xd5;" u2="&#x22;" k="48" />
    <hkern u1="&#xd6;" u2="&#x2206;" k="30" />
    <hkern u1="&#xd6;" u2="&#x2122;" k="48" />
    <hkern u1="&#xd6;" u2="&#x201e;" k="58" />
    <hkern u1="&#xd6;" u2="&#x201d;" k="48" />
    <hkern u1="&#xd6;" u2="&#x201c;" k="48" />
    <hkern u1="&#xd6;" u2="&#x201a;" k="58" />
    <hkern u1="&#xd6;" u2="&#x2019;" k="48" />
    <hkern u1="&#xd6;" u2="&#x2018;" k="48" />
    <hkern u1="&#xd6;" u2="&#x17d;" k="75" />
    <hkern u1="&#xd6;" u2="&#x17b;" k="75" />
    <hkern u1="&#xd6;" u2="&#x179;" k="75" />
    <hkern u1="&#xd6;" u2="&#x178;" k="80" />
    <hkern u1="&#xd6;" u2="&#x104;" k="30" />
    <hkern u1="&#xd6;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd6;" u2="&#xc6;" k="30" />
    <hkern u1="&#xd6;" u2="&#xc5;" k="30" />
    <hkern u1="&#xd6;" u2="&#xc4;" k="30" />
    <hkern u1="&#xd6;" u2="&#xc3;" k="30" />
    <hkern u1="&#xd6;" u2="&#xc2;" k="30" />
    <hkern u1="&#xd6;" u2="&#xc1;" k="30" />
    <hkern u1="&#xd6;" u2="&#xc0;" k="30" />
    <hkern u1="&#xd6;" u2="&#xba;" k="48" />
    <hkern u1="&#xd6;" u2="&#xb0;" k="48" />
    <hkern u1="&#xd6;" u2="&#xaa;" k="48" />
    <hkern u1="&#xd6;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd6;" u2="]" k="40" />
    <hkern u1="&#xd6;" u2="\" k="45" />
    <hkern u1="&#xd6;" u2="Z" k="75" />
    <hkern u1="&#xd6;" u2="Y" k="80" />
    <hkern u1="&#xd6;" u2="X" k="30" />
    <hkern u1="&#xd6;" u2="V" k="45" />
    <hkern u1="&#xd6;" u2="T" k="120" />
    <hkern u1="&#xd6;" u2="A" k="30" />
    <hkern u1="&#xd6;" u2="&#x2f;" k="30" />
    <hkern u1="&#xd6;" u2="&#x2e;" k="58" />
    <hkern u1="&#xd6;" u2="&#x2c;" k="58" />
    <hkern u1="&#xd6;" u2="&#x2a;" k="48" />
    <hkern u1="&#xd6;" u2="&#x29;" k="40" />
    <hkern u1="&#xd6;" u2="&#x27;" k="48" />
    <hkern u1="&#xd6;" u2="&#x26;" k="30" />
    <hkern u1="&#xd6;" u2="&#x22;" k="48" />
    <hkern u1="&#xd9;" u2="&#x2206;" k="58" />
    <hkern u1="&#xd9;" u2="&#x201e;" k="50" />
    <hkern u1="&#xd9;" u2="&#x201a;" k="50" />
    <hkern u1="&#xd9;" u2="&#x104;" k="58" />
    <hkern u1="&#xd9;" u2="&#xc6;" k="58" />
    <hkern u1="&#xd9;" u2="&#xc5;" k="58" />
    <hkern u1="&#xd9;" u2="&#xc4;" k="58" />
    <hkern u1="&#xd9;" u2="&#xc3;" k="58" />
    <hkern u1="&#xd9;" u2="&#xc2;" k="58" />
    <hkern u1="&#xd9;" u2="&#xc1;" k="58" />
    <hkern u1="&#xd9;" u2="&#xc0;" k="58" />
    <hkern u1="&#xd9;" u2="A" k="58" />
    <hkern u1="&#xd9;" u2="&#x2f;" k="58" />
    <hkern u1="&#xd9;" u2="&#x2e;" k="50" />
    <hkern u1="&#xd9;" u2="&#x2c;" k="50" />
    <hkern u1="&#xd9;" u2="&#x26;" k="58" />
    <hkern u1="&#xda;" u2="&#x2206;" k="58" />
    <hkern u1="&#xda;" u2="&#x201e;" k="50" />
    <hkern u1="&#xda;" u2="&#x201a;" k="50" />
    <hkern u1="&#xda;" u2="&#x104;" k="58" />
    <hkern u1="&#xda;" u2="&#xc6;" k="58" />
    <hkern u1="&#xda;" u2="&#xc5;" k="58" />
    <hkern u1="&#xda;" u2="&#xc4;" k="58" />
    <hkern u1="&#xda;" u2="&#xc3;" k="58" />
    <hkern u1="&#xda;" u2="&#xc2;" k="58" />
    <hkern u1="&#xda;" u2="&#xc1;" k="58" />
    <hkern u1="&#xda;" u2="&#xc0;" k="58" />
    <hkern u1="&#xda;" u2="A" k="58" />
    <hkern u1="&#xda;" u2="&#x2f;" k="58" />
    <hkern u1="&#xda;" u2="&#x2e;" k="50" />
    <hkern u1="&#xda;" u2="&#x2c;" k="50" />
    <hkern u1="&#xda;" u2="&#x26;" k="58" />
    <hkern u1="&#xdb;" u2="&#x2206;" k="58" />
    <hkern u1="&#xdb;" u2="&#x201e;" k="50" />
    <hkern u1="&#xdb;" u2="&#x201a;" k="50" />
    <hkern u1="&#xdb;" u2="&#x104;" k="58" />
    <hkern u1="&#xdb;" u2="&#xc6;" k="58" />
    <hkern u1="&#xdb;" u2="&#xc5;" k="58" />
    <hkern u1="&#xdb;" u2="&#xc4;" k="58" />
    <hkern u1="&#xdb;" u2="&#xc3;" k="58" />
    <hkern u1="&#xdb;" u2="&#xc2;" k="58" />
    <hkern u1="&#xdb;" u2="&#xc1;" k="58" />
    <hkern u1="&#xdb;" u2="&#xc0;" k="58" />
    <hkern u1="&#xdb;" u2="A" k="58" />
    <hkern u1="&#xdb;" u2="&#x2f;" k="58" />
    <hkern u1="&#xdb;" u2="&#x2e;" k="50" />
    <hkern u1="&#xdb;" u2="&#x2c;" k="50" />
    <hkern u1="&#xdb;" u2="&#x26;" k="58" />
    <hkern u1="&#xdc;" u2="&#x2206;" k="58" />
    <hkern u1="&#xdc;" u2="&#x201e;" k="50" />
    <hkern u1="&#xdc;" u2="&#x201a;" k="50" />
    <hkern u1="&#xdc;" u2="&#x104;" k="58" />
    <hkern u1="&#xdc;" u2="&#xc6;" k="58" />
    <hkern u1="&#xdc;" u2="&#xc5;" k="58" />
    <hkern u1="&#xdc;" u2="&#xc4;" k="58" />
    <hkern u1="&#xdc;" u2="&#xc3;" k="58" />
    <hkern u1="&#xdc;" u2="&#xc2;" k="58" />
    <hkern u1="&#xdc;" u2="&#xc1;" k="58" />
    <hkern u1="&#xdc;" u2="&#xc0;" k="58" />
    <hkern u1="&#xdc;" u2="A" k="58" />
    <hkern u1="&#xdc;" u2="&#x2f;" k="58" />
    <hkern u1="&#xdc;" u2="&#x2e;" k="50" />
    <hkern u1="&#xdc;" u2="&#x2c;" k="50" />
    <hkern u1="&#xdc;" u2="&#x26;" k="58" />
    <hkern u1="&#xdd;" u2="&#x2206;" k="140" />
    <hkern u1="&#xdd;" u2="&#x2122;" k="-25" />
    <hkern u1="&#xdd;" u2="&#x203a;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2039;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2022;" k="160" />
    <hkern u1="&#xdd;" u2="&#x201e;" k="135" />
    <hkern u1="&#xdd;" u2="&#x201d;" k="-25" />
    <hkern u1="&#xdd;" u2="&#x201c;" k="-25" />
    <hkern u1="&#xdd;" u2="&#x201a;" k="135" />
    <hkern u1="&#xdd;" u2="&#x2019;" k="-25" />
    <hkern u1="&#xdd;" u2="&#x2018;" k="-25" />
    <hkern u1="&#xdd;" u2="&#x2014;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2013;" k="160" />
    <hkern u1="&#xdd;" u2="&#x153;" k="160" />
    <hkern u1="&#xdd;" u2="&#x152;" k="80" />
    <hkern u1="&#xdd;" u2="&#x144;" k="110" />
    <hkern u1="&#xdd;" u2="&#x119;" k="160" />
    <hkern u1="&#xdd;" u2="&#x107;" k="160" />
    <hkern u1="&#xdd;" u2="&#x106;" k="80" />
    <hkern u1="&#xdd;" u2="&#x105;" k="108" />
    <hkern u1="&#xdd;" u2="&#x104;" k="140" />
    <hkern u1="&#xdd;" u2="&#xfc;" k="110" />
    <hkern u1="&#xdd;" u2="&#xfb;" k="110" />
    <hkern u1="&#xdd;" u2="&#xfa;" k="110" />
    <hkern u1="&#xdd;" u2="&#xf9;" k="110" />
    <hkern u1="&#xdd;" u2="&#xf8;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf6;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf5;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf4;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf3;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf2;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf1;" k="110" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="160" />
    <hkern u1="&#xdd;" u2="&#xeb;" k="160" />
    <hkern u1="&#xdd;" u2="&#xea;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe9;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe8;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe7;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe6;" k="108" />
    <hkern u1="&#xdd;" u2="&#xe5;" k="108" />
    <hkern u1="&#xdd;" u2="&#xe4;" k="108" />
    <hkern u1="&#xdd;" u2="&#xe3;" k="108" />
    <hkern u1="&#xdd;" u2="&#xe2;" k="108" />
    <hkern u1="&#xdd;" u2="&#xe1;" k="108" />
    <hkern u1="&#xdd;" u2="&#xe0;" k="108" />
    <hkern u1="&#xdd;" u2="&#xd8;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd6;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd5;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd4;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd3;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd2;" k="80" />
    <hkern u1="&#xdd;" u2="&#xc7;" k="80" />
    <hkern u1="&#xdd;" u2="&#xc6;" k="140" />
    <hkern u1="&#xdd;" u2="&#xc5;" k="140" />
    <hkern u1="&#xdd;" u2="&#xc4;" k="140" />
    <hkern u1="&#xdd;" u2="&#xc3;" k="140" />
    <hkern u1="&#xdd;" u2="&#xc2;" k="140" />
    <hkern u1="&#xdd;" u2="&#xc1;" k="140" />
    <hkern u1="&#xdd;" u2="&#xc0;" k="140" />
    <hkern u1="&#xdd;" u2="&#xbb;" k="160" />
    <hkern u1="&#xdd;" u2="&#xba;" k="-25" />
    <hkern u1="&#xdd;" u2="&#xb9;" k="-45" />
    <hkern u1="&#xdd;" u2="&#xb7;" k="160" />
    <hkern u1="&#xdd;" u2="&#xb5;" k="110" />
    <hkern u1="&#xdd;" u2="&#xb3;" k="-45" />
    <hkern u1="&#xdd;" u2="&#xb2;" k="-45" />
    <hkern u1="&#xdd;" u2="&#xb0;" k="-25" />
    <hkern u1="&#xdd;" u2="&#xad;" k="160" />
    <hkern u1="&#xdd;" u2="&#xab;" k="160" />
    <hkern u1="&#xdd;" u2="&#xaa;" k="-25" />
    <hkern u1="&#xdd;" u2="y" k="100" />
    <hkern u1="&#xdd;" u2="x" k="125" />
    <hkern u1="&#xdd;" u2="w" k="85" />
    <hkern u1="&#xdd;" u2="v" k="100" />
    <hkern u1="&#xdd;" u2="u" k="110" />
    <hkern u1="&#xdd;" u2="s" k="113" />
    <hkern u1="&#xdd;" u2="r" k="110" />
    <hkern u1="&#xdd;" u2="q" k="160" />
    <hkern u1="&#xdd;" u2="p" k="110" />
    <hkern u1="&#xdd;" u2="o" k="160" />
    <hkern u1="&#xdd;" u2="n" k="110" />
    <hkern u1="&#xdd;" u2="m" k="110" />
    <hkern u1="&#xdd;" u2="g" k="165" />
    <hkern u1="&#xdd;" u2="e" k="160" />
    <hkern u1="&#xdd;" u2="d" k="160" />
    <hkern u1="&#xdd;" u2="c" k="160" />
    <hkern u1="&#xdd;" u2="a" k="108" />
    <hkern u1="&#xdd;" u2="Q" k="80" />
    <hkern u1="&#xdd;" u2="O" k="80" />
    <hkern u1="&#xdd;" u2="J" k="200" />
    <hkern u1="&#xdd;" u2="G" k="80" />
    <hkern u1="&#xdd;" u2="C" k="80" />
    <hkern u1="&#xdd;" u2="A" k="140" />
    <hkern u1="&#xdd;" u2="&#x40;" k="80" />
    <hkern u1="&#xdd;" u2="&#x3f;" k="-37" />
    <hkern u1="&#xdd;" u2="&#x3b;" k="110" />
    <hkern u1="&#xdd;" u2="&#x3a;" k="110" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="140" />
    <hkern u1="&#xdd;" u2="&#x2e;" k="135" />
    <hkern u1="&#xdd;" u2="&#x2d;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2c;" k="135" />
    <hkern u1="&#xdd;" u2="&#x2a;" k="-25" />
    <hkern u1="&#xdd;" u2="&#x27;" k="-25" />
    <hkern u1="&#xdd;" u2="&#x26;" k="140" />
    <hkern u1="&#xdd;" u2="&#x22;" k="-25" />
    <hkern u1="&#xde;" u2="&#x2206;" k="30" />
    <hkern u1="&#xde;" u2="&#x2122;" k="48" />
    <hkern u1="&#xde;" u2="&#x201e;" k="58" />
    <hkern u1="&#xde;" u2="&#x201d;" k="48" />
    <hkern u1="&#xde;" u2="&#x201c;" k="48" />
    <hkern u1="&#xde;" u2="&#x201a;" k="58" />
    <hkern u1="&#xde;" u2="&#x2019;" k="48" />
    <hkern u1="&#xde;" u2="&#x2018;" k="48" />
    <hkern u1="&#xde;" u2="&#x17d;" k="75" />
    <hkern u1="&#xde;" u2="&#x17b;" k="75" />
    <hkern u1="&#xde;" u2="&#x179;" k="75" />
    <hkern u1="&#xde;" u2="&#x178;" k="80" />
    <hkern u1="&#xde;" u2="&#x104;" k="30" />
    <hkern u1="&#xde;" u2="&#xdd;" k="80" />
    <hkern u1="&#xde;" u2="&#xc6;" k="30" />
    <hkern u1="&#xde;" u2="&#xc5;" k="30" />
    <hkern u1="&#xde;" u2="&#xc4;" k="30" />
    <hkern u1="&#xde;" u2="&#xc3;" k="30" />
    <hkern u1="&#xde;" u2="&#xc2;" k="30" />
    <hkern u1="&#xde;" u2="&#xc1;" k="30" />
    <hkern u1="&#xde;" u2="&#xc0;" k="30" />
    <hkern u1="&#xde;" u2="&#xba;" k="48" />
    <hkern u1="&#xde;" u2="&#xb0;" k="48" />
    <hkern u1="&#xde;" u2="&#xaa;" k="48" />
    <hkern u1="&#xde;" u2="&#x7d;" k="40" />
    <hkern u1="&#xde;" u2="]" k="40" />
    <hkern u1="&#xde;" u2="\" k="45" />
    <hkern u1="&#xde;" u2="Z" k="75" />
    <hkern u1="&#xde;" u2="Y" k="80" />
    <hkern u1="&#xde;" u2="X" k="30" />
    <hkern u1="&#xde;" u2="V" k="45" />
    <hkern u1="&#xde;" u2="T" k="120" />
    <hkern u1="&#xde;" u2="A" k="30" />
    <hkern u1="&#xde;" u2="&#x2f;" k="30" />
    <hkern u1="&#xde;" u2="&#x2e;" k="58" />
    <hkern u1="&#xde;" u2="&#x2c;" k="58" />
    <hkern u1="&#xde;" u2="&#x2a;" k="48" />
    <hkern u1="&#xde;" u2="&#x29;" k="40" />
    <hkern u1="&#xde;" u2="&#x27;" k="48" />
    <hkern u1="&#xde;" u2="&#x26;" k="30" />
    <hkern u1="&#xde;" u2="&#x22;" k="48" />
    <hkern u1="&#xe0;" u2="&#x2122;" k="65" />
    <hkern u1="&#xe0;" u2="&#x201d;" k="65" />
    <hkern u1="&#xe0;" u2="&#x201c;" k="65" />
    <hkern u1="&#xe0;" u2="&#x2019;" k="65" />
    <hkern u1="&#xe0;" u2="&#x2018;" k="65" />
    <hkern u1="&#xe0;" u2="&#xba;" k="65" />
    <hkern u1="&#xe0;" u2="&#xb9;" k="65" />
    <hkern u1="&#xe0;" u2="&#xb3;" k="65" />
    <hkern u1="&#xe0;" u2="&#xb2;" k="65" />
    <hkern u1="&#xe0;" u2="&#xb0;" k="65" />
    <hkern u1="&#xe0;" u2="&#xaa;" k="65" />
    <hkern u1="&#xe0;" u2="y" k="25" />
    <hkern u1="&#xe0;" u2="w" k="13" />
    <hkern u1="&#xe0;" u2="v" k="25" />
    <hkern u1="&#xe0;" u2="&#x2a;" k="65" />
    <hkern u1="&#xe0;" u2="&#x27;" k="65" />
    <hkern u1="&#xe0;" u2="&#x22;" k="65" />
    <hkern u1="&#xe1;" u2="&#x2122;" k="65" />
    <hkern u1="&#xe1;" u2="&#x201d;" k="65" />
    <hkern u1="&#xe1;" u2="&#x201c;" k="65" />
    <hkern u1="&#xe1;" u2="&#x2019;" k="65" />
    <hkern u1="&#xe1;" u2="&#x2018;" k="65" />
    <hkern u1="&#xe1;" u2="&#xba;" k="65" />
    <hkern u1="&#xe1;" u2="&#xb9;" k="65" />
    <hkern u1="&#xe1;" u2="&#xb3;" k="65" />
    <hkern u1="&#xe1;" u2="&#xb2;" k="65" />
    <hkern u1="&#xe1;" u2="&#xb0;" k="65" />
    <hkern u1="&#xe1;" u2="&#xaa;" k="65" />
    <hkern u1="&#xe1;" u2="y" k="25" />
    <hkern u1="&#xe1;" u2="w" k="13" />
    <hkern u1="&#xe1;" u2="v" k="25" />
    <hkern u1="&#xe1;" u2="&#x2a;" k="65" />
    <hkern u1="&#xe1;" u2="&#x27;" k="65" />
    <hkern u1="&#xe1;" u2="&#x22;" k="65" />
    <hkern u1="&#xe2;" u2="&#x2122;" k="65" />
    <hkern u1="&#xe2;" u2="&#x201d;" k="65" />
    <hkern u1="&#xe2;" u2="&#x201c;" k="65" />
    <hkern u1="&#xe2;" u2="&#x2019;" k="65" />
    <hkern u1="&#xe2;" u2="&#x2018;" k="65" />
    <hkern u1="&#xe2;" u2="&#xba;" k="65" />
    <hkern u1="&#xe2;" u2="&#xb9;" k="65" />
    <hkern u1="&#xe2;" u2="&#xb3;" k="65" />
    <hkern u1="&#xe2;" u2="&#xb2;" k="65" />
    <hkern u1="&#xe2;" u2="&#xb0;" k="65" />
    <hkern u1="&#xe2;" u2="&#xaa;" k="65" />
    <hkern u1="&#xe2;" u2="y" k="25" />
    <hkern u1="&#xe2;" u2="w" k="13" />
    <hkern u1="&#xe2;" u2="v" k="25" />
    <hkern u1="&#xe2;" u2="&#x2a;" k="65" />
    <hkern u1="&#xe2;" u2="&#x27;" k="65" />
    <hkern u1="&#xe2;" u2="&#x22;" k="65" />
    <hkern u1="&#xe3;" u2="&#x2122;" k="65" />
    <hkern u1="&#xe3;" u2="&#x201d;" k="65" />
    <hkern u1="&#xe3;" u2="&#x201c;" k="65" />
    <hkern u1="&#xe3;" u2="&#x2019;" k="65" />
    <hkern u1="&#xe3;" u2="&#x2018;" k="65" />
    <hkern u1="&#xe3;" u2="&#xba;" k="65" />
    <hkern u1="&#xe3;" u2="&#xb9;" k="65" />
    <hkern u1="&#xe3;" u2="&#xb3;" k="65" />
    <hkern u1="&#xe3;" u2="&#xb2;" k="65" />
    <hkern u1="&#xe3;" u2="&#xb0;" k="65" />
    <hkern u1="&#xe3;" u2="&#xaa;" k="65" />
    <hkern u1="&#xe3;" u2="y" k="25" />
    <hkern u1="&#xe3;" u2="w" k="13" />
    <hkern u1="&#xe3;" u2="v" k="25" />
    <hkern u1="&#xe3;" u2="&#x2a;" k="65" />
    <hkern u1="&#xe3;" u2="&#x27;" k="65" />
    <hkern u1="&#xe3;" u2="&#x22;" k="65" />
    <hkern u1="&#xe4;" u2="&#x2122;" k="65" />
    <hkern u1="&#xe4;" u2="&#x201d;" k="65" />
    <hkern u1="&#xe4;" u2="&#x201c;" k="65" />
    <hkern u1="&#xe4;" u2="&#x2019;" k="65" />
    <hkern u1="&#xe4;" u2="&#x2018;" k="65" />
    <hkern u1="&#xe4;" u2="&#xba;" k="65" />
    <hkern u1="&#xe4;" u2="&#xb9;" k="65" />
    <hkern u1="&#xe4;" u2="&#xb3;" k="65" />
    <hkern u1="&#xe4;" u2="&#xb2;" k="65" />
    <hkern u1="&#xe4;" u2="&#xb0;" k="65" />
    <hkern u1="&#xe4;" u2="&#xaa;" k="65" />
    <hkern u1="&#xe4;" u2="y" k="25" />
    <hkern u1="&#xe4;" u2="w" k="13" />
    <hkern u1="&#xe4;" u2="v" k="25" />
    <hkern u1="&#xe4;" u2="&#x2a;" k="65" />
    <hkern u1="&#xe4;" u2="&#x27;" k="65" />
    <hkern u1="&#xe4;" u2="&#x22;" k="65" />
    <hkern u1="&#xe5;" u2="&#x2122;" k="65" />
    <hkern u1="&#xe5;" u2="&#x201d;" k="65" />
    <hkern u1="&#xe5;" u2="&#x201c;" k="65" />
    <hkern u1="&#xe5;" u2="&#x2019;" k="65" />
    <hkern u1="&#xe5;" u2="&#x2018;" k="65" />
    <hkern u1="&#xe5;" u2="&#xba;" k="65" />
    <hkern u1="&#xe5;" u2="&#xb9;" k="65" />
    <hkern u1="&#xe5;" u2="&#xb3;" k="65" />
    <hkern u1="&#xe5;" u2="&#xb2;" k="65" />
    <hkern u1="&#xe5;" u2="&#xb0;" k="65" />
    <hkern u1="&#xe5;" u2="&#xaa;" k="65" />
    <hkern u1="&#xe5;" u2="y" k="25" />
    <hkern u1="&#xe5;" u2="w" k="13" />
    <hkern u1="&#xe5;" u2="v" k="25" />
    <hkern u1="&#xe5;" u2="&#x2a;" k="65" />
    <hkern u1="&#xe5;" u2="&#x27;" k="65" />
    <hkern u1="&#xe5;" u2="&#x22;" k="65" />
    <hkern u1="&#xe6;" u2="&#x2122;" k="85" />
    <hkern u1="&#xe6;" u2="&#x201d;" k="85" />
    <hkern u1="&#xe6;" u2="&#x201c;" k="85" />
    <hkern u1="&#xe6;" u2="&#x2019;" k="85" />
    <hkern u1="&#xe6;" u2="&#x2018;" k="85" />
    <hkern u1="&#xe6;" u2="&#xba;" k="85" />
    <hkern u1="&#xe6;" u2="&#xb0;" k="85" />
    <hkern u1="&#xe6;" u2="&#xaa;" k="85" />
    <hkern u1="&#xe6;" u2="&#x7d;" k="25" />
    <hkern u1="&#xe6;" u2="y" k="18" />
    <hkern u1="&#xe6;" u2="x" k="60" />
    <hkern u1="&#xe6;" u2="v" k="18" />
    <hkern u1="&#xe6;" u2="]" k="25" />
    <hkern u1="&#xe6;" u2="\" k="108" />
    <hkern u1="&#xe6;" u2="W" k="20" />
    <hkern u1="&#xe6;" u2="V" k="108" />
    <hkern u1="&#xe6;" u2="&#x2a;" k="85" />
    <hkern u1="&#xe6;" u2="&#x29;" k="25" />
    <hkern u1="&#xe6;" u2="&#x27;" k="85" />
    <hkern u1="&#xe6;" u2="&#x22;" k="85" />
    <hkern u1="&#xe8;" u2="&#x2122;" k="85" />
    <hkern u1="&#xe8;" u2="&#x201d;" k="85" />
    <hkern u1="&#xe8;" u2="&#x201c;" k="85" />
    <hkern u1="&#xe8;" u2="&#x2019;" k="85" />
    <hkern u1="&#xe8;" u2="&#x2018;" k="85" />
    <hkern u1="&#xe8;" u2="&#xba;" k="85" />
    <hkern u1="&#xe8;" u2="&#xb0;" k="85" />
    <hkern u1="&#xe8;" u2="&#xaa;" k="85" />
    <hkern u1="&#xe8;" u2="&#x7d;" k="25" />
    <hkern u1="&#xe8;" u2="y" k="18" />
    <hkern u1="&#xe8;" u2="x" k="60" />
    <hkern u1="&#xe8;" u2="v" k="18" />
    <hkern u1="&#xe8;" u2="]" k="25" />
    <hkern u1="&#xe8;" u2="\" k="108" />
    <hkern u1="&#xe8;" u2="W" k="20" />
    <hkern u1="&#xe8;" u2="V" k="108" />
    <hkern u1="&#xe8;" u2="&#x2a;" k="85" />
    <hkern u1="&#xe8;" u2="&#x29;" k="25" />
    <hkern u1="&#xe8;" u2="&#x27;" k="85" />
    <hkern u1="&#xe8;" u2="&#x22;" k="85" />
    <hkern u1="&#xe9;" u2="&#x2122;" k="85" />
    <hkern u1="&#xe9;" u2="&#x201d;" k="85" />
    <hkern u1="&#xe9;" u2="&#x201c;" k="85" />
    <hkern u1="&#xe9;" u2="&#x2019;" k="85" />
    <hkern u1="&#xe9;" u2="&#x2018;" k="85" />
    <hkern u1="&#xe9;" u2="&#xba;" k="85" />
    <hkern u1="&#xe9;" u2="&#xb0;" k="85" />
    <hkern u1="&#xe9;" u2="&#xaa;" k="85" />
    <hkern u1="&#xe9;" u2="&#x7d;" k="25" />
    <hkern u1="&#xe9;" u2="y" k="18" />
    <hkern u1="&#xe9;" u2="x" k="60" />
    <hkern u1="&#xe9;" u2="v" k="18" />
    <hkern u1="&#xe9;" u2="]" k="25" />
    <hkern u1="&#xe9;" u2="\" k="108" />
    <hkern u1="&#xe9;" u2="W" k="20" />
    <hkern u1="&#xe9;" u2="V" k="108" />
    <hkern u1="&#xe9;" u2="&#x2a;" k="85" />
    <hkern u1="&#xe9;" u2="&#x29;" k="25" />
    <hkern u1="&#xe9;" u2="&#x27;" k="85" />
    <hkern u1="&#xe9;" u2="&#x22;" k="85" />
    <hkern u1="&#xea;" u2="&#x2122;" k="85" />
    <hkern u1="&#xea;" u2="&#x201d;" k="85" />
    <hkern u1="&#xea;" u2="&#x201c;" k="85" />
    <hkern u1="&#xea;" u2="&#x2019;" k="85" />
    <hkern u1="&#xea;" u2="&#x2018;" k="85" />
    <hkern u1="&#xea;" u2="&#xba;" k="85" />
    <hkern u1="&#xea;" u2="&#xb0;" k="85" />
    <hkern u1="&#xea;" u2="&#xaa;" k="85" />
    <hkern u1="&#xea;" u2="&#x7d;" k="25" />
    <hkern u1="&#xea;" u2="y" k="18" />
    <hkern u1="&#xea;" u2="x" k="60" />
    <hkern u1="&#xea;" u2="v" k="18" />
    <hkern u1="&#xea;" u2="]" k="25" />
    <hkern u1="&#xea;" u2="\" k="108" />
    <hkern u1="&#xea;" u2="W" k="20" />
    <hkern u1="&#xea;" u2="V" k="108" />
    <hkern u1="&#xea;" u2="&#x2a;" k="85" />
    <hkern u1="&#xea;" u2="&#x29;" k="25" />
    <hkern u1="&#xea;" u2="&#x27;" k="85" />
    <hkern u1="&#xea;" u2="&#x22;" k="85" />
    <hkern u1="&#xeb;" u2="&#x2122;" k="85" />
    <hkern u1="&#xeb;" u2="&#x201d;" k="85" />
    <hkern u1="&#xeb;" u2="&#x201c;" k="85" />
    <hkern u1="&#xeb;" u2="&#x2019;" k="85" />
    <hkern u1="&#xeb;" u2="&#x2018;" k="85" />
    <hkern u1="&#xeb;" u2="&#xba;" k="85" />
    <hkern u1="&#xeb;" u2="&#xb0;" k="85" />
    <hkern u1="&#xeb;" u2="&#xaa;" k="85" />
    <hkern u1="&#xeb;" u2="&#x7d;" k="25" />
    <hkern u1="&#xeb;" u2="y" k="18" />
    <hkern u1="&#xeb;" u2="x" k="60" />
    <hkern u1="&#xeb;" u2="v" k="18" />
    <hkern u1="&#xeb;" u2="]" k="25" />
    <hkern u1="&#xeb;" u2="\" k="108" />
    <hkern u1="&#xeb;" u2="W" k="20" />
    <hkern u1="&#xeb;" u2="V" k="108" />
    <hkern u1="&#xeb;" u2="&#x2a;" k="85" />
    <hkern u1="&#xeb;" u2="&#x29;" k="25" />
    <hkern u1="&#xeb;" u2="&#x27;" k="85" />
    <hkern u1="&#xeb;" u2="&#x22;" k="85" />
    <hkern u1="&#xf1;" u2="&#x2122;" k="65" />
    <hkern u1="&#xf1;" u2="&#x201d;" k="65" />
    <hkern u1="&#xf1;" u2="&#x201c;" k="65" />
    <hkern u1="&#xf1;" u2="&#x2019;" k="65" />
    <hkern u1="&#xf1;" u2="&#x2018;" k="65" />
    <hkern u1="&#xf1;" u2="&#xba;" k="65" />
    <hkern u1="&#xf1;" u2="&#xb9;" k="65" />
    <hkern u1="&#xf1;" u2="&#xb3;" k="65" />
    <hkern u1="&#xf1;" u2="&#xb2;" k="65" />
    <hkern u1="&#xf1;" u2="&#xb0;" k="65" />
    <hkern u1="&#xf1;" u2="&#xaa;" k="65" />
    <hkern u1="&#xf1;" u2="y" k="25" />
    <hkern u1="&#xf1;" u2="w" k="13" />
    <hkern u1="&#xf1;" u2="v" k="25" />
    <hkern u1="&#xf1;" u2="&#x2a;" k="65" />
    <hkern u1="&#xf1;" u2="&#x27;" k="65" />
    <hkern u1="&#xf1;" u2="&#x22;" k="65" />
    <hkern u1="&#xf2;" u2="&#x2122;" k="85" />
    <hkern u1="&#xf2;" u2="&#x201d;" k="85" />
    <hkern u1="&#xf2;" u2="&#x201c;" k="85" />
    <hkern u1="&#xf2;" u2="&#x2019;" k="85" />
    <hkern u1="&#xf2;" u2="&#x2018;" k="85" />
    <hkern u1="&#xf2;" u2="&#xba;" k="85" />
    <hkern u1="&#xf2;" u2="&#xb0;" k="85" />
    <hkern u1="&#xf2;" u2="&#xaa;" k="85" />
    <hkern u1="&#xf2;" u2="&#x7d;" k="25" />
    <hkern u1="&#xf2;" u2="y" k="18" />
    <hkern u1="&#xf2;" u2="x" k="60" />
    <hkern u1="&#xf2;" u2="v" k="18" />
    <hkern u1="&#xf2;" u2="]" k="25" />
    <hkern u1="&#xf2;" u2="\" k="108" />
    <hkern u1="&#xf2;" u2="W" k="20" />
    <hkern u1="&#xf2;" u2="V" k="108" />
    <hkern u1="&#xf2;" u2="&#x2a;" k="85" />
    <hkern u1="&#xf2;" u2="&#x29;" k="25" />
    <hkern u1="&#xf2;" u2="&#x27;" k="85" />
    <hkern u1="&#xf2;" u2="&#x22;" k="85" />
    <hkern u1="&#xf3;" u2="&#x2122;" k="85" />
    <hkern u1="&#xf3;" u2="&#x201d;" k="85" />
    <hkern u1="&#xf3;" u2="&#x201c;" k="85" />
    <hkern u1="&#xf3;" u2="&#x2019;" k="85" />
    <hkern u1="&#xf3;" u2="&#x2018;" k="85" />
    <hkern u1="&#xf3;" u2="&#xba;" k="85" />
    <hkern u1="&#xf3;" u2="&#xb0;" k="85" />
    <hkern u1="&#xf3;" u2="&#xaa;" k="85" />
    <hkern u1="&#xf3;" u2="&#x7d;" k="25" />
    <hkern u1="&#xf3;" u2="y" k="18" />
    <hkern u1="&#xf3;" u2="x" k="60" />
    <hkern u1="&#xf3;" u2="v" k="18" />
    <hkern u1="&#xf3;" u2="]" k="25" />
    <hkern u1="&#xf3;" u2="\" k="108" />
    <hkern u1="&#xf3;" u2="W" k="20" />
    <hkern u1="&#xf3;" u2="V" k="108" />
    <hkern u1="&#xf3;" u2="&#x2a;" k="85" />
    <hkern u1="&#xf3;" u2="&#x29;" k="25" />
    <hkern u1="&#xf3;" u2="&#x27;" k="85" />
    <hkern u1="&#xf3;" u2="&#x22;" k="85" />
    <hkern u1="&#xf4;" u2="&#x2122;" k="85" />
    <hkern u1="&#xf4;" u2="&#x201d;" k="85" />
    <hkern u1="&#xf4;" u2="&#x201c;" k="85" />
    <hkern u1="&#xf4;" u2="&#x2019;" k="85" />
    <hkern u1="&#xf4;" u2="&#x2018;" k="85" />
    <hkern u1="&#xf4;" u2="&#xba;" k="85" />
    <hkern u1="&#xf4;" u2="&#xb0;" k="85" />
    <hkern u1="&#xf4;" u2="&#xaa;" k="85" />
    <hkern u1="&#xf4;" u2="&#x7d;" k="25" />
    <hkern u1="&#xf4;" u2="y" k="18" />
    <hkern u1="&#xf4;" u2="x" k="60" />
    <hkern u1="&#xf4;" u2="v" k="18" />
    <hkern u1="&#xf4;" u2="]" k="25" />
    <hkern u1="&#xf4;" u2="\" k="108" />
    <hkern u1="&#xf4;" u2="W" k="20" />
    <hkern u1="&#xf4;" u2="V" k="108" />
    <hkern u1="&#xf4;" u2="&#x2a;" k="85" />
    <hkern u1="&#xf4;" u2="&#x29;" k="25" />
    <hkern u1="&#xf4;" u2="&#x27;" k="85" />
    <hkern u1="&#xf4;" u2="&#x22;" k="85" />
    <hkern u1="&#xf5;" u2="&#x2122;" k="85" />
    <hkern u1="&#xf5;" u2="&#x201d;" k="85" />
    <hkern u1="&#xf5;" u2="&#x201c;" k="85" />
    <hkern u1="&#xf5;" u2="&#x2019;" k="85" />
    <hkern u1="&#xf5;" u2="&#x2018;" k="85" />
    <hkern u1="&#xf5;" u2="&#xba;" k="85" />
    <hkern u1="&#xf5;" u2="&#xb0;" k="85" />
    <hkern u1="&#xf5;" u2="&#xaa;" k="85" />
    <hkern u1="&#xf5;" u2="&#x7d;" k="25" />
    <hkern u1="&#xf5;" u2="y" k="18" />
    <hkern u1="&#xf5;" u2="x" k="60" />
    <hkern u1="&#xf5;" u2="v" k="18" />
    <hkern u1="&#xf5;" u2="]" k="25" />
    <hkern u1="&#xf5;" u2="\" k="108" />
    <hkern u1="&#xf5;" u2="W" k="20" />
    <hkern u1="&#xf5;" u2="V" k="108" />
    <hkern u1="&#xf5;" u2="&#x2a;" k="85" />
    <hkern u1="&#xf5;" u2="&#x29;" k="25" />
    <hkern u1="&#xf5;" u2="&#x27;" k="85" />
    <hkern u1="&#xf5;" u2="&#x22;" k="85" />
    <hkern u1="&#xf6;" u2="&#x2122;" k="85" />
    <hkern u1="&#xf6;" u2="&#x201d;" k="85" />
    <hkern u1="&#xf6;" u2="&#x201c;" k="85" />
    <hkern u1="&#xf6;" u2="&#x2019;" k="85" />
    <hkern u1="&#xf6;" u2="&#x2018;" k="85" />
    <hkern u1="&#xf6;" u2="&#xba;" k="85" />
    <hkern u1="&#xf6;" u2="&#xb0;" k="85" />
    <hkern u1="&#xf6;" u2="&#xaa;" k="85" />
    <hkern u1="&#xf6;" u2="&#x7d;" k="25" />
    <hkern u1="&#xf6;" u2="y" k="18" />
    <hkern u1="&#xf6;" u2="x" k="60" />
    <hkern u1="&#xf6;" u2="v" k="18" />
    <hkern u1="&#xf6;" u2="]" k="25" />
    <hkern u1="&#xf6;" u2="\" k="108" />
    <hkern u1="&#xf6;" u2="W" k="20" />
    <hkern u1="&#xf6;" u2="V" k="108" />
    <hkern u1="&#xf6;" u2="&#x2a;" k="85" />
    <hkern u1="&#xf6;" u2="&#x29;" k="25" />
    <hkern u1="&#xf6;" u2="&#x27;" k="85" />
    <hkern u1="&#xf6;" u2="&#x22;" k="85" />
    <hkern u1="&#xf8;" u2="&#x2122;" k="85" />
    <hkern u1="&#xf8;" u2="&#x201d;" k="85" />
    <hkern u1="&#xf8;" u2="&#x201c;" k="85" />
    <hkern u1="&#xf8;" u2="&#x2019;" k="85" />
    <hkern u1="&#xf8;" u2="&#x2018;" k="85" />
    <hkern u1="&#xf8;" u2="&#xba;" k="85" />
    <hkern u1="&#xf8;" u2="&#xb0;" k="85" />
    <hkern u1="&#xf8;" u2="&#xaa;" k="85" />
    <hkern u1="&#xf8;" u2="&#x7d;" k="25" />
    <hkern u1="&#xf8;" u2="y" k="18" />
    <hkern u1="&#xf8;" u2="x" k="60" />
    <hkern u1="&#xf8;" u2="v" k="18" />
    <hkern u1="&#xf8;" u2="]" k="25" />
    <hkern u1="&#xf8;" u2="\" k="108" />
    <hkern u1="&#xf8;" u2="W" k="20" />
    <hkern u1="&#xf8;" u2="V" k="108" />
    <hkern u1="&#xf8;" u2="&#x2a;" k="85" />
    <hkern u1="&#xf8;" u2="&#x29;" k="25" />
    <hkern u1="&#xf8;" u2="&#x27;" k="85" />
    <hkern u1="&#xf8;" u2="&#x22;" k="85" />
    <hkern u1="&#xfe;" u2="&#x2122;" k="85" />
    <hkern u1="&#xfe;" u2="&#x201d;" k="85" />
    <hkern u1="&#xfe;" u2="&#x201c;" k="85" />
    <hkern u1="&#xfe;" u2="&#x2019;" k="85" />
    <hkern u1="&#xfe;" u2="&#x2018;" k="85" />
    <hkern u1="&#xfe;" u2="&#xba;" k="85" />
    <hkern u1="&#xfe;" u2="&#xb0;" k="85" />
    <hkern u1="&#xfe;" u2="&#xaa;" k="85" />
    <hkern u1="&#xfe;" u2="&#x7d;" k="25" />
    <hkern u1="&#xfe;" u2="y" k="18" />
    <hkern u1="&#xfe;" u2="x" k="60" />
    <hkern u1="&#xfe;" u2="v" k="18" />
    <hkern u1="&#xfe;" u2="]" k="25" />
    <hkern u1="&#xfe;" u2="\" k="108" />
    <hkern u1="&#xfe;" u2="W" k="20" />
    <hkern u1="&#xfe;" u2="V" k="108" />
    <hkern u1="&#xfe;" u2="&#x2a;" k="85" />
    <hkern u1="&#xfe;" u2="&#x29;" k="25" />
    <hkern u1="&#xfe;" u2="&#x27;" k="85" />
    <hkern u1="&#xfe;" u2="&#x22;" k="85" />
    <hkern u1="&#x104;" u2="&#x2122;" k="170" />
    <hkern u1="&#x104;" u2="&#x203a;" k="35" />
    <hkern u1="&#x104;" u2="&#x2039;" k="35" />
    <hkern u1="&#x104;" u2="&#x2022;" k="35" />
    <hkern u1="&#x104;" u2="&#x201d;" k="170" />
    <hkern u1="&#x104;" u2="&#x201c;" k="170" />
    <hkern u1="&#x104;" u2="&#x2019;" k="170" />
    <hkern u1="&#x104;" u2="&#x2018;" k="170" />
    <hkern u1="&#x104;" u2="&#x2014;" k="35" />
    <hkern u1="&#x104;" u2="&#x2013;" k="35" />
    <hkern u1="&#x104;" u2="&#x178;" k="140" />
    <hkern u1="&#x104;" u2="&#x152;" k="30" />
    <hkern u1="&#x104;" u2="&#x106;" k="30" />
    <hkern u1="&#x104;" u2="&#xdd;" k="140" />
    <hkern u1="&#x104;" u2="&#xdc;" k="58" />
    <hkern u1="&#x104;" u2="&#xdb;" k="58" />
    <hkern u1="&#x104;" u2="&#xda;" k="58" />
    <hkern u1="&#x104;" u2="&#xd9;" k="58" />
    <hkern u1="&#x104;" u2="&#xd8;" k="30" />
    <hkern u1="&#x104;" u2="&#xd6;" k="30" />
    <hkern u1="&#x104;" u2="&#xd5;" k="30" />
    <hkern u1="&#x104;" u2="&#xd4;" k="30" />
    <hkern u1="&#x104;" u2="&#xd3;" k="30" />
    <hkern u1="&#x104;" u2="&#xd2;" k="30" />
    <hkern u1="&#x104;" u2="&#xc7;" k="30" />
    <hkern u1="&#x104;" u2="&#xbb;" k="35" />
    <hkern u1="&#x104;" u2="&#xba;" k="170" />
    <hkern u1="&#x104;" u2="&#xb9;" k="160" />
    <hkern u1="&#x104;" u2="&#xb7;" k="35" />
    <hkern u1="&#x104;" u2="&#xb3;" k="160" />
    <hkern u1="&#x104;" u2="&#xb2;" k="160" />
    <hkern u1="&#x104;" u2="&#xb0;" k="170" />
    <hkern u1="&#x104;" u2="&#xad;" k="35" />
    <hkern u1="&#x104;" u2="&#xab;" k="35" />
    <hkern u1="&#x104;" u2="&#xaa;" k="170" />
    <hkern u1="&#x104;" u2="y" k="70" />
    <hkern u1="&#x104;" u2="v" k="70" />
    <hkern u1="&#x104;" u2="\" k="95" />
    <hkern u1="&#x104;" u2="Y" k="140" />
    <hkern u1="&#x104;" u2="W" k="60" />
    <hkern u1="&#x104;" u2="V" k="95" />
    <hkern u1="&#x104;" u2="U" k="58" />
    <hkern u1="&#x104;" u2="T" k="115" />
    <hkern u1="&#x104;" u2="Q" k="30" />
    <hkern u1="&#x104;" u2="O" k="30" />
    <hkern u1="&#x104;" u2="J" k="-45" />
    <hkern u1="&#x104;" u2="G" k="30" />
    <hkern u1="&#x104;" u2="C" k="30" />
    <hkern u1="&#x104;" u2="&#x40;" k="30" />
    <hkern u1="&#x104;" u2="&#x3f;" k="48" />
    <hkern u1="&#x104;" u2="&#x2d;" k="35" />
    <hkern u1="&#x104;" u2="&#x2a;" k="170" />
    <hkern u1="&#x104;" u2="&#x27;" k="170" />
    <hkern u1="&#x104;" u2="&#x22;" k="170" />
    <hkern u1="&#x105;" u2="&#x2122;" k="65" />
    <hkern u1="&#x105;" u2="&#x201d;" k="65" />
    <hkern u1="&#x105;" u2="&#x201c;" k="65" />
    <hkern u1="&#x105;" u2="&#x2019;" k="65" />
    <hkern u1="&#x105;" u2="&#x2018;" k="65" />
    <hkern u1="&#x105;" u2="&#xba;" k="65" />
    <hkern u1="&#x105;" u2="&#xb9;" k="65" />
    <hkern u1="&#x105;" u2="&#xb3;" k="65" />
    <hkern u1="&#x105;" u2="&#xb2;" k="65" />
    <hkern u1="&#x105;" u2="&#xb0;" k="65" />
    <hkern u1="&#x105;" u2="&#xaa;" k="65" />
    <hkern u1="&#x105;" u2="y" k="25" />
    <hkern u1="&#x105;" u2="w" k="13" />
    <hkern u1="&#x105;" u2="v" k="25" />
    <hkern u1="&#x105;" u2="&#x2a;" k="65" />
    <hkern u1="&#x105;" u2="&#x27;" k="65" />
    <hkern u1="&#x105;" u2="&#x22;" k="65" />
    <hkern u1="&#x106;" u2="&#x203a;" k="155" />
    <hkern u1="&#x106;" u2="&#x2039;" k="155" />
    <hkern u1="&#x106;" u2="&#x2022;" k="155" />
    <hkern u1="&#x106;" u2="&#x2014;" k="155" />
    <hkern u1="&#x106;" u2="&#x2013;" k="155" />
    <hkern u1="&#x106;" u2="&#xbb;" k="155" />
    <hkern u1="&#x106;" u2="&#xb7;" k="155" />
    <hkern u1="&#x106;" u2="&#xad;" k="155" />
    <hkern u1="&#x106;" u2="&#xab;" k="155" />
    <hkern u1="&#x106;" u2="&#x2d;" k="155" />
    <hkern u1="&#x119;" u2="&#x2122;" k="85" />
    <hkern u1="&#x119;" u2="&#x201d;" k="85" />
    <hkern u1="&#x119;" u2="&#x201c;" k="85" />
    <hkern u1="&#x119;" u2="&#x2019;" k="85" />
    <hkern u1="&#x119;" u2="&#x2018;" k="85" />
    <hkern u1="&#x119;" u2="&#xba;" k="85" />
    <hkern u1="&#x119;" u2="&#xb0;" k="85" />
    <hkern u1="&#x119;" u2="&#xaa;" k="85" />
    <hkern u1="&#x119;" u2="&#x7d;" k="25" />
    <hkern u1="&#x119;" u2="y" k="18" />
    <hkern u1="&#x119;" u2="x" k="60" />
    <hkern u1="&#x119;" u2="v" k="18" />
    <hkern u1="&#x119;" u2="]" k="25" />
    <hkern u1="&#x119;" u2="\" k="108" />
    <hkern u1="&#x119;" u2="W" k="20" />
    <hkern u1="&#x119;" u2="V" k="108" />
    <hkern u1="&#x119;" u2="&#x2a;" k="85" />
    <hkern u1="&#x119;" u2="&#x29;" k="25" />
    <hkern u1="&#x119;" u2="&#x27;" k="85" />
    <hkern u1="&#x119;" u2="&#x22;" k="85" />
    <hkern u1="&#x141;" u2="&#x2122;" k="140" />
    <hkern u1="&#x141;" u2="&#x203a;" k="145" />
    <hkern u1="&#x141;" u2="&#x2039;" k="145" />
    <hkern u1="&#x141;" u2="&#x2022;" k="145" />
    <hkern u1="&#x141;" u2="&#x201d;" k="140" />
    <hkern u1="&#x141;" u2="&#x201c;" k="140" />
    <hkern u1="&#x141;" u2="&#x2019;" k="140" />
    <hkern u1="&#x141;" u2="&#x2018;" k="140" />
    <hkern u1="&#x141;" u2="&#x2014;" k="145" />
    <hkern u1="&#x141;" u2="&#x2013;" k="145" />
    <hkern u1="&#x141;" u2="&#x178;" k="135" />
    <hkern u1="&#x141;" u2="&#xdd;" k="135" />
    <hkern u1="&#x141;" u2="&#xbb;" k="145" />
    <hkern u1="&#x141;" u2="&#xba;" k="140" />
    <hkern u1="&#x141;" u2="&#xb9;" k="125" />
    <hkern u1="&#x141;" u2="&#xb7;" k="145" />
    <hkern u1="&#x141;" u2="&#xb3;" k="125" />
    <hkern u1="&#x141;" u2="&#xb2;" k="125" />
    <hkern u1="&#x141;" u2="&#xb0;" k="140" />
    <hkern u1="&#x141;" u2="&#xad;" k="145" />
    <hkern u1="&#x141;" u2="&#xab;" k="145" />
    <hkern u1="&#x141;" u2="&#xaa;" k="140" />
    <hkern u1="&#x141;" u2="y" k="53" />
    <hkern u1="&#x141;" u2="v" k="53" />
    <hkern u1="&#x141;" u2="\" k="150" />
    <hkern u1="&#x141;" u2="Y" k="135" />
    <hkern u1="&#x141;" u2="W" k="110" />
    <hkern u1="&#x141;" u2="V" k="150" />
    <hkern u1="&#x141;" u2="&#x2d;" k="145" />
    <hkern u1="&#x141;" u2="&#x2a;" k="140" />
    <hkern u1="&#x141;" u2="&#x27;" k="140" />
    <hkern u1="&#x141;" u2="&#x22;" k="140" />
    <hkern u1="&#x144;" u2="&#x2122;" k="65" />
    <hkern u1="&#x144;" u2="&#x201d;" k="65" />
    <hkern u1="&#x144;" u2="&#x201c;" k="65" />
    <hkern u1="&#x144;" u2="&#x2019;" k="65" />
    <hkern u1="&#x144;" u2="&#x2018;" k="65" />
    <hkern u1="&#x144;" u2="&#xba;" k="65" />
    <hkern u1="&#x144;" u2="&#xb9;" k="65" />
    <hkern u1="&#x144;" u2="&#xb3;" k="65" />
    <hkern u1="&#x144;" u2="&#xb2;" k="65" />
    <hkern u1="&#x144;" u2="&#xb0;" k="65" />
    <hkern u1="&#x144;" u2="&#xaa;" k="65" />
    <hkern u1="&#x144;" u2="y" k="25" />
    <hkern u1="&#x144;" u2="w" k="13" />
    <hkern u1="&#x144;" u2="v" k="25" />
    <hkern u1="&#x144;" u2="&#x2a;" k="65" />
    <hkern u1="&#x144;" u2="&#x27;" k="65" />
    <hkern u1="&#x144;" u2="&#x22;" k="65" />
    <hkern u1="&#x153;" u2="&#x2122;" k="85" />
    <hkern u1="&#x153;" u2="&#x201d;" k="85" />
    <hkern u1="&#x153;" u2="&#x201c;" k="85" />
    <hkern u1="&#x153;" u2="&#x2019;" k="85" />
    <hkern u1="&#x153;" u2="&#x2018;" k="85" />
    <hkern u1="&#x153;" u2="&#xba;" k="85" />
    <hkern u1="&#x153;" u2="&#xb0;" k="85" />
    <hkern u1="&#x153;" u2="&#xaa;" k="85" />
    <hkern u1="&#x153;" u2="&#x7d;" k="25" />
    <hkern u1="&#x153;" u2="y" k="18" />
    <hkern u1="&#x153;" u2="x" k="60" />
    <hkern u1="&#x153;" u2="v" k="18" />
    <hkern u1="&#x153;" u2="]" k="25" />
    <hkern u1="&#x153;" u2="\" k="108" />
    <hkern u1="&#x153;" u2="W" k="20" />
    <hkern u1="&#x153;" u2="V" k="108" />
    <hkern u1="&#x153;" u2="&#x2a;" k="85" />
    <hkern u1="&#x153;" u2="&#x29;" k="25" />
    <hkern u1="&#x153;" u2="&#x27;" k="85" />
    <hkern u1="&#x153;" u2="&#x22;" k="85" />
    <hkern u1="&#x178;" u2="&#x2206;" k="140" />
    <hkern u1="&#x178;" u2="&#x2122;" k="-25" />
    <hkern u1="&#x178;" u2="&#x203a;" k="160" />
    <hkern u1="&#x178;" u2="&#x2039;" k="160" />
    <hkern u1="&#x178;" u2="&#x2022;" k="160" />
    <hkern u1="&#x178;" u2="&#x201e;" k="135" />
    <hkern u1="&#x178;" u2="&#x201d;" k="-25" />
    <hkern u1="&#x178;" u2="&#x201c;" k="-25" />
    <hkern u1="&#x178;" u2="&#x201a;" k="135" />
    <hkern u1="&#x178;" u2="&#x2019;" k="-25" />
    <hkern u1="&#x178;" u2="&#x2018;" k="-25" />
    <hkern u1="&#x178;" u2="&#x2014;" k="160" />
    <hkern u1="&#x178;" u2="&#x2013;" k="160" />
    <hkern u1="&#x178;" u2="&#x153;" k="160" />
    <hkern u1="&#x178;" u2="&#x152;" k="80" />
    <hkern u1="&#x178;" u2="&#x144;" k="110" />
    <hkern u1="&#x178;" u2="&#x119;" k="160" />
    <hkern u1="&#x178;" u2="&#x107;" k="160" />
    <hkern u1="&#x178;" u2="&#x106;" k="80" />
    <hkern u1="&#x178;" u2="&#x105;" k="108" />
    <hkern u1="&#x178;" u2="&#x104;" k="140" />
    <hkern u1="&#x178;" u2="&#xfc;" k="110" />
    <hkern u1="&#x178;" u2="&#xfb;" k="110" />
    <hkern u1="&#x178;" u2="&#xfa;" k="110" />
    <hkern u1="&#x178;" u2="&#xf9;" k="110" />
    <hkern u1="&#x178;" u2="&#xf8;" k="160" />
    <hkern u1="&#x178;" u2="&#xf6;" k="160" />
    <hkern u1="&#x178;" u2="&#xf5;" k="160" />
    <hkern u1="&#x178;" u2="&#xf4;" k="160" />
    <hkern u1="&#x178;" u2="&#xf3;" k="160" />
    <hkern u1="&#x178;" u2="&#xf2;" k="160" />
    <hkern u1="&#x178;" u2="&#xf1;" k="110" />
    <hkern u1="&#x178;" u2="&#xf0;" k="160" />
    <hkern u1="&#x178;" u2="&#xeb;" k="160" />
    <hkern u1="&#x178;" u2="&#xea;" k="160" />
    <hkern u1="&#x178;" u2="&#xe9;" k="160" />
    <hkern u1="&#x178;" u2="&#xe8;" k="160" />
    <hkern u1="&#x178;" u2="&#xe7;" k="160" />
    <hkern u1="&#x178;" u2="&#xe6;" k="108" />
    <hkern u1="&#x178;" u2="&#xe5;" k="108" />
    <hkern u1="&#x178;" u2="&#xe4;" k="108" />
    <hkern u1="&#x178;" u2="&#xe3;" k="108" />
    <hkern u1="&#x178;" u2="&#xe2;" k="108" />
    <hkern u1="&#x178;" u2="&#xe1;" k="108" />
    <hkern u1="&#x178;" u2="&#xe0;" k="108" />
    <hkern u1="&#x178;" u2="&#xd8;" k="80" />
    <hkern u1="&#x178;" u2="&#xd6;" k="80" />
    <hkern u1="&#x178;" u2="&#xd5;" k="80" />
    <hkern u1="&#x178;" u2="&#xd4;" k="80" />
    <hkern u1="&#x178;" u2="&#xd3;" k="80" />
    <hkern u1="&#x178;" u2="&#xd2;" k="80" />
    <hkern u1="&#x178;" u2="&#xc7;" k="80" />
    <hkern u1="&#x178;" u2="&#xc6;" k="140" />
    <hkern u1="&#x178;" u2="&#xc5;" k="140" />
    <hkern u1="&#x178;" u2="&#xc4;" k="140" />
    <hkern u1="&#x178;" u2="&#xc3;" k="140" />
    <hkern u1="&#x178;" u2="&#xc2;" k="140" />
    <hkern u1="&#x178;" u2="&#xc1;" k="140" />
    <hkern u1="&#x178;" u2="&#xc0;" k="140" />
    <hkern u1="&#x178;" u2="&#xbb;" k="160" />
    <hkern u1="&#x178;" u2="&#xba;" k="-25" />
    <hkern u1="&#x178;" u2="&#xb9;" k="-45" />
    <hkern u1="&#x178;" u2="&#xb7;" k="160" />
    <hkern u1="&#x178;" u2="&#xb5;" k="110" />
    <hkern u1="&#x178;" u2="&#xb3;" k="-45" />
    <hkern u1="&#x178;" u2="&#xb2;" k="-45" />
    <hkern u1="&#x178;" u2="&#xb0;" k="-25" />
    <hkern u1="&#x178;" u2="&#xad;" k="160" />
    <hkern u1="&#x178;" u2="&#xab;" k="160" />
    <hkern u1="&#x178;" u2="&#xaa;" k="-25" />
    <hkern u1="&#x178;" u2="y" k="100" />
    <hkern u1="&#x178;" u2="x" k="125" />
    <hkern u1="&#x178;" u2="w" k="85" />
    <hkern u1="&#x178;" u2="v" k="100" />
    <hkern u1="&#x178;" u2="u" k="110" />
    <hkern u1="&#x178;" u2="s" k="113" />
    <hkern u1="&#x178;" u2="r" k="110" />
    <hkern u1="&#x178;" u2="q" k="160" />
    <hkern u1="&#x178;" u2="p" k="110" />
    <hkern u1="&#x178;" u2="o" k="160" />
    <hkern u1="&#x178;" u2="n" k="110" />
    <hkern u1="&#x178;" u2="m" k="110" />
    <hkern u1="&#x178;" u2="g" k="165" />
    <hkern u1="&#x178;" u2="e" k="160" />
    <hkern u1="&#x178;" u2="d" k="160" />
    <hkern u1="&#x178;" u2="c" k="160" />
    <hkern u1="&#x178;" u2="a" k="108" />
    <hkern u1="&#x178;" u2="Q" k="80" />
    <hkern u1="&#x178;" u2="O" k="80" />
    <hkern u1="&#x178;" u2="J" k="200" />
    <hkern u1="&#x178;" u2="G" k="80" />
    <hkern u1="&#x178;" u2="C" k="80" />
    <hkern u1="&#x178;" u2="A" k="140" />
    <hkern u1="&#x178;" u2="&#x40;" k="80" />
    <hkern u1="&#x178;" u2="&#x3f;" k="-37" />
    <hkern u1="&#x178;" u2="&#x3b;" k="110" />
    <hkern u1="&#x178;" u2="&#x3a;" k="110" />
    <hkern u1="&#x178;" u2="&#x2f;" k="140" />
    <hkern u1="&#x178;" u2="&#x2e;" k="135" />
    <hkern u1="&#x178;" u2="&#x2d;" k="160" />
    <hkern u1="&#x178;" u2="&#x2c;" k="135" />
    <hkern u1="&#x178;" u2="&#x2a;" k="-25" />
    <hkern u1="&#x178;" u2="&#x27;" k="-25" />
    <hkern u1="&#x178;" u2="&#x26;" k="140" />
    <hkern u1="&#x178;" u2="&#x22;" k="-25" />
    <hkern u1="&#x179;" u2="&#x203a;" k="75" />
    <hkern u1="&#x179;" u2="&#x2039;" k="75" />
    <hkern u1="&#x179;" u2="&#x2022;" k="75" />
    <hkern u1="&#x179;" u2="&#x2014;" k="75" />
    <hkern u1="&#x179;" u2="&#x2013;" k="75" />
    <hkern u1="&#x179;" u2="&#x153;" k="50" />
    <hkern u1="&#x179;" u2="&#x152;" k="70" />
    <hkern u1="&#x179;" u2="&#x119;" k="50" />
    <hkern u1="&#x179;" u2="&#x107;" k="50" />
    <hkern u1="&#x179;" u2="&#x106;" k="70" />
    <hkern u1="&#x179;" u2="&#xf8;" k="50" />
    <hkern u1="&#x179;" u2="&#xf6;" k="50" />
    <hkern u1="&#x179;" u2="&#xf5;" k="50" />
    <hkern u1="&#x179;" u2="&#xf4;" k="50" />
    <hkern u1="&#x179;" u2="&#xf3;" k="50" />
    <hkern u1="&#x179;" u2="&#xf2;" k="50" />
    <hkern u1="&#x179;" u2="&#xf0;" k="50" />
    <hkern u1="&#x179;" u2="&#xeb;" k="50" />
    <hkern u1="&#x179;" u2="&#xea;" k="50" />
    <hkern u1="&#x179;" u2="&#xe9;" k="50" />
    <hkern u1="&#x179;" u2="&#xe8;" k="50" />
    <hkern u1="&#x179;" u2="&#xe7;" k="50" />
    <hkern u1="&#x179;" u2="&#xd8;" k="70" />
    <hkern u1="&#x179;" u2="&#xd6;" k="70" />
    <hkern u1="&#x179;" u2="&#xd5;" k="70" />
    <hkern u1="&#x179;" u2="&#xd4;" k="70" />
    <hkern u1="&#x179;" u2="&#xd3;" k="70" />
    <hkern u1="&#x179;" u2="&#xd2;" k="70" />
    <hkern u1="&#x179;" u2="&#xc7;" k="70" />
    <hkern u1="&#x179;" u2="&#xbb;" k="75" />
    <hkern u1="&#x179;" u2="&#xb7;" k="75" />
    <hkern u1="&#x179;" u2="&#xad;" k="75" />
    <hkern u1="&#x179;" u2="&#xab;" k="75" />
    <hkern u1="&#x179;" u2="y" k="45" />
    <hkern u1="&#x179;" u2="v" k="45" />
    <hkern u1="&#x179;" u2="s" k="40" />
    <hkern u1="&#x179;" u2="q" k="50" />
    <hkern u1="&#x179;" u2="o" k="50" />
    <hkern u1="&#x179;" u2="e" k="50" />
    <hkern u1="&#x179;" u2="d" k="50" />
    <hkern u1="&#x179;" u2="c" k="50" />
    <hkern u1="&#x179;" u2="Q" k="70" />
    <hkern u1="&#x179;" u2="O" k="70" />
    <hkern u1="&#x179;" u2="G" k="70" />
    <hkern u1="&#x179;" u2="C" k="70" />
    <hkern u1="&#x179;" u2="&#x40;" k="70" />
    <hkern u1="&#x179;" u2="&#x3f;" k="-37" />
    <hkern u1="&#x179;" u2="&#x2d;" k="75" />
    <hkern u1="&#x17b;" u2="&#x203a;" k="75" />
    <hkern u1="&#x17b;" u2="&#x2039;" k="75" />
    <hkern u1="&#x17b;" u2="&#x2022;" k="75" />
    <hkern u1="&#x17b;" u2="&#x2014;" k="75" />
    <hkern u1="&#x17b;" u2="&#x2013;" k="75" />
    <hkern u1="&#x17b;" u2="&#x153;" k="50" />
    <hkern u1="&#x17b;" u2="&#x152;" k="70" />
    <hkern u1="&#x17b;" u2="&#x119;" k="50" />
    <hkern u1="&#x17b;" u2="&#x107;" k="50" />
    <hkern u1="&#x17b;" u2="&#x106;" k="70" />
    <hkern u1="&#x17b;" u2="&#xf8;" k="50" />
    <hkern u1="&#x17b;" u2="&#xf6;" k="50" />
    <hkern u1="&#x17b;" u2="&#xf5;" k="50" />
    <hkern u1="&#x17b;" u2="&#xf4;" k="50" />
    <hkern u1="&#x17b;" u2="&#xf3;" k="50" />
    <hkern u1="&#x17b;" u2="&#xf2;" k="50" />
    <hkern u1="&#x17b;" u2="&#xf0;" k="50" />
    <hkern u1="&#x17b;" u2="&#xeb;" k="50" />
    <hkern u1="&#x17b;" u2="&#xea;" k="50" />
    <hkern u1="&#x17b;" u2="&#xe9;" k="50" />
    <hkern u1="&#x17b;" u2="&#xe8;" k="50" />
    <hkern u1="&#x17b;" u2="&#xe7;" k="50" />
    <hkern u1="&#x17b;" u2="&#xd8;" k="70" />
    <hkern u1="&#x17b;" u2="&#xd6;" k="70" />
    <hkern u1="&#x17b;" u2="&#xd5;" k="70" />
    <hkern u1="&#x17b;" u2="&#xd4;" k="70" />
    <hkern u1="&#x17b;" u2="&#xd3;" k="70" />
    <hkern u1="&#x17b;" u2="&#xd2;" k="70" />
    <hkern u1="&#x17b;" u2="&#xc7;" k="70" />
    <hkern u1="&#x17b;" u2="&#xbb;" k="75" />
    <hkern u1="&#x17b;" u2="&#xb7;" k="75" />
    <hkern u1="&#x17b;" u2="&#xad;" k="75" />
    <hkern u1="&#x17b;" u2="&#xab;" k="75" />
    <hkern u1="&#x17b;" u2="y" k="45" />
    <hkern u1="&#x17b;" u2="v" k="45" />
    <hkern u1="&#x17b;" u2="s" k="40" />
    <hkern u1="&#x17b;" u2="q" k="50" />
    <hkern u1="&#x17b;" u2="o" k="50" />
    <hkern u1="&#x17b;" u2="e" k="50" />
    <hkern u1="&#x17b;" u2="d" k="50" />
    <hkern u1="&#x17b;" u2="c" k="50" />
    <hkern u1="&#x17b;" u2="Q" k="70" />
    <hkern u1="&#x17b;" u2="O" k="70" />
    <hkern u1="&#x17b;" u2="G" k="70" />
    <hkern u1="&#x17b;" u2="C" k="70" />
    <hkern u1="&#x17b;" u2="&#x40;" k="70" />
    <hkern u1="&#x17b;" u2="&#x3f;" k="-37" />
    <hkern u1="&#x17b;" u2="&#x2d;" k="75" />
    <hkern u1="&#x17d;" u2="&#x203a;" k="75" />
    <hkern u1="&#x17d;" u2="&#x2039;" k="75" />
    <hkern u1="&#x17d;" u2="&#x2022;" k="75" />
    <hkern u1="&#x17d;" u2="&#x2014;" k="75" />
    <hkern u1="&#x17d;" u2="&#x2013;" k="75" />
    <hkern u1="&#x17d;" u2="&#x153;" k="50" />
    <hkern u1="&#x17d;" u2="&#x152;" k="70" />
    <hkern u1="&#x17d;" u2="&#x119;" k="50" />
    <hkern u1="&#x17d;" u2="&#x107;" k="50" />
    <hkern u1="&#x17d;" u2="&#x106;" k="70" />
    <hkern u1="&#x17d;" u2="&#xf8;" k="50" />
    <hkern u1="&#x17d;" u2="&#xf6;" k="50" />
    <hkern u1="&#x17d;" u2="&#xf5;" k="50" />
    <hkern u1="&#x17d;" u2="&#xf4;" k="50" />
    <hkern u1="&#x17d;" u2="&#xf3;" k="50" />
    <hkern u1="&#x17d;" u2="&#xf2;" k="50" />
    <hkern u1="&#x17d;" u2="&#xf0;" k="50" />
    <hkern u1="&#x17d;" u2="&#xeb;" k="50" />
    <hkern u1="&#x17d;" u2="&#xea;" k="50" />
    <hkern u1="&#x17d;" u2="&#xe9;" k="50" />
    <hkern u1="&#x17d;" u2="&#xe8;" k="50" />
    <hkern u1="&#x17d;" u2="&#xe7;" k="50" />
    <hkern u1="&#x17d;" u2="&#xd8;" k="70" />
    <hkern u1="&#x17d;" u2="&#xd6;" k="70" />
    <hkern u1="&#x17d;" u2="&#xd5;" k="70" />
    <hkern u1="&#x17d;" u2="&#xd4;" k="70" />
    <hkern u1="&#x17d;" u2="&#xd3;" k="70" />
    <hkern u1="&#x17d;" u2="&#xd2;" k="70" />
    <hkern u1="&#x17d;" u2="&#xc7;" k="70" />
    <hkern u1="&#x17d;" u2="&#xbb;" k="75" />
    <hkern u1="&#x17d;" u2="&#xb7;" k="75" />
    <hkern u1="&#x17d;" u2="&#xad;" k="75" />
    <hkern u1="&#x17d;" u2="&#xab;" k="75" />
    <hkern u1="&#x17d;" u2="y" k="45" />
    <hkern u1="&#x17d;" u2="v" k="45" />
    <hkern u1="&#x17d;" u2="s" k="40" />
    <hkern u1="&#x17d;" u2="q" k="50" />
    <hkern u1="&#x17d;" u2="o" k="50" />
    <hkern u1="&#x17d;" u2="e" k="50" />
    <hkern u1="&#x17d;" u2="d" k="50" />
    <hkern u1="&#x17d;" u2="c" k="50" />
    <hkern u1="&#x17d;" u2="Q" k="70" />
    <hkern u1="&#x17d;" u2="O" k="70" />
    <hkern u1="&#x17d;" u2="G" k="70" />
    <hkern u1="&#x17d;" u2="C" k="70" />
    <hkern u1="&#x17d;" u2="&#x40;" k="70" />
    <hkern u1="&#x17d;" u2="&#x3f;" k="-37" />
    <hkern u1="&#x17d;" u2="&#x2d;" k="75" />
    <hkern u1="&#x2013;" u2="&#x2206;" k="35" />
    <hkern u1="&#x2013;" u2="&#x2122;" k="190" />
    <hkern u1="&#x2013;" u2="&#x201e;" k="138" />
    <hkern u1="&#x2013;" u2="&#x201d;" k="190" />
    <hkern u1="&#x2013;" u2="&#x201c;" k="190" />
    <hkern u1="&#x2013;" u2="&#x201a;" k="138" />
    <hkern u1="&#x2013;" u2="&#x2019;" k="190" />
    <hkern u1="&#x2013;" u2="&#x2018;" k="190" />
    <hkern u1="&#x2013;" u2="&#x17d;" k="43" />
    <hkern u1="&#x2013;" u2="&#x17b;" k="43" />
    <hkern u1="&#x2013;" u2="&#x179;" k="43" />
    <hkern u1="&#x2013;" u2="&#x178;" k="160" />
    <hkern u1="&#x2013;" u2="&#x104;" k="35" />
    <hkern u1="&#x2013;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2013;" u2="&#xc6;" k="35" />
    <hkern u1="&#x2013;" u2="&#xc5;" k="35" />
    <hkern u1="&#x2013;" u2="&#xc4;" k="35" />
    <hkern u1="&#x2013;" u2="&#xc3;" k="35" />
    <hkern u1="&#x2013;" u2="&#xc2;" k="35" />
    <hkern u1="&#x2013;" u2="&#xc1;" k="35" />
    <hkern u1="&#x2013;" u2="&#xc0;" k="35" />
    <hkern u1="&#x2013;" u2="&#xba;" k="190" />
    <hkern u1="&#x2013;" u2="&#xb0;" k="190" />
    <hkern u1="&#x2013;" u2="&#xaa;" k="190" />
    <hkern u1="&#x2013;" u2="\" k="105" />
    <hkern u1="&#x2013;" u2="Z" k="43" />
    <hkern u1="&#x2013;" u2="Y" k="160" />
    <hkern u1="&#x2013;" u2="X" k="55" />
    <hkern u1="&#x2013;" u2="W" k="25" />
    <hkern u1="&#x2013;" u2="V" k="105" />
    <hkern u1="&#x2013;" u2="T" k="180" />
    <hkern u1="&#x2013;" u2="A" k="35" />
    <hkern u1="&#x2013;" u2="&#x2f;" k="35" />
    <hkern u1="&#x2013;" u2="&#x2e;" k="138" />
    <hkern u1="&#x2013;" u2="&#x2c;" k="138" />
    <hkern u1="&#x2013;" u2="&#x2a;" k="190" />
    <hkern u1="&#x2013;" u2="&#x27;" k="190" />
    <hkern u1="&#x2013;" u2="&#x26;" k="35" />
    <hkern u1="&#x2013;" u2="&#x22;" k="190" />
    <hkern u1="&#x2014;" u2="&#x2206;" k="35" />
    <hkern u1="&#x2014;" u2="&#x2122;" k="190" />
    <hkern u1="&#x2014;" u2="&#x201e;" k="138" />
    <hkern u1="&#x2014;" u2="&#x201d;" k="190" />
    <hkern u1="&#x2014;" u2="&#x201c;" k="190" />
    <hkern u1="&#x2014;" u2="&#x201a;" k="138" />
    <hkern u1="&#x2014;" u2="&#x2019;" k="190" />
    <hkern u1="&#x2014;" u2="&#x2018;" k="190" />
    <hkern u1="&#x2014;" u2="&#x17d;" k="43" />
    <hkern u1="&#x2014;" u2="&#x17b;" k="43" />
    <hkern u1="&#x2014;" u2="&#x179;" k="43" />
    <hkern u1="&#x2014;" u2="&#x178;" k="160" />
    <hkern u1="&#x2014;" u2="&#x104;" k="35" />
    <hkern u1="&#x2014;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2014;" u2="&#xc6;" k="35" />
    <hkern u1="&#x2014;" u2="&#xc5;" k="35" />
    <hkern u1="&#x2014;" u2="&#xc4;" k="35" />
    <hkern u1="&#x2014;" u2="&#xc3;" k="35" />
    <hkern u1="&#x2014;" u2="&#xc2;" k="35" />
    <hkern u1="&#x2014;" u2="&#xc1;" k="35" />
    <hkern u1="&#x2014;" u2="&#xc0;" k="35" />
    <hkern u1="&#x2014;" u2="&#xba;" k="190" />
    <hkern u1="&#x2014;" u2="&#xb0;" k="190" />
    <hkern u1="&#x2014;" u2="&#xaa;" k="190" />
    <hkern u1="&#x2014;" u2="\" k="105" />
    <hkern u1="&#x2014;" u2="Z" k="43" />
    <hkern u1="&#x2014;" u2="Y" k="160" />
    <hkern u1="&#x2014;" u2="X" k="55" />
    <hkern u1="&#x2014;" u2="W" k="25" />
    <hkern u1="&#x2014;" u2="V" k="105" />
    <hkern u1="&#x2014;" u2="T" k="180" />
    <hkern u1="&#x2014;" u2="A" k="35" />
    <hkern u1="&#x2014;" u2="&#x2f;" k="35" />
    <hkern u1="&#x2014;" u2="&#x2e;" k="138" />
    <hkern u1="&#x2014;" u2="&#x2c;" k="138" />
    <hkern u1="&#x2014;" u2="&#x2a;" k="190" />
    <hkern u1="&#x2014;" u2="&#x27;" k="190" />
    <hkern u1="&#x2014;" u2="&#x26;" k="35" />
    <hkern u1="&#x2014;" u2="&#x22;" k="190" />
    <hkern u1="&#x2018;" u2="&#x2206;" k="170" />
    <hkern u1="&#x2018;" u2="&#x203a;" k="190" />
    <hkern u1="&#x2018;" u2="&#x2039;" k="190" />
    <hkern u1="&#x2018;" u2="&#x2022;" k="190" />
    <hkern u1="&#x2018;" u2="&#x201e;" k="245" />
    <hkern u1="&#x2018;" u2="&#x201a;" k="245" />
    <hkern u1="&#x2018;" u2="&#x2014;" k="190" />
    <hkern u1="&#x2018;" u2="&#x2013;" k="190" />
    <hkern u1="&#x2018;" u2="&#x178;" k="-25" />
    <hkern u1="&#x2018;" u2="&#x153;" k="85" />
    <hkern u1="&#x2018;" u2="&#x152;" k="48" />
    <hkern u1="&#x2018;" u2="&#x119;" k="85" />
    <hkern u1="&#x2018;" u2="&#x107;" k="85" />
    <hkern u1="&#x2018;" u2="&#x106;" k="48" />
    <hkern u1="&#x2018;" u2="&#x105;" k="62" />
    <hkern u1="&#x2018;" u2="&#x104;" k="170" />
    <hkern u1="&#x2018;" u2="&#xf8;" k="85" />
    <hkern u1="&#x2018;" u2="&#xf6;" k="85" />
    <hkern u1="&#x2018;" u2="&#xf5;" k="85" />
    <hkern u1="&#x2018;" u2="&#xf4;" k="85" />
    <hkern u1="&#x2018;" u2="&#xf3;" k="85" />
    <hkern u1="&#x2018;" u2="&#xf2;" k="85" />
    <hkern u1="&#x2018;" u2="&#xf0;" k="85" />
    <hkern u1="&#x2018;" u2="&#xeb;" k="85" />
    <hkern u1="&#x2018;" u2="&#xea;" k="85" />
    <hkern u1="&#x2018;" u2="&#xe9;" k="85" />
    <hkern u1="&#x2018;" u2="&#xe8;" k="85" />
    <hkern u1="&#x2018;" u2="&#xe7;" k="85" />
    <hkern u1="&#x2018;" u2="&#xe6;" k="62" />
    <hkern u1="&#x2018;" u2="&#xe5;" k="62" />
    <hkern u1="&#x2018;" u2="&#xe4;" k="62" />
    <hkern u1="&#x2018;" u2="&#xe3;" k="62" />
    <hkern u1="&#x2018;" u2="&#xe2;" k="62" />
    <hkern u1="&#x2018;" u2="&#xe1;" k="62" />
    <hkern u1="&#x2018;" u2="&#xe0;" k="62" />
    <hkern u1="&#x2018;" u2="&#xdd;" k="-25" />
    <hkern u1="&#x2018;" u2="&#xd8;" k="48" />
    <hkern u1="&#x2018;" u2="&#xd6;" k="48" />
    <hkern u1="&#x2018;" u2="&#xd5;" k="48" />
    <hkern u1="&#x2018;" u2="&#xd4;" k="48" />
    <hkern u1="&#x2018;" u2="&#xd3;" k="48" />
    <hkern u1="&#x2018;" u2="&#xd2;" k="48" />
    <hkern u1="&#x2018;" u2="&#xc7;" k="48" />
    <hkern u1="&#x2018;" u2="&#xc6;" k="170" />
    <hkern u1="&#x2018;" u2="&#xc5;" k="170" />
    <hkern u1="&#x2018;" u2="&#xc4;" k="170" />
    <hkern u1="&#x2018;" u2="&#xc3;" k="170" />
    <hkern u1="&#x2018;" u2="&#xc2;" k="170" />
    <hkern u1="&#x2018;" u2="&#xc1;" k="170" />
    <hkern u1="&#x2018;" u2="&#xc0;" k="170" />
    <hkern u1="&#x2018;" u2="&#xbb;" k="190" />
    <hkern u1="&#x2018;" u2="&#xb7;" k="190" />
    <hkern u1="&#x2018;" u2="&#xad;" k="190" />
    <hkern u1="&#x2018;" u2="&#xab;" k="190" />
    <hkern u1="&#x2018;" u2="q" k="85" />
    <hkern u1="&#x2018;" u2="o" k="85" />
    <hkern u1="&#x2018;" u2="e" k="85" />
    <hkern u1="&#x2018;" u2="d" k="85" />
    <hkern u1="&#x2018;" u2="c" k="85" />
    <hkern u1="&#x2018;" u2="a" k="62" />
    <hkern u1="&#x2018;" u2="\" k="-55" />
    <hkern u1="&#x2018;" u2="Y" k="-25" />
    <hkern u1="&#x2018;" u2="W" k="-55" />
    <hkern u1="&#x2018;" u2="V" k="-55" />
    <hkern u1="&#x2018;" u2="Q" k="48" />
    <hkern u1="&#x2018;" u2="O" k="48" />
    <hkern u1="&#x2018;" u2="G" k="48" />
    <hkern u1="&#x2018;" u2="C" k="48" />
    <hkern u1="&#x2018;" u2="A" k="170" />
    <hkern u1="&#x2018;" u2="&#x40;" k="48" />
    <hkern u1="&#x2018;" u2="&#x2f;" k="170" />
    <hkern u1="&#x2018;" u2="&#x2e;" k="245" />
    <hkern u1="&#x2018;" u2="&#x2d;" k="190" />
    <hkern u1="&#x2018;" u2="&#x2c;" k="245" />
    <hkern u1="&#x2018;" u2="&#x26;" k="170" />
    <hkern u1="&#x2019;" u2="&#x2206;" k="170" />
    <hkern u1="&#x2019;" u2="&#x203a;" k="190" />
    <hkern u1="&#x2019;" u2="&#x2039;" k="190" />
    <hkern u1="&#x2019;" u2="&#x2022;" k="190" />
    <hkern u1="&#x2019;" u2="&#x201e;" k="245" />
    <hkern u1="&#x2019;" u2="&#x201a;" k="245" />
    <hkern u1="&#x2019;" u2="&#x2014;" k="190" />
    <hkern u1="&#x2019;" u2="&#x2013;" k="190" />
    <hkern u1="&#x2019;" u2="&#x178;" k="-25" />
    <hkern u1="&#x2019;" u2="&#x153;" k="85" />
    <hkern u1="&#x2019;" u2="&#x152;" k="48" />
    <hkern u1="&#x2019;" u2="&#x119;" k="85" />
    <hkern u1="&#x2019;" u2="&#x107;" k="85" />
    <hkern u1="&#x2019;" u2="&#x106;" k="48" />
    <hkern u1="&#x2019;" u2="&#x105;" k="62" />
    <hkern u1="&#x2019;" u2="&#x104;" k="170" />
    <hkern u1="&#x2019;" u2="&#xf8;" k="85" />
    <hkern u1="&#x2019;" u2="&#xf6;" k="85" />
    <hkern u1="&#x2019;" u2="&#xf5;" k="85" />
    <hkern u1="&#x2019;" u2="&#xf4;" k="85" />
    <hkern u1="&#x2019;" u2="&#xf3;" k="85" />
    <hkern u1="&#x2019;" u2="&#xf2;" k="85" />
    <hkern u1="&#x2019;" u2="&#xf0;" k="85" />
    <hkern u1="&#x2019;" u2="&#xeb;" k="85" />
    <hkern u1="&#x2019;" u2="&#xea;" k="85" />
    <hkern u1="&#x2019;" u2="&#xe9;" k="85" />
    <hkern u1="&#x2019;" u2="&#xe8;" k="85" />
    <hkern u1="&#x2019;" u2="&#xe7;" k="85" />
    <hkern u1="&#x2019;" u2="&#xe6;" k="62" />
    <hkern u1="&#x2019;" u2="&#xe5;" k="62" />
    <hkern u1="&#x2019;" u2="&#xe4;" k="62" />
    <hkern u1="&#x2019;" u2="&#xe3;" k="62" />
    <hkern u1="&#x2019;" u2="&#xe2;" k="62" />
    <hkern u1="&#x2019;" u2="&#xe1;" k="62" />
    <hkern u1="&#x2019;" u2="&#xe0;" k="62" />
    <hkern u1="&#x2019;" u2="&#xdd;" k="-25" />
    <hkern u1="&#x2019;" u2="&#xd8;" k="48" />
    <hkern u1="&#x2019;" u2="&#xd6;" k="48" />
    <hkern u1="&#x2019;" u2="&#xd5;" k="48" />
    <hkern u1="&#x2019;" u2="&#xd4;" k="48" />
    <hkern u1="&#x2019;" u2="&#xd3;" k="48" />
    <hkern u1="&#x2019;" u2="&#xd2;" k="48" />
    <hkern u1="&#x2019;" u2="&#xc7;" k="48" />
    <hkern u1="&#x2019;" u2="&#xc6;" k="170" />
    <hkern u1="&#x2019;" u2="&#xc5;" k="170" />
    <hkern u1="&#x2019;" u2="&#xc4;" k="170" />
    <hkern u1="&#x2019;" u2="&#xc3;" k="170" />
    <hkern u1="&#x2019;" u2="&#xc2;" k="170" />
    <hkern u1="&#x2019;" u2="&#xc1;" k="170" />
    <hkern u1="&#x2019;" u2="&#xc0;" k="170" />
    <hkern u1="&#x2019;" u2="&#xbb;" k="190" />
    <hkern u1="&#x2019;" u2="&#xb7;" k="190" />
    <hkern u1="&#x2019;" u2="&#xad;" k="190" />
    <hkern u1="&#x2019;" u2="&#xab;" k="190" />
    <hkern u1="&#x2019;" u2="q" k="85" />
    <hkern u1="&#x2019;" u2="o" k="85" />
    <hkern u1="&#x2019;" u2="e" k="85" />
    <hkern u1="&#x2019;" u2="d" k="85" />
    <hkern u1="&#x2019;" u2="c" k="85" />
    <hkern u1="&#x2019;" u2="a" k="62" />
    <hkern u1="&#x2019;" u2="\" k="-55" />
    <hkern u1="&#x2019;" u2="Y" k="-25" />
    <hkern u1="&#x2019;" u2="W" k="-55" />
    <hkern u1="&#x2019;" u2="V" k="-55" />
    <hkern u1="&#x2019;" u2="Q" k="48" />
    <hkern u1="&#x2019;" u2="O" k="48" />
    <hkern u1="&#x2019;" u2="G" k="48" />
    <hkern u1="&#x2019;" u2="C" k="48" />
    <hkern u1="&#x2019;" u2="A" k="170" />
    <hkern u1="&#x2019;" u2="&#x40;" k="48" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="170" />
    <hkern u1="&#x2019;" u2="&#x2e;" k="245" />
    <hkern u1="&#x2019;" u2="&#x2d;" k="190" />
    <hkern u1="&#x2019;" u2="&#x2c;" k="245" />
    <hkern u1="&#x2019;" u2="&#x26;" k="170" />
    <hkern u1="&#x201a;" u2="&#x2122;" k="245" />
    <hkern u1="&#x201a;" u2="&#x203a;" k="138" />
    <hkern u1="&#x201a;" u2="&#x2039;" k="138" />
    <hkern u1="&#x201a;" u2="&#x2022;" k="138" />
    <hkern u1="&#x201a;" u2="&#x201d;" k="245" />
    <hkern u1="&#x201a;" u2="&#x201c;" k="245" />
    <hkern u1="&#x201a;" u2="&#x2019;" k="245" />
    <hkern u1="&#x201a;" u2="&#x2018;" k="245" />
    <hkern u1="&#x201a;" u2="&#x2014;" k="138" />
    <hkern u1="&#x201a;" u2="&#x2013;" k="138" />
    <hkern u1="&#x201a;" u2="&#x178;" k="135" />
    <hkern u1="&#x201a;" u2="&#x152;" k="58" />
    <hkern u1="&#x201a;" u2="&#x106;" k="58" />
    <hkern u1="&#x201a;" u2="&#xdd;" k="135" />
    <hkern u1="&#x201a;" u2="&#xd8;" k="58" />
    <hkern u1="&#x201a;" u2="&#xd6;" k="58" />
    <hkern u1="&#x201a;" u2="&#xd5;" k="58" />
    <hkern u1="&#x201a;" u2="&#xd4;" k="58" />
    <hkern u1="&#x201a;" u2="&#xd3;" k="58" />
    <hkern u1="&#x201a;" u2="&#xd2;" k="58" />
    <hkern u1="&#x201a;" u2="&#xc7;" k="58" />
    <hkern u1="&#x201a;" u2="&#xbb;" k="138" />
    <hkern u1="&#x201a;" u2="&#xba;" k="245" />
    <hkern u1="&#x201a;" u2="&#xb7;" k="138" />
    <hkern u1="&#x201a;" u2="&#xb0;" k="245" />
    <hkern u1="&#x201a;" u2="&#xad;" k="138" />
    <hkern u1="&#x201a;" u2="&#xab;" k="138" />
    <hkern u1="&#x201a;" u2="&#xaa;" k="245" />
    <hkern u1="&#x201a;" u2="y" k="125" />
    <hkern u1="&#x201a;" u2="w" k="50" />
    <hkern u1="&#x201a;" u2="v" k="125" />
    <hkern u1="&#x201a;" u2="\" k="180" />
    <hkern u1="&#x201a;" u2="Y" k="135" />
    <hkern u1="&#x201a;" u2="W" k="110" />
    <hkern u1="&#x201a;" u2="V" k="180" />
    <hkern u1="&#x201a;" u2="T" k="180" />
    <hkern u1="&#x201a;" u2="Q" k="58" />
    <hkern u1="&#x201a;" u2="O" k="58" />
    <hkern u1="&#x201a;" u2="G" k="58" />
    <hkern u1="&#x201a;" u2="C" k="58" />
    <hkern u1="&#x201a;" u2="&#x40;" k="58" />
    <hkern u1="&#x201a;" u2="&#x2d;" k="138" />
    <hkern u1="&#x201a;" u2="&#x2a;" k="245" />
    <hkern u1="&#x201a;" u2="&#x27;" k="245" />
    <hkern u1="&#x201a;" u2="&#x22;" k="245" />
    <hkern u1="&#x201c;" u2="&#x2206;" k="170" />
    <hkern u1="&#x201c;" u2="&#x203a;" k="190" />
    <hkern u1="&#x201c;" u2="&#x2039;" k="190" />
    <hkern u1="&#x201c;" u2="&#x2022;" k="190" />
    <hkern u1="&#x201c;" u2="&#x201e;" k="245" />
    <hkern u1="&#x201c;" u2="&#x201a;" k="245" />
    <hkern u1="&#x201c;" u2="&#x2014;" k="190" />
    <hkern u1="&#x201c;" u2="&#x2013;" k="190" />
    <hkern u1="&#x201c;" u2="&#x178;" k="-25" />
    <hkern u1="&#x201c;" u2="&#x153;" k="85" />
    <hkern u1="&#x201c;" u2="&#x152;" k="48" />
    <hkern u1="&#x201c;" u2="&#x119;" k="85" />
    <hkern u1="&#x201c;" u2="&#x107;" k="85" />
    <hkern u1="&#x201c;" u2="&#x106;" k="48" />
    <hkern u1="&#x201c;" u2="&#x105;" k="62" />
    <hkern u1="&#x201c;" u2="&#x104;" k="170" />
    <hkern u1="&#x201c;" u2="&#xf8;" k="85" />
    <hkern u1="&#x201c;" u2="&#xf6;" k="85" />
    <hkern u1="&#x201c;" u2="&#xf5;" k="85" />
    <hkern u1="&#x201c;" u2="&#xf4;" k="85" />
    <hkern u1="&#x201c;" u2="&#xf3;" k="85" />
    <hkern u1="&#x201c;" u2="&#xf2;" k="85" />
    <hkern u1="&#x201c;" u2="&#xf0;" k="85" />
    <hkern u1="&#x201c;" u2="&#xeb;" k="85" />
    <hkern u1="&#x201c;" u2="&#xea;" k="85" />
    <hkern u1="&#x201c;" u2="&#xe9;" k="85" />
    <hkern u1="&#x201c;" u2="&#xe8;" k="85" />
    <hkern u1="&#x201c;" u2="&#xe7;" k="85" />
    <hkern u1="&#x201c;" u2="&#xe6;" k="62" />
    <hkern u1="&#x201c;" u2="&#xe5;" k="62" />
    <hkern u1="&#x201c;" u2="&#xe4;" k="62" />
    <hkern u1="&#x201c;" u2="&#xe3;" k="62" />
    <hkern u1="&#x201c;" u2="&#xe2;" k="62" />
    <hkern u1="&#x201c;" u2="&#xe1;" k="62" />
    <hkern u1="&#x201c;" u2="&#xe0;" k="62" />
    <hkern u1="&#x201c;" u2="&#xdd;" k="-25" />
    <hkern u1="&#x201c;" u2="&#xd8;" k="48" />
    <hkern u1="&#x201c;" u2="&#xd6;" k="48" />
    <hkern u1="&#x201c;" u2="&#xd5;" k="48" />
    <hkern u1="&#x201c;" u2="&#xd4;" k="48" />
    <hkern u1="&#x201c;" u2="&#xd3;" k="48" />
    <hkern u1="&#x201c;" u2="&#xd2;" k="48" />
    <hkern u1="&#x201c;" u2="&#xc7;" k="48" />
    <hkern u1="&#x201c;" u2="&#xc6;" k="170" />
    <hkern u1="&#x201c;" u2="&#xc5;" k="170" />
    <hkern u1="&#x201c;" u2="&#xc4;" k="170" />
    <hkern u1="&#x201c;" u2="&#xc3;" k="170" />
    <hkern u1="&#x201c;" u2="&#xc2;" k="170" />
    <hkern u1="&#x201c;" u2="&#xc1;" k="170" />
    <hkern u1="&#x201c;" u2="&#xc0;" k="170" />
    <hkern u1="&#x201c;" u2="&#xbb;" k="190" />
    <hkern u1="&#x201c;" u2="&#xb7;" k="190" />
    <hkern u1="&#x201c;" u2="&#xad;" k="190" />
    <hkern u1="&#x201c;" u2="&#xab;" k="190" />
    <hkern u1="&#x201c;" u2="q" k="85" />
    <hkern u1="&#x201c;" u2="o" k="85" />
    <hkern u1="&#x201c;" u2="e" k="85" />
    <hkern u1="&#x201c;" u2="d" k="85" />
    <hkern u1="&#x201c;" u2="c" k="85" />
    <hkern u1="&#x201c;" u2="a" k="62" />
    <hkern u1="&#x201c;" u2="\" k="-55" />
    <hkern u1="&#x201c;" u2="Y" k="-25" />
    <hkern u1="&#x201c;" u2="W" k="-55" />
    <hkern u1="&#x201c;" u2="V" k="-55" />
    <hkern u1="&#x201c;" u2="Q" k="48" />
    <hkern u1="&#x201c;" u2="O" k="48" />
    <hkern u1="&#x201c;" u2="G" k="48" />
    <hkern u1="&#x201c;" u2="C" k="48" />
    <hkern u1="&#x201c;" u2="A" k="170" />
    <hkern u1="&#x201c;" u2="&#x40;" k="48" />
    <hkern u1="&#x201c;" u2="&#x2f;" k="170" />
    <hkern u1="&#x201c;" u2="&#x2e;" k="245" />
    <hkern u1="&#x201c;" u2="&#x2d;" k="190" />
    <hkern u1="&#x201c;" u2="&#x2c;" k="245" />
    <hkern u1="&#x201c;" u2="&#x26;" k="170" />
    <hkern u1="&#x201d;" u2="&#x2206;" k="170" />
    <hkern u1="&#x201d;" u2="&#x203a;" k="190" />
    <hkern u1="&#x201d;" u2="&#x2039;" k="190" />
    <hkern u1="&#x201d;" u2="&#x2022;" k="190" />
    <hkern u1="&#x201d;" u2="&#x201e;" k="245" />
    <hkern u1="&#x201d;" u2="&#x201a;" k="245" />
    <hkern u1="&#x201d;" u2="&#x2014;" k="190" />
    <hkern u1="&#x201d;" u2="&#x2013;" k="190" />
    <hkern u1="&#x201d;" u2="&#x178;" k="-25" />
    <hkern u1="&#x201d;" u2="&#x153;" k="85" />
    <hkern u1="&#x201d;" u2="&#x152;" k="48" />
    <hkern u1="&#x201d;" u2="&#x119;" k="85" />
    <hkern u1="&#x201d;" u2="&#x107;" k="85" />
    <hkern u1="&#x201d;" u2="&#x106;" k="48" />
    <hkern u1="&#x201d;" u2="&#x105;" k="62" />
    <hkern u1="&#x201d;" u2="&#x104;" k="170" />
    <hkern u1="&#x201d;" u2="&#xf8;" k="85" />
    <hkern u1="&#x201d;" u2="&#xf6;" k="85" />
    <hkern u1="&#x201d;" u2="&#xf5;" k="85" />
    <hkern u1="&#x201d;" u2="&#xf4;" k="85" />
    <hkern u1="&#x201d;" u2="&#xf3;" k="85" />
    <hkern u1="&#x201d;" u2="&#xf2;" k="85" />
    <hkern u1="&#x201d;" u2="&#xf0;" k="85" />
    <hkern u1="&#x201d;" u2="&#xeb;" k="85" />
    <hkern u1="&#x201d;" u2="&#xea;" k="85" />
    <hkern u1="&#x201d;" u2="&#xe9;" k="85" />
    <hkern u1="&#x201d;" u2="&#xe8;" k="85" />
    <hkern u1="&#x201d;" u2="&#xe7;" k="85" />
    <hkern u1="&#x201d;" u2="&#xe6;" k="62" />
    <hkern u1="&#x201d;" u2="&#xe5;" k="62" />
    <hkern u1="&#x201d;" u2="&#xe4;" k="62" />
    <hkern u1="&#x201d;" u2="&#xe3;" k="62" />
    <hkern u1="&#x201d;" u2="&#xe2;" k="62" />
    <hkern u1="&#x201d;" u2="&#xe1;" k="62" />
    <hkern u1="&#x201d;" u2="&#xe0;" k="62" />
    <hkern u1="&#x201d;" u2="&#xdd;" k="-25" />
    <hkern u1="&#x201d;" u2="&#xd8;" k="48" />
    <hkern u1="&#x201d;" u2="&#xd6;" k="48" />
    <hkern u1="&#x201d;" u2="&#xd5;" k="48" />
    <hkern u1="&#x201d;" u2="&#xd4;" k="48" />
    <hkern u1="&#x201d;" u2="&#xd3;" k="48" />
    <hkern u1="&#x201d;" u2="&#xd2;" k="48" />
    <hkern u1="&#x201d;" u2="&#xc7;" k="48" />
    <hkern u1="&#x201d;" u2="&#xc6;" k="170" />
    <hkern u1="&#x201d;" u2="&#xc5;" k="170" />
    <hkern u1="&#x201d;" u2="&#xc4;" k="170" />
    <hkern u1="&#x201d;" u2="&#xc3;" k="170" />
    <hkern u1="&#x201d;" u2="&#xc2;" k="170" />
    <hkern u1="&#x201d;" u2="&#xc1;" k="170" />
    <hkern u1="&#x201d;" u2="&#xc0;" k="170" />
    <hkern u1="&#x201d;" u2="&#xbb;" k="190" />
    <hkern u1="&#x201d;" u2="&#xb7;" k="190" />
    <hkern u1="&#x201d;" u2="&#xad;" k="190" />
    <hkern u1="&#x201d;" u2="&#xab;" k="190" />
    <hkern u1="&#x201d;" u2="q" k="85" />
    <hkern u1="&#x201d;" u2="o" k="85" />
    <hkern u1="&#x201d;" u2="e" k="85" />
    <hkern u1="&#x201d;" u2="d" k="85" />
    <hkern u1="&#x201d;" u2="c" k="85" />
    <hkern u1="&#x201d;" u2="a" k="62" />
    <hkern u1="&#x201d;" u2="\" k="-55" />
    <hkern u1="&#x201d;" u2="Y" k="-25" />
    <hkern u1="&#x201d;" u2="W" k="-55" />
    <hkern u1="&#x201d;" u2="V" k="-55" />
    <hkern u1="&#x201d;" u2="Q" k="48" />
    <hkern u1="&#x201d;" u2="O" k="48" />
    <hkern u1="&#x201d;" u2="G" k="48" />
    <hkern u1="&#x201d;" u2="C" k="48" />
    <hkern u1="&#x201d;" u2="A" k="170" />
    <hkern u1="&#x201d;" u2="&#x40;" k="48" />
    <hkern u1="&#x201d;" u2="&#x2f;" k="170" />
    <hkern u1="&#x201d;" u2="&#x2e;" k="245" />
    <hkern u1="&#x201d;" u2="&#x2d;" k="190" />
    <hkern u1="&#x201d;" u2="&#x2c;" k="245" />
    <hkern u1="&#x201d;" u2="&#x26;" k="170" />
    <hkern u1="&#x201e;" u2="&#x2122;" k="245" />
    <hkern u1="&#x201e;" u2="&#x203a;" k="138" />
    <hkern u1="&#x201e;" u2="&#x2039;" k="138" />
    <hkern u1="&#x201e;" u2="&#x2022;" k="138" />
    <hkern u1="&#x201e;" u2="&#x201d;" k="245" />
    <hkern u1="&#x201e;" u2="&#x201c;" k="245" />
    <hkern u1="&#x201e;" u2="&#x2019;" k="245" />
    <hkern u1="&#x201e;" u2="&#x2018;" k="245" />
    <hkern u1="&#x201e;" u2="&#x2014;" k="138" />
    <hkern u1="&#x201e;" u2="&#x2013;" k="138" />
    <hkern u1="&#x201e;" u2="&#x178;" k="135" />
    <hkern u1="&#x201e;" u2="&#x152;" k="58" />
    <hkern u1="&#x201e;" u2="&#x106;" k="58" />
    <hkern u1="&#x201e;" u2="&#xdd;" k="135" />
    <hkern u1="&#x201e;" u2="&#xd8;" k="58" />
    <hkern u1="&#x201e;" u2="&#xd6;" k="58" />
    <hkern u1="&#x201e;" u2="&#xd5;" k="58" />
    <hkern u1="&#x201e;" u2="&#xd4;" k="58" />
    <hkern u1="&#x201e;" u2="&#xd3;" k="58" />
    <hkern u1="&#x201e;" u2="&#xd2;" k="58" />
    <hkern u1="&#x201e;" u2="&#xc7;" k="58" />
    <hkern u1="&#x201e;" u2="&#xbb;" k="138" />
    <hkern u1="&#x201e;" u2="&#xba;" k="245" />
    <hkern u1="&#x201e;" u2="&#xb7;" k="138" />
    <hkern u1="&#x201e;" u2="&#xb0;" k="245" />
    <hkern u1="&#x201e;" u2="&#xad;" k="138" />
    <hkern u1="&#x201e;" u2="&#xab;" k="138" />
    <hkern u1="&#x201e;" u2="&#xaa;" k="245" />
    <hkern u1="&#x201e;" u2="y" k="125" />
    <hkern u1="&#x201e;" u2="w" k="50" />
    <hkern u1="&#x201e;" u2="v" k="125" />
    <hkern u1="&#x201e;" u2="\" k="180" />
    <hkern u1="&#x201e;" u2="Y" k="135" />
    <hkern u1="&#x201e;" u2="W" k="110" />
    <hkern u1="&#x201e;" u2="V" k="180" />
    <hkern u1="&#x201e;" u2="T" k="180" />
    <hkern u1="&#x201e;" u2="Q" k="58" />
    <hkern u1="&#x201e;" u2="O" k="58" />
    <hkern u1="&#x201e;" u2="G" k="58" />
    <hkern u1="&#x201e;" u2="C" k="58" />
    <hkern u1="&#x201e;" u2="&#x40;" k="58" />
    <hkern u1="&#x201e;" u2="&#x2d;" k="138" />
    <hkern u1="&#x201e;" u2="&#x2a;" k="245" />
    <hkern u1="&#x201e;" u2="&#x27;" k="245" />
    <hkern u1="&#x201e;" u2="&#x22;" k="245" />
    <hkern u1="&#x2022;" u2="&#x2206;" k="35" />
    <hkern u1="&#x2022;" u2="&#x2122;" k="190" />
    <hkern u1="&#x2022;" u2="&#x201e;" k="138" />
    <hkern u1="&#x2022;" u2="&#x201d;" k="190" />
    <hkern u1="&#x2022;" u2="&#x201c;" k="190" />
    <hkern u1="&#x2022;" u2="&#x201a;" k="138" />
    <hkern u1="&#x2022;" u2="&#x2019;" k="190" />
    <hkern u1="&#x2022;" u2="&#x2018;" k="190" />
    <hkern u1="&#x2022;" u2="&#x17d;" k="43" />
    <hkern u1="&#x2022;" u2="&#x17b;" k="43" />
    <hkern u1="&#x2022;" u2="&#x179;" k="43" />
    <hkern u1="&#x2022;" u2="&#x178;" k="160" />
    <hkern u1="&#x2022;" u2="&#x104;" k="35" />
    <hkern u1="&#x2022;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2022;" u2="&#xc6;" k="35" />
    <hkern u1="&#x2022;" u2="&#xc5;" k="35" />
    <hkern u1="&#x2022;" u2="&#xc4;" k="35" />
    <hkern u1="&#x2022;" u2="&#xc3;" k="35" />
    <hkern u1="&#x2022;" u2="&#xc2;" k="35" />
    <hkern u1="&#x2022;" u2="&#xc1;" k="35" />
    <hkern u1="&#x2022;" u2="&#xc0;" k="35" />
    <hkern u1="&#x2022;" u2="&#xba;" k="190" />
    <hkern u1="&#x2022;" u2="&#xb0;" k="190" />
    <hkern u1="&#x2022;" u2="&#xaa;" k="190" />
    <hkern u1="&#x2022;" u2="\" k="105" />
    <hkern u1="&#x2022;" u2="Z" k="43" />
    <hkern u1="&#x2022;" u2="Y" k="160" />
    <hkern u1="&#x2022;" u2="X" k="55" />
    <hkern u1="&#x2022;" u2="W" k="25" />
    <hkern u1="&#x2022;" u2="V" k="105" />
    <hkern u1="&#x2022;" u2="T" k="180" />
    <hkern u1="&#x2022;" u2="A" k="35" />
    <hkern u1="&#x2022;" u2="&#x2f;" k="35" />
    <hkern u1="&#x2022;" u2="&#x2e;" k="138" />
    <hkern u1="&#x2022;" u2="&#x2c;" k="138" />
    <hkern u1="&#x2022;" u2="&#x2a;" k="190" />
    <hkern u1="&#x2022;" u2="&#x27;" k="190" />
    <hkern u1="&#x2022;" u2="&#x26;" k="35" />
    <hkern u1="&#x2022;" u2="&#x22;" k="190" />
    <hkern u1="&#x2039;" u2="&#x2206;" k="35" />
    <hkern u1="&#x2039;" u2="&#x2122;" k="190" />
    <hkern u1="&#x2039;" u2="&#x201e;" k="138" />
    <hkern u1="&#x2039;" u2="&#x201d;" k="190" />
    <hkern u1="&#x2039;" u2="&#x201c;" k="190" />
    <hkern u1="&#x2039;" u2="&#x201a;" k="138" />
    <hkern u1="&#x2039;" u2="&#x2019;" k="190" />
    <hkern u1="&#x2039;" u2="&#x2018;" k="190" />
    <hkern u1="&#x2039;" u2="&#x17d;" k="43" />
    <hkern u1="&#x2039;" u2="&#x17b;" k="43" />
    <hkern u1="&#x2039;" u2="&#x179;" k="43" />
    <hkern u1="&#x2039;" u2="&#x178;" k="160" />
    <hkern u1="&#x2039;" u2="&#x104;" k="35" />
    <hkern u1="&#x2039;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2039;" u2="&#xc6;" k="35" />
    <hkern u1="&#x2039;" u2="&#xc5;" k="35" />
    <hkern u1="&#x2039;" u2="&#xc4;" k="35" />
    <hkern u1="&#x2039;" u2="&#xc3;" k="35" />
    <hkern u1="&#x2039;" u2="&#xc2;" k="35" />
    <hkern u1="&#x2039;" u2="&#xc1;" k="35" />
    <hkern u1="&#x2039;" u2="&#xc0;" k="35" />
    <hkern u1="&#x2039;" u2="&#xba;" k="190" />
    <hkern u1="&#x2039;" u2="&#xb0;" k="190" />
    <hkern u1="&#x2039;" u2="&#xaa;" k="190" />
    <hkern u1="&#x2039;" u2="\" k="105" />
    <hkern u1="&#x2039;" u2="Z" k="43" />
    <hkern u1="&#x2039;" u2="Y" k="160" />
    <hkern u1="&#x2039;" u2="X" k="55" />
    <hkern u1="&#x2039;" u2="W" k="25" />
    <hkern u1="&#x2039;" u2="V" k="105" />
    <hkern u1="&#x2039;" u2="T" k="180" />
    <hkern u1="&#x2039;" u2="A" k="35" />
    <hkern u1="&#x2039;" u2="&#x2f;" k="35" />
    <hkern u1="&#x2039;" u2="&#x2e;" k="138" />
    <hkern u1="&#x2039;" u2="&#x2c;" k="138" />
    <hkern u1="&#x2039;" u2="&#x2a;" k="190" />
    <hkern u1="&#x2039;" u2="&#x27;" k="190" />
    <hkern u1="&#x2039;" u2="&#x26;" k="35" />
    <hkern u1="&#x2039;" u2="&#x22;" k="190" />
    <hkern u1="&#x203a;" u2="&#x2206;" k="35" />
    <hkern u1="&#x203a;" u2="&#x2122;" k="190" />
    <hkern u1="&#x203a;" u2="&#x201e;" k="138" />
    <hkern u1="&#x203a;" u2="&#x201d;" k="190" />
    <hkern u1="&#x203a;" u2="&#x201c;" k="190" />
    <hkern u1="&#x203a;" u2="&#x201a;" k="138" />
    <hkern u1="&#x203a;" u2="&#x2019;" k="190" />
    <hkern u1="&#x203a;" u2="&#x2018;" k="190" />
    <hkern u1="&#x203a;" u2="&#x17d;" k="43" />
    <hkern u1="&#x203a;" u2="&#x17b;" k="43" />
    <hkern u1="&#x203a;" u2="&#x179;" k="43" />
    <hkern u1="&#x203a;" u2="&#x178;" k="160" />
    <hkern u1="&#x203a;" u2="&#x104;" k="35" />
    <hkern u1="&#x203a;" u2="&#xdd;" k="160" />
    <hkern u1="&#x203a;" u2="&#xc6;" k="35" />
    <hkern u1="&#x203a;" u2="&#xc5;" k="35" />
    <hkern u1="&#x203a;" u2="&#xc4;" k="35" />
    <hkern u1="&#x203a;" u2="&#xc3;" k="35" />
    <hkern u1="&#x203a;" u2="&#xc2;" k="35" />
    <hkern u1="&#x203a;" u2="&#xc1;" k="35" />
    <hkern u1="&#x203a;" u2="&#xc0;" k="35" />
    <hkern u1="&#x203a;" u2="&#xba;" k="190" />
    <hkern u1="&#x203a;" u2="&#xb0;" k="190" />
    <hkern u1="&#x203a;" u2="&#xaa;" k="190" />
    <hkern u1="&#x203a;" u2="\" k="105" />
    <hkern u1="&#x203a;" u2="Z" k="43" />
    <hkern u1="&#x203a;" u2="Y" k="160" />
    <hkern u1="&#x203a;" u2="X" k="55" />
    <hkern u1="&#x203a;" u2="W" k="25" />
    <hkern u1="&#x203a;" u2="V" k="105" />
    <hkern u1="&#x203a;" u2="T" k="180" />
    <hkern u1="&#x203a;" u2="A" k="35" />
    <hkern u1="&#x203a;" u2="&#x2f;" k="35" />
    <hkern u1="&#x203a;" u2="&#x2e;" k="138" />
    <hkern u1="&#x203a;" u2="&#x2c;" k="138" />
    <hkern u1="&#x203a;" u2="&#x2a;" k="190" />
    <hkern u1="&#x203a;" u2="&#x27;" k="190" />
    <hkern u1="&#x203a;" u2="&#x26;" k="35" />
    <hkern u1="&#x203a;" u2="&#x22;" k="190" />
    <hkern u1="&#x2122;" u2="&#x2206;" k="170" />
    <hkern u1="&#x2122;" u2="&#x203a;" k="190" />
    <hkern u1="&#x2122;" u2="&#x2039;" k="190" />
    <hkern u1="&#x2122;" u2="&#x2022;" k="190" />
    <hkern u1="&#x2122;" u2="&#x201e;" k="245" />
    <hkern u1="&#x2122;" u2="&#x201a;" k="245" />
    <hkern u1="&#x2122;" u2="&#x2014;" k="190" />
    <hkern u1="&#x2122;" u2="&#x2013;" k="190" />
    <hkern u1="&#x2122;" u2="&#x178;" k="-25" />
    <hkern u1="&#x2122;" u2="&#x153;" k="85" />
    <hkern u1="&#x2122;" u2="&#x152;" k="48" />
    <hkern u1="&#x2122;" u2="&#x119;" k="85" />
    <hkern u1="&#x2122;" u2="&#x107;" k="85" />
    <hkern u1="&#x2122;" u2="&#x106;" k="48" />
    <hkern u1="&#x2122;" u2="&#x105;" k="62" />
    <hkern u1="&#x2122;" u2="&#x104;" k="170" />
    <hkern u1="&#x2122;" u2="&#xf8;" k="85" />
    <hkern u1="&#x2122;" u2="&#xf6;" k="85" />
    <hkern u1="&#x2122;" u2="&#xf5;" k="85" />
    <hkern u1="&#x2122;" u2="&#xf4;" k="85" />
    <hkern u1="&#x2122;" u2="&#xf3;" k="85" />
    <hkern u1="&#x2122;" u2="&#xf2;" k="85" />
    <hkern u1="&#x2122;" u2="&#xf0;" k="85" />
    <hkern u1="&#x2122;" u2="&#xeb;" k="85" />
    <hkern u1="&#x2122;" u2="&#xea;" k="85" />
    <hkern u1="&#x2122;" u2="&#xe9;" k="85" />
    <hkern u1="&#x2122;" u2="&#xe8;" k="85" />
    <hkern u1="&#x2122;" u2="&#xe7;" k="85" />
    <hkern u1="&#x2122;" u2="&#xe6;" k="62" />
    <hkern u1="&#x2122;" u2="&#xe5;" k="62" />
    <hkern u1="&#x2122;" u2="&#xe4;" k="62" />
    <hkern u1="&#x2122;" u2="&#xe3;" k="62" />
    <hkern u1="&#x2122;" u2="&#xe2;" k="62" />
    <hkern u1="&#x2122;" u2="&#xe1;" k="62" />
    <hkern u1="&#x2122;" u2="&#xe0;" k="62" />
    <hkern u1="&#x2122;" u2="&#xdd;" k="-25" />
    <hkern u1="&#x2122;" u2="&#xd8;" k="48" />
    <hkern u1="&#x2122;" u2="&#xd6;" k="48" />
    <hkern u1="&#x2122;" u2="&#xd5;" k="48" />
    <hkern u1="&#x2122;" u2="&#xd4;" k="48" />
    <hkern u1="&#x2122;" u2="&#xd3;" k="48" />
    <hkern u1="&#x2122;" u2="&#xd2;" k="48" />
    <hkern u1="&#x2122;" u2="&#xc7;" k="48" />
    <hkern u1="&#x2122;" u2="&#xc6;" k="170" />
    <hkern u1="&#x2122;" u2="&#xc5;" k="170" />
    <hkern u1="&#x2122;" u2="&#xc4;" k="170" />
    <hkern u1="&#x2122;" u2="&#xc3;" k="170" />
    <hkern u1="&#x2122;" u2="&#xc2;" k="170" />
    <hkern u1="&#x2122;" u2="&#xc1;" k="170" />
    <hkern u1="&#x2122;" u2="&#xc0;" k="170" />
    <hkern u1="&#x2122;" u2="&#xbb;" k="190" />
    <hkern u1="&#x2122;" u2="&#xb7;" k="190" />
    <hkern u1="&#x2122;" u2="&#xad;" k="190" />
    <hkern u1="&#x2122;" u2="&#xab;" k="190" />
    <hkern u1="&#x2122;" u2="q" k="85" />
    <hkern u1="&#x2122;" u2="o" k="85" />
    <hkern u1="&#x2122;" u2="e" k="85" />
    <hkern u1="&#x2122;" u2="d" k="85" />
    <hkern u1="&#x2122;" u2="c" k="85" />
    <hkern u1="&#x2122;" u2="a" k="62" />
    <hkern u1="&#x2122;" u2="\" k="-55" />
    <hkern u1="&#x2122;" u2="Y" k="-25" />
    <hkern u1="&#x2122;" u2="W" k="-55" />
    <hkern u1="&#x2122;" u2="V" k="-55" />
    <hkern u1="&#x2122;" u2="Q" k="48" />
    <hkern u1="&#x2122;" u2="O" k="48" />
    <hkern u1="&#x2122;" u2="G" k="48" />
    <hkern u1="&#x2122;" u2="C" k="48" />
    <hkern u1="&#x2122;" u2="A" k="170" />
    <hkern u1="&#x2122;" u2="&#x40;" k="48" />
    <hkern u1="&#x2122;" u2="&#x2f;" k="170" />
    <hkern u1="&#x2122;" u2="&#x2e;" k="245" />
    <hkern u1="&#x2122;" u2="&#x2d;" k="190" />
    <hkern u1="&#x2122;" u2="&#x2c;" k="245" />
    <hkern u1="&#x2122;" u2="&#x26;" k="170" />
    <hkern u1="&#x2206;" u2="&#x2122;" k="170" />
    <hkern u1="&#x2206;" u2="&#x203a;" k="35" />
    <hkern u1="&#x2206;" u2="&#x2039;" k="35" />
    <hkern u1="&#x2206;" u2="&#x2022;" k="35" />
    <hkern u1="&#x2206;" u2="&#x201d;" k="170" />
    <hkern u1="&#x2206;" u2="&#x201c;" k="170" />
    <hkern u1="&#x2206;" u2="&#x2019;" k="170" />
    <hkern u1="&#x2206;" u2="&#x2018;" k="170" />
    <hkern u1="&#x2206;" u2="&#x2014;" k="35" />
    <hkern u1="&#x2206;" u2="&#x2013;" k="35" />
    <hkern u1="&#x2206;" u2="&#x178;" k="140" />
    <hkern u1="&#x2206;" u2="&#x152;" k="30" />
    <hkern u1="&#x2206;" u2="&#x106;" k="30" />
    <hkern u1="&#x2206;" u2="&#xdd;" k="140" />
    <hkern u1="&#x2206;" u2="&#xdc;" k="58" />
    <hkern u1="&#x2206;" u2="&#xdb;" k="58" />
    <hkern u1="&#x2206;" u2="&#xda;" k="58" />
    <hkern u1="&#x2206;" u2="&#xd9;" k="58" />
    <hkern u1="&#x2206;" u2="&#xd8;" k="30" />
    <hkern u1="&#x2206;" u2="&#xd6;" k="30" />
    <hkern u1="&#x2206;" u2="&#xd5;" k="30" />
    <hkern u1="&#x2206;" u2="&#xd4;" k="30" />
    <hkern u1="&#x2206;" u2="&#xd3;" k="30" />
    <hkern u1="&#x2206;" u2="&#xd2;" k="30" />
    <hkern u1="&#x2206;" u2="&#xc7;" k="30" />
    <hkern u1="&#x2206;" u2="&#xbb;" k="35" />
    <hkern u1="&#x2206;" u2="&#xba;" k="170" />
    <hkern u1="&#x2206;" u2="&#xb9;" k="160" />
    <hkern u1="&#x2206;" u2="&#xb7;" k="35" />
    <hkern u1="&#x2206;" u2="&#xb3;" k="160" />
    <hkern u1="&#x2206;" u2="&#xb2;" k="160" />
    <hkern u1="&#x2206;" u2="&#xb0;" k="170" />
    <hkern u1="&#x2206;" u2="&#xad;" k="35" />
    <hkern u1="&#x2206;" u2="&#xab;" k="35" />
    <hkern u1="&#x2206;" u2="&#xaa;" k="170" />
    <hkern u1="&#x2206;" u2="y" k="70" />
    <hkern u1="&#x2206;" u2="v" k="70" />
    <hkern u1="&#x2206;" u2="\" k="95" />
    <hkern u1="&#x2206;" u2="Y" k="140" />
    <hkern u1="&#x2206;" u2="W" k="60" />
    <hkern u1="&#x2206;" u2="V" k="95" />
    <hkern u1="&#x2206;" u2="U" k="58" />
    <hkern u1="&#x2206;" u2="T" k="115" />
    <hkern u1="&#x2206;" u2="Q" k="30" />
    <hkern u1="&#x2206;" u2="O" k="30" />
    <hkern u1="&#x2206;" u2="J" k="-45" />
    <hkern u1="&#x2206;" u2="G" k="30" />
    <hkern u1="&#x2206;" u2="C" k="30" />
    <hkern u1="&#x2206;" u2="&#x40;" k="30" />
    <hkern u1="&#x2206;" u2="&#x3f;" k="48" />
    <hkern u1="&#x2206;" u2="&#x2d;" k="35" />
    <hkern u1="&#x2206;" u2="&#x2a;" k="170" />
    <hkern u1="&#x2206;" u2="&#x27;" k="170" />
    <hkern u1="&#x2206;" u2="&#x22;" k="170" />
  </font>
</defs></svg>
