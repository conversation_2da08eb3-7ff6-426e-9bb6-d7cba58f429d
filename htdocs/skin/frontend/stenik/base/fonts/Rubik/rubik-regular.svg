<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="rubikregular" horiz-adv-x="1126" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="503" />
<glyph unicode="&#xfb01;" horiz-adv-x="1112" d="M37 952v66q0 20 13 33.5t34 13.5h176v102q0 166 81 257.5t265 91.5h307q20 0 32.5 -13.5t12.5 -34.5v-65q0 -20 -12 -32.5t-33 -12.5h-303q-94 0 -131 -49t-37 -152v-92h471q20 0 32.5 -13.5t12.5 -33.5v-971q0 -20 -12 -33.5t-33 -13.5h-90q-20 0 -33.5 13.5t-13.5 33.5 v860h-334v-860q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v860h-176q-20 0 -33.5 12.5t-13.5 32.5z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1218" d="M37 952v66q0 20 13 33.5t34 13.5h176v102q0 166 81 257.5t265 91.5h115q20 0 32.5 -13.5t12.5 -34.5v-65q0 -20 -12.5 -32.5t-32.5 -12.5h-111q-94 0 -131 -49t-37 -152v-92h441v403q0 20 12 34t33 14h90q20 0 33.5 -13.5t13.5 -34.5v-1421q0 -20 -13.5 -33.5 t-33.5 -13.5h-90q-20 0 -32.5 13.5t-12.5 33.5v860h-441v-860q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v860h-176q-20 0 -33.5 12.5t-13.5 32.5z" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="503" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="503" />
<glyph unicode="&#x09;" horiz-adv-x="503" />
<glyph unicode="&#xa0;" horiz-adv-x="503" />
<glyph unicode="!" horiz-adv-x="497" d="M139 47v125q0 20 13.5 33.5t33.5 13.5h123q20 0 34.5 -13t14.5 -34v-125q0 -20 -14 -33.5t-35 -13.5h-123q-20 0 -33.5 13.5t-13.5 33.5zM158 383v1003q0 23 13 35.5t34 12.5h88q20 0 33.5 -13.5t13.5 -34.5v-1003q0 -20 -13.5 -33.5t-33.5 -13.5h-88q-21 0 -34 13 t-13 34z" />
<glyph unicode="&#x22;" horiz-adv-x="757" d="M117 1407v8q0 18 11 28.5t30 10.5h129q20 0 31.5 -13.5t9.5 -33.5l-35 -356q-4 -45 -41 -45h-59q-37 0 -41 45zM430 1407v4q0 20 11.5 31.5t29.5 11.5h129q20 0 31.5 -13.5t9.5 -33.5l-35 -356q-4 -45 -41 -45h-59q-37 0 -41 45z" />
<glyph unicode="#" horiz-adv-x="1427" d="M102 418v65q0 20 13.5 32.5t34.5 12.5h227l53 365h-227q-20 0 -33.5 12.5t-13.5 32.5v66q0 20 13 32.5t34 12.5h252l37 249q4 18 16 30.5t31 12.5h69q18 0 30.5 -12t10.5 -31l-37 -249h308l36 249q4 18 16.5 30.5t31.5 12.5h69q18 0 30.5 -12t10.5 -31l-37 -249h203 q20 0 32.5 -12.5t12.5 -32.5v-66q0 -20 -12.5 -32.5t-32.5 -12.5h-225l-54 -365h226q20 0 32.5 -12t12.5 -33v-65q0 -20 -12.5 -32.5t-32.5 -12.5h-250l-37 -250q-4 -18 -16.5 -30.5t-30.5 -12.5h-70q-18 0 -31.5 12t-11.5 31l39 250h-307l-37 -250q-4 -18 -16.5 -30.5 t-30.5 -12.5h-70q-18 0 -31.5 12t-11.5 31l39 250h-204q-20 0 -34 12t-14 33zM537 528h305l53 365h-305z" />
<glyph unicode="$" horiz-adv-x="1259" d="M84 360q0 16 12.5 28.5t30.5 12.5h98q39 0 54 -41q18 -86 105 -148t249 -62q174 0 260 61t86 170q0 70 -42 115t-125 78.5t-249 82.5q-158 45 -252 96.5t-141 126t-47 189.5q0 154 112.5 258.5t311.5 122.5v141q0 23 13 35t34 12h74q20 0 32.5 -13t12.5 -34v-143 q131 -16 225 -73.5t142 -134.5t53 -155q0 -18 -12.5 -30.5t-30.5 -12.5h-103q-16 0 -31.5 10.5t-19.5 30.5q-12 88 -98 145.5t-215 57.5q-139 0 -221.5 -54t-82.5 -161q0 -72 38 -117t117 -79.5t228 -79.5q174 -49 274.5 -98.5t150 -122t49.5 -187.5q0 -174 -124 -280.5 t-339 -122.5v-142q0 -20 -12.5 -33.5t-32.5 -13.5h-74q-21 0 -34 12.5t-13 34.5v144q-143 12 -246.5 66t-158 135t-58.5 173z" />
<glyph unicode="%" horiz-adv-x="1554" d="M100 1071q0 59 2 96q6 127 80 202t213 75t214 -75t81 -202q4 -74 4 -96q0 -20 -4 -90q-6 -125 -84 -195.5t-211 -70.5t-209.5 70.5t-83.5 195.5q-2 35 -2 90zM180 41q0 12 8 22l1012 1332q16 23 29.5 31t38.5 8h65q18 0 29.5 -11.5t11.5 -29.5q0 -12 -8 -23l-1010 -1331 q-16 -20 -29.5 -29.5t-37.5 -9.5h-66q-18 0 -30.5 11.5t-12.5 29.5zM246 1075q0 -49 2 -86q6 -61 40 -105t107 -44q74 0 109 43t41 106q4 74 4 86q0 18 -4 84q-4 63 -40 107.5t-110 44.5t-108.5 -44t-38.5 -108q-2 -33 -2 -84zM860 346q0 20 4 103q6 127 80 201.5t213 74.5 t214 -75t81 -201q4 -74 4 -95q0 -20 -4 -90q-6 -125 -84 -200.5t-211 -75.5t-210 75.5t-83 200.5q-4 61 -4 82zM1008 358q0 -51 2 -88q4 -61 38.5 -105t108.5 -44t108.5 43t41.5 106q4 74 4 88q0 16 -4 82q-4 63 -40 107.5t-110 44.5t-108.5 -44t-38.5 -108q-2 -33 -2 -82z " />
<glyph unicode="&#x26;" horiz-adv-x="1417" d="M117 383q0 129 75.5 220t213.5 171q-98 104 -138.5 181t-40.5 163q0 90 47.5 167t134.5 123t199 46q109 0 195 -44t133 -120t47 -170q0 -125 -81 -210t-232 -171l352 -346q82 135 111 316q2 18 13 29.5t32 11.5h84q18 0 30.5 -12.5t12.5 -30.5q-4 -92 -46 -207t-118 -224 l209 -206q16 -12 16 -31q0 -39 -39 -39h-108q-35 0 -56 20l-135 136q-180 -176 -442 -176q-137 0 -243.5 51t-166 143t-59.5 209zM297 387q0 -115 85 -181.5t204 -66.5q86 0 170 35t151 105l-389 376q-106 -59 -163.5 -120.5t-57.5 -147.5zM406 1116q0 -55 31.5 -110.5 t121.5 -149.5q123 66 186.5 123t63.5 137q0 84 -57.5 134t-145.5 50q-82 0 -141 -51t-59 -133z" />
<glyph unicode="'" horiz-adv-x="444" d="M117 1407v8q0 18 11 28.5t30 10.5h129q20 0 31.5 -13.5t9.5 -33.5l-35 -356q-4 -45 -41 -45h-59q-37 0 -41 45z" />
<glyph unicode="(" horiz-adv-x="688" d="M119 633q0 342 45 550t148.5 307t279.5 99q20 0 33.5 -13t13.5 -34v-67q0 -18 -12.5 -31.5t-28.5 -13.5q-117 -2 -182.5 -79t-94 -248t-28.5 -470q0 -301 28.5 -472t94 -248t182.5 -79q16 0 28.5 -13.5t12.5 -31.5v-68q0 -20 -13.5 -33.5t-33.5 -13.5q-176 0 -279.5 99.5 t-148.5 308.5t-45 551z" />
<glyph unicode=")" horiz-adv-x="688" d="M51 -211q0 18 12.5 31.5t28.5 13.5q115 2 181.5 79t95 249t28.5 471t-28.5 470t-95 247.5t-181.5 79.5q-16 0 -28.5 13t-12.5 32v67q0 20 13.5 33.5t31.5 13.5q176 0 279.5 -99t148.5 -307t45 -550t-45 -551t-148.5 -308.5t-279.5 -99.5q-18 0 -31.5 13.5t-13.5 33.5v68z " />
<glyph unicode="*" horiz-adv-x="913" d="M115 1208l35 105q4 16 21 24.5t34 3.5l16 -8l176 -98l-39 199l-2 16q0 16 13.5 30.5t31.5 14.5h111q18 0 31.5 -14.5t13.5 -30.5l-2 -16l-43 -201l180 100l17 8q4 2 10 2q16 0 29.5 -8t15.5 -22l35 -105l2 -12q0 -14 -9.5 -27.5t-21.5 -17.5l-16 -4l-205 -25l151 -135 q12 -12 13 -14q8 -12 8 -25q0 -25 -19 -37l-88 -65q-12 -6 -26 -6q-27 0 -37 16l-8 14l-86 189l-86 -189q-4 -8 -11 -14q-10 -16 -36 -16q-14 0 -27 6l-86 65q-18 12 -18 37q0 12 8 25q0 2 12 14l149 137l-202 23l-17 4q-16 4 -24 21.5t-4 35.5z" />
<glyph unicode="+" horiz-adv-x="1273" d="M86 590v61q0 20 13.5 33.5t33.5 13.5h424v416q0 23 13.5 35t33.5 12h66q23 0 35 -12t12 -35v-416h424q20 0 33.5 -13t13.5 -34v-61q0 -20 -13.5 -33.5t-33.5 -13.5h-424v-428q0 -20 -13.5 -33.5t-33.5 -13.5h-66q-20 0 -33.5 13t-13.5 34v428h-424q-20 0 -33.5 13 t-13.5 34z" />
<glyph unicode="," horiz-adv-x="507" d="M106 -111l46 289q8 59 57 60h131q14 0 24.5 -11.5t10.5 -23.5q0 -16 -8 -35l-107 -274q-18 -47 -57 -48h-64q-18 0 -27.5 12.5t-5.5 30.5z" />
<glyph unicode="-" horiz-adv-x="991" d="M141 578v73q0 20 13.5 33.5t33.5 13.5h615q20 0 33.5 -13t13.5 -34v-73q0 -20 -13.5 -33t-33.5 -13h-615q-20 0 -33.5 12.5t-13.5 33.5z" />
<glyph unicode="." horiz-adv-x="512" d="M139 47v141q0 20 13.5 35t33.5 15h142q20 0 33.5 -14.5t13.5 -35.5v-141q0 -20 -13.5 -33.5t-33.5 -13.5h-142q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="/" horiz-adv-x="980" d="M53 -131q0 8 6 25l701 1681q18 35 53 35h76q16 0 28.5 -12.5t12.5 -28.5q0 -8 -6 -25l-703 -1681q-4 -12 -17.5 -23.5t-35.5 -11.5h-74q-16 0 -28.5 12.5t-12.5 28.5z" />
<glyph unicode="0" horiz-adv-x="1296" d="M129 715l2 182q6 248 130 402.5t386 154.5q264 0 388 -154.5t130 -402.5q2 -57 2 -182q0 -121 -2 -176q-8 -250 -130 -404.5t-388 -154.5q-264 0 -386 154.5t-130 404.5zM326 719l2 -170q4 -190 84 -294.5t235 -104.5q158 0 238 104t84 295q2 59 2 170q0 113 -2 168 q-4 188 -85 293.5t-237 105.5t-235.5 -105.5t-83.5 -293.5z" />
<glyph unicode="1" horiz-adv-x="870" d="M51 1038q0 23 21 37l444 344q23 14 53 15h97q20 0 33.5 -13.5t13.5 -34.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-101q-20 0 -32.5 13.5t-12.5 33.5v1155l-344 -264q-14 -12 -31 -12q-18 0 -34 20l-50 64q-10 14 -10 28z" />
<glyph unicode="2" horiz-adv-x="1196" d="M100 47v62q0 47 52 90l446 444q152 129 209 211t57 174q0 121 -67.5 189.5t-196.5 68.5q-123 0 -194.5 -71.5t-88.5 -192.5q-4 -23 -20 -35t-33 -12h-98q-18 0 -30.5 12t-12.5 29q4 109 58.5 210t161 165.5t257.5 64.5q229 0 345 -119.5t116 -304.5q0 -129 -62.5 -232.5 t-195.5 -217.5l-404 -410h645q23 0 35.5 -12.5t12.5 -34.5v-78q0 -20 -13.5 -33.5t-34.5 -13.5h-897q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="3" horiz-adv-x="1224" d="M88 342q0 18 12.5 29.5t30.5 11.5h92q23 0 37 -9t23 -36q27 -104 115.5 -147.5t209.5 -43.5q147 0 234.5 71t87.5 202q0 250 -305 250h-193q-20 0 -33.5 13t-13.5 34v53q0 31 23 53l399 443h-606q-20 0 -33.5 12t-13.5 33v73q0 23 13 36.5t34 13.5h811q23 0 36 -13.5 t13 -36.5v-67q0 -23 -21 -47l-399 -449h33q209 -6 330.5 -108.5t121.5 -298.5q0 -135 -66.5 -232.5t-185 -149.5t-268.5 -52q-160 0 -276.5 52t-177 136t-64.5 174z" />
<glyph unicode="4" horiz-adv-x="1265" d="M63 393v76q0 29 23 61l616 869q10 16 31 25.5t45 9.5h140q23 0 35 -12.5t12 -35.5v-868h186q20 0 33.5 -13t13.5 -34v-78q0 -20 -13.5 -33.5t-33.5 -13.5h-186v-299q0 -20 -13.5 -33.5t-33.5 -13.5h-95q-20 0 -33.5 13.5t-13.5 33.5v299h-665q-20 0 -34 13.5t-14 33.5z M276 514h502v713z" />
<glyph unicode="5" horiz-adv-x="1212" d="M94 371v4q0 16 12.5 26.5t28.5 10.5h101q43 0 55 -45q31 -111 113.5 -165.5t191.5 -54.5q139 0 231.5 85t92.5 235q0 137 -92.5 218t-231.5 81q-74 0 -119 -18.5t-92 -53.5q-27 -20 -44 -29.5t-38 -9.5h-98q-18 0 -32.5 13.5t-12.5 31.5l63 682q6 51 51 52h709 q23 0 35 -12.5t12 -35.5v-75q0 -20 -13 -32.5t-34 -12.5h-590l-37 -412q98 70 265 70q137 0 249.5 -54.5t179 -159t66.5 -247.5q0 -152 -66.5 -261.5t-184 -165.5t-269.5 -56q-154 0 -266.5 55t-172 144t-63.5 192z" />
<glyph unicode="6" horiz-adv-x="1228" d="M100 471q0 176 142 377l391 545q2 2 11 14t23.5 19.5t32.5 7.5h101q18 0 29.5 -13.5t11.5 -31.5q0 -10 -11 -27l-299 -418q53 12 119 12q150 0 263.5 -66.5t176 -176t62.5 -240.5t-62.5 -244.5t-181 -181t-280.5 -67.5q-164 0 -284 66.5t-182.5 179t-62.5 245.5zM297 471 q0 -102 45 -175t121 -109.5t164 -36.5q90 0 165.5 36.5t120.5 109.5t45 175q0 100 -46 173t-121.5 111t-163.5 38t-164 -38t-121 -111t-45 -173z" />
<glyph unicode="7" horiz-adv-x="1044" d="M59 1309v75q0 23 12.5 36.5t34.5 13.5h836q23 0 35 -13.5t12 -36.5v-69q0 -37 -16 -80l-506 -1184q-10 -23 -22.5 -37t-34.5 -14h-103q-18 0 -29.5 13.5t-11.5 31.5l4 18l512 1199h-676q-20 0 -33.5 13t-13.5 34z" />
<glyph unicode="8" horiz-adv-x="1298" d="M115 412q0 121 60.5 212t162.5 140q-82 47 -127 124t-45 181q0 178 133 281.5t350 103.5t349 -102.5t132 -282.5q0 -102 -45 -179t-124 -126q102 -49 162.5 -140.5t60.5 -213.5q0 -131 -67.5 -228.5t-189.5 -149.5t-278 -52q-155 0 -277 52t-189.5 150.5t-67.5 229.5z M307 410q0 -123 97.5 -197t244.5 -74q146 0 243 74t97 197t-97 196.5t-243 73.5q-147 0 -244.5 -74t-97.5 -196zM352 1065q0 -104 84 -166.5t213 -62.5q125 4 211 65.5t86 163.5q0 104 -85 166.5t-212 62.5q-129 0 -213 -62.5t-84 -166.5z" />
<glyph unicode="9" horiz-adv-x="1210" d="M80 971q0 123 58.5 233.5t174 180t279.5 69.5t280.5 -67.5t176 -177t59.5 -234.5q0 -113 -40 -199t-105 -178l-398 -557q-14 -18 -27.5 -29.5t-37.5 -11.5h-103q-18 0 -29.5 13.5t-11.5 31.5q0 14 9 27l315 438q-49 -12 -115 -12q-147 2 -257.5 68.5t-169 174 t-58.5 230.5zM274 971q0 -92 43 -163t115 -110t160 -39t161.5 39t116.5 110t43 163q0 94 -43 165.5t-116.5 110.5t-161.5 39q-86 0 -159 -39t-116 -110.5t-43 -165.5z" />
<glyph unicode=":" horiz-adv-x="522" d="M143 47v137q0 23 12.5 36t32.5 13h142q23 0 36 -13t13 -36v-137q0 -20 -14.5 -33.5t-34.5 -13.5h-142q-20 0 -32.5 13.5t-12.5 33.5zM143 764v137q0 23 12.5 36t32.5 13h142q23 0 36 -13t13 -36v-137q0 -20 -14.5 -33.5t-34.5 -13.5h-142q-20 0 -32.5 13t-12.5 34z" />
<glyph unicode=";" horiz-adv-x="552" d="M133 -111l45 289q8 59 58 60h131q14 0 23 -11.5t9 -23.5t-6 -35l-106 -274q-10 -25 -22.5 -36.5t-37.5 -11.5h-63q-17 0 -26 12.5t-5 30.5zM168 764v137q0 23 13.5 36t33.5 13h141q20 0 33.5 -14t13.5 -35v-137q0 -20 -13 -33.5t-34 -13.5h-141q-20 0 -33.5 13t-13.5 34z " />
<glyph unicode="&#x3c;" horiz-adv-x="1005" d="M76 702v35q0 29 13 50.5t46 43.5l639 441q4 2 20.5 11t28.5 9q18 0 30.5 -11t12.5 -30v-84q0 -23 -11 -37t-36 -30l-553 -379l553 -379q27 -18 37 -32.5t10 -35.5v-84q0 -20 -12 -31.5t-31 -11.5q-14 0 -49 23l-639 440q-33 20 -46 42t-13 50z" />
<glyph unicode="=" horiz-adv-x="1167" d="M147 328v63q0 20 13.5 32.5t34.5 12.5h778q20 0 33.5 -12t13.5 -33v-63q0 -20 -13.5 -33.5t-33.5 -13.5h-778q-21 0 -34.5 13t-13.5 34zM147 840v63q0 20 13.5 32.5t34.5 12.5h778q20 0 33.5 -12t13.5 -33v-63q0 -20 -13.5 -33.5t-33.5 -13.5h-778q-21 0 -34.5 13 t-13.5 34z" />
<glyph unicode="&#x3e;" horiz-adv-x="1003" d="M139 190v84q0 20 10.5 35t36.5 33l553 379l-553 379q-25 16 -36 30.5t-11 36.5v84q0 18 11.5 29.5t29.5 11.5q12 0 29.5 -9t21.5 -11l639 -441q33 -23 46.5 -44t13.5 -50v-35q0 -29 -13.5 -50t-46.5 -42l-639 -440q-33 -23 -51 -23q-16 0 -28.5 12.5t-12.5 30.5z" />
<glyph unicode="?" horiz-adv-x="1081" d="M68 1038q4 113 64.5 208t170 151.5t250.5 56.5q150 0 252 -51t151.5 -134t49.5 -175q0 -104 -47.5 -180t-141.5 -177q-78 -84 -116.5 -142t-45.5 -130q-4 -57 -4 -86q-10 -49 -51 -49h-100q-21 0 -33 12t-12 33v84q4 109 56 188.5t148 177.5q74 76 110 125t42 107 q2 8 2 26q0 94 -80 149.5t-186 55.5q-115 0 -191.5 -60.5t-99.5 -189.5q-8 -45 -53 -45h-90q-18 0 -31.5 12.5t-13.5 32.5zM434 47v119q0 20 13.5 34.5t33.5 14.5h127q20 0 34.5 -14.5t14.5 -34.5v-119q0 -20 -14 -33.5t-35 -13.5h-127q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="@" horiz-adv-x="1701" d="M117 561q0 121 6 172q33 291 215 460t496 169q360 0 541 -163t198 -466q4 -94 4 -143q0 -84 -4 -125q-8 -147 -81 -225t-196 -78q-88 0 -143 38t-82 79q-35 -57 -93 -97.5t-165 -40.5q-156 0 -245 109.5t-89 308.5q0 197 90 308.5t242 111.5q76 0 124 -26.5t85 -71.5v34 q0 20 13.5 34t33.5 14h53q20 0 33.5 -13.5t13.5 -34.5v-438q0 -84 36 -129t93 -45q74 0 101.5 54.5t29.5 121.5q4 49 5 127q0 35 -5 117q-18 250 -156 374t-437 124q-250 0 -392.5 -130t-171.5 -368q-6 -51 -6 -162q0 -109 6 -168q27 -238 171.5 -365.5t398.5 -127.5 q135 0 222 22.5t124 44t96 66.5q4 4 18.5 15t34.5 11h78q18 0 31.5 -12t13.5 -33q0 -14 -10 -30q-96 -102 -240.5 -164t-367.5 -62q-322 0 -504 163t-213 462q-6 59 -6 178zM625 559q0 -123 49 -199.5t149 -76.5q107 0 152 71.5t45 204.5q0 135 -46 208t-151 73 q-199 0 -198 -281z" />
<glyph unicode="A" horiz-adv-x="1368" d="M41 41l4 22l508 1326q16 45 65 45h132q49 0 65 -45l508 -1326l4 -22q0 -16 -13.5 -28.5t-29.5 -12.5h-96q-20 0 -33.5 11.5t-17.5 23.5l-115 295h-678l-113 -295q-16 -35 -51 -35h-98q-16 0 -28.5 12.5t-12.5 28.5zM403 500h562l-281 735z" />
<glyph unicode="B" horiz-adv-x="1366" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h559q217 0 328 -104.5t111 -272.5q0 -115 -53.5 -192t-125.5 -111q88 -43 147.5 -133.5t59.5 -206.5q0 -115 -52 -209t-154.5 -149.5t-243.5 -55.5h-576q-20 0 -32.5 13.5t-12.5 33.5zM373 162h401q131 0 206 70.5t75 181.5 q0 113 -75 182.5t-206 69.5h-401v-504zM373 825h381q133 0 202.5 60.5t69.5 169.5q0 106 -68.5 161.5t-203.5 55.5h-381v-447z" />
<glyph unicode="C" horiz-adv-x="1361" d="M123 715q0 127 2 184q10 258 152.5 406.5t414.5 148.5q182 0 309 -64.5t191.5 -169t71.5 -223.5q0 -18 -13.5 -29.5t-31.5 -11.5h-103q-20 0 -32.5 10.5t-18.5 34.5q-35 158 -126 221.5t-247 63.5q-356 0 -370 -397q-2 -55 -3 -170q0 -115 3 -174q14 -395 370 -395 q154 0 246 63t127 219q6 25 18.5 35t32.5 10h103q18 0 31.5 -11t13.5 -30q-6 -119 -71 -223t-192 -168.5t-309 -64.5q-272 0 -414.5 148t-152.5 407q-2 55 -2 180z" />
<glyph unicode="D" horiz-adv-x="1409" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h463q309 0 446.5 -138.5t141.5 -416.5q2 -59 2 -160q0 -100 -2 -162q-4 -195 -62.5 -316.5t-183.5 -181t-332 -59.5h-473q-20 0 -32.5 13.5t-12.5 33.5zM373 170h315q143 0 228 40t123 125t42 230q4 123 4 154q0 33 -4 151 q-4 205 -97 300.5t-306 95.5h-305v-1096z" />
<glyph unicode="E" horiz-adv-x="1239" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h842q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-698v-457h653q23 0 36 -13.5t13 -35.5v-70q0 -20 -13.5 -33.5t-35.5 -13.5h-653v-473h714q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-858 q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="F" horiz-adv-x="1193" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h832q23 0 36 -13.5t13 -36.5v-75q0 -20 -13.5 -33.5t-35.5 -13.5h-686v-486h645q23 0 36 -13t13 -34v-78q0 -20 -14.5 -33.5t-34.5 -13.5h-645v-557q0 -20 -14.5 -33.5t-34.5 -13.5h-97q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="G" horiz-adv-x="1390" d="M125 719q0 133 2 192q8 250 156.5 396.5t414.5 146.5q180 0 305 -65.5t188.5 -156.5t68.5 -169v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-115q-18 0 -27.5 8t-17.5 31q-33 96 -121 161.5t-238 65.5q-172 0 -270 -94t-104 -291q-2 -59 -2 -182q0 -121 2 -182q6 -199 104 -293 t270 -94q174 0 278.5 95t104.5 285v88h-313q-20 0 -33.5 14.5t-13.5 35.5v61q0 20 13.5 34.5t33.5 14.5h459q23 0 36 -13t13 -36v-194q0 -166 -68.5 -292t-199.5 -194.5t-310 -68.5q-266 0 -413.5 147t-157.5 399q-2 61 -2 193z" />
<glyph unicode="H" horiz-adv-x="1458" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h99q23 0 36 -13.5t13 -36.5v-569h712v569q0 23 13.5 36.5t34.5 13.5h98q23 0 36 -13.5t13 -36.5v-1337q0 -20 -14.5 -33.5t-34.5 -13.5h-98q-20 0 -34 13.5t-14 33.5v590h-712v-590q0 -20 -14.5 -33.5t-34.5 -13.5h-99 q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="I" horiz-adv-x="555" d="M180 47v1339q0 20 12.5 34t32.5 14h103q23 0 35 -12.5t12 -35.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="J" horiz-adv-x="1294" d="M80 412q0 18 12 31.5t31 13.5h104q45 0 52 -47q12 -133 102 -196.5t225 -63.5q156 0 241 102t85 276v734h-731q-20 0 -33.5 13t-13.5 34v77q0 20 13 34t34 14h878q23 0 36 -13.5t13 -36.5v-860q0 -164 -62 -286.5t-181 -190t-281 -67.5q-223 0 -369.5 109.5t-154.5 322.5 z" />
<glyph unicode="K" horiz-adv-x="1224" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h97q23 0 36 -13.5t13 -36.5v-510l555 527q33 33 80 33h102q16 0 28.5 -12.5t12.5 -28.5t-10 -27l-639 -618l682 -678q10 -12 10 -29q0 -16 -12.5 -28.5t-28.5 -12.5h-107q-29 0 -45 9t-34 24l-594 579v-565q0 -20 -14.5 -33.5 t-34.5 -13.5h-97q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="L" horiz-adv-x="1148" d="M180 47v1339q0 20 12.5 34t32.5 14h103q20 0 32.5 -13.5t12.5 -34.5v-1214h684q23 0 36 -13.5t13 -33.5v-78q0 -20 -14.5 -33.5t-34.5 -13.5h-832q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="M" horiz-adv-x="1624" d="M180 47v1337q0 23 12.5 36.5t34.5 13.5h97q37 0 51 -31l436 -838l440 838q16 31 49 31h97q23 0 35 -13.5t12 -36.5v-1337q0 -20 -13.5 -33.5t-33.5 -13.5h-94q-20 0 -33 13.5t-13 33.5v1018l-350 -678q-20 -43 -65 -43h-62q-43 0 -65 43l-348 678v-1018 q0 -20 -13.5 -33.5t-34.5 -13.5h-92q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="N" horiz-adv-x="1419" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h90q20 0 30.5 -8.5t21.5 -20.5l686 -1057v1036q0 23 13 36.5t34 13.5h92q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-33.5 -14.5h-92q-35 0 -49 29l-686 1046v-1028q0 -20 -13.5 -33.5t-34.5 -13.5h-94q-20 0 -32.5 13.5 t-12.5 33.5z" />
<glyph unicode="O" horiz-adv-x="1382" d="M125 717q0 113 2 172q8 279 158.5 422t406.5 143t405.5 -143.5t157.5 -421.5q4 -119 5 -172q0 -51 -5 -174q-8 -283 -152.5 -423t-410.5 -140t-411.5 140t-153.5 423q-2 61 -2 174zM322 717q0 -102 2 -164q6 -205 104 -304t264 -99q164 0 262.5 99t106.5 304q4 123 4 164 q0 45 -4 164q-8 205 -106.5 305t-262.5 100t-263 -100.5t-105 -304.5q-2 -59 -2 -164z" />
<glyph unicode="P" horiz-adv-x="1312" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h518q231 0 363.5 -112t132.5 -325t-132 -323.5t-364 -110.5h-368v-516q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5t-12.5 33.5zM375 729h358q309 0 309 268q0 131 -76.5 201t-232.5 70h-358v-539z" />
<glyph unicode="Q" horiz-adv-x="1382" d="M125 717q0 113 2 172q8 279 158.5 422t406.5 143t405.5 -143.5t157.5 -421.5q4 -119 5 -172q0 -51 -5 -174q-8 -283 -149 -420l137 -193q6 -12 6 -22q0 -16 -12 -28.5t-29 -12.5h-104q-23 0 -39 13.5t-31 33.5l-84 117q-109 -51 -258 -51q-266 0 -411.5 140t-153.5 423 q-2 61 -2 174zM322 717q0 -102 2 -164q6 -205 104 -304t264 -99q164 0 262.5 99t104.5 304q4 123 4 164q0 45 -4 164q-6 205 -104.5 305t-262.5 100t-263 -100.5t-105 -304.5q-2 -59 -2 -164z" />
<glyph unicode="R" horiz-adv-x="1339" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h500q233 0 365.5 -110t132.5 -318q0 -156 -78 -256.5t-217 -139.5l315 -547q6 -12 6 -22q0 -16 -12 -28.5t-29 -12.5h-90q-31 0 -48 14.5t-32 40.5l-301 525h-364v-533q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5 t-12.5 33.5zM373 748h344q309 0 309 260t-309 260h-344v-520z" />
<glyph unicode="S" horiz-adv-x="1269" d="M88 360q0 16 12.5 28.5t30.5 12.5h100q37 0 52 -41q18 -86 105 -148t249 -62q174 0 260 61t86 170q0 70 -42 115t-125 78.5t-249 82.5q-158 45 -251 96.5t-140 126t-47 189.5q0 111 59.5 198t172 137t266.5 50q160 0 273.5 -56.5t173 -141.5t63.5 -171q0 -18 -12.5 -30.5 t-30.5 -12.5h-103q-16 0 -30.5 10.5t-20.5 30.5q-12 88 -98 145.5t-215 57.5q-139 0 -221 -54t-82 -161q0 -72 37.5 -117t116.5 -79.5t231 -79.5q174 -49 273 -98.5t148.5 -122t49.5 -187.5q0 -193 -146.5 -300t-396.5 -107q-168 0 -292 53t-188.5 140t-68.5 187z" />
<glyph unicode="T" horiz-adv-x="1181" d="M51 1305v79q0 23 13.5 36.5t33.5 13.5h983q23 0 35 -13.5t12 -36.5v-79q0 -20 -13 -34t-34 -14h-393v-1210q0 -20 -14.5 -33.5t-34.5 -13.5h-98q-21 0 -34 13.5t-13 33.5v1210h-396q-20 0 -33.5 13.5t-13.5 34.5z" />
<glyph unicode="U" horiz-adv-x="1433" d="M168 553v831q0 23 12.5 36.5t32.5 13.5h98q23 0 36 -13.5t13 -36.5v-835q0 -201 93.5 -299t265.5 -98q170 0 263 99t93 298v835q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-831q0 -289 -142.5 -431t-406.5 -142t-407.5 142t-143.5 431z" />
<glyph unicode="V" horiz-adv-x="1337" d="M68 1393q0 16 13 28.5t30 12.5h98q23 0 36 -11.5t17 -25.5l406 -1172l407 1172q4 14 17.5 25.5t35.5 11.5h99q16 0 28.5 -12.5t12.5 -28.5q0 -14 -2 -23l-461 -1317q-20 -53 -74 -53h-125q-54 0 -74 53l-460 1317z" />
<glyph unicode="W" horiz-adv-x="1636" d="M90 1393q0 16 12.5 28.5t28.5 12.5h100q43 0 50 -35l211 -1069l227 737q12 49 63 49h72q49 0 66 -49l225 -737l211 1069q6 35 51 35h98q16 0 28.5 -12.5t12.5 -28.5q0 -12 -2 -23l-266 -1317q-10 -53 -64 -53h-73q-51 0 -68 51l-254 789l-256 -789q-14 -51 -65 -51h-76 q-52 0 -64 53l-264 1317z" />
<glyph unicode="X" horiz-adv-x="1292" d="M51 41q0 14 8 27l469 661l-446 637q-8 12 -8 27q0 16 12 28.5t29 12.5h116q29 0 54 -35l366 -518l363 518q20 35 53 35h109q16 0 28.5 -12.5t12.5 -28.5q0 -14 -9 -27l-442 -639l469 -659q8 -12 8 -27q0 -16 -13 -28.5t-30 -12.5h-117q-31 0 -53 33l-387 540l-385 -540 q-23 -33 -53 -33h-113q-16 0 -28.5 12.5t-12.5 28.5z" />
<glyph unicode="Y" horiz-adv-x="1320" d="M63 1393q0 16 12.5 28.5t28.5 12.5h99q37 0 53 -35l406 -670l403 670q20 35 53 35h99q16 0 29.5 -12.5t13.5 -28.5q0 -10 -9 -27l-493 -834v-485q0 -20 -13.5 -33.5t-35.5 -13.5h-99q-20 0 -33.5 13.5t-13.5 33.5v485l-493 834q-6 12 -7 27z" />
<glyph unicode="Z" horiz-adv-x="1224" d="M84 49v82q0 35 14.5 55.5t16.5 22.5l768 1053h-733q-20 0 -34 13t-14 34v75q0 23 13.5 36.5t34.5 13.5h921q23 0 36 -13.5t13 -36.5v-81q0 -37 -26 -72l-760 -1059h760q23 0 36 -13.5t13 -33.5v-78q0 -20 -14.5 -33.5t-34.5 -13.5h-963q-20 0 -33.5 13.5t-13.5 35.5z" />
<glyph unicode="[" horiz-adv-x="661" d="M152 -279v1821q0 20 12 33.5t33 13.5h362q20 0 33.5 -13t13.5 -34v-67q0 -20 -13 -34t-34 -14h-231v-1585h231q20 0 33.5 -13t13.5 -34v-74q0 -20 -13 -33.5t-34 -13.5h-362q-21 0 -33 13.5t-12 33.5z" />
<glyph unicode="\" horiz-adv-x="980" d="M51 1569q0 16 12.5 28.5t28.5 12.5h76q37 0 53 -35l701 -1681q6 -16 6 -25q0 -16 -12.5 -28.5t-28.5 -12.5h-74q-22 0 -35.5 11t-17.5 24l-703 1681q-6 16 -6 25z" />
<glyph unicode="]" horiz-adv-x="661" d="M55 -205q0 20 13.5 33.5t33.5 13.5h232v1585h-232q-20 0 -33.5 13.5t-13.5 34.5v67q0 20 13.5 33.5t33.5 13.5h363q20 0 32.5 -13t12.5 -34v-1821q0 -20 -12.5 -33.5t-32.5 -13.5h-363q-20 0 -33.5 13.5t-13.5 33.5v74z" />
<glyph unicode="^" horiz-adv-x="880" d="M135 1208q0 16 23 39l186 187q20 20 33.5 25t34.5 5h57q20 0 33.5 -5t34.5 -25l186 -187q23 -20 22 -39q0 -25 -28 -24h-51q-33 0 -58 14l-168 117l-168 -117q-23 -14 -55 -14h-51q-31 -1 -31 24z" />
<glyph unicode="_" horiz-adv-x="1531" d="M125 -35v74q0 20 13.5 32.5t33.5 12.5h1188q20 0 33.5 -12.5t13.5 -32.5v-74q0 -20 -13.5 -33.5t-33.5 -13.5h-1188q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="`" horiz-adv-x="684" d="M137 1460q0 39 39 39h133q29 0 43.5 -7t32.5 -30l154 -184q10 -10 10 -27q0 -33 -33 -32h-65q-23 0 -36.5 4t-29.5 18l-238 195q-10 10 -10 24z" />
<glyph unicode="a" d="M78 289q0 135 110.5 221t300.5 115l308 43v59q0 211 -244 211q-94 0 -151.5 -38t-84.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 44 109.5t136 108.5t225 46q229 0 328.5 -106.5t99.5 -265.5v-666q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -32.5 13.5t-12.5 33.5v90q-45 -68 -131 -112.5t-221 -44.5q-101 0 -186 40.5t-135 111.5t-50 157zM258 303q0 -84 68.5 -129t162.5 -45q136 0 222 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5z" />
<glyph unicode="b" horiz-adv-x="1216" d="M156 47v1360q0 23 13 35t34 12h92q20 0 33.5 -13.5t13.5 -33.5v-473q114 151 329 151h3q215 0 322.5 -140t113.5 -351q2 -23 2 -62t-2 -61q-6 -211 -113.5 -351t-322.5 -140h-3q-219 0 -335 155v-88q0 -20 -13.5 -33.5t-33.5 -13.5h-86q-20 0 -33.5 13.5t-13.5 33.5z M340 539q0 -55 2 -78q4 -131 77 -226.5t216 -95.5q147 0 215 92.5t74 241.5q2 20 2 59q0 393 -291 394q-139 0 -214 -91.5t-79 -216.5q-2 -23 -2 -79z" />
<glyph unicode="c" d="M106 532l3 82q6 223 128.5 347t342.5 124q147 0 248.5 -52t151.5 -130t54 -155q2 -18 -13 -33t-34 -15h-90q-20 0 -30.5 9.5t-20.5 33.5q-37 100 -101.5 142.5t-162.5 42.5q-129 0 -205 -80t-82 -244l-2 -72l2 -71q6 -164 82 -244t205 -80q100 0 163.5 42t100.5 143 q10 25 20.5 34t30.5 9h90q18 0 33.5 -14.5t13.5 -33.5q-4 -76 -54 -153.5t-151.5 -130.5t-248.5 -53q-220 0 -342.5 123.5t-128.5 347.5z" />
<glyph unicode="d" horiz-adv-x="1216" d="M102 532l2 62q8 211 117 351t322 140t331 -151v473q0 20 13.5 33.5t34.5 13.5h92q20 0 33.5 -13.5t13.5 -33.5v-1360q0 -20 -13.5 -33.5t-33.5 -13.5h-88q-20 0 -32.5 13.5t-12.5 33.5v88q-116 -155 -335 -155h-3q-215 0 -322.5 139t-116.5 352zM291 532q0 -393 291 -393 q143 0 214.5 95.5t77.5 226.5q2 23 3 78q0 57 -3 79q-4 125 -78.5 216.5t-213.5 91.5q-147 0 -215 -92.5t-74 -241.5z" />
<glyph unicode="e" horiz-adv-x="1144" d="M102 535l2 67q12 221 137.5 352t333.5 131q225 0 349.5 -143t124.5 -389v-37q0 -20 -13.5 -33.5t-34.5 -13.5h-710v-18q6 -133 82 -227.5t200 -94.5q96 0 157 38t91 79q18 25 28.5 30t35.5 5h90q18 0 31.5 -10.5t13.5 -26.5q0 -45 -55.5 -108.5t-156.5 -109.5t-233 -46 q-208 0 -333 130t-138 355zM291 608h571v6q0 143 -77.5 233.5t-209.5 90.5q-133 0 -208.5 -90t-75.5 -234v-6z" />
<glyph unicode="f" horiz-adv-x="782" d="M37 952v66q0 20 13 33.5t34 13.5h176v102q0 166 81 257.5t265 91.5h121q20 0 33.5 -13.5t13.5 -34.5v-65q0 -20 -13 -32.5t-34 -12.5h-117q-94 0 -131 -49t-37 -152v-92h265q20 0 33.5 -13.5t13.5 -33.5v-66q0 -20 -13.5 -32.5t-33.5 -12.5h-265v-860q0 -20 -13 -33.5 t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v860h-176q-20 0 -33.5 12.5t-13.5 32.5z" />
<glyph unicode="g" horiz-adv-x="1218" d="M104 539l2 55q6 211 114 351t325 140q215 0 336 -157v88q0 20 13 34.5t34 14.5h88q20 0 33.5 -14.5t13.5 -34.5v-993q0 -473 -477 -474q-170 0 -272.5 57.5t-144.5 131.5t-42 127q0 18 14.5 31.5t32.5 13.5h90q18 0 30.5 -9.5t20.5 -33.5q57 -164 260 -164 q154 0 228 65.5t74 231.5v141q-118 -151 -329 -151h-3q-217 0 -324.5 139t-114.5 352zM293 539l2 -56q6 -147 72.5 -240t214.5 -93q143 0 215.5 93t79.5 224q2 18 2 72q0 51 -2 69q-6 131 -79 224.5t-216 93.5q-147 0 -214 -93.5t-73 -240.5z" />
<glyph unicode="h" horiz-adv-x="1243" d="M156 47v1360q0 23 13 35t34 12h94q23 0 35 -12t12 -35v-477q59 76 140 115.5t200 39.5q197 0 306.5 -125.5t109.5 -332.5v-580q0 -20 -13.5 -33.5t-33.5 -13.5h-97q-20 0 -33.5 13.5t-13.5 33.5v567q0 147 -71.5 229.5t-206.5 82.5q-131 0 -209 -83t-78 -229v-567 q0 -20 -13.5 -33.5t-33.5 -13.5h-94q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="i" horiz-adv-x="491" d="M137 1313v106q0 20 13.5 34.5t33.5 14.5h121q20 0 34.5 -14t14.5 -35v-106q0 -20 -14 -33.5t-35 -13.5h-121q-20 0 -33.5 13t-13.5 34zM154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5 z" />
<glyph unicode="j" horiz-adv-x="516" d="M-68 -276q0 20 12.5 32.5t32.5 12.5h29q100 0 134 52t34 152v1045q0 23 13.5 35t33.5 12h92q20 0 33.5 -13.5t13.5 -33.5v-1047q0 -360 -344 -360h-39q-20 0 -32.5 13.5t-12.5 33.5v66zM156 1313v106q0 20 13 34.5t34 14.5h127q20 0 33.5 -14t13.5 -35v-106 q0 -20 -13.5 -33.5t-33.5 -13.5h-127q-21 0 -34 13t-13 34z" />
<glyph unicode="k" horiz-adv-x="1050" d="M156 47v1360q0 23 13 35t34 12h88q20 0 33.5 -13.5t13.5 -33.5v-715l410 338q31 23 43 29t38 6h103q18 0 29.5 -11.5t11.5 -31.5q0 -18 -23 -39l-479 -393l530 -508q23 -23 23 -39q0 -18 -11.5 -30.5t-29.5 -12.5h-100q-31 0 -44.5 6t-37.5 29l-463 438v-426 q0 -20 -13.5 -33.5t-33.5 -13.5h-88q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="l" horiz-adv-x="493" d="M156 47v1360q0 23 13 35t34 12h90q20 0 33.5 -13.5t13.5 -33.5v-1360q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="m" horiz-adv-x="1818" d="M154 47v971q0 20 13 33.5t34 13.5h86q20 0 32.5 -13.5t12.5 -33.5v-70q111 137 299 137q225 0 319 -188q49 86 140.5 137t203.5 51q168 0 274.5 -114.5t106.5 -331.5v-592q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v574q0 164 -67.5 234.5t-178.5 70.5 q-98 0 -168.5 -73t-70.5 -232v-574q0 -20 -13.5 -33.5t-33.5 -13.5h-91q-20 0 -33.5 13.5t-13.5 33.5v574q0 162 -70.5 233.5t-174.5 71.5q-98 0 -169 -72t-71 -231v-576q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="n" horiz-adv-x="1236" d="M154 47v971q0 20 13 33.5t34 13.5h88q23 0 35 -12.5t12 -34.5v-90q61 76 142 116.5t202 40.5q197 0 305.5 -125.5t108.5 -332.5v-580q0 -20 -13.5 -33.5t-33.5 -13.5h-95q-20 0 -33.5 13.5t-13.5 33.5v567q0 147 -71.5 229.5t-204.5 82.5q-131 0 -209 -83t-78 -229v-567 q0 -20 -13.5 -33.5t-33.5 -13.5h-94q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="o" horiz-adv-x="1171" d="M106 532l3 89q8 211 132 337.5t345 126.5t345 -127t132 -337q4 -45 4 -89q0 -43 -4 -88q-8 -211 -130 -337.5t-347 -126.5q-226 0 -347.5 126.5t-129.5 337.5zM293 532l2 -77q6 -156 83 -239t208 -83t207.5 83t83.5 239q2 20 2 77t-2 78q-6 156 -83 239t-208 83t-208 -83 t-83 -239z" />
<glyph unicode="p" horiz-adv-x="1216" d="M156 -342v1360q0 23 13 35t34 12h86q23 0 35 -12.5t12 -34.5v-90q118 157 335 157h3q215 0 322.5 -136t113.5 -347q2 -23 2 -70t-2 -69q-6 -209 -113.5 -346t-322.5 -137q-219 0 -332 155v-477q0 -20 -13.5 -33.5t-33.5 -13.5h-92q-21 0 -34 12t-13 35zM340 526 q0 -57 2 -80q4 -125 79 -216t214 -91q147 0 215 92.5t74 241.5q2 20 2 59q0 393 -291 394q-143 0 -216 -95.5t-77 -224.5q-2 -23 -2 -80z" />
<glyph unicode="q" horiz-adv-x="1216" d="M104 532l2 70q8 211 116 347t323 136q221 0 336 -157v90q0 20 13 33.5t34 13.5h88q20 0 33.5 -13.5t13.5 -33.5v-1360q0 -20 -13.5 -33.5t-33.5 -13.5h-92q-21 0 -34 12t-13 35v477q-116 -155 -329 -155h-3q-215 0 -323.5 137t-115.5 346zM293 532q0 -393 291 -393 q139 0 213.5 91.5t79.5 215.5q2 23 2 80t-2 80q-6 131 -78 225.5t-215 94.5q-147 0 -215 -92.5t-74 -241.5z" />
<glyph unicode="r" horiz-adv-x="770" d="M154 47v969q0 20 13 34.5t34 14.5h86q23 0 36 -13.5t13 -35.5v-90q84 139 285 139h79q23 0 35.5 -12.5t12.5 -34.5v-76q0 -20 -12.5 -33.5t-35.5 -13.5h-116q-113 0 -177.5 -65.5t-64.5 -178.5v-604q0 -20 -14.5 -33.5t-34.5 -13.5h-92q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="s" horiz-adv-x="1040" d="M88 229q0 20 14.5 31.5t30.5 11.5h86q12 0 19.5 -4t19.5 -18q47 -57 105.5 -90t156.5 -33q111 0 177.5 41t66.5 119q0 51 -28.5 81.5t-95.5 55.5t-198 53q-178 39 -251.5 113t-73.5 190q0 76 45 146.5t134 114.5t212 44q129 0 220 -43t137 -100t46 -96q0 -18 -13 -30.5 t-32 -12.5h-78q-29 0 -40 22q-35 39 -58.5 60.5t-69 37t-112.5 15.5q-100 0 -153.5 -42t-53.5 -112q0 -43 22.5 -72.5t86 -55t188.5 -52.5q195 -41 275.5 -116.5t80.5 -194.5q0 -88 -50 -160t-149.5 -112.5t-236.5 -40.5q-141 0 -238.5 46t-144.5 105t-47 98z" />
<glyph unicode="t" horiz-adv-x="794" d="M39 952v66q0 20 12 33.5t33 13.5h162v342q0 20 12 33.5t33 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-342h254q20 0 33.5 -13.5t13.5 -33.5v-66q0 -20 -13.5 -32.5t-33.5 -12.5h-254v-534q0 -104 34 -157.5t116 -53.5h124q20 0 33 -13.5t13 -33.5v-68q0 -20 -12.5 -33.5 t-33.5 -13.5h-139q-317 0 -317 358v549h-162q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="u" horiz-adv-x="1228" d="M143 440v578q0 20 13.5 33.5t33.5 13.5h95q20 0 33.5 -13.5t13.5 -33.5v-567q0 -311 270 -312q131 0 208 83t77 229v567q0 20 13 33.5t34 13.5h94q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v92q-59 -80 -139 -119.5 t-203 -39.5q-195 0 -301.5 125.5t-106.5 334.5z" />
<glyph unicode="v" horiz-adv-x="1130" d="M66 1022q0 18 12 30.5t28 12.5h95q33 0 49 -33l315 -817l316 817q4 12 17 22.5t32 10.5h94q16 0 28.5 -12.5t12.5 -30.5l-4 -21l-379 -950q-23 -51 -76 -51h-82q-52 0 -75 51l-381 950q-2 6 -2 21z" />
<glyph unicode="w" horiz-adv-x="1638" d="M76 1022q0 18 12 30.5t31 12.5h84q20 0 33.5 -11.5t15.5 -21.5l231 -782l246 772q4 16 18.5 29.5t38.5 13.5h66q25 0 39 -13.5t18 -29.5l246 -772l231 782q2 10 15.5 21.5t34.5 11.5h84q18 0 30.5 -12.5t12.5 -30.5l-4 -21l-291 -950q-8 -27 -22.5 -39t-43.5 -12h-74 q-53 0 -69 51l-240 742l-239 -742q-16 -51 -70 -51h-74q-28 0 -42.5 12.5t-22.5 38.5l-291 950z" />
<glyph unicode="x" horiz-adv-x="1099" d="M61 43q0 18 15 39l356 463l-334 440q-16 23 -16 37q0 18 13.5 30.5t29.5 12.5h98q31 0 53 -33l277 -358l276 358q12 16 23.5 24.5t30.5 8.5h94q16 0 28.5 -12.5t12.5 -28.5q0 -20 -14 -39l-338 -444l356 -459q14 -20 14 -39q0 -18 -12 -30.5t-31 -12.5h-100q-31 0 -53 31 l-293 379l-293 -379q-12 -14 -23.5 -22.5t-29.5 -8.5h-97q-18 0 -30.5 12.5t-12.5 30.5z" />
<glyph unicode="y" horiz-adv-x="1124" d="M63 1022q2 18 14.5 30.5t28.5 12.5h95q33 0 49 -33l315 -753l324 753q16 33 49 33h92q16 0 28.5 -12.5t12.5 -28.5q0 -14 -10 -37l-580 -1343q-10 -16 -21 -24.5t-30 -8.5h-90q-18 0 -30.5 12t-12.5 29q0 6 10 37l160 374l-391 924q-12 25 -13 35z" />
<glyph unicode="z" horiz-adv-x="1038" d="M84 47v68q0 16 9 33.5t20 31.5l571 727h-534q-20 0 -33 13.5t-13 33.5v64q0 20 12.5 33.5t33.5 13.5h724q20 0 34 -13.5t14 -33.5v-72q0 -23 -29 -63l-563 -725h585q23 0 35.5 -12.5t12.5 -34.5v-64q0 -20 -13.5 -33.5t-34.5 -13.5h-784q-20 0 -33.5 13.5t-13.5 33.5z " />
<glyph unicode="{" horiz-adv-x="749" d="M61 614v37q0 27 11.5 44.5t40.5 33.5l16 10q59 39 85 96.5t26 161.5q0 203 33.5 331t120.5 194.5t245 66.5h10q20 0 32.5 -12t12.5 -33v-69q0 -20 -12 -32.5t-31 -12.5h-10q-98 -2 -146.5 -50.5t-63.5 -143.5t-15 -278q0 -240 -152 -325q152 -86 152 -326 q0 -182 15 -278.5t63.5 -143.5t146.5 -49h10q18 -2 30.5 -14t12.5 -31v-72q0 -20 -12 -32.5t-33 -12.5h-10q-156 0 -243 68t-121.5 196t-34.5 330q0 104 -27 162.5t-86 97.5l-14 9q-29 16 -40.5 33.5t-11.5 43.5z" />
<glyph unicode="|" horiz-adv-x="473" d="M154 -379v2167q0 20 12 33.5t33 13.5h75q20 0 34 -13.5t14 -33.5v-2167q0 -20 -13.5 -33.5t-34.5 -13.5h-75q-21 0 -33 13.5t-12 33.5z" />
<glyph unicode="}" horiz-adv-x="749" d="M53 -209q0 18 13.5 30.5t31.5 14.5h11q98 2 146 49t63.5 143.5t15.5 278.5q0 238 151 324q-152 86 -151 325q0 180 -15.5 277.5t-63.5 144.5t-146 49l-11 3q-18 0 -31.5 12t-13.5 33v69q0 20 13.5 32.5t31.5 12.5h11q155 0 243 -67.5t122 -195.5t34 -331 q0 -104 27.5 -162.5t87.5 -97.5l14 -8q29 -16 40 -33.5t11 -42.5v-39q0 -25 -11 -42t-40 -35l-16 -9q-59 -41 -86 -98t-27 -162q0 -203 -34 -329.5t-121 -194.5t-242 -68h-13q-18 0 -31.5 12.5t-13.5 32.5v72z" />
<glyph unicode="~" horiz-adv-x="1148" d="M150 537v75q0 29 31.5 57.5t83.5 47t110 18.5q59 0 105 -10t112 -29q59 -18 98 -27.5t88 -9.5q43 0 75 13.5t52.5 29t30.5 21.5q20 8 29 8q37 0 36 -41v-76q0 -29 -31.5 -56.5t-83.5 -45.5t-110 -18q-57 0 -102 9t-107 27q-55 18 -99 27.5t-95 9.5q-45 0 -76 -13 t-52.5 -27.5t-29.5 -20.5q-16 -10 -31 -10q-16 0 -25 11t-9 30z" />
<glyph unicode="&#xa1;" horiz-adv-x="481" d="M131 891v125q0 20 14.5 33.5t34.5 13.5h123q21 0 34 -13.5t13 -33.5v-125q0 -20 -13 -33.5t-34 -13.5h-123q-20 0 -34.5 13t-14.5 34zM150 -324v1004q0 20 13 33.5t34 13.5h88q22 0 34.5 -12.5t12.5 -34.5v-1004q0 -23 -12.5 -35t-34.5 -12h-88q-20 0 -33.5 13.5 t-13.5 33.5z" />
<glyph unicode="&#xa2;" horiz-adv-x="1142" d="M113 532l2 82q4 197 103 319t269 144v125q0 23 13.5 35t34.5 12h77q20 0 33.5 -13t13.5 -34v-123q131 -16 217.5 -75.5t125 -131t38.5 -129.5q0 -18 -14 -30.5t-33 -12.5h-90q-20 0 -30.5 9.5t-20.5 33.5q-70 184 -277 185q-119 0 -193.5 -80t-80.5 -244l-2 -72l2 -71 q6 -164 81 -244t193 -80q100 0 170 43t107 142q10 25 20.5 34t30.5 9h90q18 0 32.5 -12.5t14.5 -30.5q0 -57 -38.5 -130t-124.5 -132.5t-218 -73.5v-144q0 -20 -13 -33.5t-34 -13.5h-77q-21 0 -34.5 12.5t-13.5 34.5v146q-172 23 -270 144.5t-102 318.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1382" d="M102 39q4 135 69 216t169 105q-2 68 -25 191q-2 23 -10 72h-155q-20 0 -34 13t-14 34v65q0 20 13.5 33.5t34.5 13.5h131q-6 68 -7 148q0 233 124 379.5t364 146.5q154 0 264.5 -60.5t168.5 -164t62 -227.5q0 -18 -12 -32t-31 -14h-100q-18 0 -33.5 12.5t-19.5 33.5 q-51 285 -301 284q-139 0 -214 -98t-75 -262q0 -78 6 -146h400q20 0 33.5 -13t13.5 -34v-65q0 -20 -13.5 -33.5t-33.5 -13.5h-381q25 -172 28 -267q57 -10 102.5 -36.5t106.5 -71.5q59 -47 101.5 -69.5t95.5 -22.5q82 0 118.5 44t55.5 130q2 10 12 22.5t25 12.5h88 q18 0 28.5 -11.5t10.5 -27.5q-2 -160 -93.5 -249t-230.5 -89q-90 0 -156.5 31.5t-148.5 93.5q-68 49 -110 70.5t-93 21.5q-80 0 -113.5 -42t-50.5 -124q-2 -16 -10 -27.5t-31 -11.5h-90q-18 0 -28.5 11.5t-10.5 27.5z" />
<glyph unicode="&#xa4;" horiz-adv-x="1202" d="M135 242q0 18 15 32l88 88q-80 109 -80 252q0 141 80 254l-88 86q-14 18 -15 35q0 14 15 33l45 45q14 14 32 14q20 0 33 -14l88 -90q115 82 254 82q137 0 252 -82l88 90q14 14 33 14q20 0 33 -14l47 -45q14 -14 14 -33q0 -20 -14 -35l-90 -86q80 -117 79 -254 q0 -139 -79 -252l90 -88q14 -12 14 -32q0 -18 -14 -33l-47 -47q-14 -14 -33 -15q-14 0 -33 15l-88 90q-111 -82 -252 -82q-143 0 -254 82l-88 -90q-14 -14 -33 -15q-14 0 -32 15l-45 47q-14 18 -15 33zM315 614q0 -78 38 -143t104.5 -104t144.5 -39t143.5 39t104.5 104.5 t39 142.5q0 78 -39 144.5t-104.5 104.5t-143.5 38t-144.5 -38t-104.5 -104.5t-38 -144.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1255" d="M70 1393q0 16 12 28.5t29 12.5h102q37 0 53 -35l363 -621l360 621q20 35 55 35h101q16 0 29.5 -12.5t13.5 -28.5q0 -11 -8 -27l-416 -721h254q20 0 33.5 -13.5t13.5 -33.5v-68q0 -20 -13.5 -33.5t-33.5 -13.5h-293v-102h293q20 0 33.5 -12.5t13.5 -32.5v-68 q0 -20 -13.5 -33.5t-33.5 -13.5h-293v-174q0 -20 -13.5 -33.5t-33.5 -13.5h-100q-20 0 -34 13.5t-14 33.5v174h-292q-20 0 -32.5 13.5t-12.5 33.5v68q0 20 12 32.5t33 12.5h292v102h-292q-20 0 -32.5 13.5t-12.5 33.5v68q0 20 12 33.5t33 13.5h254l-414 721q-8 16 -8 27z " />
<glyph unicode="&#xa6;" horiz-adv-x="485" d="M158 47v631q0 20 13 33.5t34 13.5h76q20 0 33.5 -13.5t13.5 -33.5v-631q0 -20 -13.5 -33.5t-33.5 -13.5h-76q-21 0 -34 13.5t-13 33.5zM158 1020v631q0 20 13 33.5t34 13.5h76q20 0 33.5 -13.5t13.5 -33.5v-631q0 -20 -13.5 -33.5t-33.5 -13.5h-76q-21 0 -34 13t-13 34z " />
<glyph unicode="&#xa7;" horiz-adv-x="1161" d="M88 664q0 152 156 270q-49 63 -49 174q0 90 45 169t135 128t217 49q135 0 230.5 -44t142.5 -108.5t51 -127.5v-5q0 -18 -13.5 -28.5t-31.5 -10.5h-86q-18 0 -30.5 9.5t-20.5 29.5q-23 61 -75 98.5t-165 37.5q-106 0 -161.5 -53.5t-55.5 -135.5q0 -57 22.5 -90t77.5 -58.5 t172 -58.5q158 -47 249 -99t133 -125t42 -181q0 -152 -155 -268q49 -66 49 -177q0 -90 -45 -168.5t-135.5 -128t-217.5 -49.5q-135 0 -230 44t-142 108.5t-52 130.5v4q0 16 13.5 26.5t31.5 10.5h86q18 0 30.5 -8.5t21.5 -30.5q23 -61 75 -98t164 -37q106 0 161.5 53 t55.5 135q0 57 -22.5 90t-77.5 58.5t-172 58.5q-158 47 -249 99.5t-133 125t-42 181.5zM262 668q0 -59 24.5 -101.5t92 -80t196.5 -76.5q117 -35 205 -76q62 37 90.5 74.5t28.5 93.5q0 59 -24.5 101t-92 79t-196.5 76q-121 33 -205 76q-61 -37 -90 -75t-29 -91z" />
<glyph unicode="&#xa8;" horiz-adv-x="815" d="M137 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM479 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12 t-12.5 33z" />
<glyph unicode="&#xa9;" horiz-adv-x="1693" d="M111 717q0 201 99 369.5t268 268t370 99.5t369.5 -99.5t268 -268t99.5 -369.5t-99.5 -370t-268 -268t-369.5 -99t-370 99t-268 268t-99 370zM246 717q0 -164 81 -304.5t219 -223.5t302 -83t301 83t218 223.5t81 304.5t-81 304t-218 223t-301 83t-302 -83t-219 -223 t-81 -304zM508 719l2 72q6 139 96 231t242 92q106 0 181 -35t112 -87t39 -105q0 -20 -12.5 -32.5t-32.5 -12.5h-64q-18 0 -28.5 10t-18.5 31q-18 47 -60 71.5t-116 24.5q-98 0 -140 -56.5t-46 -134.5l-3 -69l3 -70q4 -78 46 -134t140 -56q74 0 116 24.5t60 71.5 q8 20 18.5 30.5t28.5 10.5h64q20 0 32.5 -12.5t12.5 -32.5q-2 -53 -39 -105.5t-112 -87t-181 -34.5q-152 0 -242 92t-96 231z" />
<glyph unicode="&#xaa;" horiz-adv-x="745" d="M106 1036q0 78 61.5 128t170.5 67l127 18q0 72 -92 72q-41 0 -62.5 -11.5t-25.5 -13.5q-25 -16 -43 -16h-60q-16 0 -28.5 8t-12.5 23q0 23 31 55.5t85 55t122 22.5q109 0 170 -60.5t61 -160.5v-291q0 -18 -12 -30.5t-31 -12.5h-59q-18 0 -30.5 12t-12.5 31v16 q-16 -29 -64.5 -49t-101.5 -20q-88 0 -140.5 45t-52.5 112zM252 1042q0 -20 21.5 -32.5t54.5 -12.5q69 0 103 40t34 104l-90 -15q-72 -12 -97.5 -32.5t-25.5 -51.5z" />
<glyph unicode="&#xab;" horiz-adv-x="1245" d="M88 649v49q4 41 33 70l395 383q27 23 45 23t30.5 -12.5t12.5 -31.5v-75q0 -25 -8 -41.5t-29 -36.5l-313 -303l313 -303q20 -20 28.5 -36t8.5 -42v-76q0 -18 -12 -30.5t-31 -12.5q-14 0 -45 23l-395 383q-29 29 -33 69zM612 649v49q0 37 33 70l395 383q27 23 45 23 t30.5 -12.5t12.5 -31.5v-75q0 -25 -8 -41.5t-28 -36.5l-314 -303l314 -303q20 -20 28 -36t8 -42v-76q0 -18 -12 -30.5t-31 -12.5q-14 0 -45 23l-395 383q-33 33 -33 69z" />
<glyph unicode="&#xac;" horiz-adv-x="1183" d="M143 606v64q0 20 13.5 33.5t33.5 13.5h799q23 0 35 -12.5t12 -34.5v-316q0 -20 -13 -32.5t-34 -12.5h-69q-21 0 -34.5 12.5t-13.5 32.5v205h-682q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#xad;" horiz-adv-x="512" />
<glyph unicode="&#xae;" horiz-adv-x="1693" d="M111 717q0 201 99 369.5t268 268t370 99.5t369.5 -99.5t268 -268t99.5 -369.5t-99.5 -370t-268 -268t-369.5 -99t-370 99t-268 268t-99 370zM246 717q0 -164 81 -304.5t219 -223.5t302 -83t301 83t218 223.5t81 304.5t-81 304t-218 223t-301 83t-302 -83t-219 -223 t-81 -304zM565 389v660q0 20 12.5 32.5t32.5 12.5h269q129 0 207.5 -57.5t78.5 -182.5q0 -80 -39 -134t-104 -81l133 -221q8 -12 8 -29q0 -18 -11 -31.5t-34 -13.5h-53q-37 0 -59 39l-134 229h-159v-223q0 -20 -12.5 -32.5t-32.5 -12.5h-58q-20 0 -32.5 12.5t-12.5 32.5z M713 745h161q139 0 140 109q0 47 -32 78t-108 31h-161v-218z" />
<glyph unicode="&#xaf;" horiz-adv-x="815" d="M137 1311v59q0 20 12.5 32.5t32.5 12.5h451q20 0 32.5 -12t12.5 -33v-59q0 -20 -12.5 -32.5t-32.5 -12.5h-451q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#xb0;" horiz-adv-x="792" d="M96 1165q0 82 40 147.5t108.5 103.5t150.5 38t150.5 -38t108.5 -103.5t40 -147.5q0 -80 -40 -146.5t-108.5 -104t-150.5 -37.5t-150.5 37.5t-108.5 104t-40 146.5zM240 1165q0 -70 43 -112.5t112 -42.5q70 0 113 43t43 112q0 70 -43 113t-113 43t-112.5 -43t-42.5 -113z " />
<glyph unicode="&#xb1;" horiz-adv-x="1144" d="M131 805v61q0 20 13.5 32.5t33.5 12.5h314v310q0 20 13 33.5t34 13.5h69q20 0 33.5 -13.5t13.5 -33.5v-310h314q20 0 32.5 -12t12.5 -33v-61q0 -20 -12.5 -33.5t-32.5 -13.5h-314v-318q0 -20 -13 -33.5t-34 -13.5h-69q-21 0 -34 13.5t-13 33.5v318h-314q-20 0 -33.5 13 t-13.5 34zM152 47v62q0 20 13 33.5t34 13.5h749q20 0 32.5 -13.5t12.5 -33.5v-62q0 -20 -12 -33.5t-33 -13.5h-749q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#xb2;" horiz-adv-x="698" d="M61 762v43q0 16 10.5 31.5t39.5 37.5l94 68q109 76 158 116t74.5 76.5t25.5 79.5q0 47 -34 75t-83 28q-45 0 -70.5 -14.5t-32.5 -25.5t-22 -40q-12 -23 -22.5 -30t-26.5 -7h-55q-37 0 -37 35q0 53 37 101t97 78t132 30q117 0 188.5 -66.5t71.5 -160.5q0 -72 -31.5 -126.5 t-94 -106.5t-183.5 -136h295q20 0 32.5 -12.5t12.5 -32.5v-41q0 -20 -12.5 -32.5t-32.5 -12.5h-486q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#xb3;" horiz-adv-x="698" d="M55 877v4q0 14 12.5 24t22.5 10h51q27 0 52 -24q6 -6 21 -21.5t45 -24.5t77 -9q63 0 104 24.5t41 67.5q0 49 -34.5 70.5t-100.5 21.5h-84q-20 0 -32.5 12t-12.5 33v39q0 20 25 45l166 151h-275q-20 0 -32.5 12.5t-12.5 33.5v43q0 20 12.5 32.5t32.5 12.5h420 q20 0 32.5 -12.5t12.5 -32.5v-39q0 -23 -6 -39.5t-27 -34.5l-147 -133q104 -8 156.5 -65.5t52.5 -149.5q0 -104 -76 -162.5t-213 -58.5q-133 0 -206 50t-77 120z" />
<glyph unicode="&#xb4;" horiz-adv-x="684" d="M137 1251q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32z" />
<glyph unicode="&#xb5;" horiz-adv-x="1239" d="M147 -362v1380q0 20 13.5 33.5t34.5 13.5h96q20 0 33.5 -13.5t13.5 -33.5v-567q0 -94 41 -165t105.5 -109t136.5 -38t136 38t105 108.5t41 165.5v567q0 20 13.5 33.5t33.5 13.5h94q20 0 34 -13.5t14 -33.5v-971q0 -20 -13.5 -33.5t-34.5 -13.5h-88q-20 0 -33.5 13.5 t-13.5 33.5v92q-55 -72 -124.5 -110.5t-172.5 -38.5q-92 0 -156.5 33.5t-117.5 95.5v-481q0 -20 -13.5 -34t-33.5 -14h-96q-21 0 -34.5 13.5t-13.5 34.5z" />
<glyph unicode="&#xb6;" horiz-adv-x="1079" d="M63 1065q0 100 49.5 185t134.5 134.5t185 49.5h442q20 0 34 -13.5t14 -34.5v-1544q0 -20 -13.5 -33.5t-34.5 -13.5h-69q-20 0 -33.5 12.5t-13.5 34.5v1442h-162v-1442q0 -20 -13.5 -33.5t-33.5 -13.5h-70q-20 0 -33.5 12.5t-13.5 34.5v852q-100 0 -185 50.5t-134.5 135.5 t-49.5 185z" />
<glyph unicode="&#xb7;" horiz-adv-x="675" d="M119 631q0 90 64.5 154.5t154.5 64.5t154.5 -64.5t64.5 -154.5t-64.5 -153.5t-154.5 -63.5t-154.5 63.5t-64.5 153.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="643" d="M137 -362q0 10 8 16l31 29q8 8 19 8q8 0 28.5 -12.5t39 -20.5t48.5 -8q37 0 60.5 21.5t23.5 56.5q0 33 -23.5 52t-60.5 19q-23 0 -52.5 -9t-39.5 -9q-18 0 -29 12l-34 37q-6 8 -6 18t14 43l72 164h122l-84 -170q27 16 74 17q70 0 114 -48.5t44 -123.5q0 -78 -49 -129.5 t-142 -51.5q-69 0 -123.5 30t-54.5 59z" />
<glyph unicode="&#xb9;" horiz-adv-x="495" d="M33 1233q0 20 18 35l203 151q20 14 47 15h53q20 0 32.5 -12.5t12.5 -32.5v-627q0 -20 -12 -32.5t-33 -12.5h-55q-20 0 -32.5 12t-12.5 33v491l-123 -92q-10 -8 -27 -8q-20 0 -36 18l-25 33q-10 14 -10 29z" />
<glyph unicode="&#xba;" horiz-adv-x="735" d="M111 1161l2 56q6 111 76.5 169t179.5 58t179 -58.5t77 -168.5q2 -10 2 -56q0 -45 -2 -55q-6 -113 -73 -170t-183 -57q-115 0 -182.5 57t-73.5 170zM258 1161l2 -45q6 -111 109 -110q102 -1 108 110q2 10 2 45t-2 45q-4 51 -30.5 81t-77.5 30t-78 -30t-31 -81z" />
<glyph unicode="&#xbb;" horiz-adv-x="1245" d="M117 217v76q0 27 8 42t29 36l313 303l-313 303q-20 20 -28.5 36.5t-8.5 41.5v75q0 18 12 31t31 13q18 0 45 -23l395 -383q33 -33 33 -70v-49q0 -37 -33 -69l-395 -383q-31 -23 -45 -23q-19 0 -31 12.5t-12 30.5zM641 217v76q0 27 8 42t29 36l313 303l-313 303 q-20 20 -28.5 36.5t-8.5 41.5v75q0 18 12.5 31t30.5 13t45 -23l395 -383q29 -29 33 -70v-49q-4 -41 -33 -69l-395 -383q-31 -23 -45 -23q-18 0 -30.5 12.5t-12.5 30.5z" />
<glyph unicode="&#xbc;" horiz-adv-x="1492" d="M33 1233q0 20 18 35l203 151q20 14 47 15h53q20 0 32.5 -12.5t12.5 -32.5v-627q0 -20 -12 -32.5t-33 -12.5h-55q-20 0 -32.5 12t-12.5 33v491l-123 -92q-10 -8 -27 -8q-20 0 -36 18l-25 33q-10 14 -10 29zM68 41q0 12 8 22l1010 1332q16 23 29.5 31t37.5 8h56 q18 0 30.5 -11.5t12.5 -29.5q0 -12 -8 -23l-1012 -1331q-16 -20 -29.5 -29.5t-38.5 -9.5h-55q-19 0 -30 11.5t-11 29.5zM776 184v45q0 29 25 62l276 385q27 37 62 37h81q20 0 33 -12.5t13 -32.5v-396h71q20 0 32.5 -12t12.5 -33v-43q0 -20 -12 -32.5t-33 -12.5h-71v-98 q0 -20 -12.5 -32.5t-33.5 -12.5h-53q-20 0 -32.5 12t-12.5 33v98h-301q-20 0 -32.5 12.5t-12.5 32.5zM946 270h178v256z" />
<glyph unicode="&#xbd;" horiz-adv-x="1507" d="M33 1233q0 20 18 35l203 151q20 14 47 15h53q20 0 32.5 -12.5t12.5 -32.5v-627q0 -20 -12 -32.5t-33 -12.5h-55q-20 0 -32.5 12t-12.5 33v491l-123 -92q-10 -8 -27 -8q-20 0 -36 18l-25 33q-10 14 -10 29zM68 41q0 12 8 22l1010 1332q16 23 29.5 31t37.5 8h56 q18 0 30.5 -11.5t12.5 -29.5q0 -12 -8 -23l-1012 -1331q-16 -20 -29.5 -29.5t-38.5 -9.5h-55q-19 0 -30 11.5t-11 29.5zM809 45v43q0 16 10.5 31.5t39.5 38.5l94 67q109 76 158 116t74.5 76.5t25.5 80.5q0 47 -34 74.5t-83 27.5q-45 0 -70.5 -14.5t-32.5 -25.5t-22 -40 q-12 -23 -22.5 -30t-26.5 -7h-55q-37 0 -37 35q0 53 37 101t97 78t132 30q117 0 188.5 -66.5t71.5 -160.5q0 -72 -31.5 -126.5t-94 -106.5t-183.5 -136h295q20 0 32.5 -12.5t12.5 -32.5v-41q0 -20 -12.5 -32.5t-32.5 -12.5h-486q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#xbe;" horiz-adv-x="1662" d="M55 877v4q0 14 12.5 24t22.5 10h51q27 0 52 -24q6 -6 21 -21.5t45 -24.5t77 -9q63 0 104 24.5t41 67.5q0 49 -34.5 70.5t-100.5 21.5h-84q-20 0 -32.5 12t-12.5 33v39q0 20 25 45l166 151h-275q-20 0 -32.5 12.5t-12.5 33.5v43q0 20 12.5 32.5t32.5 12.5h420 q20 0 32.5 -12.5t12.5 -32.5v-39q0 -23 -6 -39.5t-27 -34.5l-147 -133q104 -8 156.5 -65.5t52.5 -149.5q0 -104 -76 -162.5t-213 -58.5q-133 0 -206 50t-77 120zM270 41q0 12 8 22l1010 1332q16 23 29.5 31t37.5 8h56q18 0 30.5 -11.5t12.5 -29.5q0 -12 -8 -23l-1012 -1331 q-16 -20 -29.5 -29.5t-38.5 -9.5h-55q-19 0 -30 11.5t-11 29.5zM983 184v45q0 29 25 62l276 385q27 37 62 37h81q20 0 33 -12.5t13 -32.5v-396h71q20 0 32.5 -12t12.5 -33v-43q0 -20 -12 -32.5t-33 -12.5h-71v-98q0 -20 -12.5 -32.5t-33.5 -12.5h-53q-20 0 -32.5 12 t-12.5 33v98h-301q-20 0 -32.5 12.5t-12.5 32.5zM1153 270h178v256z" />
<glyph unicode="&#xbf;" horiz-adv-x="1069" d="M70 -29q0 104 47 180t141 177q78 84 117 142t45 130q4 57 4 86q10 49 51 49h100q21 0 33.5 -12t12.5 -33v-84q-4 -109 -56.5 -188.5t-148.5 -177.5q-74 -76 -110 -125t-42 -107q-2 -8 -2 -26q0 -94 80 -149.5t186 -55.5q115 0 192 60.5t99 189.5q8 45 53 45h91 q18 0 31.5 -12.5t13.5 -32.5q-4 -113 -64.5 -208t-170 -151.5t-251.5 -56.5q-150 0 -252 51t-151 134t-49 175zM418 899v119q0 20 14 33.5t35 13.5h127q20 0 33.5 -13.5t13.5 -33.5v-119q0 -20 -13.5 -34.5t-33.5 -14.5h-127q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1368" d="M41 41l4 22l508 1326q16 45 65 45h132q49 0 65 -45l508 -1326l4 -22q0 -16 -13.5 -28.5t-29.5 -12.5h-96q-20 0 -33.5 11.5t-17.5 23.5l-115 295h-678l-113 -295q-16 -35 -51 -35h-98q-16 0 -28.5 12.5t-12.5 28.5zM362 1778q0 39 39 39h146q27 0 41 -7.5t34 -29.5 l152 -185q10 -10 10 -26q0 -33 -33 -33h-75q-23 0 -37.5 4t-30.5 19l-235 194q-10 10 -11 25zM403 500h562l-281 735z" />
<glyph unicode="&#xc1;" horiz-adv-x="1368" d="M41 41l4 22l508 1326q16 45 65 45h132q49 0 65 -45l508 -1326l4 -22q0 -16 -13.5 -28.5t-29.5 -12.5h-96q-20 0 -33.5 11.5t-17.5 23.5l-115 295h-678l-113 -295q-16 -35 -51 -35h-98q-16 0 -28.5 12.5t-12.5 28.5zM403 500h562l-281 735zM600 1569q0 16 10 26l152 185 q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#xc2;" horiz-adv-x="1368" d="M41 41l4 22l508 1326q16 45 65 45h132q49 0 65 -45l508 -1326l4 -22q0 -16 -13.5 -28.5t-29.5 -12.5h-96q-20 0 -33.5 11.5t-17.5 23.5l-115 295h-678l-113 -295q-16 -35 -51 -35h-98q-16 0 -28.5 12.5t-12.5 28.5zM376 1561q0 18 23 38l186 187q20 20 33.5 25.5 t34.5 5.5h61q18 0 32.5 -5.5t33.5 -25.5l188 -187q20 -20 21 -38q0 -25 -29 -25h-53q-35 0 -55 14l-168 117l-168 -117q-25 -14 -58 -14h-53q-29 0 -29 25zM403 500h562l-281 735z" />
<glyph unicode="&#xc3;" horiz-adv-x="1368" d="M41 41l4 22l508 1326q16 45 65 45h132q49 0 65 -45l508 -1326l4 -22q0 -16 -13.5 -28.5t-29.5 -12.5h-96q-20 0 -33.5 11.5t-17.5 23.5l-115 295h-678l-113 -295q-16 -35 -51 -35h-98q-16 0 -28.5 12.5t-12.5 28.5zM401 1569q0 68 48.5 128t123.5 60q37 0 66 -11t63 -32 q18 -10 39 -19t39 -9t27.5 8t19.5 24t18.5 22.5t22.5 6.5h64q14 0 23.5 -10.5t9.5 -22.5q0 -41 -22.5 -85t-62.5 -73.5t-89 -29.5q-37 0 -64 10t-65 31q-18 10 -39 19t-39 9q-16 0 -26.5 -8t-20.5 -22q-16 -29 -39 -29h-64q-14 0 -23.5 10t-9.5 23zM403 500h562l-281 735z " />
<glyph unicode="&#xc4;" horiz-adv-x="1368" d="M41 41l4 22l508 1326q16 45 65 45h132q49 0 65 -45l508 -1326l4 -22q0 -16 -13.5 -28.5t-29.5 -12.5h-96q-20 0 -33.5 11.5t-17.5 23.5l-115 295h-678l-113 -295q-16 -35 -51 -35h-98q-16 0 -28.5 12.5t-12.5 28.5zM383 1581v109q0 20 12 33.5t33 13.5h106 q20 0 33.5 -13.5t13.5 -33.5v-109q0 -20 -13 -32.5t-34 -12.5h-106q-21 0 -33 12.5t-12 32.5zM403 500h562l-281 735zM786 1581v109q0 20 12.5 33.5t32.5 13.5h109q20 0 33.5 -13.5t13.5 -33.5v-109q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1368" d="M41 41l4 22l508 1326q16 45 65 45h132q49 0 65 -45l508 -1326l4 -22q0 -16 -13.5 -28.5t-29.5 -12.5h-96q-20 0 -33.5 11.5t-17.5 23.5l-115 295h-678l-113 -295q-16 -35 -51 -35h-98q-16 0 -28.5 12.5t-12.5 28.5zM403 500h562l-281 735zM514 1675q0 70 49 116t121 46 t121 -46t49 -116q0 -68 -49.5 -113.5t-120.5 -45.5q-72 0 -121 46t-49 113zM622 1675q0 -27 17.5 -44t44.5 -17q26 0 43.5 17.5t17.5 43.5q0 27 -17.5 44.5t-43.5 17.5q-27 0 -44.5 -17.5t-17.5 -44.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1912" d="M10 41q0 6 3 14t3 13q27 74 153 315.5t550 1007.5q20 43 59 43h965q23 0 35 -13.5t12 -36.5v-73q0 -20 -13.5 -32.5t-33.5 -12.5h-688v-457h643q20 0 33.5 -13.5t13.5 -35.5v-70q0 -20 -12.5 -33.5t-34.5 -13.5h-643v-473h704q20 0 33.5 -13.5t13.5 -35.5v-74 q0 -20 -13 -33.5t-34 -13.5h-848q-20 0 -33.5 13.5t-13.5 33.5v283h-497q-160 -285 -162 -295q-16 -35 -51 -35h-103q-16 0 -28.5 12.5t-12.5 28.5zM442 500h422v753h-12z" />
<glyph unicode="&#xc7;" horiz-adv-x="1361" d="M123 715q0 127 2 184q10 258 152.5 406.5t414.5 148.5q182 0 309 -64.5t191.5 -169t71.5 -223.5q0 -18 -13.5 -29.5t-31.5 -11.5h-103q-20 0 -32.5 10.5t-18.5 34.5q-35 158 -126 221.5t-247 63.5q-356 0 -370 -397q-2 -55 -3 -170q0 -115 3 -174q14 -395 370 -395 q154 0 246 63t127 219q6 25 18.5 35t32.5 10h103q18 0 31.5 -11t13.5 -30q-6 -119 -71 -223t-192 -168.5t-309 -64.5l-45 -95q27 16 74 17q70 0 114 -48.5t44 -123.5q0 -78 -49.5 -129.5t-141.5 -51.5q-70 0 -124 30t-54 59q0 10 8 16l31 29q8 8 18 8q8 0 28.5 -12.5 t39 -20.5t49.5 -8q37 0 60.5 21.5t23.5 56.5q0 33 -23.5 52t-60.5 19q-23 0 -52.5 -9t-39.5 -9q-18 0 -29 12l-33 37q-8 8 -8 18t15 43l41 97q-217 31 -330 174t-123 373q-2 55 -2 180z" />
<glyph unicode="&#xc8;" horiz-adv-x="1239" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h842q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-698v-457h653q23 0 36 -13.5t13 -35.5v-70q0 -20 -13.5 -33.5t-35.5 -13.5h-653v-473h714q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-858 q-20 0 -32.5 13.5t-12.5 33.5zM297 1778q0 39 39 39h146q27 0 41 -7.5t34 -29.5l152 -185q10 -10 10 -26q0 -33 -33 -33h-75q-23 0 -37.5 4t-30.5 19l-235 194q-10 10 -11 25z" />
<glyph unicode="&#xc9;" horiz-adv-x="1239" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h842q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-698v-457h653q23 0 36 -13.5t13 -35.5v-70q0 -20 -13.5 -33.5t-35.5 -13.5h-653v-473h714q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-858 q-20 0 -32.5 13.5t-12.5 33.5zM534 1569q0 16 10 26l152 185q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#xca;" horiz-adv-x="1239" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h842q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-698v-457h653q23 0 36 -13.5t13 -35.5v-70q0 -20 -13.5 -33.5t-35.5 -13.5h-653v-473h714q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-858 q-20 0 -32.5 13.5t-12.5 33.5zM311 1561q0 18 23 38l186 187q20 20 33.5 25.5t34.5 5.5h61q18 0 32.5 -5.5t33.5 -25.5l188 -187q20 -20 21 -38q0 -25 -29 -25h-53q-35 0 -55 14l-168 117l-168 -117q-25 -14 -58 -14h-53q-29 0 -29 25z" />
<glyph unicode="&#xcb;" horiz-adv-x="1239" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h842q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-698v-457h653q23 0 36 -13.5t13 -35.5v-70q0 -20 -13.5 -33.5t-35.5 -13.5h-653v-473h714q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-858 q-20 0 -32.5 13.5t-12.5 33.5zM318 1581v109q0 20 12 33.5t33 13.5h106q20 0 33.5 -13.5t13.5 -33.5v-109q0 -20 -13 -32.5t-34 -12.5h-106q-21 0 -33 12.5t-12 32.5zM721 1581v109q0 20 12.5 33.5t32.5 13.5h109q20 0 33.5 -13.5t13.5 -33.5v-109q0 -20 -13.5 -32.5 t-33.5 -12.5h-109q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="555" d="M-43 1778q0 39 39 39h146q27 0 41 -7.5t34 -29.5l152 -185q10 -10 10 -26q0 -33 -33 -33h-75q-23 0 -37.5 4t-30.5 19l-235 194q-10 10 -11 25zM180 47v1339q0 20 12.5 34t32.5 14h103q23 0 35 -12.5t12 -35.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-103 q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#xcd;" horiz-adv-x="555" d="M180 47v1339q0 20 12.5 34t32.5 14h103q23 0 35 -12.5t12 -35.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5t-12.5 33.5zM194 1569q0 16 10 26l152 185q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5 h-76q-33 0 -33 33z" />
<glyph unicode="&#xce;" horiz-adv-x="555" d="M-29 1561q0 18 23 38l186 187q20 20 33.5 25.5t34.5 5.5h61q18 0 32.5 -5.5t33.5 -25.5l188 -187q20 -20 21 -38q0 -25 -29 -25h-53q-35 0 -55 14l-168 117l-168 -117q-25 -14 -58 -14h-53q-29 0 -29 25zM180 47v1339q0 20 12.5 34t32.5 14h103q23 0 35 -12.5t12 -35.5 v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#xcf;" horiz-adv-x="555" d="M-22 1581v109q0 20 12 33.5t33 13.5h106q20 0 33.5 -13.5t13.5 -33.5v-109q0 -20 -13 -32.5t-34 -12.5h-106q-21 0 -33 12.5t-12 32.5zM180 47v1339q0 20 12.5 34t32.5 14h103q23 0 35 -12.5t12 -35.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5 t-12.5 33.5zM381 1581v109q0 20 12.5 33.5t32.5 13.5h109q20 0 33.5 -13.5t13.5 -33.5v-109q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1427" d="M31 690v62q0 20 13 33.5t34 13.5h121v585q0 23 12 36.5t33 13.5h463q309 0 446 -138.5t141 -416.5q2 -59 2 -160q0 -102 -2 -162q-4 -193 -61 -315.5t-184 -182t-334 -59.5h-471q-21 0 -33 13.5t-12 33.5v596h-121q-20 0 -33.5 13.5t-13.5 33.5zM391 170h316 q143 0 228 40t123 125t42 230v305q-4 205 -96.5 300.5t-305.5 95.5h-307v-467h275q20 0 33.5 -13.5t13.5 -33.5v-62q0 -20 -13.5 -33.5t-33.5 -13.5h-275v-473z" />
<glyph unicode="&#xd1;" horiz-adv-x="1419" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h90q20 0 30.5 -8.5t21.5 -20.5l686 -1057v1036q0 23 13 36.5t34 13.5h92q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-33.5 -14.5h-92q-35 0 -49 29l-686 1046v-1028q0 -20 -13.5 -33.5t-34.5 -13.5h-94q-20 0 -32.5 13.5 t-12.5 33.5zM428 1569q0 68 48.5 128t123.5 60q37 0 66 -11t63 -32q18 -10 39 -19t39 -9t27.5 8t19.5 24t18.5 22.5t22.5 6.5h64q14 0 23.5 -10.5t9.5 -22.5q0 -41 -22.5 -85t-62.5 -73.5t-89 -29.5q-37 0 -64 10t-65 31q-18 10 -39 19t-39 9q-16 0 -26.5 -8t-20.5 -22 q-16 -29 -39 -29h-64q-14 0 -23.5 10t-9.5 23z" />
<glyph unicode="&#xd2;" horiz-adv-x="1382" d="M125 717q0 113 2 172q8 279 158.5 422t406.5 143t405.5 -143.5t157.5 -421.5q4 -119 5 -172q0 -51 -5 -174q-8 -283 -152.5 -423t-410.5 -140t-411.5 140t-153.5 423q-2 61 -2 174zM322 717q0 -102 2 -164q6 -205 104 -304t264 -99q164 0 262.5 99t106.5 304q4 123 4 164 q0 45 -4 164q-8 205 -106.5 305t-262.5 100t-263 -100.5t-105 -304.5q-2 -59 -2 -164zM370 1778q0 39 39 39h146q27 0 41 -7.5t34 -29.5l152 -185q10 -10 10 -26q0 -33 -33 -33h-75q-23 0 -37.5 4t-30.5 19l-235 194q-10 10 -11 25z" />
<glyph unicode="&#xd3;" horiz-adv-x="1382" d="M125 717q0 113 2 172q8 279 158.5 422t406.5 143t405.5 -143.5t157.5 -421.5q4 -119 5 -172q0 -51 -5 -174q-8 -283 -152.5 -423t-410.5 -140t-411.5 140t-153.5 423q-2 61 -2 174zM322 717q0 -102 2 -164q6 -205 104 -304t264 -99q164 0 262.5 99t106.5 304q4 123 4 164 q0 45 -4 164q-8 205 -106.5 305t-262.5 100t-263 -100.5t-105 -304.5q-2 -59 -2 -164zM608 1569q0 16 10 26l152 185q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#xd4;" horiz-adv-x="1382" d="M125 717q0 113 2 172q8 279 158.5 422t406.5 143t405.5 -143.5t157.5 -421.5q4 -119 5 -172q0 -51 -5 -174q-8 -283 -152.5 -423t-410.5 -140t-411.5 140t-153.5 423q-2 61 -2 174zM322 717q0 -102 2 -164q6 -205 104 -304t264 -99q164 0 262.5 99t106.5 304q4 123 4 164 q0 45 -4 164q-8 205 -106.5 305t-262.5 100t-263 -100.5t-105 -304.5q-2 -59 -2 -164zM385 1561q0 18 23 38l186 187q20 20 33.5 25.5t34.5 5.5h61q18 0 32.5 -5.5t33.5 -25.5l188 -187q20 -20 21 -38q0 -25 -29 -25h-53q-35 0 -55 14l-168 117l-168 -117q-25 -14 -58 -14 h-53q-29 0 -29 25z" />
<glyph unicode="&#xd5;" horiz-adv-x="1382" d="M125 717q0 113 2 172q8 279 158.5 422t406.5 143t405.5 -143.5t157.5 -421.5q4 -119 5 -172q0 -51 -5 -174q-8 -283 -152.5 -423t-410.5 -140t-411.5 140t-153.5 423q-2 61 -2 174zM322 717q0 -102 2 -164q6 -205 104 -304t264 -99q164 0 262.5 99t106.5 304q4 123 4 164 q0 45 -4 164q-8 205 -106.5 305t-262.5 100t-263 -100.5t-105 -304.5q-2 -59 -2 -164zM409 1569q0 68 48.5 128t123.5 60q37 0 66 -11t63 -32q18 -10 39 -19t39 -9t27.5 8t19.5 24t18.5 22.5t22.5 6.5h64q14 0 23.5 -10.5t9.5 -22.5q0 -41 -22.5 -85t-62.5 -73.5t-89 -29.5 q-37 0 -64 10t-65 31q-18 10 -39 19t-39 9q-16 0 -26.5 -8t-20.5 -22q-16 -29 -39 -29h-64q-14 0 -23.5 10t-9.5 23z" />
<glyph unicode="&#xd6;" horiz-adv-x="1382" d="M125 717q0 113 2 172q8 279 158.5 422t406.5 143t405.5 -143.5t157.5 -421.5q4 -119 5 -172q0 -51 -5 -174q-8 -283 -152.5 -423t-410.5 -140t-411.5 140t-153.5 423q-2 61 -2 174zM322 717q0 -102 2 -164q6 -205 104 -304t264 -99q164 0 262.5 99t106.5 304q4 123 4 164 q0 45 -4 164q-8 205 -106.5 305t-262.5 100t-263 -100.5t-105 -304.5q-2 -59 -2 -164zM391 1581v109q0 20 12 33.5t33 13.5h106q20 0 33.5 -13.5t13.5 -33.5v-109q0 -20 -13 -32.5t-34 -12.5h-106q-21 0 -33 12.5t-12 32.5zM794 1581v109q0 20 12.5 33.5t32.5 13.5h109 q20 0 33.5 -13.5t13.5 -33.5v-109q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="1140" d="M127 346q0 18 14 33l301 301l-301 305q-14 14 -14 33q0 20 14 33l62 61q14 14 33 14q18 0 32 -14l301 -305l305 305q14 14 33 14q20 0 33 -14l61 -61q14 -12 15 -33q0 -18 -15 -33l-303 -305l303 -301q14 -14 15 -33q0 -18 -15 -33l-61 -61q-12 -14 -33 -14q-18 0 -33 14 l-305 301l-301 -303q-18 -14 -32 -14t-33 14l-62 61q-14 14 -14 35z" />
<glyph unicode="&#xd8;" horiz-adv-x="1429" d="M23 434v70q0 25 9 35t36 20l84 31q-2 39 -2 115q0 53 4 184q8 279 158.5 422t406.5 143q219 0 361.5 -109.5t183.5 -320.5l77 29q16 6 25 6q41 0 41 -37v-70q0 -23 -9 -33t-36 -20l-80 -31q4 -115 4 -163q0 -47 -4 -162q-8 -283 -152.5 -423t-410.5 -140 q-487 -1 -553 452l-78 -29q-16 -6 -25 -6q-41 0 -40 37zM346 668l733 284q-23 168 -118 251t-242 83q-164 0 -263.5 -100.5t-105.5 -304.5q-6 -129 -4 -213zM352 506q18 -182 114.5 -269t252.5 -87q164 0 262 99t104 304q2 66 2 170v70z" />
<glyph unicode="&#xd9;" horiz-adv-x="1433" d="M168 553v831q0 23 12.5 36.5t32.5 13.5h98q23 0 36 -13.5t13 -36.5v-835q0 -201 93.5 -299t265.5 -98q170 0 263 99t93 298v835q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-831q0 -289 -142.5 -431t-406.5 -142t-407.5 142t-143.5 431zM397 1778 q0 39 39 39h146q27 0 41 -7.5t34 -29.5l152 -185q10 -10 10 -26q0 -33 -33 -33h-75q-23 0 -37.5 4t-30.5 19l-235 194q-10 10 -11 25z" />
<glyph unicode="&#xda;" horiz-adv-x="1433" d="M168 553v831q0 23 12.5 36.5t32.5 13.5h98q23 0 36 -13.5t13 -36.5v-835q0 -201 93.5 -299t265.5 -98q170 0 263 99t93 298v835q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-831q0 -289 -142.5 -431t-406.5 -142t-407.5 142t-143.5 431zM635 1569 q0 16 10 26l152 185q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#xdb;" horiz-adv-x="1433" d="M168 553v831q0 23 12.5 36.5t32.5 13.5h98q23 0 36 -13.5t13 -36.5v-835q0 -201 93.5 -299t265.5 -98q170 0 263 99t93 298v835q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-831q0 -289 -142.5 -431t-406.5 -142t-407.5 142t-143.5 431zM411 1561 q0 18 23 38l186 187q20 20 33.5 25.5t34.5 5.5h61q18 0 32.5 -5.5t33.5 -25.5l188 -187q20 -20 21 -38q0 -25 -29 -25h-53q-35 0 -55 14l-168 117l-168 -117q-25 -14 -58 -14h-53q-29 0 -29 25z" />
<glyph unicode="&#xdc;" horiz-adv-x="1433" d="M168 553v831q0 23 12.5 36.5t32.5 13.5h98q23 0 36 -13.5t13 -36.5v-835q0 -201 93.5 -299t265.5 -98q170 0 263 99t93 298v835q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-831q0 -289 -142.5 -431t-406.5 -142t-407.5 142t-143.5 431zM418 1581v109 q0 20 12 33.5t33 13.5h106q20 0 33.5 -13.5t13.5 -33.5v-109q0 -20 -13 -32.5t-34 -12.5h-106q-21 0 -33 12.5t-12 32.5zM821 1581v109q0 20 12.5 33.5t32.5 13.5h109q20 0 33.5 -13.5t13.5 -33.5v-109q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12.5t-12.5 32.5z " />
<glyph unicode="&#xdd;" horiz-adv-x="1320" d="M63 1393q0 16 12.5 28.5t28.5 12.5h99q37 0 53 -35l406 -670l403 670q20 35 53 35h99q16 0 29.5 -12.5t13.5 -28.5q0 -10 -9 -27l-493 -834v-485q0 -20 -13.5 -33.5t-35.5 -13.5h-99q-20 0 -33.5 13.5t-13.5 33.5v485l-493 834q-6 12 -7 27zM577 1569q0 16 10 26l152 185 q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#xde;" horiz-adv-x="1320" d="M180 47v1339q0 20 12.5 34t32.5 14h105q20 0 32.5 -13.5t12.5 -34.5v-231h368q231 0 363.5 -111.5t132.5 -324.5t-132 -323.5t-364 -110.5h-368v-238q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5t-12.5 33.5zM375 451h358q154 0 231.5 68.5t77.5 199.5 t-76.5 200.5t-232.5 69.5h-358v-538z" />
<glyph unicode="&#xdf;" horiz-adv-x="1202" d="M156 47v965q0 193 109.5 317.5t334.5 124.5q152 0 251 -55.5t146 -142.5t47 -183q0 -115 -51 -189.5t-123 -111.5q233 -80 234 -354q0 -100 -49 -196.5t-153.5 -159t-260.5 -62.5h-141q-20 0 -34.5 13.5t-14.5 33.5v64q0 20 14 33.5t35 11.5h112q143 0 223 75.5t80 190.5 q0 119 -78.5 193.5t-226.5 74.5h-110q-23 0 -36 12.5t-13 34.5v58q0 20 13 33.5t36 13.5h94q270 2 270 227q0 100 -72.5 164.5t-191.5 64.5q-121 0 -191.5 -74.5t-70.5 -217.5v-959q0 -20 -12.5 -33.5t-32.5 -13.5h-90q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#xe0;" d="M78 289q0 135 110.5 221t300.5 115l308 43v59q0 211 -244 211q-94 0 -151.5 -38t-84.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 44 109.5t136 108.5t225 46q229 0 328.5 -106.5t99.5 -265.5v-666q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -32.5 13.5t-12.5 33.5v90q-45 -68 -131 -112.5t-221 -44.5q-101 0 -186 40.5t-135 111.5t-50 157zM239 1460q0 39 39 39h133q29 0 43.5 -7t32.5 -30l154 -184q10 -10 10 -27q0 -33 -33 -32h-65q-23 0 -36.5 4t-29.5 18l-238 195q-10 10 -10 24zM258 303 q0 -84 68.5 -129t162.5 -45q136 0 222 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5z" />
<glyph unicode="&#xe1;" d="M78 289q0 135 110.5 221t300.5 115l308 43v59q0 211 -244 211q-94 0 -151.5 -38t-84.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 44 109.5t136 108.5t225 46q229 0 328.5 -106.5t99.5 -265.5v-666q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -32.5 13.5t-12.5 33.5v90q-45 -68 -131 -112.5t-221 -44.5q-101 0 -186 40.5t-135 111.5t-50 157zM258 303q0 -84 68.5 -129t162.5 -45q136 0 222 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5zM438 1251q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133 q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32z" />
<glyph unicode="&#xe2;" d="M78 289q0 135 110.5 221t300.5 115l308 43v59q0 211 -244 211q-94 0 -151.5 -38t-84.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 44 109.5t136 108.5t225 46q229 0 328.5 -106.5t99.5 -265.5v-666q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -32.5 13.5t-12.5 33.5v90q-45 -68 -131 -112.5t-221 -44.5q-101 0 -186 40.5t-135 111.5t-50 157zM246 1243q0 18 23 39l186 186q20 20 33.5 25.5t34.5 5.5h57q20 0 33.5 -5t34.5 -26l186 -186q23 -23 23 -39q0 -25 -31 -24h-51q-35 0 -56 14l-168 117l-168 -117 q-25 -14 -57 -14h-51q-29 -1 -29 24zM258 303q0 -84 68.5 -129t162.5 -45q136 0 222 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5z" />
<glyph unicode="&#xe3;" d="M78 289q0 135 110.5 221t300.5 115l308 43v59q0 211 -244 211q-94 0 -151.5 -38t-84.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 44 109.5t136 108.5t225 46q229 0 328.5 -106.5t99.5 -265.5v-666q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -32.5 13.5t-12.5 33.5v90q-45 -68 -131 -112.5t-221 -44.5q-101 0 -186 40.5t-135 111.5t-50 157zM258 303q0 -84 68.5 -129t162.5 -45q136 0 222 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5zM278 1251q0 68 46 123.5t122 55.5q37 0 61.5 -10.5 t59.5 -30.5q4 -2 26.5 -15.5t47.5 -13.5q18 0 27.5 8t19.5 25q10 16 18 22t23 6h61q16 0 25.5 -10t9.5 -22q0 -68 -47 -124.5t-123 -56.5q-37 0 -61.5 10.5t-59.5 30.5q-18 10 -36.5 19.5t-37.5 9.5q-16 0 -26 -8t-21 -23q-16 -29 -39 -28h-63q-14 0 -23.5 10t-9.5 22z" />
<glyph unicode="&#xe4;" d="M78 289q0 135 110.5 221t300.5 115l308 43v59q0 211 -244 211q-94 0 -151.5 -38t-84.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 44 109.5t136 108.5t225 46q229 0 328.5 -106.5t99.5 -265.5v-666q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -32.5 13.5t-12.5 33.5v90q-45 -68 -131 -112.5t-221 -44.5q-101 0 -186 40.5t-135 111.5t-50 157zM258 303q0 -84 68.5 -129t162.5 -45q136 0 222 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5zM287 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5 t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM629 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#xe5;" d="M78 289q0 135 110.5 221t300.5 115l308 43v59q0 211 -244 211q-94 0 -151.5 -38t-84.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 44 109.5t136 108.5t225 46q229 0 328.5 -106.5t99.5 -265.5v-666q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -32.5 13.5t-12.5 33.5v90q-45 -68 -131 -112.5t-221 -44.5q-101 0 -186 40.5t-135 111.5t-50 157zM258 303q0 -84 68.5 -129t162.5 -45q136 0 222 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5zM379 1368q0 70 49.5 116t120.5 46q72 0 121 -46t49 -116 q0 -68 -49 -114t-121 -46q-71 0 -120.5 46t-49.5 114zM488 1368q0 -27 17.5 -44t43.5 -17q27 0 44.5 17t17.5 44t-17.5 44.5t-44.5 17.5t-44 -17.5t-17 -44.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1822" d="M80 289q0 137 111.5 225t300.5 115l307 43v55q0 211 -244 211q-94 0 -152.5 -38t-85.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 45 109.5t137 108.5t225 46q139 0 227 -44t129 -117q125 162 351 161q258 0 360 -159.5t102 -372.5v-37 q0 -20 -13 -33.5t-34 -13.5h-692v-18q4 -164 84 -243t191 -79q74 0 139 28.5t106 88.5q16 23 27.5 29t36.5 6h84q18 0 30.5 -11.5t12.5 -29.5q0 -41 -52.5 -103.5t-150.5 -109.5t-233 -47q-139 0 -235.5 55t-152.5 153q-125 -209 -415 -208q-111 0 -198 40.5t-134 111.5 t-47 157zM260 303q0 -84 68.5 -129t163.5 -45q135 0 221 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5zM987 608h553v8q0 145 -70.5 233.5t-205.5 88.5q-113 0 -193 -82t-84 -240v-8z" />
<glyph unicode="&#xe7;" d="M106 532l3 82q6 223 128.5 347t342.5 124q147 0 248.5 -52t151.5 -130t54 -155q2 -18 -13 -33t-34 -15h-90q-20 0 -30.5 9.5t-20.5 33.5q-37 100 -101.5 142.5t-162.5 42.5q-129 0 -205 -80t-82 -244l-2 -72l2 -71q6 -164 82 -244t205 -80q100 0 163.5 42t100.5 143 q10 25 20.5 34t30.5 9h90q18 0 33.5 -14.5t13.5 -33.5q-4 -76 -53 -153.5t-150.5 -130.5t-246.5 -53l-47 -95q27 16 75 17q70 0 114 -48.5t44 -123.5q0 -78 -49 -129.5t-141 -51.5q-70 0 -124.5 30t-54.5 59q0 10 9 16l30 29q8 8 19 8q8 0 28.5 -12.5t39 -20.5t48.5 -8 q35 0 59.5 21.5t24.5 56.5q0 33 -23.5 52t-60.5 19q-25 0 -53 -9t-39 -9q-18 0 -28 12l-35 37q-6 8 -6 18t14 43l43 99q-170 29 -264 148.5t-98 312.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1144" d="M102 535l2 67q12 221 137.5 352t333.5 131q225 0 349.5 -143t124.5 -389v-37q0 -20 -13.5 -33.5t-34.5 -13.5h-710v-18q6 -133 82 -227.5t200 -94.5q96 0 157 38t91 79q18 25 28.5 30t35.5 5h90q18 0 31.5 -10.5t13.5 -26.5q0 -45 -55.5 -108.5t-156.5 -109.5t-233 -46 q-208 0 -333 130t-138 355zM256 1460q0 39 39 39h133q29 0 43.5 -7t32.5 -30l154 -184q10 -10 10 -27q0 -33 -33 -32h-65q-23 0 -36.5 4t-29.5 18l-238 195q-10 10 -10 24zM291 608h571v6q0 143 -77.5 233.5t-209.5 90.5q-133 0 -208.5 -90t-75.5 -234v-6z" />
<glyph unicode="&#xe9;" horiz-adv-x="1144" d="M102 535l2 67q12 221 137.5 352t333.5 131q225 0 349.5 -143t124.5 -389v-37q0 -20 -13.5 -33.5t-34.5 -13.5h-710v-18q6 -133 82 -227.5t200 -94.5q96 0 157 38t91 79q18 25 28.5 30t35.5 5h90q18 0 31.5 -10.5t13.5 -26.5q0 -45 -55.5 -108.5t-156.5 -109.5t-233 -46 q-208 0 -333 130t-138 355zM291 608h571v6q0 143 -77.5 233.5t-209.5 90.5q-133 0 -208.5 -90t-75.5 -234v-6zM489 1251q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32z" />
<glyph unicode="&#xea;" horiz-adv-x="1144" d="M102 535l2 67q12 221 137.5 352t333.5 131q225 0 349.5 -143t124.5 -389v-37q0 -20 -13.5 -33.5t-34.5 -13.5h-710v-18q6 -133 82 -227.5t200 -94.5q96 0 157 38t91 79q18 25 28.5 30t35.5 5h90q18 0 31.5 -10.5t13.5 -26.5q0 -45 -55.5 -108.5t-156.5 -109.5t-233 -46 q-208 0 -333 130t-138 355zM270 1243q0 18 23 39l186 186q20 20 33.5 25.5t34.5 5.5h57q20 0 33.5 -5t34.5 -26l186 -186q23 -23 23 -39q0 -25 -31 -24h-51q-35 0 -56 14l-168 117l-168 -117q-25 -14 -57 -14h-51q-29 -1 -29 24zM291 608h571v6q0 143 -77.5 233.5 t-209.5 90.5q-133 0 -208.5 -90t-75.5 -234v-6z" />
<glyph unicode="&#xeb;" horiz-adv-x="1144" d="M102 535l2 67q12 221 137.5 352t333.5 131q225 0 349.5 -143t124.5 -389v-37q0 -20 -13.5 -33.5t-34.5 -13.5h-710v-18q6 -133 82 -227.5t200 -94.5q96 0 157 38t91 79q18 25 28.5 30t35.5 5h90q18 0 31.5 -10.5t13.5 -26.5q0 -45 -55.5 -108.5t-156.5 -109.5t-233 -46 q-208 0 -333 130t-138 355zM291 608h571v6q0 143 -77.5 233.5t-209.5 90.5q-133 0 -208.5 -90t-75.5 -234v-6zM305 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM647 1319v106 q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#xec;" horiz-adv-x="491" d="M-76 1460q0 39 39 39h133q29 0 43.5 -7t32.5 -30l154 -184q10 -10 10 -27q0 -33 -33 -32h-65q-23 0 -36.5 4t-29.5 18l-238 195q-10 10 -10 24zM154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5 t-13 33.5z" />
<glyph unicode="&#xed;" horiz-adv-x="491" d="M154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5zM157 1251q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63 q-33 -1 -33 32z" />
<glyph unicode="&#xee;" horiz-adv-x="491" d="M-60 1243q0 18 23 39l186 186q20 20 33.5 25.5t34.5 5.5h57q20 0 33.5 -5t34.5 -26l186 -186q23 -23 23 -39q0 -25 -31 -24h-51q-35 0 -56 14l-168 117l-168 -117q-25 -14 -57 -14h-51q-29 -1 -29 24zM154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5 v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#xef;" horiz-adv-x="491" d="M-27 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5 t-13 33.5zM315 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#xf0;" horiz-adv-x="1157" d="M100 473q0 221 126 355.5t347 134.5q133 0 232 -66q-45 129 -123 225l-203 -78q-14 -6 -26 -6q-18 0 -28.5 9.5t-10.5 27.5v43q0 23 9 33t36 20l131 52q-57 53 -146 125q-33 25 -32 45q0 18 11 29.5t30 11.5h98q47 0 80 -27l37 -33q49 -43 92 -86l188 72q16 6 27 6 q18 0 28.5 -9t10.5 -28v-43q0 -23 -9.5 -33t-35.5 -20l-119 -47q94 -119 143 -284t54 -408v-27q-2 -221 -126 -354t-348 -133q-223 0 -348 136t-125 357zM289 455q4 -143 76.5 -232.5t207.5 -89.5q138 0 210.5 91t74.5 235q2 10 0 24q0 143 -72.5 234.5t-212.5 91.5 q-137 0 -210.5 -91t-73.5 -235v-28z" />
<glyph unicode="&#xf1;" horiz-adv-x="1236" d="M154 47v971q0 20 13 33.5t34 13.5h88q23 0 35 -12.5t12 -34.5v-90q61 76 142 116.5t202 40.5q197 0 305.5 -125.5t108.5 -332.5v-580q0 -20 -13.5 -33.5t-33.5 -13.5h-95q-20 0 -33.5 13.5t-13.5 33.5v567q0 147 -71.5 229.5t-204.5 82.5q-131 0 -209 -83t-78 -229v-567 q0 -20 -13.5 -33.5t-33.5 -13.5h-94q-21 0 -34 13.5t-13 33.5zM350 1251q0 68 46 123.5t122 55.5q37 0 61.5 -10.5t59.5 -30.5q4 -2 26.5 -15.5t47.5 -13.5q18 0 27.5 8t19.5 25q10 16 18 22t23 6h61q16 0 25.5 -10t9.5 -22q0 -68 -47 -124.5t-123 -56.5q-37 0 -61.5 10.5 t-59.5 30.5q-18 10 -36.5 19.5t-37.5 9.5q-16 0 -26 -8t-21 -23q-16 -29 -39 -28h-63q-14 0 -23.5 10t-9.5 22z" />
<glyph unicode="&#xf2;" horiz-adv-x="1171" d="M106 532l3 89q8 211 132 337.5t345 126.5t345 -127t132 -337q4 -45 4 -89q0 -43 -4 -88q-8 -211 -130 -337.5t-347 -126.5q-226 0 -347.5 126.5t-129.5 337.5zM266 1460q0 39 39 39h133q29 0 43.5 -7t32.5 -30l154 -184q10 -10 10 -27q0 -33 -33 -32h-65q-23 0 -36.5 4 t-29.5 18l-238 195q-10 10 -10 24zM293 532l2 -77q6 -156 83 -239t208 -83t207.5 83t83.5 239q2 20 2 77t-2 78q-6 156 -83 239t-208 83t-208 -83t-83 -239z" />
<glyph unicode="&#xf3;" horiz-adv-x="1171" d="M106 532l3 89q8 211 132 337.5t345 126.5t345 -127t132 -337q4 -45 4 -89q0 -43 -4 -88q-8 -211 -130 -337.5t-347 -126.5q-226 0 -347.5 126.5t-129.5 337.5zM293 532l2 -77q6 -156 83 -239t208 -83t207.5 83t83.5 239q2 20 2 77t-2 78q-6 156 -83 239t-208 83t-208 -83 t-83 -239zM495 1251q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32z" />
<glyph unicode="&#xf4;" horiz-adv-x="1171" d="M106 532l3 89q8 211 132 337.5t345 126.5t345 -127t132 -337q4 -45 4 -89q0 -43 -4 -88q-8 -211 -130 -337.5t-347 -126.5q-226 0 -347.5 126.5t-129.5 337.5zM280 1243q0 18 23 39l186 186q20 20 33.5 25.5t34.5 5.5h57q20 0 33.5 -5t34.5 -26l186 -186q23 -23 23 -39 q0 -25 -31 -24h-51q-35 0 -56 14l-168 117l-168 -117q-25 -14 -57 -14h-51q-29 -1 -29 24zM293 532l2 -77q6 -156 83 -239t208 -83t207.5 83t83.5 239q2 20 2 77t-2 78q-6 156 -83 239t-208 83t-208 -83t-83 -239z" />
<glyph unicode="&#xf5;" horiz-adv-x="1171" d="M106 532l3 89q8 211 132 337.5t345 126.5t345 -127t132 -337q4 -45 4 -89q0 -43 -4 -88q-8 -211 -130 -337.5t-347 -126.5q-226 0 -347.5 126.5t-129.5 337.5zM293 532l2 -77q6 -156 83 -239t208 -83t207.5 83t83.5 239q2 20 2 77t-2 78q-6 156 -83 239t-208 83t-208 -83 t-83 -239zM313 1251q0 68 46 123.5t122 55.5q37 0 61.5 -10.5t59.5 -30.5q4 -2 26.5 -15.5t47.5 -13.5q18 0 27.5 8t19.5 25q10 16 18 22t23 6h61q16 0 25.5 -10t9.5 -22q0 -68 -47 -124.5t-123 -56.5q-37 0 -61.5 10.5t-59.5 30.5q-18 10 -36.5 19.5t-37.5 9.5 q-16 0 -26 -8t-21 -23q-16 -29 -39 -28h-63q-14 0 -23.5 10t-9.5 22z" />
<glyph unicode="&#xf6;" horiz-adv-x="1171" d="M106 532l3 89q8 211 132 337.5t345 126.5t345 -127t132 -337q4 -45 4 -89q0 -43 -4 -88q-8 -211 -130 -337.5t-347 -126.5q-226 0 -347.5 126.5t-129.5 337.5zM293 532l2 -77q6 -156 83 -239t208 -83t207.5 83t83.5 239q2 20 2 77t-2 78q-6 156 -83 239t-208 83t-208 -83 t-83 -239zM315 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM657 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109 q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#xf7;" horiz-adv-x="1140" d="M135 584v63q0 23 13.5 35t33.5 12h779q20 0 32.5 -13t12.5 -34v-63q0 -20 -12.5 -32.5t-32.5 -12.5h-779q-20 0 -33.5 12t-13.5 33zM461 193v120q0 20 13 33.5t34 13.5h123q20 0 34.5 -13t14.5 -34v-120q0 -20 -13.5 -34t-35.5 -14h-123q-21 0 -34 13.5t-13 34.5z M461 920v120q0 23 13 36.5t34 13.5h123q23 0 36 -13.5t13 -36.5v-120q0 -20 -14.5 -34t-34.5 -14h-123q-21 0 -34 13.5t-13 34.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1241" d="M29 283v55q0 25 9 35t36 20l71 27q-4 39 -4 112q0 59 2 89q6 197 124 330.5t354 133.5q184 0 294.5 -83.5t153.5 -223.5l78 31q16 6 27 6q18 0 28 -9t10 -28v-57q0 -23 -9 -33t-36 -20l-69 -27q4 -61 4 -117l-2 -80q-6 -199 -123 -331.5t-356 -132.5q-185 0 -295.5 82.5 t-155.5 218.5l-76 -29q-14 -6 -26 -6q-18 0 -28.5 9t-10.5 28zM330 561l2 -69l567 221q-25 113 -99.5 166t-178.5 53q-123 0 -203 -76t-86 -233zM342 348q27 -111 101.5 -163t177.5 -52q127 0 205.5 76t84.5 237q2 14 2 58q0 47 -2 65z" />
<glyph unicode="&#xf9;" horiz-adv-x="1228" d="M143 440v578q0 20 13.5 33.5t33.5 13.5h95q20 0 33.5 -13.5t13.5 -33.5v-567q0 -311 270 -312q131 0 208 83t77 229v567q0 20 13 33.5t34 13.5h94q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v92q-59 -80 -139 -119.5 t-203 -39.5q-195 0 -301.5 125.5t-106.5 334.5zM289 1460q0 39 39 39h133q29 0 43.5 -7t32.5 -30l154 -184q10 -10 10 -27q0 -33 -33 -32h-65q-23 0 -36.5 4t-29.5 18l-238 195q-10 10 -10 24z" />
<glyph unicode="&#xfa;" horiz-adv-x="1228" d="M143 440v578q0 20 13.5 33.5t33.5 13.5h95q20 0 33.5 -13.5t13.5 -33.5v-567q0 -311 270 -312q131 0 208 83t77 229v567q0 20 13 33.5t34 13.5h94q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v92q-59 -80 -139 -119.5 t-203 -39.5q-195 0 -301.5 125.5t-106.5 334.5zM518 1251q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32z" />
<glyph unicode="&#xfb;" horiz-adv-x="1228" d="M143 440v578q0 20 13.5 33.5t33.5 13.5h95q20 0 33.5 -13.5t13.5 -33.5v-567q0 -311 270 -312q131 0 208 83t77 229v567q0 20 13 33.5t34 13.5h94q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v92q-59 -80 -139 -119.5 t-203 -39.5q-195 0 -301.5 125.5t-106.5 334.5zM305 1243q0 18 23 39l186 186q20 20 33.5 25.5t34.5 5.5h57q20 0 33.5 -5t34.5 -26l186 -186q23 -23 23 -39q0 -25 -31 -24h-51q-35 0 -56 14l-168 117l-168 -117q-25 -14 -57 -14h-51q-29 -1 -29 24z" />
<glyph unicode="&#xfc;" horiz-adv-x="1228" d="M143 440v578q0 20 13.5 33.5t33.5 13.5h95q20 0 33.5 -13.5t13.5 -33.5v-567q0 -311 270 -312q131 0 208 83t77 229v567q0 20 13 33.5t34 13.5h94q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v92q-59 -80 -139 -119.5 t-203 -39.5q-195 0 -301.5 125.5t-106.5 334.5zM338 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM680 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106 q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#xfd;" horiz-adv-x="1124" d="M63 1022q2 18 14.5 30.5t28.5 12.5h95q33 0 49 -33l315 -753l324 753q16 33 49 33h92q16 0 28.5 -12.5t12.5 -28.5q0 -14 -10 -37l-580 -1343q-10 -16 -21 -24.5t-30 -8.5h-90q-18 0 -30.5 12t-12.5 29q0 6 10 37l160 374l-391 924q-12 25 -13 35zM477 1251q0 16 10 27 l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32z" />
<glyph unicode="&#xfe;" horiz-adv-x="1218" d="M156 -342v1749q0 20 13 33.5t34 13.5h92q20 0 33.5 -13.5t13.5 -33.5v-475q113 154 332 153q215 0 322.5 -136t115.5 -347q2 -23 2 -70t-2 -69q-8 -209 -115.5 -346t-322.5 -137q-219 0 -332 155v-477q0 -20 -13.5 -33.5t-33.5 -13.5h-92q-21 0 -34 13.5t-13 33.5z M340 526l2 -80q4 -125 79 -216t214 -91q147 0 215 92.5t74 241.5q2 20 2 59q0 393 -291 394q-143 0 -215 -94.5t-78 -225.5z" />
<glyph unicode="&#xff;" horiz-adv-x="1124" d="M63 1022q2 18 14.5 30.5t28.5 12.5h95q33 0 49 -33l315 -753l324 753q16 33 49 33h92q16 0 28.5 -12.5t12.5 -28.5q0 -14 -10 -37l-580 -1343q-10 -16 -21 -24.5t-30 -8.5h-90q-18 0 -30.5 12t-12.5 29q0 6 10 37l160 374l-391 924q-12 25 -13 35zM295 1319v106 q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM637 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x100;" horiz-adv-x="1368" d="M41 41l4 22l508 1326q16 45 65 45h132q49 0 65 -45l508 -1326l4 -22q0 -16 -13.5 -28.5t-29.5 -12.5h-96q-20 0 -33.5 11.5t-17.5 23.5l-115 295h-678l-113 -295q-16 -35 -51 -35h-98q-16 0 -28.5 12.5t-12.5 28.5zM403 500h562l-281 735zM404 1581v74q0 20 12 32.5 t33 12.5h469q20 0 32.5 -12.5t12.5 -32.5v-74q0 -20 -12.5 -32.5t-32.5 -12.5h-469q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x101;" d="M78 289q0 135 110.5 221t300.5 115l308 43v59q0 211 -244 211q-94 0 -151.5 -38t-84.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 44 109.5t136 108.5t225 46q229 0 328.5 -106.5t99.5 -265.5v-666q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -32.5 13.5t-12.5 33.5v90q-45 -68 -131 -112.5t-221 -44.5q-101 0 -186 40.5t-135 111.5t-50 157zM258 303q0 -84 68.5 -129t162.5 -45q136 0 222 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5zM280 1311v59q0 20 12.5 32.5t32.5 12.5h451q20 0 32.5 -12 t12.5 -33v-59q0 -20 -12.5 -32.5t-32.5 -12.5h-451q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x102;" horiz-adv-x="1368" d="M41 41l4 22l508 1326q16 45 65 45h132q49 0 65 -45l508 -1326l4 -22q0 -16 -13.5 -28.5t-29.5 -12.5h-96q-20 0 -33.5 11.5t-17.5 23.5l-115 295h-678l-113 -295q-16 -35 -51 -35h-98q-16 0 -28.5 12.5t-12.5 28.5zM403 500h562l-281 735zM428 1769q0 16 10 27.5t29 11.5 h59q18 0 28.5 -11t10.5 -28q0 -57 26.5 -95t92.5 -38t92.5 38t26.5 95q0 16 10 27.5t28 11.5h60q18 0 28.5 -11t10.5 -28q0 -111 -59.5 -181t-196.5 -70t-196.5 70.5t-59.5 180.5z" />
<glyph unicode="&#x103;" d="M78 289q0 135 110.5 221t300.5 115l308 43v59q0 211 -244 211q-94 0 -151.5 -38t-84.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 44 109.5t136 108.5t225 46q229 0 328.5 -106.5t99.5 -265.5v-666q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -32.5 13.5t-12.5 33.5v90q-45 -68 -131 -112.5t-221 -44.5q-101 0 -186 40.5t-135 111.5t-50 157zM258 303q0 -84 68.5 -129t162.5 -45q136 0 222 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5zM305 1460q0 16 10.5 27.5t28.5 11.5h58q18 0 28.5 -11 t10.5 -28q0 -131 110 -131q111 0 111 131q0 16 10 27.5t29 11.5h59q18 0 28.5 -11t10.5 -28q0 -109 -58.5 -180.5t-189.5 -71.5q-129 0 -187.5 72t-58.5 180z" />
<glyph unicode="&#x104;" horiz-adv-x="1368" d="M41 41l4 22l508 1326q16 45 65 45h132q49 0 65 -45l508 -1326l4 -22q0 -16 -13.5 -28.5t-29.5 -12.5h-59q-66 0 -106 -44t-40 -114t41 -111.5t107 -41.5h28q20 0 34 -13.5t14 -33.5v-45q0 -20 -13.5 -34t-34.5 -14h-36q-134 0 -209.5 80t-75.5 213q0 102 49 169t135 83 q-10 8 -14 23l-82 213h-678l-113 -295q-16 -35 -51 -35h-98q-16 0 -28.5 12.5t-12.5 28.5zM403 500h562l-281 735z" />
<glyph unicode="&#x105;" d="M78 289q0 135 110.5 221t300.5 115l308 43v59q0 211 -244 211q-94 0 -151.5 -38t-84.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 44 109.5t136 108.5t225 46q229 0 328.5 -106.5t99.5 -265.5v-666q0 -29 -35 -45l-8 -2q-68 -27 -98.5 -68 t-30.5 -102q0 -70 28.5 -105.5t84.5 -35.5h12q20 0 33.5 -13.5t13.5 -33.5v-45q0 -20 -13.5 -34t-33.5 -14h-21q-110 0 -179.5 75t-69.5 206q0 80 43 140.5t98 88.5q-6 6 -6 15v63q-45 -68 -131 -112.5t-221 -44.5q-100 0 -185.5 40.5t-135.5 111.5t-50 157zM258 303 q0 -84 68.5 -129t162.5 -45q136 0 222 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5z" />
<glyph unicode="&#x106;" horiz-adv-x="1361" d="M123 715q0 127 2 184q10 258 152.5 406.5t414.5 148.5q182 0 309 -64.5t191.5 -169t71.5 -223.5q0 -18 -13.5 -29.5t-31.5 -11.5h-103q-20 0 -32.5 10.5t-18.5 34.5q-35 158 -126 221.5t-247 63.5q-356 0 -370 -397q-2 -55 -3 -170q0 -115 3 -174q14 -395 370 -395 q154 0 246 63t127 219q6 25 18.5 35t32.5 10h103q18 0 31.5 -11t13.5 -30q-6 -119 -71 -223t-192 -168.5t-309 -64.5q-272 0 -414.5 148t-152.5 407q-2 55 -2 180zM598 1569q0 16 10 26l152 185q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194 q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#x107;" d="M106 532l3 82q6 223 128.5 347t342.5 124q147 0 248.5 -52t151.5 -130t54 -155q2 -18 -13 -33t-34 -15h-90q-20 0 -30.5 9.5t-20.5 33.5q-37 100 -101.5 142.5t-162.5 42.5q-129 0 -205 -80t-82 -244l-2 -72l2 -71q6 -164 82 -244t205 -80q100 0 163.5 42t100.5 143 q10 25 20.5 34t30.5 9h90q18 0 33.5 -14.5t13.5 -33.5q-4 -76 -54 -153.5t-151.5 -130.5t-248.5 -53q-220 0 -342.5 123.5t-128.5 347.5zM475 1251q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63 q-33 -1 -33 32z" />
<glyph unicode="&#x108;" horiz-adv-x="1361" d="M123 715q0 127 2 184q10 258 152.5 406.5t414.5 148.5q182 0 309 -64.5t191.5 -169t71.5 -223.5q0 -18 -13.5 -29.5t-31.5 -11.5h-103q-20 0 -32.5 10.5t-18.5 34.5q-35 158 -126 221.5t-247 63.5q-356 0 -370 -397q-2 -55 -3 -170q0 -115 3 -174q14 -395 370 -395 q154 0 246 63t127 219q6 25 18.5 35t32.5 10h103q18 0 31.5 -11t13.5 -30q-6 -119 -71 -223t-192 -168.5t-309 -64.5q-272 0 -414.5 148t-152.5 407q-2 55 -2 180zM374 1561q0 18 23 38l186 187q20 20 33.5 25.5t34.5 5.5h61q18 0 32.5 -5.5t33.5 -25.5l188 -187 q20 -20 21 -38q0 -25 -29 -25h-53q-35 0 -55 14l-168 117l-168 -117q-25 -14 -58 -14h-53q-29 0 -29 25z" />
<glyph unicode="&#x109;" d="M106 532l3 82q6 223 128.5 347t342.5 124q147 0 248.5 -52t151.5 -130t54 -155q2 -18 -13 -33t-34 -15h-90q-20 0 -30.5 9.5t-20.5 33.5q-37 100 -101.5 142.5t-162.5 42.5q-129 0 -205 -80t-82 -244l-2 -72l2 -71q6 -164 82 -244t205 -80q100 0 163.5 42t100.5 143 q10 25 20.5 34t30.5 9h90q18 0 33.5 -14.5t13.5 -33.5q-4 -76 -54 -153.5t-151.5 -130.5t-248.5 -53q-220 0 -342.5 123.5t-128.5 347.5zM266 1259q0 18 23 39l186 186q20 20 33.5 25.5t34.5 5.5h57q20 0 33.5 -5t34.5 -26l186 -186q23 -23 23 -39q0 -25 -31 -24h-51 q-35 0 -56 14l-168 117l-168 -117q-25 -14 -57 -14h-51q-29 -1 -29 24z" />
<glyph unicode="&#x10a;" horiz-adv-x="1361" d="M123 715q0 127 2 184q10 258 152.5 406.5t414.5 148.5q182 0 309 -64.5t191.5 -169t71.5 -223.5q0 -18 -13.5 -29.5t-31.5 -11.5h-103q-20 0 -32.5 10.5t-18.5 34.5q-35 158 -126 221.5t-247 63.5q-356 0 -370 -397q-2 -55 -3 -170q0 -115 3 -174q14 -395 370 -395 q154 0 246 63t127 219q6 25 18.5 35t32.5 10h103q18 0 31.5 -11t13.5 -30q-6 -119 -71 -223t-192 -168.5t-309 -64.5q-272 0 -414.5 148t-152.5 407q-2 55 -2 180zM572 1581v129q0 20 12 33.5t33 13.5h129q20 0 33.5 -13t13.5 -34v-129q0 -20 -13.5 -32.5t-33.5 -12.5h-129 q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x10b;" d="M106 532l3 82q6 223 128.5 347t342.5 124q147 0 248.5 -52t151.5 -130t54 -155q2 -18 -13 -33t-34 -15h-90q-20 0 -30.5 9.5t-20.5 33.5q-37 100 -101.5 142.5t-162.5 42.5q-129 0 -205 -80t-82 -244l-2 -72l2 -71q6 -164 82 -244t205 -80q100 0 163.5 42t100.5 143 q10 25 20.5 34t30.5 9h90q18 0 33.5 -14.5t13.5 -33.5q-4 -76 -54 -153.5t-151.5 -130.5t-248.5 -53q-220 0 -342.5 123.5t-128.5 347.5zM463 1303v124q0 20 12.5 34t32.5 14h127q20 0 33.5 -13.5t13.5 -34.5v-124q0 -20 -13 -34t-34 -14h-127q-20 0 -32.5 13.5t-12.5 34.5z " />
<glyph unicode="&#x10c;" horiz-adv-x="1361" d="M123 715q0 127 2 184q10 258 152.5 406.5t414.5 148.5q182 0 309 -64.5t191.5 -169t71.5 -223.5q0 -18 -13.5 -29.5t-31.5 -11.5h-103q-20 0 -32.5 10.5t-18.5 34.5q-35 158 -126 221.5t-247 63.5q-356 0 -370 -397q-2 -55 -3 -170q0 -115 3 -174q14 -395 370 -395 q154 0 246 63t127 219q6 25 18.5 35t32.5 10h103q18 0 31.5 -11t13.5 -30q-6 -119 -71 -223t-192 -168.5t-309 -64.5q-272 0 -414.5 148t-152.5 407q-2 55 -2 180zM374 1792q0 25 29 25h53q33 0 58 -15l168 -116l168 116q20 14 55 15h53q29 0 29 -25q0 -18 -21 -39 l-188 -186q-18 -20 -32.5 -25.5t-33.5 -5.5h-61q-21 0 -34 5t-34 26l-186 186q-23 20 -23 39z" />
<glyph unicode="&#x10d;" d="M106 532l3 82q6 223 128.5 347t342.5 124q147 0 248.5 -52t151.5 -130t54 -155q2 -18 -13 -33t-34 -15h-90q-20 0 -30.5 9.5t-20.5 33.5q-37 100 -101.5 142.5t-162.5 42.5q-129 0 -205 -80t-82 -244l-2 -72l2 -71q6 -164 82 -244t205 -80q100 0 163.5 42t100.5 143 q10 25 20.5 34t30.5 9h90q18 0 33.5 -14.5t13.5 -33.5q-4 -76 -54 -153.5t-151.5 -130.5t-248.5 -53q-220 0 -342.5 123.5t-128.5 347.5zM266 1475q0 25 29 24h51q33 0 57 -14l168 -117l168 117q20 14 56 14h51q31 0 31 -24q0 -16 -23 -39l-186 -187q-20 -20 -33.5 -25 t-34.5 -5h-57q-21 0 -34 5t-34 25l-186 187q-23 20 -23 39z" />
<glyph unicode="&#x10e;" horiz-adv-x="1409" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h463q309 0 446.5 -138.5t141.5 -416.5q2 -59 2 -160q0 -100 -2 -162q-4 -195 -62.5 -316.5t-183.5 -181t-332 -59.5h-473q-20 0 -32.5 13.5t-12.5 33.5zM373 170h315q143 0 228 40t123 125t42 230q4 123 4 154q0 33 -4 151 q-4 205 -97 300.5t-306 95.5h-305v-1096zM381 1792q0 25 29 25h53q33 0 58 -15l168 -116l168 116q20 14 55 15h53q29 0 29 -25q0 -18 -21 -39l-188 -186q-18 -20 -32.5 -25.5t-33.5 -5.5h-61q-21 0 -34 5t-34 26l-186 186q-23 20 -23 39z" />
<glyph unicode="&#x10f;" horiz-adv-x="1216" d="M102 532l2 62q8 211 117 351t322 140t331 -151v473q0 20 13.5 33.5t34.5 13.5h92q20 0 33.5 -13.5t13.5 -33.5v-1360q0 -20 -13.5 -33.5t-33.5 -13.5h-88q-20 0 -32.5 13.5t-12.5 33.5v88q-117 -156 -338 -155q-215 0 -322.5 139t-116.5 352zM291 532q0 -393 291 -393 q143 0 214.5 95.5t77.5 226.5q2 23 3 78q0 57 -3 79q-4 125 -78.5 216.5t-213.5 91.5q-147 0 -215 -92.5t-74 -241.5zM1172 1155l37 239q8 59 57 60h125q14 0 23.5 -10.5t9.5 -24.5q0 -16 -8 -33l-86 -227q-10 -25 -22.5 -36t-37.5 -11h-67q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x110;" horiz-adv-x="1427" d="M31 690v62q0 23 13 35t34 12h121v585q0 23 12 36.5t33 13.5h463q309 0 446 -138.5t141 -416.5q2 -59 2 -160q0 -100 -2 -162q-4 -195 -62 -316.5t-184 -181t-333 -59.5h-471q-21 0 -33 13.5t-12 33.5v596h-121q-20 0 -33.5 13.5t-13.5 33.5zM391 170h316q143 0 228 40 t123 125t42 230q4 123 4 154q0 33 -4 151q-4 205 -96.5 300.5t-305.5 95.5h-307v-467h275q20 0 33.5 -13.5t13.5 -33.5v-62q0 -20 -13.5 -33.5t-33.5 -13.5h-275v-473z" />
<glyph unicode="&#x111;" horiz-adv-x="1216" d="M104 532l2 62q8 203 107.5 347t331.5 144q213 0 332 -151v227h-109q-20 0 -33.5 13.5t-13.5 33.5v41q0 23 13.5 35t33.5 12h109v111q0 23 13 35t34 12h92q20 0 33.5 -13.5t13.5 -33.5v-111h108q20 0 34 -13t14 -34v-41q0 -20 -13.5 -33.5t-34.5 -13.5h-108v-1114 q0 -20 -13.5 -33.5t-33.5 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v90q-115 -158 -336 -157q-232 0 -331 144t-108 347zM293 532l2 -59q6 -147 73.5 -240.5t215.5 -93.5q143 0 214.5 95.5t78.5 226.5q2 23 2 78q0 57 -2 79q-4 125 -79 216.5t-214 91.5q-147 0 -215 -93.5 t-74 -240.5z" />
<glyph unicode="&#x112;" horiz-adv-x="1239" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h842q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-698v-457h653q23 0 36 -13.5t13 -35.5v-70q0 -20 -13.5 -33.5t-35.5 -13.5h-653v-473h714q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-858 q-20 0 -32.5 13.5t-12.5 33.5zM338 1581v74q0 20 12 32.5t33 12.5h469q20 0 32.5 -12.5t12.5 -32.5v-74q0 -20 -12.5 -32.5t-32.5 -12.5h-469q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x113;" horiz-adv-x="1144" d="M102 535l2 67q12 221 137.5 352t333.5 131q225 0 349.5 -143t124.5 -389v-37q0 -20 -13.5 -33.5t-34.5 -13.5h-710v-18q6 -133 82 -227.5t200 -94.5q96 0 157 38t91 79q18 25 28.5 30t35.5 5h90q18 0 31.5 -10.5t13.5 -26.5q0 -45 -55.5 -108.5t-156.5 -109.5t-233 -46 q-208 0 -333 130t-138 355zM291 608h571v6q0 143 -77.5 233.5t-209.5 90.5q-133 0 -208.5 -90t-75.5 -234v-6zM305 1311v59q0 20 12.5 32.5t32.5 12.5h451q20 0 32.5 -12t12.5 -33v-59q0 -20 -12.5 -32.5t-32.5 -12.5h-451q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x114;" horiz-adv-x="1239" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h842q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-698v-457h653q23 0 36 -13.5t13 -35.5v-70q0 -20 -13.5 -33.5t-35.5 -13.5h-653v-473h714q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-858 q-20 0 -32.5 13.5t-12.5 33.5zM363 1769q0 16 10 27.5t29 11.5h59q18 0 28.5 -11t10.5 -28q0 -57 26.5 -95t92.5 -38t92.5 38t26.5 95q0 16 10 27.5t28 11.5h60q18 0 28.5 -11t10.5 -28q0 -111 -59.5 -181t-196.5 -70t-196.5 70.5t-59.5 180.5z" />
<glyph unicode="&#x115;" horiz-adv-x="1144" d="M102 535l2 67q12 221 137.5 352t333.5 131q225 0 349.5 -143t124.5 -389v-37q0 -20 -13.5 -33.5t-34.5 -13.5h-710v-18q6 -133 82 -227.5t200 -94.5q96 0 157 38t91 79q18 25 28.5 30t35.5 5h90q18 0 31.5 -10.5t13.5 -26.5q0 -45 -55.5 -108.5t-156.5 -109.5t-233 -46 q-208 0 -333 130t-138 355zM291 608h571v6q0 143 -77.5 233.5t-209.5 90.5q-133 0 -208.5 -90t-75.5 -234v-6zM327 1460q0 16 10.5 27.5t28.5 11.5h58q18 0 28.5 -11t10.5 -28q0 -131 110 -131q111 0 111 131q0 16 10 27.5t29 11.5h59q18 0 28.5 -11t10.5 -28 q0 -109 -58.5 -180.5t-189.5 -71.5q-129 0 -187.5 72t-58.5 180z" />
<glyph unicode="&#x116;" horiz-adv-x="1239" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h842q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-698v-457h653q23 0 36 -13.5t13 -35.5v-70q0 -20 -13.5 -33.5t-35.5 -13.5h-653v-473h714q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-858 q-20 0 -32.5 13.5t-12.5 33.5zM508 1581v129q0 20 12 33.5t33 13.5h129q20 0 33.5 -13t13.5 -34v-129q0 -20 -13.5 -32.5t-33.5 -12.5h-129q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x117;" horiz-adv-x="1144" d="M102 535l2 67q12 221 137.5 352t333.5 131q225 0 349.5 -143t124.5 -389v-37q0 -20 -13.5 -33.5t-34.5 -13.5h-710v-18q6 -133 82 -227.5t200 -94.5q96 0 157 38t91 79q18 25 28.5 30t35.5 5h90q18 0 31.5 -10.5t13.5 -26.5q0 -45 -55.5 -108.5t-156.5 -109.5t-233 -46 q-208 0 -333 130t-138 355zM291 608h571v6q0 143 -77.5 233.5t-209.5 90.5q-133 0 -208.5 -90t-75.5 -234v-6zM467 1303v124q0 20 12.5 34t32.5 14h127q20 0 33.5 -13.5t13.5 -34.5v-124q0 -20 -13 -34t-34 -14h-127q-20 0 -32.5 13.5t-12.5 34.5z" />
<glyph unicode="&#x118;" horiz-adv-x="1239" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h842q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-698v-457h653q23 0 36 -13.5t13 -35.5v-70q0 -20 -13.5 -33.5t-35.5 -13.5h-653v-473h714q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-39 q-61 -4 -98 -46t-37 -112t40 -111.5t108 -41.5h28q20 0 34 -13.5t14 -33.5v-45q0 -20 -13.5 -34t-34.5 -14h-36q-134 0 -209.5 80t-75.5 213q0 92 37 158h-576q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x119;" horiz-adv-x="1146" d="M104 465v137q14 221 139.5 352t331.5 131q225 0 350.5 -143t125.5 -389v-37q0 -20 -14.5 -33.5t-35.5 -13.5h-708v-18q4 -133 81 -227.5t201 -94.5q96 0 157 38t91 79q18 25 27.5 30t34.5 5h92q18 0 30.5 -10.5t12.5 -26.5q0 -39 -42 -92.5t-121 -99.5t-181 -64 q-66 -12 -99.5 -47t-33.5 -99q0 -70 41 -111.5t106 -41.5h29q20 0 33.5 -13.5t13.5 -33.5v-45q0 -23 -12.5 -35.5t-34.5 -12.5h-37q-133 0 -209 80t-76 213q0 92 35 156q-145 39 -231 162t-97 305zM291 608h571v6q0 143 -77.5 233.5t-209.5 90.5q-133 0 -208.5 -90 t-75.5 -234v-6z" />
<glyph unicode="&#x11a;" horiz-adv-x="1239" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h842q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-698v-457h653q23 0 36 -13.5t13 -35.5v-70q0 -20 -13.5 -33.5t-35.5 -13.5h-653v-473h714q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-858 q-20 0 -32.5 13.5t-12.5 33.5zM311 1792q0 25 29 25h53q33 0 58 -15l168 -116l168 116q20 14 55 15h53q29 0 29 -25q0 -18 -21 -39l-188 -186q-18 -20 -32.5 -25.5t-33.5 -5.5h-61q-21 0 -34 5t-34 26l-186 186q-23 20 -23 39z" />
<glyph unicode="&#x11b;" horiz-adv-x="1144" d="M102 535l2 67q12 221 137.5 352t333.5 131q225 0 349.5 -143t124.5 -389v-37q0 -20 -13.5 -33.5t-34.5 -13.5h-710v-18q6 -133 82 -227.5t200 -94.5q96 0 157 38t91 79q18 25 28.5 30t35.5 5h90q18 0 31.5 -10.5t13.5 -26.5q0 -45 -55.5 -108.5t-156.5 -109.5t-233 -46 q-208 0 -333 130t-138 355zM270 1475q0 25 29 24h51q33 0 57 -14l168 -117l168 117q20 14 56 14h51q31 0 31 -24q0 -16 -23 -39l-186 -187q-20 -20 -33.5 -25t-34.5 -5h-57q-21 0 -34 5t-34 25l-186 187q-23 20 -23 39zM291 608h571v6q0 143 -77.5 233.5t-209.5 90.5 q-133 0 -208.5 -90t-75.5 -234v-6z" />
<glyph unicode="&#x11c;" horiz-adv-x="1390" d="M125 719q0 133 2 192q8 250 156.5 396.5t414.5 146.5q180 0 305 -65.5t188.5 -156.5t68.5 -169v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-115q-18 0 -27.5 8t-17.5 31q-33 96 -121 161.5t-238 65.5q-172 0 -270 -94t-104 -291q-2 -59 -2 -182q0 -121 2 -182q6 -199 104 -293 t270 -94q174 0 278.5 95t104.5 285v88h-313q-20 0 -33.5 14.5t-13.5 35.5v61q0 20 13.5 34.5t33.5 14.5h459q23 0 36 -13t13 -36v-194q0 -166 -68.5 -292t-199.5 -194.5t-310 -68.5q-266 0 -413.5 147t-157.5 399q-2 61 -2 193zM401 1561q0 18 23 38l186 187 q20 20 33.5 25.5t34.5 5.5h61q18 0 32.5 -5.5t33.5 -25.5l188 -187q20 -20 21 -38q0 -25 -29 -25h-53q-35 0 -55 14l-168 117l-168 -117q-25 -14 -58 -14h-53q-29 0 -29 25z" />
<glyph unicode="&#x11d;" horiz-adv-x="1218" d="M104 539l2 55q6 211 114 351t325 140q215 0 336 -157v88q0 20 13 34.5t34 14.5h88q20 0 33.5 -14.5t13.5 -34.5v-993q0 -473 -477 -474q-170 0 -272.5 57.5t-144.5 131.5t-42 127q0 18 14.5 31.5t32.5 13.5h90q18 0 30.5 -9.5t20.5 -33.5q57 -164 260 -164 q154 0 228 65.5t74 231.5v141q-119 -152 -332 -151q-217 0 -324.5 139t-114.5 352zM278 1243q0 18 23 39l186 186q20 20 33.5 25.5t34.5 5.5h57q20 0 33.5 -5t34.5 -26l186 -186q23 -23 23 -39q0 -25 -31 -24h-51q-35 0 -56 14l-168 117l-168 -117q-25 -14 -57 -14h-51 q-29 -1 -29 24zM293 539l2 -56q6 -147 72.5 -240t214.5 -93q143 0 215.5 93t79.5 224q2 18 2 72q0 51 -2 69q-6 131 -79 224.5t-216 93.5q-147 0 -214 -93.5t-73 -240.5z" />
<glyph unicode="&#x11e;" horiz-adv-x="1390" d="M125 719q0 133 2 192q8 250 156.5 396.5t414.5 146.5q180 0 305 -65.5t188.5 -156.5t68.5 -169v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-115q-18 0 -27.5 8t-17.5 31q-33 96 -121 161.5t-238 65.5q-172 0 -270 -94t-104 -291q-2 -59 -2 -182q0 -121 2 -182q6 -199 104 -293 t270 -94q174 0 278.5 95t104.5 285v88h-313q-20 0 -33.5 14.5t-13.5 35.5v61q0 20 13.5 34.5t33.5 14.5h459q23 0 36 -13t13 -36v-194q0 -166 -68.5 -292t-199.5 -194.5t-310 -68.5q-266 0 -413.5 147t-157.5 399q-2 61 -2 193zM451 1769q0 16 10 27.5t29 11.5h59 q18 0 28.5 -11t10.5 -28q0 -57 26.5 -95t92.5 -38t92.5 38t26.5 95q0 16 10 27.5t28 11.5h60q18 0 28.5 -11t10.5 -28q0 -111 -59.5 -181t-196.5 -70t-196.5 70.5t-59.5 180.5z" />
<glyph unicode="&#x11f;" horiz-adv-x="1218" d="M104 539l2 55q6 211 114 351t325 140q215 0 336 -157v88q0 20 13 34.5t34 14.5h88q20 0 33.5 -14.5t13.5 -34.5v-993q0 -473 -477 -474q-170 0 -272.5 57.5t-144.5 131.5t-42 127q0 18 14.5 31.5t32.5 13.5h90q18 0 30.5 -9.5t20.5 -33.5q57 -164 260 -164 q154 0 228 65.5t74 231.5v141q-119 -152 -332 -151q-217 0 -324.5 139t-114.5 352zM293 539l2 -56q6 -147 72.5 -240t214.5 -93q143 0 215.5 93t79.5 224q2 18 2 72q0 51 -2 69q-6 131 -79 224.5t-216 93.5q-147 0 -214 -93.5t-73 -240.5zM338 1460q0 16 10.5 27.5 t28.5 11.5h58q18 0 28.5 -11t10.5 -28q0 -131 110 -131q111 0 111 131q0 16 10 27.5t29 11.5h59q18 0 28.5 -11t10.5 -28q0 -109 -58.5 -180.5t-189.5 -71.5q-129 0 -187.5 72t-58.5 180z" />
<glyph unicode="&#x120;" horiz-adv-x="1390" d="M125 719q0 133 2 192q8 250 156.5 396.5t414.5 146.5q180 0 305 -65.5t188.5 -156.5t68.5 -169v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-115q-18 0 -27.5 8t-17.5 31q-33 96 -121 161.5t-238 65.5q-172 0 -270 -94t-104 -291q-2 -59 -2 -182q0 -121 2 -182q6 -199 104 -293 t270 -94q174 0 278.5 95t104.5 285v88h-313q-20 0 -33.5 14.5t-13.5 35.5v61q0 20 13.5 34.5t33.5 14.5h459q23 0 36 -13t13 -36v-194q0 -166 -68.5 -292t-199.5 -194.5t-310 -68.5q-266 0 -413.5 147t-157.5 399q-2 61 -2 193zM598 1581v129q0 20 12 33.5t33 13.5h129 q20 0 33.5 -13t13.5 -34v-129q0 -20 -13.5 -32.5t-33.5 -12.5h-129q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x121;" horiz-adv-x="1218" d="M104 539l2 55q6 211 114 351t325 140q215 0 336 -157v88q0 20 13 34.5t34 14.5h88q20 0 33.5 -14.5t13.5 -34.5v-993q0 -473 -477 -474q-170 0 -272.5 57.5t-144.5 131.5t-42 127q0 18 14.5 31.5t32.5 13.5h90q18 0 30.5 -9.5t20.5 -33.5q57 -164 260 -164 q154 0 228 65.5t74 231.5v141q-119 -152 -332 -151q-217 0 -324.5 139t-114.5 352zM293 539l2 -56q6 -147 72.5 -240t214.5 -93q143 0 215.5 93t79.5 224q2 18 2 72q0 51 -2 69q-6 131 -79 224.5t-216 93.5q-147 0 -214 -93.5t-73 -240.5zM475 1303v124q0 20 12.5 34 t32.5 14h127q20 0 33.5 -13.5t13.5 -34.5v-124q0 -20 -13 -34t-34 -14h-127q-20 0 -32.5 13.5t-12.5 34.5z" />
<glyph unicode="&#x122;" horiz-adv-x="1390" d="M125 719q0 133 2 192q8 250 156.5 396.5t414.5 146.5q180 0 305 -65.5t188.5 -156.5t68.5 -169v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-115q-18 0 -27.5 8t-17.5 31q-33 96 -121 161.5t-238 65.5q-172 0 -270 -94t-104 -291q-2 -59 -2 -182q0 -121 2 -182q6 -199 104 -293 t270 -94q174 0 278.5 95t104.5 285v88h-313q-20 0 -33.5 14.5t-13.5 35.5v61q0 20 13.5 34.5t33.5 14.5h459q23 0 36 -13t13 -36v-194q0 -166 -68.5 -292t-199.5 -194.5t-310 -68.5q-266 0 -413.5 147t-157.5 399q-2 61 -2 193zM565 -401l35 239q8 59 57 60h129 q14 0 23.5 -10.5t9.5 -24.5t-6 -33l-88 -227q-10 -25 -22.5 -36t-37.5 -11h-69q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x123;" horiz-adv-x="1218" d="M104 539l2 55q6 211 114 351t325 140q215 0 336 -157v88q0 20 13 34.5t34 14.5h88q20 0 33.5 -14.5t13.5 -34.5v-993q0 -473 -477 -474q-170 0 -272.5 57.5t-144.5 131.5t-42 127q0 18 14.5 31.5t32.5 13.5h90q18 0 30.5 -9.5t20.5 -33.5q57 -164 260 -164 q154 0 228 65.5t74 231.5v141q-119 -152 -332 -151q-217 0 -324.5 139t-114.5 352zM293 539l2 -56q6 -147 72.5 -240t214.5 -93q143 0 215.5 93t79.5 224q2 18 2 72q0 51 -2 69q-6 131 -79 224.5t-216 93.5q-147 0 -214 -93.5t-73 -240.5zM487 1253q0 16 9 33l86 227 q10 25 22 36.5t37 11.5h68q16 0 25 -12.5t5 -30.5l-37 -240q-8 -59 -57 -59h-125q-14 0 -23.5 10t-9.5 24z" />
<glyph unicode="&#x124;" horiz-adv-x="1458" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h99q23 0 36 -13.5t13 -36.5v-569h712v569q0 23 13.5 36.5t34.5 13.5h98q23 0 36 -13.5t13 -36.5v-1337q0 -20 -14.5 -33.5t-34.5 -13.5h-98q-20 0 -34 13.5t-14 33.5v590h-712v-590q0 -20 -14.5 -33.5t-34.5 -13.5h-99 q-20 0 -32.5 13.5t-12.5 33.5zM424 1561q0 18 23 38l186 187q20 20 33.5 25.5t34.5 5.5h61q18 0 32.5 -5.5t33.5 -25.5l188 -187q20 -20 21 -38q0 -25 -29 -25h-53q-35 0 -55 14l-168 117l-168 -117q-25 -14 -58 -14h-53q-29 0 -29 25z" />
<glyph unicode="&#x125;" horiz-adv-x="1243" d="M156 47v1360q0 23 13 35t34 12h94q23 0 35 -12t12 -35v-477q59 76 140 115.5t200 39.5q197 0 306.5 -125.5t109.5 -332.5v-580q0 -20 -13.5 -33.5t-33.5 -13.5h-97q-20 0 -33.5 13.5t-13.5 33.5v567q0 147 -71.5 229.5t-206.5 82.5q-131 0 -209 -83t-78 -229v-567 q0 -20 -13.5 -33.5t-33.5 -13.5h-94q-21 0 -34 13.5t-13 33.5zM321 1560q0 18 23 39l186 186q20 20 33.5 25.5t34.5 5.5h57q20 0 33.5 -5t34.5 -26l186 -186q23 -23 23 -39q0 -25 -31 -24h-51q-35 0 -56 14l-168 117l-168 -117q-25 -14 -57 -14h-51q-29 -1 -29 24z" />
<glyph unicode="&#x126;" horiz-adv-x="1458" d="M57 1100v47q0 23 13.5 35t33.5 12h76v190q0 23 12.5 36.5t32.5 13.5h99q23 0 36 -13.5t13 -36.5v-190h712v190q0 23 13.5 36.5t34.5 13.5h98q23 0 36 -13.5t13 -36.5v-190h92q21 0 33 -13.5t12 -33.5v-47q0 -20 -12 -33.5t-33 -13.5h-92v-1006q0 -20 -14.5 -33.5 t-34.5 -13.5h-98q-20 0 -34 13.5t-14 33.5v590h-712v-590q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5t-12.5 33.5v1006h-76q-20 0 -33.5 13t-13.5 34zM373 815h712v238h-712v-238z" />
<glyph unicode="&#x127;" horiz-adv-x="1241" d="M0 1204v43q0 20 13.5 32.5t33.5 12.5h107v115q0 20 13 33.5t34 13.5h96q20 0 33.5 -13.5t13.5 -33.5v-115h107q20 0 33.5 -12t13.5 -33v-43q0 -20 -13.5 -32.5t-33.5 -12.5h-107v-229q57 76 138 115.5t200 39.5q197 0 306.5 -125.5t109.5 -332.5v-580q0 -20 -13.5 -33.5 t-33.5 -13.5h-97q-20 0 -32.5 13.5t-12.5 33.5v567q0 147 -72.5 229.5t-205.5 82.5q-131 0 -209 -83t-78 -229v-567q0 -20 -13.5 -33.5t-33.5 -13.5h-96q-21 0 -34 13.5t-13 33.5v1112h-107q-20 0 -33.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x128;" horiz-adv-x="555" d="M-5 1569q0 68 48.5 128t123.5 60q37 0 66 -11t63 -32q18 -10 39 -19t39 -9t27.5 8t19.5 24t18.5 22.5t22.5 6.5h64q14 0 23.5 -10.5t9.5 -22.5q0 -41 -22.5 -85t-62.5 -73.5t-89 -29.5q-37 0 -64 10t-65 31q-18 10 -39 19t-39 9q-16 0 -26.5 -8t-20.5 -22 q-16 -29 -39 -29h-64q-14 0 -23.5 10t-9.5 23zM180 47v1339q0 20 12.5 34t32.5 14h103q23 0 35 -12.5t12 -35.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x129;" horiz-adv-x="491" d="M-27 1251q0 68 46 123.5t122 55.5q37 0 61.5 -10.5t59.5 -30.5q4 -2 26.5 -15.5t47.5 -13.5q18 0 27.5 8t19.5 25q10 16 18 22t23 6h61q16 0 25.5 -10t9.5 -22q0 -68 -47 -124.5t-123 -56.5q-37 0 -61.5 10.5t-59.5 30.5q-18 10 -36.5 19.5t-37.5 9.5q-16 0 -26 -8 t-21 -23q-16 -29 -39 -28h-63q-14 0 -23.5 10t-9.5 22zM154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x12a;" horiz-adv-x="555" d="M-2 1581v74q0 20 12 32.5t33 12.5h469q20 0 32.5 -12.5t12.5 -32.5v-74q0 -20 -12.5 -32.5t-32.5 -12.5h-469q-21 0 -33 12.5t-12 32.5zM180 47v1339q0 20 12.5 34t32.5 14h103q23 0 35 -12.5t12 -35.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5 t-12.5 33.5z" />
<glyph unicode="&#x12b;" horiz-adv-x="491" d="M-25 1311v59q0 20 12.5 32.5t32.5 12.5h451q20 0 32.5 -12t12.5 -33v-59q0 -20 -12.5 -32.5t-32.5 -12.5h-451q-20 0 -32.5 12t-12.5 33zM154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5 t-13 33.5z" />
<glyph unicode="&#x12c;" horiz-adv-x="555" d="M23 1769q0 16 10 27.5t29 11.5h59q18 0 28.5 -11t10.5 -28q0 -57 26.5 -95t92.5 -38t92.5 38t26.5 95q0 16 10 27.5t28 11.5h60q18 0 28.5 -11t10.5 -28q0 -111 -59.5 -181t-196.5 -70t-196.5 70.5t-59.5 180.5zM180 47v1339q0 20 12.5 34t32.5 14h103q23 0 35 -12.5 t12 -35.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x12d;" horiz-adv-x="491" d="M-2 1460q0 16 10.5 27.5t28.5 11.5h58q18 0 28.5 -11t10.5 -28q0 -131 110 -131q111 0 111 131q0 16 10 27.5t29 11.5h59q18 0 28.5 -11t10.5 -28q0 -109 -58.5 -180.5t-189.5 -71.5q-129 0 -187.5 72t-58.5 180zM154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5 t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x12e;" horiz-adv-x="585" d="M37 -158q0 102 51 169t139 83l-8 15q-8 12 -8 26v1251q0 20 12.5 34t32.5 14h102q23 0 35.5 -12.5t12.5 -35.5v-1339q0 -20 -13.5 -33.5t-34.5 -13.5h-28q-66 0 -107 -44t-41 -114t41 -112.5t107 -42.5h28q20 0 34 -12.5t14 -32.5v-48q0 -20 -13.5 -32.5t-34.5 -12.5h-36 q-134 0 -209.5 80t-75.5 213z" />
<glyph unicode="&#x12f;" horiz-adv-x="526" d="M27 -154q0 98 46 163t128 83l-2 6q-8 8 -9 21v899q0 20 12.5 33.5t33.5 13.5h90q23 0 35 -12.5t12 -34.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-11q-68 0 -107.5 -42t-39.5 -112t40 -110.5t107 -40.5h29q20 0 33.5 -13.5t13.5 -33.5v-43q0 -20 -13 -32.5t-34 -12.5h-37 q-131 0 -205.5 77.5t-74.5 208.5zM172 1313v106q0 20 13.5 34.5t33.5 14.5h123q20 0 34.5 -14t14.5 -35v-106q0 -20 -14.5 -33.5t-34.5 -13.5h-123q-20 0 -33.5 13t-13.5 34z" />
<glyph unicode="&#x130;" horiz-adv-x="555" d="M168 1581v129q0 20 12 33.5t33 13.5h129q20 0 33.5 -13t13.5 -34v-129q0 -20 -13.5 -32.5t-33.5 -12.5h-129q-21 0 -33 12.5t-12 32.5zM180 47v1339q0 20 12.5 34t32.5 14h103q23 0 35 -12.5t12 -35.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5 t-12.5 33.5z" />
<glyph unicode="&#x131;" horiz-adv-x="491" d="M154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x132;" horiz-adv-x="1280" d="M180 47v1339q0 20 12.5 34t32.5 14h103q23 0 35 -12.5t12 -35.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5t-12.5 33.5zM539 27v79q0 20 13 35t34 15q162 0 247 102t85 281v847q0 20 13 34t34 14h100q23 0 36 -13.5t13 -36.5v-860q0 -256 -138 -400 t-390 -144q-21 0 -34 13t-13 34z" />
<glyph unicode="&#x133;" horiz-adv-x="1007" d="M137 1313v106q0 20 13.5 34.5t33.5 14.5h121q20 0 34.5 -14t14.5 -35v-106q0 -20 -14 -33.5t-35 -13.5h-121q-20 0 -33.5 13t-13.5 34zM154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5 zM424 -276q0 20 12.5 32.5t32.5 12.5h29q100 0 134 52t34 152v1045q0 23 13.5 35t33.5 12h92q20 0 33.5 -13.5t13.5 -33.5v-1047q0 -360 -344 -360h-39q-20 0 -32.5 13.5t-12.5 33.5v66zM648 1313v106q0 20 13 34.5t34 14.5h127q20 0 33.5 -14t13.5 -35v-106 q0 -20 -13.5 -33.5t-33.5 -13.5h-127q-21 0 -34 13t-13 34z" />
<glyph unicode="&#x134;" horiz-adv-x="1294" d="M80 412q0 18 12 31.5t31 13.5h104q45 0 52 -47q12 -133 102 -196.5t225 -63.5q156 0 241 102t85 276v734h-731q-20 0 -33.5 13t-13.5 34v77q0 20 13 34t34 14h878q23 0 36 -13.5t13 -36.5v-860q0 -164 -62 -286.5t-181 -190t-281 -67.5q-223 0 -369.5 109.5t-154.5 322.5 zM338 1561q0 18 23 38l186 187q20 20 33.5 25.5t34.5 5.5h61q18 0 32.5 -5.5t33.5 -25.5l188 -187q20 -20 21 -38q0 -25 -29 -25h-53q-35 0 -55 14l-168 117l-168 -117q-25 -14 -58 -14h-53q-29 0 -29 25z" />
<glyph unicode="&#x135;" horiz-adv-x="624" d="M-25 1243q0 18 23 39l186 186q20 20 33.5 25.5t34.5 5.5h57q20 0 33.5 -5t34.5 -26l186 -186q23 -23 23 -39q0 -25 -31 -24h-51q-35 0 -56 14l-168 117l-168 -117q-25 -14 -57 -14h-51q-29 -1 -29 24zM25 -266q0 20 13 33.5t34 13.5h41q102 0 135 52t33 153v917h-191 q-20 0 -33.5 13.5t-13.5 33.5v68q0 23 13.5 35t33.5 12h332q20 0 33.5 -13.5t13.5 -33.5v-1032q0 -174 -82 -274.5t-264 -100.5h-51q-21 0 -34 13.5t-13 33.5v76z" />
<glyph unicode="&#x136;" horiz-adv-x="1224" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h97q23 0 36 -13.5t13 -36.5v-510l555 527q33 33 80 33h102q16 0 28.5 -12.5t12.5 -28.5t-10 -27l-639 -618l682 -678q10 -12 10 -29q0 -16 -12.5 -28.5t-28.5 -12.5h-107q-29 0 -45 9t-34 24l-594 579v-565q0 -20 -14.5 -33.5 t-34.5 -13.5h-97q-20 0 -32.5 13.5t-12.5 33.5zM486 -401l35 239q8 59 57 60h129q14 0 23.5 -10.5t9.5 -24.5t-6 -33l-88 -227q-10 -25 -22.5 -36t-37.5 -11h-69q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x137;" horiz-adv-x="1050" d="M156 47v1360q0 23 13 35t34 12h88q20 0 33.5 -13.5t13.5 -33.5v-715l410 338q31 23 43 29t38 6h103q18 0 29.5 -11.5t11.5 -31.5q0 -18 -23 -39l-479 -393l530 -508q23 -23 23 -39q0 -18 -11.5 -30.5t-29.5 -12.5h-100q-31 0 -44.5 6t-37.5 29l-463 438v-426 q0 -20 -13.5 -33.5t-33.5 -13.5h-88q-21 0 -34 13.5t-13 33.5zM371 -401l37 239q8 59 57 60h125q14 0 23.5 -10.5t9.5 -24.5q0 -16 -8 -33l-86 -227q-10 -25 -22.5 -36t-37.5 -11h-67q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x138;" horiz-adv-x="1052" d="M154 47v971q0 20 13 33.5t34 13.5h88q23 0 35 -12.5t12 -34.5v-379l428 391q29 23 41 29t41 6h92q18 0 30.5 -11.5t12.5 -31.5t-25 -39l-487 -448l530 -453q23 -20 23 -39q0 -18 -11.5 -30.5t-29.5 -12.5h-102q-27 0 -41.5 7t-40.5 28l-461 391v-379q0 -20 -13.5 -33.5 t-33.5 -13.5h-88q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x139;" horiz-adv-x="1148" d="M180 47v1339q0 20 12.5 34t32.5 14h103q20 0 32.5 -13.5t12.5 -34.5v-1214h684q23 0 36 -13.5t13 -33.5v-78q0 -20 -14.5 -33.5t-34.5 -13.5h-832q-20 0 -32.5 13.5t-12.5 33.5zM188 1569q0 16 10 26l152 185q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25 l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#x13a;" horiz-adv-x="493" d="M156 47v1360q0 23 13 35t34 12h90q20 0 33.5 -13.5t13.5 -33.5v-1360q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5zM178 1568q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63 q-33 -1 -33 32z" />
<glyph unicode="&#x13b;" horiz-adv-x="1148" d="M180 47v1339q0 20 12.5 34t32.5 14h103q20 0 32.5 -13.5t12.5 -34.5v-1214h684q23 0 36 -13.5t13 -33.5v-78q0 -20 -14.5 -33.5t-34.5 -13.5h-832q-20 0 -32.5 13.5t-12.5 33.5zM475 -401l35 239q8 59 57 60h129q14 0 23.5 -10.5t9.5 -24.5t-6 -33l-88 -227 q-10 -25 -22.5 -36t-37.5 -11h-69q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x13c;" horiz-adv-x="493" d="M94 -401l37 239q8 59 57 60h125q14 0 23.5 -10.5t9.5 -24.5q0 -16 -8 -33l-86 -227q-10 -25 -22.5 -36t-37.5 -11h-67q-17 0 -26 12t-5 31zM156 47v1360q0 23 13 35t34 12h90q20 0 33.5 -13.5t13.5 -33.5v-1360q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5 t-13 33.5z" />
<glyph unicode="&#x13d;" horiz-adv-x="1148" d="M180 47v1339q0 20 12.5 34t32.5 14h103q20 0 32.5 -13.5t12.5 -34.5v-1214h684q23 0 36 -13.5t13 -33.5v-78q0 -20 -14.5 -33.5t-34.5 -13.5h-832q-20 0 -32.5 13.5t-12.5 33.5zM570 1155l35 239q8 59 57 60h129q14 0 23.5 -10.5t9.5 -24.5t-6 -33l-88 -227 q-10 -25 -22.5 -36t-37.5 -11h-69q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x13e;" horiz-adv-x="587" d="M156 47v1360q0 23 13 35t34 12h90q20 0 33.5 -13.5t13.5 -33.5v-1360q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5zM451 1155l37 239q8 59 57 60h125q14 0 23.5 -10.5t9.5 -24.5q0 -16 -8 -33l-86 -227q-10 -25 -22.5 -36t-37.5 -11h-67q-17 0 -26 12 t-5 31z" />
<glyph unicode="&#x13f;" horiz-adv-x="1148" d="M180 47v1339q0 20 12.5 34t32.5 14h103q20 0 32.5 -13.5t12.5 -34.5v-1214h684q23 0 36 -13.5t13 -33.5v-78q0 -20 -14.5 -33.5t-34.5 -13.5h-832q-20 0 -32.5 13.5t-12.5 33.5zM602 687v124q0 20 12.5 34t32.5 14h127q20 0 33.5 -13.5t13.5 -34.5v-124q0 -20 -13 -34 t-34 -14h-127q-20 0 -32.5 13.5t-12.5 34.5z" />
<glyph unicode="&#x140;" horiz-adv-x="610" d="M156 47v1360q0 23 13 35t34 12h90q20 0 33.5 -13.5t13.5 -33.5v-1360q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5zM442 674v124q0 20 12.5 34t32.5 14h127q20 0 33.5 -13.5t13.5 -34.5v-124q0 -20 -13 -34t-34 -14h-127q-20 0 -32.5 13.5t-12.5 34.5z " />
<glyph unicode="&#x141;" horiz-adv-x="1165" d="M25 567v70q0 23 9 33t36 20l127 49v647q0 20 12 34t33 14h102q20 0 32.5 -13.5t12.5 -34.5v-571l297 115q16 6 27 6q18 0 28.5 -9t10.5 -28v-70q0 -23 -9.5 -33t-35.5 -20l-318 -125v-479h686q20 0 33.5 -13.5t13.5 -33.5v-78q0 -20 -13 -33.5t-34 -13.5h-833 q-21 0 -33 13.5t-12 33.5v528l-107 -40q-16 -6 -24 -7q-18 0 -29.5 10.5t-11.5 28.5z" />
<glyph unicode="&#x142;" horiz-adv-x="559" d="M49 625v69q0 23 9.5 33t35.5 21l94 36v623q0 20 13.5 33.5t34.5 13.5h90q23 0 35 -12t12 -35v-553l73 29q14 6 27 6q18 0 28.5 -9.5t10.5 -27.5v-70q0 -23 -9 -33t-36 -20l-94 -39v-643q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34.5 13.5t-13.5 33.5v574l-73 -27 q-16 -6 -27 -6q-18 0 -28.5 9t-10.5 28z" />
<glyph unicode="&#x143;" horiz-adv-x="1419" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h90q20 0 30.5 -8.5t21.5 -20.5l686 -1057v1036q0 23 13 36.5t34 13.5h92q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-33.5 -14.5h-92q-35 0 -49 29l-686 1046v-1028q0 -20 -13.5 -33.5t-34.5 -13.5h-94q-20 0 -32.5 13.5 t-12.5 33.5zM627 1569q0 16 10 26l152 185q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#x144;" horiz-adv-x="1236" d="M154 47v971q0 20 13 33.5t34 13.5h88q23 0 35 -12.5t12 -34.5v-90q61 76 142 116.5t202 40.5q197 0 305.5 -125.5t108.5 -332.5v-580q0 -20 -13.5 -33.5t-33.5 -13.5h-95q-20 0 -33.5 13.5t-13.5 33.5v567q0 147 -71.5 229.5t-204.5 82.5q-131 0 -209 -83t-78 -229v-567 q0 -20 -13.5 -33.5t-33.5 -13.5h-94q-21 0 -34 13.5t-13 33.5zM528 1251q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32z" />
<glyph unicode="&#x145;" horiz-adv-x="1419" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h90q20 0 30.5 -8.5t21.5 -20.5l686 -1057v1036q0 23 13 36.5t34 13.5h92q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-33.5 -14.5h-92q-35 0 -49 29l-686 1046v-1028q0 -20 -13.5 -33.5t-34.5 -13.5h-94q-20 0 -32.5 13.5 t-12.5 33.5zM563 -401l35 239q8 59 57 60h129q14 0 23.5 -10.5t9.5 -24.5t-6 -33l-88 -227q-10 -25 -22.5 -36t-37.5 -11h-69q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x146;" horiz-adv-x="1236" d="M154 47v971q0 20 13 33.5t34 13.5h88q23 0 35 -12.5t12 -34.5v-90q61 76 142 116.5t202 40.5q197 0 305.5 -125.5t108.5 -332.5v-580q0 -20 -13.5 -33.5t-33.5 -13.5h-95q-20 0 -33.5 13.5t-13.5 33.5v567q0 147 -71.5 229.5t-204.5 82.5q-131 0 -209 -83t-78 -229v-567 q0 -20 -13.5 -33.5t-33.5 -13.5h-94q-21 0 -34 13.5t-13 33.5zM481 -401l37 239q8 59 57 60h125q14 0 23.5 -10.5t9.5 -24.5q0 -16 -8 -33l-86 -227q-10 -25 -22.5 -36t-37.5 -11h-67q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x147;" horiz-adv-x="1419" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h90q20 0 30.5 -8.5t21.5 -20.5l686 -1057v1036q0 23 13 36.5t34 13.5h92q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-33.5 -14.5h-92q-35 0 -49 29l-686 1046v-1028q0 -20 -13.5 -33.5t-34.5 -13.5h-94q-20 0 -32.5 13.5 t-12.5 33.5zM403 1792q0 25 29 25h53q33 0 58 -15l168 -116l168 116q20 14 55 15h53q29 0 29 -25q0 -18 -21 -39l-188 -186q-18 -20 -32.5 -25.5t-33.5 -5.5h-61q-21 0 -34 5t-34 26l-186 186q-23 20 -23 39z" />
<glyph unicode="&#x148;" horiz-adv-x="1236" d="M154 47v971q0 20 13 33.5t34 13.5h88q23 0 35 -12.5t12 -34.5v-90q61 76 142 116.5t202 40.5q197 0 305.5 -125.5t108.5 -332.5v-580q0 -20 -13.5 -33.5t-33.5 -13.5h-95q-20 0 -33.5 13.5t-13.5 33.5v567q0 147 -71.5 229.5t-204.5 82.5q-131 0 -209 -83t-78 -229v-567 q0 -20 -13.5 -33.5t-33.5 -13.5h-94q-21 0 -34 13.5t-13 33.5zM317 1475q0 25 29 24h51q33 0 57 -14l168 -117l168 117q20 14 56 14h51q31 0 31 -24q0 -16 -23 -39l-186 -187q-20 -20 -33.5 -25t-34.5 -5h-57q-21 0 -34 5t-34 25l-186 187q-23 20 -23 39z" />
<glyph unicode="&#x149;" horiz-adv-x="1236" d="M154 47v971q0 20 13 33.5t34 13.5h88q23 0 35 -12.5t12 -34.5v-90q61 76 142 116.5t202 40.5q197 0 305.5 -125.5t108.5 -332.5v-580q0 -20 -13.5 -33.5t-33.5 -13.5h-95q-20 0 -33.5 13.5t-13.5 33.5v567q0 147 -71.5 229.5t-204.5 82.5q-131 0 -209 -83t-78 -229v-567 q0 -20 -13.5 -33.5t-33.5 -13.5h-94q-21 0 -34 13.5t-13 33.5zM180 1239l37 239q8 59 57 60h125q14 0 23.5 -10.5t9.5 -24.5q0 -16 -8 -33l-86 -227q-10 -25 -22.5 -36t-37.5 -11h-67q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x14a;" horiz-adv-x="1419" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h90q20 0 30.5 -8.5t21.5 -20.5l686 -1057v1036q0 23 13 36.5t34 13.5h92q23 0 36 -13.5t13 -36.5v-1472q0 -373 -369 -373h-12q-20 0 -33.5 12.5t-13.5 34.5v78q0 20 13.5 33.5t33.5 13.5h21q100 0 136 52.5t36 150.5v113 l-686 1048v-1028q0 -20 -13.5 -33.5t-34.5 -13.5h-94q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x14b;" horiz-adv-x="1236" d="M154 47v971q0 20 13 33.5t34 13.5h88q23 0 35 -12.5t12 -34.5v-90q61 76 142 116.5t202 40.5q197 0 305.5 -125.5t108.5 -332.5v-709q0 -379 -367 -379h-18q-21 0 -33 13.5t-12 33.5v74q0 20 12 33.5t33 13.5h20q106 0 141 53.5t35 161.5v692q0 147 -71.5 229.5 t-204.5 82.5q-131 0 -209 -83t-78 -229v-567q0 -20 -13.5 -33.5t-33.5 -13.5h-94q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x14c;" horiz-adv-x="1382" d="M125 717q0 113 2 172q8 279 158.5 422t406.5 143t405.5 -143.5t157.5 -421.5q4 -119 5 -172q0 -51 -5 -174q-8 -283 -152.5 -423t-410.5 -140t-411.5 140t-153.5 423q-2 61 -2 174zM322 717q0 -102 2 -164q6 -205 104 -304t264 -99q164 0 262.5 99t106.5 304q4 123 4 164 q0 45 -4 164q-8 205 -106.5 305t-262.5 100t-263 -100.5t-105 -304.5q-2 -59 -2 -164zM412 1581v74q0 20 12 32.5t33 12.5h469q20 0 32.5 -12.5t12.5 -32.5v-74q0 -20 -12.5 -32.5t-32.5 -12.5h-469q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x14d;" horiz-adv-x="1171" d="M106 532l3 89q8 211 132 337.5t345 126.5t345 -127t132 -337q4 -45 4 -89q0 -43 -4 -88q-8 -211 -130 -337.5t-347 -126.5q-226 0 -347.5 126.5t-129.5 337.5zM293 532l2 -77q6 -156 83 -239t208 -83t207.5 83t83.5 239q2 20 2 77t-2 78q-6 156 -83 239t-208 83t-208 -83 t-83 -239zM317 1311v59q0 20 12.5 32.5t32.5 12.5h451q20 0 32.5 -12t12.5 -33v-59q0 -20 -12.5 -32.5t-32.5 -12.5h-451q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x14e;" horiz-adv-x="1382" d="M125 717q0 113 2 172q8 279 158.5 422t406.5 143t405.5 -143.5t157.5 -421.5q4 -119 5 -172q0 -51 -5 -174q-8 -283 -152.5 -423t-410.5 -140t-411.5 140t-153.5 423q-2 61 -2 174zM322 717q0 -102 2 -164q6 -205 104 -304t264 -99q164 0 262.5 99t106.5 304q4 123 4 164 q0 45 -4 164q-8 205 -106.5 305t-262.5 100t-263 -100.5t-105 -304.5q-2 -59 -2 -164zM436 1769q0 16 10 27.5t29 11.5h59q18 0 28.5 -11t10.5 -28q0 -57 26.5 -95t92.5 -38t92.5 38t26.5 95q0 16 10 27.5t28 11.5h60q18 0 28.5 -11t10.5 -28q0 -111 -59.5 -181t-196.5 -70 t-196.5 70.5t-59.5 180.5z" />
<glyph unicode="&#x14f;" horiz-adv-x="1171" d="M106 532l3 89q8 211 132 337.5t345 126.5t345 -127t132 -337q4 -45 4 -89q0 -43 -4 -88q-8 -211 -130 -337.5t-347 -126.5q-226 0 -347.5 126.5t-129.5 337.5zM293 532l2 -77q6 -156 83 -239t208 -83t207.5 83t83.5 239q2 20 2 77t-2 78q-6 156 -83 239t-208 83t-208 -83 t-83 -239zM340 1460q0 16 10.5 27.5t28.5 11.5h58q18 0 28.5 -11t10.5 -28q0 -131 110 -131q111 0 111 131q0 16 10 27.5t29 11.5h59q18 0 28.5 -11t10.5 -28q0 -109 -58.5 -180.5t-189.5 -71.5q-129 0 -187.5 72t-58.5 180z" />
<glyph unicode="&#x150;" horiz-adv-x="1382" d="M125 717q0 113 2 172q8 279 158.5 422t406.5 143t405.5 -143.5t157.5 -421.5q4 -119 5 -172q0 -51 -5 -174q-8 -283 -152.5 -423t-410.5 -140t-411.5 140t-153.5 423q-2 61 -2 174zM322 717q0 -102 2 -164q6 -205 104 -304t264 -99q164 0 262.5 99t106.5 304q4 123 4 164 q0 45 -4 164q-8 205 -106.5 305t-262.5 100t-263 -100.5t-105 -304.5q-2 -59 -2 -164zM453 1569q0 10 10 30l80 179q10 20 27.5 29.5t48.5 9.5h121q18 0 29.5 -10.5t11.5 -28.5q0 -14 -13 -27l-159 -186q-25 -29 -64 -29h-59q-33 0 -33 33zM789 1569q0 10 10 30l80 179 q10 20 27.5 29.5t48.5 9.5h121q18 0 29.5 -10.5t11.5 -28.5q0 -14 -13 -27l-159 -186q-25 -29 -62 -29h-61q-33 0 -33 33z" />
<glyph unicode="&#x151;" horiz-adv-x="1171" d="M106 532l3 89q8 211 132 337.5t345 126.5t345 -127t132 -337q4 -45 4 -89q0 -43 -4 -88q-8 -211 -130 -337.5t-347 -126.5q-226 0 -347.5 126.5t-129.5 337.5zM293 532l2 -77q6 -156 83 -239t208 -83t207.5 83t83.5 239q2 20 2 77t-2 78q-6 156 -83 239t-208 83t-208 -83 t-83 -239zM336 1251q0 10 10 31l80 178q10 20 27.5 29.5t48.5 9.5h111q18 0 29.5 -10t11.5 -29q0 -14 -13 -26l-159 -187q-25 -29 -64 -28h-49q-33 -1 -33 32zM668 1251q0 10 10 31l80 178q10 20 27.5 29.5t48.5 9.5h110q18 0 29.5 -10t11.5 -29q0 -10 -12 -26l-162 -187 q-25 -29 -61 -28h-49q-33 -1 -33 32z" />
<glyph unicode="&#x152;" horiz-adv-x="1878" d="M129 719l2 160q6 279 143.5 417t446.5 138h987q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-623v-457h578q23 0 36 -13.5t13 -35.5v-70q0 -20 -13 -33.5t-36 -13.5h-578v-473h639q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-1013 q-207 0 -333 59.5t-183.5 181t-63.5 316.5zM324 719l2 -154q4 -145 43 -230t124 -125t228 -40h176v1096h-166q-213 0 -307 -95.5t-98 -300.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1916" d="M109 532q0 66 2 89q8 211 132 337.5t345 126.5q135 0 233.5 -53t155.5 -149q59 96 155.5 149t221.5 53q225 0 349 -143t124 -389v-37q0 -20 -13.5 -33.5t-33.5 -13.5h-711v-18q6 -133 82 -227.5t201 -94.5q92 0 150.5 38t88.5 79q18 25 27.5 30t34.5 5h92 q18 0 30.5 -10.5t12.5 -26.5q0 -45 -55.5 -108.5t-154.5 -109.5t-224 -46t-222.5 52t-154.5 148q-57 -96 -154.5 -148t-234.5 -52q-226 0 -347.5 126.5t-129.5 337.5q-2 23 -2 88zM295 532l2 -77q6 -156 83 -239t208 -83t207.5 83t83.5 239q2 20 2 77t-2 78q-6 156 -83 239 t-208 83t-208 -83t-83 -239zM1069 608h571v6q0 143 -77.5 233.5t-208.5 90.5q-133 0 -209 -90t-76 -234v-6z" />
<glyph unicode="&#x154;" horiz-adv-x="1339" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h500q233 0 365.5 -110t132.5 -318q0 -156 -78 -256.5t-217 -139.5l315 -547q6 -12 6 -22q0 -16 -12 -28.5t-29 -12.5h-90q-31 0 -48 14.5t-32 40.5l-301 525h-364v-533q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5 t-12.5 33.5zM373 748h344q309 0 309 260t-309 260h-344v-520zM545 1569q0 16 10 26l152 185q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#x155;" horiz-adv-x="770" d="M154 47v969q0 20 13 34.5t34 14.5h86q23 0 36 -13.5t13 -35.5v-90q84 139 285 139h79q23 0 35.5 -12.5t12.5 -34.5v-76q0 -20 -12.5 -33.5t-35.5 -13.5h-116q-113 0 -177.5 -65.5t-64.5 -178.5v-604q0 -20 -14.5 -33.5t-34.5 -13.5h-92q-21 0 -34 13.5t-13 33.5z M336 1251q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32z" />
<glyph unicode="&#x156;" horiz-adv-x="1339" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h500q233 0 365.5 -110t132.5 -318q0 -156 -78 -256.5t-217 -139.5l315 -547q6 -12 6 -22q0 -16 -12 -28.5t-29 -12.5h-90q-31 0 -48 14.5t-32 40.5l-301 525h-364v-533q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5 t-12.5 33.5zM373 748h344q309 0 309 260t-309 260h-344v-520zM524 -401l35 239q8 59 57 60h129q14 0 23.5 -10.5t9.5 -24.5t-6 -33l-88 -227q-10 -25 -22.5 -36t-37.5 -11h-69q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x157;" horiz-adv-x="770" d="M82 -401l37 239q8 59 57 60h125q14 0 23.5 -10.5t9.5 -24.5q0 -16 -8 -33l-86 -227q-10 -25 -22.5 -36t-37.5 -11h-67q-17 0 -26 12t-5 31zM154 47v969q0 20 13 34.5t34 14.5h86q23 0 36 -13.5t13 -35.5v-90q84 139 285 139h79q23 0 35.5 -12.5t12.5 -34.5v-76 q0 -20 -12.5 -33.5t-35.5 -13.5h-116q-113 0 -177.5 -65.5t-64.5 -178.5v-604q0 -20 -14.5 -33.5t-34.5 -13.5h-92q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x158;" horiz-adv-x="1339" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h500q233 0 365.5 -110t132.5 -318q0 -156 -78 -256.5t-217 -139.5l315 -547q6 -12 6 -22q0 -16 -12 -28.5t-29 -12.5h-90q-31 0 -48 14.5t-32 40.5l-301 525h-364v-533q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5 t-12.5 33.5zM321 1792q0 25 29 25h53q33 0 58 -15l168 -116l168 116q20 14 55 15h53q29 0 29 -25q0 -18 -21 -39l-188 -186q-18 -20 -32.5 -25.5t-33.5 -5.5h-61q-21 0 -34 5t-34 26l-186 186q-23 20 -23 39zM373 748h344q309 0 309 260t-309 260h-344v-520z" />
<glyph unicode="&#x159;" horiz-adv-x="770" d="M106 1475q0 25 29 24h51q33 0 57 -14l168 -117l168 117q20 14 56 14h51q31 0 31 -24q0 -16 -23 -39l-186 -187q-20 -20 -33.5 -25t-34.5 -5h-57q-21 0 -34 5t-34 25l-186 187q-23 20 -23 39zM154 47v969q0 20 13 34.5t34 14.5h86q23 0 36 -13.5t13 -35.5v-90 q84 139 285 139h79q23 0 35.5 -12.5t12.5 -34.5v-76q0 -20 -12.5 -33.5t-35.5 -13.5h-116q-113 0 -177.5 -65.5t-64.5 -178.5v-604q0 -20 -14.5 -33.5t-34.5 -13.5h-92q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x15a;" horiz-adv-x="1269" d="M88 360q0 16 12.5 28.5t30.5 12.5h100q37 0 52 -41q18 -86 105 -148t249 -62q174 0 260 61t86 170q0 70 -42 115t-125 78.5t-249 82.5q-158 45 -251 96.5t-140 126t-47 189.5q0 111 59.5 198t172 137t266.5 50q160 0 273.5 -56.5t173 -141.5t63.5 -171q0 -18 -12.5 -30.5 t-30.5 -12.5h-103q-16 0 -30.5 10.5t-20.5 30.5q-12 88 -98 145.5t-215 57.5q-139 0 -221 -54t-82 -161q0 -72 37.5 -117t116.5 -79.5t231 -79.5q174 -49 273 -98.5t148.5 -122t49.5 -187.5q0 -193 -146.5 -300t-396.5 -107q-168 0 -292 53t-188.5 140t-68.5 187zM553 1569 q0 16 10 26l152 185q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#x15b;" horiz-adv-x="1040" d="M88 229q0 20 14.5 31.5t30.5 11.5h86q12 0 19.5 -4t19.5 -18q47 -57 105.5 -90t156.5 -33q111 0 177.5 41t66.5 119q0 51 -28.5 81.5t-95.5 55.5t-198 53q-178 39 -251.5 113t-73.5 190q0 76 45 146.5t134 114.5t212 44q129 0 220 -43t137 -100t46 -96q0 -18 -13 -30.5 t-32 -12.5h-78q-29 0 -40 22q-35 39 -58.5 60.5t-69 37t-112.5 15.5q-100 0 -153.5 -42t-53.5 -112q0 -43 22.5 -72.5t86 -55t188.5 -52.5q195 -41 275.5 -116.5t80.5 -194.5q0 -88 -50 -160t-149.5 -112.5t-236.5 -40.5q-141 0 -238.5 46t-144.5 105t-47 98zM413 1251 q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32z" />
<glyph unicode="&#x15c;" horiz-adv-x="1269" d="M88 360q0 16 12.5 28.5t30.5 12.5h100q37 0 52 -41q18 -86 105 -148t249 -62q174 0 260 61t86 170q0 70 -42 115t-125 78.5t-249 82.5q-158 45 -251 96.5t-140 126t-47 189.5q0 111 59.5 198t172 137t266.5 50q160 0 273.5 -56.5t173 -141.5t63.5 -171q0 -18 -12.5 -30.5 t-30.5 -12.5h-103q-16 0 -30.5 10.5t-20.5 30.5q-12 88 -98 145.5t-215 57.5q-139 0 -221 -54t-82 -161q0 -72 37.5 -117t116.5 -79.5t231 -79.5q174 -49 273 -98.5t148.5 -122t49.5 -187.5q0 -193 -146.5 -300t-396.5 -107q-168 0 -292 53t-188.5 140t-68.5 187zM329 1561 q0 18 23 38l186 187q20 20 33.5 25.5t34.5 5.5h61q18 0 32.5 -5.5t33.5 -25.5l188 -187q20 -20 21 -38q0 -25 -29 -25h-53q-35 0 -55 14l-168 117l-168 -117q-25 -14 -58 -14h-53q-29 0 -29 25z" />
<glyph unicode="&#x15d;" horiz-adv-x="1040" d="M88 229q0 20 14.5 31.5t30.5 11.5h86q12 0 19.5 -4t19.5 -18q47 -57 105.5 -90t156.5 -33q111 0 177.5 41t66.5 119q0 51 -28.5 81.5t-95.5 55.5t-198 53q-178 39 -251.5 113t-73.5 190q0 76 45 146.5t134 114.5t212 44q129 0 220 -43t137 -100t46 -96q0 -18 -13 -30.5 t-32 -12.5h-78q-29 0 -40 22q-35 39 -58.5 60.5t-69 37t-112.5 15.5q-100 0 -153.5 -42t-53.5 -112q0 -43 22.5 -72.5t86 -55t188.5 -52.5q195 -41 275.5 -116.5t80.5 -194.5q0 -88 -50 -160t-149.5 -112.5t-236.5 -40.5q-141 0 -238.5 46t-144.5 105t-47 98zM215 1243 q0 18 23 39l186 186q20 20 33.5 25.5t34.5 5.5h57q20 0 33.5 -5t34.5 -26l186 -186q23 -23 23 -39q0 -25 -31 -24h-51q-35 0 -56 14l-168 117l-168 -117q-25 -14 -57 -14h-51q-29 -1 -29 24z" />
<glyph unicode="&#x15e;" horiz-adv-x="1269" d="M88 360q0 16 12.5 28.5t30.5 12.5h100q37 0 52 -41q18 -86 105 -148t249 -62q174 0 260 61t86 170q0 70 -42 115t-125 78.5t-249 82.5q-158 45 -251 96.5t-140 126t-47 189.5q0 111 59.5 198t172 137t266.5 50q160 0 273.5 -56.5t173 -141.5t63.5 -171q0 -18 -12.5 -30.5 t-30.5 -12.5h-103q-16 0 -30.5 10.5t-20.5 30.5q-12 88 -98 145.5t-215 57.5q-139 0 -221 -54t-82 -161q0 -72 37.5 -117t116.5 -79.5t231 -79.5q174 -49 273 -98.5t148.5 -122t49.5 -187.5q0 -190 -146.5 -298.5t-394.5 -108.5l-45 -95q27 16 74 17q70 0 113.5 -48.5 t43.5 -123.5q0 -78 -49 -129.5t-141 -51.5q-70 0 -124 30t-54 59q0 10 8 16l31 29q8 8 18 8q8 0 28.5 -12.5t39 -20.5t49.5 -8q37 0 60.5 21.5t23.5 56.5q0 33 -23.5 52t-60.5 19q-23 0 -52.5 -9t-39.5 -9q-18 0 -29 12l-33 37q-8 8 -8 18t14 43l41 97q-135 16 -232 70.5 t-149.5 134t-54.5 167.5z" />
<glyph unicode="&#x15f;" horiz-adv-x="1040" d="M88 229q0 20 14.5 31.5t30.5 11.5h82q14 0 21.5 -4t19.5 -18q53 -59 109.5 -91t154.5 -32q111 0 177.5 41t66.5 119q0 51 -28.5 81.5t-95.5 55.5t-198 53q-178 39 -251.5 113t-73.5 190q0 76 45 146.5t134 114.5t212 44q129 0 220 -43t137 -100t46 -96q0 -18 -13 -30.5 t-32 -12.5h-80q-27 0 -38 22q-35 39 -58.5 60.5t-69 37t-112.5 15.5q-100 0 -153.5 -42t-53.5 -112q0 -43 22.5 -72.5t86 -55t188.5 -52.5q195 -41 275.5 -116.5t80.5 -194.5q0 -88 -50 -159t-148.5 -112.5t-235.5 -41.5l-45 -95q27 16 76 17q70 0 114 -48.5t44 -123.5 q0 -78 -50.5 -129.5t-140.5 -51.5q-72 0 -125 29t-53 57q0 10 8 19l31 29q8 8 18 8q8 0 28.5 -12.5t39 -20.5t49.5 -8q35 0 58.5 21.5t23.5 56.5q0 33 -23.5 52t-58.5 19q-25 0 -54.5 -9t-39.5 -9q-16 0 -27 12l-35 37q-6 8 -6 18t15 43l43 99q-104 16 -177 60t-109 95.5 t-36 83.5z" />
<glyph unicode="&#x160;" horiz-adv-x="1269" d="M88 360q0 16 12.5 28.5t30.5 12.5h100q37 0 52 -41q18 -86 105 -148t249 -62q174 0 260 61t86 170q0 70 -42 115t-125 78.5t-249 82.5q-158 45 -251 96.5t-140 126t-47 189.5q0 111 59.5 198t172 137t266.5 50q160 0 273.5 -56.5t173 -141.5t63.5 -171q0 -18 -12.5 -30.5 t-30.5 -12.5h-103q-16 0 -30.5 10.5t-20.5 30.5q-12 88 -98 145.5t-215 57.5q-139 0 -221 -54t-82 -161q0 -72 37.5 -117t116.5 -79.5t231 -79.5q174 -49 273 -98.5t148.5 -122t49.5 -187.5q0 -193 -146.5 -300t-396.5 -107q-168 0 -292 53t-188.5 140t-68.5 187zM329 1792 q0 25 29 25h53q33 0 58 -15l168 -116l168 116q20 14 55 15h53q29 0 29 -25q0 -18 -21 -39l-188 -186q-18 -20 -32.5 -25.5t-33.5 -5.5h-61q-21 0 -34 5t-34 26l-186 186q-23 20 -23 39z" />
<glyph unicode="&#x161;" horiz-adv-x="1040" d="M88 229q0 20 14.5 31.5t30.5 11.5h86q12 0 19.5 -4t19.5 -18q47 -57 105.5 -90t156.5 -33q111 0 177.5 41t66.5 119q0 51 -28.5 81.5t-95.5 55.5t-198 53q-178 39 -251.5 113t-73.5 190q0 76 45 146.5t134 114.5t212 44q129 0 220 -43t137 -100t46 -96q0 -18 -13 -30.5 t-32 -12.5h-78q-29 0 -40 22q-35 39 -58.5 60.5t-69 37t-112.5 15.5q-100 0 -153.5 -42t-53.5 -112q0 -43 22.5 -72.5t86 -55t188.5 -52.5q195 -41 275.5 -116.5t80.5 -194.5q0 -88 -50 -160t-149.5 -112.5t-236.5 -40.5q-141 0 -238.5 46t-144.5 105t-47 98zM215 1475 q0 25 29 24h51q33 0 57 -14l168 -117l168 117q20 14 56 14h51q31 0 31 -24q0 -16 -23 -39l-186 -187q-20 -20 -33.5 -25t-34.5 -5h-57q-21 0 -34 5t-34 25l-186 187q-23 20 -23 39z" />
<glyph unicode="&#x164;" horiz-adv-x="1181" d="M51 1305v79q0 23 13.5 36.5t33.5 13.5h983q23 0 35 -13.5t12 -36.5v-79q0 -20 -13 -34t-34 -14h-393v-1210q0 -20 -14.5 -33.5t-34.5 -13.5h-98q-21 0 -34 13.5t-13 33.5v1210h-396q-20 0 -33.5 13.5t-13.5 34.5zM284 1792q0 25 29 25h53q33 0 58 -15l168 -116l168 116 q20 14 55 15h53q29 0 29 -25q0 -18 -21 -39l-188 -186q-18 -20 -32.5 -25.5t-33.5 -5.5h-61q-21 0 -34 5t-34 26l-186 186q-23 20 -23 39z" />
<glyph unicode="&#x165;" horiz-adv-x="733" d="M39 952v66q0 20 12 33.5t33 13.5h162v342q0 20 12 33.5t33 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-342h254q20 0 33.5 -13.5t13.5 -33.5v-66q0 -20 -13.5 -32.5t-33.5 -12.5h-254v-534q0 -104 34 -157.5t116 -53.5h124q20 0 33 -13.5t13 -33.5v-68q0 -20 -12.5 -33.5 t-33.5 -13.5h-139q-317 0 -317 358v549h-162q-20 0 -32.5 12.5t-12.5 32.5zM602 1293l37 239q8 59 57 60h125q14 0 23.5 -10.5t9.5 -24.5q0 -16 -8 -33l-86 -227q-10 -25 -22.5 -36t-37.5 -11h-67q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x166;" horiz-adv-x="1200" d="M61 1305v79q0 23 13.5 36.5t34.5 13.5h981q23 0 36 -13.5t13 -36.5v-79q0 -20 -13.5 -34t-35.5 -14h-392v-426h230q23 0 36 -13t13 -36v-49q0 -20 -13.5 -33.5t-35.5 -13.5h-230v-639q0 -20 -14 -33.5t-35 -13.5h-98q-21 0 -34 13.5t-13 33.5v639h-234q-20 0 -32.5 13.5 t-12.5 33.5v49q0 23 12.5 36t32.5 13h234v426h-395q-20 0 -34 13.5t-14 34.5z" />
<glyph unicode="&#x167;" horiz-adv-x="808" d="M47 655v43q0 20 13.5 32.5t33.5 12.5h160v187h-160q-20 0 -33.5 13t-13.5 34v41q0 23 13.5 35t33.5 12h160v342q0 23 13.5 35t33.5 12h88q20 0 33.5 -13.5t13.5 -33.5v-342h254q23 0 35 -12.5t12 -34.5v-41q0 -20 -13 -33.5t-34 -13.5h-254v-187h254q20 0 33.5 -12 t13.5 -33v-43q0 -20 -13 -32.5t-34 -12.5h-254v-237q0 -104 35 -157.5t117 -53.5h123q20 0 33.5 -13.5t13.5 -33.5v-68q0 -20 -13.5 -33.5t-33.5 -13.5h-140q-317 0 -317 358v252h-160q-20 0 -33.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x168;" horiz-adv-x="1433" d="M168 553v831q0 23 12.5 36.5t32.5 13.5h98q23 0 36 -13.5t13 -36.5v-835q0 -201 93.5 -299t265.5 -98q170 0 263 99t93 298v835q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-831q0 -289 -142.5 -431t-406.5 -142t-407.5 142t-143.5 431zM436 1569 q0 68 48.5 128t123.5 60q37 0 66 -11t63 -32q18 -10 39 -19t39 -9t27.5 8t19.5 24t18.5 22.5t22.5 6.5h64q14 0 23.5 -10.5t9.5 -22.5q0 -41 -22.5 -85t-62.5 -73.5t-89 -29.5q-37 0 -64 10t-65 31q-18 10 -39 19t-39 9q-16 0 -26.5 -8t-20.5 -22q-16 -29 -39 -29h-64 q-14 0 -23.5 10t-9.5 23z" />
<glyph unicode="&#x169;" horiz-adv-x="1228" d="M143 440v578q0 20 13.5 33.5t33.5 13.5h95q20 0 33.5 -13.5t13.5 -33.5v-567q0 -311 270 -312q131 0 208 83t77 229v567q0 20 13 33.5t34 13.5h94q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v92q-59 -80 -139 -119.5 t-203 -39.5q-195 0 -301.5 125.5t-106.5 334.5zM338 1251q0 68 46 123.5t122 55.5q37 0 61.5 -10.5t59.5 -30.5q4 -2 26.5 -15.5t47.5 -13.5q18 0 27.5 8t19.5 25q10 16 18 22t23 6h61q16 0 25.5 -10t9.5 -22q0 -68 -47 -124.5t-123 -56.5q-37 0 -61.5 10.5t-59.5 30.5 q-18 10 -36.5 19.5t-37.5 9.5q-16 0 -26 -8t-21 -23q-16 -29 -39 -28h-63q-14 0 -23.5 10t-9.5 22z" />
<glyph unicode="&#x16a;" horiz-adv-x="1433" d="M168 553v831q0 23 12.5 36.5t32.5 13.5h98q23 0 36 -13.5t13 -36.5v-835q0 -201 93.5 -299t265.5 -98q170 0 263 99t93 298v835q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-831q0 -289 -142.5 -431t-406.5 -142t-407.5 142t-143.5 431zM439 1581v74 q0 20 12 32.5t33 12.5h469q20 0 32.5 -12.5t12.5 -32.5v-74q0 -20 -12.5 -32.5t-32.5 -12.5h-469q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x16b;" horiz-adv-x="1228" d="M143 440v578q0 20 13.5 33.5t33.5 13.5h95q20 0 33.5 -13.5t13.5 -33.5v-567q0 -311 270 -312q131 0 208 83t77 229v567q0 20 13 33.5t34 13.5h94q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v92q-59 -80 -139 -119.5 t-203 -39.5q-195 0 -301.5 125.5t-106.5 334.5zM340 1311v59q0 20 12.5 32.5t32.5 12.5h451q20 0 32.5 -12t12.5 -33v-59q0 -20 -12.5 -32.5t-32.5 -12.5h-451q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x16c;" horiz-adv-x="1433" d="M168 553v831q0 23 12.5 36.5t32.5 13.5h98q23 0 36 -13.5t13 -36.5v-835q0 -201 93.5 -299t265.5 -98q170 0 263 99t93 298v835q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-831q0 -289 -142.5 -431t-406.5 -142t-407.5 142t-143.5 431zM463 1769 q0 16 10 27.5t29 11.5h59q18 0 28.5 -11t10.5 -28q0 -57 26.5 -95t92.5 -38t92.5 38t26.5 95q0 16 10 27.5t28 11.5h60q18 0 28.5 -11t10.5 -28q0 -111 -59.5 -181t-196.5 -70t-196.5 70.5t-59.5 180.5z" />
<glyph unicode="&#x16d;" horiz-adv-x="1228" d="M143 440v578q0 20 13.5 33.5t33.5 13.5h95q20 0 33.5 -13.5t13.5 -33.5v-567q0 -311 270 -312q131 0 208 83t77 229v567q0 20 13 33.5t34 13.5h94q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v92q-59 -80 -139 -119.5 t-203 -39.5q-195 0 -301.5 125.5t-106.5 334.5zM362 1460q0 16 10.5 27.5t28.5 11.5h58q18 0 28.5 -11t10.5 -28q0 -131 110 -131q111 0 111 131q0 16 10 27.5t29 11.5h59q18 0 28.5 -11t10.5 -28q0 -109 -58.5 -180.5t-189.5 -71.5q-129 0 -187.5 72t-58.5 180z" />
<glyph unicode="&#x16e;" horiz-adv-x="1433" d="M168 553v831q0 23 12.5 36.5t32.5 13.5h98q23 0 36 -13.5t13 -36.5v-835q0 -201 93.5 -299t265.5 -98q170 0 263 99t93 298v835q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-831q0 -289 -142.5 -431t-406.5 -142t-407.5 142t-143.5 431zM549 1675 q0 70 49 116t121 46t121 -46t49 -116q0 -68 -49.5 -113.5t-120.5 -45.5q-72 0 -121 46t-49 113zM657 1675q0 -27 17.5 -44t44.5 -17q26 0 43.5 17.5t17.5 43.5q0 27 -17.5 44.5t-43.5 17.5q-27 0 -44.5 -17.5t-17.5 -44.5z" />
<glyph unicode="&#x16f;" horiz-adv-x="1228" d="M143 440v578q0 20 13.5 33.5t33.5 13.5h95q20 0 33.5 -13.5t13.5 -33.5v-567q0 -311 270 -312q131 0 208 83t77 229v567q0 20 13 33.5t34 13.5h94q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v92q-59 -80 -139 -119.5 t-203 -39.5q-195 0 -301.5 125.5t-106.5 334.5zM438 1368q0 70 49.5 116t120.5 46q72 0 121 -46t49 -116q0 -68 -49 -114t-121 -46q-71 0 -120.5 46t-49.5 114zM547 1368q0 -27 17.5 -44t43.5 -17q27 0 44.5 17t17.5 44t-17.5 44.5t-44.5 17.5t-44 -17.5t-17 -44.5z" />
<glyph unicode="&#x170;" horiz-adv-x="1433" d="M168 553v831q0 23 12.5 36.5t32.5 13.5h98q23 0 36 -13.5t13 -36.5v-835q0 -201 93.5 -299t265.5 -98q170 0 263 99t93 298v835q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-831q0 -289 -142.5 -431t-406.5 -142t-407.5 142t-143.5 431zM479 1569 q0 10 10 30l80 179q10 20 27.5 29.5t48.5 9.5h121q18 0 29.5 -10.5t11.5 -28.5q0 -14 -13 -27l-159 -186q-25 -29 -64 -29h-59q-33 0 -33 33zM815 1569q0 10 10 30l80 179q10 20 27.5 29.5t48.5 9.5h121q18 0 29.5 -10.5t11.5 -28.5q0 -14 -13 -27l-159 -186 q-25 -29 -62 -29h-61q-33 0 -33 33z" />
<glyph unicode="&#x171;" horiz-adv-x="1228" d="M143 440v578q0 20 13.5 33.5t33.5 13.5h95q20 0 33.5 -13.5t13.5 -33.5v-567q0 -311 270 -312q131 0 208 83t77 229v567q0 20 13 33.5t34 13.5h94q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v92q-59 -80 -139 -119.5 t-203 -39.5q-195 0 -301.5 125.5t-106.5 334.5zM344 1251q0 10 10 31l80 178q10 20 27.5 29.5t48.5 9.5h111q18 0 29.5 -10t11.5 -29q0 -14 -13 -26l-159 -187q-25 -29 -64 -28h-49q-33 -1 -33 32zM676 1251q0 10 10 31l80 178q10 20 27.5 29.5t48.5 9.5h110q18 0 29.5 -10 t11.5 -29q0 -10 -12 -26l-162 -187q-25 -29 -61 -28h-49q-33 -1 -33 32z" />
<glyph unicode="&#x172;" horiz-adv-x="1433" d="M168 553v831q0 23 12.5 36.5t32.5 13.5h98q23 0 36 -13.5t13 -36.5v-835q0 -201 93.5 -299t265.5 -98q170 0 263 99t93 298v835q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-831q0 -250 -106.5 -389t-305.5 -174q-70 -14 -105.5 -48t-35.5 -100 q0 -70 41 -111.5t106 -41.5h29q20 0 33.5 -13.5t13.5 -33.5v-45q0 -23 -12.5 -35.5t-34.5 -12.5h-37q-133 0 -209 80t-76 213q0 92 29 146q-207 29 -318.5 169t-111.5 396z" />
<glyph unicode="&#x173;" horiz-adv-x="1228" d="M143 440v578q0 20 13.5 33.5t33.5 13.5h95q20 0 33.5 -13.5t13.5 -33.5v-567q0 -311 270 -312q131 0 208 83t77 229v567q0 20 13 33.5t34 13.5h94q20 0 33.5 -13.5t13.5 -33.5v-971q0 -33 -37 -47l-10 -4q-66 -25 -95.5 -63.5t-29.5 -102.5q0 -70 29 -105.5t84 -35.5h12 q20 0 33.5 -13.5t13.5 -33.5v-45q0 -20 -13 -34t-34 -14h-20q-111 0 -180.5 75t-69.5 206q0 78 43 139.5t98 89.5q-6 10 -6 23v57q-59 -80 -139 -119.5t-203 -39.5q-195 0 -301.5 125.5t-106.5 334.5z" />
<glyph unicode="&#x174;" horiz-adv-x="1636" d="M90 1393q0 16 12.5 28.5t28.5 12.5h100q43 0 50 -35l211 -1069l227 737q12 49 63 49h72q49 0 66 -49l225 -737l211 1069q6 35 51 35h98q16 0 28.5 -12.5t12.5 -28.5q0 -12 -2 -23l-266 -1317q-10 -53 -64 -53h-73q-51 0 -68 51l-254 789l-256 -789q-14 -51 -65 -51h-76 q-52 0 -64 53l-264 1317zM512 1561q0 18 23 38l186 187q20 20 33.5 25.5t34.5 5.5h61q18 0 32.5 -5.5t33.5 -25.5l188 -187q20 -20 21 -38q0 -25 -29 -25h-53q-35 0 -55 14l-168 117l-168 -117q-25 -14 -58 -14h-53q-29 0 -29 25z" />
<glyph unicode="&#x175;" horiz-adv-x="1638" d="M76 1022q0 18 12 30.5t31 12.5h84q20 0 33.5 -11.5t15.5 -21.5l231 -782l246 772q4 16 18.5 29.5t38.5 13.5h66q25 0 39 -13.5t18 -29.5l246 -772l231 782q2 10 15.5 21.5t34.5 11.5h84q18 0 30.5 -12.5t12.5 -30.5l-4 -21l-291 -950q-8 -27 -22.5 -39t-43.5 -12h-74 q-53 0 -69 51l-240 742l-239 -742q-16 -51 -70 -51h-74q-28 0 -42.5 12.5t-22.5 38.5l-291 950zM514 1243q0 18 23 39l186 186q20 20 33.5 25.5t34.5 5.5h57q20 0 33.5 -5t34.5 -26l186 -186q23 -23 23 -39q0 -25 -31 -24h-51q-35 0 -56 14l-168 117l-168 -117 q-25 -14 -57 -14h-51q-29 -1 -29 24z" />
<glyph unicode="&#x176;" horiz-adv-x="1320" d="M63 1393q0 16 12.5 28.5t28.5 12.5h99q37 0 53 -35l406 -670l403 670q20 35 53 35h99q16 0 29.5 -12.5t13.5 -28.5q0 -10 -9 -27l-493 -834v-485q0 -20 -13.5 -33.5t-35.5 -13.5h-99q-20 0 -33.5 13.5t-13.5 33.5v485l-493 834q-6 12 -7 27zM354 1561q0 18 23 38l186 187 q20 20 33.5 25.5t34.5 5.5h61q18 0 32.5 -5.5t33.5 -25.5l188 -187q20 -20 21 -38q0 -25 -29 -25h-53q-35 0 -55 14l-168 117l-168 -117q-25 -14 -58 -14h-53q-29 0 -29 25z" />
<glyph unicode="&#x177;" horiz-adv-x="1124" d="M63 1022q2 18 14.5 30.5t28.5 12.5h95q33 0 49 -33l315 -753l324 753q16 33 49 33h92q16 0 28.5 -12.5t12.5 -28.5q0 -14 -10 -37l-580 -1343q-10 -16 -21 -24.5t-30 -8.5h-90q-18 0 -30.5 12t-12.5 29q0 6 10 37l160 374l-391 924q-12 25 -13 35zM260 1243q0 18 23 39 l186 186q20 20 33.5 25.5t34.5 5.5h57q20 0 33.5 -5t34.5 -26l186 -186q23 -23 23 -39q0 -25 -31 -24h-51q-35 0 -56 14l-168 117l-168 -117q-25 -14 -57 -14h-51q-29 -1 -29 24z" />
<glyph unicode="&#x178;" horiz-adv-x="1320" d="M63 1393q0 16 12.5 28.5t28.5 12.5h99q37 0 53 -35l406 -670l403 670q20 35 53 35h99q16 0 29.5 -12.5t13.5 -28.5q0 -10 -9 -27l-493 -834v-485q0 -20 -13.5 -33.5t-35.5 -13.5h-99q-20 0 -33.5 13.5t-13.5 33.5v485l-493 834q-6 12 -7 27zM361 1581v109q0 20 12 33.5 t33 13.5h106q20 0 33.5 -13.5t13.5 -33.5v-109q0 -20 -13 -32.5t-34 -12.5h-106q-21 0 -33 12.5t-12 32.5zM764 1581v109q0 20 12.5 33.5t32.5 13.5h109q20 0 33.5 -13.5t13.5 -33.5v-109q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#x179;" horiz-adv-x="1224" d="M84 49v82q0 35 14.5 55.5t16.5 22.5l768 1053h-733q-20 0 -34 13t-14 34v75q0 23 13.5 36.5t34.5 13.5h921q23 0 36 -13.5t13 -36.5v-81q0 -37 -26 -72l-760 -1059h760q23 0 36 -13.5t13 -33.5v-78q0 -20 -14.5 -33.5t-34.5 -13.5h-963q-20 0 -33.5 13.5t-13.5 35.5z M510 1569q0 16 10 26l152 185q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#x17a;" horiz-adv-x="1038" d="M84 47v68q0 16 9 33.5t20 31.5l571 727h-534q-20 0 -33 13.5t-13 33.5v64q0 20 12.5 33.5t33.5 13.5h724q20 0 34 -13.5t14 -33.5v-72q0 -23 -29 -63l-563 -725h585q23 0 35.5 -12.5t12.5 -34.5v-64q0 -20 -13.5 -33.5t-34.5 -13.5h-784q-20 0 -33.5 13.5t-13.5 33.5z M434 1251q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32z" />
<glyph unicode="&#x17b;" horiz-adv-x="1224" d="M84 49v82q0 35 14.5 55.5t16.5 22.5l768 1053h-733q-20 0 -34 13t-14 34v75q0 23 13.5 36.5t34.5 13.5h921q23 0 36 -13.5t13 -36.5v-81q0 -37 -26 -72l-760 -1059h760q23 0 36 -13.5t13 -33.5v-78q0 -20 -14.5 -33.5t-34.5 -13.5h-963q-20 0 -33.5 13.5t-13.5 35.5z M484 1581v129q0 20 12 33.5t33 13.5h129q20 0 33.5 -13t13.5 -34v-129q0 -20 -13.5 -32.5t-33.5 -12.5h-129q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x17c;" horiz-adv-x="1038" d="M84 47v68q0 16 9 33.5t20 31.5l571 727h-534q-20 0 -33 13.5t-13 33.5v64q0 20 12.5 33.5t33.5 13.5h724q20 0 34 -13.5t14 -33.5v-72q0 -23 -29 -63l-563 -725h585q23 0 35.5 -12.5t12.5 -34.5v-64q0 -20 -13.5 -33.5t-34.5 -13.5h-784q-20 0 -33.5 13.5t-13.5 33.5z M416 1303v124q0 20 12.5 34t32.5 14h127q20 0 33.5 -13.5t13.5 -34.5v-124q0 -20 -13 -34t-34 -14h-127q-20 0 -32.5 13.5t-12.5 34.5z" />
<glyph unicode="&#x17d;" horiz-adv-x="1224" d="M84 49v82q0 35 14.5 55.5t16.5 22.5l768 1053h-733q-20 0 -34 13t-14 34v75q0 23 13.5 36.5t34.5 13.5h921q23 0 36 -13.5t13 -36.5v-81q0 -37 -26 -72l-760 -1059h760q23 0 36 -13.5t13 -33.5v-78q0 -20 -14.5 -33.5t-34.5 -13.5h-963q-20 0 -33.5 13.5t-13.5 35.5z M286 1792q0 25 29 25h53q33 0 58 -15l168 -116l168 116q20 14 55 15h53q29 0 29 -25q0 -18 -21 -39l-188 -186q-18 -20 -32.5 -25.5t-33.5 -5.5h-61q-21 0 -34 5t-34 26l-186 186q-23 20 -23 39z" />
<glyph unicode="&#x17e;" horiz-adv-x="1038" d="M84 47v68q0 16 9 33.5t20 31.5l571 727h-534q-20 0 -33 13.5t-13 33.5v64q0 20 12.5 33.5t33.5 13.5h724q20 0 34 -13.5t14 -33.5v-72q0 -23 -29 -63l-563 -725h585q23 0 35.5 -12.5t12.5 -34.5v-64q0 -20 -13.5 -33.5t-34.5 -13.5h-784q-20 0 -33.5 13.5t-13.5 33.5z M219 1475q0 25 29 24h51q33 0 57 -14l168 -117l168 117q20 14 56 14h51q31 0 31 -24q0 -16 -23 -39l-186 -187q-20 -20 -33.5 -25t-34.5 -5h-57q-21 0 -34 5t-34 25l-186 187q-23 20 -23 39z" />
<glyph unicode="&#x17f;" horiz-adv-x="595" d="M39 952v66q0 20 12 33.5t33 13.5h176v94q0 172 81 264.5t265 92.5h121q20 0 33.5 -13.5t13.5 -34.5v-65q0 -20 -13 -32.5t-34 -12.5h-117q-100 0 -134 -51.5t-34 -149.5v-1110q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v860h-176q-20 0 -32.5 12.5 t-12.5 32.5z" />
<glyph unicode="&#x192;" horiz-adv-x="851" d="M39 -340q0 20 13.5 33.5t33.5 13.5h27q106 0 141 53.5t35 161.5v985h-174q-20 0 -33.5 12.5t-13.5 32.5v66q0 23 13 35t34 12h174v102q0 166 82 257.5t266 91.5h119q23 0 35 -12.5t12 -35.5v-65q0 -20 -13.5 -32.5t-33.5 -12.5h-115q-96 0 -133 -49t-37 -152v-92h264 q23 0 35 -12.5t12 -34.5v-66q0 -20 -13 -32.5t-34 -12.5h-264v-989q0 -182 -82 -280.5t-276 -98.5h-27q-20 0 -33.5 13.5t-13.5 33.5v74z" />
<glyph unicode="&#x1fc;" horiz-adv-x="1912" d="M10 41q0 6 3 14t3 13q27 74 153 315.5t550 1007.5q20 43 59 43h965q23 0 35 -13.5t12 -36.5v-73q0 -20 -13.5 -32.5t-33.5 -12.5h-688v-457h643q20 0 33.5 -13.5t13.5 -35.5v-70q0 -20 -12.5 -33.5t-34.5 -13.5h-643v-473h704q20 0 33.5 -13.5t13.5 -35.5v-74 q0 -20 -13 -33.5t-34 -13.5h-848q-20 0 -33.5 13.5t-13.5 33.5v283h-497q-160 -285 -162 -295q-16 -35 -51 -35h-103q-16 0 -28.5 12.5t-12.5 28.5zM442 500h422v753h-12zM872 1569q0 16 10 26l152 185q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194 q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#x1fd;" horiz-adv-x="1822" d="M80 289q0 137 111.5 225t300.5 115l307 43v55q0 211 -244 211q-94 0 -152.5 -38t-85.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 45 109.5t137 108.5t225 46q139 0 227 -44t129 -117q125 162 351 161q258 0 360 -159.5t102 -372.5v-37 q0 -20 -13 -33.5t-34 -13.5h-692v-18q4 -164 84 -243t191 -79q74 0 139 28.5t106 88.5q16 23 27.5 29t36.5 6h84q18 0 30.5 -11.5t12.5 -29.5q0 -41 -52.5 -103.5t-150.5 -109.5t-233 -47q-139 0 -235.5 55t-152.5 153q-125 -209 -415 -208q-111 0 -198 40.5t-134 111.5 t-47 157zM260 303q0 -84 68.5 -129t163.5 -45q135 0 221 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5zM825 1251q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32zM987 608h553 v8q0 145 -70.5 233.5t-205.5 88.5q-113 0 -193 -82t-84 -240v-8z" />
<glyph unicode="&#x1fe;" horiz-adv-x="1429" d="M23 434v70q0 25 9 35t36 20l84 31q-2 39 -2 115q0 53 4 184q8 279 158.5 422t406.5 143q219 0 361.5 -109.5t183.5 -320.5l77 29q16 6 25 6q41 0 41 -37v-70q0 -23 -9 -33t-36 -20l-80 -31q4 -115 4 -163q0 -47 -4 -162q-8 -283 -152.5 -423t-410.5 -140 q-487 -1 -553 452l-78 -29q-16 -6 -25 -6q-41 0 -40 37zM346 668l733 284q-23 168 -118 251t-242 83q-164 0 -263.5 -100.5t-105.5 -304.5q-6 -129 -4 -213zM352 506q18 -182 114.5 -269t252.5 -87q164 0 262 99t104 304q2 66 2 170v70zM631 1569q0 16 10 26l152 185 q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#x1ff;" horiz-adv-x="1241" d="M29 283v55q0 25 9 35t36 20l71 27q-4 39 -4 112q0 59 2 89q6 197 124 330.5t354 133.5q184 0 294.5 -83.5t153.5 -223.5l78 31q16 6 27 6q18 0 28 -9t10 -28v-57q0 -23 -9 -33t-36 -20l-69 -27q4 -61 4 -117l-2 -80q-6 -199 -123 -331.5t-356 -132.5q-185 0 -295.5 82.5 t-155.5 218.5l-76 -29q-14 -6 -26 -6q-18 0 -28.5 9t-10.5 28zM330 561l2 -69l567 221q-25 113 -99.5 166t-178.5 53q-123 0 -203 -76t-86 -233zM342 348q27 -111 101.5 -163t177.5 -52q127 0 205.5 76t84.5 237q2 14 2 58q0 47 -2 65zM528 1251q0 16 10 27l152 184 q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32z" />
<glyph unicode="&#x218;" horiz-adv-x="1269" d="M88 360q0 16 12.5 28.5t30.5 12.5h100q37 0 52 -41q18 -86 105 -148t249 -62q174 0 260 61t86 170q0 70 -42 115t-125 78.5t-249 82.5q-158 45 -251 96.5t-140 126t-47 189.5q0 111 59.5 198t172 137t266.5 50q160 0 273.5 -56.5t173 -141.5t63.5 -171q0 -18 -12.5 -30.5 t-30.5 -12.5h-103q-16 0 -30.5 10.5t-20.5 30.5q-12 88 -98 145.5t-215 57.5q-139 0 -221 -54t-82 -161q0 -72 37.5 -117t116.5 -79.5t231 -79.5q174 -49 273 -98.5t148.5 -122t49.5 -187.5q0 -193 -146.5 -300t-396.5 -107q-168 0 -292 53t-188.5 140t-68.5 187zM483 -401 l35 239q8 59 57 60h129q14 0 23.5 -10.5t9.5 -24.5t-6 -33l-88 -227q-10 -25 -22.5 -36t-37.5 -11h-69q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x219;" horiz-adv-x="1040" d="M88 229q0 20 14.5 31.5t30.5 11.5h86q12 0 19.5 -4t19.5 -18q47 -57 105.5 -90t156.5 -33q111 0 177.5 41t66.5 119q0 51 -28.5 81.5t-95.5 55.5t-198 53q-178 39 -251.5 113t-73.5 190q0 76 45 146.5t134 114.5t212 44q129 0 220 -43t137 -100t46 -96q0 -18 -13 -30.5 t-32 -12.5h-78q-29 0 -40 22q-35 39 -58.5 60.5t-69 37t-112.5 15.5q-100 0 -153.5 -42t-53.5 -112q0 -43 22.5 -72.5t86 -55t188.5 -52.5q195 -41 275.5 -116.5t80.5 -194.5q0 -88 -50 -160t-149.5 -112.5t-236.5 -40.5q-141 0 -238.5 46t-144.5 105t-47 98zM371 -401 l37 239q8 59 57 60h125q14 0 23.5 -10.5t9.5 -24.5q0 -16 -8 -33l-86 -227q-10 -25 -22.5 -36t-37.5 -11h-67q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x21a;" horiz-adv-x="1181" d="M51 1305v79q0 23 13.5 36.5t33.5 13.5h983q23 0 35 -13.5t12 -36.5v-79q0 -20 -13 -34t-34 -14h-393v-1210q0 -20 -14.5 -33.5t-34.5 -13.5h-98q-21 0 -34 13.5t-13 33.5v1210h-396q-20 0 -33.5 13.5t-13.5 34.5zM436 -401l35 239q8 59 57 60h129q14 0 23.5 -10.5 t9.5 -24.5t-6 -33l-88 -227q-10 -25 -22.5 -36t-37.5 -11h-69q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x21b;" horiz-adv-x="794" d="M39 952v66q0 20 12 33.5t33 13.5h162v342q0 20 12 33.5t33 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-342h254q20 0 33.5 -13.5t13.5 -33.5v-66q0 -20 -13.5 -32.5t-33.5 -12.5h-254v-534q0 -104 34 -157.5t116 -53.5h124q20 0 33 -13.5t13 -33.5v-68q0 -20 -12.5 -33.5 t-33.5 -13.5h-139q-317 0 -317 358v549h-162q-20 0 -32.5 12.5t-12.5 32.5zM219 -401l37 239q8 59 57 60h125q14 0 23.5 -10.5t9.5 -24.5q0 -16 -8 -33l-86 -227q-10 -25 -22.5 -36t-37.5 -11h-67q-17 0 -26 12t-5 31z" />
<glyph unicode="&#x2c6;" horiz-adv-x="882" d="M137 1243q0 18 23 39l186 186q20 20 33.5 25.5t34.5 5.5h57q20 0 33.5 -5t34.5 -26l186 -186q23 -23 23 -39q0 -25 -31 -24h-51q-35 0 -56 14l-168 117l-168 -117q-25 -14 -57 -14h-51q-29 -1 -29 24z" />
<glyph unicode="&#x2da;" horiz-adv-x="614" d="M137 1368q0 70 49.5 116t120.5 46q72 0 121 -46t49 -116q0 -68 -49 -114t-121 -46q-71 0 -120.5 46t-49.5 114zM246 1368q0 -27 17.5 -44t43.5 -17q27 0 44.5 17t17.5 44t-17.5 44.5t-44.5 17.5t-44 -17.5t-17 -44.5z" />
<glyph unicode="&#x2dc;" horiz-adv-x="819" d="M137 1251q0 68 46 123.5t122 55.5q37 0 61.5 -10.5t59.5 -30.5q4 -2 26.5 -15.5t47.5 -13.5q18 0 27.5 8t19.5 25q10 16 18 22t23 6h61q16 0 25.5 -10t9.5 -22q0 -68 -47 -124.5t-123 -56.5q-37 0 -61.5 10.5t-59.5 30.5q-18 10 -36.5 19.5t-37.5 9.5q-16 0 -26 -8 t-21 -23q-16 -29 -39 -28h-63q-14 0 -23.5 10t-9.5 22z" />
<glyph unicode="&#x400;" horiz-adv-x="1239" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h842q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-698v-457h653q23 0 36 -13.5t13 -35.5v-70q0 -20 -13.5 -33.5t-35.5 -13.5h-653v-473h714q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-858 q-20 0 -32.5 13.5t-12.5 33.5zM338 1778q0 39 39 39h146q27 0 41 -7.5t34 -29.5l152 -185q10 -10 10 -26q0 -33 -33 -33h-75q-23 0 -37.5 4t-30.5 19l-235 194q-10 10 -11 25z" />
<glyph unicode="&#x401;" horiz-adv-x="1239" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h842q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-698v-457h653q23 0 36 -13.5t13 -35.5v-70q0 -20 -13.5 -33.5t-35.5 -13.5h-653v-473h714q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-858 q-20 0 -32.5 13.5t-12.5 33.5zM389 1688v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM731 1688v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5 t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x402;" horiz-adv-x="1454" d="M51 1317v69q0 20 13.5 34t33.5 14h926q23 0 35 -12.5t12 -35.5v-69q0 -20 -13.5 -32.5t-33.5 -12.5h-459v-445q43 61 148.5 91t218.5 30q219 0 331.5 -108.5t112.5 -319.5v-74q0 -205 -127 -325.5t-385 -120.5h-14q-20 0 -33.5 13.5t-13.5 33.5v70q0 20 13 33.5t34 13.5 h14q178 0 249 74.5t71 207.5v58q0 135 -70 199.5t-217 64.5q-131 0 -231.5 -48t-100.5 -167v-506q0 -20 -13 -33.5t-34 -13.5h-96q-21 0 -35 13.5t-14 33.5v1225h-275q-20 0 -33.5 12t-13.5 33z" />
<glyph unicode="&#x403;" horiz-adv-x="1067" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h787q23 0 35 -13.5t12 -36.5v-75q0 -20 -12.5 -33.5t-34.5 -13.5h-639v-1215q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5t-12.5 33.5zM571 1569q0 16 10 26l152 185q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25 l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#x404;" horiz-adv-x="1306" d="M119 715l2 125q10 317 158.5 465.5t394.5 148.5q223 0 365.5 -98.5t162.5 -261.5v-4q0 -16 -13 -27.5t-30 -11.5h-102q-20 0 -30.5 9t-18.5 32q-20 86 -104.5 140t-227.5 54q-174 0 -266.5 -109.5t-92.5 -334.5v-41h504q20 0 33.5 -14.5t13.5 -34.5v-66q0 -20 -13 -34.5 t-34 -14.5h-504v-47q0 -209 94.5 -326t264.5 -117q143 0 227 53.5t107 139.5q10 25 19 34t30 9h102q18 0 31.5 -12.5t11.5 -30.5q-12 -109 -82.5 -189.5t-186.5 -125.5t-261 -45q-248 0 -395.5 148t-157.5 466z" />
<glyph unicode="&#x405;" horiz-adv-x="1269" d="M88 360q0 16 12.5 28.5t30.5 12.5h100q37 0 52 -41q18 -86 105 -148t249 -62q174 0 260 61t86 170q0 70 -42 115t-125 78.5t-249 82.5q-158 45 -251 96.5t-140 126t-47 189.5q0 111 59.5 198t172 137t266.5 50q160 0 273.5 -56.5t173 -141.5t63.5 -171q0 -18 -12.5 -30.5 t-30.5 -12.5h-103q-16 0 -30.5 10.5t-20.5 30.5q-12 88 -98 145.5t-215 57.5q-139 0 -221 -54t-82 -161q0 -72 37.5 -117t116.5 -79.5t231 -79.5q174 -49 273 -98.5t148.5 -122t49.5 -187.5q0 -193 -146.5 -300t-396.5 -107q-168 0 -292 53t-188.5 140t-68.5 187z" />
<glyph unicode="&#x406;" horiz-adv-x="555" d="M180 47v1339q0 20 12.5 34t32.5 14h103q23 0 35 -12.5t12 -35.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x407;" horiz-adv-x="555" d="M8 1688v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM180 47v1339q0 20 12.5 34t32.5 14h103q23 0 35 -12.5t12 -35.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5 t-12.5 33.5zM350 1688v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x408;" horiz-adv-x="1294" d="M80 412q0 18 12 31.5t31 13.5h104q45 0 52 -47q12 -133 102 -196.5t225 -63.5q156 0 241 102t85 276v734h-731q-20 0 -33.5 13t-13.5 34v77q0 20 13 34t34 14h878q23 0 36 -13.5t13 -36.5v-860q0 -164 -62 -286.5t-181 -190t-281 -67.5q-223 0 -369.5 109.5t-154.5 322.5 z" />
<glyph unicode="&#x409;" horiz-adv-x="2015" d="M47 51v84q0 18 11.5 31.5t31.5 15.5q133 18 202.5 197.5t69.5 564.5v442q0 23 13.5 35.5t34.5 12.5h751q20 0 33.5 -13.5t13.5 -34.5v-499h267q223 0 342.5 -112.5t119.5 -325.5q0 -119 -52 -221.5t-156.5 -165t-253.5 -62.5h-414q-20 0 -32.5 13.5t-12.5 33.5v1208h-465 v-335q0 -330 -47 -527.5t-146.5 -290t-263.5 -100.5q-20 -2 -33.5 12.5t-13.5 36.5zM1208 166h248q133 0 209 78t76 209q0 268 -285 268h-248v-555z" />
<glyph unicode="&#x40a;" horiz-adv-x="2052" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h99q23 0 36 -13.5t13 -36.5v-569h680v569q0 23 12 36.5t35 13.5h98q20 0 33.5 -13.5t13.5 -34.5v-499h266q223 0 343 -112.5t120 -325.5q0 -119 -51 -221.5t-155.5 -165t-256.5 -62.5h-411q-20 0 -33.5 13.5t-13.5 33.5v590h-680 v-590q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5t-12.5 33.5zM1245 166h250q133 0 208 78t75 209q0 268 -283 268h-250v-555z" />
<glyph unicode="&#x40b;" horiz-adv-x="1490" d="M51 1317v69q0 20 13.5 34t33.5 14h926q23 0 35 -12.5t12 -35.5v-69q0 -20 -13.5 -32.5t-33.5 -12.5h-459v-453q43 61 149.5 92t217.5 31q219 0 331.5 -109.5t112.5 -318.5v-467q0 -20 -13 -33.5t-34 -13.5h-96q-23 0 -36 13.5t-13 33.5v451q0 135 -70 199.5t-217 64.5 q-127 0 -229.5 -48t-102.5 -169v-498q0 -20 -13 -33.5t-34 -13.5h-96q-21 0 -35 13.5t-14 33.5v1225h-275q-20 0 -33.5 12t-13.5 33z" />
<glyph unicode="&#x40c;" horiz-adv-x="1304" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h99q23 0 36 -13.5t13 -36.5v-569l235 -2l367 588q23 33 72 33h102q20 0 30.5 -11.5t10.5 -29.5q0 -10 -10 -27l-394 -627l435 -667q4 -7 4 -23q0 -20 -13.5 -34.5t-33.5 -14.5h-107q-35 0 -53 31l-395 606h-250v-590 q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5t-12.5 33.5zM549 1569q0 16 10 26l152 185q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#x40d;" horiz-adv-x="1515" d="M180 49v1335q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-1071l772 1092q16 29 49 29h96q20 0 33.5 -13.5t13.5 -36.5v-1337q0 -20 -13 -33.5t-34 -13.5h-98q-20 0 -34.5 13.5t-14.5 33.5v1061l-770 -1079q-16 -29 -49 -29h-99q-20 0 -32.5 13.5 t-12.5 35.5zM430 1778q0 39 39 39h146q27 0 41 -7.5t34 -29.5l152 -185q10 -10 10 -26q0 -33 -33 -33h-75q-23 0 -37.5 4t-30.5 19l-235 194q-10 10 -11 25z" />
<glyph unicode="&#x40e;" horiz-adv-x="1200" d="M27 1391q0 18 13 30.5t30 12.5h108q29 0 49 -33l435 -789l344 789q12 33 51 33h102q17 0 29 -12.5t12 -28.5q0 -12 -6 -29l-418 -946q-72 -162 -126 -247t-131 -128t-195 -43h-82q-20 0 -32.5 13.5t-12.5 33.5v80q0 23 12 36t33 13h73q80 0 135.5 51.5t114.5 171.5 l-532 969q-6 12 -6 23zM301 1769q0 16 10 27.5t29 11.5h65q18 0 28.5 -11t10.5 -28q0 -129 172 -129q170 0 170 129q0 16 10.5 27.5t28.5 11.5h66q18 0 28.5 -11t10.5 -28q0 -111 -72 -182t-242 -71t-242.5 71.5t-72.5 181.5z" />
<glyph unicode="&#x40f;" horiz-adv-x="1458" d="M180 47v1339q0 20 12.5 34t32.5 14h99q23 0 36 -13.5t13 -34.5v-1208h712v1208q0 20 13.5 34t34.5 14h98q23 0 36 -13.5t13 -34.5v-1339q0 -20 -14.5 -33.5t-34.5 -13.5h-406v-240q0 -20 -13 -33.5t-36 -13.5h-98q-21 0 -33 13.5t-12 33.5v240h-408q-20 0 -32.5 13.5 t-12.5 33.5z" />
<glyph unicode="&#x410;" horiz-adv-x="1368" d="M41 41l4 22l508 1326q16 45 65 45h132q49 0 65 -45l508 -1326l4 -22q0 -16 -13.5 -28.5t-29.5 -12.5h-96q-20 0 -33.5 11.5t-17.5 23.5l-115 295h-678l-113 -295q-16 -35 -51 -35h-98q-16 0 -28.5 12.5t-12.5 28.5zM403 500h562l-281 735z" />
<glyph unicode="&#x411;" horiz-adv-x="1308" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h887q23 0 36 -13.5t13 -36.5v-75q0 -23 -13 -35t-36 -12h-739v-422h411q213 0 334 -112t121 -302q0 -106 -51 -203.5t-153.5 -160t-250.5 -62.5h-559q-20 0 -32.5 13.5t-12.5 33.5zM373 162h387q131 0 206.5 76.5t75.5 191.5 t-71.5 183.5t-210.5 68.5h-387v-520z" />
<glyph unicode="&#x412;" horiz-adv-x="1366" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h559q217 0 328 -104.5t111 -272.5q0 -115 -53.5 -192t-125.5 -111q88 -43 147.5 -133.5t59.5 -206.5q0 -115 -52 -209t-154.5 -149.5t-243.5 -55.5h-576q-20 0 -32.5 13.5t-12.5 33.5zM373 162h401q131 0 206 70.5t75 181.5 q0 113 -75 182.5t-206 69.5h-401v-504zM373 825h381q133 0 202.5 60.5t69.5 169.5q0 106 -68.5 161.5t-203.5 55.5h-381v-447z" />
<glyph unicode="&#x413;" horiz-adv-x="1067" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h787q23 0 35 -13.5t12 -36.5v-75q0 -20 -12.5 -33.5t-34.5 -13.5h-639v-1215q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x414;" horiz-adv-x="1441" d="M47 131q0 20 12.5 33.5t32.5 13.5h45q98 0 148.5 186.5t50.5 579.5v442q0 20 13 34t34 14h793q23 0 35 -13.5t12 -36.5v-1206h100q23 0 36 -13t13 -34v-369q0 -23 -13 -35t-36 -12h-96q-20 0 -33.5 12.5t-13.5 34.5v238h-940v-238q0 -20 -13.5 -33.5t-36.5 -13.5h-98 q-20 0 -32.5 13.5t-12.5 33.5v369zM391 178h639v1077h-504v-335q0 -289 -33.5 -470.5t-101.5 -271.5z" />
<glyph unicode="&#x415;" horiz-adv-x="1239" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h842q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-698v-457h653q23 0 36 -13.5t13 -35.5v-70q0 -20 -13.5 -33.5t-35.5 -13.5h-653v-473h714q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-858 q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x416;" horiz-adv-x="2021" d="M80 49q0 16 4 23l434 667l-393 627q-10 16 -10 27q0 41 43 41h98q47 0 70 -33l366 -588l223 2v571q0 20 13.5 34t34.5 14h96q20 0 33.5 -13.5t13.5 -34.5v-571l219 -2l367 588q23 33 69 33h103q43 0 43 -41q0 -10 -11 -27l-393 -627l434 -667q5 -7 5 -23 q0 -20 -13.5 -34.5t-34.5 -14.5h-106q-20 0 -31.5 8t-21.5 23l-396 606h-233v-592q0 -20 -13.5 -32.5t-33.5 -12.5h-96q-20 0 -34 13.5t-14 33.5v590h-235l-393 -606q-10 -14 -21.5 -22.5t-32.5 -8.5h-106q-20 0 -33.5 14.5t-13.5 34.5z" />
<glyph unicode="&#x417;" horiz-adv-x="1265" d="M80 342v4q0 16 12 26.5t31 10.5h94q23 0 37 -9t22 -36q25 -88 110 -139.5t220 -51.5q147 0 248.5 71t101.5 188q0 121 -88 188.5t-239 67.5h-148q-20 0 -32.5 13t-12.5 36v65q0 23 12.5 36t32.5 13h140q133 0 210.5 58.5t77.5 177.5q0 100 -86 162.5t-211 62.5 q-131 0 -215 -47t-104 -147q-10 -41 -49 -41h-101q-18 0 -32.5 12t-12.5 31q16 164 148.5 262t367.5 98q156 0 266.5 -53t167 -141.5t56.5 -188.5t-39 -183t-125 -134q106 -53 159.5 -144.5t53.5 -208.5q0 -125 -66.5 -220t-190.5 -148t-288 -53q-159 0 -275 48t-179.5 130 t-73.5 184z" />
<glyph unicode="&#x418;" horiz-adv-x="1515" d="M180 49v1335q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-1071l772 1092q16 29 49 29h96q20 0 33.5 -13.5t13.5 -36.5v-1337q0 -20 -13 -33.5t-34 -13.5h-98q-20 0 -34.5 13.5t-14.5 33.5v1061l-770 -1079q-16 -29 -49 -29h-99q-20 0 -32.5 13.5 t-12.5 35.5z" />
<glyph unicode="&#x419;" horiz-adv-x="1515" d="M180 49v1335q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-1071l772 1092q16 29 49 29h96q20 0 33.5 -13.5t13.5 -36.5v-1337q0 -20 -13 -33.5t-34 -13.5h-98q-20 0 -34.5 13.5t-14.5 33.5v1061l-770 -1079q-16 -29 -49 -29h-99q-20 0 -32.5 13.5 t-12.5 35.5zM436 1769q0 16 10 27.5t29 11.5h65q18 0 28.5 -11t10.5 -28q0 -129 172 -129q170 0 170 129q0 16 10.5 27.5t28.5 11.5h66q18 0 28.5 -11t10.5 -28q0 -111 -72 -182t-242 -71t-242.5 71.5t-72.5 181.5z" />
<glyph unicode="&#x41a;" horiz-adv-x="1304" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h99q23 0 36 -13.5t13 -36.5v-569l235 -2l367 588q23 33 72 33h102q20 0 30.5 -11.5t10.5 -29.5q0 -10 -10 -27l-394 -627l435 -667q4 -7 4 -23q0 -20 -13.5 -34.5t-33.5 -14.5h-107q-35 0 -53 31l-395 606h-250v-590 q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x41b;" horiz-adv-x="1437" d="M47 51v84q0 18 11.5 31.5t31.5 15.5q133 18 202.5 197.5t69.5 564.5v442q0 23 13.5 35.5t34.5 12.5h800q23 0 36.5 -13.5t13.5 -36.5v-1337q0 -20 -14.5 -33.5t-35.5 -13.5h-98q-20 0 -32.5 13.5t-12.5 33.5v1208h-516v-335q0 -330 -47 -526.5t-146.5 -289t-263.5 -102.5 q-20 -2 -33.5 12.5t-13.5 36.5z" />
<glyph unicode="&#x41c;" horiz-adv-x="1624" d="M180 47v1337q0 23 12.5 36.5t34.5 13.5h97q37 0 51 -31l436 -838l440 838q16 31 49 31h97q23 0 35 -13.5t12 -36.5v-1337q0 -20 -13.5 -33.5t-33.5 -13.5h-94q-20 0 -33 13.5t-13 33.5v1018l-350 -678q-20 -43 -65 -43h-62q-43 0 -65 43l-348 678v-1018 q0 -20 -13.5 -33.5t-34.5 -13.5h-92q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x41d;" horiz-adv-x="1458" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h99q23 0 36 -13.5t13 -36.5v-569h712v569q0 23 13.5 36.5t34.5 13.5h98q23 0 36 -13.5t13 -36.5v-1337q0 -20 -14.5 -33.5t-34.5 -13.5h-98q-20 0 -34 13.5t-14 33.5v590h-712v-590q0 -20 -14.5 -33.5t-34.5 -13.5h-99 q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x41e;" horiz-adv-x="1382" d="M125 717q0 113 2 172q8 279 158.5 422t406.5 143t405.5 -143.5t157.5 -421.5q4 -119 5 -172q0 -51 -5 -174q-8 -283 -152.5 -423t-410.5 -140t-411.5 140t-153.5 423q-2 61 -2 174zM322 717q0 -102 2 -164q6 -205 104 -304t264 -99q164 0 262.5 99t106.5 304q4 123 4 164 q0 45 -4 164q-8 205 -106.5 305t-262.5 100t-263 -100.5t-105 -304.5q-2 -59 -2 -164z" />
<glyph unicode="&#x41f;" horiz-adv-x="1458" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h1006q23 0 36 -13.5t13 -36.5v-1337q0 -20 -14.5 -33.5t-34.5 -13.5h-98q-20 0 -34 13.5t-14 33.5v1208h-712v-1208q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x420;" horiz-adv-x="1312" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h518q231 0 363.5 -112t132.5 -325t-132 -323.5t-364 -110.5h-368v-516q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5t-12.5 33.5zM375 729h358q309 0 309 268q0 131 -76.5 201t-232.5 70h-358v-539z" />
<glyph unicode="&#x421;" horiz-adv-x="1361" d="M123 715q0 127 2 184q10 258 152.5 406.5t414.5 148.5q182 0 309 -64.5t191.5 -169t71.5 -223.5q0 -18 -13.5 -29.5t-31.5 -11.5h-103q-20 0 -32.5 10.5t-18.5 34.5q-35 158 -126 221.5t-247 63.5q-356 0 -370 -397q-2 -55 -3 -170q0 -115 3 -174q14 -395 370 -395 q154 0 246 63t127 219q6 25 18.5 35t32.5 10h103q18 0 31.5 -11t13.5 -30q-6 -119 -71 -223t-192 -168.5t-309 -64.5q-272 0 -414.5 148t-152.5 407q-2 55 -2 180z" />
<glyph unicode="&#x422;" horiz-adv-x="1181" d="M51 1305v79q0 23 13.5 36.5t33.5 13.5h983q23 0 35 -13.5t12 -36.5v-79q0 -20 -13 -34t-34 -14h-393v-1210q0 -20 -14.5 -33.5t-34.5 -13.5h-98q-21 0 -34 13.5t-13 33.5v1210h-396q-20 0 -33.5 13.5t-13.5 34.5z" />
<glyph unicode="&#x423;" horiz-adv-x="1200" d="M27 1391q0 18 13 30.5t30 12.5h108q29 0 49 -33l435 -789l344 789q12 33 51 33h102q17 0 29 -12.5t12 -28.5q0 -12 -6 -29l-418 -946q-72 -162 -126 -247t-131 -128t-195 -43h-82q-20 0 -32.5 13.5t-12.5 33.5v80q0 23 12 36t33 13h73q80 0 135.5 51.5t114.5 171.5 l-532 969q-6 12 -6 23z" />
<glyph unicode="&#x424;" horiz-adv-x="1744" d="M125 733q0 57 2 84q14 260 184 395.5t465 135.5v141q0 20 13.5 33.5t33.5 13.5h99q23 0 35 -12.5t12 -34.5v-141q295 0 466 -135.5t183 -395.5q4 -53 4 -72q0 -18 -4 -71q-23 -268 -183.5 -398.5t-465.5 -130.5v-163q0 -23 -12.5 -35.5t-34.5 -12.5h-99q-20 0 -33.5 13.5 t-13.5 34.5v163q-303 0 -462.5 130.5t-186.5 398.5q-2 18 -2 59zM322 756q0 -45 2 -72q20 -371 452 -371v865q-209 0 -322.5 -89.5t-129.5 -281.5q-2 -18 -2 -51zM969 313q434 0 454 371q2 25 2 72q0 35 -2 51q-20 193 -132.5 282t-321.5 89v-865z" />
<glyph unicode="&#x425;" horiz-adv-x="1292" d="M51 41q0 14 8 27l469 661l-446 637q-8 12 -8 27q0 16 12 28.5t29 12.5h116q29 0 54 -35l366 -518l363 518q20 35 53 35h109q16 0 28.5 -12.5t12.5 -28.5q0 -14 -9 -27l-442 -639l469 -659q8 -12 8 -27q0 -16 -13 -28.5t-30 -12.5h-117q-31 0 -53 33l-387 540l-385 -540 q-23 -33 -53 -33h-113q-16 0 -28.5 12.5t-12.5 28.5z" />
<glyph unicode="&#x426;" horiz-adv-x="1499" d="M180 49v1337q0 20 12.5 34t32.5 14h99q23 0 36 -13.5t13 -34.5v-1208h712v1208q0 20 13.5 34t34.5 14h98q23 0 36 -13.5t13 -34.5v-1208h100q20 0 33.5 -13t13.5 -34v-371q0 -23 -12 -35t-35 -12h-98q-20 0 -33.5 13.5t-13.5 33.5v240h-1010q-20 0 -32.5 13.5t-12.5 35.5 z" />
<glyph unicode="&#x427;" horiz-adv-x="1335" d="M115 915v471q0 20 13 34t34 14h98q23 0 36 -13.5t13 -34.5v-458q0 -143 74 -209t248 -66q125 0 227.5 48.5t102.5 164.5v520q0 23 13 35.5t34 12.5h96q23 0 36 -12.5t13 -35.5v-1339q0 -20 -13.5 -33.5t-35.5 -13.5h-96q-21 0 -34 13.5t-13 33.5v531q-43 -51 -148.5 -78 t-214.5 -27q-483 0 -483 442z" />
<glyph unicode="&#x428;" horiz-adv-x="1957" d="M180 47v1339q0 20 12.5 34t32.5 14h99q23 0 36 -13.5t13 -34.5v-1208h510v1208q0 20 13 34t34 14h98q23 0 35 -12.5t12 -35.5v-1208h512v1208q0 23 13.5 35.5t33.5 12.5h97q23 0 36 -13.5t13 -34.5v-1339q0 -20 -14.5 -33.5t-34.5 -13.5h-1506q-20 0 -32.5 13.5 t-12.5 33.5z" />
<glyph unicode="&#x429;" horiz-adv-x="1998" d="M180 47v1339q0 20 12.5 34t32.5 14h99q23 0 36 -13.5t13 -34.5v-1208h510v1208q0 20 13 34t34 14h98q23 0 35 -12.5t12 -35.5v-1208h512v1208q0 23 13.5 35.5t33.5 12.5h97q23 0 36 -13.5t13 -34.5v-1208h100q20 0 34.5 -13t14.5 -34v-371q0 -20 -13 -33.5t-36 -13.5h-98 q-21 0 -33 13.5t-12 33.5v240h-1512q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x42a;" horiz-adv-x="1458" d="M51 1311v73q0 23 13.5 36.5t33.5 13.5h471q23 0 35 -12.5t12 -35.5v-499h302q223 0 342.5 -112.5t119.5 -325.5q0 -119 -51 -221.5t-155.5 -165t-255.5 -62.5h-447q-20 0 -33.5 13.5t-13.5 33.5v1219h-326q-20 0 -33.5 12t-13.5 33zM616 166h285q131 0 207 78t76 209 q0 268 -283 268h-285v-555z" />
<glyph unicode="&#x42b;" horiz-adv-x="1673" d="M180 47v1339q0 20 12.5 34t32.5 14h99q23 0 36 -13.5t13 -34.5v-499h266q223 0 343 -112.5t120 -325.5q0 -119 -52.5 -221.5t-157 -165t-253.5 -62.5h-414q-20 0 -32.5 13.5t-12.5 33.5zM373 166h248q133 0 208.5 78t75.5 209q0 268 -284 268h-248v-555zM1300 47v1339 q0 20 12.5 34t33.5 14h102q23 0 35 -12.5t12 -35.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-102q-21 0 -33.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x42c;" horiz-adv-x="1230" d="M180 47v1339q0 20 12.5 34t32.5 14h99q23 0 36 -13.5t13 -34.5v-499h317q223 0 343 -112.5t120 -325.5q0 -119 -52 -221.5t-156.5 -165t-254.5 -62.5h-465q-20 0 -32.5 13.5t-12.5 33.5zM373 166h299q133 0 208.5 78t75.5 209q0 268 -284 268h-299v-555z" />
<glyph unicode="&#x42d;" horiz-adv-x="1308" d="M102 340v4q0 16 13.5 27.5t29.5 11.5h103q20 0 29.5 -9t19.5 -34q23 -86 106.5 -139.5t225.5 -53.5q170 0 265 118t95 325v47h-504q-20 0 -33.5 14.5t-13.5 34.5v66q0 20 13.5 34.5t33.5 14.5h504v41q0 225 -92 334.5t-268 109.5q-141 0 -225.5 -54t-106.5 -140 q-6 -23 -16.5 -32t-30.5 -9h-103q-18 0 -31.5 12t-11.5 31q20 164 163 262t364 98q248 0 396.5 -148.5t156.5 -465.5q4 -94 4 -125t-4 -121q-8 -317 -155.5 -465.5t-397.5 -148.5q-146 0 -261.5 45t-185 126.5t-82.5 188.5z" />
<glyph unicode="&#x42e;" horiz-adv-x="1939" d="M180 47v1339q0 20 12.5 34t32.5 14h99q23 0 36 -13.5t13 -34.5v-571h307q0 29 4 86q14 248 148.5 400.5t414.5 152.5q283 0 419 -156.5t146 -408.5q4 -119 5 -172q0 -51 -5 -174q-10 -256 -145 -409.5t-420 -153.5q-282 0 -416.5 151.5t-146.5 403.5q-4 74 -4 102h-307 v-590q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5t-12.5 33.5zM877 717q0 -111 2 -172q8 -205 105 -300t263 -95t263.5 95t105.5 300q4 123 4 172q0 53 -4 172q-8 205 -106.5 301t-262.5 96t-262 -96t-106 -301q-2 -59 -2 -172z" />
<glyph unicode="&#x42f;" horiz-adv-x="1347" d="M166 41q0 10 6 22l320 502q-143 35 -221 143.5t-78 276.5q0 119 51 221.5t155.5 165t255.5 62.5h467q21 0 33 -13.5t12 -34.5v-1339q0 -20 -12 -33.5t-33 -13.5h-98q-20 0 -34.5 13.5t-14.5 33.5v500h-285l-313 -492q-16 -29 -32.5 -42t-47.5 -13h-90q-16 0 -28.5 12.5 t-12.5 28.5zM389 981q0 -266 283 -266h303v553h-303q-131 0 -207 -78t-76 -209z" />
<glyph unicode="&#x430;" d="M78 289q0 135 110.5 221t300.5 115l308 43v59q0 211 -244 211q-94 0 -151.5 -38t-84.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 44 109.5t136 108.5t225 46q229 0 328.5 -106.5t99.5 -265.5v-666q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -32.5 13.5t-12.5 33.5v90q-45 -68 -131 -112.5t-221 -44.5q-101 0 -186 40.5t-135 111.5t-50 157zM258 303q0 -84 68.5 -129t162.5 -45q136 0 222 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5z" />
<glyph unicode="&#x431;" horiz-adv-x="1187" d="M121 469v246q2 293 78.5 439t217 211t455.5 146l56 15l10 2q14 0 26.5 -10.5t16.5 -26.5l16 -78l2 -12q0 -31 -32 -41q-25 -6 -109 -27q-233 -55 -336.5 -96t-159 -129t-61.5 -262q49 74 139 121t193 47q207 0 322.5 -126t123.5 -331q2 -20 2 -65q0 -39 -2 -56 q-8 -207 -131 -331.5t-346 -124.5q-229 0 -350 130t-131 359zM309 492l2 -46q6 -154 83 -236.5t208 -82.5q129 0 207 83t82 236q2 15 2 46q0 37 -2 55q-4 154 -78 236.5t-192 82.5q-135 0 -219.5 -83t-90.5 -236z" />
<glyph unicode="&#x432;" horiz-adv-x="1144" d="M154 47v971q0 20 13 33.5t34 13.5h424q184 0 283.5 -77t99.5 -208q0 -88 -33 -137t-103 -84q74 -31 116 -102.5t42 -155.5q0 -145 -102.5 -223t-288.5 -78h-438q-21 0 -34 13.5t-13 33.5zM334 145h289q102 0 162.5 40t60.5 118q0 82 -55.5 121t-167.5 39h-289v-318z M334 610h276q100 0 159.5 44t59.5 124q0 141 -219 142h-276v-310z" />
<glyph unicode="&#x433;" horiz-adv-x="878" d="M154 47v971q0 20 13 33.5t34 13.5h608q20 0 32.5 -13.5t12.5 -33.5v-66q0 -20 -12.5 -32.5t-32.5 -12.5h-471v-860q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x434;" horiz-adv-x="1222" d="M43 113q0 20 13.5 33.5t33.5 13.5h10q86 0 130 143t44 453v262q0 20 13.5 33.5t34.5 13.5h647q23 0 35 -12.5t12 -34.5v-856h96q23 0 35 -12.5t12 -34.5v-293q0 -23 -12 -35t-35 -12h-90q-20 0 -32.5 13t-12.5 34v178h-750v-178q0 -20 -13 -33.5t-34 -13.5h-90 q-20 0 -33.5 12t-13.5 35v291zM336 160l495 2v745h-376v-172q0 -418 -119 -575z" />
<glyph unicode="&#x435;" horiz-adv-x="1144" d="M102 535l2 67q12 221 137.5 352t333.5 131q225 0 349.5 -143t124.5 -389v-37q0 -20 -13.5 -33.5t-34.5 -13.5h-710v-18q6 -133 82 -227.5t200 -94.5q96 0 157 38t91 79q18 25 28.5 30t35.5 5h90q18 0 31.5 -10.5t13.5 -26.5q0 -45 -55.5 -108.5t-156.5 -109.5t-233 -46 q-208 0 -333 130t-138 355zM291 608h571v6q0 143 -77.5 233.5t-209.5 90.5q-133 0 -208.5 -90t-75.5 -234v-6z" />
<glyph unicode="&#x436;" horiz-adv-x="1624" d="M47 33q0 10 10 26l351 498l-310 451q-8 14 -8 24q0 14 11.5 23.5t29.5 9.5h100q33 0 54 -31l278 -411h160v395q0 23 13.5 35t33.5 12h88q20 0 33.5 -13.5t13.5 -33.5v-395h154l280 411q20 31 56 31h98q18 0 30.5 -9t12.5 -24q0 -8 -10 -24l-309 -451l350 -498 q10 -12 10 -26t-11.5 -23.5t-29.5 -9.5h-117q-18 0 -30.5 8t-24.5 23l-299 434h-160v-418q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v418h-160l-303 -434q-23 -31 -55 -31h-117q-18 0 -29.5 9t-11.5 24z" />
<glyph unicode="&#x437;" horiz-adv-x="1044" d="M88 274q0 18 12.5 29.5t30.5 11.5h88q20 0 31.5 -9t19.5 -32q23 -78 89.5 -110.5t177.5 -32.5q106 0 174.5 44t68.5 130q0 172 -198 172h-113q-20 0 -32.5 12.5t-12.5 32.5v51q0 20 12.5 32.5t32.5 12.5h100q176 0 176 142q0 74 -54 124t-171 50q-115 0 -166 -38 t-73 -124q-10 -39 -52 -39h-86q-16 0 -28.5 11.5t-12.5 27.5q2 86 49.5 156.5t140.5 113.5t228 43q141 0 232.5 -45t132.5 -112.5t41 -141.5q0 -84 -28 -135t-97 -86q88 -35 126 -103.5t38 -168.5q0 -152 -123 -232.5t-322 -80.5q-147 0 -244.5 47t-141.5 114.5t-46 132.5z " />
<glyph unicode="&#x438;" horiz-adv-x="1243" d="M154 43v975q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-750l573 766q23 31 54 31h86q16 0 30.5 -12.5t14.5 -28.5v-977q0 -20 -13.5 -33.5t-33.5 -13.5h-91q-20 0 -33.5 13.5t-13.5 33.5v750l-573 -766q-23 -31 -53 -31h-88q-16 0 -29.5 13.5t-13.5 29.5z" />
<glyph unicode="&#x439;" horiz-adv-x="1243" d="M154 43v975q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-750l573 766q23 31 54 31h86q16 0 30.5 -12.5t14.5 -28.5v-977q0 -20 -13.5 -33.5t-33.5 -13.5h-91q-20 0 -33.5 13.5t-13.5 33.5v750l-573 -766q-23 -31 -53 -31h-88q-16 0 -29.5 13.5t-13.5 29.5z M323 1460q0 16 10.5 27.5t28.5 11.5h66q18 0 28.5 -11t10.5 -28q0 -55 36.5 -92t124.5 -37q90 0 127 37t37 92q0 16 10.5 27.5t28.5 11.5h66q18 0 28.5 -11t10.5 -28q0 -111 -72 -182.5t-236 -71.5q-163 0 -234 72t-71 182z" />
<glyph unicode="&#x43a;" horiz-adv-x="1091" d="M154 47v971q0 20 13 33.5t34 13.5h88q23 0 35 -12.5t12 -34.5v-395h190l281 411q20 31 53 31h101q18 0 30.5 -9t12.5 -24q0 -4 -4.5 -11t-6.5 -13l-307 -453l348 -496q8 -14 8 -26q0 -33 -38 -33h-117q-31 0 -56 31l-301 430h-194v-414q0 -20 -13.5 -33.5t-33.5 -13.5 h-88q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x43b;" horiz-adv-x="1183" d="M51 49v72q0 18 12.5 30.5t30.5 14.5q98 2 147.5 140t49.5 450v262q0 20 13 33.5t34 13.5h643q23 0 35 -12.5t12 -34.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-20 0 -33.5 13.5t-13.5 33.5v860h-373v-172q0 -389 -90 -562t-281 -173q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x43c;" horiz-adv-x="1392" d="M154 45v981q0 14 12 26.5t29 12.5h98q33 0 49 -33l352 -657l355 657q16 33 49 33h98q16 0 28.5 -12.5t12.5 -26.5v-981q0 -20 -12.5 -32.5t-32.5 -12.5h-92q-20 0 -32.5 12.5t-12.5 32.5v686l-267 -506q-14 -29 -27 -42t-40 -13h-51q-27 0 -40.5 13.5t-27.5 41.5 l-266 494v-674q0 -20 -12.5 -32.5t-32.5 -12.5h-92q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#x43d;" horiz-adv-x="1243" d="M154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-395h565v395q0 20 13.5 33.5t33.5 13.5h90q23 0 35 -12.5t12 -34.5v-971q0 -20 -13 -33.5t-34 -13.5h-90q-20 0 -33.5 13.5t-13.5 33.5v418h-565v-418q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x43e;" horiz-adv-x="1171" d="M106 532l3 89q8 211 132 337.5t345 126.5t345 -127t132 -337q4 -45 4 -89q0 -43 -4 -88q-8 -211 -130 -337.5t-347 -126.5q-226 0 -347.5 126.5t-129.5 337.5zM293 532l2 -77q6 -156 83 -239t208 -83t207.5 83t83.5 239q2 20 2 77t-2 78q-6 156 -83 239t-208 83t-208 -83 t-83 -239z" />
<glyph unicode="&#x43f;" horiz-adv-x="1210" d="M154 47v971q0 20 13 33.5t34 13.5h807q20 0 32.5 -13.5t12.5 -33.5v-971q0 -20 -12.5 -33.5t-32.5 -13.5h-90q-20 0 -34 13.5t-14 33.5v860h-532v-860q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x440;" horiz-adv-x="1216" d="M156 -342v1360q0 23 13 35t34 12h86q23 0 35 -12.5t12 -34.5v-90q119 158 338 157q215 0 322.5 -136t113.5 -347q2 -23 2 -70t-2 -69q-6 -209 -113.5 -346t-322.5 -137q-219 0 -332 155v-477q0 -20 -13.5 -33.5t-33.5 -13.5h-92q-21 0 -34 12t-13 35zM340 526 q0 -57 2 -80q4 -125 79 -216t214 -91q147 0 215 92.5t74 241.5q2 20 2 59q0 393 -291 394q-143 0 -216 -95.5t-77 -224.5q-2 -23 -2 -80z" />
<glyph unicode="&#x441;" d="M106 532l3 82q6 223 128.5 347t342.5 124q147 0 248.5 -52t151.5 -130t54 -155q2 -18 -13 -33t-34 -15h-90q-20 0 -30.5 9.5t-20.5 33.5q-37 100 -101.5 142.5t-162.5 42.5q-129 0 -205 -80t-82 -244l-2 -72l2 -71q6 -164 82 -244t205 -80q100 0 163.5 42t100.5 143 q10 25 20.5 34t30.5 9h90q18 0 33.5 -14.5t13.5 -33.5q-4 -76 -54 -153.5t-151.5 -130.5t-248.5 -53q-220 0 -342.5 123.5t-128.5 347.5z" />
<glyph unicode="&#x442;" horiz-adv-x="1046" d="M27 952v66q0 20 13 33.5t34 13.5h899q20 0 32.5 -13.5t12.5 -33.5v-66q0 -20 -12.5 -32.5t-32.5 -12.5h-359v-860q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v860h-358q-20 0 -33.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x443;" horiz-adv-x="1118" d="M63 1022q0 18 12.5 30.5t30.5 12.5h91q33 0 49 -33l319 -758l318 758q14 33 51 33h86q16 0 29.5 -12.5t13.5 -28.5q0 -8 -12 -37l-389 -921l-19 -43q-55 -131 -99 -213t-110.5 -140.5t-157.5 -58.5h-77q-20 0 -33.5 13.5t-13.5 33.5v63q0 20 13 34t34 14h53q45 0 73.5 18 t52 59t60.5 127l35 84l-399 930q-10 27 -11 35z" />
<glyph unicode="&#x444;" horiz-adv-x="1460" d="M106 532l3 72q10 211 142 336t388 125v342q0 23 13.5 35t33.5 12h90q20 0 32.5 -13.5t12.5 -33.5v-342q258 0 389 -125t142 -336q4 -45 4 -72q0 -25 -4 -69q-10 -213 -140.5 -338t-390.5 -125v-342q0 -20 -12 -33.5t-33 -13.5h-90q-20 0 -33.5 12t-13.5 35v342 q-258 0 -389 125t-141 338zM289 532l2 -57q10 -162 95 -241.5t253 -79.5v757q-332 0 -348 -321zM821 154q168 0 253 80.5t95 240.5q2 20 2 57t-2 58q-16 322 -348 321v-757z" />
<glyph unicode="&#x445;" horiz-adv-x="1099" d="M61 43q0 18 15 39l356 463l-334 440q-16 23 -16 37q0 18 13.5 30.5t29.5 12.5h98q31 0 53 -33l277 -358l276 358q12 16 23.5 24.5t30.5 8.5h94q16 0 28.5 -12.5t12.5 -28.5q0 -20 -14 -39l-338 -444l356 -459q14 -20 14 -39q0 -18 -12 -30.5t-31 -12.5h-100q-31 0 -53 31 l-293 379l-293 -379q-12 -14 -23.5 -22.5t-29.5 -8.5h-97q-18 0 -30.5 12.5t-12.5 30.5z" />
<glyph unicode="&#x446;" horiz-adv-x="1261" d="M154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-858h532v858q0 23 13.5 35t34.5 12h90q20 0 32.5 -13.5t12.5 -33.5v-858h98q20 0 33.5 -13.5t13.5 -33.5v-326q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5v213h-813q-20 0 -33.5 13.5 t-13.5 33.5z" />
<glyph unicode="&#x447;" horiz-adv-x="1122" d="M86 670v348q0 23 13.5 35t33.5 12h92q20 0 33.5 -13.5t13.5 -33.5v-336q0 -94 53.5 -136t170.5 -42q135 0 207.5 33.5t72.5 111.5v369q0 20 13.5 33.5t33.5 13.5h97q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-97q-20 0 -33.5 13.5t-13.5 33.5v389 q-106 -100 -313 -100q-377 0 -377 334z" />
<glyph unicode="&#x448;" horiz-adv-x="1607" d="M154 47v971q0 20 13 33.5t34 13.5h86q23 0 35 -12.5t12 -34.5v-858h381v858q0 20 13 33.5t34 13.5h82q20 0 33.5 -13.5t13.5 -33.5v-858h381v858q0 20 13 33.5t34 13.5h86q23 0 35 -12.5t12 -34.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-1204q-21 0 -34 13.5t-13 33.5z " />
<glyph unicode="&#x449;" horiz-adv-x="1660" d="M154 47v971q0 20 13 33.5t34 13.5h86q23 0 35 -12.5t12 -34.5v-858h381v858q0 20 13 33.5t34 13.5h82q20 0 33.5 -13.5t13.5 -33.5v-858h381v858q0 20 13 33.5t34 13.5h86q23 0 35 -12.5t12 -34.5v-858h98q20 0 33.5 -13.5t13.5 -33.5v-326q0 -20 -13 -33.5t-34 -13.5 h-90q-20 0 -32.5 13.5t-12.5 33.5v213h-1214q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x44a;" horiz-adv-x="1163" d="M27 952v66q0 20 13 33.5t34 13.5h395q20 0 33.5 -13.5t13.5 -33.5v-301h199q201 0 299 -95.5t98 -269.5q0 -180 -104.5 -266t-319.5 -86h-309q-21 0 -34 13.5t-13 33.5v860h-258q-20 0 -33.5 12.5t-13.5 32.5zM514 150h182q129 0 186.5 46t57.5 156q0 109 -57.5 162 t-186.5 53h-182v-417z" />
<glyph unicode="&#x44b;" horiz-adv-x="1439" d="M154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-301h215q201 0 300 -96.5t99 -268.5q0 -180 -105.5 -266t-320.5 -86h-325q-21 0 -34 13.5t-13 33.5zM336 150h201q127 0 184 46t57 156q0 109 -57 162t-184 53h-201v-417zM1100 47v971q0 20 13 33.5 t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x44c;" horiz-adv-x="1019" d="M154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-301h231q203 0 301.5 -95.5t98.5 -269.5q0 -180 -104.5 -266t-319.5 -86h-344q-21 0 -34 13.5t-13 33.5zM336 150h217q127 0 184.5 46t57.5 156q0 109 -57.5 162t-184.5 53h-217v-417z" />
<glyph unicode="&#x44d;" horiz-adv-x="1130" d="M100 305v4q0 16 13.5 27.5t29.5 11.5h84q20 0 29.5 -8t19.5 -31q37 -96 102.5 -137t166.5 -41q131 0 206.5 79t79.5 241v10h-399q-20 0 -32.5 13t-12.5 32v53q0 20 12.5 32.5t32.5 12.5h399v12q-4 160 -80.5 239t-205.5 79q-100 0 -166 -41t-103 -137q-10 -23 -19 -32 t-32 -9h-80q-18 0 -31.5 13t-11.5 32q4 78 52.5 152.5t146.5 123.5t244 49q223 0 345 -122.5t130 -346.5q2 -20 2 -84q0 -61 -2 -83q-8 -223 -129 -346t-346 -123q-148 0 -246 49t-146.5 123.5t-52.5 152.5z" />
<glyph unicode="&#x44e;" horiz-adv-x="1622" d="M154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-395h221q8 209 132 335.5t345 126.5t345 -127t132 -337q5 -46 5 -89t-5 -88q-8 -211 -129.5 -337.5t-347.5 -126.5q-225 0 -347 126.5t-130 337.5v21h-221v-418q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -33.5 13.5t-13.5 33.5zM743 532l2 -77q6 -156 83 -239t208 -83t208 83t83 239q2 20 2 77t-2 78q-6 156 -83 239t-208 83t-207.5 -83t-83.5 -239z" />
<glyph unicode="&#x44f;" horiz-adv-x="1110" d="M125 41q0 20 14 39l230 336q-106 37 -160.5 118.5t-54.5 196.5q0 170 101 252t306 82h346q21 0 34 -13.5t13 -33.5v-971q0 -20 -13 -33.5t-34 -13.5h-92q-20 0 -33.5 13.5t-13.5 33.5v336h-221l-236 -352q-8 -12 -22.5 -21.5t-30.5 -9.5h-92q-16 0 -28.5 12.5t-12.5 28.5 zM328 731q0 -90 52 -144.5t157 -54.5h233v383h-217q-119 0 -172 -42t-53 -142z" />
<glyph unicode="&#x450;" horiz-adv-x="1144" d="M102 535l2 67q12 221 137.5 352t333.5 131q225 0 349.5 -143t124.5 -389v-37q0 -20 -13.5 -33.5t-34.5 -13.5h-710v-18q6 -133 82 -227.5t200 -94.5q96 0 157 38t91 79q18 25 28.5 30t35.5 5h90q18 0 31.5 -10.5t13.5 -26.5q0 -45 -55.5 -108.5t-156.5 -109.5t-233 -46 q-208 0 -333 130t-138 355zM246 1460q0 39 39 39h133q29 0 43.5 -7t32.5 -30l154 -184q10 -10 10 -27q0 -33 -33 -32h-66q-22 0 -35.5 4t-29.5 18l-238 195q-10 10 -10 24zM291 608h571v6q0 143 -77.5 233.5t-209.5 90.5q-133 0 -208.5 -90t-75.5 -234v-6z" />
<glyph unicode="&#x451;" horiz-adv-x="1144" d="M102 535l2 67q12 221 137.5 352t333.5 131q225 0 349.5 -143t124.5 -389v-37q0 -20 -13.5 -33.5t-34.5 -13.5h-710v-18q6 -133 82 -227.5t200 -94.5q96 0 157 38t91 79q18 25 28.5 30t35.5 5h90q18 0 31.5 -10.5t13.5 -26.5q0 -45 -55.5 -108.5t-156.5 -109.5t-233 -46 q-208 0 -333 130t-138 355zM291 608h571v6q0 143 -77.5 233.5t-209.5 90.5q-133 0 -208.5 -90t-75.5 -234v-6zM313 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM655 1319v106 q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x452;" horiz-adv-x="1220" d="M6 1153v43q0 20 12.5 32.5t32.5 12.5h109v166q0 20 13 33.5t34 13.5h96q20 0 32.5 -13.5t12.5 -33.5v-166h354q20 0 34 -12t14 -33v-43q0 -20 -13.5 -32.5t-34.5 -12.5h-354v-291q59 76 140 116t200 40q199 0 307.5 -127t108.5 -332v-268q0 -297 -115 -466t-387 -169h-22 q-21 0 -34.5 12t-13.5 35v66q0 20 13.5 32.5t34.5 12.5h12q135 0 205.5 60t93 161.5t22.5 257.5v254q0 147 -71.5 229t-204.5 82q-131 0 -210 -83t-79 -228v-455q0 -20 -12 -33.5t-33 -13.5h-96q-20 0 -33.5 13.5t-13.5 33.5v1061h-109q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#x453;" horiz-adv-x="878" d="M154 47v971q0 20 13 33.5t34 13.5h608q20 0 32.5 -13.5t12.5 -33.5v-66q0 -20 -12.5 -32.5t-32.5 -12.5h-471v-860q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5zM452 1251q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24 l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32z" />
<glyph unicode="&#x454;" horiz-adv-x="1130" d="M109 532l2 84q8 223 131 346t346 123q141 0 239.5 -48t147.5 -122.5t55 -154.5v-4q0 -18 -12 -29.5t-31 -11.5h-82q-18 0 -28.5 8t-20.5 31q-39 98 -103.5 139t-164.5 41q-131 0 -208 -78t-81 -240v-12h401q18 0 30.5 -12t12.5 -33v-53q0 -18 -12 -31.5t-31 -13.5h-401 v-12q4 -162 81 -240t208 -78q100 0 164.5 41t103.5 139q10 23 18.5 30t28.5 7h84q18 0 31.5 -12t11.5 -31q-6 -82 -55 -155.5t-146.5 -121.5t-240.5 -48q-226 0 -347.5 122.5t-129.5 346.5z" />
<glyph unicode="&#x455;" horiz-adv-x="1040" d="M88 229q0 20 14.5 31.5t30.5 11.5h86q12 0 19.5 -4t19.5 -18q47 -57 105.5 -90t156.5 -33q111 0 177.5 41t66.5 119q0 51 -28.5 81.5t-95.5 55.5t-198 53q-178 39 -251.5 113t-73.5 190q0 76 45 146.5t134 114.5t212 44q129 0 220 -43t137 -100t46 -96q0 -18 -13 -30.5 t-32 -12.5h-78q-29 0 -40 22q-35 39 -58.5 60.5t-69 37t-112.5 15.5q-100 0 -153.5 -42t-53.5 -112q0 -43 22.5 -72.5t86 -55t188.5 -52.5q195 -41 275.5 -116.5t80.5 -194.5q0 -88 -50 -160t-149.5 -112.5t-236.5 -40.5q-141 0 -238.5 46t-144.5 105t-47 98z" />
<glyph unicode="&#x456;" horiz-adv-x="491" d="M137 1313v106q0 20 13.5 34.5t33.5 14.5h121q20 0 34.5 -14t14.5 -35v-106q0 -20 -14 -33.5t-35 -13.5h-121q-20 0 -33.5 13t-13.5 34zM154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5 z" />
<glyph unicode="&#x457;" horiz-adv-x="491" d="M-25 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5 t-13 33.5zM317 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x458;" horiz-adv-x="516" d="M-68 -276q0 20 12.5 32.5t32.5 12.5h29q100 0 134 52t34 152v1045q0 23 13.5 35t33.5 12h92q20 0 33.5 -13.5t13.5 -33.5v-1047q0 -360 -344 -360h-39q-20 0 -32.5 13.5t-12.5 33.5v66zM156 1313v106q0 20 13 34.5t34 14.5h127q20 0 33.5 -14t13.5 -35v-106 q0 -20 -13.5 -33.5t-33.5 -13.5h-127q-21 0 -34 13t-13 34z" />
<glyph unicode="&#x459;" horiz-adv-x="1591" d="M51 51v72q0 18 12.5 29.5t30.5 15.5q106 16 159.5 147t53.5 441v262q0 20 13.5 33.5t33.5 13.5h594q20 0 33.5 -13.5t13.5 -33.5v-301h146q203 0 301 -95.5t98 -269.5q0 -180 -104.5 -266t-319.5 -86h-258q-20 0 -33.5 13.5t-13.5 33.5v860h-326v-172q0 -262 -42 -420.5 t-126 -231.5t-217 -81q-20 -2 -34.5 12.5t-14.5 36.5zM993 150h131q127 0 184.5 46t57.5 156q0 111 -56.5 163t-185.5 52h-131v-417z" />
<glyph unicode="&#x45a;" horiz-adv-x="1615" d="M154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-395h498v395q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-301h145q203 0 301.5 -95.5t98.5 -269.5q0 -180 -104.5 -266t-319.5 -86h-258q-20 0 -33.5 13.5t-13.5 33.5v418h-498v-418 q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5zM1018 150h131q127 0 184.5 46t57.5 156q0 111 -56.5 163t-185.5 52h-131v-417z" />
<glyph unicode="&#x45b;" horiz-adv-x="1259" d="M6 1153v43q0 20 13.5 32.5t33.5 12.5h109v166q0 23 13 35t34 12h94q23 0 35 -12t12 -35v-166h355q20 0 32.5 -12t12.5 -33v-43q0 -20 -12.5 -32.5t-32.5 -12.5h-355v-291q59 76 140 116t200 40q199 0 307.5 -127t108.5 -332v-467q0 -20 -13.5 -33.5t-33.5 -13.5h-96 q-20 0 -34 13.5t-14 33.5v455q0 147 -71.5 229t-206.5 82q-131 0 -209 -83t-78 -228v-455q0 -20 -13 -33.5t-34 -13.5h-94q-21 0 -34 13.5t-13 33.5v1061h-109q-20 0 -33.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x45c;" horiz-adv-x="1091" d="M154 47v971q0 20 13 33.5t34 13.5h88q23 0 35 -12.5t12 -34.5v-395h190l281 411q20 31 53 31h101q18 0 30.5 -9t12.5 -24q0 -4 -4.5 -11t-6.5 -13l-307 -453l348 -496q8 -14 8 -26q0 -33 -38 -33h-117q-31 0 -56 31l-301 430h-194v-414q0 -20 -13.5 -33.5t-33.5 -13.5 h-88q-20 0 -33.5 13.5t-13.5 33.5zM465 1251q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32z" />
<glyph unicode="&#x45d;" horiz-adv-x="1243" d="M154 43v975q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-750l573 766q23 31 54 31h86q16 0 30.5 -12.5t14.5 -28.5v-977q0 -20 -13.5 -33.5t-33.5 -13.5h-91q-20 0 -33.5 13.5t-13.5 33.5v750l-573 -766q-23 -31 -53 -31h-88q-16 0 -29.5 13.5t-13.5 29.5z M291 1460q0 39 39 39h133q29 0 43.5 -7t32.5 -30l154 -184q10 -10 10 -27q0 -33 -33 -32h-66q-22 0 -35.5 4t-29.5 18l-238 195q-10 10 -10 24z" />
<glyph unicode="&#x45e;" horiz-adv-x="1118" d="M63 1022q0 18 12.5 30.5t30.5 12.5h91q33 0 49 -33l319 -758l318 758q14 33 51 33h86q16 0 29.5 -12.5t13.5 -28.5q0 -8 -12 -37l-389 -921l-19 -43q-55 -131 -99 -213t-110.5 -140.5t-157.5 -58.5h-77q-20 0 -33.5 13.5t-13.5 33.5v63q0 20 13 34t34 14h53q45 0 73.5 18 t52 59t60.5 127l35 84l-399 930q-10 27 -11 35zM254 1460q0 16 10.5 27.5t28.5 11.5h66q18 0 28.5 -11t10.5 -28q0 -55 36.5 -92t124.5 -37q90 0 127 37t37 92q0 16 10.5 27.5t28.5 11.5h66q18 0 28.5 -11t10.5 -28q0 -111 -72 -182.5t-236 -71.5q-163 0 -234 72t-71 182z " />
<glyph unicode="&#x45f;" horiz-adv-x="1210" d="M154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-858h532v858q0 23 13.5 35t34.5 12h90q20 0 32.5 -13.5t12.5 -33.5v-971q0 -20 -12.5 -33.5t-32.5 -13.5h-314v-213q0 -20 -13 -33.5t-34 -13.5h-90q-20 0 -32.5 13.5t-12.5 33.5v213h-311 q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x462;" horiz-adv-x="1359" d="M51 1128v52q0 23 13.5 35t33.5 12h209v164q0 20 13.5 33.5t33.5 13.5h99q20 0 33.5 -13.5t13.5 -33.5v-164h393q23 0 35 -12.5t12 -34.5v-52q0 -20 -13.5 -33.5t-33.5 -13.5h-393v-194h317q223 0 343 -112.5t120 -325.5q0 -119 -51 -221.5t-155.5 -165t-256.5 -62.5h-463 q-20 0 -33.5 13.5t-13.5 33.5v1034h-209q-20 0 -33.5 13.5t-13.5 33.5zM500 166h301q133 0 208.5 78t75.5 209q0 268 -284 268h-301v-555z" />
<glyph unicode="&#x463;" horiz-adv-x="1040" d="M47 991v41q0 20 13.5 33.5t33.5 13.5h115v328q0 20 12 33.5t33 13.5h92q20 0 32.5 -13.5t12.5 -33.5v-328h285q20 0 32.5 -13t12.5 -34v-41q0 -20 -12.5 -33.5t-32.5 -13.5h-285v-227h199q203 0 301 -95.5t98 -269.5q0 -180 -105.5 -266t-318.5 -86h-311 q-20 0 -32.5 13.5t-12.5 33.5v897h-115q-20 0 -33.5 13.5t-13.5 33.5zM391 150h182q127 0 184.5 46t57.5 156q0 109 -57.5 162t-184.5 53h-182v-417z" />
<glyph unicode="&#x46a;" horiz-adv-x="1796" d="M80 51q0 8 4 21l174 469q51 139 132 193t214 57h92l-483 575q-8 10 -8 27q0 16 12 28.5t29 12.5h1306q16 0 28.5 -12.5t12.5 -28.5q0 -14 -8 -29l-491 -571h94q135 -4 217 -58.5t133 -193.5l174 -469q4 -16 4 -23q0 -20 -13 -34.5t-34 -14.5h-102q-35 0 -47 31l-162 444 q-33 86 -75 119t-126 33h-162v-580q0 -20 -13 -33.5t-34 -13.5h-98q-20 0 -33.5 13.5t-13.5 33.5v580h-162q-84 0 -127 -34t-74 -118l-164 -444q-8 -31 -47 -31h-102q-21 0 -34 15.5t-13 35.5zM510 1270l387 -467l391 467h-778z" />
<glyph unicode="&#x46b;" horiz-adv-x="1536" d="M6 33q0 12 8 26l236 367q63 98 129.5 134t152.5 36h52l-318 395q-12 12 -12 31q0 18 12.5 30.5t30.5 12.5h938q18 0 29.5 -11.5t11.5 -29.5q0 -14 -10 -29l-314 -399h47q90 0 157 -35t128 -133l236 -369q10 -12 10 -26q0 -33 -41 -33h-115q-33 0 -55 31l-205 336 q-35 57 -75 77.5t-103 20.5h-78v-418q0 -20 -12 -33.5t-33 -13.5h-90q-20 0 -33.5 13.5t-13.5 33.5v418h-76q-63 0 -103 -20.5t-75 -77.5l-205 -336q-23 -31 -55 -31h-117q-39 0 -39 33zM510 924l258 -328l258 328h-516z" />
<glyph unicode="&#x472;" horiz-adv-x="1382" d="M125 717q0 113 2 172q8 279 158.5 422t406.5 143t405.5 -143.5t157.5 -421.5q5 -119 5 -172q0 -51 -5 -174q-8 -283 -152.5 -423t-410.5 -140t-411.5 140t-153.5 423q-2 61 -2 174zM322 653q0 -63 2 -100q6 -205 104 -304t264 -99q164 0 262.5 99t106.5 304q2 37 2 100 h-741zM322 797h741l-2 84q-8 205 -106.5 305t-262.5 100t-263 -100.5t-105 -304.5z" />
<glyph unicode="&#x473;" horiz-adv-x="1171" d="M106 532l3 89q8 211 132 337.5t345 126.5t345 -127t132 -337q4 -46 4 -89t-4 -88q-8 -211 -130 -337.5t-347 -126.5t-347 126.5t-130 337.5zM295 449q6 -156 83 -239t208 -83t207.5 83t83.5 239v20h-582v-20zM295 596h582v20q-6 156 -83 239t-208 83t-208 -83t-83 -239 v-20z" />
<glyph unicode="&#x474;" horiz-adv-x="1349" d="M70 1389q0 18 11 31.5t30 13.5h98q39 0 55 -43l406 -1168l264 844q63 207 144 295t212 88h27q33 0 33 -47v-86q0 -20 -11.5 -33.5t-27.5 -13.5h-15q-63 0 -103 -54.5t-87 -201.5l-301 -961q-18 -53 -74 -53h-125q-56 0 -74 53l-460 1317q-2 6 -2 19z" />
<glyph unicode="&#x475;" horiz-adv-x="1148" d="M66 1022q0 18 12 30.5t28 12.5h95q33 0 49 -33l311 -817l219 606q49 131 109.5 186.5t177.5 57.5h29q20 0 33.5 -13.5t13.5 -33.5v-64q0 -20 -13.5 -33.5t-33.5 -13.5q-59 0 -94 -32.5t-66 -112.5l-262 -711q-18 -51 -76 -51h-80q-53 0 -76 51l-374 950q-2 6 -2 21z" />
<glyph unicode="&#x490;" horiz-adv-x="1067" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h641v239q0 20 13.5 33.5t33.5 13.5h99q22 0 34.5 -12t12.5 -35v-364q0 -20 -12.5 -33.5t-34.5 -13.5h-639v-1215q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x491;" horiz-adv-x="878" d="M154 47v971q0 20 13 33.5t34 13.5h471v213q0 20 13 33.5t34 13.5h90q20 0 32.5 -13.5t12.5 -33.5v-326q0 -20 -12.5 -32.5t-32.5 -12.5h-471v-860q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x492;" horiz-adv-x="1099" d="M20 686v47q0 23 13.5 35t34.5 12h112v604q0 23 12.5 36.5t32.5 13.5h787q22 0 34.5 -13.5t12.5 -36.5v-75q0 -20 -12.5 -33.5t-34.5 -13.5h-643v-482h295q23 0 35 -12t12 -35v-47q0 -20 -13.5 -33.5t-33.5 -13.5h-295v-592q0 -20 -13.5 -33.5t-36.5 -13.5h-94 q-20 0 -32.5 13.5t-12.5 33.5v592h-112q-20 0 -34 13.5t-14 33.5z" />
<glyph unicode="&#x493;" horiz-adv-x="878" d="M2 502v47q0 20 13.5 33.5t33.5 13.5h105v422q0 20 13 33.5t34 13.5h608q20 0 32.5 -13.5t12.5 -33.5v-66q0 -20 -12.5 -32.5t-32.5 -12.5h-471v-311h244q20 0 33.5 -13.5t13.5 -33.5v-47q0 -20 -13.5 -33.5t-33.5 -13.5h-244v-408q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -33.5 13.5t-13.5 33.5v408h-105q-20 0 -33.5 13t-13.5 34z" />
<glyph unicode="&#x494;" horiz-adv-x="1257" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h787q23 0 35 -13.5t12 -36.5v-75q0 -20 -12.5 -33.5t-34.5 -13.5h-639v-482q111 129 360 129q219 0 333 -109.5t114 -318.5v-192q0 -207 -127 -328t-387 -121h-11q-20 0 -33.5 13.5t-13.5 33.5v72q0 20 13.5 33.5t33.5 13.5h11 q178 0 248.5 75t70.5 208v176q0 139 -68.5 204.5t-218.5 65.5q-133 0 -229 -55t-96 -199v-434q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x495;" horiz-adv-x="1036" d="M154 47v971q0 20 13 33.5t34 13.5h608q20 0 32.5 -13.5t12.5 -33.5v-66q0 -20 -12.5 -32.5t-32.5 -12.5h-479v-346q51 43 115.5 63.5t152.5 20.5q190 0 294.5 -127t104.5 -332v-43q0 -250 -114.5 -391t-386.5 -141h-17q-20 0 -33.5 12t-13.5 35v66q0 20 13.5 32.5 t33.5 12.5h13q135 0 206.5 48t94 128t22.5 200v29q0 147 -68.5 229t-197.5 82q-219 0 -219 -129v-309q0 -20 -12.5 -33.5t-32.5 -13.5h-84q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x496;" horiz-adv-x="2062" d="M80 49q0 16 4 23l434 667l-393 627q-10 16 -10 27q0 41 43 41h98q47 0 70 -33l366 -588l223 2v571q0 20 13.5 34t34.5 14h96q20 0 33.5 -13.5t13.5 -34.5v-571l219 -2l367 588q23 33 69 33h103q43 0 43 -41q0 -10 -11 -27l-393 -627l365 -561h121q22 0 35.5 -13t13.5 -34 v-371q0 -23 -13.5 -35t-35.5 -12h-97q-20 0 -33.5 12.5t-13.5 34.5v240h-82l-2 4q-14 8 -26 27l-396 606h-233v-592q0 -20 -13.5 -32.5t-33.5 -12.5h-96q-20 0 -34 13.5t-14 33.5v590h-235l-393 -606q-10 -14 -21.5 -22.5t-32.5 -8.5h-106q-20 0 -33.5 14.5t-13.5 34.5z" />
<glyph unicode="&#x497;" horiz-adv-x="1669" d="M47 33q0 10 10 26l351 498l-310 451q-8 14 -8 24q0 14 11.5 23.5t29.5 9.5h100q33 0 54 -31l278 -411h160v395q0 23 13.5 35t33.5 12h88q20 0 33.5 -13.5t13.5 -33.5v-395h154l280 411q20 31 56 31h98q18 0 30.5 -9t12.5 -24q0 -8 -10 -24l-309 -451l280 -397h119 q20 0 33.5 -13.5t13.5 -33.5v-326q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-20 0 -33.5 13.5t-13.5 33.5v213h-76v2q-18 4 -39 29l-299 434h-160v-418q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v418h-160l-303 -434q-23 -31 -55 -31h-117q-18 0 -29.5 9 t-11.5 24z" />
<glyph unicode="&#x498;" horiz-adv-x="1265" d="M80 342v4q0 16 12 26.5t31 10.5h94q23 0 37 -9t22 -36q25 -88 110 -139.5t220 -51.5q147 0 248.5 71t101.5 188q0 121 -88 188.5t-239 67.5h-148q-20 0 -32.5 13t-12.5 36v65q0 23 12.5 36t32.5 13h140q133 0 210.5 58.5t77.5 177.5q0 100 -86 162.5t-211 62.5 q-131 0 -215 -47t-104 -147q-10 -41 -49 -41h-101q-18 0 -32.5 12t-12.5 31q16 164 148.5 262t367.5 98q156 0 266.5 -53t167 -141.5t56.5 -188.5t-39 -183t-125 -134q106 -53 159.5 -144.5t53.5 -208.5q0 -161 -110.5 -272t-303.5 -139v-230q0 -23 -12 -35t-35 -12h-98 q-20 0 -33.5 13.5t-13.5 33.5v222q-211 14 -333 113.5t-134 246.5z" />
<glyph unicode="&#x499;" horiz-adv-x="1044" d="M88 274q0 18 12.5 29.5t30.5 11.5h88q20 0 31.5 -9t19.5 -32q23 -78 89.5 -110.5t177.5 -32.5q106 0 174.5 44t68.5 130q0 172 -198 172h-113q-20 0 -32.5 12.5t-12.5 32.5v51q0 20 12.5 32.5t32.5 12.5h100q176 0 176 142q0 74 -54 124t-171 50q-115 0 -166 -38 t-73 -124q-10 -39 -52 -39h-86q-16 0 -28.5 11.5t-12.5 27.5q2 86 49.5 156.5t140.5 113.5t228 43q141 0 232.5 -45t132.5 -112.5t41 -141.5q0 -84 -28 -135t-97 -86q88 -35 126 -103.5t38 -168.5q0 -125 -86 -203t-234 -102v-201q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -33.5 13.5t-13.5 33.5v195q-127 10 -210 57t-122 111.5t-41 123.5z" />
<glyph unicode="&#x49a;" horiz-adv-x="1368" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h99q23 0 36 -13.5t13 -36.5v-569l235 -2l367 588q23 33 72 33h102q20 0 30.5 -11.5t10.5 -29.5q0 -10 -10 -27l-394 -627l365 -561h145q23 0 36.5 -13t13.5 -34v-371q0 -23 -13.5 -35t-36.5 -12h-96q-20 0 -33.5 12.5t-13.5 34.5 v240h-82q-35 0 -53 31l-395 606h-250v-590q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x49b;" horiz-adv-x="1153" d="M154 47v971q0 20 13 33.5t34 13.5h88q23 0 35 -12.5t12 -34.5v-395h190l281 411q20 31 53 31h101q18 0 30.5 -9t12.5 -24q0 -4 -4.5 -11t-6.5 -13l-307 -453l277 -395h137q20 0 33.5 -13.5t13.5 -33.5v-326q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-20 0 -33.5 13.5 t-13.5 33.5v213h-76q-31 0 -56 31l-301 430h-194v-414q0 -20 -13.5 -33.5t-33.5 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x49c;" horiz-adv-x="1333" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h99q23 0 36 -13.5t13 -36.5v-579h139v201q0 20 12.5 33.5t32.5 13.5h12q20 0 33.5 -13.5t13.5 -33.5v-203h13l375 598q23 33 69 33h103q41 0 41 -41q0 -12 -9 -27l-395 -627l434 -667q4 -7 4 -23q0 -20 -13 -34.5t-32 -14.5h-108 q-35 0 -54 31l-403 616h-25v-194q0 -20 -13 -33.5t-34 -13.5h-12q-20 0 -32.5 13t-12.5 34v194h-139v-600q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x49d;" horiz-adv-x="1122" d="M154 47v971q0 20 13 33.5t34 13.5h88q23 0 35 -12.5t12 -34.5v-402h106v156q0 20 13.5 33.5t33.5 13.5h11q20 0 33.5 -13t13.5 -34v-156h6l285 418q20 31 53 31h100q18 0 30.5 -9t12.5 -24q0 -4 -4 -11t-6 -13l-307 -453l348 -496q8 -14 8 -26q0 -33 -39 -33h-116 q-31 0 -56 31l-305 436h-10v-172q0 -20 -13.5 -33.5t-33.5 -13.5h-11q-20 0 -33.5 13t-13.5 34v172h-106v-420q0 -20 -13.5 -33.5t-33.5 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x4a0;" horiz-adv-x="1619" d="M51 1305v79q0 23 13.5 36.5t33.5 13.5h539q23 0 36 -13.5t13 -36.5v-569l236 -2l368 588q23 33 70 33h102q41 0 41 -41q0 -12 -8 -27l-395 -627l434 -667q4 -7 4 -23q0 -20 -13.5 -34.5t-31.5 -14.5h-109q-35 0 -53 31l-399 606h-246v-590q0 -20 -13.5 -33.5t-35.5 -13.5 h-96q-20 0 -33.5 13.5t-13.5 33.5v1210h-396q-20 0 -33.5 13.5t-13.5 34.5z" />
<glyph unicode="&#x4a1;" horiz-adv-x="1335" d="M27 952v66q0 20 13 33.5t34 13.5h458q23 0 35.5 -12.5t12.5 -34.5v-395h190l281 411q20 31 53 31h100q18 0 30.5 -9t12.5 -24q0 -8 -10 -24l-309 -453l350 -496q10 -16 10 -26q0 -14 -11 -23.5t-30 -9.5h-117q-31 0 -55 31l-301 430h-194v-414q0 -20 -13.5 -33.5 t-34.5 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v860h-323q-20 0 -33.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x4a2;" horiz-adv-x="1499" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h99q23 0 36 -13.5t13 -36.5v-569h712v569q0 23 13.5 36.5t34.5 13.5h98q23 0 36 -13.5t13 -36.5v-1206h98q23 0 36 -13t13 -34v-371q0 -23 -13 -35t-36 -12h-96q-20 0 -33.5 12.5t-13.5 34.5v240h-102q-20 0 -34 13.5t-14 33.5v590 h-712v-590q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x4a3;" horiz-adv-x="1294" d="M154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-395h565v395q0 20 13.5 33.5t33.5 13.5h90q23 0 35 -12.5t12 -34.5v-858h97q20 0 33.5 -13.5t13.5 -33.5v-326q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-20 0 -33.5 13.5t-13.5 33.5v213h-97 q-20 0 -33.5 13.5t-13.5 33.5v418h-565v-418q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x4a4;" horiz-adv-x="1888" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h99q23 0 36 -13.5t13 -36.5v-569h712v569q0 23 13.5 36.5t34.5 13.5h700q22 0 35.5 -13.5t13.5 -36.5v-75q0 -20 -13.5 -33.5t-35.5 -13.5h-553v-1215q0 -20 -14.5 -33.5t-34.5 -13.5h-98q-20 0 -34 13.5t-14 33.5v590h-712v-590 q0 -20 -14.5 -33.5t-34.5 -13.5h-99q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x4a5;" horiz-adv-x="1536" d="M154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-395h565v395q0 20 13.5 33.5t33.5 13.5h516q21 0 34 -13.5t13 -33.5v-66q0 -20 -13 -32.5t-34 -12.5h-379v-860q0 -20 -13 -33.5t-34 -13.5h-90q-20 0 -33.5 13.5t-13.5 33.5v418h-565v-418 q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x4aa;" horiz-adv-x="1361" d="M123 715q0 127 2 184q10 258 152.5 406.5t414.5 148.5q182 0 309 -64.5t191.5 -169t71.5 -223.5q0 -18 -13.5 -29.5t-31.5 -11.5h-103q-20 0 -32.5 10.5t-18.5 34.5q-35 158 -126 221.5t-247 63.5q-356 0 -370 -397q-2 -55 -3 -170q0 -115 3 -174q14 -395 370 -395 q154 0 246 63t127 219q6 25 18.5 35t32.5 10h103q18 0 31.5 -11t13.5 -30q-6 -106 -59.5 -203.5t-158 -164t-255.5 -82.5v-226q0 -23 -12.5 -35t-35.5 -12h-98q-20 0 -33.5 13.5t-13.5 33.5v224q-227 25 -345 170t-128 381q-2 55 -2 180z" />
<glyph unicode="&#x4ab;" d="M106 532l3 82q6 223 128.5 347t342.5 124q147 0 248.5 -52t151.5 -130t54 -155q2 -18 -13 -33t-34 -15h-90q-20 0 -30.5 9.5t-20.5 33.5q-37 100 -101.5 142.5t-162.5 42.5q-129 0 -205 -80t-82 -244l-2 -72l2 -71q6 -164 82 -244t205 -80q100 0 163.5 42t100.5 143 q10 25 20.5 34t30.5 9h90q19 0 34 -14.5t13 -33.5q-4 -68 -43 -137t-118.5 -123.5t-196.5 -70.5v-199q0 -20 -12.5 -33.5t-32.5 -13.5h-90q-20 0 -33.5 13.5t-13.5 33.5v199q-178 23 -278.5 144.5t-106.5 320.5z" />
<glyph unicode="&#x4ae;" horiz-adv-x="1284" d="M43 1393q0 16 13.5 28.5t29.5 12.5h98q33 0 54 -35l403 -670l406 670q18 35 53 35h98q16 0 28.5 -12.5t12.5 -28.5q0 -14 -6 -27l-494 -834v-485q0 -20 -14 -33.5t-35 -13.5h-98q-21 0 -34 13.5t-13 33.5v485l-494 834q-8 16 -8 27z" />
<glyph unicode="&#x4af;" horiz-adv-x="1085" d="M43 1022q0 18 12.5 30.5t30.5 12.5h92q18 0 31.5 -10.5t17.5 -22.5l316 -817l315 817q16 33 49 33h94q16 0 29.5 -12.5t13.5 -30.5l-4 -21l-405 -1011v-332q0 -23 -12.5 -35t-34.5 -12h-92q-20 0 -33.5 13.5t-13.5 33.5v334l-402 1009z" />
<glyph unicode="&#x4b0;" horiz-adv-x="1320" d="M43 1393q0 16 13.5 28.5t29.5 12.5h98q33 0 54 -35l403 -670l406 670q18 35 53 35h98q16 0 28.5 -12.5t12.5 -28.5q0 -15 -6 -27l-483 -815h45q12 0 19 -8t7 -21q0 -12 -7 -20t-19 -8h-56v-447q0 -20 -14 -33.5t-35 -13.5h-98q-20 0 -33.5 13.5t-13.5 33.5v447h-86 q-12 0 -20.5 8t-8.5 20t8 20.5t21 8.5h76l-484 815q-8 16 -8 27z" />
<glyph unicode="&#x4b1;" horiz-adv-x="1085" d="M43 1022q0 18 12.5 30.5t30.5 12.5h92q18 0 31.5 -10.5t17.5 -22.5l316 -817l315 817q16 33 49 33h94q16 0 29.5 -12.5t13.5 -30.5l-4 -21l-378 -944h145q20 0 33.5 -13t13.5 -34v-47q0 -20 -13.5 -33.5t-33.5 -13.5h-172v-258q0 -23 -12.5 -35t-34.5 -12h-92 q-20 0 -33.5 13.5t-13.5 33.5v258h-175q-20 0 -33.5 13.5t-13.5 33.5v47q0 20 13.5 33.5t33.5 13.5h148l-375 944z" />
<glyph unicode="&#x4b6;" horiz-adv-x="1374" d="M115 915v471q0 20 13 34t34 14h98q23 0 36 -13.5t13 -34.5v-458q0 -143 74 -209t248 -66q125 0 227.5 48.5t102.5 164.5v520q0 23 13 35.5t34 12.5h96q23 0 36 -12.5t13 -35.5v-1208h102q23 0 36.5 -13t13.5 -34v-371q0 -23 -13.5 -35t-36.5 -12h-96q-20 0 -33.5 12.5 t-13.5 34.5v240h-104q-20 0 -33.5 13.5t-13.5 33.5v531q-43 -51 -148.5 -78t-214.5 -27q-483 0 -483 442z" />
<glyph unicode="&#x4b7;" horiz-adv-x="1169" d="M86 670v348q0 23 13.5 35t33.5 12h92q20 0 33.5 -13.5t13.5 -33.5v-336q0 -94 53.5 -136t170.5 -42q135 0 207.5 33.5t72.5 111.5v369q0 20 13.5 33.5t33.5 13.5h97q20 0 33.5 -13.5t13.5 -33.5v-858h92q20 0 33.5 -13.5t13.5 -33.5v-326q0 -20 -13.5 -33.5t-33.5 -13.5 h-90q-20 0 -33.5 13.5t-13.5 33.5v213h-99q-20 0 -33.5 13.5t-13.5 33.5v389q-106 -100 -313 -100q-377 0 -377 334z" />
<glyph unicode="&#x4b8;" horiz-adv-x="1345" d="M115 915v471q0 20 13 34t34 14h98q23 0 36 -13.5t13 -34.5v-473q0 -137 70 -203.5t231 -70.5v227q0 20 13.5 33.5t33.5 13.5h11q23 0 35 -12t12 -35v-223q109 12 182.5 62.5t73.5 148.5v532q0 23 13 35.5t34 12.5h96q23 0 36 -12.5t13 -35.5v-1339q0 -20 -13 -33.5 t-36 -13.5h-96q-20 0 -33.5 13.5t-13.5 33.5v531q-63 -72 -256 -99v-168q0 -20 -12.5 -33.5t-34.5 -13.5h-11q-20 0 -33.5 13.5t-13.5 33.5v162h-6q-250 0 -369.5 111.5t-119.5 330.5z" />
<glyph unicode="&#x4b9;" d="M86 670v348q0 23 13.5 35t33.5 12h92q20 0 33.5 -13.5t13.5 -33.5v-338q0 -180 203 -184v174q0 20 13.5 33.5t33.5 13.5h13q20 0 32.5 -13.5t12.5 -33.5v-170q201 20 200 145v373q0 20 13.5 33.5t33.5 13.5h97q20 0 32.5 -13.5t12.5 -33.5v-971q0 -20 -12.5 -33.5 t-32.5 -13.5h-97q-20 0 -33.5 13.5t-13.5 33.5v395q-80 -70 -200 -88v-149q0 -20 -12.5 -32.5t-32.5 -12.5h-13q-20 0 -33.5 12t-13.5 33v139h-22q-174 0 -270.5 80t-96.5 246z" />
<glyph unicode="&#x4ba;" horiz-adv-x="1335" d="M182 47v1339q0 20 13.5 34t35.5 14h97q20 0 33.5 -13.5t13.5 -34.5v-530q43 51 148.5 78t213.5 27q483 0 484 -443v-471q0 -20 -13.5 -33.5t-33.5 -13.5h-99q-23 0 -36 13.5t-13 33.5v459q0 143 -73.5 208.5t-247.5 65.5q-125 0 -227.5 -48t-102.5 -165v-520 q0 -20 -12.5 -33.5t-34.5 -13.5h-97q-23 0 -36 12.5t-13 34.5z" />
<glyph unicode="&#x4bb;" horiz-adv-x="1243" d="M156 47v1360q0 23 13 35t34 12h94q23 0 35 -12t12 -35v-477q59 76 140 115.5t200 39.5q197 0 306.5 -125.5t109.5 -332.5v-580q0 -20 -13.5 -33.5t-33.5 -13.5h-97q-20 0 -33.5 13.5t-13.5 33.5v567q0 147 -71.5 229.5t-206.5 82.5q-131 0 -209 -83t-78 -229v-567 q0 -20 -13.5 -33.5t-33.5 -13.5h-94q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x4c0;" horiz-adv-x="555" d="M180 47v1339q0 20 12.5 34t32.5 14h103q23 0 35 -12.5t12 -35.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-103q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x4c1;" horiz-adv-x="2021" d="M80 49q0 16 4 23l434 667l-393 627q-10 16 -10 27q0 41 43 41h98q47 0 70 -33l366 -588l223 2v571q0 20 13.5 34t34.5 14h96q20 0 33.5 -13.5t13.5 -34.5v-571l219 -2l367 588q23 33 69 33h103q43 0 43 -41q0 -10 -11 -27l-393 -627l434 -667q5 -7 5 -23 q0 -20 -13.5 -34.5t-34.5 -14.5h-106q-20 0 -31.5 8t-21.5 23l-396 606h-233v-592q0 -20 -13.5 -32.5t-33.5 -12.5h-96q-20 0 -34 13.5t-14 33.5v590h-235l-393 -606q-10 -14 -21.5 -22.5t-32.5 -8.5h-106q-20 0 -33.5 14.5t-13.5 34.5zM696 1769q0 16 10 27.5t29 11.5h65 q18 0 28.5 -11t10.5 -28q0 -129 172 -129q170 0 170 129q0 16 10.5 27.5t28.5 11.5h66q18 0 28.5 -11t10.5 -28q0 -111 -72 -182t-242 -71t-242.5 71.5t-72.5 181.5z" />
<glyph unicode="&#x4c2;" horiz-adv-x="1624" d="M47 33q0 10 10 26l351 498l-310 451q-8 14 -8 24q0 14 11.5 23.5t29.5 9.5h100q33 0 54 -31l278 -411h160v395q0 23 13.5 35t33.5 12h88q20 0 33.5 -13.5t13.5 -33.5v-395h154l280 411q20 31 56 31h98q18 0 30.5 -9t12.5 -24q0 -8 -10 -24l-309 -451l350 -498 q10 -12 10 -26t-11.5 -23.5t-29.5 -9.5h-117q-18 0 -30.5 8t-24.5 23l-299 434h-160v-418q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v418h-160l-303 -434q-23 -31 -55 -31h-117q-18 0 -29.5 9t-11.5 24zM508 1460q0 16 10.5 27.5t28.5 11.5h66 q18 0 28.5 -11t10.5 -28q0 -55 36.5 -92t124.5 -37q90 0 127 37t37 92q0 16 10.5 27.5t28.5 11.5h66q18 0 28.5 -11t10.5 -28q0 -111 -72 -182.5t-236 -71.5q-163 0 -234 72t-71 182z" />
<glyph unicode="&#x4cb;" horiz-adv-x="1335" d="M115 915v471q0 20 13 34t34 14h98q23 0 36 -13.5t13 -34.5v-458q0 -143 74 -209t248 -66q125 0 227.5 48.5t102.5 164.5v520q0 23 13 35.5t34 12.5h96q22 0 35.5 -12.5t13.5 -35.5v-1339q0 -20 -13.5 -33.5t-35.5 -13.5h-100v-240q0 -23 -12.5 -35t-35.5 -12h-96 q-23 0 -36 13.5t-13 33.5v336q0 20 13.5 33.5t35.5 13.5h101v435q-43 -51 -148.5 -78t-214.5 -27q-483 0 -483 442z" />
<glyph unicode="&#x4cc;" horiz-adv-x="1093" d="M86 670v348q0 23 13.5 35t33.5 12h92q20 0 33.5 -13.5t13.5 -33.5v-336q0 -94 53.5 -136t170.5 -42q135 0 207.5 33.5t72.5 111.5v369q0 20 13.5 33.5t33.5 13.5h97q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-127v-213q0 -20 -12.5 -33.5 t-32.5 -13.5h-91q-20 0 -33.5 13.5t-13.5 33.5v301q0 20 13.5 33.5t33.5 13.5h119v301q-106 -100 -313 -100q-377 0 -377 334z" />
<glyph unicode="&#x4cf;" horiz-adv-x="493" d="M156 47v1360q0 23 13 35t34 12h90q20 0 33.5 -13.5t13.5 -33.5v-1360q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x4d0;" horiz-adv-x="1368" d="M41 41l4 22l508 1326q16 45 65 45h132q49 0 65 -45l508 -1326l4 -22q0 -16 -13.5 -28.5t-29.5 -12.5h-96q-20 0 -33.5 11.5t-17.5 23.5l-115 295h-678l-113 -295q-16 -35 -51 -35h-98q-16 0 -28.5 12.5t-12.5 28.5zM369 1769q0 16 10 27.5t29 11.5h65q18 0 28.5 -11 t10.5 -28q0 -129 172 -129q170 0 170 129q0 16 10.5 27.5t28.5 11.5h66q18 0 28.5 -11t10.5 -28q0 -111 -72 -182t-242 -71t-242.5 71.5t-72.5 181.5zM403 500h562l-281 735z" />
<glyph unicode="&#x4d1;" d="M78 289q0 135 110.5 221t300.5 115l308 43v59q0 211 -244 211q-94 0 -151.5 -38t-84.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 44 109.5t136 108.5t225 46q229 0 328.5 -106.5t99.5 -265.5v-666q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -32.5 13.5t-12.5 33.5v90q-45 -68 -131 -112.5t-221 -44.5q-101 0 -186 40.5t-135 111.5t-50 157zM258 303q0 -84 68.5 -129t162.5 -45q136 0 222 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5zM276 1460q0 16 10.5 27.5t28.5 11.5h66q18 0 28.5 -11 t10.5 -28q0 -55 36.5 -92t124.5 -37q90 0 127 37t37 92q0 16 10.5 27.5t28.5 11.5h66q18 0 28.5 -11t10.5 -28q0 -111 -72 -182.5t-236 -71.5q-163 0 -234 72t-71 182z" />
<glyph unicode="&#x4d2;" horiz-adv-x="1368" d="M41 41l4 22l508 1326q16 45 65 45h132q49 0 65 -45l508 -1326l4 -22q0 -16 -13.5 -28.5t-29.5 -12.5h-96q-20 0 -33.5 11.5t-17.5 23.5l-115 295h-678l-113 -295q-16 -35 -51 -35h-98q-16 0 -28.5 12.5t-12.5 28.5zM403 500h562l-281 735zM414 1688v106q0 20 12.5 34 t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM756 1688v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4d3;" d="M78 289q0 135 110.5 221t300.5 115l308 43v59q0 211 -244 211q-94 0 -151.5 -38t-84.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 44 109.5t136 108.5t225 46q229 0 328.5 -106.5t99.5 -265.5v-666q0 -20 -13.5 -33.5t-33.5 -13.5h-90 q-20 0 -32.5 13.5t-12.5 33.5v90q-45 -68 -131 -112.5t-221 -44.5q-101 0 -186 40.5t-135 111.5t-50 157zM258 303q0 -84 68.5 -129t162.5 -45q136 0 222 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5zM311 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5 t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM653 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4d4;" horiz-adv-x="1912" d="M10 41q0 6 3 14t3 13q27 74 153 315.5t550 1007.5q20 43 59 43h965q23 0 35 -13.5t12 -36.5v-73q0 -20 -13.5 -32.5t-33.5 -12.5h-688v-457h643q20 0 33.5 -13.5t13.5 -35.5v-70q0 -20 -12.5 -33.5t-34.5 -13.5h-643v-473h704q20 0 33.5 -13.5t13.5 -35.5v-74 q0 -20 -13 -33.5t-34 -13.5h-848q-20 0 -33.5 13.5t-13.5 33.5v283h-497q-160 -285 -162 -295q-16 -35 -51 -35h-103q-16 0 -28.5 12.5t-12.5 28.5zM442 500h422v753h-12z" />
<glyph unicode="&#x4d5;" horiz-adv-x="1822" d="M80 289q0 137 111.5 225t300.5 115l307 43v55q0 211 -244 211q-94 0 -152.5 -38t-85.5 -89q-8 -20 -17 -27.5t-26 -7.5h-79q-18 0 -31.5 13.5t-13.5 31.5q0 47 45 109.5t137 108.5t225 46q139 0 227 -44t129 -117q125 162 351 161q258 0 360 -159.5t102 -372.5v-37 q0 -20 -13 -33.5t-34 -13.5h-692v-18q4 -164 84 -243t191 -79q74 0 139 28.5t106 88.5q16 23 27.5 29t36.5 6h84q18 0 30.5 -11.5t12.5 -29.5q0 -41 -52.5 -103.5t-150.5 -109.5t-233 -47q-139 0 -235.5 55t-152.5 153q-125 -209 -415 -208q-111 0 -198 40.5t-134 111.5 t-47 157zM260 303q0 -84 68.5 -129t163.5 -45q135 0 221 88t86 256v57l-240 -34q-147 -20 -223 -69.5t-76 -123.5zM987 608h553v8q0 145 -70.5 233.5t-205.5 88.5q-113 0 -193 -82t-84 -240v-8z" />
<glyph unicode="&#x4d6;" horiz-adv-x="1239" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h842q23 0 36 -13.5t13 -36.5v-73q0 -20 -13 -32.5t-36 -12.5h-698v-457h653q23 0 36 -13.5t13 -35.5v-70q0 -20 -13.5 -33.5t-35.5 -13.5h-653v-473h714q23 0 36.5 -13.5t13.5 -35.5v-74q0 -20 -14.5 -33.5t-35.5 -13.5h-858 q-20 0 -32.5 13.5t-12.5 33.5zM344 1769q0 16 10 27.5t29 11.5h65q18 0 28.5 -11t10.5 -28q0 -129 172 -129q170 0 170 129q0 16 10.5 27.5t28.5 11.5h66q18 0 28.5 -11t10.5 -28q0 -111 -72 -182t-242 -71t-242.5 71.5t-72.5 181.5z" />
<glyph unicode="&#x4d7;" horiz-adv-x="1144" d="M102 535l2 67q12 221 137.5 352t333.5 131q225 0 349.5 -143t124.5 -389v-37q0 -20 -13.5 -33.5t-34.5 -13.5h-710v-18q6 -133 82 -227.5t200 -94.5q96 0 157 38t91 79q18 25 28.5 30t35.5 5h90q18 0 31.5 -10.5t13.5 -26.5q0 -45 -55.5 -108.5t-156.5 -109.5t-233 -46 q-208 0 -333 130t-138 355zM278 1460q0 16 10.5 27.5t28.5 11.5h66q18 0 28.5 -11t10.5 -28q0 -55 36.5 -92t124.5 -37q90 0 127 37t37 92q0 16 10.5 27.5t28.5 11.5h66q18 0 28.5 -11t10.5 -28q0 -111 -72 -182.5t-236 -71.5q-163 0 -234 72t-71 182zM291 608h571v6 q0 143 -77.5 233.5t-209.5 90.5q-133 0 -208.5 -90t-75.5 -234v-6z" />
<glyph unicode="&#x4d8;" horiz-adv-x="1372" d="M125 682v43q0 27 18.5 44t46.5 17h861q0 70 -2 103q-14 397 -371 397q-143 0 -228 -53t-114 -182q-6 -25 -17.5 -35t-31.5 -10h-101q-18 0 -31.5 11t-13.5 30q6 102 66.5 195t179.5 152.5t291 59.5q272 0 414.5 -149.5t152.5 -405.5q4 -114 4 -184t-4 -180 q-10 -258 -150.5 -406.5t-404.5 -148.5t-414.5 176t-150.5 526zM322 627q0 -477 368 -477q164 0 257 96t102 299q2 33 2 102h-729v-20z" />
<glyph unicode="&#x4d9;" horiz-adv-x="1144" d="M96 512v37q0 20 13.5 33.5t33.5 13.5h711v18q-6 133 -82 227.5t-201 94.5q-96 0 -156.5 -38t-90.5 -79q-18 -25 -28.5 -30t-35.5 -5h-90q-18 0 -31.5 10.5t-13.5 26.5q0 45 55.5 108.5t156.5 109.5t232 46q209 0 334 -130t137 -355q2 -16 2 -68q0 -51 -2 -69 q-12 -221 -137 -352t-334 -131q-225 0 -349 143t-124 389zM283 451q0 -143 77.5 -233.5t208.5 -90.5q133 0 209 90t76 234v6h-571v-6z" />
<glyph unicode="&#x4dc;" horiz-adv-x="2021" d="M80 49q0 16 4 23l434 667l-393 627q-10 16 -10 27q0 41 43 41h98q47 0 70 -33l366 -588l223 2v571q0 20 13.5 34t34.5 14h96q20 0 33.5 -13.5t13.5 -34.5v-571l219 -2l367 588q23 33 69 33h103q43 0 43 -41q0 -10 -11 -27l-393 -627l434 -667q5 -7 5 -23 q0 -20 -13.5 -34.5t-34.5 -14.5h-106q-20 0 -31.5 8t-21.5 23l-396 606h-233v-592q0 -20 -13.5 -32.5t-33.5 -12.5h-96q-20 0 -34 13.5t-14 33.5v590h-235l-393 -606q-10 -14 -21.5 -22.5t-32.5 -8.5h-106q-20 0 -33.5 14.5t-13.5 34.5zM741 1688v106q0 20 12.5 34t32.5 14 h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM1083 1688v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4dd;" horiz-adv-x="1624" d="M47 33q0 10 10 26l351 498l-310 451q-8 14 -8 24q0 14 11.5 23.5t29.5 9.5h100q33 0 54 -31l278 -411h160v395q0 23 13.5 35t33.5 12h88q20 0 33.5 -13.5t13.5 -33.5v-395h154l280 411q20 31 56 31h98q18 0 30.5 -9t12.5 -24q0 -8 -10 -24l-309 -451l350 -498 q10 -12 10 -26t-11.5 -23.5t-29.5 -9.5h-117q-18 0 -30.5 8t-24.5 23l-299 434h-160v-418q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v418h-160l-303 -434q-23 -31 -55 -31h-117q-18 0 -29.5 9t-11.5 24zM543 1319v106q0 20 12.5 34t32.5 14h107 q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM885 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4de;" horiz-adv-x="1265" d="M80 342v4q0 16 12 26.5t31 10.5h94q23 0 37 -9t22 -36q25 -88 110 -139.5t220 -51.5q147 0 248.5 71t101.5 188q0 121 -88 188.5t-239 67.5h-148q-20 0 -32.5 13t-12.5 36v65q0 23 12.5 36t32.5 13h140q133 0 210.5 58.5t77.5 177.5q0 100 -86 162.5t-211 62.5 q-131 0 -215 -47t-104 -147q-10 -41 -49 -41h-101q-18 0 -32.5 12t-12.5 31q16 164 148.5 262t367.5 98q156 0 266.5 -53t167 -141.5t56.5 -188.5t-39 -183t-125 -134q106 -53 159.5 -144.5t53.5 -208.5q0 -125 -66.5 -220t-190.5 -148t-288 -53q-159 0 -275 48t-179.5 130 t-73.5 184zM344 1688v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM686 1688v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109 q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4df;" horiz-adv-x="1044" d="M88 274q0 18 12.5 29.5t30.5 11.5h88q20 0 31.5 -9t19.5 -32q23 -78 89.5 -110.5t177.5 -32.5q106 0 174.5 44t68.5 130q0 172 -198 172h-113q-20 0 -32.5 12.5t-12.5 32.5v51q0 20 12.5 32.5t32.5 12.5h100q176 0 176 142q0 74 -54 124t-171 50q-115 0 -166 -38 t-73 -124q-10 -39 -52 -39h-86q-16 0 -28.5 11.5t-12.5 27.5q2 86 49.5 156.5t140.5 113.5t228 43q141 0 232.5 -45t132.5 -112.5t41 -141.5q0 -84 -28 -135t-97 -86q88 -35 126 -103.5t38 -168.5q0 -152 -123 -232.5t-322 -80.5q-147 0 -244.5 47t-141.5 114.5t-46 132.5z M256 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM598 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12 t-12.5 33z" />
<glyph unicode="&#x4e2;" horiz-adv-x="1515" d="M180 49v1335q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-1071l772 1092q16 29 49 29h96q20 0 33.5 -13.5t13.5 -36.5v-1337q0 -20 -13 -33.5t-34 -13.5h-98q-20 0 -34.5 13.5t-14.5 33.5v1061l-770 -1079q-16 -29 -49 -29h-99q-20 0 -32.5 13.5 t-12.5 35.5zM479 1680v59q0 20 12.5 32.5t32.5 12.5h451q20 0 32.5 -12t12.5 -33v-59q0 -20 -12.5 -32.5t-32.5 -12.5h-451q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4e3;" horiz-adv-x="1243" d="M154 43v975q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-750l573 766q23 31 54 31h86q16 0 30.5 -12.5t14.5 -28.5v-977q0 -20 -13.5 -33.5t-33.5 -13.5h-91q-20 0 -33.5 13.5t-13.5 33.5v750l-573 -766q-23 -31 -53 -31h-88q-16 0 -29.5 13.5t-13.5 29.5z M356 1311v59q0 20 12.5 32.5t32.5 12.5h451q20 0 32.5 -12t12.5 -33v-59q0 -20 -12.5 -32.5t-32.5 -12.5h-451q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4e4;" horiz-adv-x="1515" d="M180 49v1335q0 23 12.5 36.5t34.5 13.5h99q20 0 33.5 -13.5t13.5 -36.5v-1071l772 1092q16 29 49 29h96q20 0 33.5 -13.5t13.5 -36.5v-1337q0 -20 -13 -33.5t-34 -13.5h-98q-20 0 -34.5 13.5t-14.5 33.5v1061l-770 -1079q-16 -29 -49 -29h-99q-20 0 -32.5 13.5 t-12.5 35.5zM481 1688v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM823 1688v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109 q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4e5;" horiz-adv-x="1243" d="M154 43v975q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-750l573 766q23 31 54 31h86q16 0 30.5 -12.5t14.5 -28.5v-977q0 -20 -13.5 -33.5t-33.5 -13.5h-91q-20 0 -33.5 13.5t-13.5 33.5v750l-573 -766q-23 -31 -53 -31h-88q-16 0 -29.5 13.5t-13.5 29.5z M358 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM700 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12 t-12.5 33z" />
<glyph unicode="&#x4e6;" horiz-adv-x="1382" d="M125 717q0 113 2 172q8 279 158.5 422t406.5 143t405.5 -143.5t157.5 -421.5q4 -119 5 -172q0 -51 -5 -174q-8 -283 -152.5 -423t-410.5 -140t-411.5 140t-153.5 423q-2 61 -2 174zM322 717q0 -102 2 -164q6 -205 104 -304t264 -99q164 0 262.5 99t106.5 304q4 123 4 164 q0 45 -4 164q-8 205 -106.5 305t-262.5 100t-263 -100.5t-105 -304.5q-2 -59 -2 -164zM422 1688v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM764 1688v106q0 20 12.5 34t32.5 14h109 q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4e7;" horiz-adv-x="1171" d="M106 532l3 89q8 211 132 337.5t345 126.5t345 -127t132 -337q4 -45 4 -89q0 -43 -4 -88q-8 -211 -130 -337.5t-347 -126.5q-226 0 -347.5 126.5t-129.5 337.5zM293 532l2 -77q6 -156 83 -239t208 -83t207.5 83t83.5 239q2 20 2 77t-2 78q-6 156 -83 239t-208 83t-208 -83 t-83 -239zM315 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM657 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109 q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4e8;" horiz-adv-x="1382" d="M125 717q0 113 2 172q8 279 158.5 422t406.5 143t405.5 -143.5t157.5 -421.5q5 -119 5 -172q0 -51 -5 -174q-8 -283 -152.5 -423t-410.5 -140t-411.5 140t-153.5 423q-2 61 -2 174zM322 647q0 -59 2 -94q6 -205 104 -304t264 -99q164 0 262.5 99t106.5 304q2 35 2 94 h-741zM322 786h741q0 61 -2 95q-8 205 -106.5 305t-262.5 100t-263 -100.5t-105 -304.5q-2 -33 -2 -95z" />
<glyph unicode="&#x4e9;" horiz-adv-x="1171" d="M106 532l3 89q8 211 132 337.5t345 126.5t345 -127t132 -337q4 -46 4 -89t-4 -88q-8 -211 -130 -337.5t-347 -126.5t-347 126.5t-130 337.5zM295 449q6 -156 83 -239t208 -83t207.5 83t83.5 239v20h-582v-20zM295 596h582v20q-6 156 -83 239t-208 83t-208 -83t-83 -239 v-20z" />
<glyph unicode="&#x4ee;" horiz-adv-x="1200" d="M27 1391q0 18 13 30.5t30 12.5h108q29 0 49 -33l435 -789l344 789q12 33 51 33h102q17 0 29 -12.5t12 -28.5q0 -12 -6 -29l-418 -946q-72 -162 -126 -247t-131 -128t-195 -43h-82q-20 0 -32.5 13.5t-12.5 33.5v80q0 23 12 36t33 13h73q80 0 135.5 51.5t114.5 171.5 l-532 969q-6 12 -6 23zM344 1680v59q0 20 12.5 32.5t32.5 12.5h451q20 0 32.5 -12t12.5 -33v-59q0 -20 -12.5 -32.5t-32.5 -12.5h-451q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4ef;" horiz-adv-x="1118" d="M63 1022q0 18 12.5 30.5t30.5 12.5h91q33 0 49 -33l319 -758l318 758q14 33 51 33h86q16 0 29.5 -12.5t13.5 -28.5q0 -8 -12 -37l-389 -921l-19 -43q-55 -131 -99 -213t-110.5 -140.5t-157.5 -58.5h-77q-20 0 -33.5 13.5t-13.5 33.5v63q0 20 13 34t34 14h53q45 0 73.5 18 t52 59t60.5 127l35 84l-399 930q-10 27 -11 35zM287 1311v59q0 20 12.5 32.5t32.5 12.5h451q20 0 32.5 -12t12.5 -33v-59q0 -20 -12.5 -32.5t-32.5 -12.5h-451q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4f0;" horiz-adv-x="1200" d="M27 1391q0 18 13 30.5t30 12.5h108q29 0 49 -33l435 -789l344 789q12 33 51 33h102q17 0 29 -12.5t12 -28.5q0 -12 -6 -29l-418 -946q-72 -162 -126 -247t-131 -128t-195 -43h-82q-20 0 -32.5 13.5t-12.5 33.5v80q0 23 12 36t33 13h73q80 0 135.5 51.5t114.5 171.5 l-532 969q-6 12 -6 23zM346 1688v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM688 1688v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5 h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4f1;" horiz-adv-x="1118" d="M63 1022q0 18 12.5 30.5t30.5 12.5h91q33 0 49 -33l319 -758l318 758q14 33 51 33h86q16 0 29.5 -12.5t13.5 -28.5q0 -8 -12 -37l-389 -921l-19 -43q-55 -131 -99 -213t-110.5 -140.5t-157.5 -58.5h-77q-20 0 -33.5 13.5t-13.5 33.5v63q0 20 13 34t34 14h53q45 0 73.5 18 t52 59t60.5 127l35 84l-399 930q-10 27 -11 35zM289 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM631 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106 q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4f2;" horiz-adv-x="1200" d="M27 1391q0 18 13 30.5t30 12.5h108q29 0 49 -33l435 -789l344 789q12 33 51 33h102q17 0 29 -12.5t12 -28.5q0 -12 -6 -29l-418 -946q-72 -162 -126 -247t-131 -128t-195 -43h-82q-20 0 -32.5 13.5t-12.5 33.5v80q0 23 12 36t33 13h73q80 0 135.5 51.5t114.5 171.5 l-532 969q-6 12 -6 23zM402 1620q0 10 10 31l80 178q10 20 27.5 29.5t48.5 9.5h110q18 0 29.5 -10t11.5 -29q0 -14 -12 -26l-160 -187q-25 -29 -63 -28h-50q-32 -1 -32 32zM733 1620q0 10 11 31l80 178q10 20 27.5 29.5t47.5 9.5h111q18 0 29.5 -10t11.5 -29q0 -10 -12 -26 l-162 -187q-25 -29 -62 -28h-49q-33 -1 -33 32z" />
<glyph unicode="&#x4f3;" horiz-adv-x="1118" d="M63 1022q0 18 12.5 30.5t30.5 12.5h91q33 0 49 -33l319 -758l318 758q14 33 51 33h86q16 0 29.5 -12.5t13.5 -28.5q0 -8 -12 -37l-389 -921l-19 -43q-55 -131 -99 -213t-110.5 -140.5t-157.5 -58.5h-77q-20 0 -33.5 13.5t-13.5 33.5v63q0 20 13 34t34 14h53q45 0 73.5 18 t52 59t60.5 127l35 84l-399 930q-10 27 -11 35zM344 1251q0 10 10 31l80 178q10 20 27.5 29.5t48.5 9.5h110q18 0 29.5 -10t11.5 -29q0 -14 -12 -26l-160 -187q-25 -29 -63 -28h-50q-32 -1 -32 32zM675 1251q0 10 11 31l80 178q10 20 27.5 29.5t47.5 9.5h111q18 0 29.5 -10 t11.5 -29q0 -10 -12 -26l-162 -187q-25 -29 -62 -28h-49q-33 -1 -33 32z" />
<glyph unicode="&#x4f4;" horiz-adv-x="1335" d="M115 915v471q0 20 13 34t34 14h98q23 0 36 -13.5t13 -34.5v-458q0 -143 74 -209t248 -66q125 0 227.5 48.5t102.5 164.5v520q0 23 13 35.5t34 12.5h96q23 0 36 -12.5t13 -35.5v-1339q0 -20 -13.5 -33.5t-35.5 -13.5h-96q-21 0 -34 13.5t-13 33.5v531q-43 -51 -148.5 -78 t-214.5 -27q-483 0 -483 442zM348 1688v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM690 1688v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5 t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4f5;" horiz-adv-x="1122" d="M86 670v348q0 23 13.5 35t33.5 12h92q20 0 33.5 -13.5t13.5 -33.5v-336q0 -94 53.5 -136t170.5 -42q135 0 207.5 33.5t72.5 111.5v369q0 20 13.5 33.5t33.5 13.5h97q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-97q-20 0 -33.5 13.5t-13.5 33.5v389 q-106 -100 -313 -100q-377 0 -377 334zM264 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM606 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106 q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x4f6;" horiz-adv-x="1067" d="M180 47v1337q0 23 12.5 36.5t32.5 13.5h787q22 0 34.5 -13.5t12.5 -36.5v-75q0 -20 -12.5 -33.5t-34.5 -13.5h-639v-1084h100q23 0 36 -13t13 -34v-371q0 -23 -13 -35t-36 -12h-96q-20 0 -33.5 12.5t-13.5 34.5v240h-105q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x4f7;" horiz-adv-x="878" d="M154 47v971q0 20 13 33.5t34 13.5h608q20 0 32.5 -13.5t12.5 -33.5v-66q0 -20 -12.5 -32.5t-32.5 -12.5h-471v-747h96q20 0 33.5 -13.5t13.5 -33.5v-326q0 -20 -13 -33.5t-34 -13.5h-90q-20 0 -33.5 13.5t-13.5 33.5v213h-96q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x4f8;" horiz-adv-x="1673" d="M180 47v1339q0 20 12.5 34t32.5 14h99q23 0 36 -13.5t13 -34.5v-499h266q223 0 343 -112.5t120 -325.5q0 -119 -52.5 -221.5t-157 -165t-253.5 -62.5h-414q-20 0 -32.5 13.5t-12.5 33.5zM373 166h248q133 0 208.5 78t75.5 209q0 268 -284 268h-248v-555zM567 1688v106 q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM909 1688v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z M1300 47v1339q0 20 12.5 34t33.5 14h102q23 0 35 -12.5t12 -35.5v-1339q0 -20 -13.5 -33.5t-33.5 -13.5h-102q-21 0 -33.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x4f9;" horiz-adv-x="1439" d="M154 47v971q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-301h215q201 0 300 -96.5t99 -268.5q0 -180 -105.5 -266t-320.5 -86h-325q-21 0 -34 13.5t-13 33.5zM336 150h201q127 0 184 46t57 156q0 109 -57 162t-184 53h-201v-417zM467 1319v106q0 20 12.5 34 t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM809 1319v106q0 20 12.5 34t32.5 14h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33zM1100 47v971 q0 20 13 33.5t34 13.5h90q20 0 33.5 -13.5t13.5 -33.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x1e80;" horiz-adv-x="1636" d="M90 1393q0 16 12.5 28.5t28.5 12.5h100q43 0 50 -35l211 -1069l227 737q12 49 63 49h72q49 0 66 -49l225 -737l211 1069q6 35 51 35h98q16 0 28.5 -12.5t12.5 -28.5q0 -12 -2 -23l-266 -1317q-10 -53 -64 -53h-73q-51 0 -68 51l-254 789l-256 -789q-14 -51 -65 -51h-76 q-52 0 -64 53l-264 1317zM497 1778q0 39 39 39h146q27 0 41 -7.5t34 -29.5l152 -185q10 -10 10 -26q0 -33 -33 -33h-75q-23 0 -37.5 4t-30.5 19l-235 194q-10 10 -11 25z" />
<glyph unicode="&#x1e81;" horiz-adv-x="1638" d="M76 1022q0 18 12 30.5t31 12.5h84q20 0 33.5 -11.5t15.5 -21.5l231 -782l246 772q4 16 18.5 29.5t38.5 13.5h66q25 0 39 -13.5t18 -29.5l246 -772l231 782q2 10 15.5 21.5t34.5 11.5h84q18 0 30.5 -12.5t12.5 -30.5l-4 -21l-291 -950q-8 -27 -22.5 -39t-43.5 -12h-74 q-53 0 -69 51l-240 742l-239 -742q-16 -51 -70 -51h-74q-28 0 -42.5 12.5t-22.5 38.5l-291 950zM495 1460q0 39 39 39h133q29 0 43.5 -7t32.5 -30l154 -184q10 -10 10 -27q0 -33 -33 -32h-65q-23 0 -36.5 4t-29.5 18l-238 195q-10 10 -10 24z" />
<glyph unicode="&#x1e82;" horiz-adv-x="1636" d="M90 1393q0 16 12.5 28.5t28.5 12.5h100q43 0 50 -35l211 -1069l227 737q12 49 63 49h72q49 0 66 -49l225 -737l211 1069q6 35 51 35h98q16 0 28.5 -12.5t12.5 -28.5q0 -12 -2 -23l-266 -1317q-10 -53 -64 -53h-73q-51 0 -68 51l-254 789l-256 -789q-14 -51 -65 -51h-76 q-52 0 -64 53l-264 1317zM735 1569q0 16 10 26l152 185q20 23 34.5 30t41.5 7h145q39 0 39 -39q0 -14 -10 -25l-236 -194q-16 -14 -30.5 -18.5t-36.5 -4.5h-76q-33 0 -33 33z" />
<glyph unicode="&#x1e83;" horiz-adv-x="1638" d="M76 1022q0 18 12 30.5t31 12.5h84q20 0 33.5 -11.5t15.5 -21.5l231 -782l246 772q4 16 18.5 29.5t38.5 13.5h66q25 0 39 -13.5t18 -29.5l246 -772l231 782q2 10 15.5 21.5t34.5 11.5h84q18 0 30.5 -12.5t12.5 -30.5l-4 -21l-291 -950q-8 -27 -22.5 -39t-43.5 -12h-74 q-53 0 -69 51l-240 742l-239 -742q-16 -51 -70 -51h-74q-28 0 -42.5 12.5t-22.5 38.5l-291 950zM733 1251q0 16 10 27l152 184q20 23 34.5 30t41.5 7h133q39 0 39 -39q0 -14 -10 -24l-236 -195q-16 -14 -30.5 -18t-37.5 -4h-63q-33 -1 -33 32z" />
<glyph unicode="&#x1e84;" horiz-adv-x="1636" d="M90 1393q0 16 12.5 28.5t28.5 12.5h100q43 0 50 -35l211 -1069l227 737q12 49 63 49h72q49 0 66 -49l225 -737l211 1069q6 35 51 35h98q16 0 28.5 -12.5t12.5 -28.5q0 -12 -2 -23l-266 -1317q-10 -53 -64 -53h-73q-51 0 -68 51l-254 789l-256 -789q-14 -51 -65 -51h-76 q-52 0 -64 53l-264 1317zM518 1581v109q0 20 12 33.5t33 13.5h106q20 0 33.5 -13.5t13.5 -33.5v-109q0 -20 -13 -32.5t-34 -12.5h-106q-21 0 -33 12.5t-12 32.5zM921 1581v109q0 20 12.5 33.5t32.5 13.5h109q20 0 33.5 -13.5t13.5 -33.5v-109q0 -20 -13.5 -32.5t-33.5 -12.5 h-109q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#x1e85;" horiz-adv-x="1638" d="M76 1022q0 18 12 30.5t31 12.5h84q20 0 33.5 -11.5t15.5 -21.5l231 -782l246 772q4 16 18.5 29.5t38.5 13.5h66q25 0 39 -13.5t18 -29.5l246 -772l231 782q2 10 15.5 21.5t34.5 11.5h84q18 0 30.5 -12.5t12.5 -30.5l-4 -21l-291 -950q-8 -27 -22.5 -39t-43.5 -12h-74 q-53 0 -69 51l-240 742l-239 -742q-16 -51 -70 -51h-74q-28 0 -42.5 12.5t-22.5 38.5l-291 950zM549 1319v106q0 20 12.5 34t32.5 14h107q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-107q-20 0 -32.5 12t-12.5 33zM891 1319v106q0 20 12.5 34t32.5 14 h109q20 0 33.5 -13.5t13.5 -34.5v-106q0 -20 -13.5 -32.5t-33.5 -12.5h-109q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x1ef2;" horiz-adv-x="1320" d="M63 1393q0 16 12.5 28.5t28.5 12.5h99q37 0 53 -35l406 -670l403 670q20 35 53 35h99q16 0 29.5 -12.5t13.5 -28.5q0 -10 -9 -27l-493 -834v-485q0 -20 -13.5 -33.5t-35.5 -13.5h-99q-20 0 -33.5 13.5t-13.5 33.5v485l-493 834q-6 12 -7 27zM340 1778q0 39 39 39h146 q27 0 41 -7.5t34 -29.5l152 -185q10 -10 10 -26q0 -33 -33 -33h-75q-23 0 -37.5 4t-30.5 19l-235 194q-10 10 -11 25z" />
<glyph unicode="&#x1ef3;" horiz-adv-x="1124" d="M63 1022q2 18 14.5 30.5t28.5 12.5h95q33 0 49 -33l315 -753l324 753q16 33 49 33h92q16 0 28.5 -12.5t12.5 -28.5q0 -14 -10 -37l-580 -1343q-10 -16 -21 -24.5t-30 -8.5h-90q-18 0 -30.5 12t-12.5 29q0 6 10 37l160 374l-391 924q-12 25 -13 35zM246 1460q0 39 39 39 h133q29 0 43.5 -7t32.5 -30l154 -184q10 -10 10 -27q0 -33 -33 -32h-65q-23 0 -36.5 4t-29.5 18l-238 195q-10 10 -10 24z" />
<glyph unicode="&#x2000;" horiz-adv-x="934" />
<glyph unicode="&#x2001;" horiz-adv-x="1868" />
<glyph unicode="&#x2002;" horiz-adv-x="934" />
<glyph unicode="&#x2003;" horiz-adv-x="1868" />
<glyph unicode="&#x2004;" horiz-adv-x="622" />
<glyph unicode="&#x2005;" horiz-adv-x="467" />
<glyph unicode="&#x2006;" horiz-adv-x="311" />
<glyph unicode="&#x2007;" horiz-adv-x="311" />
<glyph unicode="&#x2008;" horiz-adv-x="233" />
<glyph unicode="&#x2009;" horiz-adv-x="373" />
<glyph unicode="&#x200a;" horiz-adv-x="103" />
<glyph unicode="&#x2010;" horiz-adv-x="991" d="M141 578v73q0 20 13.5 33.5t33.5 13.5h615q20 0 33.5 -13t13.5 -34v-73q0 -20 -13.5 -33t-33.5 -13h-615q-20 0 -33.5 12.5t-13.5 33.5z" />
<glyph unicode="&#x2011;" horiz-adv-x="991" d="M141 578v73q0 20 13.5 33.5t33.5 13.5h615q20 0 33.5 -13t13.5 -34v-73q0 -20 -13.5 -33t-33.5 -13h-615q-20 0 -33.5 12.5t-13.5 33.5z" />
<glyph unicode="&#x2012;" horiz-adv-x="991" d="M141 578v73q0 20 13.5 33.5t33.5 13.5h615q20 0 33.5 -13t13.5 -34v-73q0 -20 -13.5 -33t-33.5 -13h-615q-20 0 -33.5 12.5t-13.5 33.5z" />
<glyph unicode="&#x2013;" horiz-adv-x="1175" d="M141 578v73q0 20 13.5 33.5t33.5 13.5h799q20 0 33.5 -13t13.5 -34v-73q0 -20 -13 -33t-34 -13h-799q-20 0 -33.5 12.5t-13.5 33.5z" />
<glyph unicode="&#x2014;" horiz-adv-x="1564" d="M141 578v73q0 20 13.5 33.5t33.5 13.5h1188q20 0 33.5 -13t13.5 -34v-73q0 -20 -13 -33t-34 -13h-1188q-20 0 -33.5 12.5t-13.5 33.5z" />
<glyph unicode="&#x2018;" horiz-adv-x="477" d="M106 1100q0 12 7 33l106 274q10 25 22.5 36t37.5 11h63q16 0 25.5 -12t5.5 -31l-45 -287q-6 -59 -58 -59h-131q-14 0 -23.5 10t-9.5 25z" />
<glyph unicode="&#x2019;" horiz-adv-x="464" d="M119 1108l45 287q8 59 57 59h131q14 0 23.5 -10t9.5 -25q0 -20 -6 -33l-107 -276q-10 -23 -22 -34t-37 -11h-63q-17 0 -25 11t-6 32z" />
<glyph unicode="&#x201a;" horiz-adv-x="507" d="M106 -111l46 289q8 59 57 60h131q14 0 23.5 -11.5t9.5 -23.5q0 -20 -6 -35l-107 -274q-10 -25 -22.5 -36.5t-36.5 -11.5h-64q-16 0 -25.5 12.5t-5.5 30.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="798" d="M106 1100q0 12 7 33l106 274q10 25 22.5 36t37.5 11h63q16 0 25.5 -12t5.5 -31l-45 -287q-6 -59 -58 -59h-131q-14 0 -23.5 10t-9.5 25zM428 1100q0 12 6 33l107 274q10 25 22 36t37 11h64q16 0 25 -12t5 -31l-45 -287q-6 -59 -57 -59h-131q-15 0 -24 10t-9 25z" />
<glyph unicode="&#x201d;" horiz-adv-x="786" d="M119 1108l45 287q8 59 57 59h131q14 0 23.5 -10t9.5 -25q0 -20 -6 -33l-107 -276q-10 -23 -22 -34t-37 -11h-63q-17 0 -26 12.5t-5 30.5zM440 1108l45 287q8 59 58 59h131q14 0 23.5 -10t9.5 -25q0 -20 -7 -33l-106 -276q-10 -23 -22.5 -34t-36.5 -11h-64 q-16 0 -25.5 12.5t-5.5 30.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="829" d="M106 -111l46 289q8 59 57 60h131q14 0 23.5 -11.5t9.5 -23.5q0 -20 -6 -35l-107 -274q-10 -25 -22.5 -36.5t-36.5 -11.5h-64q-16 0 -25.5 12.5t-5.5 30.5zM428 -111l45 289q8 59 57 60h132q14 0 23 -11.5t9 -23.5q0 -20 -6 -35l-106 -274q-10 -25 -22.5 -36.5 t-37.5 -11.5h-63q-17 0 -26 12.5t-5 30.5z" />
<glyph unicode="&#x2020;" horiz-adv-x="872" d="M68 958v68q0 23 13 35t34 12h237v313q0 23 13.5 35.5t33.5 12.5h74q20 0 33.5 -13.5t13.5 -34.5v-313h238q20 0 33.5 -13t13.5 -34v-68q0 -20 -13.5 -33.5t-33.5 -13.5h-238v-864q0 -20 -13 -33.5t-34 -13.5h-74q-20 0 -33.5 13.5t-13.5 33.5v864h-237q-20 0 -33.5 13.5 t-13.5 33.5z" />
<glyph unicode="&#x2021;" horiz-adv-x="964" d="M115 416v69q0 20 12 33.5t33 13.5h239v400h-239q-20 0 -32.5 13t-12.5 34v68q0 20 12 33.5t33 13.5h239v292q0 20 12.5 34t32.5 14h76q20 0 33.5 -13.5t13.5 -34.5v-292h238q20 0 33.5 -13.5t13.5 -33.5v-68q0 -20 -13.5 -33.5t-33.5 -13.5h-238v-400h238q20 0 33.5 -13 t13.5 -34v-69q0 -20 -13.5 -33.5t-33.5 -13.5h-238v-322q0 -20 -13 -33.5t-34 -13.5h-76q-20 0 -32.5 13.5t-12.5 33.5v322h-239q-20 0 -32.5 13t-12.5 34z" />
<glyph unicode="&#x2022;" horiz-adv-x="780" d="M172 631q0 90 64.5 154.5t154.5 64.5t153.5 -64.5t63.5 -154.5q0 -88 -63.5 -152.5t-153.5 -64.5t-154.5 63.5t-64.5 153.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1251" d="M139 47v135q0 20 13.5 33.5t33.5 13.5h133q20 0 35 -13t15 -34v-135q0 -20 -14.5 -33.5t-35.5 -13.5h-133q-20 0 -33.5 13.5t-13.5 33.5zM512 47v135q0 20 13.5 33.5t33.5 13.5h133q20 0 34.5 -13t14.5 -34v-135q0 -20 -14 -33.5t-35 -13.5h-133q-20 0 -33.5 13.5 t-13.5 33.5zM885 47v135q0 20 13 33.5t34 13.5h135q20 0 33.5 -13t13.5 -34v-135q0 -20 -13.5 -33.5t-33.5 -13.5h-135q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="373" />
<glyph unicode="&#x2030;" horiz-adv-x="2211" d="M100 1071q0 59 2 96q6 127 80 202t213 75t214 -75t81 -202q4 -74 4 -96q0 -20 -4 -90q-6 -125 -84 -195.5t-211 -70.5t-209.5 70.5t-83.5 195.5q-2 35 -2 90zM180 41q0 12 8 22l1012 1332q16 23 29.5 31t38.5 8h65q18 0 29.5 -11.5t11.5 -29.5q0 -12 -8 -23l-1010 -1331 q-16 -20 -29.5 -29.5t-37.5 -9.5h-66q-18 0 -30.5 11.5t-12.5 29.5zM246 1075q0 -49 2 -86q6 -61 40 -105t107 -44q74 0 109 43t41 106q4 74 4 86q0 18 -4 84q-4 63 -40 107.5t-110 44.5t-108.5 -44t-38.5 -108q-2 -33 -2 -84zM860 346q0 20 4 103q6 127 80 201.5t213 74.5 t214 -75t81 -201q4 -74 4 -95q0 -20 -4 -90q-6 -125 -84 -200.5t-211 -75.5t-210 75.5t-83 200.5q-4 61 -4 82zM1008 358q0 -51 2 -88q4 -61 38.5 -105t108.5 -44t108.5 43t41.5 106q4 74 4 88q0 16 -4 82q-4 63 -40 107.5t-110 44.5t-108.5 -44t-38.5 -108q-2 -33 -2 -82z M1536 354q0 57 2 95q6 127 81 201.5t214 74.5t213 -75t82 -201q4 -74 4 -95q0 -20 -4 -90q-8 -125 -85 -200.5t-210 -75.5t-210 75.5t-85 200.5q-2 35 -2 90zM1681 358q0 -51 2 -88q6 -63 41 -106t109 -43t108.5 44t38.5 105q4 74 5 88q0 16 -5 82q-2 63 -37.5 107.5 t-109.5 44.5t-109.5 -44t-40.5 -108q-2 -33 -2 -82z" />
<glyph unicode="&#x2039;" horiz-adv-x="720" d="M88 649v49q4 41 33 70l395 383q27 23 45 23t30.5 -12.5t12.5 -31.5v-75q0 -25 -8 -41.5t-29 -36.5l-313 -303l313 -303q20 -20 28.5 -36t8.5 -42v-76q0 -18 -12 -30.5t-31 -12.5q-14 0 -45 23l-395 383q-29 29 -33 69z" />
<glyph unicode="&#x203a;" horiz-adv-x="720" d="M117 217v76q0 27 8 42t29 36l313 303l-313 303q-20 20 -28.5 36.5t-8.5 41.5v75q0 18 12 31t31 13q18 0 45 -23l395 -383q33 -33 33 -70v-49q0 -37 -33 -69l-395 -383q-31 -23 -45 -23q-19 0 -31 12.5t-12 30.5z" />
<glyph unicode="&#x2044;" horiz-adv-x="327" d="M-428 41q0 12 8 22l1010 1332q16 23 29.5 31t37.5 8h56q18 0 30.5 -11.5t12.5 -29.5q0 -12 -8 -23l-1012 -1331q-16 -20 -29.5 -29.5t-38.5 -9.5h-55q-19 0 -30 11.5t-11 29.5z" />
<glyph unicode="&#x205f;" horiz-adv-x="467" />
<glyph unicode="&#x20aa;" horiz-adv-x="1691" d="M147 47v1075q0 20 13.5 33.5t34.5 13.5h446q264 0 392 -125.5t128 -377.5v-275q0 -20 -13 -33.5t-34 -13.5h-86q-20 0 -33.5 13.5t-13.5 33.5v268q0 352 -350 353h-303v-965q0 -20 -13.5 -33.5t-33.5 -13.5h-86q-21 0 -34.5 13.5t-13.5 33.5zM543 47v733q0 20 13 33.5 t34 13.5h86q20 0 33.5 -13t13.5 -34v-620h301q352 0 352 350v612q0 23 13.5 35t33.5 12h86q20 0 33.5 -13t13.5 -34v-618q0 -252 -129 -378t-393 -126h-444q-21 0 -34 13.5t-13 33.5z" />
<glyph unicode="&#x20ac;" horiz-adv-x="1519" d="M88 539v65q0 20 13.5 33.5t33.5 13.5h131v129h-131q-20 0 -33.5 13.5t-13.5 33.5v68q0 20 13.5 33.5t33.5 13.5h133q20 240 161.5 376t401.5 136q182 0 310.5 -60.5t194 -155.5t69.5 -202v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-105q-20 0 -31.5 10.5t-19.5 36.5 q-35 131 -129 188.5t-246 57.5q-332 0 -364 -344h410q20 0 33.5 -13.5t13.5 -33.5v-68q0 -20 -13.5 -33.5t-33.5 -13.5h-414v-129h414q23 0 35 -12t12 -35v-65q0 -20 -12.5 -33.5t-34.5 -13.5h-410q33 -342 364 -342q152 0 246 57t129 186q8 27 19.5 37t31.5 10h105 q18 0 31.5 -12t11.5 -31q-4 -104 -69.5 -200.5t-193.5 -156.5t-311 -60q-262 0 -403 136t-160 376h-133q-20 0 -33.5 13t-13.5 34z" />
<glyph unicode="&#x20ae;" horiz-adv-x="1181" d="M51 1305v79q0 23 13.5 36.5t33.5 13.5h983q23 0 35 -13.5t12 -36.5v-79q0 -20 -13 -34t-34 -14h-393v-342l303 58q20 4 33.5 -11.5t13.5 -35.5v-47q0 -37 -47 -48l-303 -57v-119l303 58q20 4 33.5 -10.5t13.5 -36.5v-48q0 -39 -47 -45l-303 -57v-469q0 -20 -14.5 -33.5 t-34.5 -13.5h-98q-20 0 -33.5 13.5t-13.5 33.5v432l-281 -53q-18 -4 -32.5 10.5t-14.5 36.5v47q0 37 47 47l281 54v118l-281 -53h-8q-16 0 -27.5 13.5t-11.5 31.5v47q0 39 47 47l281 54v378h-396q-20 0 -33.5 13.5t-13.5 34.5z" />
<glyph unicode="&#x20b4;" horiz-adv-x="1271" d="M84 584v28q0 23 13.5 35t33.5 12h172q76 53 207 103h-379q-20 0 -33.5 13t-13.5 34v29q0 23 13.5 35t33.5 12h678q63 37 94 81t31 105q0 106 -66.5 160.5t-181.5 54.5q-133 0 -219 -56t-102 -147q-4 -20 -19.5 -30.5t-32.5 -10.5h-102q-18 0 -30.5 12.5t-12.5 30.5 q0 84 60.5 170t178 142.5t279.5 56.5q133 0 230.5 -49t148.5 -136t51 -194q0 -111 -61 -190h94q20 0 33.5 -13.5t13.5 -33.5v-29q0 -20 -13.5 -33.5t-33.5 -13.5h-256q-74 -37 -178 -70q-33 -10 -56.5 -18t-40.5 -15h531q20 0 33.5 -13t13.5 -34v-28q0 -20 -13.5 -33.5 t-33.5 -13.5h-754q-57 -57 -57 -152q0 -111 80 -173t225 -62q164 0 251 61t107 149q14 41 54 41h98q18 0 30.5 -12t12.5 -29q0 -100 -65.5 -187t-189.5 -140t-294 -53q-143 0 -254.5 48t-174 138t-62.5 213q0 90 37 158h-62q-20 0 -33.5 13t-13.5 34z" />
<glyph unicode="&#x20b8;" horiz-adv-x="1181" d="M51 1055v63q0 20 13.5 34.5t33.5 14.5h983q20 0 33.5 -13t13.5 -36v-63q0 -20 -13 -33.5t-34 -13.5h-393v-961q0 -20 -14.5 -33.5t-34.5 -13.5h-98q-21 0 -34 13.5t-13 33.5v961h-396q-20 0 -33.5 13t-13.5 34zM51 1335v49q0 20 13.5 35t33.5 15h983q20 0 33.5 -13.5 t13.5 -36.5v-49q0 -20 -13 -32.5t-34 -12.5h-983q-20 0 -33.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x20bd;" horiz-adv-x="1368" d="M31 360v46q0 20 13 33.5t34 13.5h158v116h-158q-20 0 -33.5 13.5t-13.5 33.5v66q0 20 13 33.5t34 13.5h158v655q0 23 12 36.5t35 13.5h518q231 0 363 -112t132 -325q0 -209 -132 -318.5t-363 -109.5h-369v-116h402q20 0 33.5 -13.5t13.5 -33.5v-46q0 -20 -13.5 -33.5 t-33.5 -13.5h-402v-266q0 -20 -14.5 -33.5t-34.5 -13.5h-100q-21 0 -34 13.5t-13 33.5v266h-158q-20 0 -33.5 13.5t-13.5 33.5zM430 729h361q154 0 231.5 67.5t77.5 200.5q0 129 -78 200t-231 71h-361v-539z" />
<glyph unicode="&#x2122;" horiz-adv-x="1292" d="M96 1350v39q0 20 12.5 32.5t32.5 12.5h346q20 0 32.5 -12.5t12.5 -32.5v-39q0 -20 -12 -32.5t-33 -12.5h-106v-371q0 -20 -12.5 -32.5t-32.5 -12.5h-43q-21 0 -33 12t-12 33v371h-107q-20 0 -32.5 12t-12.5 33zM594 934v455q0 20 12 32.5t33 12.5h41q23 0 36 -12.5 t27 -32.5l121 -195l123 195q25 45 62 45h43q20 0 32.5 -12.5t12.5 -32.5v-455q0 -20 -12.5 -32.5t-32.5 -12.5h-43q-20 0 -32.5 12t-12.5 33v248l-74 -121q-14 -23 -24.5 -32t-28.5 -9h-23q-18 0 -28.5 9t-24.5 32l-74 121v-248q0 -20 -12.5 -32.5t-32.5 -12.5h-43 q-21 0 -33 12t-12 33z" />
<glyph unicode="&#x2202;" horiz-adv-x="1157" d="M100 473q-2 145 54.5 256t164 172.5t254.5 61.5q135 0 230 -66q-45 137 -130 236.5t-229 214.5q-35 25 -34 45q0 18 12 29.5t31 11.5h98q45 0 78 -27q135 -113 220 -218.5t137 -265t61 -395.5v-57q-2 -221 -126 -356t-348 -135q-149 0 -256.5 62t-163 174t-53.5 257z M289 465q0 -145 72.5 -238.5t211.5 -93.5q138 0 211.5 93t73.5 237v39q-6 137 -78.5 222t-206.5 85q-133 0 -206.5 -85t-77.5 -222v-37z" />
<glyph unicode="&#x2206;" horiz-adv-x="1302" d="M59 55v41q0 20 13 54l446 1134q29 68 92 68h82q63 0 92 -68l447 -1134q12 -33 12 -54v-41q0 -23 -16.5 -39t-38.5 -16h-1073q-23 0 -39.5 16.5t-16.5 38.5zM254 158h795l-398 1022z" />
<glyph unicode="&#x220f;" horiz-adv-x="1261" d="M158 -362v1748q0 20 13 34t34 14h852q23 0 35 -12.5t12 -35.5v-1748q0 -23 -12.5 -35.5t-34.5 -12.5h-84q-20 0 -33.5 13.5t-13.5 34.5v1617h-590v-1617q0 -23 -12.5 -35.5t-34.5 -12.5h-84q-21 0 -34 13.5t-13 34.5z" />
<glyph unicode="&#x2211;" horiz-adv-x="1099" d="M84 -281q0 31 35 70l669 711v24l-669 711q-35 39 -35 70v81q0 20 13.5 34t33.5 14h801q20 0 32.5 -13.5t12.5 -34.5v-83q0 -20 -12.5 -34t-32.5 -14h-590l598 -634q18 -16 27.5 -33t9.5 -39v-74q0 -23 -8 -37t-29 -35l-598 -634h590q20 0 32.5 -13.5t12.5 -34.5v-83 q0 -20 -12.5 -34t-32.5 -14h-801q-20 0 -33.5 13.5t-13.5 34.5v81z" />
<glyph unicode="&#x221a;" horiz-adv-x="1400" d="M51 1022q0 18 13.5 30.5t29.5 12.5h78q18 0 31.5 -10.5t17.5 -22.5l273 -817l419 1497q6 18 19.5 33.5t34.5 15.5h356q23 0 35 -12t12 -35v-59q0 -20 -13.5 -33.5t-33.5 -13.5h-264l-443 -1557q-8 -27 -20 -39t-33 -12h-133q-35 0 -47 35l-328 966z" />
<glyph unicode="&#x221e;" horiz-adv-x="1734" d="M121 555q0 193 90 324t274 131q121 0 210 -83t173 -229q86 147 173 229.5t204 82.5q186 0 277.5 -131t91.5 -324t-91 -324t-276 -131q-119 0 -208 77t-173 216q-82 -137 -170 -215t-211 -78q-184 0 -274 131t-90 324zM279 555q0 -145 58 -222t150 -77q84 0 152 78 t135 209q-78 150 -141.5 230.5t-145.5 80.5q-92 0 -150 -77t-58 -222zM958 547q70 -135 136.5 -214t152.5 -79q94 0 151.5 78t57.5 223q0 143 -59.5 221t-149.5 78q-86 0 -152.5 -82t-136.5 -225z" />
<glyph unicode="&#x222b;" horiz-adv-x="874" d="M35 -352q0 20 12 33.5t33 13.5h102q104 0 139 53t35 156v1249q0 160 79 254t255 94h107q20 0 33.5 -12t13.5 -33v-63q0 -20 -13.5 -32.5t-33.5 -12.5h-101q-96 0 -135 -52.5t-39 -158.5v-1239q0 -358 -334 -359h-108q-21 0 -33 13.5t-12 33.5v62z" />
<glyph unicode="&#x2248;" horiz-adv-x="1148" d="M150 281v75q0 29 31.5 57.5t83.5 47t110 18.5q59 0 105 -10t112 -29q59 -18 98 -27.5t88 -9.5q43 0 74 13.5t54.5 29t35.5 21.5q10 8 25 8q35 0 34 -41v-76q0 -29 -31.5 -56.5t-83.5 -45.5t-110 -18q-57 0 -102 9t-107 27q-55 18 -99 27.5t-95 9.5q-43 0 -75 -13 t-52.5 -27.5t-28.5 -18.5q-14 -12 -31 -12q-36 0 -36 41zM150 793v75q0 29 31.5 57.5t83.5 47t110 18.5q59 0 105 -10t112 -29q59 -18 98 -27.5t88 -9.5q43 0 74 13.5t53.5 29t32.5 21.5q10 8 29 8q35 0 34 -41v-76q0 -29 -31.5 -56.5t-83.5 -45.5t-110 -18q-57 0 -102 9 t-107 27q-55 18 -99 27.5t-95 9.5q-43 0 -75 -13t-52.5 -27.5t-28.5 -18.5q-14 -12 -31 -12q-36 0 -36 41z" />
<glyph unicode="&#x2260;" horiz-adv-x="1118" d="M125 328v63q0 20 12.5 32.5t32.5 12.5h233l148 357h-381q-20 0 -32.5 13t-12.5 34v63q0 20 12.5 32.5t32.5 12.5h446l86 205q4 12 17.5 23.5t34.5 11.5h61q16 0 29.5 -12.5t13.5 -28.5q0 -8 -6 -25l-74 -174h170q20 0 33.5 -12t13.5 -33v-63q0 -20 -12 -33.5t-35 -13.5 h-235l-148 -357h383q20 0 33.5 -12t13.5 -33v-63q0 -20 -12 -33.5t-35 -13.5h-448l-84 -201q-16 -37 -54 -37h-59q-18 0 -30.5 12.5t-12.5 28.5q0 10 6 25l72 172h-168q-20 0 -32.5 13t-12.5 34z" />
<glyph unicode="&#x2264;" horiz-adv-x="1089" d="M135 47v62q0 20 13.5 33.5t33.5 13.5h697q20 0 33.5 -13.5t13.5 -33.5v-62q0 -20 -13.5 -33.5t-33.5 -13.5h-697q-20 0 -33.5 13.5t-13.5 33.5zM135 809v49q0 27 13.5 46.5t46.5 37.5l641 309q29 16 49 17q18 0 29.5 -11.5t11.5 -29.5v-66q0 -27 -11.5 -42t-35.5 -25 l-549 -260l549 -263q25 -10 36 -25.5t11 -41.5v-66q0 -18 -11.5 -29.5t-29.5 -11.5q-20 1 -49 17l-641 311q-33 16 -46.5 35.5t-13.5 48.5z" />
<glyph unicode="&#x2265;" horiz-adv-x="1089" d="M164 47v62q0 20 13 33.5t34 13.5h696q20 0 33.5 -13.5t13.5 -33.5v-62q0 -20 -13 -33.5t-34 -13.5h-696q-21 0 -34 13.5t-13 33.5zM164 438v66q0 27 11 42t36 25l549 263l-549 260q-25 10 -36 25.5t-11 41.5v66q0 18 11 29.5t30 11.5q20 0 49 -17l641 -309 q33 -18 46 -37.5t13 -46.5v-49q0 -29 -13 -48.5t-46 -35.5l-641 -311q-29 -16 -49 -17q-19 0 -30 11.5t-11 29.5z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1064" d="M0 0v1065h1065v-1065h-1065z" />
<glyph unicode="&#xfb03;" horiz-adv-x="1734" d="M37 952v66q0 20 13 33.5t34 13.5h176v102q0 166 81 257.5t265 91.5h121q20 0 33.5 -13.5t13.5 -34.5v-65q0 -20 -13 -32.5t-34 -12.5h-117q-94 0 -131 -49t-37 -152v-92h441v102q0 166 80.5 257.5t265.5 91.5h305q23 0 35 -12.5t12 -35.5v-65q0 -20 -13.5 -32.5 t-33.5 -12.5h-301q-94 0 -131 -49t-37 -152v-92h469q23 0 35 -12.5t12 -34.5v-971q0 -20 -13.5 -33.5t-33.5 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v860h-334v-860q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-20 0 -32.5 13.5t-12.5 33.5v860h-441v-860q0 -20 -13 -33.5 t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v860h-176q-20 0 -33.5 12.5t-13.5 32.5z" />
<glyph unicode="&#xfb04;" horiz-adv-x="1841" d="M37 952v66q0 20 13 33.5t34 13.5h176v102q0 166 81 257.5t265 91.5h121q20 0 33.5 -13.5t13.5 -34.5v-65q0 -20 -13 -32.5t-34 -12.5h-117q-94 0 -131 -49t-37 -152v-92h441v102q0 166 80.5 257.5t265.5 91.5h112q23 0 35.5 -12.5t12.5 -35.5v-65q0 -20 -13.5 -32.5 t-34.5 -12.5h-108q-94 0 -131 -49t-37 -152v-92h438v403q0 20 13.5 34t33.5 14h88q23 0 35.5 -12.5t12.5 -35.5v-1421q0 -20 -13.5 -33.5t-34.5 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v860h-438v-860q0 -20 -13.5 -33.5t-33.5 -13.5h-90q-20 0 -32.5 13.5t-12.5 33.5v860 h-441v-860q0 -20 -13 -33.5t-34 -13.5h-88q-20 0 -33.5 13.5t-13.5 33.5v860h-176q-20 0 -33.5 12.5t-13.5 32.5z" />
<hkern u1="&#x20;" u2="&#x135;" k="31" />
<hkern u1="&#x20;" u2="v" k="55" />
<hkern u1="&#x20;" u2="V" k="55" />
<hkern u1="&#x22;" u2="&#x12d;" k="-4" />
<hkern u1="&#x22;" u2="&#x12b;" k="-10" />
<hkern u1="&#x22;" u2="&#x129;" k="-14" />
<hkern u1="&#x22;" u2="&#xef;" k="-16" />
<hkern u1="&#x22;" u2="&#xee;" k="-18" />
<hkern u1="&#x22;" u2="&#xec;" k="-16" />
<hkern u1="&#x23;" u2="&#x37;" k="33" />
<hkern u1="&#x26;" u2="&#x166;" k="70" />
<hkern u1="&#x26;" u2="x" k="-2" />
<hkern u1="&#x26;" u2="v" k="27" />
<hkern u1="&#x26;" u2="X" k="-4" />
<hkern u1="&#x26;" u2="V" k="74" />
<hkern u1="&#x27;" u2="&#x12d;" k="-4" />
<hkern u1="&#x27;" u2="&#x12b;" k="-10" />
<hkern u1="&#x27;" u2="&#x129;" k="-14" />
<hkern u1="&#x27;" u2="&#xef;" k="-16" />
<hkern u1="&#x27;" u2="&#xee;" k="-18" />
<hkern u1="&#x27;" u2="&#xec;" k="-16" />
<hkern u1="&#x28;" u2="&#x12e;" k="-2" />
<hkern u1="&#x28;" u2="&#x12d;" k="-8" />
<hkern u1="&#x28;" u2="&#x12b;" k="-12" />
<hkern u1="&#x28;" u2="&#x12a;" k="-35" />
<hkern u1="&#x28;" u2="&#x129;" k="-18" />
<hkern u1="&#x28;" u2="&#x128;" k="-41" />
<hkern u1="&#x28;" u2="&#x127;" k="-4" />
<hkern u1="&#x28;" u2="&#xf0;" k="86" />
<hkern u1="&#x28;" u2="&#xef;" k="-18" />
<hkern u1="&#x28;" u2="&#xee;" k="-23" />
<hkern u1="&#x28;" u2="&#xec;" k="-90" />
<hkern u1="&#x28;" u2="&#xcf;" k="-59" />
<hkern u1="&#x28;" u2="&#xce;" k="-68" />
<hkern u1="&#x28;" u2="x" k="57" />
<hkern u1="&#x28;" u2="v" k="96" />
<hkern u1="&#x28;" u2="j" k="-72" />
<hkern u1="&#x28;" u2="&#x7b;" k="45" />
<hkern u1="&#x28;" u2="&#x39;" k="43" />
<hkern u1="&#x28;" u2="&#x38;" k="55" />
<hkern u1="&#x28;" u2="&#x36;" k="92" />
<hkern u1="&#x28;" u2="&#x35;" k="29" />
<hkern u1="&#x28;" u2="&#x34;" k="86" />
<hkern u1="&#x28;" u2="&#x32;" k="35" />
<hkern u1="&#x28;" u2="&#x31;" k="70" />
<hkern u1="&#x28;" u2="&#x30;" k="57" />
<hkern u1="&#x28;" u2="&#x28;" k="6" />
<hkern u1="&#x29;" u2="&#x7d;" k="6" />
<hkern u1="&#x29;" u2="]" k="4" />
<hkern u1="&#x29;" u2="&#x29;" k="6" />
<hkern u1="&#x2a;" u2="&#x135;" k="-27" />
<hkern u1="&#x2a;" u2="&#x12b;" k="-10" />
<hkern u1="&#x2a;" u2="&#x129;" k="-14" />
<hkern u1="&#x2a;" u2="&#x127;" k="-8" />
<hkern u1="&#x2a;" u2="&#xf0;" k="70" />
<hkern u1="&#x2a;" u2="&#xef;" k="-14" />
<hkern u1="&#x2a;" u2="&#xee;" k="-53" />
<hkern u1="&#x2b;" u2="&#x37;" k="111" />
<hkern u1="&#x2b;" u2="&#x33;" k="27" />
<hkern u1="&#x2b;" u2="&#x32;" k="51" />
<hkern u1="&#x2b;" u2="&#x31;" k="51" />
<hkern u1="&#x2c;" u2="&#x166;" k="131" />
<hkern u1="&#x2c;" u2="&#x135;" k="39" />
<hkern u1="&#x2d;" u2="&#x166;" k="102" />
<hkern u1="&#x2d;" u2="&#x135;" k="37" />
<hkern u1="&#x2e;" u2="&#x135;" k="39" />
<hkern u1="&#x2f;" u2="&#x135;" k="35" />
<hkern u1="&#x2f;" u2="&#x12d;" k="-8" />
<hkern u1="&#x2f;" u2="&#x12b;" k="-10" />
<hkern u1="&#x2f;" u2="&#x12a;" k="-39" />
<hkern u1="&#x2f;" u2="&#x129;" k="-4" />
<hkern u1="&#x2f;" u2="&#x128;" k="-43" />
<hkern u1="&#x2f;" u2="&#xf0;" k="98" />
<hkern u1="&#x2f;" u2="&#xef;" k="-18" />
<hkern u1="&#x2f;" u2="&#xee;" k="-4" />
<hkern u1="&#x2f;" u2="&#xec;" k="-76" />
<hkern u1="&#x2f;" u2="&#xe8;" k="96" />
<hkern u1="&#x2f;" u2="&#xe0;" k="94" />
<hkern u1="&#x2f;" u2="&#xcf;" k="-63" />
<hkern u1="&#x2f;" u2="&#xce;" k="-68" />
<hkern u1="&#x2f;" u2="x" k="55" />
<hkern u1="&#x2f;" u2="v" k="57" />
<hkern u1="&#x2f;" u2="j" k="4" />
<hkern u1="&#x2f;" u2="&#x39;" k="6" />
<hkern u1="&#x2f;" u2="&#x38;" k="39" />
<hkern u1="&#x2f;" u2="&#x36;" k="106" />
<hkern u1="&#x2f;" u2="&#x35;" k="8" />
<hkern u1="&#x2f;" u2="&#x34;" k="115" />
<hkern u1="&#x2f;" u2="&#x32;" k="27" />
<hkern u1="&#x2f;" u2="&#x31;" k="8" />
<hkern u1="&#x2f;" u2="&#x30;" k="39" />
<hkern u1="&#x2f;" u2="&#x2f;" k="418" />
<hkern u1="&#x30;" u2="X" k="4" />
<hkern u1="&#x30;" u2="V" k="33" />
<hkern u1="&#x30;" u2="&#x7d;" k="49" />
<hkern u1="&#x30;" u2="]" k="47" />
<hkern u1="&#x30;" u2="\" k="39" />
<hkern u1="&#x30;" u2="&#x2f;" k="39" />
<hkern u1="&#x30;" u2="&#x29;" k="57" />
<hkern u1="&#x32;" u2="V" k="29" />
<hkern u1="&#x32;" u2="&#xf7;" k="39" />
<hkern u1="&#x32;" u2="&#xb7;" k="33" />
<hkern u1="&#x32;" u2="&#x7d;" k="23" />
<hkern u1="&#x32;" u2="]" k="27" />
<hkern u1="&#x32;" u2="\" k="27" />
<hkern u1="&#x32;" u2="&#x34;" k="20" />
<hkern u1="&#x32;" u2="&#x2b;" k="33" />
<hkern u1="&#x32;" u2="&#x29;" k="31" />
<hkern u1="&#x33;" u2="V" k="4" />
<hkern u1="&#x33;" u2="\" k="4" />
<hkern u1="&#x33;" u2="&#x31;" k="37" />
<hkern u1="&#x33;" u2="&#x2f;" k="8" />
<hkern u1="&#x34;" u2="V" k="59" />
<hkern u1="&#x34;" u2="&#x7d;" k="47" />
<hkern u1="&#x34;" u2="]" k="45" />
<hkern u1="&#x34;" u2="\" k="59" />
<hkern u1="&#x34;" u2="&#x39;" k="16" />
<hkern u1="&#x34;" u2="&#x37;" k="37" />
<hkern u1="&#x34;" u2="&#x31;" k="41" />
<hkern u1="&#x34;" u2="&#x2f;" k="4" />
<hkern u1="&#x34;" u2="&#x29;" k="53" />
<hkern u1="&#x35;" u2="V" k="4" />
<hkern u1="&#x35;" u2="\" k="4" />
<hkern u1="&#x35;" u2="&#x31;" k="33" />
<hkern u1="&#x35;" u2="&#x2f;" k="6" />
<hkern u1="&#x36;" u2="V" k="72" />
<hkern u1="&#x36;" u2="&#x7d;" k="59" />
<hkern u1="&#x36;" u2="]" k="55" />
<hkern u1="&#x36;" u2="\" k="80" />
<hkern u1="&#x36;" u2="&#x37;" k="63" />
<hkern u1="&#x36;" u2="&#x31;" k="49" />
<hkern u1="&#x36;" u2="&#x2f;" k="6" />
<hkern u1="&#x36;" u2="&#x29;" k="72" />
<hkern u1="&#x37;" u2="&#xf7;" k="63" />
<hkern u1="&#x37;" u2="&#xb7;" k="55" />
<hkern u1="&#x37;" u2="&#xa2;" k="47" />
<hkern u1="&#x37;" u2="&#x3d;" k="27" />
<hkern u1="&#x37;" u2="&#x36;" k="63" />
<hkern u1="&#x37;" u2="&#x34;" k="70" />
<hkern u1="&#x37;" u2="&#x2f;" k="133" />
<hkern u1="&#x37;" u2="&#x2b;" k="63" />
<hkern u1="&#x37;" u2="&#x23;" k="16" />
<hkern u1="&#x38;" u2="V" k="35" />
<hkern u1="&#x38;" u2="&#x7d;" k="47" />
<hkern u1="&#x38;" u2="]" k="45" />
<hkern u1="&#x38;" u2="\" k="41" />
<hkern u1="&#x38;" u2="&#x2f;" k="8" />
<hkern u1="&#x38;" u2="&#x29;" k="55" />
<hkern u1="&#x39;" u2="X" k="25" />
<hkern u1="&#x39;" u2="V" k="27" />
<hkern u1="&#x39;" u2="&#xf7;" k="27" />
<hkern u1="&#x39;" u2="&#xb7;" k="16" />
<hkern u1="&#x39;" u2="&#x7d;" k="47" />
<hkern u1="&#x39;" u2="]" k="47" />
<hkern u1="&#x39;" u2="\" k="23" />
<hkern u1="&#x39;" u2="&#x36;" k="20" />
<hkern u1="&#x39;" u2="&#x34;" k="31" />
<hkern u1="&#x39;" u2="&#x33;" k="33" />
<hkern u1="&#x39;" u2="&#x2f;" k="109" />
<hkern u1="&#x39;" u2="&#x29;" k="57" />
<hkern u1="&#x3a;" u2="&#x166;" k="68" />
<hkern u1="&#x3b;" u2="&#x166;" k="68" />
<hkern u1="&#x3d;" u2="&#x37;" k="47" />
<hkern u1="&#x40;" u2="j" k="-2" />
<hkern u1="&#x40;" u2="X" k="23" />
<hkern u1="&#x40;" u2="V" k="49" />
<hkern u1="A" u2="&#x135;" k="31" />
<hkern u1="B" u2="&#x2122;" k="2" />
<hkern u1="B" u2="&#xee;" k="-4" />
<hkern u1="B" u2="&#x7d;" k="47" />
<hkern u1="B" u2="x" k="2" />
<hkern u1="B" u2="]" k="47" />
<hkern u1="B" u2="\" k="31" />
<hkern u1="B" u2="X" k="37" />
<hkern u1="B" u2="V" k="31" />
<hkern u1="B" u2="&#x2f;" k="25" />
<hkern u1="B" u2="&#x29;" k="57" />
<hkern u1="C" u2="&#xee;" k="-2" />
<hkern u1="E" u2="&#x135;" k="18" />
<hkern u1="E" u2="&#x129;" k="-6" />
<hkern u1="E" u2="&#xef;" k="-8" />
<hkern u1="E" u2="&#xee;" k="-10" />
<hkern u1="E" u2="&#xec;" k="-4" />
<hkern u1="F" u2="&#x17f;" k="35" />
<hkern u1="F" u2="&#x135;" k="37" />
<hkern u1="F" u2="&#x131;" k="27" />
<hkern u1="F" u2="&#x12b;" k="-6" />
<hkern u1="F" u2="&#x129;" k="-10" />
<hkern u1="F" u2="&#xf0;" k="37" />
<hkern u1="F" u2="&#xef;" k="-12" />
<hkern u1="F" u2="&#xee;" k="-14" />
<hkern u1="F" u2="&#xec;" k="-18" />
<hkern u1="F" u2="&#xdf;" k="16" />
<hkern u1="F" u2="x" k="80" />
<hkern u1="F" u2="v" k="37" />
<hkern u1="F" u2="j" k="8" />
<hkern u1="F" u2="X" k="2" />
<hkern u1="F" u2="V" k="14" />
<hkern u1="F" u2="&#x34;" k="16" />
<hkern u1="F" u2="&#x32;" k="18" />
<hkern u1="F" u2="&#x2f;" k="98" />
<hkern u1="F" u2="&#x20;" k="33" />
<hkern u1="H" u2="&#x129;" k="-4" />
<hkern u1="H" u2="&#xef;" k="-4" />
<hkern u1="H" u2="&#xee;" k="-8" />
<hkern u1="I" u2="&#x129;" k="-4" />
<hkern u1="I" u2="&#xef;" k="-4" />
<hkern u1="I" u2="&#xee;" k="-8" />
<hkern u1="J" u2="&#x129;" k="-6" />
<hkern u1="J" u2="&#xef;" k="-6" />
<hkern u1="J" u2="&#xee;" k="-10" />
<hkern u1="J" u2="&#xec;" k="-4" />
<hkern u1="K" u2="&#x135;" k="29" />
<hkern u1="K" u2="&#x12d;" k="-2" />
<hkern u1="K" u2="&#x12b;" k="-10" />
<hkern u1="K" u2="&#x129;" k="-12" />
<hkern u1="K" u2="&#xef;" k="-16" />
<hkern u1="K" u2="&#xee;" k="-10" />
<hkern u1="K" u2="&#xec;" k="-31" />
<hkern u1="L" u2="&#x135;" k="45" />
<hkern u1="M" u2="&#x129;" k="-4" />
<hkern u1="M" u2="&#xef;" k="-4" />
<hkern u1="M" u2="&#xee;" k="-8" />
<hkern u1="N" u2="&#x129;" k="-4" />
<hkern u1="N" u2="&#xef;" k="-4" />
<hkern u1="N" u2="&#xee;" k="-8" />
<hkern u1="P" u2="&#x2122;" k="2" />
<hkern u1="P" u2="&#xf0;" k="39" />
<hkern u1="P" u2="&#xee;" k="-6" />
<hkern u1="P" u2="&#x7d;" k="43" />
<hkern u1="P" u2="]" k="43" />
<hkern u1="P" u2="\" k="6" />
<hkern u1="P" u2="X" k="45" />
<hkern u1="P" u2="V" k="37" />
<hkern u1="P" u2="&#x36;" k="16" />
<hkern u1="P" u2="&#x34;" k="35" />
<hkern u1="P" u2="&#x33;" k="20" />
<hkern u1="P" u2="&#x2f;" k="111" />
<hkern u1="P" u2="&#x29;" k="49" />
<hkern u1="P" u2="&#x26;" k="4" />
<hkern u1="P" u2="&#x20;" k="47" />
<hkern u1="Q" u2="&#x2f;" k="37" />
<hkern u1="T" u2="&#x159;" k="100" />
<hkern u1="T" u2="&#x135;" k="18" />
<hkern u1="T" u2="&#x131;" k="170" />
<hkern u1="T" u2="&#x12d;" k="-2" />
<hkern u1="T" u2="&#x12b;" k="-29" />
<hkern u1="T" u2="&#x129;" k="-16" />
<hkern u1="T" u2="&#x127;" k="-4" />
<hkern u1="T" u2="&#xef;" k="-31" />
<hkern u1="T" u2="&#xee;" k="-20" />
<hkern u1="T" u2="&#xed;" k="63" />
<hkern u1="T" u2="&#xec;" k="-47" />
<hkern u1="T" u2="&#xdf;" k="90" />
<hkern u1="U" u2="&#x129;" k="-6" />
<hkern u1="U" u2="&#xef;" k="-6" />
<hkern u1="U" u2="&#xee;" k="-10" />
<hkern u1="U" u2="&#xec;" k="-4" />
<hkern u1="V" u2="&#x17f;" k="27" />
<hkern u1="V" u2="&#x159;" k="66" />
<hkern u1="V" u2="&#x135;" k="25" />
<hkern u1="V" u2="&#x131;" k="80" />
<hkern u1="V" u2="&#x12d;" k="-4" />
<hkern u1="V" u2="&#x12b;" k="-12" />
<hkern u1="V" u2="&#x129;" k="-14" />
<hkern u1="V" u2="&#xf0;" k="123" />
<hkern u1="V" u2="&#xef;" k="-18" />
<hkern u1="V" u2="&#xee;" k="-16" />
<hkern u1="V" u2="&#xed;" k="27" />
<hkern u1="V" u2="&#xec;" k="-31" />
<hkern u1="V" u2="&#xdf;" k="39" />
<hkern u1="V" u2="&#xae;" k="43" />
<hkern u1="V" u2="&#xa9;" k="43" />
<hkern u1="V" u2="x" k="49" />
<hkern u1="V" u2="v" k="37" />
<hkern u1="V" u2="&#x40;" k="57" />
<hkern u1="V" u2="&#x3f;" k="4" />
<hkern u1="V" u2="&#x39;" k="25" />
<hkern u1="V" u2="&#x38;" k="33" />
<hkern u1="V" u2="&#x36;" k="84" />
<hkern u1="V" u2="&#x35;" k="31" />
<hkern u1="V" u2="&#x34;" k="86" />
<hkern u1="V" u2="&#x32;" k="29" />
<hkern u1="V" u2="&#x31;" k="23" />
<hkern u1="V" u2="&#x30;" k="31" />
<hkern u1="V" u2="&#x2f;" k="145" />
<hkern u1="V" u2="&#x26;" k="51" />
<hkern u1="V" u2="&#x20;" k="55" />
<hkern u1="W" u2="&#x159;" k="12" />
<hkern u1="W" u2="&#x131;" k="25" />
<hkern u1="W" u2="&#x12b;" k="-8" />
<hkern u1="W" u2="&#x129;" k="-12" />
<hkern u1="W" u2="&#xef;" k="-14" />
<hkern u1="W" u2="&#xee;" k="-16" />
<hkern u1="W" u2="&#xec;" k="-10" />
<hkern u1="W" u2="&#xdf;" k="2" />
<hkern u1="X" u2="&#x17f;" k="33" />
<hkern u1="X" u2="&#x135;" k="20" />
<hkern u1="X" u2="&#x12b;" k="-10" />
<hkern u1="X" u2="&#x129;" k="-12" />
<hkern u1="X" u2="&#xf0;" k="45" />
<hkern u1="X" u2="&#xef;" k="-16" />
<hkern u1="X" u2="&#xee;" k="-10" />
<hkern u1="X" u2="&#xec;" k="-31" />
<hkern u1="X" u2="&#xae;" k="41" />
<hkern u1="X" u2="&#xa9;" k="41" />
<hkern u1="X" u2="v" k="82" />
<hkern u1="X" u2="&#x3f;" k="4" />
<hkern u1="X" u2="&#x39;" k="4" />
<hkern u1="X" u2="&#x34;" k="20" />
<hkern u1="X" u2="&#x31;" k="29" />
<hkern u1="X" u2="&#x30;" k="4" />
<hkern u1="Y" u2="&#x15d;" k="29" />
<hkern u1="Y" u2="&#x159;" k="109" />
<hkern u1="Y" u2="&#x135;" k="51" />
<hkern u1="Y" u2="&#x131;" k="141" />
<hkern u1="Y" u2="&#x12d;" k="-8" />
<hkern u1="Y" u2="&#x12b;" k="-16" />
<hkern u1="Y" u2="&#x129;" k="-18" />
<hkern u1="Y" u2="&#x127;" k="-2" />
<hkern u1="Y" u2="&#xef;" k="-23" />
<hkern u1="Y" u2="&#xee;" k="-16" />
<hkern u1="Y" u2="&#xed;" k="61" />
<hkern u1="Y" u2="&#xec;" k="-37" />
<hkern u1="Y" u2="&#xe4;" k="29" />
<hkern u1="Y" u2="&#xe0;" k="31" />
<hkern u1="Y" u2="&#xdf;" k="74" />
<hkern u1="Z" u2="&#x135;" k="18" />
<hkern u1="Z" u2="&#x129;" k="-6" />
<hkern u1="Z" u2="&#xef;" k="-6" />
<hkern u1="Z" u2="&#xee;" k="-10" />
<hkern u1="Z" u2="&#xec;" k="-4" />
<hkern u1="[" u2="&#x12e;" k="4" />
<hkern u1="[" u2="&#x12d;" k="-8" />
<hkern u1="[" u2="&#x12b;" k="-12" />
<hkern u1="[" u2="&#x12a;" k="-31" />
<hkern u1="[" u2="&#x129;" k="-18" />
<hkern u1="[" u2="&#x128;" k="-35" />
<hkern u1="[" u2="&#x127;" k="-2" />
<hkern u1="[" u2="&#xf0;" k="68" />
<hkern u1="[" u2="&#xef;" k="-18" />
<hkern u1="[" u2="&#xee;" k="-23" />
<hkern u1="[" u2="&#xec;" k="-88" />
<hkern u1="[" u2="&#xcf;" k="-53" />
<hkern u1="[" u2="&#xce;" k="-63" />
<hkern u1="[" u2="x" k="47" />
<hkern u1="[" u2="v" k="76" />
<hkern u1="[" u2="j" k="-68" />
<hkern u1="[" u2="&#x7b;" k="37" />
<hkern u1="[" u2="&#x39;" k="35" />
<hkern u1="[" u2="&#x38;" k="43" />
<hkern u1="[" u2="&#x36;" k="70" />
<hkern u1="[" u2="&#x35;" k="25" />
<hkern u1="[" u2="&#x34;" k="61" />
<hkern u1="[" u2="&#x32;" k="31" />
<hkern u1="[" u2="&#x31;" k="59" />
<hkern u1="[" u2="&#x30;" k="47" />
<hkern u1="[" u2="&#x28;" k="4" />
<hkern u1="\" u2="&#x135;" k="16" />
<hkern u1="\" u2="&#xf0;" k="33" />
<hkern u1="\" u2="v" k="100" />
<hkern u1="\" u2="j" k="-25" />
<hkern u1="\" u2="V" k="145" />
<hkern u1="\" u2="\" k="416" />
<hkern u1="\" u2="&#x39;" k="74" />
<hkern u1="\" u2="&#x38;" k="8" />
<hkern u1="\" u2="&#x37;" k="49" />
<hkern u1="\" u2="&#x36;" k="25" />
<hkern u1="\" u2="&#x35;" k="6" />
<hkern u1="\" u2="&#x34;" k="20" />
<hkern u1="\" u2="&#x33;" k="6" />
<hkern u1="\" u2="&#x31;" k="121" />
<hkern u1="\" u2="&#x30;" k="39" />
<hkern u1="a" u2="&#x135;" k="8" />
<hkern u1="b" u2="&#x135;" k="14" />
<hkern u1="d" u2="&#x129;" k="-6" />
<hkern u1="d" u2="&#xef;" k="-6" />
<hkern u1="d" u2="&#xee;" k="-10" />
<hkern u1="d" u2="&#xec;" k="-4" />
<hkern u1="e" u2="&#x135;" k="10" />
<hkern u1="f" u2="&#x12d;" k="-45" />
<hkern u1="f" u2="&#x12b;" k="-16" />
<hkern u1="f" u2="&#x129;" k="-23" />
<hkern u1="f" u2="&#xef;" k="-68" />
<hkern u1="f" u2="&#xee;" k="-27" />
<hkern u1="f" u2="&#xec;" k="-117" />
<hkern u1="g" u2="j" k="-8" />
<hkern u1="h" u2="&#x135;" k="14" />
<hkern u1="i" u2="&#x129;" k="-6" />
<hkern u1="i" u2="&#xef;" k="-6" />
<hkern u1="i" u2="&#xee;" k="-8" />
<hkern u1="i" u2="&#xec;" k="-18" />
<hkern u1="j" u2="&#x129;" k="-6" />
<hkern u1="j" u2="&#xef;" k="-6" />
<hkern u1="j" u2="&#xee;" k="-8" />
<hkern u1="j" u2="&#xec;" k="-18" />
<hkern u1="j" u2="j" k="-6" />
<hkern u1="l" u2="&#x129;" k="-6" />
<hkern u1="l" u2="&#xef;" k="-6" />
<hkern u1="l" u2="&#xee;" k="-10" />
<hkern u1="l" u2="&#xec;" k="-4" />
<hkern u1="m" u2="&#x135;" k="14" />
<hkern u1="n" u2="&#x135;" k="14" />
<hkern u1="o" u2="&#x135;" k="14" />
<hkern u1="p" u2="&#x135;" k="14" />
<hkern u1="s" u2="&#x135;" k="2" />
<hkern u1="v" u2="&#x2122;" k="18" />
<hkern u1="v" u2="&#xf0;" k="53" />
<hkern u1="v" u2="&#x7d;" k="76" />
<hkern u1="v" u2="]" k="76" />
<hkern u1="v" u2="\" k="57" />
<hkern u1="v" u2="X" k="82" />
<hkern u1="v" u2="V" k="37" />
<hkern u1="v" u2="&#x2f;" k="100" />
<hkern u1="v" u2="&#x29;" k="96" />
<hkern u1="v" u2="&#x26;" k="25" />
<hkern u1="v" u2="&#x20;" k="55" />
<hkern u1="x" u2="&#x2122;" k="23" />
<hkern u1="x" u2="&#xf0;" k="37" />
<hkern u1="x" u2="&#x7d;" k="43" />
<hkern u1="x" u2="]" k="47" />
<hkern u1="x" u2="\" k="55" />
<hkern u1="x" u2="V" k="47" />
<hkern u1="x" u2="&#x29;" k="57" />
<hkern u1="&#x7b;" u2="&#x12e;" k="4" />
<hkern u1="&#x7b;" u2="&#x12d;" k="-8" />
<hkern u1="&#x7b;" u2="&#x12b;" k="-12" />
<hkern u1="&#x7b;" u2="&#x12a;" k="-33" />
<hkern u1="&#x7b;" u2="&#x129;" k="-18" />
<hkern u1="&#x7b;" u2="&#x128;" k="-39" />
<hkern u1="&#x7b;" u2="&#x127;" k="-4" />
<hkern u1="&#x7b;" u2="&#xf0;" k="74" />
<hkern u1="&#x7b;" u2="&#xef;" k="-18" />
<hkern u1="&#x7b;" u2="&#xee;" k="-23" />
<hkern u1="&#x7b;" u2="&#xec;" k="-88" />
<hkern u1="&#x7b;" u2="&#xcf;" k="-55" />
<hkern u1="&#x7b;" u2="&#xce;" k="-66" />
<hkern u1="&#x7b;" u2="x" k="43" />
<hkern u1="&#x7b;" u2="v" k="76" />
<hkern u1="&#x7b;" u2="j" k="-70" />
<hkern u1="&#x7b;" u2="&#x7b;" k="43" />
<hkern u1="&#x7b;" u2="&#x39;" k="37" />
<hkern u1="&#x7b;" u2="&#x38;" k="45" />
<hkern u1="&#x7b;" u2="&#x36;" k="76" />
<hkern u1="&#x7b;" u2="&#x35;" k="25" />
<hkern u1="&#x7b;" u2="&#x34;" k="70" />
<hkern u1="&#x7b;" u2="&#x32;" k="27" />
<hkern u1="&#x7b;" u2="&#x31;" k="57" />
<hkern u1="&#x7b;" u2="&#x30;" k="49" />
<hkern u1="&#x7b;" u2="&#x28;" k="6" />
<hkern u1="&#x7c;" u2="&#x12b;" k="-4" />
<hkern u1="&#x7c;" u2="&#x12a;" k="-4" />
<hkern u1="&#x7c;" u2="&#x129;" k="-8" />
<hkern u1="&#x7c;" u2="&#x128;" k="-2" />
<hkern u1="&#x7c;" u2="&#xef;" k="-10" />
<hkern u1="&#x7c;" u2="&#xee;" k="-12" />
<hkern u1="&#x7c;" u2="&#xec;" k="-25" />
<hkern u1="&#x7c;" u2="&#xcf;" k="-8" />
<hkern u1="&#x7c;" u2="&#xce;" k="-8" />
<hkern u1="&#x7c;" u2="&#xcc;" k="-18" />
<hkern u1="&#x7c;" u2="j" k="-14" />
<hkern u1="&#x7d;" u2="&#x7d;" k="43" />
<hkern u1="&#x7d;" u2="]" k="37" />
<hkern u1="&#x7d;" u2="&#x29;" k="45" />
<hkern u1="&#xa1;" u2="&#x166;" k="80" />
<hkern u1="&#xa1;" u2="j" k="-16" />
<hkern u1="&#xa1;" u2="V" k="45" />
<hkern u1="&#xa9;" u2="&#x166;" k="37" />
<hkern u1="&#xa9;" u2="X" k="43" />
<hkern u1="&#xa9;" u2="V" k="45" />
<hkern u1="&#xab;" u2="&#x129;" k="-6" />
<hkern u1="&#xab;" u2="&#xef;" k="-6" />
<hkern u1="&#xab;" u2="&#xee;" k="-10" />
<hkern u1="&#xae;" u2="&#x166;" k="37" />
<hkern u1="&#xae;" u2="X" k="41" />
<hkern u1="&#xae;" u2="V" k="45" />
<hkern u1="&#xb0;" u2="&#x36;" k="66" />
<hkern u1="&#xb0;" u2="&#x34;" k="90" />
<hkern u1="&#xb7;" u2="&#x37;" k="98" />
<hkern u1="&#xb7;" u2="&#x33;" k="31" />
<hkern u1="&#xb7;" u2="&#x32;" k="53" />
<hkern u1="&#xb7;" u2="&#x31;" k="47" />
<hkern u1="&#xbb;" u2="&#x166;" k="90" />
<hkern u1="&#xbf;" u2="&#xf0;" k="33" />
<hkern u1="&#xbf;" u2="v" k="115" />
<hkern u1="&#xbf;" u2="j" k="-20" />
<hkern u1="&#xbf;" u2="V" k="143" />
<hkern u1="&#xc0;" u2="&#x135;" k="31" />
<hkern u1="&#xc1;" u2="&#x135;" k="31" />
<hkern u1="&#xc2;" u2="&#x135;" k="31" />
<hkern u1="&#xc3;" u2="&#x135;" k="31" />
<hkern u1="&#xc4;" u2="&#x135;" k="31" />
<hkern u1="&#xc5;" u2="&#x135;" k="31" />
<hkern u1="&#xc6;" u2="&#x135;" k="18" />
<hkern u1="&#xc6;" u2="&#x129;" k="-6" />
<hkern u1="&#xc6;" u2="&#xef;" k="-8" />
<hkern u1="&#xc6;" u2="&#xee;" k="-10" />
<hkern u1="&#xc6;" u2="&#xec;" k="-4" />
<hkern u1="&#xc7;" u2="&#xee;" k="-2" />
<hkern u1="&#xc8;" u2="&#x135;" k="18" />
<hkern u1="&#xc8;" u2="&#x129;" k="-6" />
<hkern u1="&#xc8;" u2="&#xef;" k="-8" />
<hkern u1="&#xc8;" u2="&#xee;" k="-10" />
<hkern u1="&#xc8;" u2="&#xec;" k="-4" />
<hkern u1="&#xc9;" u2="&#x135;" k="18" />
<hkern u1="&#xc9;" u2="&#x129;" k="-6" />
<hkern u1="&#xc9;" u2="&#xef;" k="-8" />
<hkern u1="&#xc9;" u2="&#xee;" k="-10" />
<hkern u1="&#xc9;" u2="&#xec;" k="-4" />
<hkern u1="&#xca;" u2="&#x135;" k="18" />
<hkern u1="&#xca;" u2="&#x129;" k="-6" />
<hkern u1="&#xca;" u2="&#xef;" k="-8" />
<hkern u1="&#xca;" u2="&#xee;" k="-10" />
<hkern u1="&#xca;" u2="&#xec;" k="-4" />
<hkern u1="&#xcb;" u2="&#x135;" k="18" />
<hkern u1="&#xcb;" u2="&#x129;" k="-6" />
<hkern u1="&#xcb;" u2="&#xef;" k="-8" />
<hkern u1="&#xcb;" u2="&#xee;" k="-10" />
<hkern u1="&#xcb;" u2="&#xec;" k="-4" />
<hkern u1="&#xcc;" u2="&#x129;" k="-4" />
<hkern u1="&#xcc;" u2="&#xef;" k="-4" />
<hkern u1="&#xcc;" u2="&#xee;" k="-8" />
<hkern u1="&#xcd;" u2="&#x129;" k="-4" />
<hkern u1="&#xcd;" u2="&#xef;" k="-4" />
<hkern u1="&#xcd;" u2="&#xee;" k="-8" />
<hkern u1="&#xcd;" u2="&#x7c;" k="-18" />
<hkern u1="&#xce;" u2="&#x129;" k="-4" />
<hkern u1="&#xce;" u2="&#xef;" k="-4" />
<hkern u1="&#xce;" u2="&#xee;" k="-8" />
<hkern u1="&#xce;" u2="&#x7d;" k="-70" />
<hkern u1="&#xce;" u2="&#x7c;" k="-8" />
<hkern u1="&#xce;" u2="]" k="-68" />
<hkern u1="&#xce;" u2="\" k="-74" />
<hkern u1="&#xce;" u2="&#x29;" k="-74" />
<hkern u1="&#xcf;" u2="&#x129;" k="-4" />
<hkern u1="&#xcf;" u2="&#xef;" k="-4" />
<hkern u1="&#xcf;" u2="&#xee;" k="-8" />
<hkern u1="&#xcf;" u2="&#x7d;" k="-61" />
<hkern u1="&#xcf;" u2="&#x7c;" k="-8" />
<hkern u1="&#xcf;" u2="]" k="-59" />
<hkern u1="&#xcf;" u2="\" k="-70" />
<hkern u1="&#xcf;" u2="&#x29;" k="-63" />
<hkern u1="&#xd1;" u2="&#x129;" k="-4" />
<hkern u1="&#xd1;" u2="&#xef;" k="-4" />
<hkern u1="&#xd1;" u2="&#xee;" k="-8" />
<hkern u1="&#xd7;" u2="&#x37;" k="41" />
<hkern u1="&#xd8;" u2="&#x2a;" k="-4" />
<hkern u1="&#xd9;" u2="&#x129;" k="-6" />
<hkern u1="&#xd9;" u2="&#xef;" k="-6" />
<hkern u1="&#xd9;" u2="&#xee;" k="-10" />
<hkern u1="&#xd9;" u2="&#xec;" k="-4" />
<hkern u1="&#xda;" u2="&#x129;" k="-6" />
<hkern u1="&#xda;" u2="&#xef;" k="-6" />
<hkern u1="&#xda;" u2="&#xee;" k="-10" />
<hkern u1="&#xda;" u2="&#xec;" k="-4" />
<hkern u1="&#xdb;" u2="&#x129;" k="-6" />
<hkern u1="&#xdb;" u2="&#xef;" k="-6" />
<hkern u1="&#xdb;" u2="&#xee;" k="-10" />
<hkern u1="&#xdb;" u2="&#xec;" k="-4" />
<hkern u1="&#xdc;" u2="&#x129;" k="-6" />
<hkern u1="&#xdc;" u2="&#xef;" k="-6" />
<hkern u1="&#xdc;" u2="&#xee;" k="-10" />
<hkern u1="&#xdc;" u2="&#xec;" k="-4" />
<hkern u1="&#xdd;" u2="&#x15d;" k="29" />
<hkern u1="&#xdd;" u2="&#x159;" k="109" />
<hkern u1="&#xdd;" u2="&#x135;" k="51" />
<hkern u1="&#xdd;" u2="&#x131;" k="141" />
<hkern u1="&#xdd;" u2="&#x12d;" k="-8" />
<hkern u1="&#xdd;" u2="&#x12b;" k="-16" />
<hkern u1="&#xdd;" u2="&#x129;" k="-18" />
<hkern u1="&#xdd;" u2="&#x127;" k="-2" />
<hkern u1="&#xdd;" u2="&#xef;" k="-23" />
<hkern u1="&#xdd;" u2="&#xee;" k="-16" />
<hkern u1="&#xdd;" u2="&#xed;" k="61" />
<hkern u1="&#xdd;" u2="&#xec;" k="-37" />
<hkern u1="&#xdd;" u2="&#xe4;" k="29" />
<hkern u1="&#xdd;" u2="&#xe0;" k="31" />
<hkern u1="&#xdd;" u2="&#xdf;" k="74" />
<hkern u1="&#xde;" u2="&#x2122;" k="45" />
<hkern u1="&#xde;" u2="&#xba;" k="16" />
<hkern u1="&#xde;" u2="&#x7d;" k="68" />
<hkern u1="&#xde;" u2="x" k="16" />
<hkern u1="&#xde;" u2="v" k="8" />
<hkern u1="&#xde;" u2="]" k="61" />
<hkern u1="&#xde;" u2="\" k="59" />
<hkern u1="&#xde;" u2="X" k="98" />
<hkern u1="&#xde;" u2="V" k="51" />
<hkern u1="&#xde;" u2="&#x2f;" k="59" />
<hkern u1="&#xde;" u2="&#x2a;" k="20" />
<hkern u1="&#xde;" u2="&#x29;" k="80" />
<hkern u1="&#xdf;" u2="&#x2122;" k="29" />
<hkern u1="&#xdf;" u2="&#x17f;" k="2" />
<hkern u1="&#xdf;" u2="&#x7d;" k="51" />
<hkern u1="&#xdf;" u2="x" k="18" />
<hkern u1="&#xdf;" u2="v" k="33" />
<hkern u1="&#xdf;" u2="]" k="49" />
<hkern u1="&#xdf;" u2="\" k="49" />
<hkern u1="&#xdf;" u2="&#x2f;" k="31" />
<hkern u1="&#xdf;" u2="&#x29;" k="61" />
<hkern u1="&#xe0;" u2="&#x135;" k="8" />
<hkern u1="&#xe1;" u2="&#x135;" k="8" />
<hkern u1="&#xe1;" u2="\" k="90" />
<hkern u1="&#xe2;" u2="&#x135;" k="8" />
<hkern u1="&#xe3;" u2="&#x135;" k="8" />
<hkern u1="&#xe4;" u2="&#x135;" k="8" />
<hkern u1="&#xe5;" u2="&#x135;" k="8" />
<hkern u1="&#xe6;" u2="&#x135;" k="10" />
<hkern u1="&#xe8;" u2="&#x135;" k="10" />
<hkern u1="&#xe9;" u2="&#x135;" k="10" />
<hkern u1="&#xe9;" u2="\" k="90" />
<hkern u1="&#xea;" u2="&#x135;" k="10" />
<hkern u1="&#xeb;" u2="&#x135;" k="10" />
<hkern u1="&#xec;" u2="&#x129;" k="-6" />
<hkern u1="&#xec;" u2="&#xef;" k="-6" />
<hkern u1="&#xec;" u2="&#xee;" k="-8" />
<hkern u1="&#xec;" u2="&#xec;" k="-18" />
<hkern u1="&#xed;" u2="&#x2122;" k="-66" />
<hkern u1="&#xed;" u2="&#x201d;" k="-4" />
<hkern u1="&#xed;" u2="&#x2019;" k="-4" />
<hkern u1="&#xed;" u2="&#x17e;" k="-8" />
<hkern u1="&#xed;" u2="&#x161;" k="-10" />
<hkern u1="&#xed;" u2="&#x159;" k="-16" />
<hkern u1="&#xed;" u2="&#x142;" k="-4" />
<hkern u1="&#xed;" u2="&#x140;" k="-4" />
<hkern u1="&#xed;" u2="&#x13e;" k="-4" />
<hkern u1="&#xed;" u2="&#x13c;" k="-4" />
<hkern u1="&#xed;" u2="&#x13a;" k="-4" />
<hkern u1="&#xed;" u2="&#x137;" k="-4" />
<hkern u1="&#xed;" u2="&#x133;" k="-20" />
<hkern u1="&#xed;" u2="&#x131;" k="-20" />
<hkern u1="&#xed;" u2="&#x12f;" k="-20" />
<hkern u1="&#xed;" u2="&#x12d;" k="-20" />
<hkern u1="&#xed;" u2="&#x12b;" k="-20" />
<hkern u1="&#xed;" u2="&#x129;" k="-20" />
<hkern u1="&#xed;" u2="&#x127;" k="-4" />
<hkern u1="&#xed;" u2="&#x125;" k="-4" />
<hkern u1="&#xed;" u2="&#x10d;" k="-2" />
<hkern u1="&#xed;" u2="&#xfe;" k="-4" />
<hkern u1="&#xed;" u2="&#xef;" k="-20" />
<hkern u1="&#xed;" u2="&#xee;" k="-20" />
<hkern u1="&#xed;" u2="&#xec;" k="-20" />
<hkern u1="&#xed;" u2="&#xdf;" k="-4" />
<hkern u1="&#xed;" u2="&#x7d;" k="-88" />
<hkern u1="&#xed;" u2="&#x7c;" k="-25" />
<hkern u1="&#xed;" u2="l" k="-4" />
<hkern u1="&#xed;" u2="k" k="-4" />
<hkern u1="&#xed;" u2="j" k="-14" />
<hkern u1="&#xed;" u2="i" k="-20" />
<hkern u1="&#xed;" u2="h" k="-4" />
<hkern u1="&#xed;" u2="b" k="-4" />
<hkern u1="&#xed;" u2="]" k="-88" />
<hkern u1="&#xed;" u2="\" k="-76" />
<hkern u1="&#xed;" u2="&#x29;" k="-90" />
<hkern u1="&#xed;" u2="&#x27;" k="-18" />
<hkern u1="&#xed;" u2="&#x22;" k="-18" />
<hkern u1="&#xed;" u2="&#x21;" k="-12" />
<hkern u1="&#xee;" u2="&#x2122;" k="-37" />
<hkern u1="&#xee;" u2="&#x203a;" k="-10" />
<hkern u1="&#xee;" u2="&#x201d;" k="-10" />
<hkern u1="&#xee;" u2="&#x201c;" k="-4" />
<hkern u1="&#xee;" u2="&#x2019;" k="-10" />
<hkern u1="&#xee;" u2="&#x2018;" k="-4" />
<hkern u1="&#xee;" u2="&#x142;" k="-10" />
<hkern u1="&#xee;" u2="&#x140;" k="-10" />
<hkern u1="&#xee;" u2="&#x13e;" k="-10" />
<hkern u1="&#xee;" u2="&#x13c;" k="-10" />
<hkern u1="&#xee;" u2="&#x13a;" k="-10" />
<hkern u1="&#xee;" u2="&#x137;" k="-10" />
<hkern u1="&#xee;" u2="&#x133;" k="-8" />
<hkern u1="&#xee;" u2="&#x131;" k="-8" />
<hkern u1="&#xee;" u2="&#x12f;" k="-8" />
<hkern u1="&#xee;" u2="&#x12d;" k="-8" />
<hkern u1="&#xee;" u2="&#x12b;" k="-8" />
<hkern u1="&#xee;" u2="&#x129;" k="-8" />
<hkern u1="&#xee;" u2="&#x127;" k="-10" />
<hkern u1="&#xee;" u2="&#x125;" k="-10" />
<hkern u1="&#xee;" u2="&#xfe;" k="-10" />
<hkern u1="&#xee;" u2="&#xef;" k="-8" />
<hkern u1="&#xee;" u2="&#xee;" k="-8" />
<hkern u1="&#xee;" u2="&#xed;" k="-8" />
<hkern u1="&#xee;" u2="&#xec;" k="-8" />
<hkern u1="&#xee;" u2="&#xdf;" k="-10" />
<hkern u1="&#xee;" u2="&#xbb;" k="-10" />
<hkern u1="&#xee;" u2="&#xba;" k="-20" />
<hkern u1="&#xee;" u2="&#xaa;" k="-14" />
<hkern u1="&#xee;" u2="&#x7d;" k="-23" />
<hkern u1="&#xee;" u2="&#x7c;" k="-12" />
<hkern u1="&#xee;" u2="l" k="-10" />
<hkern u1="&#xee;" u2="k" k="-10" />
<hkern u1="&#xee;" u2="j" k="-4" />
<hkern u1="&#xee;" u2="i" k="-8" />
<hkern u1="&#xee;" u2="h" k="-10" />
<hkern u1="&#xee;" u2="b" k="-10" />
<hkern u1="&#xee;" u2="]" k="-23" />
<hkern u1="&#xee;" u2="\" k="-4" />
<hkern u1="&#xee;" u2="&#x3f;" k="-51" />
<hkern u1="&#xee;" u2="&#x2a;" k="-51" />
<hkern u1="&#xee;" u2="&#x29;" k="-23" />
<hkern u1="&#xee;" u2="&#x27;" k="-18" />
<hkern u1="&#xee;" u2="&#x22;" k="-18" />
<hkern u1="&#xee;" u2="&#x21;" k="-16" />
<hkern u1="&#xef;" u2="&#x2122;" k="-43" />
<hkern u1="&#xef;" u2="&#x203a;" k="-6" />
<hkern u1="&#xef;" u2="&#x201d;" k="-8" />
<hkern u1="&#xef;" u2="&#x2019;" k="-8" />
<hkern u1="&#xef;" u2="&#x142;" k="-6" />
<hkern u1="&#xef;" u2="&#x140;" k="-6" />
<hkern u1="&#xef;" u2="&#x13e;" k="-6" />
<hkern u1="&#xef;" u2="&#x13c;" k="-6" />
<hkern u1="&#xef;" u2="&#x13a;" k="-6" />
<hkern u1="&#xef;" u2="&#x137;" k="-6" />
<hkern u1="&#xef;" u2="&#x133;" k="-6" />
<hkern u1="&#xef;" u2="&#x131;" k="-6" />
<hkern u1="&#xef;" u2="&#x12f;" k="-6" />
<hkern u1="&#xef;" u2="&#x12d;" k="-6" />
<hkern u1="&#xef;" u2="&#x12b;" k="-6" />
<hkern u1="&#xef;" u2="&#x129;" k="-6" />
<hkern u1="&#xef;" u2="&#x127;" k="-6" />
<hkern u1="&#xef;" u2="&#x125;" k="-6" />
<hkern u1="&#xef;" u2="&#xfe;" k="-6" />
<hkern u1="&#xef;" u2="&#xef;" k="-6" />
<hkern u1="&#xef;" u2="&#xee;" k="-6" />
<hkern u1="&#xef;" u2="&#xed;" k="-6" />
<hkern u1="&#xef;" u2="&#xec;" k="-6" />
<hkern u1="&#xef;" u2="&#xdf;" k="-6" />
<hkern u1="&#xef;" u2="&#xbb;" k="-6" />
<hkern u1="&#xef;" u2="&#xba;" k="-16" />
<hkern u1="&#xef;" u2="&#xaa;" k="-12" />
<hkern u1="&#xef;" u2="&#x7d;" k="-18" />
<hkern u1="&#xef;" u2="&#x7c;" k="-10" />
<hkern u1="&#xef;" u2="l" k="-6" />
<hkern u1="&#xef;" u2="k" k="-6" />
<hkern u1="&#xef;" u2="i" k="-6" />
<hkern u1="&#xef;" u2="h" k="-6" />
<hkern u1="&#xef;" u2="b" k="-6" />
<hkern u1="&#xef;" u2="]" k="-18" />
<hkern u1="&#xef;" u2="\" k="-18" />
<hkern u1="&#xef;" u2="&#x3f;" k="-8" />
<hkern u1="&#xef;" u2="&#x2a;" k="-14" />
<hkern u1="&#xef;" u2="&#x29;" k="-18" />
<hkern u1="&#xef;" u2="&#x27;" k="-16" />
<hkern u1="&#xef;" u2="&#x22;" k="-16" />
<hkern u1="&#xef;" u2="&#x21;" k="-14" />
<hkern u1="&#xf0;" u2="&#x2122;" k="4" />
<hkern u1="&#xf0;" u2="&#xba;" k="4" />
<hkern u1="&#xf0;" u2="&#x7d;" k="41" />
<hkern u1="&#xf0;" u2="x" k="23" />
<hkern u1="&#xf0;" u2="v" k="12" />
<hkern u1="&#xf0;" u2="]" k="39" />
<hkern u1="&#xf0;" u2="\" k="8" />
<hkern u1="&#xf0;" u2="&#x2f;" k="35" />
<hkern u1="&#xf0;" u2="&#x2a;" k="4" />
<hkern u1="&#xf0;" u2="&#x29;" k="51" />
<hkern u1="&#xf1;" u2="&#x135;" k="14" />
<hkern u1="&#xf2;" u2="&#x135;" k="14" />
<hkern u1="&#xf3;" u2="&#x135;" k="14" />
<hkern u1="&#xf4;" u2="&#x135;" k="14" />
<hkern u1="&#xf5;" u2="&#x135;" k="14" />
<hkern u1="&#xf6;" u2="&#x135;" k="14" />
<hkern u1="&#xf7;" u2="&#x39;" k="23" />
<hkern u1="&#xf7;" u2="&#x37;" k="100" />
<hkern u1="&#xf7;" u2="&#x33;" k="37" />
<hkern u1="&#xf7;" u2="&#x32;" k="61" />
<hkern u1="&#xf7;" u2="&#x31;" k="63" />
<hkern u1="&#xf8;" u2="&#x135;" k="14" />
<hkern u1="&#xfe;" u2="&#x135;" k="14" />
<hkern u1="&#x100;" u2="&#x135;" k="31" />
<hkern u1="&#x101;" u2="&#x135;" k="8" />
<hkern u1="&#x102;" u2="&#x135;" k="31" />
<hkern u1="&#x103;" u2="&#x135;" k="8" />
<hkern u1="&#x104;" u2="&#x135;" k="31" />
<hkern u1="&#x104;" u2="j" k="-55" />
<hkern u1="&#x105;" u2="&#x135;" k="8" />
<hkern u1="&#x105;" u2="j" k="-18" />
<hkern u1="&#x106;" u2="&#xee;" k="-2" />
<hkern u1="&#x107;" u2="\" k="84" />
<hkern u1="&#x108;" u2="&#xee;" k="-2" />
<hkern u1="&#x10a;" u2="&#xee;" k="-2" />
<hkern u1="&#x10c;" u2="&#xee;" k="-2" />
<hkern u1="&#x10f;" u2="&#x3b;" k="-2" />
<hkern u1="&#x111;" u2="&#x2122;" k="-10" />
<hkern u1="&#x111;" u2="&#x129;" k="-6" />
<hkern u1="&#x111;" u2="&#xef;" k="-6" />
<hkern u1="&#x111;" u2="&#xee;" k="-10" />
<hkern u1="&#x111;" u2="&#xec;" k="-4" />
<hkern u1="&#x111;" u2="&#xba;" k="-4" />
<hkern u1="&#x111;" u2="&#x7d;" k="-4" />
<hkern u1="&#x111;" u2="]" k="-4" />
<hkern u1="&#x111;" u2="&#x3f;" k="-2" />
<hkern u1="&#x111;" u2="&#x2a;" k="-8" />
<hkern u1="&#x111;" u2="&#x29;" k="-4" />
<hkern u1="&#x112;" u2="&#x135;" k="18" />
<hkern u1="&#x112;" u2="&#x129;" k="-6" />
<hkern u1="&#x112;" u2="&#xef;" k="-8" />
<hkern u1="&#x112;" u2="&#xee;" k="-10" />
<hkern u1="&#x112;" u2="&#xec;" k="-4" />
<hkern u1="&#x113;" u2="&#x135;" k="10" />
<hkern u1="&#x114;" u2="&#x135;" k="18" />
<hkern u1="&#x114;" u2="&#x129;" k="-6" />
<hkern u1="&#x114;" u2="&#xef;" k="-8" />
<hkern u1="&#x114;" u2="&#xee;" k="-10" />
<hkern u1="&#x114;" u2="&#xec;" k="-4" />
<hkern u1="&#x115;" u2="&#x135;" k="10" />
<hkern u1="&#x116;" u2="&#x135;" k="18" />
<hkern u1="&#x116;" u2="&#x129;" k="-6" />
<hkern u1="&#x116;" u2="&#xef;" k="-8" />
<hkern u1="&#x116;" u2="&#xee;" k="-10" />
<hkern u1="&#x116;" u2="&#xec;" k="-4" />
<hkern u1="&#x117;" u2="&#x135;" k="10" />
<hkern u1="&#x118;" u2="&#x135;" k="18" />
<hkern u1="&#x118;" u2="&#x129;" k="-6" />
<hkern u1="&#x118;" u2="&#xef;" k="-8" />
<hkern u1="&#x118;" u2="&#xee;" k="-10" />
<hkern u1="&#x118;" u2="&#xec;" k="-4" />
<hkern u1="&#x118;" u2="j" k="-18" />
<hkern u1="&#x119;" u2="&#x135;" k="10" />
<hkern u1="&#x11a;" u2="&#x135;" k="18" />
<hkern u1="&#x11a;" u2="&#x129;" k="-6" />
<hkern u1="&#x11a;" u2="&#xef;" k="-8" />
<hkern u1="&#x11a;" u2="&#xee;" k="-10" />
<hkern u1="&#x11a;" u2="&#xec;" k="-4" />
<hkern u1="&#x11b;" u2="&#x135;" k="10" />
<hkern u1="&#x11d;" u2="j" k="-8" />
<hkern u1="&#x11f;" u2="j" k="-8" />
<hkern u1="&#x121;" u2="j" k="-8" />
<hkern u1="&#x123;" u2="j" k="-8" />
<hkern u1="&#x124;" u2="&#x129;" k="-4" />
<hkern u1="&#x124;" u2="&#xef;" k="-4" />
<hkern u1="&#x124;" u2="&#xee;" k="-8" />
<hkern u1="&#x125;" u2="&#x135;" k="14" />
<hkern u1="&#x126;" u2="&#x129;" k="-4" />
<hkern u1="&#x126;" u2="&#xef;" k="-4" />
<hkern u1="&#x126;" u2="&#xee;" k="-8" />
<hkern u1="&#x127;" u2="&#x135;" k="14" />
<hkern u1="&#x128;" u2="&#x129;" k="-4" />
<hkern u1="&#x128;" u2="&#xef;" k="-4" />
<hkern u1="&#x128;" u2="&#xee;" k="-8" />
<hkern u1="&#x128;" u2="&#x7d;" k="6" />
<hkern u1="&#x128;" u2="&#x29;" k="6" />
<hkern u1="&#x129;" u2="&#x2122;" k="-43" />
<hkern u1="&#x129;" u2="&#x142;" k="-4" />
<hkern u1="&#x129;" u2="&#x140;" k="-4" />
<hkern u1="&#x129;" u2="&#x13e;" k="-4" />
<hkern u1="&#x129;" u2="&#x13c;" k="-4" />
<hkern u1="&#x129;" u2="&#x13a;" k="-4" />
<hkern u1="&#x129;" u2="&#x137;" k="-4" />
<hkern u1="&#x129;" u2="&#x133;" k="-4" />
<hkern u1="&#x129;" u2="&#x131;" k="-4" />
<hkern u1="&#x129;" u2="&#x12f;" k="-4" />
<hkern u1="&#x129;" u2="&#x12d;" k="-4" />
<hkern u1="&#x129;" u2="&#x12b;" k="-4" />
<hkern u1="&#x129;" u2="&#x129;" k="-4" />
<hkern u1="&#x129;" u2="&#x127;" k="-4" />
<hkern u1="&#x129;" u2="&#x125;" k="-4" />
<hkern u1="&#x129;" u2="&#xfe;" k="-4" />
<hkern u1="&#x129;" u2="&#xef;" k="-4" />
<hkern u1="&#x129;" u2="&#xee;" k="-4" />
<hkern u1="&#x129;" u2="&#xed;" k="-4" />
<hkern u1="&#x129;" u2="&#xec;" k="-4" />
<hkern u1="&#x129;" u2="&#xdf;" k="-4" />
<hkern u1="&#x129;" u2="&#xba;" k="-4" />
<hkern u1="&#x129;" u2="&#xaa;" k="-2" />
<hkern u1="&#x129;" u2="&#x7d;" k="-18" />
<hkern u1="&#x129;" u2="&#x7c;" k="-8" />
<hkern u1="&#x129;" u2="l" k="-4" />
<hkern u1="&#x129;" u2="k" k="-4" />
<hkern u1="&#x129;" u2="i" k="-4" />
<hkern u1="&#x129;" u2="h" k="-4" />
<hkern u1="&#x129;" u2="b" k="-4" />
<hkern u1="&#x129;" u2="]" k="-18" />
<hkern u1="&#x129;" u2="\" k="-18" />
<hkern u1="&#x129;" u2="&#x2a;" k="-4" />
<hkern u1="&#x129;" u2="&#x29;" k="-18" />
<hkern u1="&#x129;" u2="&#x27;" k="-14" />
<hkern u1="&#x129;" u2="&#x22;" k="-14" />
<hkern u1="&#x129;" u2="&#x21;" k="-10" />
<hkern u1="&#x12a;" u2="&#x129;" k="-4" />
<hkern u1="&#x12a;" u2="&#xef;" k="-4" />
<hkern u1="&#x12a;" u2="&#xee;" k="-8" />
<hkern u1="&#x12a;" u2="&#x7d;" k="-41" />
<hkern u1="&#x12a;" u2="&#x7c;" k="-6" />
<hkern u1="&#x12a;" u2="]" k="-37" />
<hkern u1="&#x12a;" u2="\" k="-47" />
<hkern u1="&#x12a;" u2="&#x29;" k="-43" />
<hkern u1="&#x12b;" u2="&#x2122;" k="-45" />
<hkern u1="&#x12b;" u2="&#x129;" k="-6" />
<hkern u1="&#x12b;" u2="&#xef;" k="-6" />
<hkern u1="&#x12b;" u2="&#xee;" k="-8" />
<hkern u1="&#x12b;" u2="&#xec;" k="-18" />
<hkern u1="&#x12b;" u2="&#xba;" k="-10" />
<hkern u1="&#x12b;" u2="&#xaa;" k="-6" />
<hkern u1="&#x12b;" u2="&#x7d;" k="-12" />
<hkern u1="&#x12b;" u2="&#x7c;" k="-4" />
<hkern u1="&#x12b;" u2="]" k="-12" />
<hkern u1="&#x12b;" u2="\" k="-10" />
<hkern u1="&#x12b;" u2="&#x3f;" k="-2" />
<hkern u1="&#x12b;" u2="&#x2a;" k="-10" />
<hkern u1="&#x12b;" u2="&#x29;" k="-12" />
<hkern u1="&#x12b;" u2="&#x27;" k="-10" />
<hkern u1="&#x12b;" u2="&#x22;" k="-10" />
<hkern u1="&#x12b;" u2="&#x21;" k="-8" />
<hkern u1="&#x12c;" u2="&#x129;" k="-4" />
<hkern u1="&#x12c;" u2="&#xef;" k="-4" />
<hkern u1="&#x12c;" u2="&#xee;" k="-8" />
<hkern u1="&#x12d;" u2="&#x2122;" k="-6" />
<hkern u1="&#x12d;" u2="&#x129;" k="-6" />
<hkern u1="&#x12d;" u2="&#xef;" k="-6" />
<hkern u1="&#x12d;" u2="&#xee;" k="-8" />
<hkern u1="&#x12d;" u2="&#xec;" k="-18" />
<hkern u1="&#x12d;" u2="&#x7d;" k="-8" />
<hkern u1="&#x12d;" u2="]" k="-8" />
<hkern u1="&#x12d;" u2="\" k="-8" />
<hkern u1="&#x12d;" u2="&#x29;" k="-8" />
<hkern u1="&#x12d;" u2="&#x27;" k="-2" />
<hkern u1="&#x12d;" u2="&#x22;" k="-2" />
<hkern u1="&#x12e;" u2="&#x129;" k="-4" />
<hkern u1="&#x12e;" u2="&#xef;" k="-4" />
<hkern u1="&#x12e;" u2="&#xee;" k="-8" />
<hkern u1="&#x12e;" u2="j" k="-14" />
<hkern u1="&#x12f;" u2="&#x129;" k="-6" />
<hkern u1="&#x12f;" u2="&#xef;" k="-6" />
<hkern u1="&#x12f;" u2="&#xee;" k="-8" />
<hkern u1="&#x12f;" u2="&#xec;" k="-18" />
<hkern u1="&#x12f;" u2="j" k="-16" />
<hkern u1="&#x130;" u2="&#x129;" k="-4" />
<hkern u1="&#x130;" u2="&#xef;" k="-4" />
<hkern u1="&#x130;" u2="&#xee;" k="-8" />
<hkern u1="&#x131;" u2="&#x129;" k="-6" />
<hkern u1="&#x131;" u2="&#xef;" k="-6" />
<hkern u1="&#x131;" u2="&#xee;" k="-8" />
<hkern u1="&#x131;" u2="&#xec;" k="-18" />
<hkern u1="&#x132;" u2="&#x7d;" k="18" />
<hkern u1="&#x132;" u2="]" k="18" />
<hkern u1="&#x132;" u2="X" k="2" />
<hkern u1="&#x132;" u2="&#x2f;" k="43" />
<hkern u1="&#x132;" u2="&#x29;" k="20" />
<hkern u1="&#x133;" u2="&#x129;" k="-6" />
<hkern u1="&#x133;" u2="&#xef;" k="-6" />
<hkern u1="&#x133;" u2="&#xee;" k="-8" />
<hkern u1="&#x133;" u2="&#xec;" k="-18" />
<hkern u1="&#x133;" u2="j" k="-6" />
<hkern u1="&#x134;" u2="&#x129;" k="-6" />
<hkern u1="&#x134;" u2="&#xef;" k="-6" />
<hkern u1="&#x134;" u2="&#xee;" k="-10" />
<hkern u1="&#x134;" u2="&#xec;" k="-4" />
<hkern u1="&#x135;" u2="&#x129;" k="-6" />
<hkern u1="&#x135;" u2="&#xef;" k="-6" />
<hkern u1="&#x135;" u2="&#xee;" k="-8" />
<hkern u1="&#x135;" u2="&#xec;" k="-18" />
<hkern u1="&#x135;" u2="j" k="-2" />
<hkern u1="&#x136;" u2="&#x135;" k="29" />
<hkern u1="&#x136;" u2="&#x12d;" k="-2" />
<hkern u1="&#x136;" u2="&#x12b;" k="-10" />
<hkern u1="&#x136;" u2="&#x129;" k="-12" />
<hkern u1="&#x136;" u2="&#xef;" k="-16" />
<hkern u1="&#x136;" u2="&#xee;" k="-10" />
<hkern u1="&#x136;" u2="&#xec;" k="-31" />
<hkern u1="&#x139;" u2="&#x135;" k="45" />
<hkern u1="&#x13a;" u2="&#x129;" k="-6" />
<hkern u1="&#x13a;" u2="&#xef;" k="-6" />
<hkern u1="&#x13a;" u2="&#xee;" k="-10" />
<hkern u1="&#x13a;" u2="&#xec;" k="-4" />
<hkern u1="&#x13a;" u2="&#x7c;" k="-27" />
<hkern u1="&#x13b;" u2="&#x135;" k="45" />
<hkern u1="&#x13c;" u2="&#x129;" k="-6" />
<hkern u1="&#x13c;" u2="&#xef;" k="-6" />
<hkern u1="&#x13c;" u2="&#xee;" k="-10" />
<hkern u1="&#x13c;" u2="&#xec;" k="-4" />
<hkern u1="&#x13d;" u2="&#x2122;" k="201" />
<hkern u1="&#x13d;" u2="&#x201d;" k="205" />
<hkern u1="&#x13d;" u2="&#x2019;" k="205" />
<hkern u1="&#x13d;" u2="&#x1ef2;" k="186" />
<hkern u1="&#x13d;" u2="&#x1e84;" k="90" />
<hkern u1="&#x13d;" u2="&#x1e82;" k="90" />
<hkern u1="&#x13d;" u2="&#x1e80;" k="90" />
<hkern u1="&#x13d;" u2="&#x21a;" k="217" />
<hkern u1="&#x13d;" u2="&#x178;" k="186" />
<hkern u1="&#x13d;" u2="&#x176;" k="186" />
<hkern u1="&#x13d;" u2="&#x174;" k="90" />
<hkern u1="&#x13d;" u2="&#x166;" k="217" />
<hkern u1="&#x13d;" u2="&#x164;" k="217" />
<hkern u1="&#x13d;" u2="&#x135;" k="45" />
<hkern u1="&#x13d;" u2="&#xdd;" k="186" />
<hkern u1="&#x13d;" u2="&#xba;" k="217" />
<hkern u1="&#x13d;" u2="&#xaa;" k="219" />
<hkern u1="&#x13d;" u2="\" k="154" />
<hkern u1="&#x13d;" u2="Y" k="186" />
<hkern u1="&#x13d;" u2="W" k="90" />
<hkern u1="&#x13d;" u2="V" k="170" />
<hkern u1="&#x13d;" u2="T" k="217" />
<hkern u1="&#x13d;" u2="&#x2a;" k="217" />
<hkern u1="&#x13d;" u2="&#x27;" k="186" />
<hkern u1="&#x13d;" u2="&#x22;" k="186" />
<hkern u1="&#x13e;" u2="&#x161;" k="-4" />
<hkern u1="&#x13e;" u2="&#x10d;" k="4" />
<hkern u1="&#x13e;" u2="&#x3b;" k="-2" />
<hkern u1="&#x13f;" u2="&#x135;" k="45" />
<hkern u1="&#x141;" u2="&#x135;" k="45" />
<hkern u1="&#x142;" u2="&#x129;" k="-6" />
<hkern u1="&#x142;" u2="&#xef;" k="-6" />
<hkern u1="&#x142;" u2="&#xee;" k="-10" />
<hkern u1="&#x142;" u2="&#xec;" k="-4" />
<hkern u1="&#x143;" u2="&#x129;" k="-4" />
<hkern u1="&#x143;" u2="&#xef;" k="-4" />
<hkern u1="&#x143;" u2="&#xee;" k="-8" />
<hkern u1="&#x144;" u2="&#x135;" k="14" />
<hkern u1="&#x145;" u2="&#x129;" k="-4" />
<hkern u1="&#x145;" u2="&#xef;" k="-4" />
<hkern u1="&#x145;" u2="&#xee;" k="-8" />
<hkern u1="&#x146;" u2="&#x135;" k="14" />
<hkern u1="&#x147;" u2="&#x129;" k="-4" />
<hkern u1="&#x147;" u2="&#xef;" k="-4" />
<hkern u1="&#x147;" u2="&#xee;" k="-8" />
<hkern u1="&#x148;" u2="&#x135;" k="14" />
<hkern u1="&#x149;" u2="&#x135;" k="14" />
<hkern u1="&#x14a;" u2="&#x129;" k="-4" />
<hkern u1="&#x14a;" u2="&#xef;" k="-4" />
<hkern u1="&#x14a;" u2="&#xee;" k="-8" />
<hkern u1="&#x14b;" u2="&#x135;" k="14" />
<hkern u1="&#x14d;" u2="&#x135;" k="14" />
<hkern u1="&#x14f;" u2="&#x135;" k="14" />
<hkern u1="&#x151;" u2="&#x2122;" k="59" />
<hkern u1="&#x151;" u2="&#x135;" k="14" />
<hkern u1="&#x151;" u2="&#x7d;" k="63" />
<hkern u1="&#x151;" u2="]" k="57" />
<hkern u1="&#x151;" u2="\" k="78" />
<hkern u1="&#x151;" u2="&#x29;" k="80" />
<hkern u1="&#x152;" u2="&#x135;" k="18" />
<hkern u1="&#x152;" u2="&#x129;" k="-6" />
<hkern u1="&#x152;" u2="&#xef;" k="-8" />
<hkern u1="&#x152;" u2="&#xee;" k="-10" />
<hkern u1="&#x152;" u2="&#xec;" k="-4" />
<hkern u1="&#x153;" u2="&#x135;" k="10" />
<hkern u1="&#x155;" u2="&#x7d;" k="12" />
<hkern u1="&#x155;" u2="]" k="14" />
<hkern u1="&#x155;" u2="\" k="-6" />
<hkern u1="&#x155;" u2="&#x29;" k="10" />
<hkern u1="&#x159;" u2="&#x7d;" k="37" />
<hkern u1="&#x159;" u2="]" k="37" />
<hkern u1="&#x159;" u2="\" k="6" />
<hkern u1="&#x159;" u2="&#x29;" k="35" />
<hkern u1="&#x15b;" u2="&#x135;" k="2" />
<hkern u1="&#x15b;" u2="\" k="86" />
<hkern u1="&#x15d;" u2="&#x135;" k="2" />
<hkern u1="&#x15f;" u2="&#x135;" k="2" />
<hkern u1="&#x161;" u2="&#x135;" k="2" />
<hkern u1="&#x164;" u2="&#x159;" k="100" />
<hkern u1="&#x164;" u2="&#x135;" k="18" />
<hkern u1="&#x164;" u2="&#x131;" k="170" />
<hkern u1="&#x164;" u2="&#x12d;" k="-2" />
<hkern u1="&#x164;" u2="&#x12b;" k="-29" />
<hkern u1="&#x164;" u2="&#x129;" k="-16" />
<hkern u1="&#x164;" u2="&#x127;" k="-4" />
<hkern u1="&#x164;" u2="&#xef;" k="-31" />
<hkern u1="&#x164;" u2="&#xee;" k="-20" />
<hkern u1="&#x164;" u2="&#xed;" k="63" />
<hkern u1="&#x164;" u2="&#xec;" k="-47" />
<hkern u1="&#x164;" u2="&#xdf;" k="90" />
<hkern u1="&#x165;" u2="&#x2122;" k="-217" />
<hkern u1="&#x165;" u2="&#x17f;" k="-14" />
<hkern u1="&#x165;" u2="&#x161;" k="-55" />
<hkern u1="&#x165;" u2="&#x11b;" k="-4" />
<hkern u1="&#x165;" u2="&#x10d;" k="-4" />
<hkern u1="&#x165;" u2="&#xed;" k="-78" />
<hkern u1="&#x165;" u2="&#xe4;" k="-16" />
<hkern u1="&#x165;" u2="&#xba;" k="-119" />
<hkern u1="&#x165;" u2="&#xaa;" k="-109" />
<hkern u1="&#x165;" u2="&#x7d;" k="-190" />
<hkern u1="&#x165;" u2="&#x7c;" k="-131" />
<hkern u1="&#x165;" u2="x" k="-14" />
<hkern u1="&#x165;" u2="v" k="-18" />
<hkern u1="&#x165;" u2="j" k="-131" />
<hkern u1="&#x165;" u2="]" k="-188" />
<hkern u1="&#x165;" u2="\" k="-180" />
<hkern u1="&#x165;" u2="&#x3f;" k="-143" />
<hkern u1="&#x165;" u2="&#x3b;" k="-8" />
<hkern u1="&#x165;" u2="&#x2f;" k="14" />
<hkern u1="&#x165;" u2="&#x2a;" k="-147" />
<hkern u1="&#x165;" u2="&#x29;" k="-190" />
<hkern u1="&#x165;" u2="&#x21;" k="-152" />
<hkern u1="&#x165;" u2="&#x20;" k="-6" />
<hkern u1="&#x166;" g2="uniFB02" k="82" />
<hkern u1="&#x166;" g2="uniFB01" k="82" />
<hkern u1="&#x166;" u2="&#x2039;" k="90" />
<hkern u1="&#x166;" u2="&#x2014;" k="102" />
<hkern u1="&#x166;" u2="&#x2013;" k="102" />
<hkern u1="&#x166;" u2="&#x1ef3;" k="131" />
<hkern u1="&#x166;" u2="&#x1e85;" k="115" />
<hkern u1="&#x166;" u2="&#x1e83;" k="115" />
<hkern u1="&#x166;" u2="&#x1e81;" k="115" />
<hkern u1="&#x166;" u2="&#x21b;" k="59" />
<hkern u1="&#x166;" u2="&#x219;" k="141" />
<hkern u1="&#x166;" u2="&#x1ff;" k="147" />
<hkern u1="&#x166;" u2="&#x1fd;" k="162" />
<hkern u1="&#x166;" u2="&#x17f;" k="82" />
<hkern u1="&#x166;" u2="&#x177;" k="131" />
<hkern u1="&#x166;" u2="&#x175;" k="115" />
<hkern u1="&#x166;" u2="&#x173;" k="125" />
<hkern u1="&#x166;" u2="&#x171;" k="125" />
<hkern u1="&#x166;" u2="&#x16f;" k="125" />
<hkern u1="&#x166;" u2="&#x16d;" k="125" />
<hkern u1="&#x166;" u2="&#x16b;" k="125" />
<hkern u1="&#x166;" u2="&#x169;" k="125" />
<hkern u1="&#x166;" u2="&#x167;" k="59" />
<hkern u1="&#x166;" u2="&#x165;" k="59" />
<hkern u1="&#x166;" u2="&#x161;" k="141" />
<hkern u1="&#x166;" u2="&#x15f;" k="141" />
<hkern u1="&#x166;" u2="&#x15d;" k="141" />
<hkern u1="&#x166;" u2="&#x15b;" k="141" />
<hkern u1="&#x166;" u2="&#x159;" k="127" />
<hkern u1="&#x166;" u2="&#x157;" k="127" />
<hkern u1="&#x166;" u2="&#x155;" k="127" />
<hkern u1="&#x166;" u2="&#x153;" k="147" />
<hkern u1="&#x166;" u2="&#x151;" k="147" />
<hkern u1="&#x166;" u2="&#x14f;" k="147" />
<hkern u1="&#x166;" u2="&#x14d;" k="147" />
<hkern u1="&#x166;" u2="&#x14b;" k="127" />
<hkern u1="&#x166;" u2="&#x149;" k="127" />
<hkern u1="&#x166;" u2="&#x148;" k="127" />
<hkern u1="&#x166;" u2="&#x146;" k="127" />
<hkern u1="&#x166;" u2="&#x144;" k="127" />
<hkern u1="&#x166;" u2="&#x142;" k="25" />
<hkern u1="&#x166;" u2="&#x140;" k="25" />
<hkern u1="&#x166;" u2="&#x13e;" k="25" />
<hkern u1="&#x166;" u2="&#x13c;" k="25" />
<hkern u1="&#x166;" u2="&#x13a;" k="25" />
<hkern u1="&#x166;" u2="&#x138;" k="127" />
<hkern u1="&#x166;" u2="&#x137;" k="25" />
<hkern u1="&#x166;" u2="&#x135;" k="18" />
<hkern u1="&#x166;" u2="&#x131;" k="170" />
<hkern u1="&#x166;" u2="&#x12d;" k="-2" />
<hkern u1="&#x166;" u2="&#x12b;" k="-29" />
<hkern u1="&#x166;" u2="&#x129;" k="-16" />
<hkern u1="&#x166;" u2="&#x127;" k="25" />
<hkern u1="&#x166;" u2="&#x125;" k="25" />
<hkern u1="&#x166;" u2="&#x123;" k="145" />
<hkern u1="&#x166;" u2="&#x121;" k="145" />
<hkern u1="&#x166;" u2="&#x11f;" k="145" />
<hkern u1="&#x166;" u2="&#x11d;" k="145" />
<hkern u1="&#x166;" u2="&#x11b;" k="147" />
<hkern u1="&#x166;" u2="&#x119;" k="147" />
<hkern u1="&#x166;" u2="&#x117;" k="147" />
<hkern u1="&#x166;" u2="&#x115;" k="147" />
<hkern u1="&#x166;" u2="&#x113;" k="147" />
<hkern u1="&#x166;" u2="&#x111;" k="145" />
<hkern u1="&#x166;" u2="&#x10f;" k="145" />
<hkern u1="&#x166;" u2="&#x10d;" k="147" />
<hkern u1="&#x166;" u2="&#x10b;" k="147" />
<hkern u1="&#x166;" u2="&#x109;" k="147" />
<hkern u1="&#x166;" u2="&#x107;" k="147" />
<hkern u1="&#x166;" u2="&#x105;" k="162" />
<hkern u1="&#x166;" u2="&#x103;" k="162" />
<hkern u1="&#x166;" u2="&#x101;" k="162" />
<hkern u1="&#x166;" u2="&#xff;" k="131" />
<hkern u1="&#x166;" u2="&#xfe;" k="25" />
<hkern u1="&#x166;" u2="&#xfd;" k="131" />
<hkern u1="&#x166;" u2="&#xfc;" k="125" />
<hkern u1="&#x166;" u2="&#xfb;" k="125" />
<hkern u1="&#x166;" u2="&#xfa;" k="125" />
<hkern u1="&#x166;" u2="&#xf9;" k="125" />
<hkern u1="&#x166;" u2="&#xf8;" k="147" />
<hkern u1="&#x166;" u2="&#xf6;" k="147" />
<hkern u1="&#x166;" u2="&#xf5;" k="147" />
<hkern u1="&#x166;" u2="&#xf4;" k="147" />
<hkern u1="&#x166;" u2="&#xf3;" k="147" />
<hkern u1="&#x166;" u2="&#xf2;" k="147" />
<hkern u1="&#x166;" u2="&#xf1;" k="127" />
<hkern u1="&#x166;" u2="&#xef;" k="-31" />
<hkern u1="&#x166;" u2="&#xee;" k="-20" />
<hkern u1="&#x166;" u2="&#xed;" k="63" />
<hkern u1="&#x166;" u2="&#xec;" k="-47" />
<hkern u1="&#x166;" u2="&#xeb;" k="147" />
<hkern u1="&#x166;" u2="&#xea;" k="147" />
<hkern u1="&#x166;" u2="&#xe9;" k="147" />
<hkern u1="&#x166;" u2="&#xe8;" k="147" />
<hkern u1="&#x166;" u2="&#xe7;" k="147" />
<hkern u1="&#x166;" u2="&#xe6;" k="162" />
<hkern u1="&#x166;" u2="&#xe5;" k="162" />
<hkern u1="&#x166;" u2="&#xe4;" k="162" />
<hkern u1="&#x166;" u2="&#xe3;" k="162" />
<hkern u1="&#x166;" u2="&#xe2;" k="162" />
<hkern u1="&#x166;" u2="&#xe1;" k="162" />
<hkern u1="&#x166;" u2="&#xe0;" k="162" />
<hkern u1="&#x166;" u2="&#xdf;" k="25" />
<hkern u1="&#x166;" u2="&#xae;" k="37" />
<hkern u1="&#x166;" u2="&#xab;" k="90" />
<hkern u1="&#x166;" u2="&#xa9;" k="37" />
<hkern u1="&#x166;" u2="y" k="131" />
<hkern u1="&#x166;" u2="x" k="135" />
<hkern u1="&#x166;" u2="w" k="115" />
<hkern u1="&#x166;" u2="v" k="131" />
<hkern u1="&#x166;" u2="u" k="125" />
<hkern u1="&#x166;" u2="t" k="59" />
<hkern u1="&#x166;" u2="s" k="141" />
<hkern u1="&#x166;" u2="r" k="127" />
<hkern u1="&#x166;" u2="q" k="100" />
<hkern u1="&#x166;" u2="p" k="127" />
<hkern u1="&#x166;" u2="o" k="147" />
<hkern u1="&#x166;" u2="n" k="127" />
<hkern u1="&#x166;" u2="m" k="127" />
<hkern u1="&#x166;" u2="l" k="25" />
<hkern u1="&#x166;" u2="k" k="25" />
<hkern u1="&#x166;" u2="j" k="4" />
<hkern u1="&#x166;" u2="h" k="25" />
<hkern u1="&#x166;" u2="g" k="145" />
<hkern u1="&#x166;" u2="f" k="82" />
<hkern u1="&#x166;" u2="e" k="147" />
<hkern u1="&#x166;" u2="d" k="145" />
<hkern u1="&#x166;" u2="c" k="147" />
<hkern u1="&#x166;" u2="b" k="25" />
<hkern u1="&#x166;" u2="a" k="162" />
<hkern u1="&#x166;" u2="&#x3b;" k="68" />
<hkern u1="&#x166;" u2="&#x3a;" k="68" />
<hkern u1="&#x166;" u2="&#x2d;" k="102" />
<hkern u1="&#x168;" u2="&#x129;" k="-6" />
<hkern u1="&#x168;" u2="&#xef;" k="-6" />
<hkern u1="&#x168;" u2="&#xee;" k="-10" />
<hkern u1="&#x168;" u2="&#xec;" k="-4" />
<hkern u1="&#x16a;" u2="&#x129;" k="-6" />
<hkern u1="&#x16a;" u2="&#xef;" k="-6" />
<hkern u1="&#x16a;" u2="&#xee;" k="-10" />
<hkern u1="&#x16a;" u2="&#xec;" k="-4" />
<hkern u1="&#x16c;" u2="&#x129;" k="-6" />
<hkern u1="&#x16c;" u2="&#xef;" k="-6" />
<hkern u1="&#x16c;" u2="&#xee;" k="-10" />
<hkern u1="&#x16c;" u2="&#xec;" k="-4" />
<hkern u1="&#x16e;" u2="&#x129;" k="-6" />
<hkern u1="&#x16e;" u2="&#xef;" k="-6" />
<hkern u1="&#x16e;" u2="&#xee;" k="-10" />
<hkern u1="&#x16e;" u2="&#xec;" k="-4" />
<hkern u1="&#x170;" u2="&#x129;" k="-6" />
<hkern u1="&#x170;" u2="&#xef;" k="-6" />
<hkern u1="&#x170;" u2="&#xee;" k="-10" />
<hkern u1="&#x170;" u2="&#xec;" k="-4" />
<hkern u1="&#x171;" u2="\" k="59" />
<hkern u1="&#x172;" u2="&#x129;" k="-6" />
<hkern u1="&#x172;" u2="&#xef;" k="-6" />
<hkern u1="&#x172;" u2="&#xee;" k="-10" />
<hkern u1="&#x172;" u2="&#xec;" k="-4" />
<hkern u1="&#x173;" u2="j" k="-16" />
<hkern u1="&#x174;" u2="&#x159;" k="12" />
<hkern u1="&#x174;" u2="&#x131;" k="25" />
<hkern u1="&#x174;" u2="&#x12b;" k="-8" />
<hkern u1="&#x174;" u2="&#x129;" k="-12" />
<hkern u1="&#x174;" u2="&#xef;" k="-14" />
<hkern u1="&#x174;" u2="&#xee;" k="-16" />
<hkern u1="&#x174;" u2="&#xec;" k="-10" />
<hkern u1="&#x174;" u2="&#xdf;" k="2" />
<hkern u1="&#x176;" u2="&#x15d;" k="29" />
<hkern u1="&#x176;" u2="&#x159;" k="109" />
<hkern u1="&#x176;" u2="&#x135;" k="51" />
<hkern u1="&#x176;" u2="&#x131;" k="141" />
<hkern u1="&#x176;" u2="&#x12d;" k="-8" />
<hkern u1="&#x176;" u2="&#x12b;" k="-16" />
<hkern u1="&#x176;" u2="&#x129;" k="-18" />
<hkern u1="&#x176;" u2="&#x127;" k="-2" />
<hkern u1="&#x176;" u2="&#xef;" k="-23" />
<hkern u1="&#x176;" u2="&#xee;" k="-16" />
<hkern u1="&#x176;" u2="&#xed;" k="61" />
<hkern u1="&#x176;" u2="&#xec;" k="-37" />
<hkern u1="&#x176;" u2="&#xe4;" k="29" />
<hkern u1="&#x176;" u2="&#xe0;" k="31" />
<hkern u1="&#x176;" u2="&#xdf;" k="74" />
<hkern u1="&#x178;" u2="&#x15d;" k="29" />
<hkern u1="&#x178;" u2="&#x159;" k="109" />
<hkern u1="&#x178;" u2="&#x135;" k="51" />
<hkern u1="&#x178;" u2="&#x131;" k="141" />
<hkern u1="&#x178;" u2="&#x12d;" k="-8" />
<hkern u1="&#x178;" u2="&#x12b;" k="-16" />
<hkern u1="&#x178;" u2="&#x129;" k="-18" />
<hkern u1="&#x178;" u2="&#x127;" k="-2" />
<hkern u1="&#x178;" u2="&#xef;" k="-23" />
<hkern u1="&#x178;" u2="&#xee;" k="-16" />
<hkern u1="&#x178;" u2="&#xed;" k="61" />
<hkern u1="&#x178;" u2="&#xec;" k="-37" />
<hkern u1="&#x178;" u2="&#xe4;" k="29" />
<hkern u1="&#x178;" u2="&#xe0;" k="31" />
<hkern u1="&#x178;" u2="&#xdf;" k="74" />
<hkern u1="&#x179;" u2="&#x135;" k="18" />
<hkern u1="&#x179;" u2="&#x129;" k="-6" />
<hkern u1="&#x179;" u2="&#xef;" k="-6" />
<hkern u1="&#x179;" u2="&#xee;" k="-10" />
<hkern u1="&#x179;" u2="&#xec;" k="-4" />
<hkern u1="&#x17a;" u2="\" k="57" />
<hkern u1="&#x17b;" u2="&#x135;" k="18" />
<hkern u1="&#x17b;" u2="&#x129;" k="-6" />
<hkern u1="&#x17b;" u2="&#xef;" k="-6" />
<hkern u1="&#x17b;" u2="&#xee;" k="-10" />
<hkern u1="&#x17b;" u2="&#xec;" k="-4" />
<hkern u1="&#x17d;" u2="&#x135;" k="18" />
<hkern u1="&#x17d;" u2="&#x129;" k="-6" />
<hkern u1="&#x17d;" u2="&#xef;" k="-6" />
<hkern u1="&#x17d;" u2="&#xee;" k="-10" />
<hkern u1="&#x17d;" u2="&#xec;" k="-4" />
<hkern u1="&#x17f;" u2="&#x177;" k="-8" />
<hkern u1="&#x17f;" u2="&#x159;" k="-119" />
<hkern u1="&#x17f;" u2="&#x151;" k="-6" />
<hkern u1="&#x17f;" u2="&#x149;" k="-25" />
<hkern u1="&#x17f;" u2="&#x131;" k="-2" />
<hkern u1="&#x17f;" u2="&#x12f;" k="-53" />
<hkern u1="&#x17f;" u2="&#x12d;" k="-231" />
<hkern u1="&#x17f;" u2="&#x12b;" k="-207" />
<hkern u1="&#x17f;" u2="&#x129;" k="-158" />
<hkern u1="&#x17f;" u2="&#x11d;" k="-6" />
<hkern u1="&#x17f;" u2="&#x11b;" k="-4" />
<hkern u1="&#x17f;" u2="&#x10d;" k="-4" />
<hkern u1="&#x17f;" u2="&#x109;" k="-10" />
<hkern u1="&#x17f;" u2="&#xff;" k="-4" />
<hkern u1="&#x17f;" u2="&#xfb;" k="-2" />
<hkern u1="&#x17f;" u2="&#xf9;" k="-12" />
<hkern u1="&#x17f;" u2="&#xf6;" k="-4" />
<hkern u1="&#x17f;" u2="&#xf5;" k="-4" />
<hkern u1="&#x17f;" u2="&#xf4;" k="-8" />
<hkern u1="&#x17f;" u2="&#xf2;" k="-14" />
<hkern u1="&#x17f;" u2="&#xef;" k="-254" />
<hkern u1="&#x17f;" u2="&#xee;" k="-160" />
<hkern u1="&#x17f;" u2="&#xed;" k="-25" />
<hkern u1="&#x17f;" u2="&#xec;" k="-303" />
<hkern u1="&#x17f;" u2="&#xeb;" k="-6" />
<hkern u1="&#x17f;" u2="&#xea;" k="-10" />
<hkern u1="&#x17f;" u2="&#xe8;" k="-18" />
<hkern u1="&#x17f;" u2="&#xe4;" k="-8" />
<hkern u1="&#x17f;" u2="&#xe3;" k="-8" />
<hkern u1="&#x17f;" u2="&#xe2;" k="-12" />
<hkern u1="&#x17f;" u2="&#xe0;" k="-18" />
<hkern u1="&#x17f;" u2="j" k="-72" />
<hkern u1="&#x1fc;" u2="&#x135;" k="18" />
<hkern u1="&#x1fc;" u2="&#x129;" k="-6" />
<hkern u1="&#x1fc;" u2="&#xef;" k="-8" />
<hkern u1="&#x1fc;" u2="&#xee;" k="-10" />
<hkern u1="&#x1fc;" u2="&#xec;" k="-4" />
<hkern u1="&#x1fd;" u2="&#x135;" k="10" />
<hkern u1="&#x1fe;" u2="&#x2a;" k="-4" />
<hkern u1="&#x1ff;" u2="&#x135;" k="14" />
<hkern u1="&#x219;" u2="&#x135;" k="2" />
<hkern u1="&#x21a;" u2="&#x159;" k="100" />
<hkern u1="&#x21a;" u2="&#x135;" k="18" />
<hkern u1="&#x21a;" u2="&#x131;" k="170" />
<hkern u1="&#x21a;" u2="&#x12d;" k="-2" />
<hkern u1="&#x21a;" u2="&#x12b;" k="-29" />
<hkern u1="&#x21a;" u2="&#x129;" k="-16" />
<hkern u1="&#x21a;" u2="&#x127;" k="-4" />
<hkern u1="&#x21a;" u2="&#xef;" k="-31" />
<hkern u1="&#x21a;" u2="&#xee;" k="-20" />
<hkern u1="&#x21a;" u2="&#xed;" k="63" />
<hkern u1="&#x21a;" u2="&#xec;" k="-47" />
<hkern u1="&#x21a;" u2="&#xdf;" k="90" />
<hkern u1="&#x1e80;" u2="&#x159;" k="12" />
<hkern u1="&#x1e80;" u2="&#x131;" k="25" />
<hkern u1="&#x1e80;" u2="&#x12b;" k="-8" />
<hkern u1="&#x1e80;" u2="&#x129;" k="-12" />
<hkern u1="&#x1e80;" u2="&#xef;" k="-14" />
<hkern u1="&#x1e80;" u2="&#xee;" k="-16" />
<hkern u1="&#x1e80;" u2="&#xec;" k="-10" />
<hkern u1="&#x1e80;" u2="&#xdf;" k="2" />
<hkern u1="&#x1e82;" u2="&#x159;" k="12" />
<hkern u1="&#x1e82;" u2="&#x131;" k="25" />
<hkern u1="&#x1e82;" u2="&#x12b;" k="-8" />
<hkern u1="&#x1e82;" u2="&#x129;" k="-12" />
<hkern u1="&#x1e82;" u2="&#xef;" k="-14" />
<hkern u1="&#x1e82;" u2="&#xee;" k="-16" />
<hkern u1="&#x1e82;" u2="&#xec;" k="-10" />
<hkern u1="&#x1e82;" u2="&#xdf;" k="2" />
<hkern u1="&#x1e84;" u2="&#x159;" k="12" />
<hkern u1="&#x1e84;" u2="&#x131;" k="25" />
<hkern u1="&#x1e84;" u2="&#x12b;" k="-8" />
<hkern u1="&#x1e84;" u2="&#x129;" k="-12" />
<hkern u1="&#x1e84;" u2="&#xef;" k="-14" />
<hkern u1="&#x1e84;" u2="&#xee;" k="-16" />
<hkern u1="&#x1e84;" u2="&#xec;" k="-10" />
<hkern u1="&#x1e84;" u2="&#xdf;" k="2" />
<hkern u1="&#x1ef2;" u2="&#x15d;" k="29" />
<hkern u1="&#x1ef2;" u2="&#x159;" k="109" />
<hkern u1="&#x1ef2;" u2="&#x135;" k="51" />
<hkern u1="&#x1ef2;" u2="&#x131;" k="141" />
<hkern u1="&#x1ef2;" u2="&#x12d;" k="-8" />
<hkern u1="&#x1ef2;" u2="&#x12b;" k="-16" />
<hkern u1="&#x1ef2;" u2="&#x129;" k="-18" />
<hkern u1="&#x1ef2;" u2="&#x127;" k="-2" />
<hkern u1="&#x1ef2;" u2="&#xef;" k="-23" />
<hkern u1="&#x1ef2;" u2="&#xee;" k="-16" />
<hkern u1="&#x1ef2;" u2="&#xed;" k="61" />
<hkern u1="&#x1ef2;" u2="&#xec;" k="-37" />
<hkern u1="&#x1ef2;" u2="&#xe4;" k="29" />
<hkern u1="&#x1ef2;" u2="&#xe0;" k="31" />
<hkern u1="&#x1ef2;" u2="&#xdf;" k="74" />
<hkern u1="&#x2013;" u2="&#x166;" k="102" />
<hkern u1="&#x2013;" u2="&#x135;" k="37" />
<hkern u1="&#x2014;" u2="&#x166;" k="102" />
<hkern u1="&#x2014;" u2="&#x135;" k="37" />
<hkern u1="&#x2018;" u2="&#x12d;" k="-6" />
<hkern u1="&#x2018;" u2="&#x12b;" k="-12" />
<hkern u1="&#x2018;" u2="&#x129;" k="-16" />
<hkern u1="&#x2018;" u2="&#xef;" k="-18" />
<hkern u1="&#x2018;" u2="&#xee;" k="-18" />
<hkern u1="&#x2018;" u2="&#xec;" k="-39" />
<hkern u1="&#x2019;" u2="&#x135;" k="4" />
<hkern u1="&#x2019;" u2="&#x12d;" k="-12" />
<hkern u1="&#x2019;" u2="&#x12b;" k="-18" />
<hkern u1="&#x2019;" u2="&#x129;" k="-20" />
<hkern u1="&#x2019;" u2="&#x127;" k="-2" />
<hkern u1="&#x2019;" u2="&#xef;" k="-25" />
<hkern u1="&#x2019;" u2="&#xee;" k="-20" />
<hkern u1="&#x2019;" u2="&#xec;" k="-63" />
<hkern u1="&#x201a;" u2="&#x166;" k="131" />
<hkern u1="&#x201a;" u2="&#x135;" k="39" />
<hkern u1="&#x201c;" u2="&#x12d;" k="-6" />
<hkern u1="&#x201c;" u2="&#x12b;" k="-12" />
<hkern u1="&#x201c;" u2="&#x129;" k="-16" />
<hkern u1="&#x201c;" u2="&#xef;" k="-18" />
<hkern u1="&#x201c;" u2="&#xee;" k="-18" />
<hkern u1="&#x201c;" u2="&#xec;" k="-39" />
<hkern u1="&#x201d;" u2="&#x135;" k="4" />
<hkern u1="&#x201d;" u2="&#x12d;" k="-12" />
<hkern u1="&#x201d;" u2="&#x12b;" k="-18" />
<hkern u1="&#x201d;" u2="&#x129;" k="-20" />
<hkern u1="&#x201d;" u2="&#x127;" k="-2" />
<hkern u1="&#x201d;" u2="&#xef;" k="-25" />
<hkern u1="&#x201d;" u2="&#xee;" k="-20" />
<hkern u1="&#x201d;" u2="&#xec;" k="-63" />
<hkern u1="&#x201e;" u2="&#x166;" k="131" />
<hkern u1="&#x201e;" u2="&#x135;" k="39" />
<hkern u1="&#x2026;" u2="&#x135;" k="39" />
<hkern u1="&#x2039;" u2="&#x129;" k="-6" />
<hkern u1="&#x2039;" u2="&#xef;" k="-6" />
<hkern u1="&#x2039;" u2="&#xee;" k="-10" />
<hkern u1="&#x203a;" u2="&#x166;" k="90" />
<hkern g1="uniFB01" u2="&#x129;" k="-6" />
<hkern g1="uniFB01" u2="&#xef;" k="-6" />
<hkern g1="uniFB01" u2="&#xee;" k="-8" />
<hkern g1="uniFB01" u2="&#xec;" k="-18" />
<hkern g1="uniFB02" u2="&#x129;" k="-6" />
<hkern g1="uniFB02" u2="&#xef;" k="-6" />
<hkern g1="uniFB02" u2="&#xee;" k="-10" />
<hkern g1="uniFB02" u2="&#xec;" k="-18" />
<hkern g1="colon,semicolon" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="colon,semicolon" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="74" />
<hkern g1="hyphen,endash,emdash" 	g2="quoteright,quotedblright" 	k="135" />
<hkern g1="hyphen,endash,emdash" 	g2="quotedbl,quotesingle" 	k="129" />
<hkern g1="hyphen,endash,emdash" 	g2="one" 	k="76" />
<hkern g1="hyphen,endash,emdash" 	g2="seven" 	k="119" />
<hkern g1="hyphen,endash,emdash" 	g2="two" 	k="82" />
<hkern g1="hyphen,endash,emdash" 	g2="five" 	k="18" />
<hkern g1="hyphen,endash,emdash" 	g2="nine" 	k="27" />
<hkern g1="hyphen,endash,emdash" 	g2="three" 	k="45" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="211" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="209" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="one" 	k="137" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="seven" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="nine" 	k="92" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="eight" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="four" 	k="33" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="215" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="hyphen,endash,emdash" 	k="109" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="six" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="zero" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="219" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,endash,emdash" 	k="158" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="217" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="106" />
<hkern g1="quoteright,quotedblright" 	g2="ampersand" 	k="37" />
<hkern g1="quoteright,quotedblright" 	g2="at" 	k="57" />
<hkern g1="quoteright,quotedblright" 	g2="copyright" 	k="39" />
<hkern g1="quoteright,quotedblright" 	g2="registered" 	k="39" />
<hkern g1="quoteright,quotedblright" 	g2="slash" 	k="147" />
<hkern g1="quoteright,quotedblright" 	g2="colon,semicolon" 	k="47" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="109" />
<hkern g1="quotedbl,quotesingle" 	g2="hyphen,endash,emdash" 	k="129" />
<hkern g1="quotedbl,quotesingle" 	g2="six" 	k="82" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="209" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="74" />
<hkern g1="quotedbl,quotesingle" 	g2="ampersand" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="at" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="copyright" 	k="18" />
<hkern g1="quotedbl,quotesingle" 	g2="registered" 	k="18" />
<hkern g1="quotedbl,quotesingle" 	g2="slash" 	k="137" />
<hkern g1="quotedbl,quotesingle" 	g2="colon,semicolon" 	k="16" />
<hkern g1="backslash" 	g2="quoteright,quotedblright" 	k="137" />
<hkern g1="backslash" 	g2="quotedbl,quotesingle" 	k="137" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle" 	k="80" />
<hkern g1="ampersand" 	g2="quoteright,quotedblright" 	k="84" />
<hkern g1="at" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="at" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="copyright" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="copyright" 	g2="quoteright,quotedblright" 	k="23" />
<hkern g1="registered" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="registered" 	g2="quoteright,quotedblright" 	k="23" />
<hkern g1="eight" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="five" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="18" />
<hkern g1="four" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="33" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="47" />
<hkern g1="nine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="133" />
<hkern g1="nine" 	g2="hyphen,endash,emdash" 	k="31" />
<hkern g1="seven" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="135" />
<hkern g1="seven" 	g2="hyphen,endash,emdash" 	k="72" />
<hkern g1="six" 	g2="quotedbl,quotesingle" 	k="76" />
<hkern g1="two" 	g2="hyphen,endash,emdash" 	k="49" />
<hkern g1="zero" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="J,Jcircumflex" 	k="18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="T,Tcaron,Tbar,uni021A" 	k="152" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="145" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="d,q,dcaron,dcroat" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="f,uniFB01,uniFB02" 	k="43" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="guillemotleft,guilsinglleft" 	k="47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="hyphen,endash,emdash" 	k="47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="quoteleft,quotedblleft" 	k="135" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="quoteright,quotedblright" 	k="139" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="quotedbl,quotesingle" 	k="139" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="t,tcaron,tbar,uni021B" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="66" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="V" 	k="100" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="asterisk" 	k="125" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="backslash" 	k="137" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="copyright" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="eth" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="longs" 	k="43" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="nine" 	k="57" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="one" 	k="88" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="ordfeminine" 	k="115" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="ordmasculine" 	k="121" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="question" 	k="84" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="registered" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="seven" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="space" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="trademark" 	k="141" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="v" 	k="63" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="zero" 	k="4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="braceright" 	k="47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="bracketright" 	k="49" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="parenright" 	k="57" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="39" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="hyphen,endash,emdash" 	k="27" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="V" 	k="35" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="backslash" 	k="25" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="trademark" 	k="4" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="braceright" 	k="43" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="bracketright" 	k="43" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="parenright" 	k="53" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="14" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="AE,AEacute" 	k="4" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="X" 	k="23" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="slash" 	k="25" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="27" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="T,Tcaron,Tbar,uni021A" 	k="31" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="16" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="59" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="V" 	k="39" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="backslash" 	k="43" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="trademark" 	k="23" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="braceright" 	k="53" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="bracketright" 	k="51" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="parenright" 	k="63" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="25" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="AE,AEacute" 	k="23" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="X" 	k="49" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="slash" 	k="41" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="33" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="14" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="x" 	k="4" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="d,q,dcaron,dcroat" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="hyphen,endash,emdash" 	k="33" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="t,tcaron,tbar,uni021B" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="V" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="eth" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="longs" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="one" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="v" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="four" 	k="16" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="T,Tcaron,Tbar,uni021A" 	k="20" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="20" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="51" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="V" 	k="47" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="backslash" 	k="31" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="trademark" 	k="18" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="braceright" 	k="47" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="bracketright" 	k="43" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="parenright" 	k="57" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="29" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="AE,AEacute" 	k="20" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="X" 	k="41" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="slash" 	k="33" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="33" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="8" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="x" 	k="12" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="hyphen,endash,emdash" 	k="16" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="eth" 	k="8" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="braceright" 	k="16" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="bracketright" 	k="16" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="parenright" 	k="18" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="16" />
<hkern g1="K,uni0136" 	g2="J,Jcircumflex" 	k="16" />
<hkern g1="K,uni0136" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="76" />
<hkern g1="K,uni0136" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="29" />
<hkern g1="K,uni0136" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="2" />
<hkern g1="K,uni0136" 	g2="d,q,dcaron,dcroat" 	k="59" />
<hkern g1="K,uni0136" 	g2="f,uniFB01,uniFB02" 	k="39" />
<hkern g1="K,uni0136" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="63" />
<hkern g1="K,uni0136" 	g2="guillemotleft,guilsinglleft" 	k="115" />
<hkern g1="K,uni0136" 	g2="hyphen,endash,emdash" 	k="121" />
<hkern g1="K,uni0136" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="61" />
<hkern g1="K,uni0136" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="K,uni0136" 	g2="t,tcaron,tbar,uni021B" 	k="39" />
<hkern g1="K,uni0136" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="45" />
<hkern g1="K,uni0136" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="88" />
<hkern g1="K,uni0136" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="102" />
<hkern g1="K,uni0136" 	g2="copyright" 	k="47" />
<hkern g1="K,uni0136" 	g2="eth" 	k="55" />
<hkern g1="K,uni0136" 	g2="longs" 	k="39" />
<hkern g1="K,uni0136" 	g2="nine" 	k="4" />
<hkern g1="K,uni0136" 	g2="one" 	k="39" />
<hkern g1="K,uni0136" 	g2="registered" 	k="47" />
<hkern g1="K,uni0136" 	g2="space" 	k="20" />
<hkern g1="K,uni0136" 	g2="v" 	k="100" />
<hkern g1="K,uni0136" 	g2="zero" 	k="4" />
<hkern g1="K,uni0136" 	g2="four" 	k="25" />
<hkern g1="K,uni0136" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="14" />
<hkern g1="K,uni0136" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="18" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="20" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="T,Tcaron,Tbar,uni021A" 	k="246" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="37" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="92" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="219" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="d,q,dcaron,dcroat" 	k="25" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="f,uniFB01,uniFB02" 	k="63" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="25" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="guillemotleft,guilsinglleft" 	k="145" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="hyphen,endash,emdash" 	k="137" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="23" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="quoteleft,quotedblleft" 	k="223" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="quoteright,quotedblright" 	k="219" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="quotedbl,quotesingle" 	k="215" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="t,tcaron,tbar,uni021B" 	k="98" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="35" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="125" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="199" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="V" 	k="195" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="asterisk" 	k="240" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="backslash" 	k="166" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="copyright" 	k="59" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="eth" 	k="16" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="longs" 	k="63" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="nine" 	k="80" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="one" 	k="137" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="ordfeminine" 	k="238" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="ordmasculine" 	k="240" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="question" 	k="106" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="registered" 	k="59" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="seven" 	k="41" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="space" 	k="53" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="trademark" 	k="240" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="v" 	k="184" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="zero" 	k="16" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="braceright" 	k="37" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="bracketright" 	k="39" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="parenright" 	k="43" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="four" 	k="41" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="periodcentered" 	k="381" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="guillemotright,guilsinglright" 	k="29" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="31" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="18" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="61" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="V" 	k="41" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="backslash" 	k="41" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="seven" 	k="16" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="trademark" 	k="23" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="braceright" 	k="53" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="bracketright" 	k="51" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="parenright" 	k="63" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="27" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="AE,AEacute" 	k="25" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="X" 	k="47" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="slash" 	k="39" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="14" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="x" 	k="4" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="J,Jcircumflex" 	k="4" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="2" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="18" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="20" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="49" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="d,q,dcaron,dcroat" 	k="12" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="12" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="hyphen,endash,emdash" 	k="43" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="V" 	k="43" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="backslash" 	k="10" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="eth" 	k="23" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="trademark" 	k="4" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="braceright" 	k="33" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="bracketright" 	k="35" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="parenright" 	k="41" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="T,Tcaron,Tbar,uni021A" 	k="16" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="23" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="51" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="hyphen,endash,emdash" 	k="16" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="2" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="V" 	k="47" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="backslash" 	k="31" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="trademark" 	k="18" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="v" 	k="8" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="braceright" 	k="43" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="bracketright" 	k="43" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="parenright" 	k="51" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="20" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="AE,AEacute" 	k="6" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="X" 	k="29" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="slash" 	k="6" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="x" 	k="12" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="J,Jcircumflex" 	k="4" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="31" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="8" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="d,q,dcaron,dcroat" 	k="195" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="f,uniFB01,uniFB02" 	k="106" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="193" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotleft,guilsinglleft" 	k="129" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="hyphen,endash,emdash" 	k="131" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="197" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="t,tcaron,tbar,uni021B" 	k="82" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="166" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="152" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="170" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="asterisk" 	k="-2" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="copyright" 	k="57" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="eth" 	k="162" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="longs" 	k="106" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="one" 	k="29" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="registered" 	k="57" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="space" 	k="51" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="v" 	k="166" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="zero" 	k="18" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="152" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="AE,AEacute" 	k="170" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="slash" 	k="137" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="133" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="x" 	k="152" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="four" 	k="117" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="188" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="184" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotright,guilsinglright" 	k="76" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="b,h,k,germandbls,thorn,hcircumflex,hbar,uni0137" 	k="41" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij" 	k="6" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="41" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="170" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="z,zacute,zdotaccent,zcaron" 	k="176" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="ampersand" 	k="35" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="j" 	k="16" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="six" 	k="113" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="colon,semicolon" 	k="94" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="at" 	k="76" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="hyphen,endash,emdash" 	k="14" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="8" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="eth" 	k="10" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="braceright" 	k="18" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="bracketright" 	k="18" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="parenright" 	k="20" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="27" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="AE,AEacute" 	k="31" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="X" 	k="2" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="slash" 	k="43" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="x" 	k="2" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="8" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="8" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="J,Jcircumflex" 	k="18" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="18" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="18" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="d,q,dcaron,dcroat" 	k="43" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="43" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="guillemotleft,guilsinglleft" 	k="25" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="hyphen,endash,emdash" 	k="39" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="43" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="20" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="eth" 	k="53" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="space" 	k="35" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="55" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="AE,AEacute" 	k="84" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="slash" 	k="76" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="76" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="x" 	k="2" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="four" 	k="27" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="45" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="33" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="25" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="z,zacute,zdotaccent,zcaron" 	k="4" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="ampersand" 	k="20" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="six" 	k="31" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="J,Jcircumflex" 	k="37" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="61" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="47" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="d,q,dcaron,dcroat" 	k="172" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="f,uniFB01,uniFB02" 	k="76" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="172" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="guillemotleft,guilsinglleft" 	k="129" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="hyphen,endash,emdash" 	k="143" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="172" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="quoteleft,quotedblleft" 	k="4" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="t,tcaron,tbar,uni021B" 	k="66" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="133" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="109" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="111" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="copyright" 	k="76" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="eth" 	k="184" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="longs" 	k="76" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="nine" 	k="31" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="one" 	k="35" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="question" 	k="20" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="registered" 	k="76" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="space" 	k="61" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="trademark" 	k="-4" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="v" 	k="111" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="zero" 	k="47" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="145" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="AE,AEacute" 	k="184" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="slash" 	k="170" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="166" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="x" 	k="115" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="four" 	k="145" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="201" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="180" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="guillemotright,guilsinglright" 	k="47" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="b,h,k,germandbls,thorn,hcircumflex,hbar,uni0137" 	k="8" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="8" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="141" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="z,zacute,zdotaccent,zcaron" 	k="125" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="ampersand" 	k="68" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="six" 	k="135" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="colon,semicolon" 	k="88" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="at" 	k="92" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="eight" 	k="45" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="five" 	k="39" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="two" 	k="37" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="14" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="d,q,dcaron,dcroat" 	k="35" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="37" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="guillemotleft,guilsinglleft" 	k="78" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="hyphen,endash,emdash" 	k="100" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="33" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="t,tcaron,tbar,uni021B" 	k="20" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="35" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="39" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="51" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="copyright" 	k="31" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="eth" 	k="31" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="longs" 	k="20" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="registered" 	k="29" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="v" 	k="51" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="four" 	k="25" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="8" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="10" />
<hkern g1="B" 	g2="T,Tcaron,Tbar,uni021A" 	k="25" />
<hkern g1="B" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="2" />
<hkern g1="B" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="43" />
<hkern g1="B" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="16" />
<hkern g1="B" 	g2="AE,AEacute" 	k="6" />
<hkern g1="B" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="F" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="10" />
<hkern g1="F" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="16" />
<hkern g1="F" 	g2="d,q,dcaron,dcroat" 	k="23" />
<hkern g1="F" 	g2="f,uniFB01,uniFB02" 	k="35" />
<hkern g1="F" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="23" />
<hkern g1="F" 	g2="hyphen,endash,emdash" 	k="23" />
<hkern g1="F" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="23" />
<hkern g1="F" 	g2="t,tcaron,tbar,uni021B" 	k="27" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="23" />
<hkern g1="F" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="31" />
<hkern g1="F" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="39" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="90" />
<hkern g1="F" 	g2="AE,AEacute" 	k="133" />
<hkern g1="F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="125" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="59" />
<hkern g1="F" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="31" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="F" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="29" />
<hkern g1="F" 	g2="z,zacute,zdotaccent,zcaron" 	k="72" />
<hkern g1="F" 	g2="colon,semicolon" 	k="16" />
<hkern g1="IJ" 	g2="hyphen,endash,emdash" 	k="14" />
<hkern g1="IJ" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="8" />
<hkern g1="IJ" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="27" />
<hkern g1="IJ" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="37" />
<hkern g1="IJ" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="8" />
<hkern g1="IJ" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="8" />
<hkern g1="P" 	g2="J,Jcircumflex" 	k="27" />
<hkern g1="P" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="10" />
<hkern g1="P" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="39" />
<hkern g1="P" 	g2="d,q,dcaron,dcroat" 	k="14" />
<hkern g1="P" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="12" />
<hkern g1="P" 	g2="hyphen,endash,emdash" 	k="33" />
<hkern g1="P" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="14" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="82" />
<hkern g1="P" 	g2="AE,AEacute" 	k="137" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="150" />
<hkern g1="P" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="12" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="20" />
<hkern g1="Thorn" 	g2="T,Tcaron,Tbar,uni021A" 	k="119" />
<hkern g1="Thorn" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="25" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="104" />
<hkern g1="Thorn" 	g2="quoteleft,quotedblleft" 	k="49" />
<hkern g1="Thorn" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="Thorn" 	g2="quotedbl,quotesingle" 	k="39" />
<hkern g1="Thorn" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="2" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="39" />
<hkern g1="Thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="84" />
<hkern g1="Thorn" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="47" />
<hkern g1="Thorn" 	g2="z,zacute,zdotaccent,zcaron" 	k="8" />
<hkern g1="V" 	g2="J,Jcircumflex" 	k="39" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="41" />
<hkern g1="V" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="45" />
<hkern g1="V" 	g2="d,q,dcaron,dcroat" 	k="104" />
<hkern g1="V" 	g2="f,uniFB01,uniFB02" 	k="27" />
<hkern g1="V" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="104" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="72" />
<hkern g1="V" 	g2="hyphen,endash,emdash" 	k="86" />
<hkern g1="V" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="106" />
<hkern g1="V" 	g2="t,tcaron,tbar,uni021B" 	k="23" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="74" />
<hkern g1="V" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="35" />
<hkern g1="V" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="39" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="100" />
<hkern g1="V" 	g2="AE,AEacute" 	k="133" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="141" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="115" />
<hkern g1="V" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="100" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="V" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="80" />
<hkern g1="V" 	g2="z,zacute,zdotaccent,zcaron" 	k="55" />
<hkern g1="V" 	g2="colon,semicolon" 	k="49" />
<hkern g1="X" 	g2="J,Jcircumflex" 	k="16" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="47" />
<hkern g1="X" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="25" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="2" />
<hkern g1="X" 	g2="d,q,dcaron,dcroat" 	k="49" />
<hkern g1="X" 	g2="f,uniFB01,uniFB02" 	k="33" />
<hkern g1="X" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="51" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="94" />
<hkern g1="X" 	g2="hyphen,endash,emdash" 	k="100" />
<hkern g1="X" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="49" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="8" />
<hkern g1="X" 	g2="t,tcaron,tbar,uni021B" 	k="33" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="41" />
<hkern g1="X" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="63" />
<hkern g1="X" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="84" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="4" />
<hkern g1="X" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="14" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="T,Tcaron,Tbar,uni021A" 	k="201" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="37" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="160" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="quoteright,quotedblright" 	k="55" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="quotedbl,quotesingle" 	k="51" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="t,tcaron,tbar,uni021B" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="4" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="23" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="V" 	k="100" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="asterisk" 	k="39" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="backslash" 	k="96" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="braceright" 	k="59" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="longs" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="ordfeminine" 	k="27" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="ordmasculine" 	k="35" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="parenright" 	k="76" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="question" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="trademark" 	k="57" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="v" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="12" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="bracketright" 	k="55" />
<hkern g1="b,p,thorn" 	g2="T,Tcaron,Tbar,uni021A" 	k="195" />
<hkern g1="b,p,thorn" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="43" />
<hkern g1="b,p,thorn" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="172" />
<hkern g1="b,p,thorn" 	g2="f,uniFB01,uniFB02" 	k="14" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="86" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="72" />
<hkern g1="b,p,thorn" 	g2="t,tcaron,tbar,uni021B" 	k="12" />
<hkern g1="b,p,thorn" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="29" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="41" />
<hkern g1="b,p,thorn" 	g2="V" 	k="104" />
<hkern g1="b,p,thorn" 	g2="asterisk" 	k="49" />
<hkern g1="b,p,thorn" 	g2="backslash" 	k="100" />
<hkern g1="b,p,thorn" 	g2="braceright" 	k="78" />
<hkern g1="b,p,thorn" 	g2="longs" 	k="14" />
<hkern g1="b,p,thorn" 	g2="ordfeminine" 	k="37" />
<hkern g1="b,p,thorn" 	g2="ordmasculine" 	k="47" />
<hkern g1="b,p,thorn" 	g2="parenright" 	k="92" />
<hkern g1="b,p,thorn" 	g2="question" 	k="39" />
<hkern g1="b,p,thorn" 	g2="trademark" 	k="70" />
<hkern g1="b,p,thorn" 	g2="v" 	k="37" />
<hkern g1="b,p,thorn" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="27" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="80" />
<hkern g1="b,p,thorn" 	g2="bracketright" 	k="72" />
<hkern g1="b,p,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="12" />
<hkern g1="b,p,thorn" 	g2="X" 	k="49" />
<hkern g1="b,p,thorn" 	g2="slash" 	k="35" />
<hkern g1="b,p,thorn" 	g2="x" 	k="29" />
<hkern g1="b,p,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="b,p,thorn" 	g2="z,zacute,zdotaccent,zcaron" 	k="10" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="209" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="27" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="178" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="quoteright,quotedblright" 	k="57" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="quotedbl,quotesingle" 	k="51" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="20" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="31" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="V" 	k="92" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="asterisk" 	k="37" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="backslash" 	k="88" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="braceright" 	k="72" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="ordfeminine" 	k="23" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="ordmasculine" 	k="33" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="parenright" 	k="88" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="question" 	k="6" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="trademark" 	k="57" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="v" 	k="29" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="8" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="quoteleft,quotedblleft" 	k="53" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="bracketright" 	k="66" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="X" 	k="27" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="slash" 	k="8" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="x" 	k="6" />
<hkern g1="d,dcroat" 	g2="T,Tcaron,Tbar,uni021A" 	k="41" />
<hkern g1="d,dcroat" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="8" />
<hkern g1="dcaron,lcaron" 	g2="f,uniFB01,uniFB02" 	k="-8" />
<hkern g1="dcaron,lcaron" 	g2="quoteright,quotedblright" 	k="-31" />
<hkern g1="dcaron,lcaron" 	g2="quotedbl,quotesingle" 	k="-80" />
<hkern g1="dcaron,lcaron" 	g2="t,tcaron,tbar,uni021B" 	k="-10" />
<hkern g1="dcaron,lcaron" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="-8" />
<hkern g1="dcaron,lcaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="-8" />
<hkern g1="dcaron,lcaron" 	g2="asterisk" 	k="-88" />
<hkern g1="dcaron,lcaron" 	g2="backslash" 	k="-94" />
<hkern g1="dcaron,lcaron" 	g2="braceright" 	k="-106" />
<hkern g1="dcaron,lcaron" 	g2="longs" 	k="-8" />
<hkern g1="dcaron,lcaron" 	g2="ordfeminine" 	k="-20" />
<hkern g1="dcaron,lcaron" 	g2="ordmasculine" 	k="-35" />
<hkern g1="dcaron,lcaron" 	g2="parenright" 	k="-109" />
<hkern g1="dcaron,lcaron" 	g2="question" 	k="-55" />
<hkern g1="dcaron,lcaron" 	g2="trademark" 	k="-158" />
<hkern g1="dcaron,lcaron" 	g2="v" 	k="-10" />
<hkern g1="dcaron,lcaron" 	g2="quoteleft,quotedblleft" 	k="-4" />
<hkern g1="dcaron,lcaron" 	g2="bracketright" 	k="-104" />
<hkern g1="dcaron,lcaron" 	g2="slash" 	k="18" />
<hkern g1="dcaron,lcaron" 	g2="x" 	k="-6" />
<hkern g1="dcaron,lcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="16" />
<hkern g1="dcaron,lcaron" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="20" />
<hkern g1="dcaron,lcaron" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="dcaron,lcaron" 	g2="d,q,dcaron,dcroat" 	k="16" />
<hkern g1="dcaron,lcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="16" />
<hkern g1="dcaron,lcaron" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="dcaron,lcaron" 	g2="guillemotright,guilsinglright" 	k="-10" />
<hkern g1="dcaron,lcaron" 	g2="b,h,k,germandbls,thorn,hcircumflex,hbar,uni0137" 	k="-86" />
<hkern g1="dcaron,lcaron" 	g2="hyphen,endash,emdash" 	k="16" />
<hkern g1="dcaron,lcaron" 	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij" 	k="-102" />
<hkern g1="dcaron,lcaron" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="-86" />
<hkern g1="dcaron,lcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="23" />
<hkern g1="dcaron,lcaron" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="14" />
<hkern g1="dcaron,lcaron" 	g2="bar" 	k="-45" />
<hkern g1="dcaron,lcaron" 	g2="exclam" 	k="-66" />
<hkern g1="dcaron,lcaron" 	g2="j" 	k="-84" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="195" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="37" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="201" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quoteright,quotedblright" 	k="66" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quotedbl,quotesingle" 	k="59" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="t,tcaron,tbar,uni021B" 	k="6" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="29" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="39" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="V" 	k="111" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="asterisk" 	k="43" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="backslash" 	k="94" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="braceright" 	k="74" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="longs" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="ordfeminine" 	k="33" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="ordmasculine" 	k="39" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="parenright" 	k="88" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="question" 	k="33" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="trademark" 	k="63" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="v" 	k="37" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quoteleft,quotedblleft" 	k="59" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="bracketright" 	k="66" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="X" 	k="35" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="slash" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="x" 	k="23" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="z,zacute,zdotaccent,zcaron" 	k="2" />
<hkern g1="f" 	g2="T,Tcaron,Tbar,uni021A" 	k="6" />
<hkern g1="f" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="2" />
<hkern g1="f" 	g2="quotedbl,quotesingle" 	k="-2" />
<hkern g1="f" 	g2="V" 	k="-2" />
<hkern g1="f" 	g2="asterisk" 	k="-2" />
<hkern g1="f" 	g2="braceright" 	k="-16" />
<hkern g1="f" 	g2="ordmasculine" 	k="-4" />
<hkern g1="f" 	g2="parenright" 	k="-20" />
<hkern g1="f" 	g2="trademark" 	k="-4" />
<hkern g1="f" 	g2="bracketright" 	k="-16" />
<hkern g1="f" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="86" />
<hkern g1="f" 	g2="slash" 	k="80" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="80" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="14" />
<hkern g1="f" 	g2="d,q,dcaron,dcroat" 	k="18" />
<hkern g1="f" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="18" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="25" />
<hkern g1="f" 	g2="hyphen,endash,emdash" 	k="63" />
<hkern g1="f" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="20" />
<hkern g1="f" 	g2="eth" 	k="61" />
<hkern g1="f" 	g2="space" 	k="45" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,uniFB01" 	g2="T,Tcaron,Tbar,uni021A" 	k="6" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="T,Tcaron,Tbar,uni021A" 	k="152" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="2" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="102" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="V" 	k="37" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="asterisk" 	k="14" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="backslash" 	k="61" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="braceright" 	k="41" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="parenright" 	k="53" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="trademark" 	k="37" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="2" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="bracketright" 	k="45" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="20" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="d,q,dcaron,dcroat" 	k="49" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="51" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="hyphen,endash,emdash" 	k="88" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="49" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="20" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="eth" 	k="57" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="space" 	k="20" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="J,Jcircumflex" 	k="23" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="18" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="2" />
<hkern g1="l,lacute,uni013C,lslash,uniFB02" 	g2="T,Tcaron,Tbar,uni021A" 	k="41" />
<hkern g1="l,lacute,uni013C,lslash,uniFB02" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="8" />
<hkern g1="l,lacute,uni013C,lslash,uniFB02" 	g2="periodcentered" 	k="84" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="T,Tcaron,Tbar,uni021A" 	k="203" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="41" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="170" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="f,uniFB01,uniFB02" 	k="16" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="quoteright,quotedblright" 	k="66" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="t,tcaron,tbar,uni021B" 	k="14" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="14" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="33" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="V" 	k="102" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="asterisk" 	k="45" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="backslash" 	k="102" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="braceright" 	k="61" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="longs" 	k="16" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="ordfeminine" 	k="33" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="ordmasculine" 	k="41" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="parenright" 	k="76" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="question" 	k="37" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="trademark" 	k="63" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="v" 	k="31" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="14" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="10" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="bracketright" 	k="55" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="197" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="43" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="172" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="f,uniFB01,uniFB02" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quoteright,quotedblright" 	k="68" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="t,tcaron,tbar,uni021B" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="29" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="V" 	k="106" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="asterisk" 	k="45" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="backslash" 	k="100" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="braceright" 	k="78" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="longs" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="ordfeminine" 	k="33" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="ordmasculine" 	k="39" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="parenright" 	k="94" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="question" 	k="39" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="trademark" 	k="63" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="v" 	k="39" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="29" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quoteleft,quotedblleft" 	k="63" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="bracketright" 	k="70" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="X" 	k="49" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="slash" 	k="33" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="x" 	k="27" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="z,zacute,zdotaccent,zcaron" 	k="12" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="158" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="98" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="V" 	k="35" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="backslash" 	k="12" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="braceright" 	k="63" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="parenright" 	k="82" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="trademark" 	k="4" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="76" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="bracketright" 	k="61" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="96" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="X" 	k="72" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="slash" 	k="96" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="111" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="16" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="d,q,dcaron,dcroat" 	k="23" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="23" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="guillemotleft,guilsinglleft" 	k="23" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="hyphen,endash,emdash" 	k="84" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="20" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="eth" 	k="66" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="space" 	k="47" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="J,Jcircumflex" 	k="88" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="T,Tcaron,Tbar,uni021A" 	k="186" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="37" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="184" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="f,uniFB01,uniFB02" 	k="2" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="quoteright,quotedblright" 	k="53" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="quotedbl,quotesingle" 	k="49" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="t,tcaron,tbar,uni021B" 	k="2" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="29" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="37" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="V" 	k="106" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="asterisk" 	k="39" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="backslash" 	k="92" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="braceright" 	k="66" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="longs" 	k="2" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="ordfeminine" 	k="23" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="ordmasculine" 	k="31" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="parenright" 	k="84" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="question" 	k="6" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="trademark" 	k="57" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="v" 	k="37" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="8" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="quoteleft,quotedblleft" 	k="49" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="bracketright" 	k="61" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="X" 	k="14" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="slash" 	k="6" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="x" 	k="6" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="hyphen,endash,emdash" 	k="23" />
<hkern g1="t,tbar,uni021B" 	g2="T,Tcaron,Tbar,uni021A" 	k="129" />
<hkern g1="t,tbar,uni021B" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="88" />
<hkern g1="t,tbar,uni021B" 	g2="V" 	k="31" />
<hkern g1="t,tbar,uni021B" 	g2="backslash" 	k="41" />
<hkern g1="t,tbar,uni021B" 	g2="braceright" 	k="27" />
<hkern g1="t,tbar,uni021B" 	g2="parenright" 	k="37" />
<hkern g1="t,tbar,uni021B" 	g2="trademark" 	k="8" />
<hkern g1="t,tbar,uni021B" 	g2="bracketright" 	k="27" />
<hkern g1="t,tbar,uni021B" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="t,tbar,uni021B" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="T,Tcaron,Tbar,uni021A" 	k="170" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="25" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="141" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="V" 	k="80" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="asterisk" 	k="16" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="backslash" 	k="72" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="braceright" 	k="55" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="parenright" 	k="70" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="trademark" 	k="35" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="8" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="12" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="bracketright" 	k="49" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="T,Tcaron,Tbar,uni021A" 	k="152" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="109" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="V" 	k="35" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="backslash" 	k="55" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="braceright" 	k="68" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="parenright" 	k="86" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="trademark" 	k="4" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="53" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="bracketright" 	k="66" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="41" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="X" 	k="63" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="slash" 	k="76" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="z,zacute,zdotaccent,zcaron" 	k="2" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="31" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="d,q,dcaron,dcroat" 	k="29" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="27" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="hyphen,endash,emdash" 	k="31" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="29" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="27" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="eth" 	k="37" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="space" 	k="47" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="J,Jcircumflex" 	k="37" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="ampersand" 	k="4" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="T,Tcaron,Tbar,uni021A" 	k="168" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="106" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="V" 	k="35" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="backslash" 	k="53" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="braceright" 	k="74" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="parenright" 	k="90" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="question" 	k="-2" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="trademark" 	k="4" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="78" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="bracketright" 	k="72" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="70" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="X" 	k="84" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="slash" 	k="100" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="119" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="z,zacute,zdotaccent,zcaron" 	k="4" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="39" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="d,q,dcaron,dcroat" 	k="37" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="35" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="guillemotleft,guilsinglleft" 	k="25" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="hyphen,endash,emdash" 	k="47" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="37" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="33" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="eth" 	k="55" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="space" 	k="55" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="J,Jcircumflex" 	k="66" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="ampersand" 	k="23" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="182" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="16" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="133" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="4" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="V" 	k="63" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="backslash" 	k="66" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="braceright" 	k="43" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="parenright" 	k="57" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="trademark" 	k="35" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="v" 	k="2" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="14" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="bracketright" 	k="47" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="d,q,dcaron,dcroat" 	k="12" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="12" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="hyphen,endash,emdash" 	k="53" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="eth" 	k="16" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="J,Jcircumflex" 	k="16" />
<hkern g1="eth" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="14" />
<hkern g1="eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="eth" 	g2="z,zacute,zdotaccent,zcaron" 	k="6" />
<hkern g1="germandbls" 	g2="f,uniFB01,uniFB02" 	k="2" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="14" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="14" />
<hkern g1="germandbls" 	g2="t,tcaron,tbar,uni021B" 	k="2" />
<hkern g1="germandbls" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="27" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="35" />
<hkern g1="germandbls" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="16" />
<hkern g1="germandbls" 	g2="z,zacute,zdotaccent,zcaron" 	k="8" />
<hkern g1="longs" 	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij" 	k="-90" />
<hkern g1="tcaron" 	g2="f,uniFB01,uniFB02" 	k="-14" />
<hkern g1="tcaron" 	g2="quoteright,quotedblright" 	k="-117" />
<hkern g1="tcaron" 	g2="quotedbl,quotesingle" 	k="-166" />
<hkern g1="tcaron" 	g2="t,tcaron,tbar,uni021B" 	k="-47" />
<hkern g1="tcaron" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="-16" />
<hkern g1="tcaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="-16" />
<hkern g1="tcaron" 	g2="quoteleft,quotedblleft" 	k="-68" />
<hkern g1="tcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="tcaron" 	g2="z,zacute,zdotaccent,zcaron" 	k="-6" />
<hkern g1="tcaron" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="10" />
<hkern g1="tcaron" 	g2="colon,semicolon" 	k="-14" />
<hkern g1="tcaron" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="tcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="8" />
<hkern g1="tcaron" 	g2="guillemotleft,guilsinglleft" 	k="4" />
<hkern g1="tcaron" 	g2="guillemotright,guilsinglright" 	k="-68" />
<hkern g1="tcaron" 	g2="b,h,k,germandbls,thorn,hcircumflex,hbar,uni0137" 	k="-131" />
<hkern g1="tcaron" 	g2="hyphen,endash,emdash" 	k="33" />
<hkern g1="tcaron" 	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij" 	k="-150" />
<hkern g1="tcaron" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="-131" />
<hkern g1="tcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="tcaron" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="8" />
<hkern g1="tcaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="-6" />
<hkern g1="tcaron" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="-4" />
<hkern g1="v" 	g2="T,Tcaron,Tbar,uni021A" 	k="166" />
<hkern g1="v" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="111" />
<hkern g1="v" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="76" />
<hkern g1="v" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="63" />
<hkern g1="v" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="111" />
<hkern g1="v" 	g2="z,zacute,zdotaccent,zcaron" 	k="4" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="41" />
<hkern g1="v" 	g2="d,q,dcaron,dcroat" 	k="37" />
<hkern g1="v" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="37" />
<hkern g1="v" 	g2="guillemotleft,guilsinglleft" 	k="27" />
<hkern g1="v" 	g2="hyphen,endash,emdash" 	k="45" />
<hkern g1="v" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="39" />
<hkern g1="v" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="33" />
<hkern g1="v" 	g2="J,Jcircumflex" 	k="61" />
<hkern g1="x" 	g2="T,Tcaron,Tbar,uni021A" 	k="152" />
<hkern g1="x" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="2" />
<hkern g1="x" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="113" />
<hkern g1="x" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="2" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="12" />
<hkern g1="x" 	g2="d,q,dcaron,dcroat" 	k="29" />
<hkern g1="x" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="29" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="x" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="x" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="29" />
<hkern g1="x" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="4" />
<hkern g1="x" 	g2="J,Jcircumflex" 	k="27" />
<hkern g1="x" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="4" />
<hkern g1="x" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="2" />
<hkern g1="x" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="2" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="88" />
<hkern g1="colon,semicolon" 	g2="V" 	k="49" />
<hkern g1="colon,semicolon" 	g2="T,Tcaron,Tbar,uni021A" 	k="94" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="47" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T,Tcaron,Tbar,uni021A" 	k="76" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="129" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="72" />
<hkern g1="guillemotright,guilsinglright" 	g2="T,Tcaron,Tbar,uni021A" 	k="129" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="47" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE,AEacute" 	k="61" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="74" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="94" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="39" />
<hkern g1="guillemotright,guilsinglright" 	g2="J,Jcircumflex" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="18" />
<hkern g1="guillemotright,guilsinglright" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="25" />
<hkern g1="guillemotright,guilsinglright" 	g2="f,uniFB01,uniFB02" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="18" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="27" />
<hkern g1="guillemotright,guilsinglright" 	g2="z,zacute,zdotaccent,zcaron" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="j" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="v" 	k="27" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="143" />
<hkern g1="hyphen,endash,emdash" 	g2="V" 	k="86" />
<hkern g1="hyphen,endash,emdash" 	g2="T,Tcaron,Tbar,uni021A" 	k="131" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="47" />
<hkern g1="hyphen,endash,emdash" 	g2="AE,AEacute" 	k="63" />
<hkern g1="hyphen,endash,emdash" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="84" />
<hkern g1="hyphen,endash,emdash" 	g2="X" 	k="100" />
<hkern g1="hyphen,endash,emdash" 	g2="x" 	k="63" />
<hkern g1="hyphen,endash,emdash" 	g2="J,Jcircumflex" 	k="29" />
<hkern g1="hyphen,endash,emdash" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="37" />
<hkern g1="hyphen,endash,emdash" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="39" />
<hkern g1="hyphen,endash,emdash" 	g2="f,uniFB01,uniFB02" 	k="39" />
<hkern g1="hyphen,endash,emdash" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="31" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="49" />
<hkern g1="hyphen,endash,emdash" 	g2="z,zacute,zdotaccent,zcaron" 	k="57" />
<hkern g1="hyphen,endash,emdash" 	g2="j" 	k="25" />
<hkern g1="hyphen,endash,emdash" 	g2="v" 	k="45" />
<hkern g1="hyphen,endash,emdash" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="16" />
<hkern g1="hyphen,endash,emdash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="14" />
<hkern g1="hyphen,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="23" />
<hkern g1="hyphen,endash,emdash" 	g2="t,tcaron,tbar,uni021B" 	k="33" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="166" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="141" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T,Tcaron,Tbar,uni021A" 	k="133" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="14" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="76" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="f,uniFB01,uniFB02" 	k="49" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="119" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v" 	k="111" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="t,tcaron,tbar,uni021B" 	k="57" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="d,q,dcaron,dcroat" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="eth" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="145" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE,AEacute" 	k="178" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="76" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="d,q,dcaron,dcroat" 	k="98" />
<hkern g1="quoteleft,quotedblleft" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="80" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="82" />
<hkern g1="quoteleft,quotedblleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="eth" 	k="94" />
<hkern g1="quoteleft,quotedblleft" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="23" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="-6" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-2" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="152" />
<hkern g1="quoteright,quotedblright" 	g2="AE,AEacute" 	k="184" />
<hkern g1="quoteright,quotedblright" 	g2="x" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="2" />
<hkern g1="quoteright,quotedblright" 	g2="z,zacute,zdotaccent,zcaron" 	k="25" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="88" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="d,q,dcaron,dcroat" 	k="111" />
<hkern g1="quoteright,quotedblright" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="90" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="94" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="eth" 	k="96" />
<hkern g1="quoteright,quotedblright" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="74" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="139" />
<hkern g1="quotedbl,quotesingle" 	g2="AE,AEacute" 	k="170" />
<hkern g1="quotedbl,quotesingle" 	g2="J,Jcircumflex" 	k="2" />
<hkern g1="quotedbl,quotesingle" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="59" />
<hkern g1="quotedbl,quotesingle" 	g2="d,q,dcaron,dcroat" 	k="72" />
<hkern g1="quotedbl,quotesingle" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="59" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="61" />
<hkern g1="quotedbl,quotesingle" 	g2="eth" 	k="90" />
<hkern g1="quotedbl,quotesingle" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="45" />
<hkern g1="quotedbl,quotesingle" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="16" />
<hkern g1="asterisk" 	g2="T,Tcaron,Tbar,uni021A" 	k="-2" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="125" />
<hkern g1="asterisk" 	g2="AE,AEacute" 	k="154" />
<hkern g1="asterisk" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="47" />
<hkern g1="asterisk" 	g2="d,q,dcaron,dcroat" 	k="49" />
<hkern g1="asterisk" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="43" />
<hkern g1="asterisk" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="45" />
<hkern g1="asterisk" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="16" />
<hkern g1="asterisk" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="35" />
<hkern g1="asterisk" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="16" />
<hkern g1="backslash" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="170" />
<hkern g1="backslash" 	g2="T,Tcaron,Tbar,uni021A" 	k="137" />
<hkern g1="backslash" 	g2="J,Jcircumflex" 	k="6" />
<hkern g1="backslash" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="6" />
<hkern g1="backslash" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="76" />
<hkern g1="backslash" 	g2="f,uniFB01,uniFB02" 	k="55" />
<hkern g1="backslash" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="76" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="82" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="45" />
<hkern g1="backslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="4" />
<hkern g1="backslash" 	g2="t,tcaron,tbar,uni021B" 	k="70" />
<hkern g1="backslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="41" />
<hkern g1="backslash" 	g2="d,q,dcaron,dcroat" 	k="35" />
<hkern g1="backslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="33" />
<hkern g1="backslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="37" />
<hkern g1="backslash" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="6" />
<hkern g1="braceleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="47" />
<hkern g1="braceleft" 	g2="AE,AEacute" 	k="39" />
<hkern g1="braceleft" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="39" />
<hkern g1="braceleft" 	g2="f,uniFB01,uniFB02" 	k="47" />
<hkern g1="braceleft" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="68" />
<hkern g1="braceleft" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="53" />
<hkern g1="braceleft" 	g2="z,zacute,zdotaccent,zcaron" 	k="45" />
<hkern g1="braceleft" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="16" />
<hkern g1="braceleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="18" />
<hkern g1="braceleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="63" />
<hkern g1="braceleft" 	g2="t,tcaron,tbar,uni021B" 	k="43" />
<hkern g1="braceleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="53" />
<hkern g1="braceleft" 	g2="d,q,dcaron,dcroat" 	k="78" />
<hkern g1="braceleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="78" />
<hkern g1="braceleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="68" />
<hkern g1="braceleft" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="66" />
<hkern g1="braceleft" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="57" />
<hkern g1="bracketleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="49" />
<hkern g1="bracketleft" 	g2="AE,AEacute" 	k="43" />
<hkern g1="bracketleft" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="39" />
<hkern g1="bracketleft" 	g2="f,uniFB01,uniFB02" 	k="47" />
<hkern g1="bracketleft" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="66" />
<hkern g1="bracketleft" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="53" />
<hkern g1="bracketleft" 	g2="z,zacute,zdotaccent,zcaron" 	k="47" />
<hkern g1="bracketleft" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="16" />
<hkern g1="bracketleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="18" />
<hkern g1="bracketleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="61" />
<hkern g1="bracketleft" 	g2="t,tcaron,tbar,uni021B" 	k="41" />
<hkern g1="bracketleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="51" />
<hkern g1="bracketleft" 	g2="d,q,dcaron,dcroat" 	k="72" />
<hkern g1="bracketleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="70" />
<hkern g1="bracketleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="63" />
<hkern g1="bracketleft" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="63" />
<hkern g1="bracketleft" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="51" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="84" />
<hkern g1="exclamdown" 	g2="T,Tcaron,Tbar,uni021A" 	k="104" />
<hkern g1="parenleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="57" />
<hkern g1="parenleft" 	g2="AE,AEacute" 	k="47" />
<hkern g1="parenleft" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="47" />
<hkern g1="parenleft" 	g2="f,uniFB01,uniFB02" 	k="57" />
<hkern g1="parenleft" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="86" />
<hkern g1="parenleft" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="63" />
<hkern g1="parenleft" 	g2="z,zacute,zdotaccent,zcaron" 	k="57" />
<hkern g1="parenleft" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="18" />
<hkern g1="parenleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="20" />
<hkern g1="parenleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="82" />
<hkern g1="parenleft" 	g2="t,tcaron,tbar,uni021B" 	k="49" />
<hkern g1="parenleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="63" />
<hkern g1="parenleft" 	g2="d,q,dcaron,dcroat" 	k="92" />
<hkern g1="parenleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="94" />
<hkern g1="parenleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="84" />
<hkern g1="parenleft" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="84" />
<hkern g1="parenleft" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="72" />
<hkern g1="periodcentered" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="84" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="168" />
<hkern g1="questiondown" 	g2="T,Tcaron,Tbar,uni021A" 	k="143" />
<hkern g1="questiondown" 	g2="J,Jcircumflex" 	k="6" />
<hkern g1="questiondown" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="6" />
<hkern g1="questiondown" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="78" />
<hkern g1="questiondown" 	g2="f,uniFB01,uniFB02" 	k="43" />
<hkern g1="questiondown" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="90" />
<hkern g1="questiondown" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="98" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="61" />
<hkern g1="questiondown" 	g2="t,tcaron,tbar,uni021B" 	k="66" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="59" />
<hkern g1="questiondown" 	g2="d,q,dcaron,dcroat" 	k="35" />
<hkern g1="questiondown" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="37" />
<hkern g1="questiondown" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="35" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="137" />
<hkern g1="slash" 	g2="AE,AEacute" 	k="147" />
<hkern g1="slash" 	g2="J,Jcircumflex" 	k="4" />
<hkern g1="slash" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="27" />
<hkern g1="slash" 	g2="f,uniFB01,uniFB02" 	k="29" />
<hkern g1="slash" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="55" />
<hkern g1="slash" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="57" />
<hkern g1="slash" 	g2="z,zacute,zdotaccent,zcaron" 	k="59" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="100" />
<hkern g1="slash" 	g2="t,tcaron,tbar,uni021B" 	k="10" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="41" />
<hkern g1="slash" 	g2="d,q,dcaron,dcroat" 	k="100" />
<hkern g1="slash" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="98" />
<hkern g1="slash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="100" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="66" />
<hkern g1="slash" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="88" />
<hkern g1="slash" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="72" />
<hkern g1="ampersand" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="-4" />
<hkern g1="ampersand" 	g2="AE,AEacute" 	k="-2" />
<hkern g1="ampersand" 	g2="T,Tcaron,Tbar,uni021A" 	k="98" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="119" />
<hkern g1="ampersand" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="29" />
<hkern g1="ampersand" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="25" />
<hkern g1="at" 	g2="T,Tcaron,Tbar,uni021A" 	k="66" />
<hkern g1="at" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="82" />
<hkern g1="at" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="16" />
<hkern g1="copyright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="31" />
<hkern g1="copyright" 	g2="AE,AEacute" 	k="37" />
<hkern g1="copyright" 	g2="T,Tcaron,Tbar,uni021A" 	k="57" />
<hkern g1="copyright" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="76" />
<hkern g1="copyright" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="31" />
<hkern g1="registered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="31" />
<hkern g1="registered" 	g2="AE,AEacute" 	k="37" />
<hkern g1="registered" 	g2="T,Tcaron,Tbar,uni021A" 	k="55" />
<hkern g1="registered" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="76" />
<hkern g1="registered" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="31" />
<hkern g1="eight" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="49" />
<hkern g1="five" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="18" />
<hkern g1="four" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="74" />
<hkern g1="four" 	g2="T,Tcaron,Tbar,uni021A" 	k="45" />
<hkern g1="four" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="18" />
<hkern g1="nine" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="37" />
<hkern g1="nine" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="76" />
<hkern g1="nine" 	g2="J,Jcircumflex" 	k="27" />
<hkern g1="nine" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="20" />
<hkern g1="seven" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="102" />
<hkern g1="six" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="104" />
<hkern g1="six" 	g2="T,Tcaron,Tbar,uni021A" 	k="84" />
<hkern g1="six" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="20" />
<hkern g1="two" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="39" />
<hkern g1="zero" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="49" />
<hkern g1="zero" 	g2="T,Tcaron,Tbar,uni021A" 	k="18" />
<hkern g1="zero" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="4" />
<hkern g1="space" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="55" />
<hkern g1="space" 	g2="AE,AEacute" 	k="61" />
<hkern g1="space" 	g2="T,Tcaron,Tbar,uni021A" 	k="51" />
<hkern g1="space" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="61" />
<hkern g1="space" 	g2="f,uniFB01,uniFB02" 	k="39" />
<hkern g1="space" 	g2="t,tcaron,tbar,uni021B" 	k="43" />
<hkern g1="space" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="47" />
<hkern g1="space" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="55" />
<hkern g1="space" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="35" />
<hkern g1="uni0403,uni0413" 	g2="uni0409,uni041B" 	k="59" />
<hkern g1="uni0403,uni0413" 	g2="uni0404,uni041E,uni0421" 	k="27" />
<hkern g1="uni0403,uni0413" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="176" />
<hkern g1="uni0403,uni0413" 	g2="uni043B,uni0459" 	k="248" />
<hkern g1="uni0403,uni0413" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="256" />
<hkern g1="uni0403,uni0413" 	g2="uni0456,uni0457" 	k="2" />
<hkern g1="uni0403,uni0413" 	g2="uni0452,uni045B" 	k="-8" />
<hkern g1="uni0403,uni0413" 	g2="guillemotleft,guilsinglleft" 	k="150" />
<hkern g1="uni0403,uni0413" 	g2="hyphen,endash,emdash" 	k="168" />
<hkern g1="uni0403,uni0413" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="203" />
<hkern g1="uni0403,uni0413" 	g2="asterisk" 	k="-4" />
<hkern g1="uni0403,uni0413" 	g2="slash" 	k="166" />
<hkern g1="uni0403,uni0413" 	g2="uni0443,uni045E" 	k="170" />
<hkern g1="uni0403,uni0413" 	g2="colon,semicolon" 	k="111" />
<hkern g1="uni0403,uni0413" 	g2="guillemotright,guilsinglright" 	k="57" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="uni0404,uni041E,uni0421" 	k="14" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="12" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="hyphen,endash,emdash" 	k="33" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="uni0443,uni045E" 	k="37" />
<hkern g1="uni040C,uni041A" 	g2="uni0404,uni041E,uni0421" 	k="76" />
<hkern g1="uni040C,uni041A" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="61" />
<hkern g1="uni040C,uni041A" 	g2="guillemotleft,guilsinglleft" 	k="115" />
<hkern g1="uni040C,uni041A" 	g2="hyphen,endash,emdash" 	k="121" />
<hkern g1="uni040C,uni041A" 	g2="uni0443,uni045E" 	k="104" />
<hkern g1="uni040C,uni041A" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="uni0409,uni041B" 	k="20" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="uni043B,uni0459" 	k="29" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="slash" 	k="39" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="uni040E,uni0423" 	k="16" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="backslash" 	k="41" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="braceright" 	k="53" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="bracketright" 	k="51" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="parenright" 	k="63" />
<hkern g1="uni040E,uni0423" 	g2="uni0409,uni041B" 	k="63" />
<hkern g1="uni040E,uni0423" 	g2="uni0404,uni041E,uni0421" 	k="23" />
<hkern g1="uni040E,uni0423" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="82" />
<hkern g1="uni040E,uni0423" 	g2="uni043B,uni0459" 	k="164" />
<hkern g1="uni040E,uni0423" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="137" />
<hkern g1="uni040E,uni0423" 	g2="uni0452,uni045B" 	k="-10" />
<hkern g1="uni040E,uni0423" 	g2="guillemotleft,guilsinglleft" 	k="80" />
<hkern g1="uni040E,uni0423" 	g2="hyphen,endash,emdash" 	k="98" />
<hkern g1="uni040E,uni0423" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="166" />
<hkern g1="uni040E,uni0423" 	g2="slash" 	k="147" />
<hkern g1="uni040E,uni0423" 	g2="uni0443,uni045E" 	k="33" />
<hkern g1="uni040E,uni0423" 	g2="colon,semicolon" 	k="49" />
<hkern g1="uni040E,uni0423" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="uni040E,uni0423" 	g2="braceright" 	k="-2" />
<hkern g1="uni040E,uni0423" 	g2="parenright" 	k="-2" />
<hkern g1="uni0426,uni0429" 	g2="uni0404,uni041E,uni0421" 	k="2" />
<hkern g1="uni0426,uni0429" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="8" />
<hkern g1="uni0426,uni0429" 	g2="uni0452,uni045B" 	k="2" />
<hkern g1="uni0426,uni0429" 	g2="guillemotleft,guilsinglleft" 	k="4" />
<hkern g1="uni0426,uni0429" 	g2="hyphen,endash,emdash" 	k="23" />
<hkern g1="uni0426,uni0429" 	g2="asterisk" 	k="10" />
<hkern g1="uni0426,uni0429" 	g2="uni0443,uni045E" 	k="23" />
<hkern g1="uni0426,uni0429" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="uni0426,uni0429" 	g2="backslash" 	k="12" />
<hkern g1="uni0426,uni0429" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="uni0426,uni0429" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="uni0426,uni0429" 	g2="question" 	k="8" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="uni0452,uni045B" 	k="4" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="asterisk" 	k="100" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="slash" 	k="6" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="uni0443,uni045E" 	k="43" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="quoteleft,quotedblleft" 	k="133" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="uni040E,uni0423" 	k="35" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="backslash" 	k="113" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="braceright" 	k="68" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="bracketright" 	k="61" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="parenright" 	k="84" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="quoteright,quotedblright" 	k="137" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="quotedbl,quotesingle" 	k="131" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="question" 	k="63" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="hyphen,endash,emdash" 	k="16" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="16" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="braceright" 	k="16" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="bracketright" 	k="16" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="parenright" 	k="18" />
<hkern g1="uni0433,uni0453" 	g2="uni043B,uni0459" 	k="55" />
<hkern g1="uni0433,uni0453" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="23" />
<hkern g1="uni0433,uni0453" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="170" />
<hkern g1="uni0433,uni0453" 	g2="backslash" 	k="12" />
<hkern g1="uni0433,uni0453" 	g2="braceright" 	k="63" />
<hkern g1="uni0433,uni0453" 	g2="bracketright" 	k="61" />
<hkern g1="uni0433,uni0453" 	g2="parenright" 	k="82" />
<hkern g1="uni0433,uni0453" 	g2="slash" 	k="119" />
<hkern g1="uni0433,uni0453" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="uni0433,uni0453" 	g2="hyphen,endash,emdash" 	k="98" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="uni043B,uni0459" 	k="8" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="backslash" 	k="94" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="braceright" 	k="74" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="bracketright" 	k="66" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="parenright" 	k="88" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="slash" 	k="8" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="uni0443,uni045E" 	k="41" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="uni0452,uni045B" 	k="4" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="quoteright,quotedblright" 	k="66" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="quotedbl,quotesingle" 	k="59" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="asterisk" 	k="43" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="question" 	k="33" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="quoteleft,quotedblleft" 	k="59" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="backslash" 	k="72" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="braceright" 	k="57" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="bracketright" 	k="51" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="parenright" 	k="72" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="uni0452,uni045B" 	k="2" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="asterisk" 	k="16" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="uni043A,uni045C" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="49" />
<hkern g1="uni043A,uni045C" 	g2="backslash" 	k="57" />
<hkern g1="uni043A,uni045C" 	g2="braceright" 	k="41" />
<hkern g1="uni043A,uni045C" 	g2="bracketright" 	k="45" />
<hkern g1="uni043A,uni045C" 	g2="parenright" 	k="53" />
<hkern g1="uni043A,uni045C" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="uni043A,uni045C" 	g2="hyphen,endash,emdash" 	k="86" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="uni043B,uni0459" 	k="20" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="backslash" 	k="100" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="braceright" 	k="78" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="bracketright" 	k="70" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="parenright" 	k="94" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="slash" 	k="33" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="uni0443,uni045E" 	k="41" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="uni0452,uni045B" 	k="6" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="quoteright,quotedblright" 	k="68" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="asterisk" 	k="45" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="question" 	k="39" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="quoteleft,quotedblleft" 	k="63" />
<hkern g1="uni0440,uni0444" 	g2="uni043B,uni0459" 	k="18" />
<hkern g1="uni0440,uni0444" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="uni0440,uni0444" 	g2="backslash" 	k="100" />
<hkern g1="uni0440,uni0444" 	g2="braceright" 	k="78" />
<hkern g1="uni0440,uni0444" 	g2="bracketright" 	k="72" />
<hkern g1="uni0440,uni0444" 	g2="parenright" 	k="94" />
<hkern g1="uni0440,uni0444" 	g2="slash" 	k="33" />
<hkern g1="uni0440,uni0444" 	g2="uni0443,uni045E" 	k="41" />
<hkern g1="uni0440,uni0444" 	g2="uni0452,uni045B" 	k="4" />
<hkern g1="uni0440,uni0444" 	g2="quoteright,quotedblright" 	k="63" />
<hkern g1="uni0440,uni0444" 	g2="quotedbl,quotesingle" 	k="59" />
<hkern g1="uni0440,uni0444" 	g2="asterisk" 	k="43" />
<hkern g1="uni0440,uni0444" 	g2="question" 	k="35" />
<hkern g1="uni0440,uni0444" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="uni0443,uni045E" 	g2="uni043B,uni0459" 	k="63" />
<hkern g1="uni0443,uni045E" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="37" />
<hkern g1="uni0443,uni045E" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="117" />
<hkern g1="uni0443,uni045E" 	g2="backslash" 	k="55" />
<hkern g1="uni0443,uni045E" 	g2="braceright" 	k="74" />
<hkern g1="uni0443,uni045E" 	g2="bracketright" 	k="72" />
<hkern g1="uni0443,uni045E" 	g2="parenright" 	k="90" />
<hkern g1="uni0443,uni045E" 	g2="slash" 	k="100" />
<hkern g1="uni0443,uni045E" 	g2="guillemotleft,guilsinglleft" 	k="27" />
<hkern g1="uni0443,uni045E" 	g2="hyphen,endash,emdash" 	k="47" />
<hkern g1="uni0443,uni045E" 	g2="question" 	k="-2" />
<hkern g1="uni0446,uni0449" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="2" />
<hkern g1="uni0446,uni0449" 	g2="backslash" 	k="88" />
<hkern g1="uni0446,uni0449" 	g2="guillemotleft,guilsinglleft" 	k="6" />
<hkern g1="uni0446,uni0449" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="uni0446,uni0449" 	g2="uni0443,uni045E" 	k="23" />
<hkern g1="uni0446,uni0449" 	g2="uni0452,uni045B" 	k="4" />
<hkern g1="uni0446,uni0449" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="uni0446,uni0449" 	g2="quotedbl,quotesingle" 	k="33" />
<hkern g1="uni0446,uni0449" 	g2="asterisk" 	k="37" />
<hkern g1="uni0446,uni0449" 	g2="question" 	k="10" />
<hkern g1="uni0446,uni0449" 	g2="quoteleft,quotedblleft" 	k="33" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="backslash" 	k="127" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="braceright" 	k="74" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="bracketright" 	k="68" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="parenright" 	k="92" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="slash" 	k="8" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="uni0443,uni045E" 	k="70" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="uni0452,uni045B" 	k="6" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="quoteright,quotedblright" 	k="156" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="quotedbl,quotesingle" 	k="160" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="asterisk" 	k="131" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="question" 	k="84" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="quoteleft,quotedblleft" 	k="154" />
<hkern g1="guillemotright,guilsinglright" 	g2="uni0409,uni041B" 	k="57" />
<hkern g1="guillemotright,guilsinglright" 	g2="uni040E,uni0423" 	k="78" />
<hkern g1="guillemotright,guilsinglright" 	g2="uni043B,uni0459" 	k="45" />
<hkern g1="guillemotright,guilsinglright" 	g2="uni0443,uni045E" 	k="27" />
<hkern g1="hyphen,endash,emdash" 	g2="uni0409,uni041B" 	k="61" />
<hkern g1="hyphen,endash,emdash" 	g2="uni040E,uni0423" 	k="96" />
<hkern g1="hyphen,endash,emdash" 	g2="uni043B,uni0459" 	k="49" />
<hkern g1="hyphen,endash,emdash" 	g2="uni0443,uni045E" 	k="51" />
<hkern g1="hyphen,endash,emdash" 	g2="uni0452,uni045B" 	k="4" />
<hkern g1="hyphen,endash,emdash" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni040E,uni0423" 	k="16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0443,uni045E" 	k="121" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0452,uni045B" 	k="4" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0404,uni041E,uni0421" 	k="35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0409,uni041B" 	k="66" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni043B,uni0459" 	k="72" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0452,uni045B" 	k="-2" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0404,uni041E,uni0421" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="82" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="23" />
<hkern g1="quoteright,quotedblright" 	g2="uni0409,uni041B" 	k="63" />
<hkern g1="quoteright,quotedblright" 	g2="uni040E,uni0423" 	k="-4" />
<hkern g1="quoteright,quotedblright" 	g2="uni043B,uni0459" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="uni0443,uni045E" 	k="2" />
<hkern g1="quoteright,quotedblright" 	g2="uni0452,uni045B" 	k="-6" />
<hkern g1="quoteright,quotedblright" 	g2="uni0404,uni041E,uni0421" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="94" />
<hkern g1="quoteright,quotedblright" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="uni0409,uni041B" 	k="63" />
<hkern g1="quotedbl,quotesingle" 	g2="uni043B,uni0459" 	k="61" />
<hkern g1="quotedbl,quotesingle" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="61" />
<hkern g1="quotedbl,quotesingle" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="16" />
<hkern g1="asterisk" 	g2="uni0409,uni041B" 	k="61" />
<hkern g1="asterisk" 	g2="uni043B,uni0459" 	k="63" />
<hkern g1="asterisk" 	g2="uni0452,uni045B" 	k="-12" />
<hkern g1="asterisk" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="45" />
<hkern g1="asterisk" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="16" />
<hkern g1="backslash" 	g2="uni0443,uni045E" 	k="70" />
<hkern g1="backslash" 	g2="uni0452,uni045B" 	k="8" />
<hkern g1="backslash" 	g2="uni0404,uni041E,uni0421" 	k="41" />
<hkern g1="backslash" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="33" />
<hkern g1="braceleft" 	g2="uni0409,uni041B" 	k="27" />
<hkern g1="braceleft" 	g2="uni043B,uni0459" 	k="45" />
<hkern g1="braceleft" 	g2="uni0443,uni045E" 	k="4" />
<hkern g1="braceleft" 	g2="uni0452,uni045B" 	k="-4" />
<hkern g1="braceleft" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="16" />
<hkern g1="braceleft" 	g2="uni0404,uni041E,uni0421" 	k="53" />
<hkern g1="braceleft" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="78" />
<hkern g1="braceleft" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="57" />
<hkern g1="bracketleft" 	g2="uni0409,uni041B" 	k="31" />
<hkern g1="bracketleft" 	g2="uni043B,uni0459" 	k="47" />
<hkern g1="bracketleft" 	g2="uni0443,uni045E" 	k="4" />
<hkern g1="bracketleft" 	g2="uni0452,uni045B" 	k="-4" />
<hkern g1="bracketleft" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="16" />
<hkern g1="bracketleft" 	g2="uni0404,uni041E,uni0421" 	k="51" />
<hkern g1="bracketleft" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="70" />
<hkern g1="bracketleft" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="51" />
<hkern g1="parenleft" 	g2="uni0409,uni041B" 	k="33" />
<hkern g1="parenleft" 	g2="uni043B,uni0459" 	k="53" />
<hkern g1="parenleft" 	g2="uni0443,uni045E" 	k="4" />
<hkern g1="parenleft" 	g2="uni0452,uni045B" 	k="-4" />
<hkern g1="parenleft" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="20" />
<hkern g1="parenleft" 	g2="uni0404,uni041E,uni0421" 	k="63" />
<hkern g1="parenleft" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="94" />
<hkern g1="parenleft" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="72" />
<hkern g1="slash" 	g2="uni0409,uni041B" 	k="61" />
<hkern g1="slash" 	g2="uni043B,uni0459" 	k="119" />
<hkern g1="slash" 	g2="uni0443,uni045E" 	k="57" />
<hkern g1="slash" 	g2="uni0404,uni041E,uni0421" 	k="41" />
<hkern g1="slash" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="100" />
<hkern g1="slash" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="72" />
</font>
</defs></svg> 