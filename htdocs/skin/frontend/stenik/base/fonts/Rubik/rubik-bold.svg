<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="rubikbold" horiz-adv-x="1214" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="430" />
<glyph unicode="&#xfb01;" horiz-adv-x="1409" d="M33 838v176q0 23 15 37t38 14h154v61q0 205 118.5 297.5t339.5 92.5h543q23 0 37 -14.5t14 -37.5v-176q0 -20 -14 -35.5t-37 -15.5h-526q-66 0 -93.5 -29.5t-27.5 -91.5v-51h647q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5 v735h-344v-735q0 -20 -14.5 -35.5t-36.5 -15.5h-250q-23 0 -38 14.5t-15 36.5v735h-154q-23 0 -38 14.5t-15 37.5z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1466" d="M33 838v176q0 23 15 37t38 14h154v61q0 205 118.5 297.5t339.5 92.5h136q23 0 37 -14.5t14 -37.5v-176q0 -20 -14.5 -35.5t-36.5 -15.5h-119q-66 0 -93.5 -29.5t-27.5 -91.5v-51h401v399q0 23 15.5 37.5t36.5 14.5h251q23 0 37.5 -14.5t14.5 -37.5v-1413 q0 -20 -14.5 -35.5t-37.5 -15.5h-251q-20 0 -36 15.5t-16 35.5v735h-401v-735q0 -20 -14.5 -35.5t-36.5 -15.5h-250q-23 0 -38 14.5t-15 36.5v735h-154q-23 0 -38 14.5t-15 37.5z" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="430" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="430" />
<glyph unicode="&#x09;" horiz-adv-x="430" />
<glyph unicode="&#xa0;" horiz-adv-x="430" />
<glyph unicode="!" horiz-adv-x="604" d="M119 51v264q0 23 15.5 38.5t35.5 15.5h264q20 0 35.5 -15.5t15.5 -38.5v-264q0 -20 -14 -35.5t-37 -15.5h-264q-20 0 -35.5 15.5t-15.5 35.5zM125 516v866q0 23 15.5 37.5t37.5 14.5h248q23 0 38 -14.5t15 -37.5v-866q0 -23 -15 -38t-38 -15h-248q-22 0 -37.5 15.5 t-15.5 37.5z" />
<glyph unicode="&#x22;" horiz-adv-x="940" d="M82 1391q-2 29 11 46t38 17h240q25 0 38 -17.5t11 -45.5l-47 -430q-8 -57 -60 -58h-125q-51 1 -59 58zM520 1391q-2 29 11.5 46t39.5 17h238q27 0 40 -17.5t9 -45.5l-47 -430q-4 -57 -59 -58h-125q-52 1 -60 58z" />
<glyph unicode="#" horiz-adv-x="1402" d="M90 365v172q0 20 14.5 34.5t34.5 14.5h180l37 250h-163q-20 0 -35 14t-15 35v172q0 20 14.5 34.5t35.5 14.5h204l29 192q4 18 17.5 30.5t31.5 12.5h170q18 0 31.5 -13t11.5 -32l-29 -190h203l29 192q4 18 17.5 30.5t31.5 12.5h170q18 0 31.5 -13t11.5 -32l-29 -190h140 q20 0 34.5 -14.5t14.5 -34.5v-172q0 -20 -14.5 -34.5t-34.5 -14.5h-179l-36 -250h161q20 0 35 -14.5t15 -34.5v-172q0 -20 -14.5 -35t-35.5 -15h-202l-29 -192q-4 -18 -16.5 -30.5t-32.5 -12.5h-170q-18 0 -31.5 13t-13.5 32l28 190h-200l-29 -192q-4 -18 -16.5 -30.5 t-32.5 -12.5h-170q-19 0 -33 13t-12 32l29 190h-140q-20 0 -34.5 14.5t-14.5 35.5zM584 586h200l37 250h-200z" />
<glyph unicode="$" horiz-adv-x="1378" d="M63 393q0 18 13.5 30.5t32.5 12.5h272q27 0 41 -8t33 -29q63 -113 233 -112q111 0 171.5 33.5t60.5 93.5q0 43 -32 70.5t-98.5 49t-195.5 48.5q-248 49 -371 149t-123 287q0 166 119 281.5t326 144.5v143q0 23 15 37t36 14h178q23 0 37 -14t14 -37v-147 q135 -25 233.5 -87.5t151 -143.5t56.5 -156q0 -18 -12.5 -31.5t-30.5 -13.5h-287q-47 0 -70 36q-12 43 -64 73t-126 30q-86 0 -133 -31t-47 -90t61 -93t227 -69q193 -35 307.5 -87t168 -135t53.5 -210q0 -180 -132 -297t-356 -145v-144q0 -23 -14 -37t-37 -14h-178 q-21 0 -36 14.5t-15 36.5v144q-154 18 -261.5 76.5t-161.5 144.5t-59 182z" />
<glyph unicode="%" horiz-adv-x="1660" d="M72 1063q0 27 4 108q6 125 88 199t227 74t227 -74t91 -199q4 -82 4 -108q0 -25 -4 -86q-8 -123 -93.5 -194.5t-224.5 -71.5t-223 71.5t-92 194.5q-4 61 -4 86zM182 41q0 12 8 22l1012 1332q16 20 30.5 29.5t41.5 9.5h153q20 0 34 -11.5t14 -29.5q0 -12 -9 -23 l-1011 -1331q-16 -20 -30.5 -29.5t-41.5 -9.5h-154q-20 0 -33.5 11.5t-13.5 29.5zM299 1075q0 -55 2 -86q4 -43 25.5 -69.5t64.5 -26.5q45 0 66.5 27.5t23.5 68.5q4 61 4 86q0 27 -4 84q-2 43 -23.5 70.5t-66.5 27.5q-82 0 -90 -98q-2 -29 -2 -84zM948 346q0 25 4 107 q8 127 90 200.5t228 73.5q145 0 227 -73.5t88 -200.5q4 -82 4 -107t-4 -86q-8 -123 -92 -196.5t-223 -73.5q-140 0 -224.5 73.5t-93.5 196.5q-4 61 -4 86zM1178 358q0 -55 2 -86q2 -43 23.5 -70.5t66.5 -27.5q43 0 64.5 27.5t25.5 70.5q4 61 4 86q0 27 -4 84q-4 43 -25.5 70 t-64.5 27q-45 0 -66.5 -28t-23.5 -69q-2 -29 -2 -84z" />
<glyph unicode="&#x26;" horiz-adv-x="1529" d="M88 399q0 127 70.5 221.5t206.5 170.5q-78 82 -112 153.5t-34 147.5q0 98 51.5 181t153.5 132t248 49q133 0 230 -49t148.5 -130t51.5 -177q0 -117 -72 -204t-211 -165l234 -235q31 51 55.5 99t48.5 128q8 29 43 29h219q16 0 27.5 -11.5t11.5 -27.5q-2 -70 -67.5 -192 t-143.5 -218l230 -235q14 -14 14 -29q0 -16 -11.5 -26.5t-27.5 -10.5h-293q-33 0 -53 20l-88 84q-84 -66 -182.5 -95t-219.5 -29q-243 0 -385.5 111.5t-142.5 307.5zM428 416q0 -76 56.5 -122t131.5 -46q119 -1 203 71l-264 263q-127 -68 -127 -166zM545 1085 q0 -35 21.5 -72.5t72.5 -90.5q76 39 117 76.5t41 88.5t-36 84t-91 33q-51 0 -88 -33.5t-37 -85.5z" />
<glyph unicode="'" horiz-adv-x="501" d="M82 1391q-2 29 11 46t38 17h240q25 0 38 -17.5t11 -45.5l-47 -430q-8 -57 -60 -58h-125q-51 1 -59 58z" />
<glyph unicode="(" horiz-adv-x="794" d="M84 633q0 956 612 956q23 0 37.5 -14t14.5 -37v-184q0 -20 -14.5 -34.5t-35.5 -16.5q-160 -10 -223 -175t-63 -495t63.5 -496t222.5 -176q20 -2 35 -16.5t15 -34.5v-184q0 -23 -14.5 -37.5t-37.5 -14.5q-612 1 -612 959z" />
<glyph unicode=")" horiz-adv-x="794" d="M47 -90q0 20 14.5 34.5t34.5 16.5q162 10 224.5 175t62.5 497q0 330 -62.5 494.5t-224.5 175.5q-20 2 -34.5 16t-14.5 35v184q0 23 15.5 37t35.5 14q614 0 615 -956q0 -958 -615 -959q-20 0 -35.5 14.5t-15.5 37.5v184z" />
<glyph unicode="*" horiz-adv-x="911" d="M82 1155l59 182q4 16 21.5 24.5t34.5 4.5l16 -6l143 -90l-39 164l-2 16q0 16 13.5 30.5t31.5 14.5h191q18 0 31.5 -14.5t13.5 -30.5l-2 -16l-43 -166l145 92l17 6q4 2 10 2q16 0 29.5 -8t15.5 -23l61 -182l2 -12q0 -14 -9 -27.5t-23 -17.5l-15 -4l-172 -13l133 -108 q10 -10 11 -12q8 -16 8 -27q0 -23 -19 -35l-153 -115q-12 -6 -27 -6q-25 0 -37 19l-8 12l-65 160l-64 -160q-6 -10 -10 -12q-12 -18 -37 -19q-14 0 -27 6l-151 115q-18 12 -19 35q0 10 9 27q0 2 10 12l131 110l-170 11l-16 4q-16 4 -24.5 21.5t-4.5 35.5z" />
<glyph unicode="+" horiz-adv-x="1239" d="M70 541v157q0 23 14 38.5t37 15.5h360v358q0 23 15.5 37t35.5 14h177q23 0 38 -14t15 -37v-358h356q23 0 38 -15.5t15 -38.5v-157q0 -23 -15 -37.5t-38 -14.5h-356v-370q0 -23 -15.5 -37t-37.5 -14h-177q-20 0 -35.5 15t-15.5 36v370h-360q-20 0 -35.5 15.5t-15.5 36.5z " />
<glyph unicode="," horiz-adv-x="575" d="M66 -109l63 412q4 29 22.5 50.5t53.5 21.5h248q16 0 28.5 -12.5t12.5 -28.5q0 -12 -5 -23l-159 -407q-23 -57 -76 -58h-150q-18 0 -29.5 13.5t-8.5 31.5z" />
<glyph unicode="-" horiz-adv-x="974" d="M113 512v205q0 23 15 37t38 14h645q23 0 37 -14.5t14 -36.5v-205q0 -20 -14 -35.5t-37 -15.5h-645q-23 0 -38 14t-15 37z" />
<glyph unicode="." horiz-adv-x="587" d="M106 51v271q0 23 15.5 38t38.5 15h270q23 0 37 -15.5t14 -37.5v-271q0 -20 -14 -35.5t-37 -15.5h-270q-23 0 -38.5 14.5t-15.5 36.5z" />
<glyph unicode="/" horiz-adv-x="1103" d="M47 -129q0 10 4 20l692 1668q23 51 76 51h195q18 0 30.5 -13.5t12.5 -29.5q0 -10 -4 -21l-697 -1667q-20 -51 -75 -51h-191q-16 0 -29.5 13.5t-13.5 29.5z" />
<glyph unicode="0" horiz-adv-x="1400" d="M102 717q0 90 4 205q8 154 77 273.5t199 189t316 69.5t317.5 -69.5t200 -189.5t76.5 -273q4 -57 4 -205q0 -143 -4 -199q-14 -242 -161.5 -389t-432.5 -149q-282 2 -429.5 149t-162.5 389q-4 111 -4 199zM502 721q0 -133 2 -193q10 -240 194 -239q97 0 144 62.5t51 176.5 q4 59 4 193q0 133 -4 188q-4 111 -51 175.5t-144 64.5q-184 0 -194 -240q-2 -55 -2 -188z" />
<glyph unicode="1" horiz-adv-x="1032" d="M33 1008q0 29 22 45l475 366q20 14 48 15h270q23 0 38 -14.5t15 -37.5v-1331q0 -23 -15.5 -37t-37.5 -14h-281q-20 0 -35.5 15.5t-15.5 35.5v959l-274 -211q-16 -10 -31 -11q-25 0 -43 25l-127 164q-8 14 -8 31z" />
<glyph unicode="2" horiz-adv-x="1343" d="M84 51v154q0 61 51 102l191 187q195 147 294 229t151 148.5t52 125.5q0 152 -151 152q-80 0 -123 -48t-57 -120q-12 -47 -70 -47h-270q-18 0 -32 13.5t-14 29.5q4 135 75 244.5t198 174t293 64.5q172 0 297 -55t189.5 -155.5t64.5 -233.5q0 -147 -91.5 -266t-277.5 -258 l-170 -170h518q23 0 37 -14.5t14 -37.5v-219q0 -20 -14 -35.5t-37 -15.5h-1067q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="3" horiz-adv-x="1361" d="M63 381q0 18 13.5 30.5t32.5 12.5h282q25 0 39 -9t27 -30q39 -100 215 -100q100 0 155.5 47t55.5 127q0 154 -205 153h-229q-23 0 -38.5 15.5t-15.5 38.5v120q0 47 35 70l330 270h-565q-20 0 -36 15.5t-16 36.5v202q0 23 14.5 38.5t37.5 15.5h954q23 0 37 -15.5t14 -38.5 v-182q0 -43 -33 -68l-303 -276l15 -4q184 -27 292.5 -127t108.5 -289q0 -137 -79 -240.5t-217 -158.5t-314 -55q-201 0 -337 62t-200.5 154.5t-69.5 184.5z" />
<glyph unicode="4" horiz-adv-x="1409" d="M57 328v207q0 31 23 61l594 803q29 35 76 35h311q23 0 38 -14.5t15 -37.5v-788h180q25 0 39.5 -15.5t14.5 -35.5v-215q0 -20 -14.5 -36t-37.5 -16h-182v-225q0 -23 -15.5 -37t-37.5 -14h-266q-21 0 -36.5 15.5t-15.5 35.5v225h-634q-20 0 -36 15.5t-16 36.5zM422 580h330 v460z" />
<glyph unicode="5" horiz-adv-x="1333" d="M66 410q0 18 12 30.5t31 12.5h288q49 0 68 -39q29 -68 77 -98.5t120 -30.5q88 0 146 53t58 149q0 84 -57 139.5t-147 55.5q-51 0 -82 -13.5t-60 -35.5q-31 -25 -53 -25h-285q-18 0 -31.5 13.5t-13.5 31.5l76 719q2 29 21.5 45.5t46.5 16.5h833q23 0 37 -14.5t14 -37.5 v-204q0 -20 -14 -36t-37 -16h-596l-24 -252q49 35 110.5 52.5t159.5 17.5q131 0 245.5 -58.5t183.5 -165t69 -243.5q0 -143 -72 -257t-208 -178.5t-320 -64.5q-187 0 -321 59.5t-202.5 158t-72.5 215.5z" />
<glyph unicode="6" horiz-adv-x="1341" d="M66 494q0 176 174 419l358 480q29 41 74 41h286q18 0 32 -13.5t14 -31.5q0 -10 -11 -27l-274 -373q25 6 57 6q125 -6 243 -69.5t192.5 -173t74.5 -250.5q0 -143 -75.5 -263t-213 -189.5t-315.5 -69.5q-182 0 -321.5 64.5t-217 181t-77.5 268.5zM461 496 q0 -98 62.5 -153.5t154.5 -55.5q90 0 153.5 56t63.5 153q0 98 -62.5 153.5t-154.5 55.5t-154.5 -55.5t-62.5 -153.5z" />
<glyph unicode="7" horiz-adv-x="1185" d="M57 1165v215q0 23 15.5 38.5t38.5 15.5h968q23 0 38.5 -15.5t15.5 -38.5v-190q0 -37 -10.5 -63.5t-12.5 -30.5l-471 -1047q-23 -49 -74 -49h-278q-19 0 -31 13.5t-12 31.5q0 10 4 18l471 1049h-608q-23 0 -38.5 15.5t-15.5 37.5z" />
<glyph unicode="8" horiz-adv-x="1394" d="M90 432q0 106 54.5 193.5t142.5 134.5q-145 94 -146 282q0 182 140.5 297t414.5 115q272 0 410.5 -113.5t138.5 -296.5q0 -186 -139 -284q90 -43 144.5 -131t54.5 -199q0 -127 -68 -228.5t-205 -161.5t-336 -60q-198 0 -335.5 60t-204 162.5t-66.5 229.5zM473 430 q0 -82 63.5 -131t159.5 -49q97 0 159.5 49t62.5 131t-62.5 131t-159.5 49q-96 0 -159.5 -49t-63.5 -131zM500 1024q0 -70 54 -114t142 -44q86 2 141.5 46t55.5 112q0 70 -55.5 114t-141.5 44q-88 0 -142 -44t-54 -114z" />
<glyph unicode="9" horiz-adv-x="1323" d="M57 940q0 135 69 253t203 189.5t322 71.5q190 0 327.5 -65.5t208 -179t70.5 -255.5q0 -113 -46 -206t-138 -218l-37 -51l-321 -438q-29 -41 -74 -41h-289q-18 0 -30.5 13.5t-12.5 31.5q0 14 8 27l287 391q-27 -6 -55 -4q-125 8 -237.5 68.5t-183.5 168t-71 244.5z M449 942q0 -94 57 -149.5t149 -55.5q90 0 148.5 55.5t58.5 149.5t-59.5 149.5t-147.5 55.5q-90 0 -148 -55.5t-58 -149.5z" />
<glyph unicode=":" horiz-adv-x="604" d="M115 51v258q0 23 15 37t36 14h270q23 0 38 -14t15 -37v-258q0 -23 -15 -37t-38 -14h-270q-21 0 -36 15.5t-15 35.5zM115 768v258q0 23 15 37t36 14h270q23 0 38 -14t15 -37v-258q0 -23 -15 -37t-38 -14h-270q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode=";" horiz-adv-x="620" d="M76 -109l63 412q6 31 24.5 51.5t51.5 20.5h250q16 0 28.5 -12.5t12.5 -28.5q0 -2 -4 -23l-160 -407q-23 -57 -76 -58h-149q-19 0 -30 13.5t-11 31.5zM141 768v258q0 23 15.5 37t38.5 14h270q23 0 37 -14t14 -37v-258q0 -20 -14.5 -35.5t-36.5 -15.5h-270q-23 0 -38.5 14 t-15.5 37z" />
<glyph unicode="&#x3c;" horiz-adv-x="1062" d="M59 686v66q0 29 13.5 47t46.5 41l747 508q20 12 33 12q18 0 30.5 -11.5t12.5 -31.5v-232q0 -31 -15.5 -48t-41.5 -33l-436 -285l436 -287q29 -20 43 -35.5t14 -44.5v-233q0 -18 -12 -30.5t-31 -12.5q-13 0 -33 12l-747 508q-31 20 -45.5 40.5t-14.5 49.5z" />
<glyph unicode="=" horiz-adv-x="1159" d="M123 274v168q0 23 15.5 37.5t35.5 14.5h809q23 0 38 -14.5t15 -37.5v-168q0 -23 -15 -37t-38 -14h-809q-20 0 -35.5 15.5t-15.5 35.5zM123 786v168q0 23 15.5 37.5t35.5 14.5h809q23 0 38 -14.5t15 -37.5v-168q0 -23 -15 -37t-38 -14h-809q-20 0 -35.5 15.5t-15.5 35.5z " />
<glyph unicode="&#x3e;" horiz-adv-x="1062" d="M121 119v233q0 29 14.5 44.5t42.5 35.5l436 287l-436 285q-27 16 -42 33.5t-15 47.5v232q0 20 12 31.5t31 11.5q12 0 33 -12l747 -508q33 -23 46.5 -41.5t13.5 -46.5v-66q0 -29 -14.5 -49.5t-45.5 -40.5l-747 -508q-20 -12 -33 -12q-19 0 -31 12t-12 31z" />
<glyph unicode="?" horiz-adv-x="1234" d="M57 1010q4 111 72 213t201 166.5t317 64.5q188 0 306 -58.5t169.5 -142.5t51.5 -170q0 -104 -47.5 -182t-135.5 -174q-55 -61 -83.5 -98t-47.5 -80q-8 -18 -14 -51q-16 -31 -29.5 -43.5t-36.5 -12.5h-303q-16 0 -28.5 12.5t-12.5 28.5q0 29 2 43q14 84 64.5 151.5 t136.5 151.5q63 61 96 103.5t35 83.5q6 59 -38 96t-109 37q-152 0 -187 -145q-10 -25 -24.5 -37.5t-40.5 -12.5h-267q-20 0 -33.5 15.5t-13.5 40.5zM430 51v244q0 23 15.5 38t37.5 15h275q23 0 38 -15.5t15 -37.5v-244q0 -23 -15.5 -37t-37.5 -14h-275q-22 0 -37.5 14.5 t-15.5 36.5z" />
<glyph unicode="@" horiz-adv-x="1732" d="M94 557q0 123 6 174q16 178 105.5 326.5t253.5 237.5t395 89q401 0 583.5 -174t204.5 -479q4 -78 5 -125q0 -68 -5 -133q-8 -145 -98 -228t-217 -83q-92 0 -156.5 31.5t-95.5 70.5q-33 -53 -90 -88t-176 -35q-154 0 -241 113t-87 305q0 195 85 307.5t237 112.5 q74 0 122 -24.5t83 -69.5v26q0 23 15 37.5t36 14.5h110q23 0 37.5 -14.5t14.5 -37.5v-413q0 -63 28.5 -97t77.5 -34q55 0 80 37.5t25 93.5q2 35 2 120q0 68 -2 103q-10 160 -67.5 260t-180.5 149.5t-330 49.5q-250 0 -382 -122t-159 -337q-6 -51 -6 -164q0 -106 6 -166 q49 -457 568 -457q170 0 254 27t153 80q14 12 29.5 20.5t34.5 8.5h151q23 0 38 -14.5t13 -37.5q0 -14 -12 -30q-98 -115 -260 -186.5t-401 -71.5q-365 0 -559.5 176t-221.5 475q-6 59 -6 176zM694 559q0 -104 40 -158.5t116 -54.5q80 0 119 51.5t39 161.5q0 111 -39 163 t-119 52q-156 0 -156 -215z" />
<glyph unicode="A" horiz-adv-x="1474" d="M16 43q0 12 2 18l478 1313q20 59 82 60h319q61 0 82 -60l477 -1313q2 -6 2 -18q0 -16 -13 -29.5t-30 -13.5h-266q-49 0 -70 45l-73 199h-537l-74 -199q-20 -45 -69 -45h-267q-16 0 -29.5 13.5t-13.5 29.5zM555 551h365l-183 522z" />
<glyph unicode="B" horiz-adv-x="1447" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h633q260 0 387 -106.5t127 -303.5q0 -98 -52 -170t-116 -104q84 -37 141.5 -122t57.5 -192q0 -207 -136 -321.5t-390 -114.5h-652q-20 0 -35.5 15.5t-15.5 35.5zM524 276h275q80 0 124 46.5t44 113.5q0 70 -45 116t-123 46h-275 v-322zM524 866h256q76 0 117 41t41 107q0 63 -40 103t-118 40h-256v-291z" />
<glyph unicode="C" horiz-adv-x="1445" d="M94 715q0 127 2 184q10 266 179 410.5t458 144.5q182 0 326.5 -59.5t228.5 -172t88 -266.5q0 -18 -13 -30.5t-32 -12.5h-291q-29 0 -42 11.5t-23 42.5q-29 100 -87.5 140t-154.5 40q-231 0 -239 -258q-2 -55 -2 -170t2 -174q8 -258 239 -258q94 0 154.5 41t87.5 139 q8 31 22.5 42t42.5 11h291q18 0 31.5 -12t13.5 -31q-4 -154 -88 -266.5t-228.5 -171.5t-326.5 -59q-293 0 -460 141t-177 414q-2 55 -2 180z" />
<glyph unicode="D" horiz-adv-x="1472" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h539q303 0 471 -144.5t176 -415.5q2 -59 2 -157t-2 -156q-20 -561 -637 -561h-549q-20 0 -35.5 15.5t-15.5 35.5zM524 307h205q129 0 190.5 61.5t65.5 196.5q4 59 4 154q0 94 -4 151q-4 131 -70.5 193.5t-195.5 62.5h-195v-819z " />
<glyph unicode="E" horiz-adv-x="1314" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h967q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-647v-260h602q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-602v-266h664q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37t-37.5 -14h-984q-20 0 -35.5 15.5 t-15.5 35.5z" />
<glyph unicode="F" horiz-adv-x="1271" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h953q23 0 38 -15.5t15 -38.5v-217q0 -23 -15.5 -37t-37.5 -14h-627v-285h586q23 0 38 -15t15 -38v-217q0 -23 -15.5 -37t-37.5 -14h-586v-455q0 -20 -14.5 -35.5t-36.5 -15.5h-275q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="G" horiz-adv-x="1480" d="M94 723q0 129 2 188q8 258 179 400.5t464 142.5q197 0 343.5 -63.5t223 -163t80.5 -203.5q0 -18 -13 -31.5t-32 -13.5h-317q-20 0 -31.5 7t-21.5 22q-25 57 -79 98t-153 41q-238 0 -245 -246q-2 -57 -2 -176t2 -178q8 -260 249 -260q119 0 188.5 59.5t69.5 179.5v37h-182 q-23 0 -38 15.5t-15 37.5v160q0 23 15.5 38t37.5 15h520q23 0 38.5 -15t15.5 -38v-237q0 -174 -81 -300t-228.5 -192.5t-344.5 -66.5q-297 0 -465 144t-178 413q-2 57 -2 186z" />
<glyph unicode="H" horiz-adv-x="1531" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-485h484v485q0 23 15 38.5t38 15.5h278q23 0 38.5 -15.5t15.5 -38.5v-1329q0 -23 -15.5 -37t-38.5 -14h-278q-23 0 -38 14.5t-15 36.5v502h-484v-502q0 -23 -15 -37t-38 -14h-281q-22 0 -36.5 14.5 t-14.5 36.5z" />
<glyph unicode="I" horiz-adv-x="673" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h293q23 0 37.5 -14.5t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5h-293q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="J" horiz-adv-x="1380" d="M45 453q0 18 12.5 31.5t30.5 13.5h293q29 0 45 -13.5t27 -44.5q33 -153 198 -153h2q98 0 150.5 64.5t52.5 187.5v577h-657q-20 0 -36 15.5t-16 35.5v215q0 23 15.5 37.5t36.5 14.5h1001q23 0 38 -15.5t15 -38.5v-854q0 -174 -76.5 -298t-215 -186t-318.5 -62 q-160 0 -295 54t-218 161.5t-85 257.5z" />
<glyph unicode="K" horiz-adv-x="1368" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h275q23 0 37 -15.5t14 -38.5v-446l363 459q23 41 79 41h314q16 0 29.5 -13.5t13.5 -29.5t-8 -25l-478 -618l517 -680q8 -8 8 -25q0 -16 -13.5 -29.5t-31.5 -13.5h-322q-33 0 -53.5 13.5t-28.5 27.5l-389 500v-490 q0 -20 -14.5 -35.5t-36.5 -15.5h-275q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="L" horiz-adv-x="1228" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h287q23 0 37 -14.5t14 -37.5v-1060h615q23 0 38 -15.5t15 -38.5v-217q0 -23 -15.5 -37t-37.5 -14h-953q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="M" horiz-adv-x="1701" d="M139 51v1329q0 23 15.5 38.5t38.5 15.5h231q51 0 74 -45l352 -631l354 631q23 45 74 45h231q23 0 38.5 -15.5t15.5 -38.5v-1329q0 -23 -15.5 -37t-38.5 -14h-260q-20 0 -35.5 15.5t-15.5 35.5v746l-221 -406q-27 -47 -72 -47h-110q-41 0 -72 47l-219 406v-746 q0 -23 -15.5 -37t-37.5 -14h-258q-23 0 -38.5 14.5t-15.5 36.5z" />
<glyph unicode="N" horiz-adv-x="1476" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h232q47 0 70 -39l483 -764v749q0 23 14.5 38.5t36.5 15.5h260q23 0 37 -15.5t14 -38.5v-1327q0 -23 -14 -38t-37 -15h-231q-47 0 -70 39l-481 733v-721q0 -23 -15.5 -37t-37.5 -14h-261q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="O" horiz-adv-x="1464" d="M94 713q0 119 2 178q8 270 179 416.5t458 146.5q285 0 456 -146.5t179 -416.5q4 -119 4 -178q0 -61 -4 -176q-10 -274 -177 -415.5t-458 -141.5t-459 141t-178 416q-2 57 -2 176zM492 719q0 -111 2 -170q4 -133 67.5 -197.5t171.5 -64.5q107 0 170.5 64.5t67.5 197.5 q4 119 4 170t-4 166q-4 133 -67.5 197.5t-170.5 64.5q-109 0 -172 -64.5t-67 -197.5q-2 -57 -2 -166z" />
<glyph unicode="P" horiz-adv-x="1390" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h580q266 0 417.5 -121t151.5 -355q0 -233 -151.5 -348.5t-417.5 -115.5h-233v-443q0 -20 -14.5 -35.5t-37.5 -15.5h-295q-20 0 -35.5 15.5t-15.5 35.5zM530 786h230q84 0 132 44t48 131q0 80 -44 129t-136 49h-230v-353z" />
<glyph unicode="Q" horiz-adv-x="1464" d="M92 713q0 119 2 178q10 270 180 416.5t457 146.5t457 -146.5t180 -416.5q4 -119 4 -178q0 -61 -4 -176q-12 -250 -149 -392l151 -215q6 -12 6 -20v-6q0 -14 -12 -25.5t-29 -11.5h-282q-29 0 -46.5 13.5t-31.5 33.5l-70 90q-73 -24 -166 -24h-8q-291 0 -459 141t-178 416 q-2 57 -2 176zM492 719q0 -111 2 -170q4 -133 67.5 -197.5t169.5 -64.5q107 0 170.5 64.5t67.5 197.5q4 119 4 170t-4 166q-4 133 -67.5 197.5t-170.5 64.5q-106 0 -169.5 -64.5t-67.5 -197.5q-2 -57 -2 -166z" />
<glyph unicode="R" horiz-adv-x="1423" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h564q270 0 422.5 -123t152.5 -346q0 -143 -66.5 -243.5t-183.5 -156.5l277 -499q6 -12 6 -23q0 -16 -12.5 -29.5t-30.5 -13.5h-289q-59 0 -84 55l-225 445h-197v-449q0 -23 -15 -37t-38 -14h-281q-20 0 -35.5 15.5t-15.5 35.5z M524 801h228q88 0 134 44t46 122t-46 125t-134 47h-228v-338z" />
<glyph unicode="S" horiz-adv-x="1363" d="M57 393q0 18 13.5 30.5t31.5 12.5h273q25 0 40 -8t34 -29q27 -49 83 -80.5t150 -31.5q111 0 170 33.5t59 93.5q0 43 -30.5 70.5t-98 49t-194.5 48.5q-248 49 -371 149t-123 287q0 127 71 226t201 154.5t302 55.5q180 0 312 -62.5t202.5 -155.5t74.5 -183q0 -18 -12 -31.5 t-31 -13.5h-286q-45 0 -70 36q-12 43 -63.5 73t-126.5 30q-86 0 -133.5 -31t-47.5 -90t61.5 -93t229.5 -69q193 -35 306.5 -87t168 -135t54.5 -210q0 -139 -80 -241.5t-221.5 -156.5t-323.5 -54q-199 0 -338 57t-211 151.5t-76 204.5z" />
<glyph unicode="T" horiz-adv-x="1300" d="M41 1151v229q0 23 15.5 38.5t37.5 15.5h1112q23 0 38.5 -15.5t15.5 -38.5v-229q0 -23 -15.5 -37t-38.5 -14h-360v-1049q0 -23 -15.5 -37t-37.5 -14h-285q-23 0 -38 14.5t-15 36.5v1049h-361q-23 0 -38 14t-15 37z" />
<glyph unicode="U" horiz-adv-x="1490" d="M127 545v835q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-831q0 -127 60.5 -190.5t172.5 -63.5q113 0 173.5 64.5t60.5 189.5v831q0 23 15.5 38.5t37.5 15.5h279q23 0 38 -15.5t15 -38.5v-835q0 -283 -164 -424t-452 -141q-291 0 -456 141t-165 424z" />
<glyph unicode="V" horiz-adv-x="1427" d="M45 1391q0 16 13.5 29.5t31.5 13.5h268q31 0 50.5 -17.5t25.5 -38.5l279 -925l280 925q6 23 25.5 39.5t50.5 16.5h268q18 0 30.5 -13.5t12.5 -29.5q0 -10 -2 -19l-415 -1311q-6 -27 -28 -44t-56 -17h-330q-35 0 -56.5 17.5t-29.5 43.5l-414 1311z" />
<glyph unicode="W" horiz-adv-x="1705" d="M66 1391q0 16 13 29.5t30 13.5h268q59 0 65 -48l125 -774l142 449q16 55 67 55h154q51 0 67 -55l142 -447l127 772q4 25 18 36.5t45 11.5h268q18 0 30.5 -13.5t12.5 -29.5v-13l-219 -1310q-4 -29 -26.5 -48.5t-55.5 -19.5h-206q-33 0 -51.5 16.5t-24.5 36.5l-203 568 l-203 -568q-8 -20 -26.5 -36.5t-51.5 -16.5h-204q-35 0 -56.5 19.5t-25.5 48.5l-219 1310q-2 4 -2 13z" />
<glyph unicode="X" horiz-adv-x="1425" d="M20 43q0 12 7 25l446 663l-412 635q-6 12 -6 25q0 16 13.5 29.5t29.5 13.5h314q45 0 73 -45l232 -357l239 357q29 45 72 45h299q16 0 29.5 -13.5t13.5 -29.5q0 -12 -6 -25l-416 -631l451 -667q6 -12 6 -25q0 -16 -13.5 -29.5t-29.5 -13.5h-324q-45 0 -71 41l-262 381 l-256 -381q-27 -41 -72 -41h-314q-16 0 -29.5 13.5t-13.5 29.5z" />
<glyph unicode="Y" horiz-adv-x="1400" d="M29 1391q0 16 13 29.5t30 13.5h270q29 0 47.5 -14.5t26.5 -30.5l284 -515l285 515q8 16 26.5 30.5t47.5 14.5h270q16 0 29.5 -13.5t13.5 -29.5q0 -12 -6 -23l-473 -881v-436q0 -23 -15.5 -37t-37.5 -14h-281q-20 0 -35.5 15.5t-15.5 35.5v436l-473 881q-6 10 -6 23z" />
<glyph unicode="Z" horiz-adv-x="1341" d="M76 53v209q0 27 8 41t22 33l652 780h-611q-20 0 -35.5 15.5t-15.5 35.5v213q0 23 14.5 38.5t36.5 15.5h1043q23 0 38 -15.5t15 -38.5v-206q0 -39 -26 -70l-629 -782h626q23 0 38.5 -15.5t15.5 -38.5v-217q0 -23 -15.5 -37t-38.5 -14h-1085q-23 0 -38 15.5t-15 37.5z" />
<glyph unicode="[" horiz-adv-x="794" d="M123 -274v1812q0 23 15.5 37t35.5 14h518q23 0 37 -14t14 -37v-186q0 -20 -14 -36t-37 -16h-239v-1312h239q23 0 37 -15.5t14 -35.5v-211q0 -23 -14 -37.5t-37 -14.5h-518q-20 0 -35.5 14.5t-15.5 37.5z" />
<glyph unicode="\" horiz-adv-x="1101" d="M45 1567q0 16 13.5 29.5t29.5 13.5h195q53 0 75 -51l693 -1668q4 -10 4 -20q0 -16 -12.5 -29.5t-30.5 -13.5h-191q-30 0 -48.5 15.5t-27.5 35.5l-696 1667q-4 10 -4 21z" />
<glyph unicode="]" horiz-adv-x="794" d="M51 -63q0 23 15.5 37t37.5 14h238v1312h-238q-23 0 -38 14.5t-15 37.5v186q0 23 15.5 37t37.5 14h517q23 0 37 -14t14 -37v-1812q0 -23 -14.5 -37.5t-36.5 -14.5h-517q-22 0 -37.5 14.5t-15.5 37.5v211z" />
<glyph unicode="^" horiz-adv-x="956" d="M94 1235q0 16 15 31l213 225q18 20 34.5 27.5t36.5 7.5h170q23 0 38 -7.5t36 -27.5l211 -225q16 -16 16 -31q0 -31 -33 -31h-100q-45 0 -72 15l-180 98l-180 -98q-27 -14 -72 -15h-102q-31 0 -31 31z" />
<glyph unicode="_" horiz-adv-x="1503" d="M90 -100v202q0 23 15.5 37.5t37.5 14.5h1219q23 0 37 -14.5t14 -37.5v-202q0 -23 -14.5 -38.5t-36.5 -15.5h-1219q-22 0 -37.5 15.5t-15.5 38.5z" />
<glyph unicode="`" horiz-adv-x="827" d="M121 1499q0 18 10 29.5t29 11.5h280q27 0 41.5 -7t34.5 -32l184 -223q10 -10 11 -27q0 -33 -33 -32h-195q-24 0 -40.5 7t-36.5 23l-275 226q-10 10 -10 24z" />
<glyph unicode="a" d="M51 299q0 139 116 226t318 120l254 39v25q0 76 -30.5 110.5t-104.5 34.5q-41 0 -68.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -33.5 13t-11.5 32q2 55 55 123.5t162 117.5t268 49q254 0 377 -111.5t123 -301.5v-621q0 -20 -14.5 -35.5 t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5v72q-45 -63 -125 -103t-191 -40q-108 0 -197 41.5t-139.5 115.5t-50.5 162zM401 326q0 -47 40 -76t100 -29q90 0 146 59.5t56 172.5v24l-170 -28q-172 -33 -172 -123z" />
<glyph unicode="b" horiz-adv-x="1298" d="M117 51v1352q0 23 15 37t38 14h264q23 0 37 -14.5t14 -36.5v-436q112 118 294 118h3q197 0 311.5 -128t123.5 -351q2 -27 2 -74q0 -45 -2 -71q-8 -231 -122 -356t-313 -125q-206 0 -317 135v-64q0 -20 -14.5 -35.5t-36.5 -15.5h-244q-23 0 -38 14.5t-15 36.5zM483 541 q0 -49 2 -78q4 -86 47 -142.5t132 -56.5q92 0 132 54.5t46 150.5q2 20 2 63t-2 64q-6 96 -46 150.5t-132 54.5q-86 0 -130.5 -52.5t-48.5 -132.5q-2 -27 -2 -75z" />
<glyph unicode="c" d="M78 530l2 84q8 221 153.5 346t378.5 125q178 0 299 -63.5t180.5 -150.5t63.5 -158q2 -23 -14.5 -38.5t-38.5 -15.5h-275q-23 0 -35 10.5t-22 32.5q-23 57 -58.5 84t-90.5 27q-158 0 -164 -209l-2 -78l2 -67q2 -106 43 -156.5t121 -50.5q59 0 93 25.5t56 84.5 q10 23 22.5 33.5t34.5 10.5h275q20 0 36.5 -14.5t16.5 -35.5q0 -63 -56.5 -152t-179 -156.5t-307.5 -67.5q-233 0 -377.5 123.5t-154.5 345.5z" />
<glyph unicode="d" horiz-adv-x="1298" d="M80 532l2 74q8 223 122.5 351t311.5 128q184 0 297 -118v436q0 23 15.5 37t35.5 14h264q23 0 38.5 -14.5t15.5 -36.5v-1352q0 -23 -15.5 -37t-38.5 -14h-243q-20 0 -35.5 15.5t-15.5 35.5v64q-111 -135 -318 -135q-199 0 -312.5 124.5t-121.5 356.5zM455 532l2 -63 q6 -96 46 -150.5t132 -54.5q88 0 131 56.5t47 142.5q4 57 4 78q0 23 -4 75q-4 80 -48 132.5t-130 52.5q-92 0 -132 -54.5t-46 -150.5z" />
<glyph unicode="e" horiz-adv-x="1222" d="M76 504v63q8 244 151.5 381t386.5 137q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t116 -56.5q49 0 79 19.5t57 50.5q18 20 29 25t36 5h268q18 0 31.5 -11t13.5 -27q0 -53 -60 -125t-176 -123t-274 -51 q-249 0 -392.5 134t-147.5 390zM453 643h325v4q0 98 -43 152.5t-121 54.5t-119.5 -54.5t-41.5 -152.5v-4z" />
<glyph unicode="f" horiz-adv-x="917" d="M33 838v176q0 23 15 37t38 14h154v61q0 205 118.5 297.5t339.5 92.5h162q23 0 38 -14.5t15 -37.5v-176q0 -23 -15 -37t-38 -14h-145q-66 0 -93.5 -29.5t-27.5 -91.5v-51h246q23 0 38 -14.5t15 -36.5v-176q0 -23 -15.5 -37.5t-37.5 -14.5h-246v-735q0 -20 -14.5 -35.5 t-36.5 -15.5h-250q-23 0 -38 14.5t-15 36.5v735h-154q-23 0 -38 14.5t-15 37.5z" />
<glyph unicode="g" horiz-adv-x="1306" d="M78 553q0 250 111.5 391t326.5 141q104 0 184 -39.5t134 -107.5v74q0 23 14 38t37 15h250q23 0 38 -15.5t15 -37.5v-981q0 -236 -141.5 -359t-397.5 -123q-196 0 -319 63.5t-176.5 146.5t-53.5 139q0 20 16.5 34.5t37.5 14.5h274q20 0 32.5 -9.5t22.5 -31.5 q20 -45 53 -70.5t103 -25.5q88 0 131 44t43 146v137q-108 -112 -294 -112h-3q-207 0 -316.5 119.5t-119.5 353.5zM455 551q0 -117 38.5 -183.5t139.5 -66.5q88 0 131 53.5t49 134.5q2 14 2 62q0 49 -2 61q-6 84 -49 136.5t-131 52.5q-90 0 -131 -54.5t-45 -150.5z" />
<glyph unicode="h" horiz-adv-x="1339" d="M117 51v1352q0 23 15 37t38 14h276q23 0 38.5 -14.5t15.5 -36.5v-457q119 139 321 139q117 0 211 -53t147.5 -157.5t53.5 -251.5v-572q0 -23 -15.5 -37t-37.5 -14h-279q-20 0 -35.5 15.5t-15.5 35.5v559q0 90 -45 140.5t-129 50.5q-82 0 -129 -50.5t-47 -140.5v-559 q0 -23 -15.5 -37t-38.5 -14h-276q-23 0 -38 14.5t-15 36.5z" />
<glyph unicode="i" horiz-adv-x="593" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-963q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5zM117 1266v190q0 23 15 38t36 15h256q23 0 38 -15t15 -38v-190q0 -23 -15.5 -38.5t-37.5 -15.5h-256q-23 0 -37 15.5t-14 38.5z" />
<glyph unicode="j" horiz-adv-x="630" d="M-113 -162q0 20 14.5 35.5t37.5 15.5h81q68 0 94.5 37t26.5 107v981q0 23 15.5 37t36.5 14h268q23 0 37 -14.5t14 -36.5v-989q0 -199 -119 -306.5t-332 -107.5h-122q-23 0 -37.5 14.5t-14.5 36.5v176zM139 1266v190q0 23 15.5 38t38.5 15h268q23 0 38 -15t15 -38v-190 q0 -23 -15.5 -38.5t-37.5 -15.5h-268q-23 0 -38.5 15.5t-15.5 38.5z" />
<glyph unicode="k" horiz-adv-x="1210" d="M117 51v1352q0 23 15 37t38 14h250q23 0 37 -14.5t14 -36.5v-684l272 311q4 4 15.5 14.5t24 15.5t30.5 5h289q18 0 31.5 -13.5t13.5 -33.5q0 -18 -14 -33l-361 -397l412 -510q14 -18 14 -31q0 -20 -13.5 -33.5t-33.5 -13.5h-297q-27 0 -40 8t-32 27l-311 385v-369 q0 -20 -14.5 -35.5t-36.5 -15.5h-250q-23 0 -38 14.5t-15 36.5z" />
<glyph unicode="l" horiz-adv-x="593" d="M117 51v1352q0 23 15 37t38 14h256q23 0 37 -14.5t14 -36.5v-1352q0 -20 -14 -35.5t-37 -15.5h-256q-23 0 -38 14.5t-15 36.5z" />
<glyph unicode="m" horiz-adv-x="1880" d="M117 51v963q0 23 15 37t36 14h238q23 0 37 -14.5t14 -36.5v-70q49 59 127 100t176 41q225 0 309 -176q49 78 137 127t189 49q168 0 273.5 -113.5t105.5 -338.5v-582q0 -20 -14.5 -35.5t-37.5 -15.5h-252q-23 0 -38 14.5t-15 36.5v567q0 94 -40 138.5t-107 44.5 q-63 0 -103.5 -44t-40.5 -139v-567q0 -20 -14 -35.5t-37 -15.5h-252q-23 0 -38 14.5t-15 36.5v567q0 94 -41 138.5t-106 44.5q-63 0 -104.5 -44t-41.5 -136v-570q0 -20 -14 -35.5t-37 -15.5h-258q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="n" horiz-adv-x="1323" d="M117 51v963q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-78q55 68 142 108.5t200 40.5q117 0 208 -53t143.5 -157.5t52.5 -251.5v-572q0 -20 -14.5 -35.5t-37.5 -15.5h-272q-23 0 -38 14.5t-15 36.5v559q0 92 -44 141.5t-128 49.5q-82 0 -129 -50.5t-47 -140.5v-559 q0 -20 -14.5 -35.5t-37.5 -15.5h-272q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="o" horiz-adv-x="1257" d="M80 532q0 66 2 93q12 217 154.5 338.5t392.5 121.5t392 -121.5t155 -338.5q4 -53 4 -93q0 -39 -4 -92q-12 -221 -150.5 -340.5t-396.5 -119.5t-396.5 119.5t-150.5 340.5q-2 27 -2 92zM455 532l2 -81q6 -109 47 -161t125 -52t125 52t47 161q2 20 2 81t-2 82 q-6 106 -47 159.5t-125 53.5t-125 -53t-47 -160z" />
<glyph unicode="p" horiz-adv-x="1298" d="M117 -338v1352q0 23 15 37t38 14h244q23 0 37 -14.5t14 -36.5v-74q111 145 317 145q199 0 312.5 -123.5t122.5 -353.5q2 -27 2 -76t-2 -77q-8 -221 -123 -348t-312 -127q-199 0 -297 135v-453q0 -23 -14 -37t-37 -14h-264q-23 0 -38 14.5t-15 36.5zM483 524q0 -49 2 -75 q4 -80 48.5 -132.5t130.5 -52.5q92 0 132 54.5t46 150.5q2 20 2 63t-2 64q-6 96 -46 150.5t-132 54.5q-88 0 -131.5 -56.5t-47.5 -142.5q-2 -27 -2 -78z" />
<glyph unicode="q" horiz-adv-x="1298" d="M80 532l2 76q8 229 121.5 353t314.5 124q106 0 185 -38.5t131 -106.5v74q0 23 15 37t38 14h243q23 0 37.5 -14.5t14.5 -36.5v-1352q0 -23 -14.5 -37t-37.5 -14h-264q-22 0 -37.5 14.5t-15.5 36.5v453q-98 -135 -295 -135q-199 0 -313.5 126.5t-122.5 348.5zM457 532 q0 -43 2 -63q6 -96 45 -150.5t131 -54.5q86 0 130 52.5t48 132.5q4 53 4 75q0 25 -4 78q-4 88 -46 143.5t-132 55.5q-92 0 -131 -54.5t-45 -150.5q-2 -20 -2 -64z" />
<glyph unicode="r" horiz-adv-x="944" d="M117 51v961q0 23 14 38t37 15h250q23 0 38 -15.5t15 -37.5v-78q57 63 134 97t171 34h92q23 0 37.5 -14.5t14.5 -36.5v-223q0 -20 -14.5 -36t-37.5 -16h-206q-82 0 -126 -45t-44 -127v-516q0 -23 -15.5 -37t-38.5 -14h-270q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="s" horiz-adv-x="1140" d="M61 258q0 20 15.5 33.5t34.5 13.5h247q12 0 21 -8q35 -23 39 -27q39 -29 72.5 -44t80.5 -15q55 0 91 21.5t36 60.5q0 33 -19.5 53.5t-73.5 40t-165 39.5q-354 72 -354 328q0 84 54.5 160.5t160 123.5t252.5 47q150 0 259.5 -46t166.5 -110.5t57 -117.5q0 -18 -13 -32.5 t-32 -14.5h-225q-18 0 -29 8q-25 12 -49 31q-37 25 -64.5 38t-70.5 13q-51 0 -80 -22.5t-29 -57.5q0 -31 17.5 -50t72 -37.5t164.5 -39.5q201 -35 292 -124t91 -208q0 -154 -136 -244.5t-382 -90.5q-168 0 -280.5 47t-167 112.5t-54.5 118.5z" />
<glyph unicode="t" horiz-adv-x="970" d="M29 838v176q0 23 15 37t36 14h162v338q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-338h256q23 0 38 -14.5t15 -36.5v-176q0 -23 -15.5 -37.5t-37.5 -14.5h-256v-348q0 -72 26.5 -108.5t86.5 -36.5h161q23 0 37.5 -15.5t14.5 -37.5v-189q0 -20 -14.5 -35.5 t-37.5 -15.5h-194q-434 0 -434 408v378h-162q-20 0 -35.5 15.5t-15.5 36.5z" />
<glyph unicode="u" horiz-adv-x="1318" d="M106 442v572q0 23 15.5 37t36.5 14h272q23 0 38 -14.5t15 -36.5v-559q0 -190 170 -191q82 0 128 50.5t46 140.5v559q0 23 15.5 37t38.5 14h270q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5v78q-105 -149 -337 -149h-3 q-180 0 -291 120.5t-111 341.5z" />
<glyph unicode="v" horiz-adv-x="1226" d="M37 1018q0 20 13 33.5t34 13.5h244q43 0 61 -43l223 -612l226 612q6 16 20 29.5t39 13.5h246q18 0 32.5 -13.5t14.5 -33.5l-4 -17l-387 -950q-10 -25 -26.5 -38t-45.5 -13h-229q-29 0 -44.5 13.5t-25.5 37.5l-389 950q-2 6 -2 17z" />
<glyph unicode="w" horiz-adv-x="1722" d="M49 1018q0 20 14.5 33.5t32.5 13.5h223q25 0 41.5 -13.5t20.5 -29.5l168 -567l178 563q4 18 20.5 32.5t43.5 14.5h141q27 0 43 -14.5t22 -32.5l179 -563l165 567q6 16 21.5 29.5t40.5 13.5h223q20 0 33.5 -13.5t13.5 -33.5q0 -10 -2 -19l-297 -948q-14 -51 -65 -51h-195 q-51 0 -70 51l-182 545l-184 -545q-14 -51 -68 -51h-194q-29 0 -44.5 13.5t-23.5 37.5l-295 948z" />
<glyph unicode="x" d="M29 47q0 16 12 31l340 469l-309 440q-12 16 -13 31q0 20 14.5 33.5t32.5 13.5h265q25 0 37 -10.5t24 -26.5l180 -252l183 252q2 2 10 12.5t21.5 17.5t29.5 7h252q18 0 32.5 -13.5t14.5 -31.5q0 -16 -12 -33l-316 -440l347 -469q10 -14 10 -31q0 -20 -13.5 -33.5 t-33.5 -13.5h-277q-35 0 -57 33l-199 270l-201 -270q-12 -16 -24 -24.5t-35 -8.5h-268q-19 0 -33 13.5t-14 33.5z" />
<glyph unicode="y" horiz-adv-x="1230" d="M41 1018q4 20 17.5 33.5t31.5 13.5h244q43 0 59 -43l228 -582l233 582q20 43 61 43h242q18 0 31.5 -13.5t13.5 -29.5q0 -14 -4 -27l-580 -1341q-20 -43 -63 -43h-238q-18 0 -30.5 12t-12.5 31q0 14 5 27l161 385l-393 929q-6 16 -6 23z" />
<glyph unicode="z" horiz-adv-x="1140" d="M78 51v170q0 37 31 68l473 502h-437q-20 0 -35.5 15t-15.5 36v172q0 23 15.5 37t35.5 14h834q23 0 37 -14.5t14 -36.5v-185q0 -33 -29 -61l-458 -494h479q23 0 37 -14t14 -37v-172q0 -20 -14.5 -35.5t-36.5 -15.5h-893q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="{" horiz-adv-x="882" d="M16 594v78q0 31 13.5 49t44.5 35l26 12q111 55 111 229q0 193 72.5 325t199.5 198.5t291 68.5h12q20 0 32.5 -13t12.5 -32v-196q0 -18 -12 -30.5t-28 -14.5h-7q-129 -10 -186 -86t-57 -248q0 -127 -42 -216t-110 -120q66 -31 109 -123t43 -213q0 -170 56 -247t183 -87h11 q16 -2 28 -14t12 -31v-197q0 -20 -12 -32.5t-33 -12.5h-10q-164 2 -292 68t-200.5 199t-72.5 325q0 174 -111 232l-26 10q-31 16 -44.5 34.5t-13.5 49.5z" />
<glyph unicode="|" horiz-adv-x="548" d="M125 -375v2159q0 23 15.5 37t35.5 14h195q23 0 38 -14.5t15 -36.5v-2159q0 -23 -15.5 -37t-37.5 -14h-195q-20 0 -35.5 14.5t-15.5 36.5z" />
<glyph unicode="}" horiz-adv-x="882" d="M49 -82q0 18 12.5 30.5t28.5 14.5h8q129 10 186.5 87t57.5 247q0 127 42 216t110 120q-66 31 -109 123t-43 211q0 170 -57.5 246.5t-184.5 86.5l-10 3q-16 2 -28.5 14t-12.5 31v196q0 18 13.5 31.5t31.5 13.5h10q164 -2 292 -67.5t201 -197.5t73 -327q0 -174 112 -231 l27 -12q29 -16 42 -35t13 -47v-80q0 -29 -13 -47.5t-42 -34.5l-27 -12q-113 -55 -112 -230q0 -193 -73 -325.5t-199 -198.5t-289 -68h-15q-18 0 -31.5 12.5t-13.5 32.5v197z" />
<glyph unicode="~" horiz-adv-x="1134" d="M121 496v161q0 59 64.5 103.5t160.5 44.5q66 0 118 -11.5t116 -31.5q70 -20 112.5 -29.5t100.5 -9.5q45 0 79.5 16.5t65.5 40.5q2 2 12.5 8.5t22.5 6.5q25 0 34 -14.5t9 -47.5v-164q0 -59 -64.5 -102t-160.5 -43q-63 0 -113.5 10t-118.5 31q-59 18 -107.5 28.5 t-107.5 10.5q-72 0 -145 -55q-18 -14 -37 -17q-23 0 -32 15.5t-9 48.5z" />
<glyph unicode="&#xa1;" horiz-adv-x="589" d="M113 745v265q0 23 15 37t36 14h264q23 0 37 -14.5t14 -36.5v-265q0 -23 -15 -38t-36 -15h-264q-20 0 -35.5 15.5t-15.5 37.5zM119 -322v867q0 23 15.5 38t37.5 15h248q22 0 37.5 -15.5t15.5 -37.5v-867q0 -23 -15.5 -37t-37.5 -14h-248q-23 0 -38 14.5t-15 36.5z" />
<glyph unicode="&#xa2;" horiz-adv-x="1228" d="M80 530l2 84q6 180 104.5 297t262.5 154v133q0 23 15 37t38 14h213q23 0 38 -14t15 -37v-129q131 -27 218 -93.5t128 -141t41 -125.5q0 -20 -15.5 -35t-35.5 -15h-277q-20 0 -32.5 10.5t-22.5 32.5q-43 111 -156 111q-74 0 -115.5 -53t-43.5 -156l-2 -78l2 -67 q8 -207 159 -207q59 0 96.5 25.5t59.5 84.5q16 43 55 44h277q20 0 35.5 -14.5t15.5 -35.5q0 -49 -40 -123.5t-127 -142t-220 -94.5v-150q0 -23 -15.5 -37t-37.5 -14h-213q-23 0 -38 14.5t-15 36.5v152q-164 37 -261.5 153.5t-105.5 297.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1484" d="M86 37q4 127 64.5 220t162.5 136q-2 47 -18 164h-156q-20 0 -35.5 15.5t-15.5 35.5v164q0 23 15.5 37t35.5 14h97q-4 31 -5 97q0 260 146.5 398t423.5 138q172 0 304 -59.5t206.5 -168t77.5 -255.5q0 -18 -12.5 -31.5t-30.5 -13.5h-291q-53 0 -72 53q-23 86 -67 127 t-117 41q-170 0 -170 -231q0 -33 4 -95h299q23 0 37 -14t14 -37v-164q0 -20 -14.5 -35.5t-36.5 -15.5h-285q2 -16 6 -69.5t2 -100.5q59 -12 146 -53q49 -25 84 -36t78 -11q61 0 99 29.5t62 84.5q14 25 37 25h197q18 0 28.5 -11.5t10.5 -27.5q-2 -125 -56.5 -216t-139.5 -139 t-181 -48q-82 0 -148.5 20.5t-152.5 61.5q-72 31 -113 45t-88 14q-53 0 -84 -24.5t-49 -73.5q-8 -27 -35 -27h-225q-16 0 -27.5 10t-11.5 27z" />
<glyph unicode="&#xa4;" horiz-adv-x="1259" d="M102 248q0 23 15 37l92 92q-63 109 -64 237q0 129 64 238l-92 92q-14 14 -15 37q0 23 15 37l110 110q16 16 37 17q23 0 35 -17l94 -94q109 66 238 66q123 0 237 -66l93 94q16 16 36 17q20 0 37 -17l111 -110q14 -14 14 -37t-14 -37l-94 -92q63 -113 63 -238t-63 -237 l94 -92q14 -14 14 -37t-14 -37l-111 -111q-14 -14 -37 -14t-36 14l-93 95q-109 -66 -237 -66q-135 0 -238 66l-94 -95q-12 -14 -35 -14t-37 14l-110 111q-14 14 -15 37zM406 614q0 -94 65.5 -159.5t159.5 -65.5t159.5 65.5t65.5 159.5t-65.5 160t-159.5 66t-159.5 -65.5 t-65.5 -160.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1423" d="M55 1391q0 16 13.5 29.5t31.5 13.5h271q29 0 47 -14.5t26 -30.5l269 -496l266 496q8 16 26.5 30.5t47.5 14.5h272q16 0 29.5 -13.5t13.5 -29.5q0 -13 -6 -23l-412 -782h217q23 0 37.5 -14.5t14.5 -36.5v-170q0 -20 -14.5 -36t-37.5 -16h-262v-262q0 -23 -15.5 -37 t-37.5 -14h-281q-20 0 -35.5 15.5t-15.5 35.5v262h-262q-20 0 -35.5 15.5t-15.5 36.5v170q0 23 15.5 37t35.5 14h217l-414 782q-6 18 -6 23z" />
<glyph unicode="&#xa6;" horiz-adv-x="557" d="M129 51v623q0 23 15.5 37t37.5 14h195q23 0 37 -14.5t14 -36.5v-623q0 -20 -14.5 -35.5t-36.5 -15.5h-195q-22 0 -37.5 14.5t-15.5 36.5zM129 1024v623q0 23 15.5 37t37.5 14h195q23 0 37 -14.5t14 -36.5v-623q0 -20 -14.5 -35.5t-36.5 -15.5h-195q-22 0 -37.5 14 t-15.5 37z" />
<glyph unicode="&#xa7;" horiz-adv-x="1245" d="M68 629q0 164 145 276q-39 63 -39 158q0 106 54.5 195.5t160 142.5t252.5 53t257 -48t168 -118.5t62 -136.5v-4q0 -18 -13 -29.5t-32 -11.5h-237q-20 0 -31.5 7t-26.5 26q-20 33 -50.5 51t-92.5 18q-55 0 -86 -30.5t-31 -77.5q0 -37 17.5 -60.5t70 -46t156.5 -47.5 q223 -59 315.5 -152.5t92.5 -254.5q0 -84 -39 -154t-109 -123q41 -63 41 -158q0 -106 -54 -195t-160.5 -142.5t-254.5 -53.5q-147 0 -256.5 48.5t-168 120t-62.5 136.5v5q0 18 13 29t32 11h239q33 0 54 -30q20 -35 51 -54.5t94 -19.5q55 0 86 30.5t31 78.5q0 37 -17.5 60.5 t-69.5 46t-157 46.5q-223 59 -314 152.5t-91 255.5zM393 645q0 -53 38 -86t147 -67q147 -45 194 -62q82 22 82 92q0 55 -39 88t-143 64q-76 20 -197 61q-41 -8 -61.5 -32.5t-20.5 -57.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="952" d="M117 1286v197q0 20 12 33.5t33 13.5h196q20 0 34 -13.5t14 -33.5v-197q0 -20 -13.5 -32.5t-34.5 -12.5h-196q-21 0 -33 12.5t-12 32.5zM547 1286v197q0 20 13 33.5t34 13.5h194q20 0 34 -13.5t14 -33.5v-197q0 -20 -13.5 -32.5t-34.5 -12.5h-194q-21 0 -34 12.5t-13 32.5 z" />
<glyph unicode="&#xa9;" horiz-adv-x="1650" d="M88 717q0 201 99.5 369.5t268.5 268t369 99.5q201 0 370 -99.5t268.5 -268t99.5 -369.5t-99.5 -370t-268.5 -268t-370 -99q-200 0 -369 99t-268.5 268t-99.5 370zM266 717q0 -154 75 -285t203 -208t281 -77q154 0 282 77t202.5 208t74.5 285t-74.5 285t-202.5 207.5 t-282 76.5t-281.5 -76.5t-202.5 -208t-75 -284.5zM473 719l2 76q8 143 100.5 231t249.5 88q115 0 195 -38t120 -95t42 -117v-4q0 -18 -13.5 -29.5t-31.5 -11.5h-144q-20 0 -31.5 12.5t-19.5 30.5q-16 37 -42 53.5t-75 16.5q-66 0 -90 -40t-28 -104l-2 -69l2 -70 q4 -63 28.5 -103t89.5 -40q49 0 75 16.5t42 52.5q8 18 19.5 30.5t31.5 12.5h144q20 0 33.5 -12t11.5 -33q-2 -59 -42 -116.5t-120 -95t-195 -37.5q-157 0 -249.5 88t-100.5 231z" />
<glyph unicode="&#xaa;" horiz-adv-x="729" d="M70 1040q0 76 60 122t171 65l117 18q0 31 -10.5 46.5t-45.5 15.5q-29 0 -57 -21q-20 -16 -39 -16h-131q-12 0 -20.5 6t-8.5 18q0 29 30 64t88.5 60.5t142.5 25.5q129 0 194.5 -57.5t65.5 -167.5v-295q0 -14 -9.5 -24.5t-23.5 -10.5h-133q-14 0 -25.5 10t-11.5 25v22 q-20 -31 -62.5 -49t-91.5 -18q-88 0 -144 46t-56 115zM268 1053q0 -14 15.5 -23.5t40.5 -9.5q47 0 70.5 27.5t23.5 76.5l-70 -14q-80 -16 -80 -57z" />
<glyph unicode="&#xab;" horiz-adv-x="1273" d="M61 623v102q0 35 31 61l424 412q18 16 35 16q18 0 31.5 -13t13.5 -32v-202q0 -31 -8 -48.5t-29 -37.5l-211 -207l211 -207q20 -20 28.5 -37.5t8.5 -48.5v-203q0 -18 -13.5 -31.5t-31.5 -13.5q-17 1 -35 17l-424 411q-31 29 -31 62zM627 623v102q0 35 30 61l424 412 q18 16 37 16q18 0 30.5 -13t12.5 -32v-202q0 -31 -8 -48.5t-29 -37.5l-209 -207l209 -207q20 -20 28.5 -37.5t8.5 -48.5v-203q0 -18 -12 -31.5t-31 -13.5q-19 1 -37 17l-424 411q-31 29 -30 62z" />
<glyph unicode="&#xac;" horiz-adv-x="1167" d="M109 498v168q0 23 15 37t38 14h829q23 0 37 -14.5t14 -36.5v-426q0 -20 -14 -36t-37 -16h-172q-20 0 -35.5 15.5t-15.5 36.5v204h-606q-23 0 -38 15.5t-15 38.5z" />
<glyph unicode="&#xad;" horiz-adv-x="512" />
<glyph unicode="&#xae;" horiz-adv-x="1650" d="M88 717q0 201 99.5 369.5t268.5 268t369 99.5q201 0 370 -99.5t268.5 -268t99.5 -369.5t-99.5 -370t-268.5 -268t-370 -99q-200 0 -369 99t-268.5 268t-99.5 370zM266 717q0 -154 75 -285t203 -208t281 -77q154 0 282 77t202.5 208t74.5 285t-74.5 285t-202.5 207.5 t-282 76.5t-281.5 -76.5t-202.5 -208t-75 -284.5zM524 389v660q0 20 12.5 32.5t32.5 12.5h287q131 0 221 -60.5t90 -189.5q0 -78 -34.5 -131.5t-90.5 -79.5l109 -215q8 -16 8 -29q0 -18 -11 -31.5t-34 -13.5h-127q-41 0 -59 39l-94 209h-89v-203q0 -20 -12 -32.5t-33 -12.5 h-131q-20 0 -32.5 12.5t-12.5 32.5zM745 766h109q45 0 66.5 21.5t21.5 56.5t-21.5 56.5t-66.5 21.5h-109v-156z" />
<glyph unicode="&#xaf;" horiz-adv-x="907" d="M117 1284v152q0 20 12 32.5t33 12.5h583q20 0 33 -12.5t13 -32.5v-152q0 -20 -12.5 -33.5t-33.5 -13.5h-583q-21 0 -33 13.5t-12 33.5z" />
<glyph unicode="&#xb0;" horiz-adv-x="845" d="M61 1104q0 98 48.5 178t131.5 126t181 46t181 -46t131 -126t48 -178t-48 -178t-131 -126t-181 -46t-181 46t-131.5 126t-48.5 178zM266 1104q0 -70 43 -113t113 -43t113 43t43 113t-43 113t-113 43t-113 -43t-43 -113z" />
<glyph unicode="&#xb1;" horiz-adv-x="1165" d="M100 791v157q0 23 15.5 37t36.5 14h292v291q0 23 14.5 38t37.5 15h178q23 0 37 -15t14 -38v-291h289q23 0 37 -14t14 -37v-157q0 -23 -14.5 -38.5t-36.5 -15.5h-289v-299q0 -23 -14.5 -38t-36.5 -15h-178q-23 0 -37.5 15.5t-14.5 37.5v299h-292q-23 0 -37.5 15.5 t-14.5 38.5zM121 51v160q0 23 15.5 37t35.5 14h821q23 0 37 -14.5t14 -36.5v-160q0 -20 -14 -35.5t-37 -15.5h-821q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#xb2;" horiz-adv-x="733" d="M53 762v72q0 16 10.5 31.5t38.5 37.5q80 57 101 74q137 98 182 140t45 89q0 29 -17.5 47.5t-52.5 18.5q-39 0 -54 -15.5t-25 -39.5q-12 -23 -22.5 -30t-27.5 -7h-127q-37 0 -36 39q6 96 90 160.5t202 64.5q131 0 209 -62.5t78 -167.5q0 -88 -59.5 -155.5t-200.5 -163.5 h246q20 0 32.5 -12.5t12.5 -32.5v-88q0 -20 -12.5 -32.5t-32.5 -12.5h-535q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#xb3;" horiz-adv-x="727" d="M45 893v4q0 14 12.5 24.5t22.5 10.5h82q29 0 69 -8q12 -4 27.5 -16.5t33 -19.5t52.5 -7q45 0 70.5 17t25.5 46q0 33 -22.5 45t-69.5 12h-94q-18 0 -29.5 12.5t-11.5 30.5v52q0 23 20 43l117 108h-225q-20 0 -32.5 12.5t-12.5 32.5v97q0 20 12 32.5t33 12.5h461 q20 0 32.5 -12.5t12.5 -32.5v-84q0 -18 -5 -30.5t-24 -25.5l-127 -100q195 -33 195 -211q0 -104 -87 -167.5t-231 -63.5q-104 0 -171.5 29.5t-99.5 71.5t-36 85z" />
<glyph unicode="&#xb4;" horiz-adv-x="827" d="M117 1251q0 16 10 27l182 223q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23t-43.5 -7h-192q-33 -1 -33 32z" />
<glyph unicode="&#xb5;" horiz-adv-x="1353" d="M125 -356v1370q0 23 15.5 37t35.5 14h279q23 0 38 -14.5t15 -36.5v-559q0 -88 50 -139.5t124 -51.5t124 51.5t50 139.5v559q0 23 15.5 37t37.5 14h271q23 0 37 -14.5t14 -36.5v-963q0 -20 -14.5 -35.5t-36.5 -15.5h-250q-23 0 -38 14.5t-15 36.5v78q-82 -117 -232 -117 q-68 0 -137 35v-403q0 -23 -15.5 -37.5t-37.5 -14.5h-279q-20 0 -35.5 14.5t-15.5 37.5z" />
<glyph unicode="&#xb6;" horiz-adv-x="1302" d="M53 1044q0 106 52.5 195.5t141.5 142t195 52.5h672q23 0 37 -14.5t14 -37.5v-1536q0 -23 -14 -37t-37 -14h-176q-23 0 -38 14.5t-15 36.5v1344h-162v-1344q0 -23 -14.5 -37t-36.5 -14h-176q-23 0 -38.5 14.5t-15.5 36.5v807q-106 0 -195 52.5t-141.5 142.5t-52.5 196z " />
<glyph unicode="&#xb7;" horiz-adv-x="675" d="M78 682q0 70 34.5 130t95 95t130.5 35t130 -35t95 -95t35 -130t-35 -129t-95 -94t-130 -35t-130.5 35t-95 94t-34.5 129z" />
<glyph unicode="&#xb8;" horiz-adv-x="604" d="M117 -367q0 12 10 21l39 33q10 10 22 10q8 0 37 -13.5t64 -13.5q31 0 49 17.5t18 44.5q0 25 -18.5 41t-48.5 16q-18 0 -46 -7t-40 -7q-16 0 -29 12l-41 41q-12 12 -12 25q0 12 14 41l72 161h131l-80 -155q14 6 33.5 9t34.5 3q74 0 117.5 -50t43.5 -130t-53 -131.5 t-145 -51.5q-68 0 -120 28t-52 56z" />
<glyph unicode="&#xb9;" horiz-adv-x="563" d="M20 1217q0 20 19 34l219 168q20 14 47 15h129q20 0 32.5 -12.5t12.5 -32.5v-627q0 -20 -12 -32.5t-33 -12.5h-133q-20 0 -32.5 12t-12.5 33v420l-104 -78q-10 -8 -27 -8q-20 0 -37 18l-57 74q-10 14 -11 29z" />
<glyph unicode="&#xba;" horiz-adv-x="724" d="M76 1161l2 56q6 111 80.5 169t203.5 58t204 -58.5t81 -168.5q2 -10 2 -56q0 -45 -2 -55q-6 -111 -77.5 -169t-207.5 -58q-135 0 -206.5 58t-77.5 169zM289 1161l2 -45q4 -47 19.5 -66.5t51.5 -19.5q37 0 52.5 19.5t19.5 66.5q2 10 2 45t-2 45q-2 43 -18.5 64.5 t-53.5 21.5t-53 -21.5t-18 -64.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="1273" d="M113 178v203q0 31 8 48t29 38l210 207l-210 207q-20 20 -28.5 37.5t-8.5 48.5v202q0 18 13 31.5t32 13.5q16 0 35 -16l423 -412q31 -27 31 -61v-102q0 -33 -31 -62l-423 -411q-18 -16 -35 -17q-19 0 -32 13.5t-13 31.5zM680 178v203q0 31 8 48t29 38l209 207l-209 207 q-20 20 -28.5 37.5t-8.5 48.5v202q0 18 12.5 31.5t30.5 13.5t35 -16l426 -412q29 -27 30 -61v-102q-2 -33 -30 -62l-426 -411q-16 -16 -35 -17q-18 0 -30.5 13.5t-12.5 31.5z" />
<glyph unicode="&#xbc;" horiz-adv-x="1562" d="M20 1217q0 20 19 34l219 168q20 14 47 15h129q20 0 32.5 -12.5t12.5 -32.5v-627q0 -20 -12 -32.5t-33 -12.5h-133q-20 0 -32.5 12t-12.5 33v420l-104 -78q-10 -8 -27 -8q-20 0 -37 18l-57 74q-10 14 -11 29zM102 41q0 12 8 22l1010 1332q14 20 29.5 29.5t42.5 9.5h112 q20 0 33.5 -11.5t13.5 -29.5q0 -12 -8 -23l-1011 -1331q-16 -20 -30.5 -29.5t-41.5 -9.5h-113q-20 0 -32.5 11.5t-12.5 29.5zM809 158v100q0 29 25 61l268 359q27 37 61 37h170q20 0 32.5 -12.5t12.5 -32.5v-369h72q20 0 32.5 -12.5t12.5 -32.5v-98q0 -20 -12.5 -32.5 t-32.5 -12.5h-72v-70q0 -20 -12 -32.5t-33 -12.5h-127q-20 0 -32.5 12t-12.5 33v70h-307q-20 0 -32.5 12t-12.5 33zM1030 295h135v190z" />
<glyph unicode="&#xbd;" horiz-adv-x="1570" d="M20 1217q0 20 19 34l219 168q20 14 47 15h129q20 0 32.5 -12.5t12.5 -32.5v-627q0 -20 -12 -32.5t-33 -12.5h-133q-20 0 -32.5 12t-12.5 33v420l-104 -78q-10 -8 -27 -8q-20 0 -37 18l-57 74q-10 14 -11 29zM102 41q0 12 8 22l1010 1332q14 20 29.5 29.5t42.5 9.5h112 q20 0 33.5 -11.5t13.5 -29.5q0 -12 -8 -23l-1011 -1331q-16 -20 -30.5 -29.5t-41.5 -9.5h-113q-20 0 -32.5 11.5t-12.5 29.5zM872 45v72q0 16 10.5 31.5t38.5 37.5q80 57 101 74q137 98 182 140t45 89q0 29 -17.5 47.5t-52.5 18.5q-39 0 -54 -15.5t-25 -39.5 q-12 -23 -22.5 -30t-27.5 -7h-127q-37 0 -36 39q6 96 90 160.5t202 64.5q131 0 209 -62.5t78 -166.5q0 -88 -59.5 -156t-200.5 -164h246q20 0 32.5 -12.5t12.5 -32.5v-88q0 -20 -12.5 -32.5t-32.5 -12.5h-535q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#xbe;" horiz-adv-x="1691" d="M45 893v4q0 14 12.5 24.5t22.5 10.5h82q29 0 69 -8q12 -4 27.5 -16.5t33 -19.5t52.5 -7q45 0 70.5 17t25.5 46q0 33 -22.5 45t-69.5 12h-94q-18 0 -29.5 12.5t-11.5 30.5v52q0 23 20 43l117 108h-225q-20 0 -32.5 12.5t-12.5 32.5v97q0 20 12 32.5t33 12.5h461 q20 0 32.5 -12.5t12.5 -32.5v-84q0 -18 -5 -30.5t-24 -25.5l-127 -100q195 -33 195 -211q0 -104 -87 -167.5t-231 -63.5q-104 0 -171.5 29.5t-99.5 71.5t-36 85zM266 41q0 12 8 22l1010 1332q14 20 29.5 29.5t42.5 9.5h112q20 0 33.5 -11.5t13.5 -29.5q0 -12 -8 -23 l-1011 -1331q-16 -20 -30.5 -29.5t-41.5 -9.5h-113q-20 0 -32.5 11.5t-12.5 29.5zM979 156v100q0 29 25 61l268 359q27 37 61 37h170q20 0 32.5 -12.5t12.5 -32.5v-369h72q20 0 32.5 -12.5t12.5 -32.5v-98q0 -20 -12.5 -32.5t-32.5 -12.5h-72v-70q0 -20 -12 -32.5t-33 -12.5 h-127q-20 0 -32.5 12t-12.5 33v70h-307q-20 0 -32.5 12t-12.5 33zM1200 293h135v190z" />
<glyph unicode="&#xbf;" horiz-adv-x="1224" d="M55 -20q0 104 47.5 182t135.5 174q55 61 83.5 98t47.5 80q8 18 14 51q16 31 29.5 43.5t36.5 12.5h303q16 0 28.5 -12.5t12.5 -28.5q0 -29 -2 -43q-14 -84 -64.5 -152t-136.5 -152q-63 -61 -96 -103t-35 -83q-6 -59 38 -96t109 -37q152 0 187 145q10 25 24 37.5t41 12.5 h266q20 0 33.5 -15.5t13.5 -40.5q-4 -111 -71.5 -213t-200.5 -166.5t-317 -64.5q-188 0 -306 58.5t-169.5 142.5t-51.5 170zM418 768v244q0 23 15.5 37t37.5 14h274q23 0 38.5 -14.5t15.5 -36.5v-244q0 -23 -15.5 -38t-38.5 -15h-274q-23 0 -38 15t-15 38z" />
<glyph unicode="&#xc0;" horiz-adv-x="1474" d="M16 43q0 12 2 18l478 1313q20 59 82 60h319q61 0 82 -60l477 -1313q2 -6 2 -18q0 -16 -13 -29.5t-30 -13.5h-266q-49 0 -70 45l-73 199h-537l-74 -199q-20 -45 -69 -45h-267q-16 0 -29.5 13.5t-13.5 29.5zM301 1817q0 18 10 29.5t29 11.5h299q27 0 41 -7.5t35 -31.5 l184 -224q10 -10 10 -26q0 -33 -33 -33h-211q-26 0 -42.5 7t-36.5 24l-275 225q-10 10 -10 25zM555 551h365l-183 522z" />
<glyph unicode="&#xc1;" horiz-adv-x="1474" d="M16 43q0 12 2 18l478 1313q20 59 82 60h319q61 0 82 -60l477 -1313q2 -6 2 -18q0 -16 -13 -29.5t-30 -13.5h-266q-49 0 -70 45l-73 199h-537l-74 -199q-20 -45 -69 -45h-267q-16 0 -29.5 13.5t-13.5 29.5zM555 551h365l-183 522zM569 1569q0 16 11 26l182 224 q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#xc2;" horiz-adv-x="1474" d="M16 43q0 12 2 18l478 1313q20 59 82 60h319q61 0 82 -60l477 -1313q2 -6 2 -18q0 -16 -13 -29.5t-30 -13.5h-266q-49 0 -70 45l-73 199h-537l-74 -199q-20 -45 -69 -45h-267q-16 0 -29.5 13.5t-13.5 29.5zM346 1565q0 18 14 32l213 224q20 20 35.5 28.5t36.5 8.5h184 q20 0 35.5 -8.5t36.5 -28.5l213 -224q14 -14 14 -32q0 -29 -31 -29h-108q-45 0 -72 14l-180 99l-180 -99q-27 -14 -72 -14h-108q-31 0 -31 29zM555 551h365l-183 522z" />
<glyph unicode="&#xc3;" horiz-adv-x="1474" d="M16 43q0 12 2 18l478 1313q20 59 82 60h319q61 0 82 -60l477 -1313q2 -6 2 -18q0 -16 -13 -29.5t-30 -13.5h-266q-49 0 -70 45l-73 199h-537l-74 -199q-20 -45 -69 -45h-267q-16 0 -29.5 13.5t-13.5 29.5zM375 1571q0 53 23.5 115.5t76.5 107.5t135 45q43 0 74 -11 t67 -32q20 -10 43 -19.5t43 -9.5t32 8.5t24 24.5q10 14 18 21.5t23 7.5h129q16 0 26.5 -10.5t10.5 -26.5q0 -51 -25 -113.5t-78 -107.5t-135 -45q-41 0 -69.5 10t-69.5 31q-27 16 -46.5 23t-41.5 7q-20 0 -31.5 -8t-24.5 -24q-10 -14 -18 -21.5t-23 -7.5h-126 q-14 0 -25.5 10t-11.5 25zM555 551h365l-183 522z" />
<glyph unicode="&#xc4;" horiz-adv-x="1474" d="M16 43q0 12 2 18l478 1313q20 59 82 60h319q61 0 82 -60l477 -1313q2 -6 2 -18q0 -16 -13 -29.5t-30 -13.5h-266q-49 0 -70 45l-73 199h-537l-74 -199q-20 -45 -69 -45h-267q-16 0 -29.5 13.5t-13.5 29.5zM346 1583v195q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5 t13.5 -33.5v-195q0 -20 -13.5 -33.5t-33.5 -13.5h-197q-20 0 -32.5 13.5t-12.5 33.5zM555 551h365l-183 522zM837 1583v195q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-195q0 -20 -13 -33.5t-34 -13.5h-194q-21 0 -34.5 13.5t-13.5 33.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1474" d="M16 43q0 12 2 18l478 1313q20 59 82 60h319q61 0 82 -60l477 -1313q2 -6 2 -18q0 -16 -13 -29.5t-30 -13.5h-266q-49 0 -70 45l-73 199h-537l-74 -199q-20 -45 -69 -45h-267q-16 0 -29.5 13.5t-13.5 29.5zM546 1696q0 78 55.5 130t135.5 52t135 -52t55 -130t-55 -129 t-135 -51t-135.5 51t-55.5 129zM555 551h365l-183 522zM675 1696q0 -27 17.5 -44.5t44.5 -17.5q26 0 43.5 17.5t17.5 44.5t-17.5 44t-43.5 17q-27 0 -44.5 -17.5t-17.5 -43.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="2000" d="M8 43q0 4 4 20q100 287 212 558.5t343 754.5q8 25 28.5 41.5t51.5 16.5h1196q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-606v-260h561q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-561v-266h623q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37 t-37.5 -14h-942q-20 0 -36 15.5t-16 35.5v193h-383q-74 -141 -94 -199q-16 -45 -67 -45h-269q-18 0 -31.5 13.5t-13.5 29.5zM575 551h291v571h-45z" />
<glyph unicode="&#xc7;" horiz-adv-x="1445" d="M94 715q0 127 2 184q10 266 179 410.5t458 144.5q182 0 326.5 -59.5t228.5 -172t88 -266.5q0 -18 -13 -30.5t-32 -12.5h-291q-29 0 -42 11.5t-23 42.5q-29 100 -87.5 140t-154.5 40q-231 0 -239 -258q-2 -55 -2 -170t2 -174q8 -258 239 -258q94 0 154.5 41t87.5 139 q8 31 22.5 42t42.5 11h291q18 0 31.5 -12t13.5 -31q-4 -154 -88 -266.5t-228.5 -171.5t-326.5 -59l-39 -80q14 6 32.5 9t33.5 3q74 0 119 -50t45 -130t-54.5 -131.5t-146.5 -51.5q-66 0 -118 28t-52 56q0 12 10 21l37 33q10 10 23 10q8 0 36.5 -13.5t63.5 -13.5 q31 0 49.5 17.5t18.5 44.5q0 25 -18.5 41t-49.5 16q-16 0 -44 -7t-40 -7q-18 0 -31 12l-39 41q-12 12 -12 27q0 14 12 39l41 94q-240 29 -373 167t-141 380q-2 55 -2 180z" />
<glyph unicode="&#xc8;" horiz-adv-x="1314" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h967q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-647v-260h602q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-602v-266h664q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37t-37.5 -14h-984q-20 0 -35.5 15.5 t-15.5 35.5zM221 1817q0 18 10 29.5t29 11.5h299q27 0 41 -7.5t35 -31.5l184 -224q10 -10 10 -26q0 -33 -33 -33h-211q-26 0 -42.5 7t-36.5 24l-275 225q-10 10 -10 25z" />
<glyph unicode="&#xc9;" horiz-adv-x="1314" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h967q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-647v-260h602q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-602v-266h664q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37t-37.5 -14h-984q-20 0 -35.5 15.5 t-15.5 35.5zM489 1569q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#xca;" horiz-adv-x="1314" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h967q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-647v-260h602q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-602v-266h664q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37t-37.5 -14h-984q-20 0 -35.5 15.5 t-15.5 35.5zM266 1565q0 18 14 32l213 224q20 20 35.5 28.5t36.5 8.5h184q20 0 35.5 -8.5t36.5 -28.5l213 -224q14 -14 14 -32q0 -29 -31 -29h-108q-45 0 -72 14l-180 99l-180 -99q-27 -14 -72 -14h-108q-31 0 -31 29z" />
<glyph unicode="&#xcb;" horiz-adv-x="1314" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h967q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-647v-260h602q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-602v-266h664q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37t-37.5 -14h-984q-20 0 -35.5 15.5 t-15.5 35.5zM266 1583v195q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-195q0 -20 -13.5 -33.5t-33.5 -13.5h-197q-20 0 -32.5 13.5t-12.5 33.5zM757 1583v195q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-195q0 -20 -13 -33.5t-34 -13.5 h-194q-21 0 -34.5 13.5t-13.5 33.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="673" d="M-98 1817q0 18 10 29.5t29 11.5h299q27 0 41 -7.5t35 -31.5l184 -224q10 -10 10 -26q0 -33 -33 -33h-211q-26 0 -42.5 7t-36.5 24l-275 225q-10 10 -10 25zM139 51v1331q0 23 15.5 37.5t35.5 14.5h293q23 0 37.5 -14.5t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5 h-293q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#xcd;" horiz-adv-x="673" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h293q23 0 37.5 -14.5t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5h-293q-20 0 -35.5 15.5t-15.5 35.5zM170 1569q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225 q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#xce;" horiz-adv-x="673" d="M-53 1565q0 18 14 32l213 224q20 20 35.5 28.5t36.5 8.5h184q20 0 35.5 -8.5t36.5 -28.5l213 -224q14 -14 14 -32q0 -29 -31 -29h-108q-45 0 -72 14l-180 99l-180 -99q-27 -14 -72 -14h-108q-31 0 -31 29zM139 51v1331q0 23 15.5 37.5t35.5 14.5h293q23 0 37.5 -14.5 t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5h-293q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#xcf;" horiz-adv-x="673" d="M-53 1583v195q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-195q0 -20 -13.5 -33.5t-33.5 -13.5h-197q-20 0 -32.5 13.5t-12.5 33.5zM139 51v1331q0 23 15.5 37.5t35.5 14.5h293q23 0 37.5 -14.5t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5h-293 q-20 0 -35.5 15.5t-15.5 35.5zM438 1583v195q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-195q0 -20 -13 -33.5t-34 -13.5h-194q-21 0 -34.5 13.5t-13.5 33.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1511" d="M29 645v150q0 23 15 37t38 14h94v534q0 23 14.5 38.5t36.5 15.5h539q303 0 474 -144.5t173 -415.5q2 -55 2 -155q0 -102 -2 -158q-2 -283 -168 -422t-473 -139h-545q-20 0 -35.5 15.5t-15.5 35.5v543h-94q-23 0 -38 14.5t-15 36.5zM561 307h205q129 0 188.5 61.5 t61.5 196.5v305q-2 131 -63.5 193.5t-188.5 62.5h-203v-280h178q23 0 37.5 -14.5t14.5 -36.5v-150q0 -20 -14.5 -35.5t-37.5 -15.5h-178v-287z" />
<glyph unicode="&#xd1;" horiz-adv-x="1476" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h232q47 0 70 -39l483 -764v749q0 23 14.5 38.5t36.5 15.5h260q23 0 37 -15.5t14 -38.5v-1327q0 -23 -14 -38t-37 -15h-231q-47 0 -70 39l-481 733v-721q0 -23 -15.5 -37t-37.5 -14h-261q-20 0 -35.5 15.5t-15.5 35.5zM377 1571 q0 53 23.5 115.5t76.5 107.5t135 45q43 0 74 -11t67 -32q20 -10 43 -19.5t43 -9.5t32 8.5t24 24.5q10 14 18 21.5t23 7.5h129q16 0 26.5 -10.5t10.5 -26.5q0 -51 -25 -113.5t-78 -107.5t-135 -45q-41 0 -69.5 10t-69.5 31q-27 16 -46.5 23t-41.5 7q-20 0 -31.5 -8t-24.5 -24 q-10 -14 -18 -21.5t-23 -7.5h-126q-14 0 -25.5 10t-11.5 25z" />
<glyph unicode="&#xd2;" horiz-adv-x="1464" d="M94 713q0 119 2 178q8 270 179 416.5t458 146.5q285 0 456 -146.5t179 -416.5q4 -119 4 -178q0 -61 -4 -176q-10 -274 -177 -415.5t-458 -141.5t-459 141t-178 416q-2 57 -2 176zM297 1817q0 18 10 29.5t29 11.5h299q27 0 41 -7.5t35 -31.5l184 -224q10 -10 10 -26 q0 -33 -33 -33h-211q-26 0 -42.5 7t-36.5 24l-275 225q-10 10 -10 25zM492 719q0 -111 2 -170q4 -133 67.5 -197.5t171.5 -64.5q107 0 170.5 64.5t67.5 197.5q4 119 4 170t-4 166q-4 133 -67.5 197.5t-170.5 64.5q-109 0 -172 -64.5t-67 -197.5q-2 -57 -2 -166z" />
<glyph unicode="&#xd3;" horiz-adv-x="1464" d="M94 713q0 119 2 178q8 270 179 416.5t458 146.5q285 0 456 -146.5t179 -416.5q4 -119 4 -178q0 -61 -4 -176q-10 -274 -177 -415.5t-458 -141.5t-459 141t-178 416q-2 57 -2 176zM492 719q0 -111 2 -170q4 -133 67.5 -197.5t171.5 -64.5q107 0 170.5 64.5t67.5 197.5 q4 119 4 170t-4 166q-4 133 -67.5 197.5t-170.5 64.5q-109 0 -172 -64.5t-67 -197.5q-2 -57 -2 -166zM565 1569q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#xd4;" horiz-adv-x="1464" d="M94 713q0 119 2 178q8 270 179 416.5t458 146.5q285 0 456 -146.5t179 -416.5q4 -119 4 -178q0 -61 -4 -176q-10 -274 -177 -415.5t-458 -141.5t-459 141t-178 416q-2 57 -2 176zM342 1565q0 18 14 32l213 224q20 20 35.5 28.5t36.5 8.5h184q20 0 35.5 -8.5t36.5 -28.5 l213 -224q14 -14 14 -32q0 -29 -31 -29h-108q-45 0 -72 14l-180 99l-180 -99q-27 -14 -72 -14h-108q-31 0 -31 29zM492 719q0 -111 2 -170q4 -133 67.5 -197.5t171.5 -64.5q107 0 170.5 64.5t67.5 197.5q4 119 4 170t-4 166q-4 133 -67.5 197.5t-170.5 64.5 q-109 0 -172 -64.5t-67 -197.5q-2 -57 -2 -166z" />
<glyph unicode="&#xd5;" horiz-adv-x="1464" d="M94 713q0 119 2 178q8 270 179 416.5t458 146.5q285 0 456 -146.5t179 -416.5q4 -119 4 -178q0 -61 -4 -176q-10 -274 -177 -415.5t-458 -141.5t-459 141t-178 416q-2 57 -2 176zM371 1571q0 53 23.5 115.5t76.5 107.5t135 45q43 0 74 -11t67 -32q20 -10 43 -19.5 t43 -9.5t32 8.5t24 24.5q10 14 18 21.5t23 7.5h129q16 0 26.5 -10.5t10.5 -26.5q0 -51 -25 -113.5t-78 -107.5t-135 -45q-41 0 -69.5 10t-69.5 31q-27 16 -46.5 23t-41.5 7q-20 0 -31.5 -8t-24.5 -24q-10 -14 -18 -21.5t-23 -7.5h-126q-14 0 -25.5 10t-11.5 25zM492 719 q0 -111 2 -170q4 -133 67.5 -197.5t171.5 -64.5q107 0 170.5 64.5t67.5 197.5q4 119 4 170t-4 166q-4 133 -67.5 197.5t-170.5 64.5q-109 0 -172 -64.5t-67 -197.5q-2 -57 -2 -166z" />
<glyph unicode="&#xd6;" horiz-adv-x="1464" d="M94 713q0 119 2 178q8 270 179 416.5t458 146.5q285 0 456 -146.5t179 -416.5q4 -119 4 -178q0 -61 -4 -176q-10 -274 -177 -415.5t-458 -141.5t-459 141t-178 416q-2 57 -2 176zM342 1583v195q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-195 q0 -20 -13.5 -33.5t-33.5 -13.5h-197q-20 0 -32.5 13.5t-12.5 33.5zM492 719q0 -111 2 -170q4 -133 67.5 -197.5t171.5 -64.5q107 0 170.5 64.5t67.5 197.5q4 119 4 170t-4 166q-4 133 -67.5 197.5t-170.5 64.5q-109 0 -172 -64.5t-67 -197.5q-2 -57 -2 -166zM833 1583v195 q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-195q0 -20 -13 -33.5t-34 -13.5h-194q-21 0 -34.5 13.5t-13.5 33.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="1241" d="M92 375q0 23 14 37l271 270l-271 270q-14 14 -14 37q0 20 14 35l173 172q16 16 36 16t37 -16l269 -270l270 270q14 14 37 14q20 0 35 -14l172 -172q16 -16 16 -37q0 -20 -16 -37l-269 -268l269 -268q16 -16 16 -37q0 -20 -16 -37l-172 -172q-14 -14 -35 -14q-23 0 -37 14 l-270 268l-269 -270q-18 -14 -37 -14q-18 0 -36 14l-173 172q-14 16 -14 37z" />
<glyph unicode="&#xd8;" horiz-adv-x="1529" d="M25 369v161q0 27 10 39.5t35 22.5l61 24l-2 115q0 53 4 160q10 270 180 416.5t457 146.5q221 0 374.5 -92t215.5 -266l84 32q16 6 22 7q18 0 30.5 -11.5t12.5 -29.5v-160q0 -27 -10 -39t-35 -23l-57 -22q2 -45 2 -135q0 -121 -2 -178q-10 -274 -178 -415.5t-459 -141.5 q-240 0 -394.5 96t-209.5 286l-76 -28q-16 -6 -24 -6q-18 0 -29.5 11t-11.5 30zM528 772l469 182q-18 98 -77.5 145.5t-149.5 47.5q-106 0 -169.5 -64.5t-68.5 -197.5q-4 -41 -4 -113zM535 508q14 -113 74.5 -167t160.5 -54q107 0 170.5 64.5t67.5 197.5q4 100 2 145z" />
<glyph unicode="&#xd9;" horiz-adv-x="1490" d="M127 545v835q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-831q0 -127 60.5 -190.5t172.5 -63.5q113 0 173.5 64.5t60.5 189.5v831q0 23 15.5 38.5t37.5 15.5h279q23 0 38 -15.5t15 -38.5v-835q0 -283 -164 -424t-452 -141q-291 0 -456 141t-165 424zM309 1817 q0 18 10 29.5t29 11.5h299q27 0 41 -7.5t35 -31.5l184 -224q10 -10 10 -26q0 -33 -33 -33h-211q-26 0 -42.5 7t-36.5 24l-275 225q-10 10 -10 25z" />
<glyph unicode="&#xda;" horiz-adv-x="1490" d="M127 545v835q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-831q0 -127 60.5 -190.5t172.5 -63.5q113 0 173.5 64.5t60.5 189.5v831q0 23 15.5 38.5t37.5 15.5h279q23 0 38 -15.5t15 -38.5v-835q0 -283 -164 -424t-452 -141q-291 0 -456 141t-165 424zM577 1569 q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#xdb;" horiz-adv-x="1490" d="M127 545v835q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-831q0 -127 60.5 -190.5t172.5 -63.5q113 0 173.5 64.5t60.5 189.5v831q0 23 15.5 38.5t37.5 15.5h279q23 0 38 -15.5t15 -38.5v-835q0 -283 -164 -424t-452 -141q-291 0 -456 141t-165 424zM355 1565 q0 18 14 32l213 224q20 20 35.5 28.5t36.5 8.5h184q20 0 35.5 -8.5t36.5 -28.5l213 -224q14 -14 14 -32q0 -29 -31 -29h-108q-45 0 -72 14l-180 99l-180 -99q-27 -14 -72 -14h-108q-31 0 -31 29z" />
<glyph unicode="&#xdc;" horiz-adv-x="1490" d="M127 545v835q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-831q0 -127 60.5 -190.5t172.5 -63.5q113 0 173.5 64.5t60.5 189.5v831q0 23 15.5 38.5t37.5 15.5h279q23 0 38 -15.5t15 -38.5v-835q0 -283 -164 -424t-452 -141q-291 0 -456 141t-165 424zM354 1583 v195q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-195q0 -20 -13.5 -33.5t-33.5 -13.5h-197q-20 0 -32.5 13.5t-12.5 33.5zM845 1583v195q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-195q0 -20 -13 -33.5t-34 -13.5h-194q-21 0 -34.5 13.5 t-13.5 33.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1400" d="M29 1391q0 16 13 29.5t30 13.5h270q29 0 47.5 -14.5t26.5 -30.5l284 -515l285 515q8 16 26.5 30.5t47.5 14.5h270q16 0 29.5 -13.5t13.5 -29.5q0 -12 -6 -23l-473 -881v-436q0 -23 -15.5 -37t-37.5 -14h-281q-20 0 -35.5 15.5t-15.5 35.5v436l-473 881q-6 10 -6 23z M532 1569q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#xde;" horiz-adv-x="1394" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h295q23 0 37.5 -14.5t14.5 -37.5v-182h227q270 0 422.5 -121t152.5 -354t-152.5 -349t-422.5 -116h-227v-209q0 -20 -14.5 -35.5t-37.5 -15.5h-295q-20 0 -35.5 15.5t-15.5 35.5zM530 553h230q84 0 132 44t48 130q0 80 -44 129 t-136 49h-230v-352z" />
<glyph unicode="&#xdf;" horiz-adv-x="1318" d="M117 51v910q0 217 135 354t405 139q180 0 299 -57.5t174.5 -149.5t55.5 -200q0 -180 -142 -277q203 -84 203 -338q0 -106 -58.5 -205.5t-177 -163t-294.5 -63.5h-99q-29 0 -45 14.5t-16 36.5v166q0 23 16.5 38t44.5 13h48q92 0 147 50.5t55 130.5t-54 129t-155 49h-47 q-27 0 -41 14t-14 39v143q0 51 55 51h41q88 2 138.5 42t50.5 112q0 68 -50.5 112t-134.5 44q-86 0 -134 -53.5t-48 -163.5v-916q0 -20 -14.5 -35.5t-36.5 -15.5h-254q-23 0 -38 14.5t-15 36.5z" />
<glyph unicode="&#xe0;" d="M51 299q0 139 116 226t318 120l254 39v25q0 76 -30.5 110.5t-104.5 34.5q-41 0 -68.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -33.5 13t-11.5 32q2 55 55 123.5t162 117.5t268 49q254 0 377 -111.5t123 -301.5v-621q0 -20 -14.5 -35.5 t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5v72q-45 -63 -125 -103t-191 -40q-108 0 -197 41.5t-139.5 115.5t-50.5 162zM172 1499q0 18 10 29.5t29 11.5h280q27 0 41.5 -7t34.5 -32l184 -223q10 -10 11 -27q0 -33 -33 -32h-195q-24 0 -40.5 7t-36.5 23l-275 226 q-10 10 -10 24zM401 326q0 -47 40 -76t100 -29q90 0 146 59.5t56 172.5v24l-170 -28q-172 -33 -172 -123z" />
<glyph unicode="&#xe1;" d="M51 299q0 139 116 226t318 120l254 39v25q0 76 -30.5 110.5t-104.5 34.5q-41 0 -68.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -33.5 13t-11.5 32q2 55 55 123.5t162 117.5t268 49q254 0 377 -111.5t123 -301.5v-621q0 -20 -14.5 -35.5 t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5v72q-45 -63 -125 -103t-191 -40q-108 0 -197 41.5t-139.5 115.5t-50.5 162zM401 326q0 -47 40 -76t100 -29q90 0 146 59.5t56 172.5v24l-170 -28q-172 -33 -172 -123zM439 1251q0 16 10 27l182 223q20 25 35.5 32t40.5 7h283 q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23t-43.5 -7h-192q-33 -1 -33 32z" />
<glyph unicode="&#xe2;" d="M51 299q0 139 116 226t318 120l254 39v25q0 76 -30.5 110.5t-104.5 34.5q-41 0 -68.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -33.5 13t-11.5 32q2 55 55 123.5t162 117.5t268 49q254 0 377 -111.5t123 -301.5v-621q0 -20 -14.5 -35.5 t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5v72q-45 -63 -125 -103t-191 -40q-108 0 -197 41.5t-139.5 115.5t-50.5 162zM215 1247q0 18 14 33l213 223q20 20 35.5 28.5t36.5 8.5h170q23 0 38 -7t35 -30l211 -223q16 -16 17 -33q0 -29 -33 -28h-100q-45 0 -72 14l-180 98 l-180 -98q-27 -14 -72 -14h-103q-30 -1 -30 28zM401 326q0 -47 40 -76t100 -29q90 0 146 59.5t56 172.5v24l-170 -28q-172 -33 -172 -123z" />
<glyph unicode="&#xe3;" d="M51 299q0 139 116 226t318 120l254 39v25q0 76 -30.5 110.5t-104.5 34.5q-41 0 -68.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -33.5 13t-11.5 32q2 55 55 123.5t162 117.5t268 49q254 0 377 -111.5t123 -301.5v-621q0 -20 -14.5 -35.5 t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5v72q-45 -63 -125 -103t-191 -40q-108 0 -197 41.5t-139.5 115.5t-50.5 162zM242 1253q0 53 23.5 115t75.5 106t134 44q41 0 70 -10.5t67 -30.5q29 -16 46.5 -23.5t39.5 -7.5q20 0 31.5 8t24.5 27q10 14 18 21t23 7h129 q16 0 26.5 -11t10.5 -25q0 -53 -25 -115t-77 -106t-134 -44q-41 0 -67.5 10.5t-69.5 30.5q-49 31 -86 31q-20 0 -31.5 -8t-23.5 -25q-10 -14 -18.5 -21t-22.5 -7h-127q-14 0 -25.5 10t-11.5 24zM401 326q0 -47 40 -76t100 -29q90 0 146 59.5t56 172.5v24l-170 -28 q-172 -33 -172 -123z" />
<glyph unicode="&#xe4;" d="M51 299q0 139 116 226t318 120l254 39v25q0 76 -30.5 110.5t-104.5 34.5q-41 0 -68.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -33.5 13t-11.5 32q2 55 55 123.5t162 117.5t268 49q254 0 377 -111.5t123 -301.5v-621q0 -20 -14.5 -35.5 t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5v72q-45 -63 -125 -103t-191 -40q-108 0 -197 41.5t-139.5 115.5t-50.5 162zM244 1286v197q0 20 12 33.5t33 13.5h196q20 0 34 -13.5t14 -33.5v-197q0 -20 -13.5 -32.5t-34.5 -12.5h-196q-21 0 -33 12.5t-12 32.5zM401 326 q0 -47 40 -76t100 -29q90 0 146 59.5t56 172.5v24l-170 -28q-172 -33 -172 -123zM674 1286v197q0 20 13 33.5t34 13.5h194q20 0 34 -13.5t14 -33.5v-197q0 -20 -13.5 -32.5t-34.5 -12.5h-194q-21 0 -34 12.5t-13 32.5z" />
<glyph unicode="&#xe5;" d="M51 299q0 139 116 226t318 120l254 39v25q0 76 -30.5 110.5t-104.5 34.5q-41 0 -68.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -33.5 13t-11.5 32q2 55 55 123.5t162 117.5t268 49q254 0 377 -111.5t123 -301.5v-621q0 -20 -14.5 -35.5 t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5v72q-45 -63 -125 -103t-191 -40q-108 0 -197 41.5t-139.5 115.5t-50.5 162zM401 326q0 -47 40 -76t100 -29q90 0 146 59.5t56 172.5v24l-170 -28q-172 -33 -172 -123zM410 1389q0 78 55 130t135 52t135.5 -52.5t55.5 -129.5 q0 -78 -55.5 -129.5t-135.5 -51.5t-135 51.5t-55 129.5zM539 1389q0 -27 17.5 -44.5t43.5 -17.5q27 0 44.5 17.5t17.5 44.5t-17.5 44t-44.5 17t-44 -17.5t-17 -43.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1878" d="M53 299q0 139 115 227t319 121l252 39v25q0 74 -29.5 108.5t-103.5 34.5q-43 0 -70.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -32.5 13t-12.5 32q0 55 53 123.5t163 117.5t269 49q231 0 336 -110q133 111 328 110q186 0 307 -77.5t175 -201.5 t54 -267v-52q0 -23 -15 -38t-38 -15h-643v-12q2 -102 45 -153.5t113 -51.5q80 0 135 70q16 20 28.5 25t36.5 5h267q18 0 30.5 -12t12.5 -31q0 -51 -59.5 -121.5t-174 -121.5t-274.5 -51q-129 0 -229.5 40.5t-165.5 116.5q-143 -158 -410 -157q-125 0 -219 41.5t-144.5 114.5 t-50.5 163zM401 326q0 -47 40 -76t100 -29q92 0 147 58.5t55 173.5v24l-168 -28q-174 -33 -174 -123zM1112 643h320v8q0 96 -41 149.5t-119 53.5q-70 0 -114 -51t-46 -152v-8z" />
<glyph unicode="&#xe7;" d="M78 530l2 84q8 221 153.5 346t378.5 125q178 0 299 -63.5t180.5 -150.5t63.5 -158q2 -23 -14.5 -38.5t-38.5 -15.5h-275q-23 0 -35 10.5t-22 32.5q-23 57 -58.5 84t-90.5 27q-158 0 -164 -209l-2 -78l2 -67q2 -106 43 -156.5t121 -50.5q59 0 93 25.5t56 84.5 q10 23 22.5 33.5t34.5 10.5h275q20 0 36.5 -14.5t16.5 -35.5q0 -63 -56.5 -151t-176 -155.5t-299.5 -69.5l-41 -80q14 6 33.5 9t33.5 3q74 0 118 -50t44 -130t-53 -131.5t-146 -51.5q-65 0 -118.5 28t-53.5 56q0 10 11 21l38 33q10 10 23 10q8 0 37 -13.5t63 -13.5 q31 0 49.5 17.5t18.5 44.5q0 25 -18.5 41t-49.5 16q-18 0 -45.5 -7t-38.5 -7q-18 0 -30 12l-39 41q-12 12 -13 25q0 16 13 41l41 94q-186 29 -299 148.5t-121 312.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1222" d="M76 504v63q8 244 151.5 381t386.5 137q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t116 -56.5q49 0 79 19.5t57 50.5q18 20 29 25t36 5h268q18 0 31.5 -11t13.5 -27q0 -53 -60 -125t-176 -123t-274 -51 q-249 0 -392.5 134t-147.5 390zM187 1499q0 18 10 29.5t29 11.5h280q27 0 41.5 -7t34.5 -32l184 -223q10 -10 11 -27q0 -33 -33 -32h-195q-24 0 -40.5 7t-36.5 23l-275 226q-10 10 -10 24zM453 643h325v4q0 98 -43 152.5t-121 54.5t-119.5 -54.5t-41.5 -152.5v-4z" />
<glyph unicode="&#xe9;" horiz-adv-x="1222" d="M76 504v63q8 244 151.5 381t386.5 137q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t116 -56.5q49 0 79 19.5t57 50.5q18 20 29 25t36 5h268q18 0 31.5 -11t13.5 -27q0 -53 -60 -125t-176 -123t-274 -51 q-249 0 -392.5 134t-147.5 390zM453 643h325v4q0 98 -43 152.5t-121 54.5t-119.5 -54.5t-41.5 -152.5v-4zM461 1251q0 16 10 27l182 223q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23t-43.5 -7h-192q-33 -1 -33 32z" />
<glyph unicode="&#xea;" horiz-adv-x="1222" d="M76 504v63q8 244 151.5 381t386.5 137q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t116 -56.5q49 0 79 19.5t57 50.5q18 20 29 25t36 5h268q18 0 31.5 -11t13.5 -27q0 -53 -60 -125t-176 -123t-274 -51 q-249 0 -392.5 134t-147.5 390zM230 1247q0 18 14 33l213 223q20 20 35.5 28.5t36.5 8.5h170q23 0 38 -7t35 -30l211 -223q16 -16 17 -33q0 -29 -33 -28h-100q-45 0 -72 14l-180 98l-180 -98q-27 -14 -72 -14h-103q-30 -1 -30 28zM453 643h325v4q0 98 -43 152.5t-121 54.5 t-119.5 -54.5t-41.5 -152.5v-4z" />
<glyph unicode="&#xeb;" horiz-adv-x="1222" d="M76 504v63q8 244 151.5 381t386.5 137q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t116 -56.5q49 0 79 19.5t57 50.5q18 20 29 25t36 5h268q18 0 31.5 -11t13.5 -27q0 -53 -60 -125t-176 -123t-274 -51 q-249 0 -392.5 134t-147.5 390zM256 1286v197q0 20 12 33.5t33 13.5h196q20 0 34 -13.5t14 -33.5v-197q0 -20 -13.5 -32.5t-34.5 -12.5h-196q-21 0 -33 12.5t-12 32.5zM453 643h325v4q0 98 -43 152.5t-121 54.5t-119.5 -54.5t-41.5 -152.5v-4zM686 1286v197q0 20 13 33.5 t34 13.5h194q20 0 34 -13.5t14 -33.5v-197q0 -20 -13.5 -32.5t-34.5 -12.5h-194q-21 0 -34 12.5t-13 32.5z" />
<glyph unicode="&#xec;" horiz-adv-x="593" d="M-137 1499q0 18 10 29.5t29 11.5h280q27 0 41.5 -7t34.5 -32l184 -223q10 -10 11 -27q0 -33 -33 -32h-195q-24 0 -40.5 7t-36.5 23l-275 226q-10 10 -10 24zM117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-963q0 -23 -15.5 -37t-37.5 -14h-256 q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="&#xed;" horiz-adv-x="593" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-963q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5zM142 1251q0 16 10 27l182 223q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23t-43.5 -7h-192 q-33 -1 -33 32z" />
<glyph unicode="&#xee;" horiz-adv-x="593" d="M-88 1247q0 18 14 33l213 223q20 20 35.5 28.5t36.5 8.5h170q23 0 38 -7t35 -30l211 -223q16 -16 17 -33q0 -29 -33 -28h-100q-45 0 -72 14l-180 98l-180 -98q-27 -14 -72 -14h-103q-30 -1 -30 28zM117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-963 q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="&#xef;" horiz-adv-x="593" d="M-63 1286v197q0 20 12 33.5t33 13.5h196q20 0 34 -13.5t14 -33.5v-197q0 -20 -13.5 -32.5t-34.5 -12.5h-196q-21 0 -33 12.5t-12 32.5zM117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-963q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5zM367 1286 v197q0 20 13 33.5t34 13.5h194q20 0 34 -13.5t14 -33.5v-197q0 -20 -13.5 -32.5t-34.5 -12.5h-194q-21 0 -34 12.5t-13 32.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1247" d="M72 471q0 154 68.5 265.5t192.5 169t285 57.5q59 0 109 -27q-35 90 -86 158l-225 -88q-16 -6 -25 -7q-18 0 -29.5 11.5t-11.5 29.5v52q0 27 10.5 39t34.5 22l144 55q-63 63 -170 142q-29 20 -29 41q0 18 12.5 30.5t30.5 12.5h270q59 0 99 -27l30 -27q12 -10 28.5 -23.5 t33.5 -29.5l186 72q16 6 23 6q18 0 30.5 -11.5t12.5 -29.5v-51q0 -27 -10.5 -39t-34.5 -23l-99 -39q96 -123 149.5 -282.5t61.5 -378.5q2 -12 2 -43t-2 -45q-8 -229 -151.5 -356t-393.5 -127q-253 0 -399.5 129t-146.5 362zM446 471l3 -39q2 -90 42.5 -142t126.5 -52 q84 0 125 53t43 141q2 14 2 39q0 23 -2 37q-2 90 -41.5 143.5t-126.5 53.5q-88 0 -127.5 -53.5t-41.5 -143.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1323" d="M117 51v963q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-78q55 68 142 108.5t200 40.5q117 0 208 -53t143.5 -157.5t52.5 -251.5v-572q0 -20 -14.5 -35.5t-37.5 -15.5h-272q-23 0 -38 14.5t-15 36.5v559q0 92 -44 141.5t-128 49.5q-82 0 -129 -50.5t-47 -140.5v-559 q0 -20 -14.5 -35.5t-37.5 -15.5h-272q-21 0 -36 15.5t-15 35.5zM307 1253q0 53 23.5 115t75.5 106t134 44q41 0 70 -10.5t67 -30.5q29 -16 46.5 -23.5t39.5 -7.5q20 0 31.5 8t24.5 27q10 14 18 21t23 7h129q16 0 26.5 -11t10.5 -25q0 -53 -25 -115t-77 -106t-134 -44 q-41 0 -67.5 10.5t-69.5 30.5q-49 31 -86 31q-20 0 -31.5 -8t-23.5 -25q-10 -14 -18.5 -21t-22.5 -7h-127q-14 0 -25.5 10t-11.5 24z" />
<glyph unicode="&#xf2;" horiz-adv-x="1257" d="M80 532q0 66 2 93q12 217 154.5 338.5t392.5 121.5t392 -121.5t155 -338.5q4 -53 4 -93q0 -39 -4 -92q-12 -221 -150.5 -340.5t-396.5 -119.5t-396.5 119.5t-150.5 340.5q-2 27 -2 92zM199 1499q0 18 10 29.5t29 11.5h280q27 0 41.5 -7t34.5 -32l184 -223q10 -10 11 -27 q0 -33 -33 -32h-195q-24 0 -40.5 7t-36.5 23l-275 226q-10 10 -10 24zM455 532l2 -81q6 -109 47 -161t125 -52t125 52t47 161q2 20 2 81t-2 82q-6 106 -47 159.5t-125 53.5t-125 -53t-47 -160z" />
<glyph unicode="&#xf3;" horiz-adv-x="1257" d="M80 532q0 66 2 93q12 217 154.5 338.5t392.5 121.5t392 -121.5t155 -338.5q4 -53 4 -93q0 -39 -4 -92q-12 -221 -150.5 -340.5t-396.5 -119.5t-396.5 119.5t-150.5 340.5q-2 27 -2 92zM455 532l2 -81q6 -109 47 -161t125 -52t125 52t47 161q2 20 2 81t-2 82 q-6 106 -47 159.5t-125 53.5t-125 -53t-47 -160zM471 1251q0 16 10 27l182 223q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23t-43.5 -7h-192q-33 -1 -33 32z" />
<glyph unicode="&#xf4;" horiz-adv-x="1257" d="M80 532q0 66 2 93q12 217 154.5 338.5t392.5 121.5t392 -121.5t155 -338.5q4 -53 4 -93q0 -39 -4 -92q-12 -221 -150.5 -340.5t-396.5 -119.5t-396.5 119.5t-150.5 340.5q-2 27 -2 92zM244 1247q0 18 14 33l213 223q20 20 35.5 28.5t36.5 8.5h170q23 0 38 -7t35 -30 l211 -223q16 -16 17 -33q0 -29 -33 -28h-100q-45 0 -72 14l-180 98l-180 -98q-27 -14 -72 -14h-103q-30 -1 -30 28zM455 532l2 -81q6 -109 47 -161t125 -52t125 52t47 161q2 20 2 81t-2 82q-6 106 -47 159.5t-125 53.5t-125 -53t-47 -160z" />
<glyph unicode="&#xf5;" horiz-adv-x="1257" d="M80 532q0 66 2 93q12 217 154.5 338.5t392.5 121.5t392 -121.5t155 -338.5q4 -53 4 -93q0 -39 -4 -92q-12 -221 -150.5 -340.5t-396.5 -119.5t-396.5 119.5t-150.5 340.5q-2 27 -2 92zM271 1253q0 53 23.5 115t75.5 106t134 44q41 0 70 -10.5t67 -30.5q29 -16 46.5 -23.5 t39.5 -7.5q20 0 31.5 8t24.5 27q10 14 18 21t23 7h129q16 0 26.5 -11t10.5 -25q0 -53 -25 -115t-77 -106t-134 -44q-41 0 -67.5 10.5t-69.5 30.5q-49 31 -86 31q-20 0 -31.5 -8t-23.5 -25q-10 -14 -18.5 -21t-22.5 -7h-127q-14 0 -25.5 10t-11.5 24zM455 532l2 -81 q6 -109 47 -161t125 -52t125 52t47 161q2 20 2 81t-2 82q-6 106 -47 159.5t-125 53.5t-125 -53t-47 -160z" />
<glyph unicode="&#xf6;" horiz-adv-x="1257" d="M80 532q0 66 2 93q12 217 154.5 338.5t392.5 121.5t392 -121.5t155 -338.5q4 -53 4 -93q0 -39 -4 -92q-12 -221 -150.5 -340.5t-396.5 -119.5t-396.5 119.5t-150.5 340.5q-2 27 -2 92zM271 1286v197q0 20 12 33.5t33 13.5h196q20 0 34 -13.5t14 -33.5v-197 q0 -20 -13.5 -32.5t-34.5 -12.5h-196q-21 0 -33 12.5t-12 32.5zM455 532l2 -81q6 -109 47 -161t125 -52t125 52t47 161q2 20 2 81t-2 82q-6 106 -47 159.5t-125 53.5t-125 -53t-47 -160zM701 1286v197q0 20 13 33.5t34 13.5h194q20 0 34 -13.5t14 -33.5v-197 q0 -20 -13.5 -32.5t-34.5 -12.5h-194q-21 0 -34 12.5t-13 32.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1091" d="M90 530v168q0 23 14.5 38.5t36.5 15.5h809q23 0 37 -15.5t14 -38.5v-168q0 -20 -14 -35.5t-37 -15.5h-809q-20 0 -35.5 15.5t-15.5 35.5zM391 139v189q0 23 15.5 38t37.5 15h201q23 0 38 -15.5t15 -37.5v-189q0 -23 -15 -37t-38 -14h-201q-22 0 -37.5 14.5t-15.5 36.5z M391 903v189q0 23 15.5 37t37.5 14h201q23 0 38 -14.5t15 -36.5v-189q0 -23 -15 -38t-38 -15h-201q-22 0 -37.5 15.5t-15.5 37.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1316" d="M20 246v106q0 27 10.5 39t35.5 23l47 16q-2 33 -2 98q0 66 2 97q12 213 152.5 336.5t393.5 123.5q186 0 309.5 -65.5t182.5 -185.5l78 30q16 6 22 6q18 0 30.5 -11t12.5 -30v-106q0 -27 -10 -39t-35 -22l-43 -17q4 -70 4 -123l-2 -82q-12 -217 -148 -338.5t-401 -121.5 q-192 0 -316 67.5t-183 192.5l-74 -29q-16 -6 -23 -6q-18 0 -30.5 11t-12.5 30zM487 606l2 -31l330 130q-29 123 -160 122q-158 0 -170 -190zM498 371q27 -133 161 -133q84 0 126 49t46 159v54z" />
<glyph unicode="&#xf9;" horiz-adv-x="1318" d="M106 442v572q0 23 15.5 37t36.5 14h272q23 0 38 -14.5t15 -36.5v-559q0 -190 170 -191q82 0 128 50.5t46 140.5v559q0 23 15.5 37t38.5 14h270q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5v78q-106 -150 -340 -149 q-180 0 -291 120.5t-111 341.5zM219 1499q0 18 10 29.5t29 11.5h280q27 0 41.5 -7t34.5 -32l184 -223q10 -10 11 -27q0 -33 -33 -32h-195q-24 0 -40.5 7t-36.5 23l-275 226q-10 10 -10 24z" />
<glyph unicode="&#xfa;" horiz-adv-x="1318" d="M106 442v572q0 23 15.5 37t36.5 14h272q23 0 38 -14.5t15 -36.5v-559q0 -190 170 -191q82 0 128 50.5t46 140.5v559q0 23 15.5 37t38.5 14h270q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5v78q-106 -150 -340 -149 q-180 0 -291 120.5t-111 341.5zM500 1251q0 16 10 27l182 223q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23t-43.5 -7h-192q-33 -1 -33 32z" />
<glyph unicode="&#xfb;" horiz-adv-x="1318" d="M106 442v572q0 23 15.5 37t36.5 14h272q23 0 38 -14.5t15 -36.5v-559q0 -190 170 -191q82 0 128 50.5t46 140.5v559q0 23 15.5 37t38.5 14h270q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5v78q-106 -150 -340 -149 q-180 0 -291 120.5t-111 341.5zM271 1247q0 18 14 33l213 223q20 20 35.5 28.5t36.5 8.5h170q23 0 38 -7t35 -30l211 -223q16 -16 17 -33q0 -29 -33 -28h-100q-45 0 -72 14l-180 98l-180 -98q-27 -14 -72 -14h-103q-30 -1 -30 28z" />
<glyph unicode="&#xfc;" horiz-adv-x="1318" d="M106 442v572q0 23 15.5 37t36.5 14h272q23 0 38 -14.5t15 -36.5v-559q0 -190 170 -191q82 0 128 50.5t46 140.5v559q0 23 15.5 37t38.5 14h270q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5v78q-106 -150 -340 -149 q-180 0 -291 120.5t-111 341.5zM295 1286v197q0 20 12 33.5t33 13.5h196q20 0 34 -13.5t14 -33.5v-197q0 -20 -13.5 -32.5t-34.5 -12.5h-196q-21 0 -33 12.5t-12 32.5zM725 1286v197q0 20 13 33.5t34 13.5h194q20 0 34 -13.5t14 -33.5v-197q0 -20 -13.5 -32.5t-34.5 -12.5 h-194q-21 0 -34 12.5t-13 32.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1230" d="M41 1018q4 20 17.5 33.5t31.5 13.5h244q43 0 59 -43l228 -582l233 582q20 43 61 43h242q18 0 31.5 -13.5t13.5 -29.5q0 -14 -4 -27l-580 -1341q-20 -43 -63 -43h-238q-18 0 -30.5 12t-12.5 31q0 14 5 27l161 385l-393 929q-6 16 -6 23zM471 1251q0 16 10 27l182 223 q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23t-43.5 -7h-192q-33 -1 -33 32z" />
<glyph unicode="&#xfe;" horiz-adv-x="1300" d="M119 -338v1741q0 23 15.5 37t35.5 14h264q23 0 38 -14.5t15 -36.5v-449q49 66 118 98.5t177 32.5q201 0 314.5 -123.5t122.5 -353.5q2 -27 2 -76t-2 -77q-8 -221 -123 -348t-314 -127q-197 0 -295 135v-453q0 -23 -15 -37t-38 -14h-264q-20 0 -35.5 14.5t-15.5 36.5z M485 524q0 -49 2 -75q4 -80 48.5 -132.5t130.5 -52.5q92 0 131 54.5t45 150.5q4 41 4 63q0 23 -4 64q-6 96 -45 150.5t-131 54.5q-90 0 -132 -55.5t-47 -143.5q-2 -27 -2 -78z" />
<glyph unicode="&#xff;" horiz-adv-x="1230" d="M41 1018q4 20 17.5 33.5t31.5 13.5h244q43 0 59 -43l228 -582l233 582q20 43 61 43h242q18 0 31.5 -13.5t13.5 -29.5q0 -14 -4 -27l-580 -1341q-20 -43 -63 -43h-238q-18 0 -30.5 12t-12.5 31q0 14 5 27l161 385l-393 929q-6 16 -6 23zM264 1286v197q0 20 12 33.5 t33 13.5h196q20 0 34 -13.5t14 -33.5v-197q0 -20 -13.5 -32.5t-34.5 -12.5h-196q-21 0 -33 12.5t-12 32.5zM694 1286v197q0 20 13 33.5t34 13.5h194q20 0 34 -13.5t14 -33.5v-197q0 -20 -13.5 -32.5t-34.5 -12.5h-194q-21 0 -34 12.5t-13 32.5z" />
<glyph unicode="&#x100;" horiz-adv-x="1474" d="M16 43q0 12 2 18l478 1313q20 59 82 60h319q61 0 82 -60l477 -1313q2 -6 2 -18q0 -16 -13 -29.5t-30 -13.5h-266q-49 0 -70 45l-73 199h-537l-74 -199q-20 -45 -69 -45h-267q-16 0 -29.5 13.5t-13.5 29.5zM365 1583v156q0 20 12 33.5t33 13.5h651q20 0 33.5 -13.5 t13.5 -33.5v-156q0 -20 -13 -33.5t-34 -13.5h-651q-21 0 -33 13.5t-12 33.5zM555 551h365l-183 522z" />
<glyph unicode="&#x101;" d="M51 299q0 139 116 226t318 120l254 39v25q0 76 -30.5 110.5t-104.5 34.5q-41 0 -68.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -33.5 13t-11.5 32q2 55 55 123.5t162 117.5t268 49q254 0 377 -111.5t123 -301.5v-621q0 -20 -14.5 -35.5 t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5v72q-45 -63 -125 -103t-191 -40q-108 0 -197 41.5t-139.5 115.5t-50.5 162zM264 1284v152q0 20 12 32.5t33 12.5h583q20 0 33 -12.5t13 -32.5v-152q0 -20 -12.5 -33.5t-33.5 -13.5h-583q-21 0 -33 13.5t-12 33.5zM401 326 q0 -47 40 -76t100 -29q90 0 146 59.5t56 172.5v24l-170 -28q-172 -33 -172 -123z" />
<glyph unicode="&#x102;" horiz-adv-x="1474" d="M16 43q0 12 2 18l478 1313q20 59 82 60h319q61 0 82 -60l477 -1313q2 -6 2 -18q0 -16 -13 -29.5t-30 -13.5h-266q-49 0 -70 45l-73 199h-537l-74 -199q-20 -45 -69 -45h-267q-16 0 -29.5 13.5t-13.5 29.5zM418 1815q0 16 10.5 27t28.5 11h146q18 0 28.5 -11t10.5 -27 q0 -121 96 -121t96 121q0 16 10.5 27t28.5 11h145q18 0 28.5 -11t10.5 -27q0 -129 -79.5 -211t-239.5 -82t-240 82t-80 211zM555 551h365l-183 522z" />
<glyph unicode="&#x103;" d="M51 299q0 139 116 226t318 120l254 39v25q0 76 -30.5 110.5t-104.5 34.5q-41 0 -68.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -33.5 13t-11.5 32q2 55 55 123.5t162 117.5t268 49q254 0 377 -111.5t123 -301.5v-621q0 -20 -14.5 -35.5 t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5v72q-45 -63 -125 -103t-191 -40q-108 0 -197 41.5t-139.5 115.5t-50.5 162zM289 1501q0 16 10 27.5t29 11.5h139q18 0 28.5 -11t10.5 -28q0 -121 94 -121q92 0 92 121q0 16 10.5 27.5t28.5 11.5h139q18 0 28.5 -11t10.5 -28 q0 -129 -79.5 -211t-229.5 -82q-151 0 -231 82t-80 211zM401 326q0 -47 40 -76t100 -29q90 0 146 59.5t56 172.5v24l-170 -28q-172 -33 -172 -123z" />
<glyph unicode="&#x104;" horiz-adv-x="1474" d="M16 43q0 12 2 18l478 1313q20 59 82 60h319q61 0 82 -60l477 -1313q2 -6 2 -18q0 -16 -13 -29.5t-30 -13.5h-63q-53 -2 -82 -38t-29 -85q0 -51 31 -87t88 -36h24q23 0 37.5 -15t14.5 -36v-102q0 -23 -14.5 -37.5t-37.5 -14.5h-32q-158 0 -241 90.5t-83 237.5 q0 55 21.5 97t58.5 63q-33 8 -41 41l-61 166h-537l-74 -199q-20 -45 -69 -45h-267q-16 0 -29.5 13.5t-13.5 29.5zM555 551h365l-183 522z" />
<glyph unicode="&#x105;" d="M51 299q0 139 116 226t318 120l254 39v25q0 76 -30.5 110.5t-104.5 34.5q-41 0 -68.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -33.5 13t-11.5 32q2 55 55 123.5t162 117.5t268 49q254 0 377 -111.5t123 -301.5v-621q0 -18 -13.5 -33.5 t-33.5 -17.5h-23q-55 -12 -80.5 -46t-25.5 -83q0 -51 26.5 -84t79.5 -33h19q23 0 37 -15t14 -36v-102q0 -23 -14.5 -37.5t-36.5 -14.5h-27q-149 0 -230 88.5t-81 233.5q0 45 18.5 86t42.5 68q-12 2 -19 13t-7 25v60q-45 -63 -125 -103t-191 -40q-109 0 -197.5 41.5 t-139 115.5t-50.5 162zM401 326q0 -47 40 -76t100 -29q90 0 146 59.5t56 172.5v24l-170 -28q-172 -33 -172 -123z" />
<glyph unicode="&#x106;" horiz-adv-x="1445" d="M94 715q0 127 2 184q10 266 179 410.5t458 144.5q182 0 326.5 -59.5t228.5 -172t88 -266.5q0 -18 -13 -30.5t-32 -12.5h-291q-29 0 -42 11.5t-23 42.5q-29 100 -87.5 140t-154.5 40q-231 0 -239 -258q-2 -55 -2 -170t2 -174q8 -258 239 -258q94 0 154.5 41t87.5 139 q8 31 22.5 42t42.5 11h291q18 0 31.5 -12t13.5 -31q-4 -154 -88 -266.5t-228.5 -171.5t-326.5 -59q-293 0 -460 141t-177 414q-2 55 -2 180zM555 1569q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225 q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#x107;" d="M78 530l2 84q8 221 153.5 346t378.5 125q178 0 299 -63.5t180.5 -150.5t63.5 -158q2 -23 -14.5 -38.5t-38.5 -15.5h-275q-23 0 -35 10.5t-22 32.5q-23 57 -58.5 84t-90.5 27q-158 0 -164 -209l-2 -78l2 -67q2 -106 43 -156.5t121 -50.5q59 0 93 25.5t56 84.5 q10 23 22.5 33.5t34.5 10.5h275q20 0 36.5 -14.5t16.5 -35.5q0 -63 -56.5 -152t-179 -156.5t-307.5 -67.5q-233 0 -377.5 123.5t-154.5 345.5zM457 1251q0 16 10 27l182 223q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23 t-43.5 -7h-192q-33 -1 -33 32z" />
<glyph unicode="&#x108;" horiz-adv-x="1445" d="M94 715q0 127 2 184q10 266 179 410.5t458 144.5q182 0 326.5 -59.5t228.5 -172t88 -266.5q0 -18 -13 -30.5t-32 -12.5h-291q-29 0 -42 11.5t-23 42.5q-29 100 -87.5 140t-154.5 40q-231 0 -239 -258q-2 -55 -2 -170t2 -174q8 -258 239 -258q94 0 154.5 41t87.5 139 q8 31 22.5 42t42.5 11h291q18 0 31.5 -12t13.5 -31q-4 -154 -88 -266.5t-228.5 -171.5t-326.5 -59q-293 0 -460 141t-177 414q-2 55 -2 180zM332 1565q0 18 14 32l213 224q20 20 35.5 28.5t36.5 8.5h184q20 0 35.5 -8.5t36.5 -28.5l213 -224q14 -14 14 -32q0 -29 -31 -29 h-108q-45 0 -72 14l-180 99l-180 -99q-27 -14 -72 -14h-108q-31 0 -31 29z" />
<glyph unicode="&#x109;" d="M78 530l2 84q8 221 153.5 346t378.5 125q178 0 299 -63.5t180.5 -150.5t63.5 -158q2 -23 -14.5 -38.5t-38.5 -15.5h-275q-23 0 -35 10.5t-22 32.5q-23 57 -58.5 84t-90.5 27q-158 0 -164 -209l-2 -78l2 -67q2 -106 43 -156.5t121 -50.5q59 0 93 25.5t56 84.5 q10 23 22.5 33.5t34.5 10.5h275q20 0 36.5 -14.5t16.5 -35.5q0 -63 -56.5 -152t-179 -156.5t-307.5 -67.5q-233 0 -377.5 123.5t-154.5 345.5zM232 1253q0 18 14 33l213 223q20 20 35.5 28.5t36.5 8.5h170q23 0 38 -7t35 -30l211 -223q16 -16 17 -33q0 -29 -33 -28h-100 q-45 0 -72 14l-180 98l-180 -98q-27 -14 -72 -14h-103q-30 -1 -30 28z" />
<glyph unicode="&#x10a;" horiz-adv-x="1445" d="M94 715q0 127 2 184q10 266 179 410.5t458 144.5q182 0 326.5 -59.5t228.5 -172t88 -266.5q0 -18 -13 -30.5t-32 -12.5h-291q-29 0 -42 11.5t-23 42.5q-29 100 -87.5 140t-154.5 40q-231 0 -239 -258q-2 -55 -2 -170t2 -174q8 -258 239 -258q94 0 154.5 41t87.5 139 q8 31 22.5 42t42.5 11h291q18 0 31.5 -12t13.5 -31q-4 -154 -88 -266.5t-228.5 -171.5t-326.5 -59q-293 0 -460 141t-177 414q-2 55 -2 180zM567 1583v217q0 20 12.5 33.5t32.5 13.5h219q20 0 33.5 -13t13.5 -34v-217q0 -20 -13 -33.5t-34 -13.5h-219q-20 0 -32.5 13.5 t-12.5 33.5z" />
<glyph unicode="&#x10b;" d="M78 530l2 84q8 221 153.5 346t378.5 125q178 0 299 -63.5t180.5 -150.5t63.5 -158q2 -23 -14.5 -38.5t-38.5 -15.5h-275q-23 0 -35 10.5t-22 32.5q-23 57 -58.5 84t-90.5 27q-158 0 -164 -209l-2 -78l2 -67q2 -106 43 -156.5t121 -50.5q59 0 93 25.5t56 84.5 q10 23 22.5 33.5t34.5 10.5h275q20 0 36.5 -14.5t16.5 -35.5q0 -63 -56.5 -152t-179 -156.5t-307.5 -67.5q-233 0 -377.5 123.5t-154.5 345.5zM465 1280v211q0 20 12 33.5t33 13.5h213q20 0 33.5 -13.5t13.5 -33.5v-211q0 -20 -13.5 -33.5t-33.5 -13.5h-213q-21 0 -33 13.5 t-12 33.5z" />
<glyph unicode="&#x10c;" horiz-adv-x="1445" d="M94 715q0 127 2 184q10 266 179 410.5t458 144.5q182 0 326.5 -59.5t228.5 -172t88 -266.5q0 -18 -13 -30.5t-32 -12.5h-291q-29 0 -42 11.5t-23 42.5q-29 100 -87.5 140t-154.5 40q-231 0 -239 -258q-2 -55 -2 -170t2 -174q8 -258 239 -258q94 0 154.5 41t87.5 139 q8 31 22.5 42t42.5 11h291q18 0 31.5 -12t13.5 -31q-4 -154 -88 -266.5t-228.5 -171.5t-326.5 -59q-293 0 -460 141t-177 414q-2 55 -2 180zM332 1827q0 31 31 31h108q45 0 72 -15l180 -98l180 98q27 14 72 15h108q31 0 31 -31q0 -16 -14 -31l-213 -225q-18 -20 -34.5 -27.5 t-37.5 -7.5h-184q-21 0 -37 7t-35 28l-213 225q-14 14 -14 31z" />
<glyph unicode="&#x10d;" d="M78 530l2 84q8 221 153.5 346t378.5 125q178 0 299 -63.5t180.5 -150.5t63.5 -158q2 -23 -14.5 -38.5t-38.5 -15.5h-275q-23 0 -35 10.5t-22 32.5q-23 57 -58.5 84t-90.5 27q-158 0 -164 -209l-2 -78l2 -67q2 -106 43 -156.5t121 -50.5q59 0 93 25.5t56 84.5 q10 23 22.5 33.5t34.5 10.5h275q20 0 36.5 -14.5t16.5 -35.5q0 -63 -56.5 -152t-179 -156.5t-307.5 -67.5q-233 0 -377.5 123.5t-154.5 345.5zM232 1509q0 31 30 31h103q45 0 72 -14l180 -99l180 99q27 14 72 14h100q33 0 33 -31q0 -14 -17 -30l-211 -226q-20 -20 -35.5 -27 t-37.5 -7h-170q-21 0 -37.5 7t-34.5 27l-213 226q-14 14 -14 30z" />
<glyph unicode="&#x10e;" horiz-adv-x="1472" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h539q303 0 471 -144.5t176 -415.5q2 -59 2 -157t-2 -156q-20 -561 -637 -561h-549q-20 0 -35.5 15.5t-15.5 35.5zM332 1827q0 31 31 31h108q45 0 72 -15l180 -98l180 98q27 14 72 15h108q31 0 31 -31q0 -16 -14 -31l-213 -225 q-18 -20 -34.5 -27.5t-37.5 -7.5h-184q-21 0 -37 7t-35 28l-213 225q-14 14 -14 31zM524 307h205q129 0 190.5 61.5t65.5 196.5q4 59 4 154q0 94 -4 151q-4 131 -70.5 193.5t-195.5 62.5h-195v-819z" />
<glyph unicode="&#x10f;" horiz-adv-x="1298" d="M80 532l2 74q8 223 122.5 351t311.5 128q184 0 297 -118v436q0 23 15.5 37t35.5 14h264q23 0 38.5 -14.5t15.5 -36.5v-1352q0 -23 -15.5 -37t-38.5 -14h-243q-20 0 -35.5 15.5t-15.5 35.5v64q-111 -135 -318 -135q-199 0 -312.5 124.5t-121.5 356.5zM455 532l2 -63 q6 -96 46 -150.5t132 -54.5q88 0 131 56.5t47 142.5q4 57 4 78q0 23 -4 75q-4 80 -48 132.5t-130 52.5q-92 0 -132 -54.5t-46 -150.5zM1286 1116l41 266q4 29 22.5 50.5t53.5 21.5h221q16 0 28.5 -12.5t12.5 -28.5q0 -2 -4 -23l-102 -260q-12 -27 -29.5 -43t-48.5 -16h-156 q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x110;" horiz-adv-x="1509" d="M29 647v150q0 23 15 37t38 14h94v532q0 23 14.5 38.5t36.5 15.5h539q303 0 471 -144.5t176 -415.5q2 -59 2 -157t-2 -156q-20 -561 -641 -561h-545q-20 0 -35.5 15.5t-15.5 35.5v545h-94q-23 0 -38 14.5t-15 36.5zM561 307h205q129 0 190.5 61.5t65.5 196.5q4 59 4 154 q0 94 -4 151q-4 131 -67.5 193.5t-190.5 62.5h-203v-278h178q23 0 37.5 -14.5t14.5 -36.5v-150q0 -20 -14.5 -35.5t-37.5 -15.5h-178v-289z" />
<glyph unicode="&#x111;" horiz-adv-x="1300" d="M80 532l2 74q8 217 120.5 348t315.5 131q182 0 295 -118v172h-80q-20 0 -35.5 15t-15.5 36v80q0 23 15.5 37t35.5 14h80v82q0 23 15.5 37t37.5 14h264q23 0 37.5 -14.5t14.5 -36.5v-82h80q23 0 38 -14.5t15 -36.5v-80q0 -23 -15.5 -37t-37.5 -14h-80v-1088 q0 -20 -14.5 -35.5t-37.5 -15.5h-243q-23 0 -38 14.5t-15 36.5v74q-51 -68 -130 -106.5t-186 -38.5q-205 0 -315.5 128t-120.5 353zM457 532q0 -43 2 -63q6 -96 45 -150.5t131 -54.5q90 0 132 55.5t46 143.5q4 57 4 78q0 23 -4 75q-4 80 -48 132.5t-130 52.5 q-92 0 -131 -54.5t-45 -150.5q-2 -20 -2 -64z" />
<glyph unicode="&#x112;" horiz-adv-x="1314" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h967q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-647v-260h602q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-602v-266h664q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37t-37.5 -14h-984q-20 0 -35.5 15.5 t-15.5 35.5zM285 1583v156q0 20 12 33.5t33 13.5h651q20 0 33.5 -13.5t13.5 -33.5v-156q0 -20 -13 -33.5t-34 -13.5h-651q-21 0 -33 13.5t-12 33.5z" />
<glyph unicode="&#x113;" horiz-adv-x="1222" d="M76 504v63q8 244 151.5 381t386.5 137q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t116 -56.5q49 0 79 19.5t57 50.5q18 20 29 25t36 5h268q18 0 31.5 -11t13.5 -27q0 -53 -60 -125t-176 -123t-274 -51 q-249 0 -392.5 134t-147.5 390zM279 1284v152q0 20 12 32.5t33 12.5h583q20 0 33 -12.5t13 -32.5v-152q0 -20 -12.5 -33.5t-33.5 -13.5h-583q-21 0 -33 13.5t-12 33.5zM453 643h325v4q0 98 -43 152.5t-121 54.5t-119.5 -54.5t-41.5 -152.5v-4z" />
<glyph unicode="&#x114;" horiz-adv-x="1314" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h967q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-647v-260h602q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-602v-266h664q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37t-37.5 -14h-984q-20 0 -35.5 15.5 t-15.5 35.5zM338 1815q0 16 10.5 27t28.5 11h146q18 0 28.5 -11t10.5 -27q0 -121 96 -121t96 121q0 16 10.5 27t28.5 11h145q18 0 28.5 -11t10.5 -27q0 -129 -79.5 -211t-239.5 -82t-240 82t-80 211z" />
<glyph unicode="&#x115;" horiz-adv-x="1222" d="M76 504v63q8 244 151.5 381t386.5 137q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t116 -56.5q49 0 79 19.5t57 50.5q18 20 29 25t36 5h268q18 0 31.5 -11t13.5 -27q0 -53 -60 -125t-176 -123t-274 -51 q-249 0 -392.5 134t-147.5 390zM303 1501q0 16 10 27.5t29 11.5h139q18 0 28.5 -11t10.5 -28q0 -121 94 -121q92 0 92 121q0 16 10.5 27.5t28.5 11.5h139q18 0 28.5 -11t10.5 -28q0 -129 -79.5 -211t-229.5 -82q-151 0 -231 82t-80 211zM453 643h325v4q0 98 -43 152.5 t-121 54.5t-119.5 -54.5t-41.5 -152.5v-4z" />
<glyph unicode="&#x116;" horiz-adv-x="1314" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h967q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-647v-260h602q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-602v-266h664q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37t-37.5 -14h-984q-20 0 -35.5 15.5 t-15.5 35.5zM502 1583v217q0 20 12.5 33.5t32.5 13.5h219q20 0 33.5 -13t13.5 -34v-217q0 -20 -13 -33.5t-34 -13.5h-219q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x117;" horiz-adv-x="1222" d="M76 504v63q8 244 151.5 381t386.5 137q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t116 -56.5q49 0 79 19.5t57 50.5q18 20 29 25t36 5h268q18 0 31.5 -11t13.5 -27q0 -53 -60 -125t-176 -123t-274 -51 q-249 0 -392.5 134t-147.5 390zM453 643h325v4q0 98 -43 152.5t-121 54.5t-119.5 -54.5t-41.5 -152.5v-4zM463 1280v211q0 20 12 33.5t33 13.5h213q20 0 33.5 -13.5t13.5 -33.5v-211q0 -20 -13.5 -33.5t-33.5 -13.5h-213q-21 0 -33 13.5t-12 33.5z" />
<glyph unicode="&#x118;" horiz-adv-x="1314" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h967q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-647v-260h602q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-602v-266h664q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37t-37.5 -14h-27q-57 -2 -87 -37 t-30 -86t31 -87t90 -36h25q23 0 37 -15t14 -36v-102q0 -23 -14.5 -37.5t-36.5 -14.5h-33q-160 0 -243 90.5t-83 237.5q0 66 23 123h-650q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x119;" horiz-adv-x="1224" d="M76 504v61q8 246 151.5 383t386.5 137q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t118 -56.5q49 0 78 19.5t56 50.5q18 20 30.5 25t34.5 5h270q18 0 31.5 -12t11.5 -31q-2 -47 -48 -106t-134 -109.5t-207 -68.5 q-63 -12 -94 -40t-31 -73q0 -51 31 -87t90 -36h25q23 0 37 -15t14 -36v-102q0 -23 -14.5 -37.5t-36.5 -14.5h-33q-160 0 -243 90.5t-83 237.5q0 70 23 125q-162 45 -252 173t-94 329zM453 643h325v4q0 98 -43 152.5t-121 54.5t-119.5 -54.5t-41.5 -152.5v-4z" />
<glyph unicode="&#x11a;" horiz-adv-x="1314" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h967q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-647v-260h602q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-602v-266h664q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37t-37.5 -14h-984q-20 0 -35.5 15.5 t-15.5 35.5zM266 1827q0 31 31 31h108q45 0 72 -15l180 -98l180 98q27 14 72 15h108q31 0 31 -31q0 -16 -14 -31l-213 -225q-18 -20 -34.5 -27.5t-37.5 -7.5h-184q-21 0 -37 7t-35 28l-213 225q-14 14 -14 31z" />
<glyph unicode="&#x11b;" horiz-adv-x="1222" d="M76 504v63q8 244 151.5 381t386.5 137q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t116 -56.5q49 0 79 19.5t57 50.5q18 20 29 25t36 5h268q18 0 31.5 -11t13.5 -27q0 -53 -60 -125t-176 -123t-274 -51 q-249 0 -392.5 134t-147.5 390zM230 1509q0 31 30 31h103q45 0 72 -14l180 -99l180 99q27 14 72 14h100q33 0 33 -31q0 -14 -17 -30l-211 -226q-20 -20 -35.5 -27t-37.5 -7h-170q-21 0 -37.5 7t-34.5 27l-213 226q-14 14 -14 30zM453 643h325v4q0 98 -43 152.5t-121 54.5 t-119.5 -54.5t-41.5 -152.5v-4z" />
<glyph unicode="&#x11c;" horiz-adv-x="1480" d="M94 723q0 129 2 188q8 258 179 400.5t464 142.5q197 0 343.5 -63.5t223 -163t80.5 -203.5q0 -18 -13 -31.5t-32 -13.5h-317q-20 0 -31.5 7t-21.5 22q-25 57 -79 98t-153 41q-238 0 -245 -246q-2 -57 -2 -176t2 -178q8 -260 249 -260q119 0 188.5 59.5t69.5 179.5v37h-182 q-23 0 -38 15.5t-15 37.5v160q0 23 15.5 38t37.5 15h520q23 0 38.5 -15t15.5 -38v-237q0 -174 -81 -300t-228.5 -192.5t-344.5 -66.5q-297 0 -465 144t-178 413q-2 57 -2 186zM355 1565q0 18 14 32l213 224q20 20 35.5 28.5t36.5 8.5h184q20 0 35.5 -8.5t36.5 -28.5 l213 -224q14 -14 14 -32q0 -29 -31 -29h-108q-45 0 -72 14l-180 99l-180 -99q-27 -14 -72 -14h-108q-31 0 -31 29z" />
<glyph unicode="&#x11d;" horiz-adv-x="1306" d="M78 553q0 250 111.5 391t326.5 141q104 0 184 -39.5t134 -107.5v74q0 23 14 38t37 15h250q23 0 38 -15.5t15 -37.5v-981q0 -236 -141.5 -359t-397.5 -123q-196 0 -319 63.5t-176.5 146.5t-53.5 139q0 20 16.5 34.5t37.5 14.5h274q20 0 32.5 -9.5t22.5 -31.5 q20 -45 53 -70.5t103 -25.5q88 0 131 44t43 146v137q-109 -113 -297 -112q-207 0 -316.5 119.5t-119.5 353.5zM248 1247q0 18 14 33l213 223q20 20 35.5 28.5t36.5 8.5h170q23 0 38 -7t35 -30l211 -223q16 -16 17 -33q0 -29 -33 -28h-100q-45 0 -72 14l-180 98l-180 -98 q-27 -14 -72 -14h-103q-30 -1 -30 28zM455 551q0 -117 38.5 -183.5t139.5 -66.5q88 0 131 53.5t49 134.5q2 14 2 62q0 49 -2 61q-6 84 -49 136.5t-131 52.5q-90 0 -131 -54.5t-45 -150.5z" />
<glyph unicode="&#x11e;" horiz-adv-x="1480" d="M94 723q0 129 2 188q8 258 179 400.5t464 142.5q197 0 343.5 -63.5t223 -163t80.5 -203.5q0 -18 -13 -31.5t-32 -13.5h-317q-20 0 -31.5 7t-21.5 22q-25 57 -79 98t-153 41q-238 0 -245 -246q-2 -57 -2 -176t2 -178q8 -260 249 -260q119 0 188.5 59.5t69.5 179.5v37h-182 q-23 0 -38 15.5t-15 37.5v160q0 23 15.5 38t37.5 15h520q23 0 38.5 -15t15.5 -38v-237q0 -174 -81 -300t-228.5 -192.5t-344.5 -66.5q-297 0 -465 144t-178 413q-2 57 -2 186zM426 1815q0 16 10.5 27t28.5 11h146q18 0 28.5 -11t10.5 -27q0 -121 96 -121t96 121 q0 16 10.5 27t28.5 11h145q18 0 28.5 -11t10.5 -27q0 -129 -79.5 -211t-239.5 -82t-240 82t-80 211z" />
<glyph unicode="&#x11f;" horiz-adv-x="1306" d="M78 553q0 250 111.5 391t326.5 141q104 0 184 -39.5t134 -107.5v74q0 23 14 38t37 15h250q23 0 38 -15.5t15 -37.5v-981q0 -236 -141.5 -359t-397.5 -123q-196 0 -319 63.5t-176.5 146.5t-53.5 139q0 20 16.5 34.5t37.5 14.5h274q20 0 32.5 -9.5t22.5 -31.5 q20 -45 53 -70.5t103 -25.5q88 0 131 44t43 146v137q-109 -113 -297 -112q-207 0 -316.5 119.5t-119.5 353.5zM322 1501q0 16 10 27.5t29 11.5h139q18 0 28.5 -11t10.5 -28q0 -121 94 -121q92 0 92 121q0 16 10.5 27.5t28.5 11.5h139q18 0 28.5 -11t10.5 -28 q0 -129 -79.5 -211t-229.5 -82q-151 0 -231 82t-80 211zM455 551q0 -117 38.5 -183.5t139.5 -66.5q88 0 131 53.5t49 134.5q2 14 2 62q0 49 -2 61q-6 84 -49 136.5t-131 52.5q-90 0 -131 -54.5t-45 -150.5z" />
<glyph unicode="&#x120;" horiz-adv-x="1480" d="M94 723q0 129 2 188q8 258 179 400.5t464 142.5q197 0 343.5 -63.5t223 -163t80.5 -203.5q0 -18 -13 -31.5t-32 -13.5h-317q-20 0 -31.5 7t-21.5 22q-25 57 -79 98t-153 41q-238 0 -245 -246q-2 -57 -2 -176t2 -178q8 -260 249 -260q119 0 188.5 59.5t69.5 179.5v37h-182 q-23 0 -38 15.5t-15 37.5v160q0 23 15.5 38t37.5 15h520q23 0 38.5 -15t15.5 -38v-237q0 -174 -81 -300t-228.5 -192.5t-344.5 -66.5q-297 0 -465 144t-178 413q-2 57 -2 186zM590 1583v217q0 20 12.5 33.5t32.5 13.5h219q20 0 33.5 -13t13.5 -34v-217q0 -20 -13 -33.5 t-34 -13.5h-219q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x121;" horiz-adv-x="1306" d="M78 553q0 250 111.5 391t326.5 141q104 0 184 -39.5t134 -107.5v74q0 23 14 38t37 15h250q23 0 38 -15.5t15 -37.5v-981q0 -236 -141.5 -359t-397.5 -123q-196 0 -319 63.5t-176.5 146.5t-53.5 139q0 20 16.5 34.5t37.5 14.5h274q20 0 32.5 -9.5t22.5 -31.5 q20 -45 53 -70.5t103 -25.5q88 0 131 44t43 146v137q-109 -113 -297 -112q-207 0 -316.5 119.5t-119.5 353.5zM455 551q0 -117 38.5 -183.5t139.5 -66.5q88 0 131 53.5t49 134.5q2 14 2 62q0 49 -2 61q-6 84 -49 136.5t-131 52.5q-90 0 -131 -54.5t-45 -150.5zM482 1280v211 q0 20 12 33.5t33 13.5h213q20 0 33.5 -13.5t13.5 -33.5v-211q0 -20 -13.5 -33.5t-33.5 -13.5h-213q-21 0 -33 13.5t-12 33.5z" />
<glyph unicode="&#x122;" horiz-adv-x="1480" d="M94 723q0 129 2 188q8 258 179 400.5t464 142.5q197 0 343.5 -63.5t223 -163t80.5 -203.5q0 -18 -13 -31.5t-32 -13.5h-317q-20 0 -31.5 7t-21.5 22q-25 57 -79 98t-153 41q-238 0 -245 -246q-2 -57 -2 -176t2 -178q8 -260 249 -260q119 0 188.5 59.5t69.5 179.5v37h-182 q-23 0 -38 15.5t-15 37.5v160q0 23 15.5 38t37.5 15h520q23 0 38.5 -15t15.5 -38v-237q0 -174 -81 -300t-228.5 -192.5t-344.5 -66.5q-297 0 -465 144t-178 413q-2 57 -2 186zM526 -440l41 266q4 29 22.5 50.5t53.5 21.5h235q16 0 28.5 -12.5t12.5 -28.5q0 -12 -4 -23 l-102 -260q-23 -59 -76 -59h-172q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x123;" horiz-adv-x="1306" d="M78 553q0 250 111.5 391t326.5 141q104 0 184 -39.5t134 -107.5v74q0 23 14 38t37 15h250q23 0 38 -15.5t15 -37.5v-981q0 -236 -141.5 -359t-397.5 -123q-196 0 -319 63.5t-176.5 146.5t-53.5 139q0 20 16.5 34.5t37.5 14.5h274q20 0 32.5 -9.5t22.5 -31.5 q20 -45 53 -70.5t103 -25.5q88 0 131 44t43 146v137q-109 -113 -297 -112q-207 0 -316.5 119.5t-119.5 353.5zM455 551q0 -117 38.5 -183.5t139.5 -66.5q88 0 131 53.5t49 134.5q2 14 2 62q0 49 -2 61q-6 84 -49 136.5t-131 52.5q-90 0 -131 -54.5t-45 -150.5zM496 1260 q0 2 4 22l102 260q12 27 29.5 43.5t48.5 16.5h156q16 0 28.5 -13.5t9.5 -32.5l-40 -266q-4 -29 -22.5 -50t-53.5 -21h-221q-16 0 -28.5 12t-12.5 29z" />
<glyph unicode="&#x124;" horiz-adv-x="1531" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-485h484v485q0 23 15 38.5t38 15.5h278q23 0 38.5 -15.5t15.5 -38.5v-1329q0 -23 -15.5 -37t-38.5 -14h-278q-23 0 -38 14.5t-15 36.5v502h-484v-502q0 -23 -15 -37t-38 -14h-281q-22 0 -36.5 14.5 t-14.5 36.5zM375 1565q0 18 14 32l213 224q20 20 35.5 28.5t36.5 8.5h184q20 0 35.5 -8.5t36.5 -28.5l213 -224q14 -14 14 -32q0 -29 -31 -29h-108q-45 0 -72 14l-180 99l-180 -99q-27 -14 -72 -14h-108q-31 0 -31 29z" />
<glyph unicode="&#x125;" horiz-adv-x="1339" d="M117 51v1352q0 23 15 37t38 14h276q23 0 38.5 -14.5t15.5 -36.5v-457q119 139 321 139q117 0 211 -53t147.5 -157.5t53.5 -251.5v-572q0 -23 -15.5 -37t-37.5 -14h-279q-20 0 -35.5 15.5t-15.5 35.5v559q0 90 -45 140.5t-129 50.5q-82 0 -129 -50.5t-47 -140.5v-559 q0 -23 -15.5 -37t-38.5 -14h-276q-23 0 -38 14.5t-15 36.5zM291 1564q0 18 14 33l213 223q20 20 35.5 28.5t36.5 8.5h170q23 0 38 -7t35 -30l211 -223q16 -16 17 -33q0 -29 -33 -28h-100q-45 0 -72 14l-180 98l-180 -98q-27 -14 -72 -14h-103q-30 -1 -30 28z" />
<glyph unicode="&#x126;" horiz-adv-x="1531" d="M98 1114v103q0 18 11.5 32.5t29.5 18.5v112q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-112h484v112q0 23 15 38.5t38 15.5h278q23 0 38.5 -15.5t15.5 -38.5v-112q20 -4 32.5 -17.5t12.5 -33.5v-103q0 -20 -12.5 -34.5t-32.5 -18.5v-1010q0 -23 -15.5 -37 t-38.5 -14h-278q-23 0 -38 14.5t-15 36.5v502h-484v-502q0 -23 -15 -37t-38 -14h-281q-23 0 -37 14.5t-14 36.5v1010q-18 4 -29.5 18.5t-11.5 34.5zM524 895h484v166h-484v-166z" />
<glyph unicode="&#x127;" horiz-adv-x="1339" d="M-8 1176v79q0 23 15.5 37.5t35.5 14.5h76v96q0 23 15.5 37t35.5 14h279q23 0 37 -14.5t14 -36.5v-96h71q23 0 38.5 -14.5t15.5 -37.5v-79q0 -23 -15.5 -37.5t-38.5 -14.5h-71v-178q119 139 321 139q117 0 211 -53t147.5 -157.5t53.5 -251.5v-570q0 -20 -14.5 -35.5 t-36.5 -15.5h-281q-20 0 -35.5 15.5t-15.5 35.5v557q0 90 -45 140.5t-127 50.5t-130 -50.5t-48 -140.5v-559q0 -20 -14.5 -35.5t-36.5 -15.5h-279q-20 0 -35.5 15.5t-15.5 35.5v1073h-76q-20 0 -35.5 15.5t-15.5 36.5z" />
<glyph unicode="&#x128;" horiz-adv-x="673" d="M-24 1571q0 53 23.5 115.5t76.5 107.5t135 45q43 0 74 -11t67 -32q20 -10 43 -19.5t43 -9.5t32 8.5t24 24.5q10 14 18 21.5t23 7.5h129q16 0 26.5 -10.5t10.5 -26.5q0 -51 -25 -113.5t-78 -107.5t-135 -45q-41 0 -69.5 10t-69.5 31q-27 16 -46.5 23t-41.5 7 q-20 0 -31.5 -8t-24.5 -24q-10 -14 -18 -21.5t-23 -7.5h-126q-14 0 -25.5 10t-11.5 25zM139 51v1331q0 23 15.5 37.5t35.5 14.5h293q23 0 37.5 -14.5t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5h-293q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x129;" horiz-adv-x="593" d="M-61 1253q0 53 23.5 115t75.5 106t134 44q41 0 70 -10.5t67 -30.5q29 -16 46.5 -23.5t39.5 -7.5q20 0 31.5 8t24.5 27q10 14 18 21t23 7h129q16 0 26.5 -11t10.5 -25q0 -53 -25 -115t-77 -106t-134 -44q-41 0 -67.5 10.5t-69.5 30.5q-49 31 -86 31q-20 0 -31.5 -8 t-23.5 -25q-10 -14 -18.5 -21t-22.5 -7h-127q-14 0 -25.5 10t-11.5 24zM117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-963q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="&#x12a;" horiz-adv-x="673" d="M-35 1583v156q0 20 12 33.5t33 13.5h651q20 0 33.5 -13.5t13.5 -33.5v-156q0 -20 -13 -33.5t-34 -13.5h-651q-21 0 -33 13.5t-12 33.5zM139 51v1331q0 23 15.5 37.5t35.5 14.5h293q23 0 37.5 -14.5t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5h-293 q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x12b;" horiz-adv-x="593" d="M-41 1284v152q0 20 12 32.5t33 12.5h583q20 0 33 -12.5t13 -32.5v-152q0 -20 -12.5 -33.5t-33.5 -13.5h-583q-21 0 -33 13.5t-12 33.5zM117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-963q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="&#x12c;" horiz-adv-x="673" d="M18 1815q0 16 10.5 27t28.5 11h146q18 0 28.5 -11t10.5 -27q0 -121 96 -121t96 121q0 16 10.5 27t28.5 11h145q18 0 28.5 -11t10.5 -27q0 -129 -79.5 -211t-239.5 -82t-240 82t-80 211zM139 51v1331q0 23 15.5 37.5t35.5 14.5h293q23 0 37.5 -14.5t14.5 -37.5v-1331 q0 -20 -14.5 -35.5t-37.5 -15.5h-293q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x12d;" horiz-adv-x="593" d="M-14 1501q0 16 10 27.5t29 11.5h139q18 0 28.5 -11t10.5 -28q0 -121 94 -121q92 0 92 121q0 16 10.5 27.5t28.5 11.5h139q18 0 28.5 -11t10.5 -28q0 -129 -79.5 -211t-229.5 -82q-151 0 -231 82t-80 211zM117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-963 q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="&#x12e;" horiz-adv-x="686" d="M137 -125q0 57 22.5 99t59.5 63l-31 6q-14 2 -25 15.5t-11 29.5v1294q0 23 15 37.5t36 14.5h293q23 0 37 -14.5t14 -37.5v-1331q0 -20 -14.5 -35.5t-36.5 -15.5h-25q-59 0 -90 -37t-31 -88t31 -87t90 -36h25q23 0 37 -15.5t14 -35.5v-102q0 -23 -14.5 -37.5t-36.5 -14.5 h-33q-160 0 -243 90.5t-83 237.5z" />
<glyph unicode="&#x12f;" horiz-adv-x="608" d="M102 -111q0 51 20.5 90t55.5 58l-10 2q-14 2 -25.5 13t-11.5 28v934q0 23 15.5 37t35.5 14h256q23 0 38.5 -14.5t15.5 -36.5v-963q0 -23 -15.5 -37t-38.5 -14h-16q-59 0 -90 -30.5t-31 -80.5q0 -51 30.5 -80.5t90.5 -29.5h24q23 0 37.5 -15.5t14.5 -35.5v-88 q0 -23 -14.5 -37.5t-37.5 -14.5h-32q-150 0 -231 83t-81 218zM131 1266v190q0 23 15.5 38t35.5 15h256q23 0 38.5 -15t15.5 -38v-190q0 -23 -15.5 -38.5t-38.5 -15.5h-256q-22 0 -36.5 15.5t-14.5 38.5z" />
<glyph unicode="&#x130;" horiz-adv-x="673" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h293q23 0 37.5 -14.5t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5h-293q-20 0 -35.5 15.5t-15.5 35.5zM182 1583v217q0 20 12.5 33.5t32.5 13.5h219q20 0 33.5 -13t13.5 -34v-217q0 -20 -13 -33.5t-34 -13.5h-219 q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x131;" horiz-adv-x="593" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-963q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="&#x132;" horiz-adv-x="1503" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h293q23 0 37.5 -14.5t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5h-293q-20 0 -35.5 15.5t-15.5 35.5zM698 31v231q0 23 15.5 37t36.5 14q119 0 175 66.5t56 202.5v800q0 23 15.5 37.5t35.5 14.5h293q23 0 37 -15.5t14 -38.5 v-854q0 -289 -160.5 -417.5t-465.5 -128.5q-21 0 -36.5 15t-15.5 36z" />
<glyph unicode="&#x133;" horiz-adv-x="1224" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-963q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5zM117 1266v190q0 23 15 38t36 15h256q23 0 38 -15t15 -38v-190q0 -23 -15.5 -38.5t-37.5 -15.5h-256q-23 0 -37 15.5t-14 38.5zM481 -162 q0 20 14.5 35.5t37.5 15.5h81q68 0 94.5 37t26.5 107v981q0 23 15.5 37t36.5 14h268q23 0 37 -14.5t14 -36.5v-989q0 -199 -119 -306.5t-332 -107.5h-122q-23 0 -37.5 14.5t-14.5 36.5v176zM733 1266v190q0 23 15.5 38t38.5 15h268q23 0 38 -15t15 -38v-190 q0 -23 -15.5 -38.5t-37.5 -15.5h-268q-23 0 -38.5 15.5t-15.5 38.5z" />
<glyph unicode="&#x134;" horiz-adv-x="1380" d="M45 453q0 18 12.5 31.5t30.5 13.5h293q29 0 45 -13.5t27 -44.5q33 -154 200 -153q98 0 150.5 64.5t52.5 187.5v577h-657q-20 0 -36 15.5t-16 35.5v215q0 23 15.5 37.5t36.5 14.5h1001q23 0 38 -15.5t15 -38.5v-854q0 -174 -76.5 -298t-215 -186t-318.5 -62 q-160 0 -295 54t-218 161.5t-85 257.5zM322 1565q0 18 14 32l213 224q20 20 35.5 28.5t36.5 8.5h184q20 0 35.5 -8.5t36.5 -28.5l213 -224q14 -14 14 -32q0 -29 -31 -29h-108q-45 0 -72 14l-180 99l-180 -99q-27 -14 -72 -14h-108q-31 0 -31 29z" />
<glyph unicode="&#x135;" horiz-adv-x="841" d="M21 1247q0 18 14 33l213 223q20 20 35.5 28.5t36.5 8.5h170q23 0 38 -7t35 -30l211 -223q16 -16 17 -33q0 -29 -33 -28h-100q-45 0 -72 14l-180 98l-180 -98q-27 -14 -72 -14h-103q-30 -1 -30 28zM37 -115q0 20 15.5 36t35.5 16h141q68 0 94.5 36.5t26.5 106.5v696h-250 q-23 0 -38 14.5t-15 36.5v187q0 23 15.5 37t37.5 14h572q23 0 38 -14.5t15 -36.5v-934q0 -217 -121 -343t-332 -126h-184q-20 0 -35.5 14.5t-15.5 36.5v223z" />
<glyph unicode="&#x136;" horiz-adv-x="1368" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h275q23 0 37 -15.5t14 -38.5v-446l363 459q23 41 79 41h314q16 0 29.5 -13.5t13.5 -29.5t-8 -25l-478 -618l517 -680q8 -8 8 -25q0 -16 -13.5 -29.5t-31.5 -13.5h-322q-33 0 -53.5 13.5t-28.5 27.5l-389 500v-490 q0 -20 -14.5 -35.5t-36.5 -15.5h-275q-20 0 -35.5 15.5t-15.5 35.5zM477 -440l41 266q4 29 22.5 50.5t53.5 21.5h235q16 0 28.5 -12.5t12.5 -28.5q0 -12 -4 -23l-102 -260q-23 -59 -76 -59h-172q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x137;" horiz-adv-x="1210" d="M117 51v1352q0 23 15 37t38 14h250q23 0 37 -14.5t14 -36.5v-684l272 311q4 4 15.5 14.5t24 15.5t30.5 5h289q18 0 31.5 -13.5t13.5 -33.5q0 -18 -14 -33l-361 -397l412 -510q14 -18 14 -31q0 -20 -13.5 -33.5t-33.5 -13.5h-297q-27 0 -40 8t-32 27l-311 385v-369 q0 -20 -14.5 -35.5t-36.5 -15.5h-250q-23 0 -38 14.5t-15 36.5zM374 -440l41 266q4 29 22.5 50.5t53.5 21.5h221q16 0 28.5 -12.5t12.5 -28.5q0 -2 -4 -23l-102 -260q-12 -27 -29.5 -43t-48.5 -16h-156q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x138;" horiz-adv-x="1210" d="M117 51v963q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-355l291 371q4 4 15 14.5t23.5 15.5t30.5 5h273q20 0 33.5 -13.5t13.5 -33.5t-16 -33l-351 -444l398 -463q14 -12 14 -31q0 -20 -13.5 -33.5t-31.5 -13.5h-299q-23 0 -37 7t-22.5 16.5t-12.5 11.5l-309 364 v-348q0 -20 -14.5 -35.5t-36.5 -15.5h-252q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="&#x139;" horiz-adv-x="1228" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h287q23 0 37 -14.5t14 -37.5v-1060h615q23 0 38 -15.5t15 -38.5v-217q0 -23 -15.5 -37t-37.5 -14h-953q-20 0 -35.5 15.5t-15.5 35.5zM153 1569q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5 q0 -14 -11 -25l-274 -225q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#x13a;" horiz-adv-x="593" d="M117 51v1352q0 23 15 37t38 14h256q23 0 37 -14.5t14 -36.5v-1352q0 -20 -14 -35.5t-37 -15.5h-256q-23 0 -38 14.5t-15 36.5zM166 1568q0 16 10 27l182 223q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23t-43.5 -7h-192 q-33 -1 -33 32z" />
<glyph unicode="&#x13b;" horiz-adv-x="1228" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h287q23 0 37 -14.5t14 -37.5v-1060h615q23 0 38 -15.5t15 -38.5v-217q0 -23 -15.5 -37t-37.5 -14h-953q-20 0 -35.5 15.5t-15.5 35.5zM412 -440l41 266q4 29 22.5 50.5t53.5 21.5h235q16 0 28.5 -12.5t12.5 -28.5q0 -12 -4 -23 l-102 -260q-23 -59 -76 -59h-172q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x13c;" horiz-adv-x="593" d="M77 -440l41 266q4 29 22.5 50.5t53.5 21.5h221q16 0 28.5 -12.5t12.5 -28.5q0 -2 -4 -23l-102 -260q-12 -27 -29.5 -43t-48.5 -16h-156q-16 0 -28.5 13t-10.5 32zM117 51v1352q0 23 15 37t38 14h256q23 0 37 -14.5t14 -36.5v-1352q0 -20 -14 -35.5t-37 -15.5h-256 q-23 0 -38 14.5t-15 36.5z" />
<glyph unicode="&#x13d;" horiz-adv-x="1228" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h287q23 0 37 -14.5t14 -37.5v-1060h615q23 0 38 -15.5t15 -38.5v-217q0 -23 -15.5 -37t-37.5 -14h-953q-20 0 -35.5 15.5t-15.5 35.5zM684 1116l41 266q4 29 22.5 50.5t53.5 21.5h235q16 0 28.5 -12.5t12.5 -28.5q0 -12 -4 -23 l-102 -260q-23 -59 -76 -59h-172q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x13e;" horiz-adv-x="817" d="M117 51v1352q0 23 15 37t38 14h256q23 0 37 -14.5t14 -36.5v-1352q0 -20 -14 -35.5t-37 -15.5h-256q-23 0 -38 14.5t-15 36.5zM581 1116l41 266q4 29 22.5 50.5t53.5 21.5h221q16 0 28.5 -12.5t12.5 -28.5q0 -2 -4 -23l-102 -260q-12 -27 -29.5 -43t-48.5 -16h-156 q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x13f;" horiz-adv-x="1228" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h287q23 0 37 -14.5t14 -37.5v-1060h615q23 0 38 -15.5t15 -38.5v-217q0 -23 -15.5 -37t-37.5 -14h-953q-20 0 -35.5 15.5t-15.5 35.5zM692 684v211q0 20 12 33.5t33 13.5h213q20 0 33.5 -13.5t13.5 -33.5v-211q0 -20 -13.5 -33.5 t-33.5 -13.5h-213q-21 0 -33 13.5t-12 33.5z" />
<glyph unicode="&#x140;" horiz-adv-x="866" d="M117 51v1352q0 23 15 37t38 14h256q23 0 37 -14.5t14 -36.5v-1352q0 -20 -14 -35.5t-37 -15.5h-256q-23 0 -38 14.5t-15 36.5zM582 631v211q0 20 12 33.5t33 13.5h213q20 0 33.5 -13.5t13.5 -33.5v-211q0 -20 -13.5 -33.5t-33.5 -13.5h-213q-21 0 -33 13.5t-12 33.5z" />
<glyph unicode="&#x141;" horiz-adv-x="1245" d="M27 516v162q0 27 10 39t35 22l84 33v610q0 23 15 37.5t36 14.5h287q23 0 37 -14.5t14 -37.5v-456l297 116q14 6 24 7q18 0 29.5 -11.5t11.5 -29.5v-162q0 -27 -10 -39t-35 -23l-317 -125v-337h614q23 0 38 -15.5t15 -38.5v-217q0 -23 -15 -37t-38 -14h-952 q-21 0 -36 15.5t-15 35.5v455l-64 -25q-14 -6 -24 -6q-18 0 -29.5 11.5t-11.5 29.5z" />
<glyph unicode="&#x142;" horiz-adv-x="714" d="M49 541v159q0 27 10.5 39.5t34.5 22.5l84 33v608q0 23 15.5 37t37.5 14h256q23 0 37.5 -14.5t14.5 -36.5v-469l63 24q16 6 25 7q18 0 29.5 -11.5t11.5 -29.5v-160q0 -27 -10.5 -39t-34.5 -23l-84 -34v-617q0 -20 -14.5 -35.5t-37.5 -15.5h-256q-22 0 -37.5 14.5 t-15.5 36.5v479l-63 -24q-14 -6 -25 -6q-16 0 -28.5 11t-12.5 30z" />
<glyph unicode="&#x143;" horiz-adv-x="1476" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h232q47 0 70 -39l483 -764v749q0 23 14.5 38.5t36.5 15.5h260q23 0 37 -15.5t14 -38.5v-1327q0 -23 -14 -38t-37 -15h-231q-47 0 -70 39l-481 733v-721q0 -23 -15.5 -37t-37.5 -14h-261q-20 0 -35.5 15.5t-15.5 35.5zM571 1569 q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#x144;" horiz-adv-x="1323" d="M117 51v963q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-78q55 68 142 108.5t200 40.5q117 0 208 -53t143.5 -157.5t52.5 -251.5v-572q0 -20 -14.5 -35.5t-37.5 -15.5h-272q-23 0 -38 14.5t-15 36.5v559q0 92 -44 141.5t-128 49.5q-82 0 -129 -50.5t-47 -140.5v-559 q0 -20 -14.5 -35.5t-37.5 -15.5h-272q-21 0 -36 15.5t-15 35.5zM510 1251q0 16 10 27l182 223q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23t-43.5 -7h-192q-33 -1 -33 32z" />
<glyph unicode="&#x145;" horiz-adv-x="1476" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h232q47 0 70 -39l483 -764v749q0 23 14.5 38.5t36.5 15.5h260q23 0 37 -15.5t14 -38.5v-1327q0 -23 -14 -38t-37 -15h-231q-47 0 -70 39l-481 733v-721q0 -23 -15.5 -37t-37.5 -14h-261q-20 0 -35.5 15.5t-15.5 35.5zM520 -440 l41 266q4 29 22.5 50.5t53.5 21.5h235q16 0 28.5 -12.5t12.5 -28.5q0 -12 -4 -23l-102 -260q-23 -59 -76 -59h-172q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x146;" horiz-adv-x="1323" d="M117 51v963q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-78q55 68 142 108.5t200 40.5q117 0 208 -53t143.5 -157.5t52.5 -251.5v-572q0 -20 -14.5 -35.5t-37.5 -15.5h-272q-23 0 -38 14.5t-15 36.5v559q0 92 -44 141.5t-128 49.5q-82 0 -129 -50.5t-47 -140.5v-559 q0 -20 -14.5 -35.5t-37.5 -15.5h-272q-21 0 -36 15.5t-15 35.5zM458 -440l41 266q4 29 22.5 50.5t53.5 21.5h221q16 0 28.5 -12.5t12.5 -28.5q0 -2 -4 -23l-102 -260q-12 -27 -29.5 -43t-48.5 -16h-156q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x147;" horiz-adv-x="1476" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h232q47 0 70 -39l483 -764v749q0 23 14.5 38.5t36.5 15.5h260q23 0 37 -15.5t14 -38.5v-1327q0 -23 -14 -38t-37 -15h-231q-47 0 -70 39l-481 733v-721q0 -23 -15.5 -37t-37.5 -14h-261q-20 0 -35.5 15.5t-15.5 35.5zM348 1827 q0 31 31 31h108q45 0 72 -15l180 -98l180 98q27 14 72 15h108q31 0 31 -31q0 -16 -14 -31l-213 -225q-18 -20 -34.5 -27.5t-37.5 -7.5h-184q-21 0 -37 7t-35 28l-213 225q-14 14 -14 31z" />
<glyph unicode="&#x148;" horiz-adv-x="1323" d="M117 51v963q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-78q55 68 142 108.5t200 40.5q117 0 208 -53t143.5 -157.5t52.5 -251.5v-572q0 -20 -14.5 -35.5t-37.5 -15.5h-272q-23 0 -38 14.5t-15 36.5v559q0 92 -44 141.5t-128 49.5q-82 0 -129 -50.5t-47 -140.5v-559 q0 -20 -14.5 -35.5t-37.5 -15.5h-272q-21 0 -36 15.5t-15 35.5zM281 1509q0 31 30 31h103q45 0 72 -14l180 -99l180 99q27 14 72 14h100q33 0 33 -31q0 -14 -17 -30l-211 -226q-20 -20 -35.5 -27t-37.5 -7h-170q-21 0 -37.5 7t-34.5 27l-213 226q-14 14 -14 30z" />
<glyph unicode="&#x149;" horiz-adv-x="1323" d="M117 51v963q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-78q55 68 142 108.5t200 40.5q117 0 208 -53t143.5 -157.5t52.5 -251.5v-572q0 -20 -14.5 -35.5t-37.5 -15.5h-272q-23 0 -38 14.5t-15 36.5v559q0 92 -44 141.5t-128 49.5q-82 0 -129 -50.5t-47 -140.5v-559 q0 -20 -14.5 -35.5t-37.5 -15.5h-272q-21 0 -36 15.5t-15 35.5zM176 1254l41 266q4 29 22.5 50.5t53.5 21.5h221q16 0 28.5 -12.5t12.5 -28.5q0 -2 -4 -23l-102 -260q-12 -27 -29.5 -43t-48.5 -16h-156q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x14a;" horiz-adv-x="1476" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h232q47 0 70 -39l483 -764v749q0 23 14.5 38.5t36.5 15.5h260q23 0 37 -15.5t14 -38.5v-1384q0 -219 -146.5 -338t-399.5 -119h-25q-23 0 -38 14.5t-15 36.5v215q0 23 15 37.5t38 14.5h57q78 0 115 41t37 112v47l-471 715v-721 q0 -23 -15.5 -37t-37.5 -14h-261q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x14b;" horiz-adv-x="1323" d="M117 51v963q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-78q55 68 142 108.5t200 40.5q117 0 208 -53t143.5 -157.5t52.5 -251.5v-596q0 -238 -144.5 -363t-398.5 -125h-43q-21 0 -36 14.5t-15 36.5v213q0 23 14 38.5t37 15.5h57q82 0 117 44t35 140v569 q0 92 -44 141.5t-128 49.5q-82 0 -129 -50.5t-47 -140.5v-559q0 -20 -14.5 -35.5t-37.5 -15.5h-272q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x14c;" horiz-adv-x="1464" d="M94 713q0 119 2 178q8 270 179 416.5t458 146.5q285 0 456 -146.5t179 -416.5q4 -119 4 -178q0 -61 -4 -176q-10 -274 -177 -415.5t-458 -141.5t-459 141t-178 416q-2 57 -2 176zM361 1583v156q0 20 12 33.5t33 13.5h651q20 0 33.5 -13.5t13.5 -33.5v-156 q0 -20 -13 -33.5t-34 -13.5h-651q-21 0 -33 13.5t-12 33.5zM492 719q0 -111 2 -170q4 -133 67.5 -197.5t171.5 -64.5q107 0 170.5 64.5t67.5 197.5q4 119 4 170t-4 166q-4 133 -67.5 197.5t-170.5 64.5q-109 0 -172 -64.5t-67 -197.5q-2 -57 -2 -166z" />
<glyph unicode="&#x14d;" horiz-adv-x="1257" d="M80 532q0 66 2 93q12 217 154.5 338.5t392.5 121.5t392 -121.5t155 -338.5q4 -53 4 -93q0 -39 -4 -92q-12 -221 -150.5 -340.5t-396.5 -119.5t-396.5 119.5t-150.5 340.5q-2 27 -2 92zM293 1284v152q0 20 12 32.5t33 12.5h583q20 0 33 -12.5t13 -32.5v-152 q0 -20 -12.5 -33.5t-33.5 -13.5h-583q-21 0 -33 13.5t-12 33.5zM455 532l2 -81q6 -109 47 -161t125 -52t125 52t47 161q2 20 2 81t-2 82q-6 106 -47 159.5t-125 53.5t-125 -53t-47 -160z" />
<glyph unicode="&#x14e;" horiz-adv-x="1464" d="M94 713q0 119 2 178q8 270 179 416.5t458 146.5q285 0 456 -146.5t179 -416.5q4 -119 4 -178q0 -61 -4 -176q-10 -274 -177 -415.5t-458 -141.5t-459 141t-178 416q-2 57 -2 176zM413 1815q0 16 10.5 27t28.5 11h146q18 0 28.5 -11t10.5 -27q0 -121 96 -121t96 121 q0 16 10.5 27t28.5 11h145q18 0 28.5 -11t10.5 -27q0 -129 -79.5 -211t-239.5 -82t-240 82t-80 211zM492 719q0 -111 2 -170q4 -133 67.5 -197.5t171.5 -64.5q107 0 170.5 64.5t67.5 197.5q4 119 4 170t-4 166q-4 133 -67.5 197.5t-170.5 64.5q-109 0 -172 -64.5t-67 -197.5 q-2 -57 -2 -166z" />
<glyph unicode="&#x14f;" horiz-adv-x="1257" d="M80 532q0 66 2 93q12 217 154.5 338.5t392.5 121.5t392 -121.5t155 -338.5q4 -53 4 -93q0 -39 -4 -92q-12 -221 -150.5 -340.5t-396.5 -119.5t-396.5 119.5t-150.5 340.5q-2 27 -2 92zM318 1501q0 16 10 27.5t29 11.5h139q18 0 28.5 -11t10.5 -28q0 -121 94 -121 q92 0 92 121q0 16 10.5 27.5t28.5 11.5h139q18 0 28.5 -11t10.5 -28q0 -129 -79.5 -211t-229.5 -82q-151 0 -231 82t-80 211zM455 532l2 -81q6 -109 47 -161t125 -52t125 52t47 161q2 20 2 81t-2 82q-6 106 -47 159.5t-125 53.5t-125 -53t-47 -160z" />
<glyph unicode="&#x150;" horiz-adv-x="1464" d="M94 713q0 119 2 178q8 270 179 416.5t458 146.5q285 0 456 -146.5t179 -416.5q4 -119 4 -178q0 -61 -4 -176q-10 -274 -177 -415.5t-458 -141.5t-459 141t-178 416q-2 57 -2 176zM405 1569q0 10 10 30l99 220q10 20 27.5 29.5t48.5 9.5h241q18 0 29.5 -10.5t11.5 -28.5 q0 -14 -12 -27l-205 -227q-25 -29 -67 -29h-150q-33 0 -33 33zM492 719q0 -111 2 -170q4 -133 67.5 -197.5t171.5 -64.5q107 0 170.5 64.5t67.5 197.5q4 119 4 170t-4 166q-4 133 -67.5 197.5t-170.5 64.5q-109 0 -172 -64.5t-67 -197.5q-2 -57 -2 -166zM839 1569 q0 10 11 30l98 220q10 20 27.5 29.5t48.5 9.5h241q18 0 29.5 -10.5t11.5 -28.5q0 -16 -12 -27l-203 -227q-25 -29 -67 -29h-152q-33 0 -33 33z" />
<glyph unicode="&#x151;" horiz-adv-x="1257" d="M80 532q0 66 2 93q12 217 154.5 338.5t392.5 121.5t392 -121.5t155 -338.5q4 -53 4 -93q0 -39 -4 -92q-12 -221 -150.5 -340.5t-396.5 -119.5t-396.5 119.5t-150.5 340.5q-2 27 -2 92zM271 1251q0 10 10 31l98 219q10 20 27.5 29.5t48.5 9.5h225q18 0 29.5 -10t11.5 -29 q0 -14 -12 -26l-205 -228q-25 -29 -67 -28h-133q-33 -1 -33 32zM455 532l2 -81q6 -109 47 -161t125 -52t125 52t47 161q2 20 2 81t-2 82q-6 106 -47 159.5t-125 53.5t-125 -53t-47 -160zM717 1251q0 10 10 31l99 219q10 20 27.5 29.5t48.5 9.5h225q18 0 29.5 -10t11.5 -29 q0 -14 -12 -26l-205 -228q-25 -29 -66 -28h-135q-33 -1 -33 32z" />
<glyph unicode="&#x152;" horiz-adv-x="1982" d="M94 717q0 98 2 157q8 270 176 415t469 145h1084q23 0 38 -15.5t15 -38.5v-202q0 -23 -15.5 -37.5t-37.5 -14.5h-594v-260h549q23 0 38 -15t15 -38v-186q0 -23 -15.5 -38.5t-37.5 -15.5h-549v-266h610q23 0 38 -15.5t15 -37.5v-203q0 -23 -15 -37t-38 -14h-1110 q-617 0 -635 561q-2 57 -2 156zM483 719q0 -94 2 -154q4 -135 66.5 -196.5t189.5 -61.5h121v819h-110q-129 0 -196 -62.5t-71 -193.5q-2 -57 -2 -151z" />
<glyph unicode="&#x153;" horiz-adv-x="1949" d="M80 532q0 66 2 93q12 217 154.5 338.5t392.5 121.5q231 0 368 -124q135 125 346 124q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t116 -56.5q47 0 76 19.5t56 50.5q18 20 30.5 25t34.5 5h270q18 0 31.5 -12 t11.5 -31q-2 -53 -62 -123.5t-175 -120.5t-268 -50q-109 0 -198 30.5t-153 87.5q-135 -119 -366 -118q-258 0 -396.5 119.5t-150.5 340.5q-2 27 -2 92zM457 532l2 -81q4 -109 45 -161t125 -52q86 0 127 52t45 161q2 20 2 81t-2 82q-4 106 -45 159.5t-127 53.5 q-84 0 -125 -53t-45 -160zM1182 643h325v4q0 98 -43 152.5t-121 54.5t-119.5 -54.5t-41.5 -152.5v-4z" />
<glyph unicode="&#x154;" horiz-adv-x="1423" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h564q270 0 422.5 -123t152.5 -346q0 -143 -66.5 -243.5t-183.5 -156.5l277 -499q6 -12 6 -23q0 -16 -12.5 -29.5t-30.5 -13.5h-289q-59 0 -84 55l-225 445h-197v-449q0 -23 -15 -37t-38 -14h-281q-20 0 -35.5 15.5t-15.5 35.5z M501 1569q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33zM524 801h228q88 0 134 44t46 122t-46 125t-134 47h-228v-338z" />
<glyph unicode="&#x155;" horiz-adv-x="944" d="M117 51v961q0 23 14 38t37 15h250q23 0 38 -15.5t15 -37.5v-78q57 63 134 97t171 34h92q23 0 37.5 -14.5t14.5 -36.5v-223q0 -20 -14.5 -36t-37.5 -16h-206q-82 0 -126 -45t-44 -127v-516q0 -23 -15.5 -37t-38.5 -14h-270q-21 0 -36 15.5t-15 35.5zM350 1251q0 16 10 27 l182 223q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23t-43.5 -7h-192q-33 -1 -33 32z" />
<glyph unicode="&#x156;" horiz-adv-x="1423" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h564q270 0 422.5 -123t152.5 -346q0 -143 -66.5 -243.5t-183.5 -156.5l277 -499q6 -12 6 -23q0 -16 -12.5 -29.5t-30.5 -13.5h-289q-59 0 -84 55l-225 445h-197v-449q0 -23 -15 -37t-38 -14h-281q-20 0 -35.5 15.5t-15.5 35.5z M500 -440l41 266q4 29 22.5 50.5t53.5 21.5h235q16 0 28.5 -12.5t12.5 -28.5q0 -12 -4 -23l-102 -260q-23 -59 -76 -59h-172q-16 0 -28.5 13t-10.5 32zM524 801h228q88 0 134 44t46 122t-46 125t-134 47h-228v-338z" />
<glyph unicode="&#x157;" horiz-adv-x="944" d="M77 -440l41 266q4 29 22.5 50.5t53.5 21.5h221q16 0 28.5 -12.5t12.5 -28.5q0 -2 -4 -23l-102 -260q-12 -27 -29.5 -43t-48.5 -16h-156q-16 0 -28.5 13t-10.5 32zM117 51v961q0 23 14 38t37 15h250q23 0 38 -15.5t15 -37.5v-78q57 63 134 97t171 34h92q23 0 37.5 -14.5 t14.5 -36.5v-223q0 -20 -14.5 -36t-37.5 -16h-206q-82 0 -126 -45t-44 -127v-516q0 -23 -15.5 -37t-38.5 -14h-270q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="&#x158;" horiz-adv-x="1423" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h564q270 0 422.5 -123t152.5 -346q0 -143 -66.5 -243.5t-183.5 -156.5l277 -499q6 -12 6 -23q0 -16 -12.5 -29.5t-30.5 -13.5h-289q-59 0 -84 55l-225 445h-197v-449q0 -23 -15 -37t-38 -14h-281q-20 0 -35.5 15.5t-15.5 35.5z M279 1827q0 31 31 31h108q45 0 72 -15l180 -98l180 98q27 14 72 15h108q31 0 31 -31q0 -16 -14 -31l-213 -225q-18 -20 -34.5 -27.5t-37.5 -7.5h-184q-21 0 -37 7t-35 28l-213 225q-14 14 -14 31zM524 801h228q88 0 134 44t46 122t-46 125t-134 47h-228v-338z" />
<glyph unicode="&#x159;" horiz-adv-x="944" d="M117 51v961q0 23 14 38t37 15h250q23 0 38 -15.5t15 -37.5v-78q57 63 134 97t171 34h92q23 0 37.5 -14.5t14.5 -36.5v-223q0 -20 -14.5 -36t-37.5 -16h-206q-82 0 -126 -45t-44 -127v-516q0 -23 -15.5 -37t-38.5 -14h-270q-21 0 -36 15.5t-15 35.5zM119 1509q0 31 30 31 h103q45 0 72 -14l180 -99l180 99q27 14 72 14h100q33 0 33 -31q0 -14 -17 -30l-211 -226q-20 -20 -35.5 -27t-37.5 -7h-170q-21 0 -37.5 7t-34.5 27l-213 226q-14 14 -14 30z" />
<glyph unicode="&#x15a;" horiz-adv-x="1363" d="M57 393q0 18 13.5 30.5t31.5 12.5h273q25 0 40 -8t34 -29q27 -49 83 -80.5t150 -31.5q111 0 170 33.5t59 93.5q0 43 -30.5 70.5t-98 49t-194.5 48.5q-248 49 -371 149t-123 287q0 127 71 226t201 154.5t302 55.5q180 0 312 -62.5t202.5 -155.5t74.5 -183q0 -18 -12 -31.5 t-31 -13.5h-286q-45 0 -70 36q-12 43 -63.5 73t-126.5 30q-86 0 -133.5 -31t-47.5 -90t61.5 -93t229.5 -69q193 -35 306.5 -87t168 -135t54.5 -210q0 -139 -80 -241.5t-221.5 -156.5t-323.5 -54q-199 0 -338 57t-211 151.5t-76 204.5zM516 1569q0 16 11 26l182 224 q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#x15b;" horiz-adv-x="1140" d="M61 258q0 20 15.5 33.5t34.5 13.5h247q12 0 21 -8q35 -23 39 -27q39 -29 72.5 -44t80.5 -15q55 0 91 21.5t36 60.5q0 33 -19.5 53.5t-73.5 40t-165 39.5q-354 72 -354 328q0 84 54.5 160.5t160 123.5t252.5 47q150 0 259.5 -46t166.5 -110.5t57 -117.5q0 -18 -13 -32.5 t-32 -14.5h-225q-18 0 -29 8q-25 12 -49 31q-37 25 -64.5 38t-70.5 13q-51 0 -80 -22.5t-29 -57.5q0 -31 17.5 -50t72 -37.5t164.5 -39.5q201 -35 292 -124t91 -208q0 -154 -136 -244.5t-382 -90.5q-168 0 -280.5 47t-167 112.5t-54.5 118.5zM389 1251q0 16 10 27l182 223 q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23t-43.5 -7h-192q-33 -1 -33 32z" />
<glyph unicode="&#x15c;" horiz-adv-x="1363" d="M57 393q0 18 13.5 30.5t31.5 12.5h273q25 0 40 -8t34 -29q27 -49 83 -80.5t150 -31.5q111 0 170 33.5t59 93.5q0 43 -30.5 70.5t-98 49t-194.5 48.5q-248 49 -371 149t-123 287q0 127 71 226t201 154.5t302 55.5q180 0 312 -62.5t202.5 -155.5t74.5 -183q0 -18 -12 -31.5 t-31 -13.5h-286q-45 0 -70 36q-12 43 -63.5 73t-126.5 30q-86 0 -133.5 -31t-47.5 -90t61.5 -93t229.5 -69q193 -35 306.5 -87t168 -135t54.5 -210q0 -139 -80 -241.5t-221.5 -156.5t-323.5 -54q-199 0 -338 57t-211 151.5t-76 204.5zM293 1565q0 18 14 32l213 224 q20 20 35.5 28.5t36.5 8.5h184q20 0 35.5 -8.5t36.5 -28.5l213 -224q14 -14 14 -32q0 -29 -31 -29h-108q-45 0 -72 14l-180 99l-180 -99q-27 -14 -72 -14h-108q-31 0 -31 29z" />
<glyph unicode="&#x15d;" horiz-adv-x="1140" d="M61 258q0 20 15.5 33.5t34.5 13.5h247q12 0 21 -8q35 -23 39 -27q39 -29 72.5 -44t80.5 -15q55 0 91 21.5t36 60.5q0 33 -19.5 53.5t-73.5 40t-165 39.5q-354 72 -354 328q0 84 54.5 160.5t160 123.5t252.5 47q150 0 259.5 -46t166.5 -110.5t57 -117.5q0 -18 -13 -32.5 t-32 -14.5h-225q-18 0 -29 8q-25 12 -49 31q-37 25 -64.5 38t-70.5 13q-51 0 -80 -22.5t-29 -57.5q0 -31 17.5 -50t72 -37.5t164.5 -39.5q201 -35 292 -124t91 -208q0 -154 -136 -244.5t-382 -90.5q-168 0 -280.5 47t-167 112.5t-54.5 118.5zM187 1247q0 18 14 33l213 223 q20 20 35.5 28.5t36.5 8.5h170q23 0 38 -7t35 -30l211 -223q16 -16 17 -33q0 -29 -33 -28h-100q-45 0 -72 14l-180 98l-180 -98q-27 -14 -72 -14h-103q-30 -1 -30 28z" />
<glyph unicode="&#x15e;" horiz-adv-x="1363" d="M57 393q0 18 13.5 30.5t31.5 12.5h273q25 0 40 -8t34 -29q27 -49 83 -80.5t150 -31.5q111 0 170 33.5t59 93.5q0 43 -30.5 70.5t-98 49t-194.5 48.5q-248 49 -371 149t-123 287q0 127 71 226t201 154.5t302 55.5q180 0 312 -62.5t202.5 -155.5t74.5 -183q0 -18 -12 -31.5 t-31 -13.5h-286q-45 0 -70 36q-12 43 -63.5 73t-126.5 30q-86 0 -133.5 -31t-47.5 -90t61.5 -93t229.5 -69q193 -35 306.5 -87t168 -135t54.5 -210q0 -137 -79 -239.5t-219.5 -156.5t-322.5 -56l-39 -80q14 6 32.5 9t33.5 3q74 0 119 -50t45 -130t-54.5 -131.5t-146.5 -51.5 q-66 0 -118 28t-52 56q0 12 10 21l37 33q10 10 22 10q8 0 37 -13.5t64 -13.5q31 0 49 17.5t18 44.5q0 25 -18 41t-49 16q-16 0 -44 -7t-40 -7q-18 0 -31 12l-39 41q-12 12 -12 27q0 14 12 39l41 92q-162 16 -274.5 75.5t-171 146.5t-60.5 185z" />
<glyph unicode="&#x15f;" horiz-adv-x="1140" d="M61 258q0 20 15.5 33.5t34.5 13.5h235q16 0 27 -8q16 -8 29.5 -16.5t23.5 -14.5q37 -27 68.5 -41t76.5 -14q55 0 91 21.5t36 60.5q0 33 -19.5 53.5t-73.5 40t-165 39.5q-354 72 -354 328q0 84 54.5 160.5t160 123.5t252.5 47q150 0 259.5 -46t166.5 -110.5t57 -117.5 q0 -18 -13 -32.5t-32 -14.5h-227q-16 0 -27 8q-25 12 -49 31q-37 25 -64.5 38t-70.5 13q-51 0 -80 -22.5t-29 -57.5q0 -31 17.5 -50t72 -37.5t164.5 -39.5q201 -35 292 -124t91 -208q0 -152 -134 -242.5t-376 -92.5l-41 -80q14 6 34 9t34 3q74 0 118 -50t44 -130 t-53.5 -131.5t-145.5 -51.5q-67 0 -119.5 28t-52.5 56q0 12 10 21l39 33q10 10 23 10q8 0 36.5 -13.5t63.5 -13.5q31 0 49.5 17.5t18.5 44.5q0 25 -18.5 41t-49.5 16q-18 0 -46 -7t-40 -7q-16 0 -29 12l-40 41q-12 12 -13 25q0 12 15 41l41 94q-129 14 -216.5 61 t-129.5 104.5t-42 104.5z" />
<glyph unicode="&#x160;" horiz-adv-x="1363" d="M57 393q0 18 13.5 30.5t31.5 12.5h273q25 0 40 -8t34 -29q27 -49 83 -80.5t150 -31.5q111 0 170 33.5t59 93.5q0 43 -30.5 70.5t-98 49t-194.5 48.5q-248 49 -371 149t-123 287q0 127 71 226t201 154.5t302 55.5q180 0 312 -62.5t202.5 -155.5t74.5 -183q0 -18 -12 -31.5 t-31 -13.5h-286q-45 0 -70 36q-12 43 -63.5 73t-126.5 30q-86 0 -133.5 -31t-47.5 -90t61.5 -93t229.5 -69q193 -35 306.5 -87t168 -135t54.5 -210q0 -139 -80 -241.5t-221.5 -156.5t-323.5 -54q-199 0 -338 57t-211 151.5t-76 204.5zM293 1827q0 31 31 31h108q45 0 72 -15 l180 -98l180 98q27 14 72 15h108q31 0 31 -31q0 -16 -14 -31l-213 -225q-18 -20 -34.5 -27.5t-37.5 -7.5h-184q-21 0 -37 7t-35 28l-213 225q-14 14 -14 31z" />
<glyph unicode="&#x161;" horiz-adv-x="1140" d="M61 258q0 20 15.5 33.5t34.5 13.5h247q12 0 21 -8q35 -23 39 -27q39 -29 72.5 -44t80.5 -15q55 0 91 21.5t36 60.5q0 33 -19.5 53.5t-73.5 40t-165 39.5q-354 72 -354 328q0 84 54.5 160.5t160 123.5t252.5 47q150 0 259.5 -46t166.5 -110.5t57 -117.5q0 -18 -13 -32.5 t-32 -14.5h-225q-18 0 -29 8q-25 12 -49 31q-37 25 -64.5 38t-70.5 13q-51 0 -80 -22.5t-29 -57.5q0 -31 17.5 -50t72 -37.5t164.5 -39.5q201 -35 292 -124t91 -208q0 -154 -136 -244.5t-382 -90.5q-168 0 -280.5 47t-167 112.5t-54.5 118.5zM187 1509q0 31 30 31h103 q45 0 72 -14l180 -99l180 99q27 14 72 14h100q33 0 33 -31q0 -14 -17 -30l-211 -226q-20 -20 -35.5 -27t-37.5 -7h-170q-21 0 -37.5 7t-34.5 27l-213 226q-14 14 -14 30z" />
<glyph unicode="&#x164;" horiz-adv-x="1300" d="M41 1151v229q0 23 15.5 38.5t37.5 15.5h1112q23 0 38.5 -15.5t15.5 -38.5v-229q0 -23 -15.5 -37t-38.5 -14h-360v-1049q0 -23 -15.5 -37t-37.5 -14h-285q-23 0 -38 14.5t-15 36.5v1049h-361q-23 0 -38 14t-15 37zM260 1827q0 31 31 31h108q45 0 72 -15l180 -98l180 98 q27 14 72 15h108q31 0 31 -31q0 -16 -14 -31l-213 -225q-18 -20 -34.5 -27.5t-37.5 -7.5h-184q-21 0 -37 7t-35 28l-213 225q-14 14 -14 31z" />
<glyph unicode="&#x165;" horiz-adv-x="937" d="M29 838v176q0 23 15 37t36 14h162v338q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-338h256q23 0 38 -14.5t15 -36.5v-176q0 -23 -15.5 -37.5t-37.5 -14.5h-256v-348q0 -72 26.5 -108.5t86.5 -36.5h161q23 0 37.5 -15.5t14.5 -37.5v-189q0 -20 -14.5 -35.5 t-37.5 -15.5h-194q-434 0 -434 408v378h-162q-20 0 -35.5 15.5t-15.5 36.5zM731 1295l41 266q4 29 22.5 50.5t53.5 21.5h221q16 0 28.5 -12.5t12.5 -28.5q0 -2 -4 -23l-102 -260q-12 -27 -29.5 -43t-48.5 -16h-156q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x166;" horiz-adv-x="1318" d="M51 1151v229q0 23 14.5 38.5t36.5 15.5h1112q23 0 38.5 -15.5t15.5 -38.5v-229q0 -23 -15.5 -37t-38.5 -14h-360v-297h203q23 0 38 -15.5t15 -37.5v-107q0 -23 -15.5 -37t-37.5 -14h-203v-541q0 -23 -15.5 -37t-37.5 -14h-285q-20 0 -35.5 15.5t-15.5 35.5v541h-205 q-20 0 -35.5 15.5t-15.5 35.5v107q0 23 14.5 38t36.5 15h205v297h-363q-20 0 -35.5 15t-15.5 36z" />
<glyph unicode="&#x167;" horiz-adv-x="978" d="M33 621v79q0 23 15 37.5t38 14.5h160v131h-160q-23 0 -38 14t-15 37v80q0 23 15 37t38 14h160v338q0 23 15 37t38 14h250q23 0 37 -14.5t14 -36.5v-338h258q23 0 37 -14.5t14 -36.5v-80q0 -20 -14 -35.5t-37 -15.5h-258v-131h258q23 0 37 -14.5t14 -37.5v-79 q0 -20 -14 -36t-37 -16h-258v-131q0 -145 115 -145h159q23 0 38.5 -15.5t15.5 -37.5v-189q0 -23 -15.5 -37t-38.5 -14h-194q-434 0 -434 408v161h-160q-23 0 -38 14.5t-15 37.5z" />
<glyph unicode="&#x168;" horiz-adv-x="1490" d="M127 545v835q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-831q0 -127 60.5 -190.5t172.5 -63.5q113 0 173.5 64.5t60.5 189.5v831q0 23 15.5 38.5t37.5 15.5h279q23 0 38 -15.5t15 -38.5v-835q0 -283 -164 -424t-452 -141q-291 0 -456 141t-165 424zM383 1571 q0 53 23.5 115.5t76.5 107.5t135 45q43 0 74 -11t67 -32q20 -10 43 -19.5t43 -9.5t32 8.5t24 24.5q10 14 18 21.5t23 7.5h129q16 0 26.5 -10.5t10.5 -26.5q0 -51 -25 -113.5t-78 -107.5t-135 -45q-41 0 -69.5 10t-69.5 31q-27 16 -46.5 23t-41.5 7q-20 0 -31.5 -8t-24.5 -24 q-10 -14 -18 -21.5t-23 -7.5h-126q-14 0 -25.5 10t-11.5 25z" />
<glyph unicode="&#x169;" horiz-adv-x="1318" d="M106 442v572q0 23 15.5 37t36.5 14h272q23 0 38 -14.5t15 -36.5v-559q0 -190 170 -191q82 0 128 50.5t46 140.5v559q0 23 15.5 37t38.5 14h270q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5v78q-106 -150 -340 -149 q-180 0 -291 120.5t-111 341.5zM297 1253q0 53 23.5 115t75.5 106t134 44q41 0 70 -10.5t67 -30.5q29 -16 46.5 -23.5t39.5 -7.5q20 0 31.5 8t24.5 27q10 14 18 21t23 7h129q16 0 26.5 -11t10.5 -25q0 -53 -25 -115t-77 -106t-134 -44q-41 0 -67.5 10.5t-69.5 30.5 q-49 31 -86 31q-20 0 -31.5 -8t-23.5 -25q-10 -14 -18.5 -21t-22.5 -7h-127q-14 0 -25.5 10t-11.5 24z" />
<glyph unicode="&#x16a;" horiz-adv-x="1490" d="M127 545v835q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-831q0 -127 60.5 -190.5t172.5 -63.5q113 0 173.5 64.5t60.5 189.5v831q0 23 15.5 38.5t37.5 15.5h279q23 0 38 -15.5t15 -38.5v-835q0 -283 -164 -424t-452 -141q-291 0 -456 141t-165 424zM373 1583 v156q0 20 12 33.5t33 13.5h651q20 0 33.5 -13.5t13.5 -33.5v-156q0 -20 -13 -33.5t-34 -13.5h-651q-21 0 -33 13.5t-12 33.5z" />
<glyph unicode="&#x16b;" horiz-adv-x="1318" d="M106 442v572q0 23 15.5 37t36.5 14h272q23 0 38 -14.5t15 -36.5v-559q0 -190 170 -191q82 0 128 50.5t46 140.5v559q0 23 15.5 37t38.5 14h270q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5v78q-106 -150 -340 -149 q-180 0 -291 120.5t-111 341.5zM318 1284v152q0 20 12 32.5t33 12.5h583q20 0 33 -12.5t13 -32.5v-152q0 -20 -12.5 -33.5t-33.5 -13.5h-583q-21 0 -33 13.5t-12 33.5z" />
<glyph unicode="&#x16c;" horiz-adv-x="1490" d="M127 545v835q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-831q0 -127 60.5 -190.5t172.5 -63.5q113 0 173.5 64.5t60.5 189.5v831q0 23 15.5 38.5t37.5 15.5h279q23 0 38 -15.5t15 -38.5v-835q0 -283 -164 -424t-452 -141q-291 0 -456 141t-165 424zM426 1815 q0 16 10.5 27t28.5 11h146q18 0 28.5 -11t10.5 -27q0 -121 96 -121t96 121q0 16 10.5 27t28.5 11h145q18 0 28.5 -11t10.5 -27q0 -129 -79.5 -211t-239.5 -82t-240 82t-80 211z" />
<glyph unicode="&#x16d;" horiz-adv-x="1318" d="M106 442v572q0 23 15.5 37t36.5 14h272q23 0 38 -14.5t15 -36.5v-559q0 -190 170 -191q82 0 128 50.5t46 140.5v559q0 23 15.5 37t38.5 14h270q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5v78q-106 -150 -340 -149 q-180 0 -291 120.5t-111 341.5zM344 1501q0 16 10 27.5t29 11.5h139q18 0 28.5 -11t10.5 -28q0 -121 94 -121q92 0 92 121q0 16 10.5 27.5t28.5 11.5h139q18 0 28.5 -11t10.5 -28q0 -129 -79.5 -211t-229.5 -82q-151 0 -231 82t-80 211z" />
<glyph unicode="&#x16e;" horiz-adv-x="1490" d="M127 545v835q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-831q0 -127 60.5 -190.5t172.5 -63.5q113 0 173.5 64.5t60.5 189.5v831q0 23 15.5 38.5t37.5 15.5h279q23 0 38 -15.5t15 -38.5v-835q0 -283 -164 -424t-452 -141q-291 0 -456 141t-165 424zM555 1696 q0 78 55.5 130t135.5 52t135 -52t55 -130t-55 -129t-135 -51t-135.5 51t-55.5 129zM684 1696q0 -27 17.5 -44.5t44.5 -17.5q26 0 43.5 17.5t17.5 44.5t-17.5 44t-43.5 17q-27 0 -44.5 -17.5t-17.5 -43.5z" />
<glyph unicode="&#x16f;" horiz-adv-x="1318" d="M106 442v572q0 23 15.5 37t36.5 14h272q23 0 38 -14.5t15 -36.5v-559q0 -190 170 -191q82 0 128 50.5t46 140.5v559q0 23 15.5 37t38.5 14h270q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5v78q-106 -150 -340 -149 q-180 0 -291 120.5t-111 341.5zM463 1389q0 78 55 130t135 52t135.5 -52.5t55.5 -129.5q0 -78 -55.5 -129.5t-135.5 -51.5t-135 51.5t-55 129.5zM592 1389q0 -27 17.5 -44.5t43.5 -17.5q27 0 44.5 17.5t17.5 44.5t-17.5 44t-44.5 17t-44 -17.5t-17 -43.5z" />
<glyph unicode="&#x170;" horiz-adv-x="1490" d="M127 545v835q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-831q0 -127 60.5 -190.5t172.5 -63.5q113 0 173.5 64.5t60.5 189.5v831q0 23 15.5 38.5t37.5 15.5h279q23 0 38 -15.5t15 -38.5v-835q0 -283 -164 -424t-452 -141q-291 0 -456 141t-165 424zM418 1569 q0 10 10 30l99 220q10 20 27.5 29.5t48.5 9.5h241q18 0 29.5 -10.5t11.5 -28.5q0 -14 -12 -27l-205 -227q-25 -29 -67 -29h-150q-33 0 -33 33zM852 1569q0 10 11 30l98 220q10 20 27.5 29.5t48.5 9.5h241q18 0 29.5 -10.5t11.5 -28.5q0 -16 -12 -27l-203 -227 q-25 -29 -67 -29h-152q-33 0 -33 33z" />
<glyph unicode="&#x171;" horiz-adv-x="1318" d="M106 442v572q0 23 15.5 37t36.5 14h272q23 0 38 -14.5t15 -36.5v-559q0 -190 170 -191q82 0 128 50.5t46 140.5v559q0 23 15.5 37t38.5 14h270q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5v78q-106 -150 -340 -149 q-180 0 -291 120.5t-111 341.5zM297 1251q0 10 10 31l98 219q10 20 27.5 29.5t48.5 9.5h225q18 0 29.5 -10t11.5 -29q0 -14 -12 -26l-205 -228q-25 -29 -67 -28h-133q-33 -1 -33 32zM743 1251q0 10 10 31l99 219q10 20 27.5 29.5t48.5 9.5h225q18 0 29.5 -10t11.5 -29 q0 -14 -12 -26l-205 -228q-25 -29 -66 -28h-135q-33 -1 -33 32z" />
<glyph unicode="&#x172;" horiz-adv-x="1490" d="M127 545v835q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-831q0 -127 60.5 -190.5t172.5 -63.5q113 0 173.5 64.5t60.5 189.5v831q0 23 15.5 38.5t37.5 15.5h279q23 0 38 -15.5t15 -38.5v-835q0 -244 -124 -380t-347 -175q-53 -10 -77.5 -38t-24.5 -75 q0 -51 30.5 -87t89.5 -36h25q23 0 37 -15t14 -36v-102q0 -23 -14.5 -37.5t-36.5 -14.5h-33q-160 0 -242.5 90.5t-82.5 237.5q0 61 16 115q-221 31 -344 169t-123 384z" />
<glyph unicode="&#x173;" horiz-adv-x="1318" d="M106 442v572q0 23 15.5 37t36.5 14h272q23 0 38 -14.5t15 -36.5v-559q0 -190 170 -191q82 0 128 50.5t46 140.5v559q0 23 15.5 37t38.5 14h270q23 0 37 -14.5t14 -36.5v-963q0 -18 -13 -33.5t-34 -17.5l-25 -2q-53 -10 -77.5 -44t-24.5 -83q0 -51 25.5 -84t81.5 -33h16 q23 0 37 -15t14 -36v-102q0 -23 -14 -37.5t-37 -14.5h-25q-149 0 -230 88.5t-81 233.5q0 84 62 154q-12 4 -20.5 15t-8.5 26v63q-106 -150 -340 -149q-180 0 -291 120.5t-111 341.5z" />
<glyph unicode="&#x174;" horiz-adv-x="1705" d="M66 1391q0 16 13 29.5t30 13.5h268q59 0 65 -48l125 -774l142 449q16 55 67 55h154q51 0 67 -55l142 -447l127 772q4 25 18 36.5t45 11.5h268q18 0 30.5 -13.5t12.5 -29.5v-13l-219 -1310q-4 -29 -26.5 -48.5t-55.5 -19.5h-206q-33 0 -51.5 16.5t-24.5 36.5l-203 568 l-203 -568q-8 -20 -26.5 -36.5t-51.5 -16.5h-204q-35 0 -56.5 19.5t-25.5 48.5l-219 1310q-2 4 -2 13zM463 1565q0 18 14 32l213 224q20 20 35.5 28.5t36.5 8.5h184q20 0 35.5 -8.5t36.5 -28.5l213 -224q14 -14 14 -32q0 -29 -31 -29h-108q-45 0 -72 14l-180 99l-180 -99 q-27 -14 -72 -14h-108q-31 0 -31 29z" />
<glyph unicode="&#x175;" horiz-adv-x="1722" d="M49 1018q0 20 14.5 33.5t32.5 13.5h223q25 0 41.5 -13.5t20.5 -29.5l168 -567l178 563q4 18 20.5 32.5t43.5 14.5h141q27 0 43 -14.5t22 -32.5l179 -563l165 567q6 16 21.5 29.5t40.5 13.5h223q20 0 33.5 -13.5t13.5 -33.5q0 -10 -2 -19l-297 -948q-14 -51 -65 -51h-195 q-51 0 -70 51l-182 545l-184 -545q-14 -51 -68 -51h-194q-29 0 -44.5 13.5t-23.5 37.5l-295 948zM477 1247q0 18 14 33l213 223q20 20 35.5 28.5t36.5 8.5h170q23 0 38 -7t35 -30l211 -223q16 -16 17 -33q0 -29 -33 -28h-100q-45 0 -72 14l-180 98l-180 -98q-27 -14 -72 -14 h-103q-30 -1 -30 28z" />
<glyph unicode="&#x176;" horiz-adv-x="1400" d="M29 1391q0 16 13 29.5t30 13.5h270q29 0 47.5 -14.5t26.5 -30.5l284 -515l285 515q8 16 26.5 30.5t47.5 14.5h270q16 0 29.5 -13.5t13.5 -29.5q0 -12 -6 -23l-473 -881v-436q0 -23 -15.5 -37t-37.5 -14h-281q-20 0 -35.5 15.5t-15.5 35.5v436l-473 881q-6 10 -6 23z M309 1565q0 18 14 32l213 224q20 20 35.5 28.5t36.5 8.5h184q20 0 35.5 -8.5t36.5 -28.5l213 -224q14 -14 14 -32q0 -29 -31 -29h-108q-45 0 -72 14l-180 99l-180 -99q-27 -14 -72 -14h-108q-31 0 -31 29z" />
<glyph unicode="&#x177;" horiz-adv-x="1230" d="M41 1018q4 20 17.5 33.5t31.5 13.5h244q43 0 59 -43l228 -582l233 582q20 43 61 43h242q18 0 31.5 -13.5t13.5 -29.5q0 -14 -4 -27l-580 -1341q-20 -43 -63 -43h-238q-18 0 -30.5 12t-12.5 31q0 14 5 27l161 385l-393 929q-6 16 -6 23zM236 1247q0 18 14 33l213 223 q20 20 35.5 28.5t36.5 8.5h170q23 0 38 -7t35 -30l211 -223q16 -16 17 -33q0 -29 -33 -28h-100q-45 0 -72 14l-180 98l-180 -98q-27 -14 -72 -14h-103q-30 -1 -30 28z" />
<glyph unicode="&#x178;" horiz-adv-x="1400" d="M29 1391q0 16 13 29.5t30 13.5h270q29 0 47.5 -14.5t26.5 -30.5l284 -515l285 515q8 16 26.5 30.5t47.5 14.5h270q16 0 29.5 -13.5t13.5 -29.5q0 -12 -6 -23l-473 -881v-436q0 -23 -15.5 -37t-37.5 -14h-281q-20 0 -35.5 15.5t-15.5 35.5v436l-473 881q-6 10 -6 23z M309 1583v195q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-195q0 -20 -13.5 -33.5t-33.5 -13.5h-197q-20 0 -32.5 13.5t-12.5 33.5zM800 1583v195q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-195q0 -20 -13 -33.5t-34 -13.5h-194 q-21 0 -34.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x179;" horiz-adv-x="1341" d="M76 53v209q0 27 8 41t22 33l652 780h-611q-20 0 -35.5 15.5t-15.5 35.5v213q0 23 14.5 38.5t36.5 15.5h1043q23 0 38 -15.5t15 -38.5v-206q0 -39 -26 -70l-629 -782h626q23 0 38.5 -15.5t15.5 -38.5v-217q0 -23 -15.5 -37t-38.5 -14h-1085q-23 0 -38 15.5t-15 37.5z M483 1569q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#x17a;" horiz-adv-x="1140" d="M78 51v170q0 37 31 68l473 502h-437q-20 0 -35.5 15t-15.5 36v172q0 23 15.5 37t35.5 14h834q23 0 37 -14.5t14 -36.5v-185q0 -33 -29 -61l-458 -494h479q23 0 37 -14t14 -37v-172q0 -20 -14.5 -35.5t-36.5 -15.5h-893q-20 0 -35.5 15.5t-15.5 35.5zM424 1251q0 16 10 27 l182 223q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23t-43.5 -7h-192q-33 -1 -33 32z" />
<glyph unicode="&#x17b;" horiz-adv-x="1341" d="M76 53v209q0 27 8 41t22 33l652 780h-611q-20 0 -35.5 15.5t-15.5 35.5v213q0 23 14.5 38.5t36.5 15.5h1043q23 0 38 -15.5t15 -38.5v-206q0 -39 -26 -70l-629 -782h626q23 0 38.5 -15.5t15.5 -38.5v-217q0 -23 -15.5 -37t-38.5 -14h-1085q-23 0 -38 15.5t-15 37.5z M496 1583v217q0 20 12.5 33.5t32.5 13.5h219q20 0 33.5 -13t13.5 -34v-217q0 -20 -13 -33.5t-34 -13.5h-219q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x17c;" horiz-adv-x="1140" d="M78 51v170q0 37 31 68l473 502h-437q-20 0 -35.5 15t-15.5 36v172q0 23 15.5 37t35.5 14h834q23 0 37 -14.5t14 -36.5v-185q0 -33 -29 -61l-458 -494h479q23 0 37 -14t14 -37v-172q0 -20 -14.5 -35.5t-36.5 -15.5h-893q-20 0 -35.5 15.5t-15.5 35.5zM424 1280v211 q0 20 12 33.5t33 13.5h213q20 0 33.5 -13.5t13.5 -33.5v-211q0 -20 -13.5 -33.5t-33.5 -13.5h-213q-21 0 -33 13.5t-12 33.5z" />
<glyph unicode="&#x17d;" horiz-adv-x="1341" d="M76 53v209q0 27 8 41t22 33l652 780h-611q-20 0 -35.5 15.5t-15.5 35.5v213q0 23 14.5 38.5t36.5 15.5h1043q23 0 38 -15.5t15 -38.5v-206q0 -39 -26 -70l-629 -782h626q23 0 38.5 -15.5t15.5 -38.5v-217q0 -23 -15.5 -37t-38.5 -14h-1085q-23 0 -38 15.5t-15 37.5z M260 1827q0 31 31 31h108q45 0 72 -15l180 -98l180 98q27 14 72 15h108q31 0 31 -31q0 -16 -14 -31l-213 -225q-18 -20 -34.5 -27.5t-37.5 -7.5h-184q-21 0 -37 7t-35 28l-213 225q-14 14 -14 31z" />
<glyph unicode="&#x17e;" horiz-adv-x="1140" d="M78 51v170q0 37 31 68l473 502h-437q-20 0 -35.5 15t-15.5 36v172q0 23 15.5 37t35.5 14h834q23 0 37 -14.5t14 -36.5v-185q0 -33 -29 -61l-458 -494h479q23 0 37 -14t14 -37v-172q0 -20 -14.5 -35.5t-36.5 -15.5h-893q-20 0 -35.5 15.5t-15.5 35.5zM191 1509q0 31 30 31 h103q45 0 72 -14l180 -99l180 99q27 14 72 14h100q33 0 33 -31q0 -14 -17 -30l-211 -226q-20 -20 -35.5 -27t-37.5 -7h-170q-21 0 -37.5 7t-34.5 27l-213 226q-14 14 -14 30z" />
<glyph unicode="&#x17f;" horiz-adv-x="714" d="M35 838v176q0 23 15.5 37t35.5 14h156v59q0 207 117.5 299.5t340.5 92.5h162q23 0 37 -14.5t14 -37.5v-176q0 -20 -14 -35.5t-37 -15.5h-145q-68 0 -94.5 -30.5t-26.5 -90.5v-1065q0 -20 -14.5 -35.5t-36.5 -15.5h-252q-21 0 -36 15.5t-15 35.5v735h-156 q-20 0 -35.5 15.5t-15.5 36.5z" />
<glyph unicode="&#x192;" horiz-adv-x="983" d="M4 -197q0 23 14.5 38.5t36.5 15.5h60q82 0 116.5 44t34.5 140v745h-153q-23 0 -38.5 14.5t-15.5 37.5v176q0 23 15.5 37t38.5 14h153v61q0 205 119 297.5t340 92.5h162q23 0 38 -14.5t15 -37.5v-176q0 -23 -15.5 -37t-37.5 -14h-146q-66 0 -93 -29.5t-27 -91.5v-51h245 q23 0 37.5 -14.5t14.5 -36.5v-176q0 -20 -14.5 -36t-37.5 -16h-245v-759q0 -244 -125 -366t-381 -122h-60q-20 0 -35.5 14.5t-15.5 36.5v213z" />
<glyph unicode="&#x1fc;" horiz-adv-x="2000" d="M8 43q0 4 4 20q100 287 212 558.5t343 754.5q8 25 28.5 41.5t51.5 16.5h1196q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-606v-260h561q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-561v-266h623q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37 t-37.5 -14h-942q-20 0 -36 15.5t-16 35.5v193h-383q-74 -141 -94 -199q-16 -45 -67 -45h-269q-18 0 -31.5 13.5t-13.5 29.5zM575 551h291v571h-45zM833 1569q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225 q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#x1fd;" horiz-adv-x="1878" d="M53 299q0 139 115 227t319 121l252 39v25q0 74 -29.5 108.5t-103.5 34.5q-43 0 -70.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -32.5 13t-12.5 32q0 55 53 123.5t163 117.5t269 49q231 0 336 -110q133 111 328 110q186 0 307 -77.5t175 -201.5 t54 -267v-52q0 -23 -15 -38t-38 -15h-643v-12q2 -102 45 -153.5t113 -51.5q80 0 135 70q16 20 28.5 25t36.5 5h267q18 0 30.5 -12t12.5 -31q0 -51 -59.5 -121.5t-174 -121.5t-274.5 -51q-129 0 -229.5 40.5t-165.5 116.5q-143 -158 -410 -157q-125 0 -219 41.5t-144.5 114.5 t-50.5 163zM401 326q0 -47 40 -76t100 -29q92 0 147 58.5t55 173.5v24l-168 -28q-174 -33 -174 -123zM791 1251q0 16 10 27l182 223q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23t-43.5 -7h-192q-33 -1 -33 32zM1112 643 h320v8q0 96 -41 149.5t-119 53.5q-70 0 -114 -51t-46 -152v-8z" />
<glyph unicode="&#x1fe;" horiz-adv-x="1529" d="M25 369v161q0 27 10 39.5t35 22.5l61 24l-2 115q0 53 4 160q10 270 180 416.5t457 146.5q221 0 374.5 -92t215.5 -266l84 32q16 6 22 7q18 0 30.5 -11.5t12.5 -29.5v-160q0 -27 -10 -39t-35 -23l-57 -22q2 -45 2 -135q0 -121 -2 -178q-10 -274 -178 -415.5t-459 -141.5 q-240 0 -394.5 96t-209.5 286l-76 -28q-16 -6 -24 -6q-18 0 -29.5 11t-11.5 30zM528 772l469 182q-18 98 -77.5 145.5t-149.5 47.5q-106 0 -169.5 -64.5t-68.5 -197.5q-4 -41 -4 -113zM535 508q14 -113 74.5 -167t160.5 -54q107 0 170.5 64.5t67.5 197.5q4 100 2 145z M598 1569q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#x1ff;" horiz-adv-x="1316" d="M20 246v106q0 27 10.5 39t35.5 23l47 16q-2 33 -2 98q0 66 2 97q12 213 152.5 336.5t393.5 123.5q186 0 309.5 -65.5t182.5 -185.5l78 30q16 6 22 6q18 0 30.5 -11t12.5 -30v-106q0 -27 -10 -39t-35 -22l-43 -17q4 -70 4 -123l-2 -82q-12 -217 -148 -338.5t-401 -121.5 q-192 0 -316 67.5t-183 192.5l-74 -29q-16 -6 -23 -6q-18 0 -30.5 11t-12.5 30zM487 606l2 -31l330 130q-29 123 -160 122q-158 0 -170 -190zM492 1251q0 16 10 27l182 223q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23 t-43.5 -7h-192q-33 -1 -33 32zM498 371q27 -133 161 -133q84 0 126 49t46 159v54z" />
<glyph unicode="&#x218;" horiz-adv-x="1363" d="M57 393q0 18 13.5 30.5t31.5 12.5h273q25 0 40 -8t34 -29q27 -49 83 -80.5t150 -31.5q111 0 170 33.5t59 93.5q0 43 -30.5 70.5t-98 49t-194.5 48.5q-248 49 -371 149t-123 287q0 127 71 226t201 154.5t302 55.5q180 0 312 -62.5t202.5 -155.5t74.5 -183q0 -18 -12 -31.5 t-31 -13.5h-286q-45 0 -70 36q-12 43 -63.5 73t-126.5 30q-86 0 -133.5 -31t-47.5 -90t61.5 -93t229.5 -69q193 -35 306.5 -87t168 -135t54.5 -210q0 -139 -80 -241.5t-221.5 -156.5t-323.5 -54q-199 0 -338 57t-211 151.5t-76 204.5zM457 -440l41 266q4 29 22.5 50.5 t53.5 21.5h235q16 0 28.5 -12.5t12.5 -28.5q0 -12 -4 -23l-102 -260q-23 -59 -76 -59h-172q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x219;" horiz-adv-x="1140" d="M61 258q0 20 15.5 33.5t34.5 13.5h247q12 0 21 -8q35 -23 39 -27q39 -29 72.5 -44t80.5 -15q55 0 91 21.5t36 60.5q0 33 -19.5 53.5t-73.5 40t-165 39.5q-354 72 -354 328q0 84 54.5 160.5t160 123.5t252.5 47q150 0 259.5 -46t166.5 -110.5t57 -117.5q0 -18 -13 -32.5 t-32 -14.5h-225q-18 0 -29 8q-25 12 -49 31q-37 25 -64.5 38t-70.5 13q-51 0 -80 -22.5t-29 -57.5q0 -31 17.5 -50t72 -37.5t164.5 -39.5q201 -35 292 -124t91 -208q0 -154 -136 -244.5t-382 -90.5q-168 0 -280.5 47t-167 112.5t-54.5 118.5zM356 -440l41 266 q4 29 22.5 50.5t53.5 21.5h221q16 0 28.5 -12.5t12.5 -28.5q0 -2 -4 -23l-102 -260q-12 -27 -29.5 -43t-48.5 -16h-156q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x21a;" horiz-adv-x="1300" d="M41 1151v229q0 23 15.5 38.5t37.5 15.5h1112q23 0 38.5 -15.5t15.5 -38.5v-229q0 -23 -15.5 -37t-38.5 -14h-360v-1049q0 -23 -15.5 -37t-37.5 -14h-285q-23 0 -38 14.5t-15 36.5v1049h-361q-23 0 -38 14t-15 37zM424 -440l41 266q4 29 22.5 50.5t53.5 21.5h235 q16 0 28.5 -12.5t12.5 -28.5q0 -12 -4 -23l-102 -260q-23 -59 -76 -59h-172q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x21b;" horiz-adv-x="970" d="M29 838v176q0 23 15 37t36 14h162v338q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-338h256q23 0 38 -14.5t15 -36.5v-176q0 -23 -15.5 -37.5t-37.5 -14.5h-256v-348q0 -72 26.5 -108.5t86.5 -36.5h161q23 0 37.5 -15.5t14.5 -37.5v-189q0 -20 -14.5 -35.5 t-37.5 -15.5h-194q-434 0 -434 408v378h-162q-20 0 -35.5 15.5t-15.5 36.5zM288 -440l41 266q4 29 22.5 50.5t53.5 21.5h221q16 0 28.5 -12.5t12.5 -28.5q0 -2 -4 -23l-102 -260q-12 -27 -29.5 -43t-48.5 -16h-156q-16 0 -28.5 13t-10.5 32z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1001" d="M117 1247q0 18 14 33l213 223q20 20 35.5 28.5t36.5 8.5h170q23 0 38 -7t35 -30l211 -223q16 -16 17 -33q0 -29 -33 -28h-100q-45 0 -72 14l-180 98l-180 -98q-27 -14 -72 -14h-103q-30 -1 -30 28z" />
<glyph unicode="&#x2da;" horiz-adv-x="614" d="M117 1389q0 78 55 130t135 52t135.5 -52.5t55.5 -129.5q0 -78 -55.5 -129.5t-135.5 -51.5t-135 51.5t-55 129.5zM246 1389q0 -27 17.5 -44.5t43.5 -17.5q27 0 44.5 17.5t17.5 44.5t-17.5 44t-44.5 17t-44 -17.5t-17 -43.5z" />
<glyph unicode="&#x2dc;" horiz-adv-x="950" d="M117 1253q0 53 23.5 115t75.5 106t134 44q41 0 70 -10.5t67 -30.5q29 -16 46.5 -23.5t39.5 -7.5q20 0 31.5 8t24.5 27q10 14 18 21t23 7h129q16 0 26.5 -11t10.5 -25q0 -53 -25 -115t-77 -106t-134 -44q-41 0 -67.5 10.5t-69.5 30.5q-49 31 -86 31q-20 0 -31.5 -8 t-23.5 -25q-10 -14 -18.5 -21t-22.5 -7h-127q-14 0 -25.5 10t-11.5 24z" />
<glyph unicode="&#x400;" horiz-adv-x="1314" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h967q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-647v-260h602q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-602v-266h664q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37t-37.5 -14h-984q-20 0 -35.5 15.5 t-15.5 35.5zM262 1817q0 18 10 29.5t29 11.5h299q27 0 41 -7.5t35 -31.5l184 -224q10 -10 10 -26q0 -33 -33 -33h-211q-26 0 -42.5 7t-36.5 24l-275 225q-10 10 -10 25z" />
<glyph unicode="&#x401;" horiz-adv-x="1314" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h967q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-647v-260h602q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-602v-266h664q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37t-37.5 -14h-984q-20 0 -35.5 15.5 t-15.5 35.5zM338 1655v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM768 1655v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194 q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x402;" horiz-adv-x="1591" d="M41 1198v188q0 20 13.5 34t33.5 14h1075q20 0 33.5 -13.5t13.5 -34.5v-188q0 -20 -13 -33.5t-34 -13.5h-436v-258q43 39 131 61.5t176 22.5q248 0 369 -118t121 -353v-60q0 -219 -149.5 -332.5t-432.5 -113.5h-31q-20 0 -35.5 15.5t-15.5 35.5v185q0 23 15.5 37t35.5 14 h31q109 0 153 42t44 120v26q0 78 -47.5 114t-149.5 36q-96 0 -155.5 -30t-59.5 -93v-451q0 -23 -15.5 -37t-37.5 -14h-279q-22 0 -37.5 14.5t-15.5 36.5v1100h-254q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x403;" horiz-adv-x="1163" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h893q23 0 37.5 -15.5t14.5 -38.5v-215q0 -23 -14.5 -38t-37.5 -15h-559v-1061q0 -23 -15 -37t-38 -14h-281q-20 0 -35.5 15.5t-15.5 35.5zM528 1569q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5 q0 -14 -11 -25l-274 -225q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#x404;" horiz-adv-x="1417" d="M84 717l2 157q12 289 181 434.5t452 145.5q254 0 417.5 -104.5t186.5 -296.5v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-285q-25 0 -36 7t-23 27q-14 43 -68.5 73t-140.5 30q-242 0 -242 -260v-29h389q20 0 36 -16.5t16 -38.5v-174q0 -23 -15.5 -39.5t-36.5 -16.5h-389v-30 q0 -125 63.5 -191.5t178.5 -66.5q168 0 217 100q14 23 26.5 30t35.5 7h280q18 0 32.5 -12.5t12.5 -30.5q-25 -193 -193.5 -296t-418.5 -103q-289 0 -455 142t-178 437z" />
<glyph unicode="&#x405;" horiz-adv-x="1363" d="M57 393q0 18 13.5 30.5t31.5 12.5h273q25 0 40 -8t34 -29q27 -49 83 -80.5t150 -31.5q111 0 170 33.5t59 93.5q0 43 -30.5 70.5t-98 49t-194.5 48.5q-248 49 -371 149t-123 287q0 127 71 226t201 154.5t302 55.5q180 0 312 -62.5t202.5 -155.5t74.5 -183q0 -18 -12 -31.5 t-31 -13.5h-286q-45 0 -70 36q-12 43 -63.5 73t-126.5 30q-86 0 -133.5 -31t-47.5 -90t61.5 -93t229.5 -69q193 -35 306.5 -87t168 -135t54.5 -210q0 -139 -80 -241.5t-221.5 -156.5t-323.5 -54q-199 0 -338 57t-211 151.5t-76 204.5z" />
<glyph unicode="&#x406;" horiz-adv-x="673" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h293q23 0 37.5 -14.5t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5h-293q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x407;" horiz-adv-x="673" d="M-23 1655v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM139 51v1331q0 23 15.5 37.5t35.5 14.5h293q23 0 37.5 -14.5t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5h-293 q-20 0 -35.5 15.5t-15.5 35.5zM407 1655v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x408;" horiz-adv-x="1380" d="M45 453q0 18 12.5 31.5t30.5 13.5h293q29 0 45 -13.5t27 -44.5q33 -154 200 -153q98 0 150.5 64.5t52.5 187.5v577h-657q-20 0 -36 15.5t-16 35.5v215q0 23 15.5 37.5t36.5 14.5h1001q23 0 38 -15.5t15 -38.5v-854q0 -174 -76.5 -298t-215 -186t-318.5 -62 q-160 0 -295 54t-218 161.5t-85 257.5z" />
<glyph unicode="&#x409;" horiz-adv-x="2228" d="M61 53v228q0 43 52 53q109 18 153.5 180t44.5 455v413q0 23 15.5 37.5t38.5 14.5h997q23 0 38 -14.5t15 -37.5v-436h187q270 0 415.5 -117.5t145.5 -347.5q0 -221 -147.5 -351t-413.5 -130h-521q-20 0 -35.5 15.5t-15.5 35.5v1041h-348v-172q0 -348 -45 -541t-167 -284 t-355 -95q-21 0 -37.5 16.5t-16.5 36.5zM1415 295h178q82 0 126 51t44 133q0 86 -44 130t-126 44h-178v-358z" />
<glyph unicode="&#x40a;" horiz-adv-x="2193" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-485h471v485q0 23 14.5 38.5t37.5 15.5h280q23 0 37 -14.5t14 -37.5v-436h187q270 0 415.5 -116.5t145.5 -348.5q0 -221 -146.5 -351t-414.5 -130h-518q-20 0 -36 15.5t-16 35.5v502h-471v-502 q0 -23 -15 -37t-38 -14h-281q-22 0 -36.5 14.5t-14.5 36.5zM1378 295h181q80 0 125 51t45 133q0 86 -44.5 130t-125.5 44h-181v-358z" />
<glyph unicode="&#x40b;" horiz-adv-x="1615" d="M41 1198v188q0 20 13.5 34t33.5 14h1075q20 0 33.5 -13.5t13.5 -34.5v-188q0 -20 -13 -33.5t-34 -13.5h-436v-287q45 39 133 62.5t174 23.5q248 0 369 -117.5t121 -353.5v-428q0 -20 -14.5 -35.5t-36.5 -15.5h-281q-23 0 -38 14.5t-15 36.5v395q0 78 -47.5 115t-149.5 37 q-86 0 -150.5 -27.5t-64.5 -97.5v-422q0 -23 -15.5 -37t-37.5 -14h-279q-22 0 -37.5 14.5t-15.5 36.5v1100h-254q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x40c;" horiz-adv-x="1447" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-485h115l295 498q27 41 78 41h313q18 0 30.5 -12.5t12.5 -30.5q0 -14 -8 -25l-379 -623l416 -673q6 -9 6 -23q0 -20 -13.5 -33.5t-31.5 -13.5h-324q-49 0 -71 41l-306 512h-133v-502q0 -23 -15 -37 t-38 -14h-281q-23 0 -37 14.5t-14 36.5zM530 1569q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#x40d;" horiz-adv-x="1601" d="M139 53v1327q0 23 15.5 38.5t38.5 15.5h278q23 0 38 -15.5t15 -38.5v-800l566 815q25 39 69 39h252q23 0 37 -15.5t14 -38.5v-1329q0 -20 -14 -35.5t-37 -15.5h-281q-23 0 -38 14.5t-15 36.5v770l-565 -782q-12 -18 -27.5 -28.5t-42.5 -10.5h-252q-22 0 -36.5 15.5 t-14.5 37.5zM375 1817q0 18 10 29.5t29 11.5h299q27 0 41 -7.5t35 -31.5l184 -224q10 -10 10 -26q0 -33 -33 -33h-211q-26 0 -42.5 7t-36.5 24l-275 225q-10 10 -10 25z" />
<glyph unicode="&#x40e;" horiz-adv-x="1341" d="M16 1386q0 18 14.5 33t35.5 15h301q37 0 59 -43l283 -560l245 560q16 43 62 43h282q21 0 33 -12.5t12 -30.5t-10 -39l-405 -934q-72 -166 -136.5 -253t-157.5 -126t-247 -39h-113q-20 0 -35.5 15.5t-15.5 35.5v230q0 23 14.5 38t36.5 15h80q57 0 97.5 22.5t72.5 73.5 l-501 940q-6 6 -7 16zM332 1815q0 16 10 27t29 11h172q18 0 28.5 -11t10.5 -27q0 -106 119 -107q117 0 116 107q0 16 10.5 27t28.5 11h172q18 0 28.5 -11t10.5 -27q0 -133 -90 -215t-276 -82q-189 0 -279 81.5t-90 215.5z" />
<glyph unicode="&#x40f;" horiz-adv-x="1531" d="M139 51v1331q0 23 14.5 37.5t36.5 14.5h281q23 0 38 -14.5t15 -37.5v-1040h484v1040q0 23 15 37.5t38 14.5h278q23 0 38.5 -14.5t15.5 -37.5v-1331q0 -23 -15.5 -37t-38.5 -14h-381v-236q0 -23 -15 -37t-38 -14h-280q-21 0 -36.5 14.5t-15.5 36.5v236h-383 q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x410;" horiz-adv-x="1474" d="M16 43q0 12 2 18l478 1313q20 59 82 60h319q61 0 82 -60l477 -1313q2 -6 2 -18q0 -16 -13 -29.5t-30 -13.5h-266q-49 0 -70 45l-73 199h-537l-74 -199q-20 -45 -69 -45h-267q-16 0 -29.5 13.5t-13.5 29.5zM555 551h365l-183 522z" />
<glyph unicode="&#x411;" horiz-adv-x="1349" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h992q23 0 38 -15.5t15 -38.5v-215q0 -23 -15.5 -38t-37.5 -15h-658v-227h275q248 0 391 -117t143 -319q0 -129 -62.5 -230.5t-183 -160t-288.5 -58.5h-609q-20 0 -35.5 15.5t-15.5 35.5zM524 276h240q80 0 125 50.5t45 124.5 q0 72 -44 118.5t-126 46.5h-240v-340z" />
<glyph unicode="&#x412;" horiz-adv-x="1447" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h633q260 0 387 -106.5t127 -303.5q0 -98 -52 -170t-116 -104q84 -37 141.5 -122t57.5 -192q0 -207 -136 -321.5t-390 -114.5h-652q-20 0 -35.5 15.5t-15.5 35.5zM524 276h275q80 0 124 46.5t44 113.5q0 70 -45 116t-123 46h-275 v-322zM524 866h256q76 0 117 41t41 107q0 63 -40 103t-118 40h-256v-291z" />
<glyph unicode="&#x413;" horiz-adv-x="1163" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h893q23 0 37.5 -15.5t14.5 -38.5v-215q0 -23 -14.5 -38t-37.5 -15h-559v-1061q0 -23 -15 -37t-38 -14h-281q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x414;" horiz-adv-x="1615" d="M61 -233v522q0 23 14.5 38t37.5 15h37q94 0 131.5 165t37.5 462v413q0 23 15.5 37.5t38.5 14.5h977q23 0 38 -15.5t15 -38.5v-1038h94q23 0 38 -15.5t15 -37.5v-522q0 -23 -15 -38.5t-38 -15.5h-278q-23 0 -38.5 15.5t-15.5 38.5v233h-719v-233q0 -23 -15 -38.5 t-38 -15.5h-280q-23 0 -37.5 15.5t-14.5 38.5zM602 342h416v750h-328v-172q0 -406 -88 -578z" />
<glyph unicode="&#x415;" horiz-adv-x="1314" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h967q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-647v-260h602q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-602v-266h664q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37t-37.5 -14h-984q-20 0 -35.5 15.5 t-15.5 35.5z" />
<glyph unicode="&#x416;" horiz-adv-x="2203" d="M45 47q0 16 4 23l416 673l-379 623q-6 12 -6 25q0 18 12 30.5t31 12.5h311q51 0 78 -41l295 -498h111v487q0 23 15 37.5t36 14.5h266q23 0 38 -14.5t15 -37.5v-487h109l295 498q23 41 77 41h312q20 0 32.5 -12.5t12.5 -30.5q0 -8 -8 -25l-379 -623l415 -673q7 -9 7 -23 q0 -20 -13.5 -33.5t-33.5 -13.5h-322q-49 0 -72 41l-305 512h-127v-502q0 -23 -15.5 -37t-37.5 -14h-266q-23 0 -37 14.5t-14 36.5v502h-127l-306 -512q-12 -18 -27.5 -29.5t-43.5 -11.5h-324q-18 0 -31.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x417;" horiz-adv-x="1384" d="M76 379v4q0 16 13 27.5t30 11.5h278q25 0 39.5 -9.5t26.5 -27.5q47 -100 215 -100q106 0 167.5 38.5t61.5 110.5q0 74 -56 112t-161 38h-151q-20 0 -36 16t-16 39v174q0 23 15.5 39t36.5 16h147q86 0 135 34t49 106q0 63 -50 101t-140 38q-86 0 -138.5 -26.5t-66.5 -76.5 q-10 -18 -22.5 -26t-34.5 -8h-285q-18 0 -32.5 12t-12.5 31q18 168 162.5 284.5t437.5 116.5q172 0 302 -55.5t201 -149.5t71 -205q0 -88 -36 -164.5t-124 -125.5q106 -53 154.5 -143.5t48.5 -192.5q0 -131 -76 -230.5t-215.5 -153.5t-327.5 -54q-203 0 -336 57t-198.5 147 t-75.5 195z" />
<glyph unicode="&#x418;" horiz-adv-x="1601" d="M139 53v1327q0 23 15.5 38.5t38.5 15.5h278q23 0 38 -15.5t15 -38.5v-800l566 815q25 39 69 39h252q23 0 37 -15.5t14 -38.5v-1329q0 -20 -14 -35.5t-37 -15.5h-281q-23 0 -38 14.5t-15 36.5v770l-565 -782q-12 -18 -27.5 -28.5t-42.5 -10.5h-252q-22 0 -36.5 15.5 t-14.5 37.5z" />
<glyph unicode="&#x419;" horiz-adv-x="1601" d="M139 53v1327q0 23 15.5 38.5t38.5 15.5h278q23 0 38 -15.5t15 -38.5v-800l566 815q25 39 69 39h252q23 0 37 -15.5t14 -38.5v-1329q0 -20 -14 -35.5t-37 -15.5h-281q-23 0 -38 14.5t-15 36.5v770l-565 -782q-12 -18 -27.5 -28.5t-42.5 -10.5h-252q-22 0 -36.5 15.5 t-14.5 37.5zM442 1815q0 16 10 27t29 11h172q18 0 28.5 -11t10.5 -27q0 -106 119 -107q117 0 116 107q0 16 10.5 27t28.5 11h172q18 0 28.5 -11t10.5 -27q0 -133 -90 -215t-276 -82q-189 0 -279 81.5t-90 215.5z" />
<glyph unicode="&#x41a;" horiz-adv-x="1447" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-485h115l295 498q27 41 78 41h313q18 0 30.5 -12.5t12.5 -30.5q0 -14 -8 -25l-379 -623l416 -673q6 -9 6 -23q0 -20 -13.5 -33.5t-31.5 -13.5h-324q-49 0 -71 41l-306 512h-133v-502q0 -23 -15 -37 t-38 -14h-281q-23 0 -37 14.5t-14 36.5z" />
<glyph unicode="&#x41b;" horiz-adv-x="1574" d="M61 53v228q0 43 52 53q109 18 153.5 180t44.5 455v413q0 23 15.5 37.5t38.5 14.5h1017q23 0 38.5 -15.5t15.5 -38.5v-1329q0 -23 -15.5 -37t-38.5 -14h-280q-21 0 -36 15.5t-15 35.5v1041h-369v-172q0 -348 -45 -541t-167 -284t-355 -95q-20 0 -37 16.5t-17 36.5z" />
<glyph unicode="&#x41c;" horiz-adv-x="1701" d="M139 51v1329q0 23 15.5 38.5t38.5 15.5h231q51 0 74 -45l352 -631l354 631q23 45 74 45h231q23 0 38.5 -15.5t15.5 -38.5v-1329q0 -23 -15.5 -37t-38.5 -14h-260q-20 0 -35.5 15.5t-15.5 35.5v746l-221 -406q-27 -47 -72 -47h-110q-41 0 -72 47l-219 406v-746 q0 -23 -15.5 -37t-37.5 -14h-258q-23 0 -38.5 14.5t-15.5 36.5z" />
<glyph unicode="&#x41d;" horiz-adv-x="1531" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-485h484v485q0 23 15 38.5t38 15.5h278q23 0 38.5 -15.5t15.5 -38.5v-1329q0 -23 -15.5 -37t-38.5 -14h-278q-23 0 -38 14.5t-15 36.5v502h-484v-502q0 -23 -15 -37t-38 -14h-281q-22 0 -36.5 14.5 t-14.5 36.5z" />
<glyph unicode="&#x41e;" horiz-adv-x="1464" d="M94 713q0 119 2 178q8 270 179 416.5t458 146.5q285 0 456 -146.5t179 -416.5q4 -119 4 -178q0 -61 -4 -176q-10 -274 -177 -415.5t-458 -141.5t-459 141t-178 416q-2 57 -2 176zM492 719q0 -111 2 -170q4 -133 67.5 -197.5t171.5 -64.5q107 0 170.5 64.5t67.5 197.5 q4 119 4 170t-4 166q-4 133 -67.5 197.5t-170.5 64.5q-109 0 -172 -64.5t-67 -197.5q-2 -57 -2 -166z" />
<glyph unicode="&#x41f;" horiz-adv-x="1531" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h1149q23 0 38.5 -15.5t15.5 -38.5v-1329q0 -23 -15.5 -37t-38.5 -14h-278q-23 0 -38 14.5t-15 36.5v1041h-484v-1041q0 -23 -15 -37t-38 -14h-281q-22 0 -36.5 14.5t-14.5 36.5z" />
<glyph unicode="&#x420;" horiz-adv-x="1390" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h580q266 0 417.5 -121t151.5 -355q0 -233 -151.5 -348.5t-417.5 -115.5h-233v-443q0 -20 -14.5 -35.5t-37.5 -15.5h-295q-20 0 -35.5 15.5t-15.5 35.5zM530 786h230q84 0 132 44t48 131q0 80 -44 129t-136 49h-230v-353z" />
<glyph unicode="&#x421;" horiz-adv-x="1445" d="M94 715q0 127 2 184q10 266 179 410.5t458 144.5q182 0 326.5 -59.5t228.5 -172t88 -266.5q0 -18 -13 -30.5t-32 -12.5h-291q-29 0 -42 11.5t-23 42.5q-29 100 -87.5 140t-154.5 40q-231 0 -239 -258q-2 -55 -2 -170t2 -174q8 -258 239 -258q94 0 154.5 41t87.5 139 q8 31 22.5 42t42.5 11h291q18 0 31.5 -12t13.5 -31q-4 -154 -88 -266.5t-228.5 -171.5t-326.5 -59q-293 0 -460 141t-177 414q-2 55 -2 180z" />
<glyph unicode="&#x422;" horiz-adv-x="1300" d="M41 1151v229q0 23 15.5 38.5t37.5 15.5h1112q23 0 38.5 -15.5t15.5 -38.5v-229q0 -23 -15.5 -37t-38.5 -14h-360v-1049q0 -23 -15.5 -37t-37.5 -14h-285q-23 0 -38 14.5t-15 36.5v1049h-361q-23 0 -38 14t-15 37z" />
<glyph unicode="&#x423;" horiz-adv-x="1341" d="M16 1386q0 18 14.5 33t35.5 15h301q37 0 59 -43l283 -560l245 560q16 43 62 43h282q21 0 33 -12.5t12 -30.5t-10 -39l-405 -934q-72 -166 -136.5 -253t-157.5 -126t-247 -39h-113q-20 0 -35.5 15.5t-15.5 35.5v230q0 23 14.5 38t36.5 15h80q57 0 97.5 22.5t72.5 73.5 l-501 940q-6 6 -7 16z" />
<glyph unicode="&#x424;" horiz-adv-x="1912" d="M92 745l2 76q14 262 192.5 405.5t477.5 143.5v115q0 23 14.5 38t36.5 15h281q23 0 38 -15.5t15 -37.5v-115q299 0 477 -143.5t191 -405.5q4 -61 4 -76q0 -14 -4 -75q-18 -268 -189.5 -406.5t-478.5 -138.5v-139q0 -23 -15.5 -37.5t-37.5 -14.5h-281q-20 0 -35.5 14.5 t-15.5 37.5v139q-307 0 -478 138t-192 407zM492 680q8 -127 76.5 -187.5t197.5 -60.5v631q-139 0 -206.5 -70.5t-67.5 -222.5v-90zM1145 432q131 0 199.5 60.5t76.5 187.5q2 14 2 70q0 53 -2 65q-10 127 -78.5 187.5t-197.5 60.5v-631z" />
<glyph unicode="&#x425;" horiz-adv-x="1425" d="M20 43q0 12 7 25l446 663l-412 635q-6 12 -6 25q0 16 13.5 29.5t29.5 13.5h314q45 0 73 -45l232 -357l239 357q29 45 72 45h299q16 0 29.5 -13.5t13.5 -29.5q0 -12 -6 -25l-416 -631l451 -667q6 -12 6 -25q0 -16 -13.5 -29.5t-29.5 -13.5h-324q-45 0 -71 41l-262 381 l-256 -381q-27 -41 -72 -41h-314q-16 0 -29.5 13.5t-13.5 29.5z" />
<glyph unicode="&#x426;" horiz-adv-x="1605" d="M139 53v1329q0 23 14.5 37.5t36.5 14.5h281q23 0 38 -14.5t15 -37.5v-1040h484v1040q0 23 15 37.5t38 14.5h278q23 0 38.5 -14.5t15.5 -37.5v-1040h96q23 0 37 -15.5t14 -37.5v-525q0 -23 -14.5 -37t-36.5 -14h-281q-20 0 -35.5 14.5t-15.5 36.5v236h-967q-23 0 -37 15.5 t-14 37.5z" />
<glyph unicode="&#x427;" horiz-adv-x="1439" d="M92 932v450q0 23 15.5 37.5t37.5 14.5h279q23 0 38 -14.5t15 -37.5v-432q0 -100 49 -146t162 -46q82 0 148.5 28.5t66.5 87.5v508q0 23 15.5 37.5t37.5 14.5h279q23 0 38 -14.5t15 -37.5v-1331q0 -23 -15.5 -37t-37.5 -14h-279q-22 0 -37.5 14.5t-15.5 36.5v432 q-45 -39 -125 -58t-170 -19q-262 0 -389 130t-127 396z" />
<glyph unicode="&#x428;" horiz-adv-x="2027" d="M139 51v1331q0 23 14.5 37.5t36.5 14.5h281q23 0 38 -14.5t15 -37.5v-1040h299v1040q0 23 15.5 37.5t35.5 14.5h281q23 0 37 -14.5t14 -37.5v-1040h299v1040q0 23 15.5 37.5t35.5 14.5h281q23 0 37 -14.5t14 -37.5v-1331q0 -20 -14 -35.5t-37 -15.5h-1647 q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x429;" horiz-adv-x="2101" d="M139 51v1331q0 23 14.5 37.5t36.5 14.5h281q23 0 38 -14.5t15 -37.5v-1040h299v1040q0 23 15.5 37.5t35.5 14.5h281q23 0 37 -14.5t14 -37.5v-1040h299v1040q0 23 15.5 37.5t35.5 14.5h281q23 0 37 -14.5t14 -37.5v-1040h97q23 0 38 -15.5t15 -37.5v-525q0 -23 -15.5 -37 t-37.5 -14h-281q-21 0 -36 14.5t-15 36.5v236h-1463q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x42a;" horiz-adv-x="1574" d="M41 1178v202q0 23 15.5 38.5t37.5 15.5h600q23 0 38.5 -14.5t15.5 -37.5v-436h198q270 0 415.5 -116.5t145.5 -348.5q0 -221 -146.5 -351t-414.5 -130h-532q-21 0 -36.5 15.5t-15.5 35.5v1075h-268q-23 0 -38 14.5t-15 37.5zM748 295h192q80 0 125 51t45 133 q0 86 -44 130t-126 44h-192v-358z" />
<glyph unicode="&#x42b;" horiz-adv-x="1957" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h281q23 0 38 -14.5t15 -37.5v-436h187q270 0 415.5 -117.5t145.5 -347.5q0 -221 -147.5 -351t-413.5 -130h-521q-20 0 -35.5 15.5t-15.5 35.5zM524 295h178q82 0 126 51t44 133q0 86 -44 130t-126 44h-178v-358zM1423 51v1331 q0 23 15.5 37.5t36.5 14.5h292q23 0 37.5 -14.5t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5h-292q-21 0 -36.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x42c;" horiz-adv-x="1357" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h281q23 0 38 -14.5t15 -37.5v-436h207q270 0 415.5 -117.5t145.5 -347.5q0 -221 -147.5 -351t-413.5 -130h-541q-20 0 -35.5 15.5t-15.5 35.5zM524 295h199q82 0 126 51t44 133q0 86 -44 130t-126 44h-199v-358z" />
<glyph unicode="&#x42d;" horiz-adv-x="1423" d="M84 379v4q0 16 13.5 27.5t31.5 11.5h281q23 0 35 -8t26 -29q51 -100 215 -100q117 0 180.5 67.5t63.5 190.5v30h-389q-20 0 -36 16.5t-16 39.5v174q0 23 15.5 39t36.5 16h389v29q0 260 -244 260q-86 0 -139 -28.5t-70 -74.5q-10 -20 -22.5 -27t-34.5 -7h-285 q-18 0 -32.5 12t-12.5 31q25 197 189.5 299t414.5 102q285 0 454 -145.5t179 -434.5q4 -127 4 -157q0 -31 -4 -158q-10 -295 -176 -437t-457 -142q-252 0 -420 105t-190 294z" />
<glyph unicode="&#x42e;" horiz-adv-x="2140" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h281q23 0 38 -14.5t15 -37.5v-487h248l2 45q27 238 189.5 376t443.5 138q295 0 461 -151.5t176 -411.5q4 -119 4 -178q0 -61 -4 -176q-10 -262 -175 -409.5t-462 -147.5q-289 0 -450.5 139t-182.5 387q-4 37 -4 47h-246v-502 q0 -23 -15 -37t-38 -14h-281q-23 0 -37 14.5t-14 36.5zM1167 717q0 -113 2 -172q4 -131 66.5 -194.5t171.5 -63.5t171 63.5t67 194.5q4 119 4 172q0 57 -4 172q-4 131 -68 194.5t-170 63.5t-169.5 -63.5t-68.5 -194.5q-2 -57 -2 -172z" />
<glyph unicode="&#x42f;" horiz-adv-x="1443" d="M109 43q0 10 6 23l278 477q-119 55 -185.5 162.5t-66.5 250.5q0 139 65.5 248t192.5 169.5t303 60.5h551q23 0 37.5 -14.5t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5h-280q-20 0 -35.5 15.5t-15.5 35.5v436h-164l-232 -432q-31 -55 -86 -55h-286q-18 0 -31.5 13.5 t-13.5 29.5zM541 956q0 -82 43 -126t127 -44h211v353h-211q-80 0 -125 -50.5t-45 -132.5z" />
<glyph unicode="&#x430;" d="M51 299q0 139 116 226t318 120l254 39v25q0 76 -30.5 110.5t-104.5 34.5q-41 0 -68.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -33.5 13t-11.5 32q2 55 55 123.5t162 117.5t268 49q254 0 377 -111.5t123 -301.5v-621q0 -20 -14.5 -35.5 t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5v72q-45 -63 -125 -103t-191 -40q-108 0 -197 41.5t-139.5 115.5t-50.5 162zM401 326q0 -47 40 -76t100 -29q90 0 146 59.5t56 172.5v24l-170 -28q-172 -33 -172 -123z" />
<glyph unicode="&#x431;" horiz-adv-x="1263" d="M84 662l2 122q10 281 97 424.5t260 215t542 153.5q20 4 38.5 -7t20.5 -34l37 -199q4 -23 -6 -40t-31 -21q-268 -51 -379.5 -86t-166 -101.5t-64.5 -205.5q47 45 119 73.5t149 28.5q223 0 345 -112.5t135 -315.5q4 -49 4 -78q0 -27 -4 -71q-12 -205 -150.5 -316.5 t-396.5 -111.5q-279 0 -407 133t-142 424zM461 479l2 -63q4 -102 46 -152.5t126 -50.5t126 50t46 153q2 16 2 63q0 51 -2 70q-10 201 -160 201q-174 0 -184 -201z" />
<glyph unicode="&#x432;" horiz-adv-x="1249" d="M117 51v963q0 23 15 37t36 14h485q240 0 357.5 -80t117.5 -225q0 -72 -28.5 -122t-81.5 -87q68 -35 105.5 -95.5t37.5 -140.5q0 -150 -118.5 -232.5t-358.5 -82.5h-516q-21 0 -36 15.5t-15 35.5zM465 227h201q133 0 133 92q0 51 -33 74t-100 23h-201v-189zM465 651h197 q131 0 131 99q0 49 -34 68.5t-97 19.5h-197v-187z" />
<glyph unicode="&#x433;" horiz-adv-x="946" d="M117 51v963q0 23 15 37t36 14h692q23 0 37 -14.5t14 -36.5v-176q0 -20 -14 -36t-37 -16h-383v-735q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="&#x434;" horiz-adv-x="1353" d="M47 -174v401q0 23 15.5 37.5t37.5 14.5h21q74 0 102.5 107.5t28.5 334.5v293q0 23 15.5 37t37.5 14h819q23 0 37.5 -14.5t14.5 -36.5v-733h86q23 0 38 -15.5t15 -38.5v-401q0 -23 -15.5 -37t-37.5 -14h-256q-20 0 -36 14t-16 37v174h-546v-174q0 -23 -14.5 -37t-37.5 -14 h-256q-22 0 -37.5 14t-15.5 37zM524 279l291 2v505h-221v-104q0 -283 -70 -403z" />
<glyph unicode="&#x435;" horiz-adv-x="1222" d="M76 504v63q8 244 151.5 381t386.5 137q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t116 -56.5q49 0 79 19.5t57 50.5q18 20 29 25t36 5h268q18 0 31.5 -11t13.5 -27q0 -53 -60 -125t-176 -123t-274 -51 q-249 0 -392.5 134t-147.5 390zM453 643h325v4q0 98 -43 152.5t-121 54.5t-119.5 -54.5t-41.5 -152.5v-4z" />
<glyph unicode="&#x436;" horiz-adv-x="1894" d="M37 43q0 12 10 27l326 479l-279 444q-10 14 -10 29q0 18 13.5 30.5t31.5 12.5h277q35 0 57 -33l213 -352h94v334q0 23 15.5 37t37.5 14h250q23 0 37 -14.5t14 -36.5v-334h95l213 352q23 33 57 33h276q18 0 31.5 -12.5t13.5 -30.5q0 -14 -10 -29l-278 -444l325 -479 q11 -15 11 -27q0 -18 -13.5 -30.5t-34.5 -12.5h-303q-35 0 -61 33l-234 368h-88v-350q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5v350h-88l-233 -368q-27 -33 -62 -33h-303q-20 0 -33.5 12.5t-13.5 30.5z" />
<glyph unicode="&#x437;" horiz-adv-x="1153" d="M72 293v4q0 12 12 22.5t25 10.5h249q23 0 34 -7.5t22 -23.5q16 -37 55 -54.5t106 -17.5q70 0 108 26t38 79q0 49 -29.5 74.5t-99.5 25.5h-115q-18 0 -31.5 13.5t-13.5 31.5v123q0 18 12.5 31.5t32.5 13.5h109q61 0 88.5 21.5t27.5 66.5t-32.5 74t-102.5 29 q-68 0 -99.5 -18.5t-51.5 -61.5q-10 -16 -21.5 -23.5t-34.5 -7.5h-241q-16 0 -27.5 11.5t-9.5 25.5q8 78 57 151.5t155.5 122.5t276.5 49q248 0 363 -83.5t115 -219.5q0 -158 -134 -221q160 -57 164 -260q0 -147 -136 -234t-372 -87q-178 0 -286.5 50t-156.5 120.5 t-56 142.5z" />
<glyph unicode="&#x438;" horiz-adv-x="1312" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-555l400 573q23 33 57 33h229q21 0 36 -14.5t15 -34.5v-965q0 -20 -14 -35.5t-37 -15.5h-256q-23 0 -38 14.5t-15 36.5v555l-399 -573q-23 -33 -58 -33h-229q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x439;" horiz-adv-x="1312" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-555l400 573q23 33 57 33h229q21 0 36 -14.5t15 -34.5v-965q0 -20 -14 -35.5t-37 -15.5h-256q-23 0 -38 14.5t-15 36.5v555l-399 -573q-23 -33 -58 -33h-229q-20 0 -35.5 15.5t-15.5 35.5zM320 1501 q0 16 10 27.5t29 11.5h174q18 0 28.5 -11t10.5 -28q0 -106 112 -106q115 0 115 106q0 16 10 27.5t29 11.5h172q18 0 28.5 -11t10.5 -28q0 -133 -90 -216t-275 -83q-184 0 -274 83t-90 216z" />
<glyph unicode="&#x43a;" horiz-adv-x="1255" d="M117 51v963q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-334h107l215 352q23 33 57 33h274q20 0 33.5 -12.5t13.5 -30.5q0 -8 -10 -29l-276 -446l323 -477q9 -11 9 -27q0 -18 -12.5 -30.5t-33.5 -12.5h-305q-37 0 -59 33l-234 366h-102v-348q0 -20 -14.5 -35.5 t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x43b;" horiz-adv-x="1290" d="M51 53v189q0 20 14.5 33.5t36.5 17.5q82 8 111 106.5t29 321.5v293q0 23 15 37t36 14h819q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-256q-23 0 -38 14.5t-15 36.5v735h-219v-104q0 -268 -41 -412.5t-142.5 -207t-296.5 -62.5q-22 0 -37.5 15.5 t-15.5 37.5z" />
<glyph unicode="&#x43c;" horiz-adv-x="1490" d="M117 43v981q0 16 12 28.5t29 12.5h250q37 0 59 -37l272 -483l275 483q23 37 59 37h250q16 0 28.5 -12.5t12.5 -28.5v-981q0 -18 -12.5 -30.5t-30.5 -12.5h-268q-18 0 -30.5 12.5t-12.5 30.5v453l-168 -293q-12 -23 -25.5 -34t-34.5 -11h-84q-37 0 -61 45l-168 291v-451 q0 -18 -12.5 -30.5t-28.5 -12.5h-268q-18 0 -30.5 12.5t-12.5 30.5z" />
<glyph unicode="&#x43d;" horiz-adv-x="1312" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-334h348v334q0 23 15.5 37t38.5 14h256q23 0 37 -14.5t14 -36.5v-963q0 -20 -14.5 -35.5t-36.5 -15.5h-256q-23 0 -38.5 14.5t-15.5 36.5v350h-348v-350q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5 t-15 35.5z" />
<glyph unicode="&#x43e;" horiz-adv-x="1257" d="M80 532q0 66 2 93q12 217 154.5 338.5t392.5 121.5t392 -121.5t155 -338.5q4 -53 4 -93q0 -39 -4 -92q-12 -221 -150.5 -340.5t-396.5 -119.5t-396.5 119.5t-150.5 340.5q-2 27 -2 92zM455 532l2 -81q6 -109 47 -161t125 -52t125 52t47 161q2 20 2 81t-2 82 q-6 106 -47 159.5t-125 53.5t-125 -53t-47 -160z" />
<glyph unicode="&#x43f;" horiz-adv-x="1298" d="M117 51v963q0 23 15 37t36 14h952q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-256q-20 0 -35.5 15.5t-15.5 35.5v735h-336v-735q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="&#x440;" horiz-adv-x="1298" d="M117 -338v1352q0 23 15 37t38 14h244q23 0 37 -14.5t14 -36.5v-74q111 145 317 145q199 0 312.5 -123.5t122.5 -353.5q2 -27 2 -76t-2 -77q-8 -221 -123 -348t-312 -127q-199 0 -297 135v-453q0 -23 -14 -37t-37 -14h-264q-23 0 -38 14.5t-15 36.5zM483 524q0 -49 2 -75 q4 -80 48.5 -132.5t130.5 -52.5q92 0 132 54.5t46 150.5q2 20 2 63t-2 64q-6 96 -46 150.5t-132 54.5q-88 0 -131.5 -56.5t-47.5 -142.5q-2 -27 -2 -78z" />
<glyph unicode="&#x441;" d="M78 530l2 84q8 221 153.5 346t378.5 125q178 0 299 -63.5t180.5 -150.5t63.5 -158q2 -23 -14.5 -38.5t-38.5 -15.5h-275q-23 0 -35 10.5t-22 32.5q-23 57 -58.5 84t-90.5 27q-158 0 -164 -209l-2 -78l2 -67q2 -106 43 -156.5t121 -50.5q59 0 93 25.5t56 84.5 q10 23 22.5 33.5t34.5 10.5h275q20 0 36.5 -14.5t16.5 -35.5q0 -63 -56.5 -152t-179 -156.5t-307.5 -67.5q-233 0 -377.5 123.5t-154.5 345.5z" />
<glyph unicode="&#x442;" horiz-adv-x="1130" d="M29 838v176q0 23 15 37t38 14h967q23 0 37 -14.5t14 -36.5v-176q0 -20 -14.5 -36t-36.5 -16h-308v-735q0 -20 -14 -35.5t-37 -15.5h-250q-22 0 -37.5 14.5t-15.5 36.5v735h-305q-23 0 -38 14.5t-15 37.5z" />
<glyph unicode="&#x443;" horiz-adv-x="1222" d="M37 1018q4 20 17.5 33.5t31.5 13.5h244q43 0 59 -43l223 -576l236 576q16 43 63 43h232q18 0 31.5 -13.5t13.5 -29.5q0 -14 -6 -27l-389 -940q-66 -156 -125.5 -249t-140.5 -145t-199 -52h-103q-23 0 -38 15.5t-15 37.5v174q0 23 15.5 37t37.5 14h72q53 0 82 30t63 114 l9 18l-408 946q-6 16 -6 23z" />
<glyph unicode="&#x444;" horiz-adv-x="1669" d="M80 532q0 59 2 86q12 211 159.5 329t413.5 118v338q0 23 15.5 37t36.5 14h256q23 0 37 -14.5t14 -36.5v-338q266 0 413.5 -118t159.5 -329q4 -53 4 -86q0 -29 -4 -86q-12 -215 -155.5 -330.5t-417.5 -115.5v-338q0 -23 -14.5 -37t-36.5 -14h-256q-21 0 -36.5 14.5 t-15.5 36.5v338q-274 0 -417.5 115.5t-155.5 330.5q-2 29 -2 86zM436 530q0 -147 49.5 -209.5t171.5 -62.5v549q-113 0 -164 -52.5t-55 -160.5zM1012 258q125 0 173 63.5t48 208.5q0 45 -2 64q-4 111 -56.5 162t-162.5 51v-549z" />
<glyph unicode="&#x445;" d="M29 47q0 16 12 31l340 469l-309 440q-12 16 -13 31q0 20 14.5 33.5t32.5 13.5h265q25 0 37 -10.5t24 -26.5l180 -252l183 252q2 2 10 12.5t21.5 17.5t29.5 7h252q18 0 32.5 -13.5t14.5 -31.5q0 -16 -12 -33l-316 -440l347 -469q10 -14 10 -31q0 -20 -13.5 -33.5 t-33.5 -13.5h-277q-35 0 -57 33l-199 270l-201 -270q-12 -16 -24 -24.5t-35 -8.5h-268q-19 0 -33 13.5t-14 33.5z" />
<glyph unicode="&#x446;" horiz-adv-x="1351" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-735h336v735q0 23 15.5 37t35.5 14h256q23 0 37 -14.5t14 -36.5v-735h89q23 0 37 -14.5t14 -37.5v-413q0 -23 -14.5 -38.5t-36.5 -15.5h-256q-23 0 -38.5 15.5t-15.5 38.5v186h-782q-20 0 -35.5 15.5 t-15.5 35.5z" />
<glyph unicode="&#x447;" horiz-adv-x="1249" d="M66 702v312q0 23 15 37t36 14h266q23 0 37 -14.5t14 -36.5v-293q0 -70 33 -98.5t111 -28.5q76 0 119.5 23.5t43.5 68.5v328q0 23 15.5 37t38.5 14h276q23 0 38 -14.5t15 -36.5v-963q0 -23 -15 -37t-38 -14h-276q-23 0 -38.5 14.5t-15.5 36.5v305q-92 -74 -274 -73 q-199 0 -300 101t-101 318z" />
<glyph unicode="&#x448;" horiz-adv-x="1708" d="M117 51v963q0 23 15 37t36 14h244q23 0 37 -14.5t14 -36.5v-735h223v735q0 23 15.5 37t37.5 14h222q23 0 37 -14.5t14 -36.5v-735h225v735q0 23 15.5 37t35.5 14h242q23 0 38 -14.5t15 -36.5v-963q0 -23 -15.5 -37t-37.5 -14h-1362q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="&#x449;" horiz-adv-x="1761" d="M117 51v963q0 23 15 37t36 14h244q23 0 37 -14.5t14 -36.5v-735h223v735q0 23 15.5 37t37.5 14h222q23 0 37 -14.5t14 -36.5v-735h225v735q0 23 15.5 37t35.5 14h242q23 0 38 -14.5t15 -36.5v-735h86q23 0 37 -14.5t14 -37.5v-413q0 -23 -14 -38.5t-37 -15.5h-256 q-22 0 -36.5 15.5t-14.5 38.5v186h-1194q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x44a;" horiz-adv-x="1343" d="M29 838v176q0 23 15 37t38 14h547q23 0 37 -14.5t14 -36.5v-275h178q205 0 319.5 -99t114.5 -273q0 -178 -116.5 -272.5t-327.5 -94.5h-477q-21 0 -36.5 15.5t-15.5 35.5v735h-237q-23 0 -38 14.5t-15 37.5zM674 244h153q76 0 111 28.5t35 94.5t-35 98.5t-111 32.5h-153 v-254z" />
<glyph unicode="&#x44b;" horiz-adv-x="1708" d="M117 51v963q0 23 15 37t36 14h258q23 0 37 -14.5t14 -36.5v-275h185q205 0 320.5 -99t115.5 -273q0 -178 -118 -272.5t-329 -94.5h-483q-21 0 -36 15.5t-15 35.5zM471 244h160q76 0 110.5 28.5t34.5 94.5t-34.5 98.5t-110.5 32.5h-160v-254zM1221 51v963q0 23 15 37 t36 14h256q23 0 38 -14.5t15 -36.5v-963q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="&#x44c;" horiz-adv-x="1155" d="M117 51v963q0 23 15 37t36 14h258q23 0 37 -14.5t14 -36.5v-275h193q205 0 319.5 -99t114.5 -273q0 -178 -117 -272.5t-328 -94.5h-491q-21 0 -36 15.5t-15 35.5zM471 244h166q76 0 111.5 28.5t35.5 94.5q0 131 -147 131h-166v-254z" />
<glyph unicode="&#x44d;" horiz-adv-x="1228" d="M86 305v4q0 14 12.5 24.5t24.5 10.5h250q23 0 32 -7t21 -24q23 -45 61.5 -65.5t102.5 -20.5q164 0 170 189v10h-289q-18 0 -30.5 13.5t-12.5 31.5v123q0 18 12.5 31.5t30.5 13.5h289v12q-4 88 -47 136.5t-123 48.5q-63 0 -101 -19.5t-65 -68.5q-2 -2 -10 -12.5 t-19.5 -14.5t-25.5 -4h-240q-16 0 -27.5 12t-9.5 27q10 76 59.5 150.5t158 126.5t280.5 52q250 0 392 -119.5t155 -340.5q4 -53 4 -93q0 -35 -4 -92q-12 -225 -150.5 -342.5t-396.5 -117.5q-254 0 -371 99t-133 226z" />
<glyph unicode="&#x44e;" horiz-adv-x="1800" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-334h154q29 193 169 299t371 106q250 0 392.5 -121.5t154.5 -338.5q4 -54 4 -93t-4 -92q-12 -221 -150 -340.5t-397 -119.5q-246 0 -383 109.5t-159 311.5h-152v-350q0 -23 -15.5 -37t-37.5 -14h-256 q-20 0 -35.5 15.5t-15.5 35.5zM997 532l2 -81q6 -109 47 -161t125 -52t125 52t47 161q3 20 3 81t-3 82q-6 106 -47 159.5t-125 53.5t-124.5 -53t-47.5 -160z" />
<glyph unicode="&#x44f;" horiz-adv-x="1249" d="M98 41q0 16 11 29l202 336q-86 45 -131 120.5t-45 178.5q0 174 116 267t322 93h498q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-264q-23 0 -38 14.5t-15 36.5v287h-140l-165 -307q-14 -31 -48 -31h-258q-20 0 -32.5 12.5t-12.5 28.5zM455 705 q0 -57 33.5 -90t99.5 -33h174v239h-168q-72 0 -105.5 -27.5t-33.5 -88.5z" />
<glyph unicode="&#x450;" horiz-adv-x="1222" d="M76 504v63q8 244 151.5 381t386.5 137q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t116 -56.5q49 0 79 19.5t57 50.5q18 20 29 25t36 5h268q18 0 31.5 -11t13.5 -27q0 -53 -60 -125t-176 -123t-274 -51 q-249 0 -392.5 134t-147.5 390zM170 1499q0 18 10.5 29.5t28.5 11.5h281q27 0 41 -7t35 -32l184 -223q10 -10 10 -27q0 -33 -33 -32h-194q-25 0 -41 7t-37 23l-274 226q-10 10 -11 24zM453 643h325v4q0 98 -43 152.5t-121 54.5t-119.5 -54.5t-41.5 -152.5v-4z" />
<glyph unicode="&#x451;" horiz-adv-x="1222" d="M76 504v63q8 244 151.5 381t386.5 137q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t116 -56.5q49 0 79 19.5t57 50.5q18 20 29 25t36 5h268q18 0 31.5 -11t13.5 -27q0 -53 -60 -125t-176 -123t-274 -51 q-249 0 -392.5 134t-147.5 390zM256 1286v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM453 643h325v4q0 98 -43 152.5t-121 54.5t-119.5 -54.5t-41.5 -152.5v-4zM686 1286v197 q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x452;" horiz-adv-x="1357" d="M20 1155v80q0 23 15.5 37t36.5 14h75v117q0 23 15.5 37t36.5 14h278q23 0 37 -14.5t14 -36.5v-117h258q23 0 37.5 -14.5t14.5 -36.5v-80q0 -20 -14.5 -35.5t-37.5 -15.5h-258v-240q119 139 324 140q193 0 301.5 -128t108.5 -335v-408q0 -244 -135.5 -383t-387.5 -139h-71 q-21 0 -36.5 14.5t-15.5 36.5v176q0 20 15.5 35.5t36.5 15.5h30q104 0 142.5 64.5t38.5 187.5v387q0 92 -44 141.5t-128 49.5q-82 0 -130.5 -50.5t-48.5 -140.5v-477q0 -20 -14 -35.5t-37 -15.5h-278q-20 0 -36 15.5t-16 35.5v1053h-75q-20 0 -36 15.5t-16 35.5z" />
<glyph unicode="&#x453;" horiz-adv-x="946" d="M117 51v963q0 23 15 37t36 14h692q23 0 37 -14.5t14 -36.5v-176q0 -20 -14 -36t-37 -16h-383v-735q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5zM378 1251q0 16 11 27l182 223q20 25 35.5 32t40.5 7h282q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-274 -226 q-20 -16 -37 -23t-43 -7h-193q-33 -1 -33 32z" />
<glyph unicode="&#x454;" horiz-adv-x="1228" d="M90 532l2 93q10 221 152.5 340.5t394.5 119.5q236 0 357.5 -95t140.5 -234v-6q0 -14 -10.5 -23.5t-26.5 -9.5h-240q-31 0 -45 22q-33 53 -73 75t-103 22q-78 0 -123 -47.5t-47 -139.5v-10h289q18 0 30.5 -12.5t12.5 -32.5v-123q0 -18 -12.5 -31.5t-30.5 -13.5h-289v-12 q2 -98 45 -142.5t125 -44.5q68 0 104.5 20.5t69.5 74.5q14 23 43 22h250q16 0 27.5 -12.5t9.5 -26.5q-18 -139 -140 -232t-364 -93q-258 0 -396.5 116.5t-150.5 343.5z" />
<glyph unicode="&#x455;" horiz-adv-x="1140" d="M61 258q0 20 15.5 33.5t34.5 13.5h247q12 0 21 -8q35 -23 39 -27q39 -29 72.5 -44t80.5 -15q55 0 91 21.5t36 60.5q0 33 -19.5 53.5t-73.5 40t-165 39.5q-354 72 -354 328q0 84 54.5 160.5t160 123.5t252.5 47q150 0 259.5 -46t166.5 -110.5t57 -117.5q0 -18 -13 -32.5 t-32 -14.5h-225q-18 0 -29 8q-25 12 -49 31q-37 25 -64.5 38t-70.5 13q-51 0 -80 -22.5t-29 -57.5q0 -31 17.5 -50t72 -37.5t164.5 -39.5q201 -35 292 -124t91 -208q0 -154 -136 -244.5t-382 -90.5q-168 0 -280.5 47t-167 112.5t-54.5 118.5z" />
<glyph unicode="&#x456;" horiz-adv-x="593" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-963q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5zM117 1266v190q0 23 15 38t36 15h256q23 0 38 -15t15 -38v-190q0 -23 -15.5 -38.5t-37.5 -15.5h-256q-23 0 -37 15.5t-14 38.5z" />
<glyph unicode="&#x457;" horiz-adv-x="593" d="M-62 1286v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-963q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5z M368 1286v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x458;" horiz-adv-x="630" d="M-113 -162q0 20 14.5 35.5t37.5 15.5h81q68 0 94.5 37t26.5 107v981q0 23 15.5 37t36.5 14h268q23 0 37 -14.5t14 -36.5v-989q0 -199 -119 -306.5t-332 -107.5h-122q-23 0 -37.5 14.5t-14.5 36.5v176zM139 1266v190q0 23 15.5 38t38.5 15h268q23 0 38 -15t15 -38v-190 q0 -23 -15.5 -38.5t-37.5 -15.5h-268q-23 0 -38.5 15.5t-15.5 38.5z" />
<glyph unicode="&#x459;" horiz-adv-x="1796" d="M51 53v191q0 41 51 51q57 10 88 51t44.5 130t13.5 245v293q0 23 15.5 37t37.5 14h799q23 0 38 -14.5t15 -36.5v-275h156q205 0 320.5 -99t115.5 -273q0 -178 -118 -272.5t-329 -94.5h-454q-23 0 -38 14.5t-15 36.5v735h-201v-104q0 -268 -41 -411.5t-144.5 -205 t-300.5 -65.5q-23 0 -38 15.5t-15 37.5zM1145 244h133q76 0 110.5 28.5t34.5 94.5t-34.5 98.5t-110.5 32.5h-133v-254z" />
<glyph unicode="&#x45a;" horiz-adv-x="1802" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-334h322v334q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-275h156q205 0 320.5 -99t115.5 -273q0 -178 -117.5 -272.5t-328.5 -94.5h-455q-20 0 -35.5 15.5t-15.5 35.5v350h-322v-350q0 -23 -15.5 -37 t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5zM1151 244h133q76 0 111 28.5t35 94.5t-35 98.5t-111 32.5h-133v-254z" />
<glyph unicode="&#x45b;" horiz-adv-x="1413" d="M20 1155v80q0 23 15.5 37t38.5 14h73v117q0 23 15.5 37t38.5 14h276q23 0 38 -14.5t15 -36.5v-117h256q23 0 37.5 -14.5t14.5 -36.5v-80q0 -20 -14.5 -35.5t-37.5 -15.5h-256v-240q119 139 322 140q195 0 303.5 -128t108.5 -335v-490q0 -23 -15.5 -37t-38.5 -14h-278 q-20 0 -35.5 15.5t-15.5 35.5v477q0 90 -45 140.5t-129 50.5q-82 0 -129.5 -50.5t-47.5 -140.5v-477q0 -23 -15 -37t-38 -14h-276q-23 0 -38.5 14.5t-15.5 36.5v1053h-73q-23 0 -38.5 14.5t-15.5 36.5z" />
<glyph unicode="&#x45c;" horiz-adv-x="1255" d="M117 51v963q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-334h107l215 352q23 33 57 33h274q20 0 33.5 -12.5t13.5 -30.5q0 -8 -10 -29l-276 -446l323 -477q9 -11 9 -27q0 -18 -12.5 -30.5t-33.5 -12.5h-305q-37 0 -59 33l-234 366h-102v-348q0 -20 -14.5 -35.5 t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5zM477 1251q0 16 11 27l182 223q20 25 35.5 32t40.5 7h282q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-274 -226q-20 -16 -37 -23t-43 -7h-193q-33 -1 -33 32z" />
<glyph unicode="&#x45d;" horiz-adv-x="1312" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-555l400 573q23 33 57 33h229q21 0 36 -14.5t15 -34.5v-965q0 -20 -14 -35.5t-37 -15.5h-256q-23 0 -38 14.5t-15 36.5v555l-399 -573q-23 -33 -58 -33h-229q-20 0 -35.5 15.5t-15.5 35.5zM237 1499 q0 18 10.5 29.5t28.5 11.5h281q27 0 41 -7t35 -32l184 -223q10 -10 10 -27q0 -33 -33 -32h-194q-25 0 -41 7t-37 23l-274 226q-10 10 -11 24z" />
<glyph unicode="&#x45e;" horiz-adv-x="1222" d="M37 1018q4 20 17.5 33.5t31.5 13.5h244q43 0 59 -43l223 -576l236 576q16 43 63 43h232q18 0 31.5 -13.5t13.5 -29.5q0 -14 -6 -27l-389 -940q-66 -156 -125.5 -249t-140.5 -145t-199 -52h-103q-23 0 -38 15.5t-15 37.5v174q0 23 15.5 37t37.5 14h72q53 0 82 30t63 114 l9 18l-408 946q-6 16 -6 23zM260 1501q0 16 10 27.5t29 11.5h174q18 0 28.5 -11t10.5 -28q0 -106 112 -106q115 0 115 106q0 16 10 27.5t29 11.5h172q18 0 28.5 -11t10.5 -28q0 -133 -90 -216t-275 -83q-184 0 -274 83t-90 216z" />
<glyph unicode="&#x45f;" horiz-adv-x="1298" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-735h336v735q0 23 15.5 37t35.5 14h256q23 0 37 -14.5t14 -36.5v-963q0 -20 -14 -35.5t-37 -15.5h-297v-186q0 -23 -14 -38.5t-37 -15.5h-256q-22 0 -36.5 15.5t-14.5 38.5v186h-297q-20 0 -35.5 15.5 t-15.5 35.5z" />
<glyph unicode="&#x462;" horiz-adv-x="1503" d="M41 1118v115q0 23 12.5 35t34.5 12h197v115q0 23 14 38t37 15h280q23 0 37.5 -15.5t14.5 -37.5v-115h344q23 0 35 -12.5t12 -34.5v-115q0 -20 -13.5 -33.5t-33.5 -13.5h-344v-125h206q271 0 416.5 -116.5t145.5 -348.5q0 -221 -146.5 -351t-415.5 -130h-538 q-20 0 -35.5 15.5t-15.5 35.5v1020h-197q-20 0 -33.5 13.5t-13.5 33.5zM668 295h200q80 0 125 51t45 133q0 174 -170 174h-200v-358z" />
<glyph unicode="&#x463;" horiz-adv-x="1204" d="M37 989v78q0 23 15.5 38t37.5 15h90v283q0 23 15.5 37t35.5 14h258q23 0 37.5 -14.5t14.5 -36.5v-283h241q23 0 37.5 -15t14.5 -38v-78q0 -23 -14.5 -38t-37.5 -15h-241v-197h178q205 0 319.5 -99t114.5 -273q0 -178 -116.5 -272.5t-327.5 -94.5h-478q-20 0 -35.5 15.5 t-15.5 35.5v885h-90q-23 0 -38 15.5t-15 37.5zM535 244h153q76 0 111 28.5t35 94.5t-35 98.5t-111 32.5h-153v-254z" />
<glyph unicode="&#x46a;" horiz-adv-x="1957" d="M43 49q0 8 4 21l154 444q51 150 139 216.5t223 74.5h47l-493 559q-8 8 -8 25q0 18 12 31.5t29 13.5h1658q16 0 30.5 -13.5t14.5 -29.5t-14 -31l-508 -553h47q143 -8 234.5 -75.5t142.5 -217.5l154 -444q4 -12 4 -21q0 -20 -12.5 -34.5t-32.5 -14.5h-289q-57 0 -70 41 l-112 346q-23 63 -55.5 91t-88.5 28h-82v-455q0 -23 -15 -37t-38 -14h-278q-23 0 -38.5 14.5t-15.5 36.5v455h-81q-57 0 -90 -27.5t-54 -91.5l-115 -346q-12 -41 -69 -41h-289q-20 0 -32.5 14.5t-12.5 34.5zM702 1147l271 -330l274 330h-545z" />
<glyph unicode="&#x46b;" horiz-adv-x="1859" d="M20 43q0 10 9 27l200 307q72 109 146 150.5t160 41.5h90l-318 418q-10 14 -10 31q0 20 14.5 33.5t32.5 13.5h1169q20 0 34 -13.5t14 -31.5q0 -14 -11 -31l-315 -420h76q96 0 170.5 -40t142.5 -142l205 -317q10 -14 10 -27q0 -18 -13.5 -30.5t-33.5 -12.5h-303 q-35 0 -62 33l-188 313q-18 33 -41.5 44t-56.5 11h-35v-350q0 -20 -14.5 -35.5t-36.5 -15.5h-250q-23 0 -38 14.5t-15 36.5v350h-33q-68 0 -98 -55l-191 -313q-20 -33 -59 -33h-305q-19 0 -32.5 12.5t-13.5 30.5zM723 858l207 -291l205 291h-412z" />
<glyph unicode="&#x472;" horiz-adv-x="1464" d="M94 713q0 119 2 178q8 270 179 416.5t458 146.5q285 0 456 -146.5t179 -416.5q4 -119 4 -178q0 -61 -4 -176q-10 -274 -177 -415.5t-458 -141.5t-459 141t-178 416q-2 57 -2 176zM492 616l2 -67q4 -133 67.5 -197.5t171.5 -64.5q107 0 170.5 64.5t67.5 197.5l2 67h-481z M492 823h481l-2 62q-4 133 -67.5 197.5t-170.5 64.5q-108 0 -171.5 -64.5t-67.5 -197.5z" />
<glyph unicode="&#x473;" horiz-adv-x="1257" d="M80 532q0 66 2 93q12 217 154.5 338.5t392.5 121.5t392 -121.5t155 -338.5q4 -54 4 -93t-4 -92q-12 -221 -150.5 -340.5t-396.5 -119.5t-396.5 119.5t-150.5 340.5q-2 27 -2 92zM457 422q6 -109 47 -160t125 -51t125 51t47 160v35h-344v-35zM457 608h344v35 q-6 106 -47 158.5t-125 52.5t-125 -52t-47 -159v-35z" />
<glyph unicode="&#x474;" horiz-adv-x="1443" d="M47 1389q0 20 11.5 32.5t31.5 12.5h268q31 0 50.5 -16.5t25.5 -41.5l281 -923l174 579q63 215 167.5 311.5t274.5 96.5h68q20 0 32.5 -14.5t12.5 -39.5v-251q0 -20 -14.5 -36t-34.5 -16h-39q-45 0 -76 -38.5t-59 -131.5l-258 -852q-18 -61 -84 -61h-330q-63 0 -86 61 l-414 1311q-2 6 -2 17z" />
<glyph unicode="&#x475;" horiz-adv-x="1232" d="M37 1018q0 20 13 33.5t34 13.5h244q43 0 61 -43l223 -614l154 409q37 92 71.5 143.5t89 78t142.5 26.5h84q23 0 38 -15.5t15 -37.5v-172q0 -23 -14 -38.5t-35 -15.5q-53 0 -85 -32.5t-64 -116.5l-226 -586q-20 -51 -69 -51h-217q-29 0 -44.5 13.5t-25.5 37.5l-387 950 q-2 6 -2 17z" />
<glyph unicode="&#x490;" horiz-adv-x="1163" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h562v235q0 23 15 37t36 14h280q23 0 37.5 -14t14.5 -37v-504q0 -23 -14.5 -38t-37.5 -15h-559v-1061q0 -23 -15 -37t-38 -14h-281q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x491;" horiz-adv-x="946" d="M117 51v963q0 23 15 37t36 14h385v186q0 23 14.5 38.5t36.5 15.5h256q23 0 37 -15.5t14 -38.5v-413q0 -20 -14 -36t-37 -16h-383v-735q0 -23 -15.5 -37t-37.5 -14h-256q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x492;" horiz-adv-x="1175" d="M-12 639v102q0 23 15 37.5t36 14.5h100v587q0 23 14.5 38.5t36.5 15.5h893q23 0 37.5 -15.5t14.5 -38.5v-215q0 -23 -14.5 -38t-37.5 -15h-573v-319h229q23 0 37.5 -14.5t14.5 -37.5v-102q0 -23 -14.5 -38t-37.5 -15h-229v-535q0 -23 -15.5 -37t-37.5 -14h-267 q-20 0 -35.5 15.5t-15.5 35.5v535h-100q-23 0 -37 15t-14 38z" />
<glyph unicode="&#x493;" horiz-adv-x="946" d="M-18 455v102q0 23 15 37t36 14h84v406q0 23 15 37t36 14h692q23 0 37 -14.5t14 -36.5v-176q0 -20 -14 -36t-37 -16h-383v-178h180q23 0 38.5 -14t15.5 -37v-102q0 -23 -15.5 -38.5t-38.5 -15.5h-180v-350q0 -23 -15.5 -37t-37.5 -14h-256q-20 0 -35.5 15.5t-15.5 35.5 v350h-84q-23 0 -37 15.5t-14 38.5z" />
<glyph unicode="&#x494;" horiz-adv-x="1384" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h893q23 0 37.5 -15.5t14.5 -38.5v-215q0 -23 -14.5 -38t-37.5 -15h-559v-346q53 41 131 63.5t172 22.5q490 0 490 -471v-109q0 -219 -149.5 -332.5t-432.5 -113.5h-16q-23 0 -38 14.5t-15 36.5v184q0 23 15 37.5t38 14.5h16 q109 0 153 42t44 119v74q0 92 -47 135t-150 43q-96 0 -153.5 -32.5t-57.5 -118.5v-324q0 -23 -15 -37t-38 -14h-281q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x495;" horiz-adv-x="1181" d="M117 51v963q0 23 15 37t36 14h692q23 0 37 -14.5t14 -36.5v-176q0 -20 -14 -36t-37 -16h-411v-247q117 76 294 75q191 0 298.5 -128t107.5 -334v-60q0 -225 -135 -353t-389 -128h-45q-20 0 -36 14.5t-16 36.5v176q0 20 15.5 35.5t36.5 15.5h30q106 0 144.5 53.5 t38.5 157.5v39q0 92 -45.5 141.5t-135.5 49.5q-76 0 -119.5 -21.5t-43.5 -66.5v-191q0 -20 -14.5 -35.5t-37.5 -15.5h-229q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x496;" horiz-adv-x="2271" d="M45 47q0 16 4 23l416 673l-379 623q-6 12 -6 25q0 18 12 30.5t31 12.5h311q51 0 78 -41l295 -498h111v487q0 23 15 37.5t36 14.5h266q23 0 38 -14.5t15 -37.5v-487h109l295 498q23 41 77 41h312q20 0 32.5 -12.5t12.5 -30.5q0 -8 -8 -25l-379 -623l248 -401h196 q23 0 38 -15.5t15 -37.5v-525q0 -23 -15 -37t-38 -14h-278q-23 0 -38.5 14.5t-15.5 36.5v236h-59q-49 0 -72 41l-305 512h-127v-502q0 -23 -15.5 -37t-37.5 -14h-266q-23 0 -37 14.5t-14 36.5v502h-127l-306 -512q-12 -18 -27.5 -29.5t-43.5 -11.5h-324q-18 0 -31.5 13.5 t-13.5 33.5z" />
<glyph unicode="&#x497;" horiz-adv-x="1918" d="M37 43q0 12 10 27l326 479l-279 444q-10 14 -10 29q0 18 13.5 30.5t31.5 12.5h277q35 0 57 -33l213 -352h94v334q0 23 15.5 37t37.5 14h250q23 0 37 -14.5t14 -36.5v-334h95l213 352q23 33 57 33h276q18 0 31.5 -12.5t13.5 -30.5q0 -14 -10 -29l-278 -444l184 -270h135 q23 0 37 -14.5t14 -37.5v-413q0 -23 -14 -38.5t-37 -15.5h-256q-23 0 -37 15.5t-14 38.5v186h-61l-3 10q-14 8 -24 23l-234 368h-88v-350q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5v350h-88l-233 -368q-27 -33 -62 -33h-303q-20 0 -33.5 12.5t-13.5 30.5z " />
<glyph unicode="&#x498;" horiz-adv-x="1384" d="M76 379v4q0 16 13 27.5t30 11.5h278q25 0 39.5 -9.5t26.5 -27.5q47 -100 215 -100q106 0 167.5 38.5t61.5 110.5q0 74 -56 112t-161 38h-151q-20 0 -36 16t-16 39v174q0 23 15.5 39t36.5 16h147q86 0 135 34t49 106q0 63 -50 101t-140 38q-86 0 -138.5 -26.5t-66.5 -76.5 q-10 -18 -22.5 -26t-34.5 -8h-285q-18 0 -32.5 12t-12.5 31q18 168 162.5 284.5t437.5 116.5q172 0 302 -55.5t201 -149.5t71 -205q0 -88 -36 -164.5t-124 -125.5q106 -53 154.5 -143.5t48.5 -192.5q0 -160 -112 -271.5t-310 -148.5v-234q0 -23 -14.5 -37t-37.5 -14h-280 q-20 0 -35.5 14.5t-15.5 36.5v230q-203 37 -306.5 143.5t-117.5 241.5z" />
<glyph unicode="&#x499;" horiz-adv-x="1153" d="M72 293v4q0 12 12 22.5t25 10.5h249q23 0 34 -7.5t22 -23.5q16 -37 55 -54.5t106 -17.5q70 0 108 26t38 79q0 49 -29.5 74.5t-99.5 25.5h-115q-18 0 -31.5 13.5t-13.5 31.5v123q0 18 12.5 31.5t32.5 13.5h109q61 0 88.5 21.5t27.5 66.5t-32.5 74t-102.5 29 q-68 0 -99.5 -18.5t-51.5 -61.5q-10 -16 -21.5 -23.5t-34.5 -7.5h-241q-16 0 -27.5 11.5t-9.5 25.5q8 78 57 151.5t155.5 122.5t276.5 49q248 0 363 -83.5t115 -219.5q0 -158 -134 -221q160 -57 164 -260q0 -108 -77.5 -186t-217.5 -113v-188q0 -23 -14 -38.5t-37 -15.5 h-256q-23 0 -37 15.5t-14 38.5v176q-178 29 -260 117t-94 186z" />
<glyph unicode="&#x49a;" horiz-adv-x="1511" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-485h115l295 498q27 41 78 41h313q18 0 30.5 -12.5t12.5 -30.5q0 -14 -8 -25l-379 -623l248 -401h196q23 0 38.5 -15.5t15.5 -37.5v-525q0 -23 -15.5 -37t-38.5 -14h-278q-23 0 -38 14.5t-15 36.5v236 h-60q-49 0 -71 41l-306 512h-133v-502q0 -23 -15 -37t-38 -14h-281q-23 0 -37 14.5t-14 36.5z" />
<glyph unicode="&#x49b;" horiz-adv-x="1314" d="M117 51v963q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-334h107l215 352q23 33 57 33h274q20 0 33.5 -12.5t13.5 -30.5q0 -8 -10 -29l-276 -446l182 -268h168q22 0 36.5 -14.5t14.5 -37.5v-413q0 -23 -14.5 -38.5t-36.5 -15.5h-256q-23 0 -37 15.5t-14 38.5v186h-62 q-37 0 -59 33l-234 366h-102v-348q0 -20 -14.5 -35.5t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x49c;" horiz-adv-x="1556" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-526h82v160q0 23 15.5 37t35.5 14h21q23 0 37 -14.5t14 -36.5v-152l315 531q23 41 76 41h314q18 0 30.5 -12.5t12.5 -30.5q0 -12 -7 -25l-378 -623l415 -673q4 -7 4 -23q0 -20 -13 -33.5t-32 -13.5 h-323q-29 0 -44.5 11.5t-27.5 29.5l-330 553h-12v-164q0 -23 -14.5 -38t-36.5 -15h-21q-23 0 -37 15.5t-14 37.5v164h-82v-543q0 -23 -15 -37t-38 -14h-281q-23 0 -37 14.5t-14 36.5z" />
<glyph unicode="&#x49d;" horiz-adv-x="1378" d="M117 51v963q0 23 15 37t36 14h252q23 0 37 -14.5t14 -36.5v-363h74v150q0 23 14 38t37 15h18q23 0 38.5 -15.5t15.5 -37.5v-150h14l233 381q23 33 58 33h274q20 0 33.5 -12.5t13.5 -30.5q0 -8 -10 -29l-276 -446l323 -477q8 -11 8 -27q0 -18 -12 -30.5t-33 -12.5h-305 q-37 0 -59 33l-252 393h-10v-154q0 -23 -15.5 -37t-38.5 -14h-18q-20 0 -35.5 15.5t-15.5 35.5v154h-74v-375q0 -20 -14.5 -35.5t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x4a0;" horiz-adv-x="1763" d="M41 1151v229q0 23 15.5 38.5t37.5 15.5h692q23 0 38.5 -15.5t15.5 -38.5v-485h114l297 498q23 41 76 41h313q18 0 30.5 -12.5t12.5 -30.5q0 -12 -6 -25l-379 -623l416 -673q4 -7 4 -23q0 -20 -13 -33.5t-32 -13.5h-323q-29 0 -44.5 11.5t-27.5 29.5l-311 512h-127v-502 q0 -23 -15.5 -37t-38.5 -14h-278q-23 0 -38 14.5t-15 36.5v1049h-361q-23 0 -38 14t-15 37z" />
<glyph unicode="&#x4a1;" horiz-adv-x="1511" d="M29 838v176q0 23 15 37t38 14h594q23 0 38 -14.5t15 -36.5v-334h102l218 352q12 16 24 24.5t33 8.5h276q20 0 34 -12.5t14 -30.5q0 -8 -11 -29l-280 -444l327 -479q9 -17 9 -27q0 -18 -13.5 -30.5t-31.5 -12.5h-306q-37 0 -59 33l-238 366h-98v-348q0 -23 -15.5 -37 t-37.5 -14h-250q-20 0 -35.5 15.5t-15.5 35.5v735h-293q-23 0 -38 14.5t-15 37.5z" />
<glyph unicode="&#x4a2;" horiz-adv-x="1605" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-485h484v485q0 23 15 38.5t38 15.5h278q23 0 38.5 -15.5t15.5 -38.5v-1038h94q22 0 37.5 -15.5t15.5 -37.5v-525q0 -23 -15.5 -37t-37.5 -14h-279q-23 0 -38 14.5t-15 36.5v236h-94q-23 0 -38 14.5 t-15 36.5v502h-484v-502q0 -23 -15 -37t-38 -14h-281q-23 0 -37 14.5t-14 36.5z" />
<glyph unicode="&#x4a3;" horiz-adv-x="1363" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-334h348v334q0 23 15.5 37t38.5 14h256q23 0 37 -14.5t14 -36.5v-735h86q22 0 36.5 -14.5t14.5 -37.5v-413q0 -23 -14.5 -38.5t-36.5 -15.5h-256q-23 0 -37 15.5t-14 38.5v186h-86q-23 0 -38.5 14.5t-15.5 36.5 v350h-348v-350q0 -23 -15.5 -37t-37.5 -14h-256q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x4a4;" horiz-adv-x="1935" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h281q23 0 38 -15.5t15 -38.5v-485h484v485q0 23 15 38.5t38 15.5h794q23 0 38.5 -15.5t15.5 -38.5v-215q0 -23 -15.5 -38t-38.5 -15h-462v-1061q0 -23 -15.5 -37t-38.5 -14h-278q-23 0 -38 14.5t-15 36.5v502h-484v-502 q0 -23 -15 -37t-38 -14h-281q-23 0 -37 14.5t-14 36.5z" />
<glyph unicode="&#x4a5;" horiz-adv-x="1593" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-334h348v334q0 23 15.5 37t38.5 14h628q23 0 38.5 -14.5t15.5 -36.5v-176q0 -23 -15.5 -37.5t-38.5 -14.5h-321v-735q0 -20 -14.5 -35.5t-36.5 -15.5h-256q-23 0 -38.5 14.5t-15.5 36.5v350h-348v-350 q0 -23 -15.5 -37t-37.5 -14h-256q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x4aa;" horiz-adv-x="1445" d="M94 715q0 127 2 184q10 266 179 410.5t458 144.5q182 0 326.5 -59.5t228.5 -172t88 -266.5q0 -18 -13 -30.5t-32 -12.5h-291q-29 0 -42 11.5t-23 42.5q-29 100 -87.5 140t-154.5 40q-231 0 -239 -258q-2 -55 -2 -170t2 -174q8 -258 239 -258q94 0 154.5 41t87.5 139 q8 31 22.5 42t42.5 11h291q19 0 32 -12t13 -31q-4 -186 -126 -311t-322 -168v-234q0 -23 -14.5 -37t-36.5 -14h-281q-20 0 -35.5 14.5t-15.5 36.5v232q-207 43 -324 179t-125 360q-2 55 -2 180z" />
<glyph unicode="&#x4ab;" d="M78 530l2 84q8 221 153.5 346t378.5 125q178 0 299 -63.5t180.5 -150.5t63.5 -158q2 -23 -14.5 -38.5t-38.5 -15.5h-275q-23 0 -35 10.5t-22 32.5q-23 57 -58.5 84t-90.5 27q-158 0 -164 -209l-2 -78l2 -67q2 -106 43 -156.5t121 -50.5q59 0 93 25.5t56 84.5 q10 23 22.5 33.5t34.5 10.5h275q20 0 36.5 -14.5t16.5 -35.5q0 -51 -37 -121.5t-116.5 -136t-200.5 -96.5v-188q0 -23 -14.5 -38.5t-36.5 -15.5h-256q-23 0 -37.5 15.5t-14.5 38.5v186q-162 39 -258 155.5t-104 293.5z" />
<glyph unicode="&#x4ae;" horiz-adv-x="1386" d="M20 1391q0 16 13.5 29.5t32.5 13.5h270q49 0 72 -45l284 -515l285 515q8 16 26.5 30.5t47.5 14.5h270q18 0 30.5 -13.5t12.5 -29.5q0 -10 -4 -23l-475 -881v-436q0 -20 -14.5 -35.5t-36.5 -15.5h-281q-23 0 -38 14.5t-15 36.5v436l-473 881q-6 18 -7 23z" />
<glyph unicode="&#x4af;" horiz-adv-x="1185" d="M16 1018q0 20 14.5 33.5t32.5 13.5h246q25 0 39 -13.5t21 -29.5l225 -612l223 612q18 43 62 43h243q20 0 33.5 -13.5t13.5 -33.5q0 -11 -2 -17l-391 -952v-387q0 -23 -14 -37t-37 -14h-264q-23 0 -38 14.5t-15 36.5v393l-388 946z" />
<glyph unicode="&#x4b0;" horiz-adv-x="1400" d="M20 1391q0 16 13.5 29.5t32.5 13.5h270q49 0 72 -45l284 -515l285 515q8 16 26.5 30.5t47.5 14.5h270q18 0 30.5 -13.5t12.5 -29.5q0 -11 -4 -23l-451 -836v-86q0 -8 -7 -14t-15 -6h-2v-375q0 -20 -14.5 -35.5t-36.5 -15.5h-281q-23 0 -38 14.5t-15 36.5v375h-23 q-8 0 -15 6t-7 14v89q0 12 10 18l-438 815q-6 18 -7 23z" />
<glyph unicode="&#x4b1;" horiz-adv-x="1185" d="M16 1018q0 20 14.5 33.5t32.5 13.5h246q25 0 39 -13.5t21 -29.5l225 -612l223 612q18 43 62 43h243q20 0 33.5 -13.5t13.5 -33.5q0 -11 -2 -17l-379 -923h117q23 0 38 -14.5t15 -36.5v-103q0 -23 -15 -38t-38 -15h-129v-209q0 -23 -14 -37t-37 -14h-264q-23 0 -38 14.5 t-15 36.5v209h-127q-23 0 -37.5 15.5t-14.5 37.5v103q0 23 15.5 37t36.5 14h118l-379 923z" />
<glyph unicode="&#x4b6;" horiz-adv-x="1501" d="M92 932v450q0 23 15.5 37.5t37.5 14.5h279q23 0 38 -14.5t15 -37.5v-432q0 -100 49 -146t162 -46q82 0 148.5 28.5t66.5 87.5v508q0 23 15.5 37.5t37.5 14.5h279q23 0 38 -14.5t15 -37.5v-1040h96q23 0 38.5 -15.5t15.5 -37.5v-525q0 -23 -15.5 -37t-38.5 -14h-278 q-23 0 -38 14.5t-15 36.5v236h-97q-23 0 -38 14.5t-15 36.5v432q-45 -39 -125 -58t-170 -19q-262 0 -389 130t-127 396z" />
<glyph unicode="&#x4b7;" horiz-adv-x="1282" d="M66 702v312q0 23 15 37t36 14h266q23 0 37 -14.5t14 -36.5v-293q0 -70 33 -98.5t111 -28.5q76 0 119.5 23.5t43.5 68.5v328q0 23 15.5 37t38.5 14h276q23 0 38 -14.5t15 -36.5v-735h66q22 0 36.5 -14.5t14.5 -37.5v-413q0 -23 -14.5 -38.5t-36.5 -15.5h-256 q-23 0 -37 15.5t-14 38.5v186h-88q-23 0 -38.5 14.5t-15.5 36.5v305q-92 -74 -274 -73q-199 0 -300 101t-101 318z" />
<glyph unicode="&#x4b8;" horiz-adv-x="1480" d="M92 932v450q0 23 15.5 37.5t37.5 14.5h279q23 0 38 -14.5t15 -37.5v-487q0 -92 46 -138t149 -52v169q0 23 15 37.5t36 14.5h20q23 0 37.5 -14.5t14.5 -37.5v-165q66 12 107.5 39.5t41.5 72.5v561q0 23 15.5 37.5t37.5 14.5h279q22 0 37.5 -14.5t15.5 -37.5v-1331 q0 -23 -15.5 -37t-37.5 -14h-279q-23 0 -38 14.5t-15 36.5v432q-53 -41 -149 -61v-146q0 -20 -14.5 -35.5t-37.5 -15.5h-20q-20 0 -35.5 15.5t-15.5 35.5v130h-35q-283 0 -414 128t-131 398z" />
<glyph unicode="&#x4b9;" horiz-adv-x="1325" d="M66 702v312q0 23 15 37t36 14h266q23 0 37 -14.5t14 -36.5v-307q0 -131 141 -142v129q0 23 15.5 37t38.5 14h18q23 0 37 -14t14 -37v-121q119 23 119 99v342q0 23 15.5 37t35.5 14h279q22 0 36.5 -14.5t14.5 -36.5v-963q0 -20 -14.5 -35.5t-36.5 -15.5h-279 q-20 0 -35.5 15.5t-15.5 35.5v332q-57 -35 -119 -49v-115q0 -20 -14 -35.5t-37 -15.5h-18q-23 0 -38.5 14.5t-15.5 36.5v94q-57 -4 -88 -4q-197 0 -309 99.5t-112 293.5z" />
<glyph unicode="&#x4ba;" horiz-adv-x="1439" d="M152 51v1331q0 23 15 37.5t38 14.5h278q23 0 38.5 -14.5t15.5 -37.5v-432q45 39 124.5 58.5t169.5 19.5q262 0 389.5 -130t127.5 -396v-451q0 -23 -15.5 -37t-38.5 -14h-278q-23 0 -38 14.5t-15 36.5v432q0 100 -49.5 146.5t-161.5 46.5q-82 0 -148.5 -29t-66.5 -88v-508 q0 -23 -15.5 -37t-38.5 -14h-278q-23 0 -38 14.5t-15 36.5z" />
<glyph unicode="&#x4bb;" horiz-adv-x="1339" d="M117 51v1352q0 23 15 37t38 14h276q23 0 38.5 -14.5t15.5 -36.5v-457q119 139 321 139q117 0 211 -53t147.5 -157.5t53.5 -251.5v-572q0 -23 -15.5 -37t-37.5 -14h-279q-20 0 -35.5 15.5t-15.5 35.5v559q0 90 -45 140.5t-129 50.5q-82 0 -129 -50.5t-47 -140.5v-559 q0 -23 -15.5 -37t-38.5 -14h-276q-23 0 -38 14.5t-15 36.5z" />
<glyph unicode="&#x4c0;" horiz-adv-x="673" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h293q23 0 37.5 -14.5t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5h-293q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x4c1;" horiz-adv-x="2203" d="M45 47q0 16 4 23l416 673l-379 623q-6 12 -6 25q0 18 12 30.5t31 12.5h311q51 0 78 -41l295 -498h111v487q0 23 15 37.5t36 14.5h266q23 0 38 -14.5t15 -37.5v-487h109l295 498q23 41 77 41h312q20 0 32.5 -12.5t12.5 -30.5q0 -8 -8 -25l-379 -623l415 -673q7 -9 7 -23 q0 -20 -13.5 -33.5t-33.5 -13.5h-322q-49 0 -72 41l-305 512h-127v-502q0 -23 -15.5 -37t-37.5 -14h-266q-23 0 -37 14.5t-14 36.5v502h-127l-306 -512q-12 -18 -27.5 -29.5t-43.5 -11.5h-324q-18 0 -31.5 13.5t-13.5 33.5zM733 1815q0 16 10 27t29 11h172q18 0 28.5 -11 t10.5 -27q0 -106 119 -107q117 0 116 107q0 16 10.5 27t28.5 11h172q18 0 28.5 -11t10.5 -27q0 -133 -90 -215t-276 -82q-189 0 -279 81.5t-90 215.5z" />
<glyph unicode="&#x4c2;" horiz-adv-x="1894" d="M37 43q0 12 10 27l326 479l-279 444q-10 14 -10 29q0 18 13.5 30.5t31.5 12.5h277q35 0 57 -33l213 -352h94v334q0 23 15.5 37t37.5 14h250q23 0 37 -14.5t14 -36.5v-334h95l213 352q23 33 57 33h276q18 0 31.5 -12.5t13.5 -30.5q0 -14 -10 -29l-278 -444l325 -479 q11 -15 11 -27q0 -18 -13.5 -30.5t-34.5 -12.5h-303q-35 0 -61 33l-234 368h-88v-350q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5v350h-88l-233 -368q-27 -33 -62 -33h-303q-20 0 -33.5 12.5t-13.5 30.5zM584 1501q0 16 10 27.5t29 11.5h174q18 0 28.5 -11 t10.5 -28q0 -106 112 -106q115 0 115 106q0 16 10 27.5t29 11.5h172q18 0 28.5 -11t10.5 -28q0 -133 -90 -216t-275 -83q-184 0 -274 83t-90 216z" />
<glyph unicode="&#x4cb;" horiz-adv-x="1439" d="M92 932v450q0 23 15.5 37.5t37.5 14.5h279q23 0 38 -14.5t15 -37.5v-432q0 -100 49 -146t162 -46q82 0 148.5 28.5t66.5 87.5v508q0 23 15.5 37.5t37.5 14.5h279q22 0 37.5 -14.5t15.5 -37.5v-1331q0 -23 -15.5 -37t-37.5 -14h-94v-236q0 -23 -15.5 -37t-38.5 -14h-278 q-23 0 -38 14.5t-15 36.5v390q0 23 15 37t38 14h94v278q-45 -39 -125 -58t-170 -19q-262 0 -389 130t-127 396z" />
<glyph unicode="&#x4cc;" horiz-adv-x="1239" d="M66 702v312q0 23 15 37t36 14h266q23 0 37 -14.5t14 -36.5v-293q0 -70 33 -98.5t111 -28.5q76 0 119.5 23.5t43.5 68.5v328q0 23 15.5 37t38.5 14h276q23 0 38 -14.5t15 -36.5v-963q0 -23 -15 -37t-38 -14h-98v-186q0 -23 -14.5 -38.5t-36.5 -15.5h-256q-23 0 -37.5 15.5 t-14.5 38.5v317q0 23 15.5 37t36.5 14h75v174q-92 -74 -274 -73q-199 0 -300 101t-101 318z" />
<glyph unicode="&#x4cf;" horiz-adv-x="593" d="M117 51v1352q0 23 15 37t38 14h256q23 0 37 -14.5t14 -36.5v-1352q0 -20 -14 -35.5t-37 -15.5h-256q-23 0 -38 14.5t-15 36.5z" />
<glyph unicode="&#x4d0;" horiz-adv-x="1474" d="M16 43q0 12 2 18l478 1313q20 59 82 60h319q61 0 82 -60l477 -1313q2 -6 2 -18q0 -16 -13 -29.5t-30 -13.5h-266q-49 0 -70 45l-73 199h-537l-74 -199q-20 -45 -69 -45h-267q-16 0 -29.5 13.5t-13.5 29.5zM369 1815q0 16 10 27t29 11h172q18 0 28.5 -11t10.5 -27 q0 -106 119 -107q117 0 116 107q0 16 10.5 27t28.5 11h172q18 0 28.5 -11t10.5 -27q0 -133 -90 -215t-276 -82q-189 0 -279 81.5t-90 215.5zM555 551h365l-183 522z" />
<glyph unicode="&#x4d1;" d="M51 299q0 139 116 226t318 120l254 39v25q0 76 -30.5 110.5t-104.5 34.5q-41 0 -68.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -33.5 13t-11.5 32q2 55 55 123.5t162 117.5t268 49q254 0 377 -111.5t123 -301.5v-621q0 -20 -14.5 -35.5 t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5v72q-45 -63 -125 -103t-191 -40q-108 0 -197 41.5t-139.5 115.5t-50.5 162zM263 1501q0 16 10 27.5t29 11.5h174q18 0 28.5 -11t10.5 -28q0 -106 112 -106q115 0 115 106q0 16 10 27.5t29 11.5h172q18 0 28.5 -11t10.5 -28 q0 -133 -90 -216t-275 -83q-184 0 -274 83t-90 216zM401 326q0 -47 40 -76t100 -29q90 0 146 59.5t56 172.5v24l-170 -28q-172 -33 -172 -123z" />
<glyph unicode="&#x4d2;" horiz-adv-x="1474" d="M16 43q0 12 2 18l478 1313q20 59 82 60h319q61 0 82 -60l477 -1313q2 -6 2 -18q0 -16 -13 -29.5t-30 -13.5h-266q-49 0 -70 45l-73 199h-537l-74 -199q-20 -45 -69 -45h-267q-16 0 -29.5 13.5t-13.5 29.5zM376 1655v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5 t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM555 551h365l-183 522zM806 1655v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x4d3;" d="M51 299q0 139 116 226t318 120l254 39v25q0 76 -30.5 110.5t-104.5 34.5q-41 0 -68.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -33.5 13t-11.5 32q2 55 55 123.5t162 117.5t268 49q254 0 377 -111.5t123 -301.5v-621q0 -20 -14.5 -35.5 t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5v72q-45 -63 -125 -103t-191 -40q-108 0 -197 41.5t-139.5 115.5t-50.5 162zM266 1286v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5z M401 326q0 -47 40 -76t100 -29q90 0 146 59.5t56 172.5v24l-170 -28q-172 -33 -172 -123zM696 1286v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x4d4;" horiz-adv-x="2000" d="M8 43q0 4 4 20q100 287 212 558.5t343 754.5q8 25 28.5 41.5t51.5 16.5h1196q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-606v-260h561q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-561v-266h623q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37 t-37.5 -14h-942q-20 0 -36 15.5t-16 35.5v193h-383q-74 -141 -94 -199q-16 -45 -67 -45h-269q-18 0 -31.5 13.5t-13.5 29.5zM575 551h291v571h-45z" />
<glyph unicode="&#x4d5;" horiz-adv-x="1878" d="M53 299q0 139 115 227t319 121l252 39v25q0 74 -29.5 108.5t-103.5 34.5q-43 0 -70.5 -14.5t-62.5 -42.5q-27 -23 -47 -33q-8 -14 -20 -14h-238q-20 0 -32.5 13t-12.5 32q0 55 53 123.5t163 117.5t269 49q231 0 336 -110q133 111 328 110q186 0 307 -77.5t175 -201.5 t54 -267v-52q0 -23 -15 -38t-38 -15h-643v-12q2 -102 45 -153.5t113 -51.5q80 0 135 70q16 20 28.5 25t36.5 5h267q18 0 30.5 -12t12.5 -31q0 -51 -59.5 -121.5t-174 -121.5t-274.5 -51q-129 0 -229.5 40.5t-165.5 116.5q-143 -158 -410 -157q-125 0 -219 41.5t-144.5 114.5 t-50.5 163zM401 326q0 -47 40 -76t100 -29q92 0 147 58.5t55 173.5v24l-168 -28q-174 -33 -174 -123zM1112 643h320v8q0 96 -41 149.5t-119 53.5q-70 0 -114 -51t-46 -152v-8z" />
<glyph unicode="&#x4d6;" horiz-adv-x="1314" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h967q23 0 38 -15.5t15 -38.5v-202q0 -23 -15 -37.5t-38 -14.5h-647v-260h602q23 0 38 -15t15 -38v-186q0 -23 -15 -38.5t-38 -15.5h-602v-266h664q23 0 38 -15.5t15 -37.5v-203q0 -23 -15.5 -37t-37.5 -14h-984q-20 0 -35.5 15.5 t-15.5 35.5zM330 1815q0 16 10 27t29 11h172q18 0 28.5 -11t10.5 -27q0 -106 119 -107q117 0 116 107q0 16 10.5 27t28.5 11h172q18 0 28.5 -11t10.5 -27q0 -133 -90 -215t-276 -82q-189 0 -279 81.5t-90 215.5z" />
<glyph unicode="&#x4d7;" horiz-adv-x="1222" d="M76 504v63q8 244 151.5 381t386.5 137q176 0 298 -70.5t182.5 -194.5t60.5 -281v-52q0 -23 -15.5 -38t-37.5 -15h-649v-12q2 -92 43 -148.5t116 -56.5q49 0 79 19.5t57 50.5q18 20 29 25t36 5h268q18 0 31.5 -11t13.5 -27q0 -53 -60 -125t-176 -123t-274 -51 q-249 0 -392.5 134t-147.5 390zM252 1501q0 16 10 27.5t29 11.5h174q18 0 28.5 -11t10.5 -28q0 -106 112 -106q115 0 115 106q0 16 10 27.5t29 11.5h172q18 0 28.5 -11t10.5 -28q0 -133 -90 -216t-275 -83q-184 0 -274 83t-90 216zM453 643h325v4q0 98 -43 152.5t-121 54.5 t-119.5 -54.5t-41.5 -152.5v-4z" />
<glyph unicode="&#x4d8;" horiz-adv-x="1460" d="M94 705v45q0 31 20.5 50t49.5 19h805l-2 70q-8 258 -240 258q-88 0 -141.5 -30t-75.5 -107q-10 -31 -24.5 -42.5t-41.5 -11.5h-280q-18 0 -31.5 12.5t-13.5 30.5q2 123 81 227.5t216 166t311 61.5q289 0 458 -144.5t179 -410.5q4 -114 4 -184t-4 -180 q-10 -270 -176 -412.5t-457 -142.5q-203 0 -346 90t-217 252.5t-74 382.5zM494 573q2 -139 62 -212.5t175 -73.5q228 0 236 258l2 69h-475v-41z" />
<glyph unicode="&#x4d9;" horiz-adv-x="1222" d="M68 526v52q0 23 15 38t38 15h649v12q-2 92 -43 148.5t-117 56.5q-49 0 -78.5 -19.5t-56.5 -50.5q-18 -20 -29.5 -25t-35.5 -5h-269q-18 0 -31.5 11t-13.5 27q0 53 60.5 125t176.5 123t273 51q250 0 393.5 -134t147.5 -390v-63q-8 -244 -151.5 -381t-387.5 -137 q-176 0 -298 70.5t-182 194.5t-60 281zM444 418q0 -98 43 -152.5t121 -54.5t120 54t42 153v4h-326v-4z" />
<glyph unicode="&#x4dc;" horiz-adv-x="2203" d="M45 47q0 16 4 23l416 673l-379 623q-6 12 -6 25q0 18 12 30.5t31 12.5h311q51 0 78 -41l295 -498h111v487q0 23 15 37.5t36 14.5h266q23 0 38 -14.5t15 -37.5v-487h109l295 498q23 41 77 41h312q20 0 32.5 -12.5t12.5 -30.5q0 -8 -8 -25l-379 -623l415 -673q7 -9 7 -23 q0 -20 -13.5 -33.5t-33.5 -13.5h-322q-49 0 -72 41l-305 512h-127v-502q0 -23 -15.5 -37t-37.5 -14h-266q-23 0 -37 14.5t-14 36.5v502h-127l-306 -512q-12 -18 -27.5 -29.5t-43.5 -11.5h-324q-18 0 -31.5 13.5t-13.5 33.5zM741 1655v197q0 20 12.5 33.5t32.5 13.5h197 q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM1171 1655v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x4dd;" horiz-adv-x="1894" d="M37 43q0 12 10 27l326 479l-279 444q-10 14 -10 29q0 18 13.5 30.5t31.5 12.5h277q35 0 57 -33l213 -352h94v334q0 23 15.5 37t37.5 14h250q23 0 37 -14.5t14 -36.5v-334h95l213 352q23 33 57 33h276q18 0 31.5 -12.5t13.5 -30.5q0 -14 -10 -29l-278 -444l325 -479 q11 -15 11 -27q0 -18 -13.5 -30.5t-34.5 -12.5h-303q-35 0 -61 33l-234 368h-88v-350q0 -20 -14 -35.5t-37 -15.5h-250q-23 0 -38 14.5t-15 36.5v350h-88l-233 -368q-27 -33 -62 -33h-303q-20 0 -33.5 12.5t-13.5 30.5zM587 1286v197q0 20 12.5 33.5t32.5 13.5h197 q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM1017 1286v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x4de;" horiz-adv-x="1384" d="M76 379v4q0 16 13 27.5t30 11.5h278q25 0 39.5 -9.5t26.5 -27.5q47 -100 215 -100q106 0 167.5 38.5t61.5 110.5q0 74 -56 112t-161 38h-151q-20 0 -36 16t-16 39v174q0 23 15.5 39t36.5 16h147q86 0 135 34t49 106q0 63 -50 101t-140 38q-86 0 -138.5 -26.5t-66.5 -76.5 q-10 -18 -22.5 -26t-34.5 -8h-285q-18 0 -32.5 12t-12.5 31q18 168 162.5 284.5t437.5 116.5q172 0 302 -55.5t201 -149.5t71 -205q0 -88 -36 -164.5t-124 -125.5q106 -53 154.5 -143.5t48.5 -192.5q0 -131 -76 -230.5t-215.5 -153.5t-327.5 -54q-203 0 -336 57t-198.5 147 t-75.5 195zM327 1655v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM757 1655v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194 q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x4df;" horiz-adv-x="1153" d="M72 293v4q0 12 12 22.5t25 10.5h249q23 0 34 -7.5t22 -23.5q16 -37 55 -54.5t106 -17.5q70 0 108 26t38 79q0 49 -29.5 74.5t-99.5 25.5h-115q-18 0 -31.5 13.5t-13.5 31.5v123q0 18 12.5 31.5t32.5 13.5h109q61 0 88.5 21.5t27.5 66.5t-32.5 74t-102.5 29 q-68 0 -99.5 -18.5t-51.5 -61.5q-10 -16 -21.5 -23.5t-34.5 -7.5h-241q-16 0 -27.5 11.5t-9.5 25.5q8 78 57 151.5t155.5 122.5t276.5 49q248 0 363 -83.5t115 -219.5q0 -158 -134 -221q160 -57 164 -260q0 -147 -136 -234t-372 -87q-178 0 -286.5 50t-156.5 120.5 t-56 142.5zM227 1286v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM657 1286v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194 q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x4e2;" horiz-adv-x="1601" d="M139 53v1327q0 23 15.5 38.5t38.5 15.5h278q23 0 38 -15.5t15 -38.5v-800l566 815q25 39 69 39h252q23 0 37 -15.5t14 -38.5v-1329q0 -20 -14 -35.5t-37 -15.5h-281q-23 0 -38 14.5t-15 36.5v770l-565 -782q-12 -18 -27.5 -28.5t-42.5 -10.5h-252q-22 0 -36.5 15.5 t-14.5 37.5zM473 1653v152q0 20 12.5 32.5t33.5 12.5h583q20 0 32.5 -12.5t12.5 -32.5v-152q0 -20 -12 -33.5t-33 -13.5h-583q-21 0 -33.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x4e3;" horiz-adv-x="1312" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-555l400 573q23 33 57 33h229q21 0 36 -14.5t15 -34.5v-965q0 -20 -14 -35.5t-37 -15.5h-256q-23 0 -38 14.5t-15 36.5v555l-399 -573q-23 -33 -58 -33h-229q-20 0 -35.5 15.5t-15.5 35.5zM346 1284v152 q0 20 12.5 32.5t33.5 12.5h583q20 0 32.5 -12.5t12.5 -32.5v-152q0 -20 -12 -33.5t-33 -13.5h-583q-21 0 -33.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x4e4;" horiz-adv-x="1601" d="M139 53v1327q0 23 15.5 38.5t38.5 15.5h278q23 0 38 -15.5t15 -38.5v-800l566 815q25 39 69 39h252q23 0 37 -15.5t14 -38.5v-1329q0 -20 -14 -35.5t-37 -15.5h-281q-23 0 -38 14.5t-15 36.5v770l-565 -782q-12 -18 -27.5 -28.5t-42.5 -10.5h-252q-22 0 -36.5 15.5 t-14.5 37.5zM450 1655v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM880 1655v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194 q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x4e5;" horiz-adv-x="1312" d="M117 51v963q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-555l400 573q23 33 57 33h229q21 0 36 -14.5t15 -34.5v-965q0 -20 -14 -35.5t-37 -15.5h-256q-23 0 -38 14.5t-15 36.5v555l-399 -573q-23 -33 -58 -33h-229q-20 0 -35.5 15.5t-15.5 35.5zM323 1286v197 q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM753 1286v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5 z" />
<glyph unicode="&#x4e6;" horiz-adv-x="1464" d="M94 713q0 119 2 178q8 270 179 416.5t458 146.5q285 0 456 -146.5t179 -416.5q4 -119 4 -178q0 -61 -4 -176q-10 -274 -177 -415.5t-458 -141.5t-459 141t-178 416q-2 57 -2 176zM372 1655v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197 q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM492 719q0 -111 2 -170q4 -133 67.5 -197.5t171.5 -64.5q107 0 170.5 64.5t67.5 197.5q4 119 4 170t-4 166q-4 133 -67.5 197.5t-170.5 64.5q-109 0 -172 -64.5t-67 -197.5q-2 -57 -2 -166zM802 1655v197 q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x4e7;" horiz-adv-x="1257" d="M80 532q0 66 2 93q12 217 154.5 338.5t392.5 121.5t392 -121.5t155 -338.5q4 -53 4 -93q0 -39 -4 -92q-12 -221 -150.5 -340.5t-396.5 -119.5t-396.5 119.5t-150.5 340.5q-2 27 -2 92zM268 1286v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197 q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM455 532l2 -81q6 -109 47 -161t125 -52t125 52t47 161q2 20 2 81t-2 82q-6 106 -47 159.5t-125 53.5t-125 -53t-47 -160zM698 1286v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197 q0 -20 -13 -32.5t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x4e8;" horiz-adv-x="1464" d="M94 713q0 119 2 178q8 270 179 416.5t458 146.5q285 0 456 -146.5t179 -416.5q4 -119 4 -178q0 -61 -4 -176q-10 -274 -177 -415.5t-458 -141.5t-459 141t-178 416q-2 57 -2 176zM492 614l2 -65q4 -133 67.5 -197.5t171.5 -64.5q107 0 170.5 64.5t67.5 197.5l2 65h-481z M492 819h481l-2 66q-4 133 -67.5 197.5t-170.5 64.5q-108 0 -171.5 -64.5t-67.5 -197.5z" />
<glyph unicode="&#x4e9;" horiz-adv-x="1257" d="M80 532q0 66 2 93q12 217 154.5 338.5t392.5 121.5t392 -121.5t155 -338.5q4 -54 4 -93t-4 -92q-12 -221 -150.5 -340.5t-396.5 -119.5t-396.5 119.5t-150.5 340.5q-2 27 -2 92zM457 422q6 -109 47 -160t125 -51t125 51t47 160v35h-344v-35zM457 608h344v35 q-6 106 -47 158.5t-125 52.5t-125 -52t-47 -159v-35z" />
<glyph unicode="&#x4ee;" horiz-adv-x="1341" d="M16 1386q0 18 14.5 33t35.5 15h301q37 0 59 -43l283 -560l245 560q16 43 62 43h282q21 0 33 -12.5t12 -30.5t-10 -39l-405 -934q-72 -166 -136.5 -253t-157.5 -126t-247 -39h-113q-20 0 -35.5 15.5t-15.5 35.5v230q0 23 14.5 38t36.5 15h80q57 0 97.5 22.5t72.5 73.5 l-501 940q-6 6 -7 16zM362 1653v152q0 20 12.5 32.5t33.5 12.5h583q20 0 32.5 -12.5t12.5 -32.5v-152q0 -20 -12 -33.5t-33 -13.5h-583q-21 0 -33.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x4ef;" horiz-adv-x="1222" d="M37 1018q4 20 17.5 33.5t31.5 13.5h244q43 0 59 -43l223 -576l236 576q16 43 63 43h232q18 0 31.5 -13.5t13.5 -29.5q0 -14 -6 -27l-389 -940q-66 -156 -125.5 -249t-140.5 -145t-199 -52h-103q-23 0 -38 15.5t-15 37.5v174q0 23 15.5 37t37.5 14h72q53 0 82 30t63 114 l9 18l-408 946q-6 16 -6 23zM286 1284v152q0 20 12.5 32.5t33.5 12.5h583q20 0 32.5 -12.5t12.5 -32.5v-152q0 -20 -12 -33.5t-33 -13.5h-583q-21 0 -33.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x4f0;" horiz-adv-x="1341" d="M16 1386q0 18 14.5 33t35.5 15h301q37 0 59 -43l283 -560l245 560q16 43 62 43h282q21 0 33 -12.5t12 -30.5t-10 -39l-405 -934q-72 -166 -136.5 -253t-157.5 -126t-247 -39h-113q-20 0 -35.5 15.5t-15.5 35.5v230q0 23 14.5 38t36.5 15h80q57 0 97.5 22.5t72.5 73.5 l-501 940q-6 6 -7 16zM340 1655v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM770 1655v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5 t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x4f1;" horiz-adv-x="1222" d="M37 1018q4 20 17.5 33.5t31.5 13.5h244q43 0 59 -43l223 -576l236 576q16 43 63 43h232q18 0 31.5 -13.5t13.5 -29.5q0 -14 -6 -27l-389 -940q-66 -156 -125.5 -249t-140.5 -145t-199 -52h-103q-23 0 -38 15.5t-15 37.5v174q0 23 15.5 37t37.5 14h72q53 0 82 30t63 114 l9 18l-408 946q-6 16 -6 23zM264 1286v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM694 1286v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5 t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x4f2;" horiz-adv-x="1341" d="M16 1386q0 18 14.5 33t35.5 15h301q37 0 59 -43l283 -560l245 560q16 43 62 43h282q21 0 33 -12.5t12 -30.5t-10 -39l-405 -934q-72 -166 -136.5 -253t-157.5 -126t-247 -39h-113q-20 0 -35.5 15.5t-15.5 35.5v230q0 23 14.5 38t36.5 15h80q57 0 97.5 22.5t72.5 73.5 l-501 940q-6 6 -7 16zM381 1620q0 10 10 31l99 219q10 20 27.5 29.5t48.5 9.5h225q18 0 29.5 -10t11.5 -29q0 -14 -12 -26l-205 -228q-25 -29 -68 -28h-133q-33 -1 -33 32zM828 1620q0 10 10 31l98 219q10 20 27.5 29.5t48.5 9.5h225q18 0 29.5 -10t11.5 -29q0 -14 -12 -26 l-205 -228q-25 -29 -65 -28h-136q-32 -1 -32 32z" />
<glyph unicode="&#x4f3;" horiz-adv-x="1222" d="M37 1018q4 20 17.5 33.5t31.5 13.5h244q43 0 59 -43l223 -576l236 576q16 43 63 43h232q18 0 31.5 -13.5t13.5 -29.5q0 -14 -6 -27l-389 -940q-66 -156 -125.5 -249t-140.5 -145t-199 -52h-103q-23 0 -38 15.5t-15 37.5v174q0 23 15.5 37t37.5 14h72q53 0 82 30t63 114 l9 18l-408 946q-6 16 -6 23zM305 1251q0 10 10 31l99 219q10 20 27.5 29.5t48.5 9.5h225q18 0 29.5 -10t11.5 -29q0 -14 -12 -26l-205 -228q-25 -29 -68 -28h-133q-33 -1 -33 32zM752 1251q0 10 10 31l98 219q10 20 27.5 29.5t48.5 9.5h225q18 0 29.5 -10t11.5 -29 q0 -14 -12 -26l-205 -228q-25 -29 -65 -28h-136q-32 -1 -32 32z" />
<glyph unicode="&#x4f4;" horiz-adv-x="1439" d="M92 932v450q0 23 15.5 37.5t37.5 14.5h279q23 0 38 -14.5t15 -37.5v-432q0 -100 49 -146t162 -46q82 0 148.5 28.5t66.5 87.5v508q0 23 15.5 37.5t37.5 14.5h279q23 0 38 -14.5t15 -37.5v-1331q0 -23 -15.5 -37t-37.5 -14h-279q-22 0 -37.5 14.5t-15.5 36.5v432 q-45 -39 -125 -58t-170 -19q-262 0 -389 130t-127 396zM315 1655v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM745 1655v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5 t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x4f5;" horiz-adv-x="1249" d="M66 702v312q0 23 15 37t36 14h266q23 0 37 -14.5t14 -36.5v-293q0 -70 33 -98.5t111 -28.5q76 0 119.5 23.5t43.5 68.5v328q0 23 15.5 37t38.5 14h276q23 0 38 -14.5t15 -36.5v-963q0 -23 -15 -37t-38 -14h-276q-23 0 -38.5 14.5t-15.5 36.5v305q-92 -74 -274 -73 q-199 0 -300 101t-101 318zM227 1286v197q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM657 1286v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5 t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5z" />
<glyph unicode="&#x4f6;" horiz-adv-x="1163" d="M139 51v1329q0 23 14.5 38.5t36.5 15.5h893q23 0 37.5 -15.5t14.5 -38.5v-215q0 -23 -14.5 -38t-37.5 -15h-559v-770h94q23 0 38.5 -15.5t15.5 -37.5v-525q0 -23 -15.5 -37t-38.5 -14h-278q-23 0 -38 14.5t-15 36.5v236h-97q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x4f7;" horiz-adv-x="946" d="M117 51v963q0 23 15 37t36 14h692q23 0 37 -14.5t14 -36.5v-176q0 -20 -14 -36t-37 -16h-383v-507h86q23 0 37 -14.5t14 -37.5v-413q0 -23 -14 -38.5t-37 -15.5h-256q-23 0 -37 15.5t-14 38.5v186h-88q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x4f8;" horiz-adv-x="1957" d="M139 51v1331q0 23 15.5 37.5t35.5 14.5h281q23 0 38 -14.5t15 -37.5v-436h187q270 0 415.5 -117.5t145.5 -347.5q0 -221 -147.5 -351t-413.5 -130h-521q-20 0 -35.5 15.5t-15.5 35.5zM524 295h178q82 0 126 51t44 133q0 86 -44 130t-126 44h-178v-358zM618 1655v197 q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM1048 1655v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194q-21 0 -34.5 12.5 t-13.5 32.5zM1423 51v1331q0 23 15.5 37.5t36.5 14.5h292q23 0 37.5 -14.5t14.5 -37.5v-1331q0 -20 -14.5 -35.5t-37.5 -15.5h-292q-21 0 -36.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x4f9;" horiz-adv-x="1708" d="M117 51v963q0 23 15 37t36 14h258q23 0 37 -14.5t14 -36.5v-275h185q205 0 320.5 -99t115.5 -273q0 -178 -118 -272.5t-329 -94.5h-483q-21 0 -36 15.5t-15 35.5zM471 244h160q76 0 110.5 28.5t34.5 94.5t-34.5 98.5t-110.5 32.5h-160v-254zM501 1286v197q0 20 12.5 33.5 t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-197q-20 0 -32.5 12.5t-12.5 32.5zM931 1286v197q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-197q0 -20 -13 -32.5t-34 -12.5h-194q-21 0 -34.5 12.5t-13.5 32.5zM1221 51v963 q0 23 15 37t36 14h256q23 0 38 -14.5t15 -36.5v-963q0 -23 -15.5 -37t-37.5 -14h-256q-21 0 -36 15.5t-15 35.5z" />
<glyph unicode="&#x1e80;" horiz-adv-x="1705" d="M66 1391q0 16 13 29.5t30 13.5h268q59 0 65 -48l125 -774l142 449q16 55 67 55h154q51 0 67 -55l142 -447l127 772q4 25 18 36.5t45 11.5h268q18 0 30.5 -13.5t12.5 -29.5v-13l-219 -1310q-4 -29 -26.5 -48.5t-55.5 -19.5h-206q-33 0 -51.5 16.5t-24.5 36.5l-203 568 l-203 -568q-8 -20 -26.5 -36.5t-51.5 -16.5h-204q-35 0 -56.5 19.5t-25.5 48.5l-219 1310q-2 4 -2 13zM418 1817q0 18 10 29.5t29 11.5h299q27 0 41 -7.5t35 -31.5l184 -224q10 -10 10 -26q0 -33 -33 -33h-211q-26 0 -42.5 7t-36.5 24l-275 225q-10 10 -10 25z" />
<glyph unicode="&#x1e81;" horiz-adv-x="1722" d="M49 1018q0 20 14.5 33.5t32.5 13.5h223q25 0 41.5 -13.5t20.5 -29.5l168 -567l178 563q4 18 20.5 32.5t43.5 14.5h141q27 0 43 -14.5t22 -32.5l179 -563l165 567q6 16 21.5 29.5t40.5 13.5h223q20 0 33.5 -13.5t13.5 -33.5q0 -10 -2 -19l-297 -948q-14 -51 -65 -51h-195 q-51 0 -70 51l-182 545l-184 -545q-14 -51 -68 -51h-194q-29 0 -44.5 13.5t-23.5 37.5l-295 948zM428 1499q0 18 10 29.5t29 11.5h280q27 0 41.5 -7t34.5 -32l184 -223q10 -10 11 -27q0 -33 -33 -32h-195q-24 0 -40.5 7t-36.5 23l-275 226q-10 10 -10 24z" />
<glyph unicode="&#x1e82;" horiz-adv-x="1705" d="M66 1391q0 16 13 29.5t30 13.5h268q59 0 65 -48l125 -774l142 449q16 55 67 55h154q51 0 67 -55l142 -447l127 772q4 25 18 36.5t45 11.5h268q18 0 30.5 -13.5t12.5 -29.5v-13l-219 -1310q-4 -29 -26.5 -48.5t-55.5 -19.5h-206q-33 0 -51.5 16.5t-24.5 36.5l-203 568 l-203 -568q-8 -20 -26.5 -36.5t-51.5 -16.5h-204q-35 0 -56.5 19.5t-25.5 48.5l-219 1310q-2 4 -2 13zM686 1569q0 16 11 26l182 224q20 25 35.5 32t40.5 7h299q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-274 -225q-20 -16 -36.5 -23.5t-41.5 -7.5h-211q-33 0 -33 33z" />
<glyph unicode="&#x1e83;" horiz-adv-x="1722" d="M49 1018q0 20 14.5 33.5t32.5 13.5h223q25 0 41.5 -13.5t20.5 -29.5l168 -567l178 563q4 18 20.5 32.5t43.5 14.5h141q27 0 43 -14.5t22 -32.5l179 -563l165 567q6 16 21.5 29.5t40.5 13.5h223q20 0 33.5 -13.5t13.5 -33.5q0 -10 -2 -19l-297 -948q-14 -51 -65 -51h-195 q-51 0 -70 51l-182 545l-184 -545q-14 -51 -68 -51h-194q-29 0 -44.5 13.5t-23.5 37.5l-295 948zM715 1251q0 16 10 27l182 223q20 25 35.5 32t40.5 7h283q18 0 28.5 -11t10.5 -30q0 -14 -11 -24l-274 -226q-20 -16 -36.5 -23t-43.5 -7h-192q-33 -1 -33 32z" />
<glyph unicode="&#x1e84;" horiz-adv-x="1705" d="M66 1391q0 16 13 29.5t30 13.5h268q59 0 65 -48l125 -774l142 449q16 55 67 55h154q51 0 67 -55l142 -447l127 772q4 25 18 36.5t45 11.5h268q18 0 30.5 -13.5t12.5 -29.5v-13l-219 -1310q-4 -29 -26.5 -48.5t-55.5 -19.5h-206q-33 0 -51.5 16.5t-24.5 36.5l-203 568 l-203 -568q-8 -20 -26.5 -36.5t-51.5 -16.5h-204q-35 0 -56.5 19.5t-25.5 48.5l-219 1310q-2 4 -2 13zM463 1583v195q0 20 12.5 33.5t32.5 13.5h197q20 0 33.5 -13.5t13.5 -33.5v-195q0 -20 -13.5 -33.5t-33.5 -13.5h-197q-20 0 -32.5 13.5t-12.5 33.5zM954 1583v195 q0 20 13.5 33.5t34.5 13.5h194q20 0 33.5 -13.5t13.5 -33.5v-195q0 -20 -13 -33.5t-34 -13.5h-194q-21 0 -34.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x1e85;" horiz-adv-x="1722" d="M49 1018q0 20 14.5 33.5t32.5 13.5h223q25 0 41.5 -13.5t20.5 -29.5l168 -567l178 563q4 18 20.5 32.5t43.5 14.5h141q27 0 43 -14.5t22 -32.5l179 -563l165 567q6 16 21.5 29.5t40.5 13.5h223q20 0 33.5 -13.5t13.5 -33.5q0 -10 -2 -19l-297 -948q-14 -51 -65 -51h-195 q-51 0 -70 51l-182 545l-184 -545q-14 -51 -68 -51h-194q-29 0 -44.5 13.5t-23.5 37.5l-295 948zM504 1286v197q0 20 12 33.5t33 13.5h196q20 0 34 -13.5t14 -33.5v-197q0 -20 -13.5 -32.5t-34.5 -12.5h-196q-21 0 -33 12.5t-12 32.5zM934 1286v197q0 20 13 33.5t34 13.5 h194q20 0 34 -13.5t14 -33.5v-197q0 -20 -13.5 -32.5t-34.5 -12.5h-194q-21 0 -34 12.5t-13 32.5z" />
<glyph unicode="&#x1ef2;" horiz-adv-x="1400" d="M29 1391q0 16 13 29.5t30 13.5h270q29 0 47.5 -14.5t26.5 -30.5l284 -515l285 515q8 16 26.5 30.5t47.5 14.5h270q16 0 29.5 -13.5t13.5 -29.5q0 -12 -6 -23l-473 -881v-436q0 -23 -15.5 -37t-37.5 -14h-281q-20 0 -35.5 15.5t-15.5 35.5v436l-473 881q-6 10 -6 23z M264 1817q0 18 10 29.5t29 11.5h299q27 0 41 -7.5t35 -31.5l184 -224q10 -10 10 -26q0 -33 -33 -33h-211q-26 0 -42.5 7t-36.5 24l-275 225q-10 10 -10 25z" />
<glyph unicode="&#x1ef3;" horiz-adv-x="1230" d="M41 1018q4 20 17.5 33.5t31.5 13.5h244q43 0 59 -43l228 -582l233 582q20 43 61 43h242q18 0 31.5 -13.5t13.5 -29.5q0 -14 -4 -27l-580 -1341q-20 -43 -63 -43h-238q-18 0 -30.5 12t-12.5 31q0 14 5 27l161 385l-393 929q-6 16 -6 23zM191 1499q0 18 10 29.5t29 11.5 h280q27 0 41.5 -7t34.5 -32l184 -223q10 -10 11 -27q0 -33 -33 -32h-195q-24 0 -40.5 7t-36.5 23l-275 226q-10 10 -10 24z" />
<glyph unicode="&#x2000;" horiz-adv-x="954" />
<glyph unicode="&#x2001;" horiz-adv-x="1909" />
<glyph unicode="&#x2002;" horiz-adv-x="954" />
<glyph unicode="&#x2003;" horiz-adv-x="1909" />
<glyph unicode="&#x2004;" horiz-adv-x="636" />
<glyph unicode="&#x2005;" horiz-adv-x="477" />
<glyph unicode="&#x2006;" horiz-adv-x="318" />
<glyph unicode="&#x2007;" horiz-adv-x="318" />
<glyph unicode="&#x2008;" horiz-adv-x="238" />
<glyph unicode="&#x2009;" horiz-adv-x="381" />
<glyph unicode="&#x200a;" horiz-adv-x="106" />
<glyph unicode="&#x2010;" horiz-adv-x="974" d="M113 512v205q0 23 15 37t38 14h645q23 0 37 -14.5t14 -36.5v-205q0 -20 -14 -35.5t-37 -15.5h-645q-23 0 -38 14t-15 37z" />
<glyph unicode="&#x2011;" horiz-adv-x="974" d="M113 512v205q0 23 15 37t38 14h645q23 0 37 -14.5t14 -36.5v-205q0 -20 -14 -35.5t-37 -15.5h-645q-23 0 -38 14t-15 37z" />
<glyph unicode="&#x2012;" horiz-adv-x="974" d="M113 512v205q0 23 15 37t38 14h645q23 0 37 -14.5t14 -36.5v-205q0 -20 -14 -35.5t-37 -15.5h-645q-23 0 -38 14t-15 37z" />
<glyph unicode="&#x2013;" horiz-adv-x="1159" d="M113 512v205q0 23 15 37t38 14h829q23 0 37.5 -14.5t14.5 -36.5v-205q0 -20 -14.5 -35.5t-37.5 -15.5h-829q-23 0 -38 14t-15 37z" />
<glyph unicode="&#x2014;" horiz-adv-x="1548" d="M113 512v205q0 23 15 37t38 14h1218q23 0 37.5 -14.5t14.5 -36.5v-205q0 -20 -14.5 -35.5t-37.5 -15.5h-1218q-23 0 -38 14t-15 37z" />
<glyph unicode="&#x2018;" horiz-adv-x="559" d="M61 967q0 12 5 22l157 408q12 27 29.5 42t48.5 15h150q18 0 29.5 -12t8.5 -35l-63 -410q-6 -31 -24.5 -51t-51.5 -20h-250q-16 0 -27.5 12t-11.5 29z" />
<glyph unicode="&#x2019;" horiz-adv-x="548" d="M82 973l63 409q6 31 24.5 51.5t51.5 20.5h250q16 0 27.5 -12t11.5 -29q0 -12 -4 -22l-158 -408q-12 -27 -29.5 -42t-48.5 -15h-149q-19 0 -30 12t-9 35z" />
<glyph unicode="&#x201a;" horiz-adv-x="573" d="M63 -106l64 409q6 31 24.5 51.5t51.5 20.5h250q16 0 27.5 -12.5t11.5 -28.5q0 -12 -5 -23l-157 -407q-12 -27 -29.5 -42.5t-48.5 -15.5h-150q-18 0 -29.5 12.5t-9.5 35.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="1021" d="M61 967q0 12 5 22l157 408q12 27 29.5 42t48.5 15h150q18 0 29.5 -14.5t8.5 -32.5l-63 -410q-6 -31 -24.5 -51t-51.5 -20h-250q-16 0 -27.5 12t-11.5 29zM524 967q0 12 4 22l158 408q12 27 29.5 42t48.5 15h149q18 0 29.5 -14.5t9.5 -32.5l-63 -410q-6 -31 -24.5 -51 t-51.5 -20h-250q-16 0 -27.5 12t-11.5 29z" />
<glyph unicode="&#x201d;" horiz-adv-x="1011" d="M82 973l63 409q6 31 24.5 51.5t51.5 20.5h250q16 0 27.5 -12t11.5 -29q0 -12 -4 -22l-158 -408q-12 -27 -29.5 -42t-48.5 -15h-149q-19 0 -30 14t-9 33zM545 973l63 409q6 31 24.5 51.5t51.5 20.5h250q16 0 27.5 -12t11.5 -29q0 -12 -4 -22l-158 -408q-12 -27 -29.5 -42 t-48.5 -15h-149q-19 0 -30 14t-9 33z" />
<glyph unicode="&#x201e;" horiz-adv-x="1036" d="M63 -106l64 409q6 31 24.5 51.5t51.5 20.5h250q16 0 27.5 -12.5t11.5 -28.5q0 -12 -5 -23l-157 -407q-12 -27 -29.5 -42.5t-48.5 -15.5h-150q-18 0 -29.5 14.5t-9.5 33.5zM526 -109l64 412q6 31 24.5 51.5t51.5 20.5h249q16 0 27.5 -12.5t11.5 -28.5q0 -12 -4 -23 l-157 -407q-12 -27 -30 -42.5t-48 -15.5h-150q-16 0 -28.5 13.5t-10.5 31.5z" />
<glyph unicode="&#x2020;" horiz-adv-x="985" d="M59 831v191q0 23 15.5 37t36.5 14h233v309q0 23 15.5 37.5t35.5 14.5h195q23 0 37 -14.5t14 -37.5v-309h233q23 0 37.5 -14.5t14.5 -36.5v-191q0 -20 -14.5 -35.5t-37.5 -15.5h-233v-729q0 -20 -14.5 -35.5t-36.5 -15.5h-195q-20 0 -35.5 15.5t-15.5 35.5v729h-233 q-20 0 -36 15.5t-16 35.5z" />
<glyph unicode="&#x2021;" horiz-adv-x="1064" d="M98 354v191q0 23 15.5 37t36.5 14h233v266h-233q-20 0 -36 15.5t-16 35.5v191q0 23 15.5 37t36.5 14h233v227q0 23 15.5 37.5t35.5 14.5h195q23 0 38 -14.5t15 -37.5v-227h231q23 0 38.5 -14.5t15.5 -36.5v-191q0 -23 -15.5 -37t-38.5 -14h-231v-266h231q23 0 38.5 -14.5 t15.5 -36.5v-191q0 -23 -15.5 -37t-38.5 -14h-231v-252q0 -23 -15.5 -37t-37.5 -14h-195q-20 0 -35.5 15.5t-15.5 35.5v252h-233q-20 0 -36 15.5t-16 35.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="788" d="M135 682q0 70 35 130t95.5 95t129.5 35q70 0 129.5 -35t94 -95t34.5 -130t-34.5 -129t-94 -94t-129.5 -35q-69 0 -129.5 35t-95.5 94t-35 129z" />
<glyph unicode="&#x2026;" horiz-adv-x="1540" d="M106 51v244q0 23 15.5 38t36.5 15h243q20 0 36 -15.5t16 -37.5v-244q0 -20 -14.5 -35.5t-37.5 -15.5h-243q-21 0 -36.5 15.5t-15.5 35.5zM596 51v244q0 23 15.5 38t37.5 15h242q23 0 38 -15.5t15 -37.5v-244q0 -23 -15.5 -37t-37.5 -14h-242q-22 0 -37.5 14.5t-15.5 36.5 zM1087 51v244q0 23 15.5 38t36.5 15h243q23 0 38.5 -15.5t15.5 -37.5v-244q0 -20 -15.5 -35.5t-38.5 -15.5h-243q-21 0 -36.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="381" />
<glyph unicode="&#x2030;" horiz-adv-x="2375" d="M72 1063q0 27 4 108q6 125 88 199t227 74t227 -74t91 -199q4 -82 4 -108q0 -25 -4 -86q-8 -123 -93.5 -194.5t-224.5 -71.5t-223 71.5t-92 194.5q-4 61 -4 86zM182 41q0 12 8 22l1012 1332q16 20 30.5 29.5t41.5 9.5h153q20 0 34 -11.5t14 -29.5q0 -12 -9 -23 l-1011 -1331q-16 -20 -30.5 -29.5t-41.5 -9.5h-154q-20 0 -33.5 11.5t-13.5 29.5zM299 1075q0 -55 2 -86q4 -43 25.5 -69.5t64.5 -26.5q45 0 66.5 27.5t23.5 68.5q4 61 4 86q0 27 -4 84q-2 43 -23.5 70.5t-66.5 27.5q-82 0 -90 -98q-2 -29 -2 -84zM948 346q0 25 4 107 q8 127 90 200.5t228 73.5q145 0 227 -73.5t88 -200.5q4 -82 4 -107t-4 -86q-8 -123 -92 -196.5t-223 -73.5q-140 0 -224.5 73.5t-93.5 196.5q-4 61 -4 86zM1178 358q0 -55 2 -86q2 -43 23.5 -70.5t66.5 -27.5q43 0 64.5 27.5t25.5 70.5q4 61 4 86q0 27 -4 84q-4 43 -25.5 70 t-64.5 27q-45 0 -66.5 -28t-23.5 -69q-2 -29 -2 -84zM1671 346q0 25 4 107q6 127 88 200.5t228 73.5q145 0 227 -73.5t90 -200.5q4 -82 4 -107t-4 -86q-8 -123 -93 -196.5t-224 -73.5q-140 0 -224 73.5t-92 196.5q-4 61 -4 86zM1898 358q0 -55 3 -86q4 -43 25.5 -70.5 t64.5 -27.5q45 0 66.5 27.5t23.5 70.5q4 61 4 86q0 27 -4 84q-2 41 -23.5 69t-66.5 28q-43 0 -64.5 -27t-25.5 -70q-2 -29 -3 -84z" />
<glyph unicode="&#x2039;" horiz-adv-x="708" d="M61 623v102q0 35 31 61l424 412q18 16 35 16q18 0 31.5 -13t13.5 -32v-202q0 -31 -8 -48.5t-29 -37.5l-211 -207l211 -207q20 -20 28.5 -37.5t8.5 -48.5v-203q0 -18 -13.5 -31.5t-31.5 -13.5q-17 1 -35 17l-424 411q-31 29 -31 62z" />
<glyph unicode="&#x203a;" horiz-adv-x="708" d="M113 178v203q0 31 8 48t29 38l210 207l-210 207q-20 20 -28.5 37.5t-8.5 48.5v202q0 18 13 31.5t32 13.5q16 0 35 -16l423 -412q31 -27 31 -61v-102q0 -33 -31 -62l-423 -411q-18 -16 -35 -17q-19 0 -32 13.5t-13 31.5z" />
<glyph unicode="&#x2044;" horiz-adv-x="327" d="M-461 41q0 12 8 22l1010 1332q14 20 29.5 29.5t42.5 9.5h112q20 0 33.5 -11.5t13.5 -29.5q0 -12 -8 -23l-1011 -1331q-16 -20 -30.5 -29.5t-41.5 -9.5h-113q-20 0 -32.5 11.5t-12.5 29.5z" />
<glyph unicode="&#x205f;" horiz-adv-x="477" />
<glyph unicode="&#x20aa;" horiz-adv-x="2015" d="M123 51v1067q0 23 15.5 37t35.5 14h690q289 0 428.5 -119.5t139.5 -394.5v-206q0 -23 -14.5 -38.5t-37.5 -15.5h-243q-20 0 -36 15.5t-16 35.5v211q0 233 -231 234h-385v-840q0 -20 -14.5 -35.5t-36.5 -15.5h-244q-20 0 -35.5 15.5t-15.5 35.5zM596 51v672q0 23 15.5 37 t35.5 14h242q23 0 38 -14.5t15 -36.5v-444h385q231 0 232 233v606q0 23 14 37t37 14h241q23 0 38.5 -14t15.5 -37v-604q0 -274 -139.5 -394t-428.5 -120h-690q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x20ac;" horiz-adv-x="1615" d="M66 633v170q0 23 15 37t38 14h125v12q-2 289 166 438.5t471 149.5q186 0 331.5 -59.5t228.5 -166t85 -243.5q0 -18 -13.5 -31.5t-31.5 -13.5h-293q-29 0 -44.5 12.5t-25.5 42.5q-27 80 -86 116t-151 36q-121 0 -181.5 -68.5t-56.5 -224.5h279q23 0 37 -14.5t14 -36.5 v-170q0 -20 -14.5 -35.5t-36.5 -15.5h-279q-4 -156 56.5 -225.5t181.5 -69.5q92 0 151.5 35.5t85.5 115.5q10 31 25.5 43.5t44.5 12.5h293q18 0 31.5 -13.5t13.5 -31.5q-2 -137 -85 -244t-228.5 -166t-331.5 -59q-303 0 -473 149.5t-164 439.5v13h-125q-23 0 -38 14t-15 37z " />
<glyph unicode="&#x20ae;" horiz-adv-x="1300" d="M41 1151v229q0 23 15.5 38.5t37.5 15.5h1112q23 0 38.5 -15.5t15.5 -38.5v-229q0 -23 -15.5 -37t-38.5 -14h-360v-209l215 24q20 2 35.5 -13t15.5 -38v-102q0 -20 -14.5 -34.5t-36.5 -16.5l-215 -25v-106l215 24q20 2 35.5 -14.5t15.5 -38.5v-102q0 -20 -14.5 -34 t-36.5 -18l-215 -24v-322q0 -23 -15.5 -37t-37.5 -14h-285q-23 0 -38 14.5t-15 36.5v275l-205 -25q-20 -2 -35.5 14.5t-15.5 38.5v103q0 20 14 33.5t37 17.5l205 24v107l-205 -25q-20 -2 -35.5 13.5t-15.5 38.5v102q0 23 14 36t37 17l205 25v254h-361q-23 0 -38 14t-15 37z " />
<glyph unicode="&#x20b4;" horiz-adv-x="1398" d="M84 700v84q0 23 15.5 37.5t37.5 14.5h541q123 47 170 89t47 101t-38 92t-107 33q-86 0 -141.5 -28.5t-76.5 -78.5q-25 -37 -69 -36h-287q-18 0 -30.5 13t-12.5 32q0 90 74 183t214 155.5t329 62.5q147 0 257.5 -49t169 -136t58.5 -198q0 -92 -29.5 -156.5t-91.5 -111.5 h152q22 0 36.5 -15.5t14.5 -37.5v-82q0 -23 -14.5 -38.5t-36.5 -15.5h-580q-115 -47 -158 -86t-43 -100q0 -68 51.5 -104.5t139.5 -36.5q162 0 225 112q18 20 33.5 28.5t40.5 8.5h272q18 0 31.5 -12t13.5 -31q-2 -111 -71.5 -205t-205.5 -151t-327 -57q-156 0 -278.5 53 t-193.5 150.5t-71 222.5q0 80 27 139t82 104h-117q-23 0 -38 14.5t-15 36.5z" />
<glyph unicode="&#x20b8;" horiz-adv-x="1300" d="M41 887v162q0 23 15.5 38t37.5 15h1112q23 0 38.5 -15.5t15.5 -37.5v-162q0 -23 -15.5 -37t-38.5 -14h-360v-785q0 -23 -15.5 -37t-37.5 -14h-285q-23 0 -38 14.5t-15 36.5v785h-361q-23 0 -38 14t-15 37zM41 1276v104q0 23 15.5 37.5t37.5 14.5h1112q23 0 38.5 -14.5 t15.5 -37.5v-104q0 -23 -15.5 -37t-38.5 -14h-1112q-22 0 -37.5 14t-15.5 37z" />
<glyph unicode="&#x20bd;" horiz-adv-x="1486" d="M31 285v82q0 23 15 37t36 14h151v102h-151q-20 0 -35.5 15.5t-15.5 35.5v164q0 23 15 37t36 14h151v594q0 23 15.5 38.5t38.5 15.5h579q264 0 416 -121t152 -355q0 -219 -150.5 -328.5t-417.5 -109.5h-233v-102h262q23 0 37 -14.5t14 -36.5v-82q0 -23 -14 -38.5 t-37 -15.5h-262v-180q0 -23 -15.5 -37t-37.5 -14h-293q-23 0 -38.5 14.5t-15.5 36.5v180h-151q-23 0 -37 15.5t-14 38.5zM625 786h231q84 0 132 44t48 131q0 80 -45 129t-135 49h-231v-353z" />
<glyph unicode="&#x2122;" horiz-adv-x="1327" d="M80 1309v80q0 20 12 32.5t33 12.5h387q20 0 32.5 -12.5t12.5 -32.5v-80q0 -20 -12.5 -32.5t-32.5 -12.5h-106v-330q0 -20 -12.5 -32.5t-33.5 -12.5h-84q-20 0 -32.5 12t-12.5 33v330h-106q-20 0 -32.5 12t-12.5 33zM616 934v455q0 20 12.5 32.5t33.5 12.5h79 q20 0 32.5 -11.5t29.5 -33.5l106 -172l107 172q12 20 25.5 32.5t33.5 12.5h80q20 0 32.5 -12.5t12.5 -32.5v-455q0 -20 -12 -32.5t-33 -12.5h-84q-20 0 -32.5 12t-12.5 33v196l-43 -69q-14 -23 -24.5 -32t-28.5 -9h-43q-18 0 -28.5 9t-24.5 32l-43 69v-196 q0 -20 -12.5 -32.5t-33.5 -12.5h-83q-21 0 -33.5 12t-12.5 33z" />
<glyph unicode="&#x2202;" horiz-adv-x="1245" d="M70 471q0 154 68.5 265.5t192.5 169t283 57.5q61 0 111 -27q-45 119 -136 218t-222 196q-29 18 -29 41q0 18 12 30.5t31 12.5h270q57 0 97 -27q193 -147 297 -340t116 -502v-100q-10 -231 -152.5 -358t-394.5 -127q-254 0 -399 129t-145 362zM444 477l2 -41 q0 -92 41 -145t127 -53q84 0 126 53t44 143q2 16 2 41q0 106 -38.5 168t-133.5 62q-162 0 -168 -189z" />
<glyph unicode="&#x2206;" horiz-adv-x="1433" d="M53 55v72q0 27 5 40t8 17l446 1100q29 68 92 68h225q63 0 93 -68l446 -1100q2 -4 7 -17t5 -40v-72q0 -23 -16 -39t-39 -16h-1216q-23 0 -39.5 16.5t-16.5 38.5zM428 276h580l-291 764z" />
<glyph unicode="&#x220f;" horiz-adv-x="1368" d="M133 -358v1740q0 23 15.5 37.5t35.5 14.5h998q23 0 38 -14.5t15 -37.5v-1740q0 -23 -15.5 -37.5t-37.5 -14.5h-230q-20 0 -35.5 14.5t-15.5 37.5v1435h-434v-1435q0 -23 -15.5 -37.5t-37.5 -14.5h-230q-22 0 -36.5 14.5t-14.5 37.5z" />
<glyph unicode="&#x2211;" horiz-adv-x="1273" d="M84 -135q0 29 12.5 47t38.5 41l613 553v14l-613 551q-27 23 -39 41t-12 47v223q0 23 15.5 37.5t35.5 14.5h977q23 0 37 -14.5t14 -37.5v-254q0 -20 -14 -35.5t-37 -15.5h-502l498 -450q29 -23 42 -40.5t13 -41.5v-66q0 -25 -13 -42t-42 -40l-498 -450h502q23 0 37 -15.5 t14 -35.5v-254q0 -23 -14 -37.5t-37 -14.5h-977q-20 0 -35.5 14.5t-15.5 37.5v223z" />
<glyph unicode="&#x221a;" horiz-adv-x="1525" d="M35 1018q0 20 13 33.5t32 13.5h190q45 0 60 -43l209 -612l374 1302q18 49 62 49h467q23 0 37 -14t14 -37v-156q0 -20 -14.5 -35.5t-36.5 -15.5h-283l-446 -1452q-8 -25 -21.5 -38t-40.5 -13h-225q-45 0 -59 45l-330 956q-2 6 -2 17z" />
<glyph unicode="&#x221e;" horiz-adv-x="1824" d="M94 555q0 117 47 220.5t143.5 169t235.5 65.5q123 0 212 -64.5t181 -183.5q90 117 179.5 182.5t210.5 65.5q141 0 237 -65.5t143.5 -169t47.5 -220.5t-47.5 -220.5t-143.5 -169t-235 -65.5q-123 0 -213.5 63.5t-180.5 178.5q-88 -115 -178 -178.5t-213 -63.5 q-139 0 -235.5 65.5t-143.5 169t-47 220.5zM356 555q0 -92 50.5 -146.5t119.5 -54.5q72 0 135.5 65.5t96.5 131.5q-37 70 -99.5 137.5t-132.5 67.5t-120 -54.5t-50 -146.5zM1065 551q35 -66 98.5 -131.5t134.5 -65.5q68 0 118 54.5t50 146.5t-50 146.5t-118 54.5 q-72 0 -136 -69t-97 -136z" />
<glyph unicode="&#x222b;" horiz-adv-x="913" d="M37 -248q0 20 15.5 35.5t35.5 15.5h80q147 0 147 162v1100q0 180 110 287.5t296 107.5h104q23 0 38.5 -14t15.5 -37v-162q0 -23 -15.5 -37t-38.5 -14h-77q-147 0 -148 -162v-1095q0 -182 -109.5 -291t-295.5 -109h-107q-20 0 -35.5 14.5t-15.5 36.5v162z" />
<glyph unicode="&#x2248;" horiz-adv-x="1136" d="M123 240v161q0 59 64.5 103.5t160.5 44.5q66 0 118 -11.5t116 -31.5q72 -20 112.5 -29.5t98.5 -9.5q43 0 74.5 14.5t50 27.5t22.5 15q18 14 35 15q25 0 33 -14.5t8 -47.5v-164q0 -59 -64.5 -102t-160.5 -43q-63 0 -111.5 10t-118.5 31q-59 18 -107 28.5t-108 10.5 q-72 0 -145 -55q-16 -14 -37 -17q-23 0 -32 15.5t-9 48.5zM123 752v161q0 59 64.5 103.5t160.5 44.5q66 0 118 -11.5t116 -31.5q72 -20 112.5 -29.5t98.5 -9.5q47 0 77.5 15.5t69.5 41.5q14 14 35 15q25 0 33 -14.5t8 -47.5v-164q0 -59 -64.5 -102t-160.5 -43 q-63 0 -111.5 10t-118.5 31q-59 18 -107 28.5t-108 10.5q-72 0 -145 -55q-18 -14 -37 -17q-23 0 -32 15.5t-9 48.5z" />
<glyph unicode="&#x2260;" horiz-adv-x="1124" d="M109 274v168q0 23 15 37.5t36 14.5h223l100 241h-323q-20 0 -35.5 15.5t-15.5 35.5v168q0 23 15 37.5t36 14.5h436l55 131q8 20 26.5 35.5t47.5 15.5h141q16 0 29.5 -13.5t13.5 -29.5q0 -10 -4 -21l-51 -118h115q23 0 38 -14.5t15 -37.5v-168q0 -23 -15.5 -37t-37.5 -14 h-228l-100 -241h328q23 0 38 -14.5t15 -37.5v-168q0 -23 -15.5 -37t-37.5 -14h-443l-53 -129q-20 -51 -76 -51h-135q-18 0 -30.5 13.5t-12.5 29.5q0 10 4 20l47 117h-110q-20 0 -35.5 15.5t-15.5 35.5z" />
<glyph unicode="&#x2264;" horiz-adv-x="1118" d="M104 51v160q0 23 15.5 37t36.5 14h780q23 0 37 -14.5t14 -36.5v-160q0 -20 -14.5 -35.5t-36.5 -15.5h-780q-21 0 -36.5 15.5t-15.5 35.5zM104 838v71q0 31 14.5 50.5t45.5 35.5l747 344q20 10 33 11q18 0 30.5 -11.5t12.5 -29.5v-176q0 -61 -57 -82l-436 -177l436 -178 q57 -25 57 -80v-176q0 -20 -12 -31.5t-31 -11.5q-13 1 -33 11l-747 346q-31 16 -45.5 34.5t-14.5 49.5z" />
<glyph unicode="&#x2265;" horiz-adv-x="1118" d="M131 51v160q0 23 15.5 37t37.5 14h779q23 0 37 -14.5t14 -36.5v-160q0 -20 -14.5 -35.5t-36.5 -15.5h-779q-22 0 -37.5 14.5t-15.5 36.5zM131 440v176q0 55 59 80l435 178l-435 177q-25 8 -42 28.5t-17 53.5v176q0 18 12.5 29.5t30.5 11.5q12 0 33 -11l747 -344 q33 -14 46.5 -34.5t13.5 -51.5v-71q0 -31 -13.5 -49.5t-46.5 -34.5l-747 -346q-20 -10 -33 -11q-18 0 -30.5 11.5t-12.5 31.5z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1064" d="M0 0v1065h1065v-1065h-1065z" />
<glyph unicode="&#xfb03;" horiz-adv-x="2164" d="M33 838v176q0 23 15 37t38 14h154v61q0 205 118.5 297.5t339.5 92.5h162q23 0 38 -14.5t15 -37.5v-176q0 -23 -15 -37t-38 -14h-145q-66 0 -93.5 -29.5t-27.5 -91.5v-51h401v61q0 205 118 297.5t341 92.5h543q23 0 37 -14.5t14 -37.5v-176q0 -20 -14.5 -35.5t-36.5 -15.5 h-527q-66 0 -93 -29.5t-27 -91.5v-51h647q23 0 37 -14.5t14 -36.5v-963q0 -20 -14.5 -35.5t-36.5 -15.5h-252q-20 0 -35.5 15.5t-15.5 35.5v735h-344v-735q0 -20 -14.5 -35.5t-37.5 -15.5h-251q-20 0 -36 15.5t-16 35.5v735h-401v-735q0 -20 -14.5 -35.5t-36.5 -15.5h-250 q-23 0 -38 14.5t-15 36.5v735h-154q-23 0 -38 14.5t-15 37.5z" />
<glyph unicode="&#xfb04;" horiz-adv-x="2222" d="M33 838v176q0 23 15 37t38 14h154v61q0 205 118.5 297.5t339.5 92.5h162q23 0 38 -14.5t15 -37.5v-176q0 -23 -15 -37t-38 -14h-145q-66 0 -93.5 -29.5t-27.5 -91.5v-51h401v61q0 205 118 297.5t341 92.5h135q23 0 37 -14.5t14 -37.5v-176q0 -20 -14 -35.5t-37 -15.5 h-119q-66 0 -93 -29.5t-27 -91.5v-51h401v399q0 23 15.5 37.5t35.5 14.5h250q23 0 38 -14.5t15 -37.5v-1413q0 -23 -15 -37t-38 -14h-250q-20 0 -35.5 15.5t-15.5 35.5v735h-401v-735q0 -20 -14.5 -35.5t-37.5 -15.5h-251q-20 0 -36 15.5t-16 35.5v735h-401v-735 q0 -20 -14.5 -35.5t-36.5 -15.5h-250q-23 0 -38 14.5t-15 36.5v735h-154q-23 0 -38 14.5t-15 37.5z" />
<hkern u1="&#x20;" u2="&#x135;" k="12" />
<hkern u1="&#x20;" u2="v" k="51" />
<hkern u1="&#x20;" u2="V" k="51" />
<hkern u1="&#x22;" u2="&#x12d;" k="-12" />
<hkern u1="&#x22;" u2="&#x12b;" k="-43" />
<hkern u1="&#x22;" u2="&#x129;" k="-59" />
<hkern u1="&#x22;" u2="&#xef;" k="-68" />
<hkern u1="&#x22;" u2="&#xee;" k="-74" />
<hkern u1="&#x22;" u2="&#xec;" k="-68" />
<hkern u1="&#x23;" u2="&#x37;" k="12" />
<hkern u1="&#x26;" u2="&#x166;" k="66" />
<hkern u1="&#x26;" u2="x" k="-6" />
<hkern u1="&#x26;" u2="v" k="27" />
<hkern u1="&#x26;" u2="X" k="-16" />
<hkern u1="&#x26;" u2="V" k="66" />
<hkern u1="&#x27;" u2="&#x12d;" k="-12" />
<hkern u1="&#x27;" u2="&#x12b;" k="-43" />
<hkern u1="&#x27;" u2="&#x129;" k="-59" />
<hkern u1="&#x27;" u2="&#xef;" k="-68" />
<hkern u1="&#x27;" u2="&#xee;" k="-74" />
<hkern u1="&#x27;" u2="&#xec;" k="-68" />
<hkern u1="&#x28;" u2="&#x12d;" k="-33" />
<hkern u1="&#x28;" u2="&#x12b;" k="-51" />
<hkern u1="&#x28;" u2="&#x12a;" k="-61" />
<hkern u1="&#x28;" u2="&#x129;" k="-76" />
<hkern u1="&#x28;" u2="&#x128;" k="-55" />
<hkern u1="&#x28;" u2="&#x127;" k="-12" />
<hkern u1="&#x28;" u2="&#xf0;" k="78" />
<hkern u1="&#x28;" u2="&#xef;" k="-76" />
<hkern u1="&#x28;" u2="&#xee;" k="-90" />
<hkern u1="&#x28;" u2="&#xec;" k="-160" />
<hkern u1="&#x28;" u2="&#xcf;" k="-80" />
<hkern u1="&#x28;" u2="&#xce;" k="-88" />
<hkern u1="&#x28;" u2="x" k="23" />
<hkern u1="&#x28;" u2="v" k="88" />
<hkern u1="&#x28;" u2="j" k="-117" />
<hkern u1="&#x28;" u2="&#x7b;" k="53" />
<hkern u1="&#x28;" u2="&#x39;" k="16" />
<hkern u1="&#x28;" u2="&#x38;" k="41" />
<hkern u1="&#x28;" u2="&#x36;" k="86" />
<hkern u1="&#x28;" u2="&#x35;" k="27" />
<hkern u1="&#x28;" u2="&#x34;" k="80" />
<hkern u1="&#x28;" u2="&#x32;" k="14" />
<hkern u1="&#x28;" u2="&#x31;" k="66" />
<hkern u1="&#x28;" u2="&#x30;" k="47" />
<hkern u1="&#x28;" u2="&#x28;" k="23" />
<hkern u1="&#x29;" u2="&#x7d;" k="20" />
<hkern u1="&#x29;" u2="]" k="18" />
<hkern u1="&#x29;" u2="&#x29;" k="23" />
<hkern u1="&#x2a;" u2="&#x135;" k="-10" />
<hkern u1="&#x2a;" u2="&#x12b;" k="-39" />
<hkern u1="&#x2a;" u2="&#x129;" k="-59" />
<hkern u1="&#x2a;" u2="&#x127;" k="-33" />
<hkern u1="&#x2a;" u2="&#xf0;" k="66" />
<hkern u1="&#x2a;" u2="&#xef;" k="-57" />
<hkern u1="&#x2a;" u2="&#xee;" k="-88" />
<hkern u1="&#x2b;" u2="&#x37;" k="96" />
<hkern u1="&#x2b;" u2="&#x33;" k="10" />
<hkern u1="&#x2b;" u2="&#x32;" k="37" />
<hkern u1="&#x2b;" u2="&#x31;" k="43" />
<hkern u1="&#x2c;" u2="&#x166;" k="117" />
<hkern u1="&#x2c;" u2="&#x135;" k="16" />
<hkern u1="&#x2d;" u2="&#x166;" k="90" />
<hkern u1="&#x2d;" u2="&#x135;" k="14" />
<hkern u1="&#x2e;" u2="&#x135;" k="16" />
<hkern u1="&#x2f;" u2="&#x135;" k="61" />
<hkern u1="&#x2f;" u2="&#x12d;" k="-35" />
<hkern u1="&#x2f;" u2="&#x12b;" k="-39" />
<hkern u1="&#x2f;" u2="&#x12a;" k="-72" />
<hkern u1="&#x2f;" u2="&#x129;" k="-18" />
<hkern u1="&#x2f;" u2="&#x128;" k="-63" />
<hkern u1="&#x2f;" u2="&#xf0;" k="106" />
<hkern u1="&#x2f;" u2="&#xef;" k="-76" />
<hkern u1="&#x2f;" u2="&#xee;" k="-14" />
<hkern u1="&#x2f;" u2="&#xec;" k="-154" />
<hkern u1="&#x2f;" u2="&#xe8;" k="104" />
<hkern u1="&#x2f;" u2="&#xe0;" k="96" />
<hkern u1="&#x2f;" u2="&#xcf;" k="-92" />
<hkern u1="&#x2f;" u2="&#xce;" k="-94" />
<hkern u1="&#x2f;" u2="x" k="72" />
<hkern u1="&#x2f;" u2="v" k="68" />
<hkern u1="&#x2f;" u2="j" k="18" />
<hkern u1="&#x2f;" u2="&#x39;" k="27" />
<hkern u1="&#x2f;" u2="&#x38;" k="53" />
<hkern u1="&#x2f;" u2="&#x36;" k="115" />
<hkern u1="&#x2f;" u2="&#x35;" k="35" />
<hkern u1="&#x2f;" u2="&#x34;" k="125" />
<hkern u1="&#x2f;" u2="&#x32;" k="47" />
<hkern u1="&#x2f;" u2="&#x31;" k="31" />
<hkern u1="&#x2f;" u2="&#x30;" k="55" />
<hkern u1="&#x2f;" u2="&#x2f;" k="475" />
<hkern u1="&#x30;" u2="X" k="18" />
<hkern u1="&#x30;" u2="V" k="35" />
<hkern u1="&#x30;" u2="&#x7d;" k="43" />
<hkern u1="&#x30;" u2="]" k="43" />
<hkern u1="&#x30;" u2="\" k="53" />
<hkern u1="&#x30;" u2="&#x2f;" k="53" />
<hkern u1="&#x30;" u2="&#x29;" k="47" />
<hkern u1="&#x32;" u2="V" k="31" />
<hkern u1="&#x32;" u2="&#xf7;" k="16" />
<hkern u1="&#x32;" u2="&#xb7;" k="12" />
<hkern u1="&#x32;" u2="&#x7d;" k="8" />
<hkern u1="&#x32;" u2="]" k="10" />
<hkern u1="&#x32;" u2="\" k="47" />
<hkern u1="&#x32;" u2="&#x34;" k="8" />
<hkern u1="&#x32;" u2="&#x2b;" k="12" />
<hkern u1="&#x32;" u2="&#x29;" k="12" />
<hkern u1="&#x33;" u2="V" k="14" />
<hkern u1="&#x33;" u2="\" k="18" />
<hkern u1="&#x33;" u2="&#x31;" k="27" />
<hkern u1="&#x33;" u2="&#x2f;" k="31" />
<hkern u1="&#x34;" u2="V" k="63" />
<hkern u1="&#x34;" u2="&#x7d;" k="53" />
<hkern u1="&#x34;" u2="]" k="47" />
<hkern u1="&#x34;" u2="\" k="80" />
<hkern u1="&#x34;" u2="&#x39;" k="6" />
<hkern u1="&#x34;" u2="&#x37;" k="51" />
<hkern u1="&#x34;" u2="&#x31;" k="51" />
<hkern u1="&#x34;" u2="&#x2f;" k="16" />
<hkern u1="&#x34;" u2="&#x29;" k="55" />
<hkern u1="&#x35;" u2="V" k="14" />
<hkern u1="&#x35;" u2="\" k="18" />
<hkern u1="&#x35;" u2="&#x31;" k="12" />
<hkern u1="&#x35;" u2="&#x2f;" k="27" />
<hkern u1="&#x36;" u2="V" k="68" />
<hkern u1="&#x36;" u2="&#x7d;" k="68" />
<hkern u1="&#x36;" u2="]" k="59" />
<hkern u1="&#x36;" u2="\" k="88" />
<hkern u1="&#x36;" u2="&#x37;" k="70" />
<hkern u1="&#x36;" u2="&#x31;" k="43" />
<hkern u1="&#x36;" u2="&#x2f;" k="27" />
<hkern u1="&#x36;" u2="&#x29;" k="72" />
<hkern u1="&#x37;" u2="&#xf7;" k="53" />
<hkern u1="&#x37;" u2="&#xb7;" k="35" />
<hkern u1="&#x37;" u2="&#xa2;" k="41" />
<hkern u1="&#x37;" u2="&#x3d;" k="10" />
<hkern u1="&#x37;" u2="&#x36;" k="53" />
<hkern u1="&#x37;" u2="&#x34;" k="66" />
<hkern u1="&#x37;" u2="&#x2f;" k="141" />
<hkern u1="&#x37;" u2="&#x2b;" k="53" />
<hkern u1="&#x37;" u2="&#x23;" k="6" />
<hkern u1="&#x38;" u2="V" k="37" />
<hkern u1="&#x38;" u2="&#x7d;" k="39" />
<hkern u1="&#x38;" u2="]" k="37" />
<hkern u1="&#x38;" u2="\" k="57" />
<hkern u1="&#x38;" u2="&#x2f;" k="31" />
<hkern u1="&#x38;" u2="&#x29;" k="43" />
<hkern u1="&#x39;" u2="X" k="27" />
<hkern u1="&#x39;" u2="V" k="25" />
<hkern u1="&#x39;" u2="&#xf7;" k="10" />
<hkern u1="&#x39;" u2="&#xb7;" k="6" />
<hkern u1="&#x39;" u2="&#x7d;" k="18" />
<hkern u1="&#x39;" u2="]" k="18" />
<hkern u1="&#x39;" u2="\" k="31" />
<hkern u1="&#x39;" u2="&#x36;" k="8" />
<hkern u1="&#x39;" u2="&#x34;" k="12" />
<hkern u1="&#x39;" u2="&#x33;" k="12" />
<hkern u1="&#x39;" u2="&#x2f;" k="117" />
<hkern u1="&#x39;" u2="&#x29;" k="23" />
<hkern u1="&#x3a;" u2="&#x166;" k="27" />
<hkern u1="&#x3b;" u2="&#x166;" k="27" />
<hkern u1="&#x3d;" u2="&#x37;" k="18" />
<hkern u1="&#x40;" u2="j" k="-8" />
<hkern u1="&#x40;" u2="X" k="8" />
<hkern u1="&#x40;" u2="V" k="45" />
<hkern u1="A" u2="&#x135;" k="12" />
<hkern u1="B" u2="&#x2122;" k="10" />
<hkern u1="B" u2="&#xee;" k="-14" />
<hkern u1="B" u2="&#x7d;" k="39" />
<hkern u1="B" u2="x" k="10" />
<hkern u1="B" u2="]" k="37" />
<hkern u1="B" u2="\" k="45" />
<hkern u1="B" u2="X" k="51" />
<hkern u1="B" u2="V" k="39" />
<hkern u1="B" u2="&#x2f;" k="35" />
<hkern u1="B" u2="&#x29;" k="43" />
<hkern u1="C" u2="&#xee;" k="-8" />
<hkern u1="E" u2="&#x135;" k="8" />
<hkern u1="E" u2="&#x129;" k="-27" />
<hkern u1="E" u2="&#xef;" k="-29" />
<hkern u1="E" u2="&#xee;" k="-43" />
<hkern u1="E" u2="&#xec;" k="-16" />
<hkern u1="F" u2="&#x17f;" k="14" />
<hkern u1="F" u2="&#x135;" k="14" />
<hkern u1="F" u2="&#x131;" k="10" />
<hkern u1="F" u2="&#x12b;" k="-20" />
<hkern u1="F" u2="&#x129;" k="-43" />
<hkern u1="F" u2="&#xf0;" k="27" />
<hkern u1="F" u2="&#xef;" k="-45" />
<hkern u1="F" u2="&#xee;" k="-59" />
<hkern u1="F" u2="&#xec;" k="-37" />
<hkern u1="F" u2="&#xdf;" k="6" />
<hkern u1="F" u2="x" k="39" />
<hkern u1="F" u2="v" k="14" />
<hkern u1="F" u2="j" k="4" />
<hkern u1="F" u2="X" k="6" />
<hkern u1="F" u2="V" k="6" />
<hkern u1="F" u2="&#x34;" k="6" />
<hkern u1="F" u2="&#x32;" k="8" />
<hkern u1="F" u2="&#x2f;" k="98" />
<hkern u1="F" u2="&#x20;" k="31" />
<hkern u1="H" u2="&#x129;" k="-14" />
<hkern u1="H" u2="&#xef;" k="-16" />
<hkern u1="H" u2="&#xee;" k="-31" />
<hkern u1="I" u2="&#x129;" k="-14" />
<hkern u1="I" u2="&#xef;" k="-16" />
<hkern u1="I" u2="&#xee;" k="-31" />
<hkern u1="J" u2="&#x129;" k="-23" />
<hkern u1="J" u2="&#xef;" k="-25" />
<hkern u1="J" u2="&#xee;" k="-39" />
<hkern u1="J" u2="&#xec;" k="-12" />
<hkern u1="K" u2="&#x135;" k="12" />
<hkern u1="K" u2="&#x12d;" k="-10" />
<hkern u1="K" u2="&#x12b;" k="-39" />
<hkern u1="K" u2="&#x129;" k="-51" />
<hkern u1="K" u2="&#xef;" k="-66" />
<hkern u1="K" u2="&#xee;" k="-41" />
<hkern u1="K" u2="&#xec;" k="-70" />
<hkern u1="L" u2="&#x135;" k="18" />
<hkern u1="M" u2="&#x129;" k="-14" />
<hkern u1="M" u2="&#xef;" k="-16" />
<hkern u1="M" u2="&#xee;" k="-31" />
<hkern u1="N" u2="&#x129;" k="-14" />
<hkern u1="N" u2="&#xef;" k="-16" />
<hkern u1="N" u2="&#xee;" k="-31" />
<hkern u1="P" u2="&#x2122;" k="10" />
<hkern u1="P" u2="&#xf0;" k="39" />
<hkern u1="P" u2="&#xee;" k="-23" />
<hkern u1="P" u2="&#x7d;" k="35" />
<hkern u1="P" u2="]" k="37" />
<hkern u1="P" u2="\" k="25" />
<hkern u1="P" u2="X" k="78" />
<hkern u1="P" u2="V" k="45" />
<hkern u1="P" u2="&#x36;" k="6" />
<hkern u1="P" u2="&#x34;" k="14" />
<hkern u1="P" u2="&#x33;" k="8" />
<hkern u1="P" u2="&#x2f;" k="121" />
<hkern u1="P" u2="&#x29;" k="39" />
<hkern u1="P" u2="&#x26;" k="18" />
<hkern u1="P" u2="&#x20;" k="43" />
<hkern u1="Q" u2="&#x2f;" k="45" />
<hkern u1="T" u2="&#x159;" k="41" />
<hkern u1="T" u2="&#x135;" k="23" />
<hkern u1="T" u2="&#x131;" k="100" />
<hkern u1="T" u2="&#x12d;" k="-10" />
<hkern u1="T" u2="&#x12b;" k="-49" />
<hkern u1="T" u2="&#x129;" k="-66" />
<hkern u1="T" u2="&#x127;" k="-18" />
<hkern u1="T" u2="&#xef;" k="-72" />
<hkern u1="T" u2="&#xee;" k="-80" />
<hkern u1="T" u2="&#xed;" k="25" />
<hkern u1="T" u2="&#xec;" k="-63" />
<hkern u1="T" u2="&#xdf;" k="63" />
<hkern u1="U" u2="&#x129;" k="-23" />
<hkern u1="U" u2="&#xef;" k="-25" />
<hkern u1="U" u2="&#xee;" k="-39" />
<hkern u1="U" u2="&#xec;" k="-12" />
<hkern u1="V" u2="&#x17f;" k="29" />
<hkern u1="V" u2="&#x159;" k="66" />
<hkern u1="V" u2="&#x135;" k="27" />
<hkern u1="V" u2="&#x131;" k="72" />
<hkern u1="V" u2="&#x12d;" k="-12" />
<hkern u1="V" u2="&#x12b;" k="-47" />
<hkern u1="V" u2="&#x129;" k="-57" />
<hkern u1="V" u2="&#xf0;" k="127" />
<hkern u1="V" u2="&#xef;" k="-72" />
<hkern u1="V" u2="&#xee;" k="-63" />
<hkern u1="V" u2="&#xed;" k="31" />
<hkern u1="V" u2="&#xec;" k="-63" />
<hkern u1="V" u2="&#xdf;" k="45" />
<hkern u1="V" u2="&#xae;" k="45" />
<hkern u1="V" u2="&#xa9;" k="45" />
<hkern u1="V" u2="x" k="53" />
<hkern u1="V" u2="v" k="39" />
<hkern u1="V" u2="&#x40;" k="59" />
<hkern u1="V" u2="&#x3f;" k="14" />
<hkern u1="V" u2="&#x39;" k="25" />
<hkern u1="V" u2="&#x38;" k="35" />
<hkern u1="V" u2="&#x36;" k="76" />
<hkern u1="V" u2="&#x35;" k="33" />
<hkern u1="V" u2="&#x34;" k="82" />
<hkern u1="V" u2="&#x32;" k="31" />
<hkern u1="V" u2="&#x31;" k="25" />
<hkern u1="V" u2="&#x30;" k="33" />
<hkern u1="V" u2="&#x2f;" k="150" />
<hkern u1="V" u2="&#x26;" k="61" />
<hkern u1="V" u2="&#x20;" k="51" />
<hkern u1="W" u2="&#x159;" k="4" />
<hkern u1="W" u2="&#x131;" k="20" />
<hkern u1="W" u2="&#x12b;" k="-35" />
<hkern u1="W" u2="&#x129;" k="-47" />
<hkern u1="W" u2="&#xef;" k="-59" />
<hkern u1="W" u2="&#xee;" k="-61" />
<hkern u1="W" u2="&#xec;" k="-43" />
<hkern u1="W" u2="&#xdf;" k="10" />
<hkern u1="X" u2="&#x17f;" k="55" />
<hkern u1="X" u2="&#x135;" k="8" />
<hkern u1="X" u2="&#x12b;" k="-39" />
<hkern u1="X" u2="&#x129;" k="-51" />
<hkern u1="X" u2="&#xf0;" k="53" />
<hkern u1="X" u2="&#xef;" k="-63" />
<hkern u1="X" u2="&#xee;" k="-39" />
<hkern u1="X" u2="&#xec;" k="-63" />
<hkern u1="X" u2="&#xae;" k="49" />
<hkern u1="X" u2="&#xa9;" k="49" />
<hkern u1="X" u2="v" k="98" />
<hkern u1="X" u2="&#x3f;" k="14" />
<hkern u1="X" u2="&#x39;" k="16" />
<hkern u1="X" u2="&#x34;" k="8" />
<hkern u1="X" u2="&#x31;" k="43" />
<hkern u1="X" u2="&#x30;" k="16" />
<hkern u1="Y" u2="&#x15d;" k="117" />
<hkern u1="Y" u2="&#x159;" k="43" />
<hkern u1="Y" u2="&#x135;" k="55" />
<hkern u1="Y" u2="&#x131;" k="137" />
<hkern u1="Y" u2="&#x12d;" k="-31" />
<hkern u1="Y" u2="&#x12b;" k="-61" />
<hkern u1="Y" u2="&#x129;" k="-72" />
<hkern u1="Y" u2="&#x127;" k="-8" />
<hkern u1="Y" u2="&#xef;" k="-88" />
<hkern u1="Y" u2="&#xee;" k="-63" />
<hkern u1="Y" u2="&#xed;" k="53" />
<hkern u1="Y" u2="&#xec;" k="-80" />
<hkern u1="Y" u2="&#xe4;" k="117" />
<hkern u1="Y" u2="&#xe0;" k="121" />
<hkern u1="Y" u2="&#xdf;" k="82" />
<hkern u1="Z" u2="&#x135;" k="8" />
<hkern u1="Z" u2="&#x129;" k="-27" />
<hkern u1="Z" u2="&#xef;" k="-27" />
<hkern u1="Z" u2="&#xee;" k="-43" />
<hkern u1="Z" u2="&#xec;" k="-14" />
<hkern u1="[" u2="&#x12e;" k="2" />
<hkern u1="[" u2="&#x12d;" k="-31" />
<hkern u1="[" u2="&#x12b;" k="-49" />
<hkern u1="[" u2="&#x12a;" k="-57" />
<hkern u1="[" u2="&#x129;" k="-72" />
<hkern u1="[" u2="&#x128;" k="-53" />
<hkern u1="[" u2="&#x127;" k="-10" />
<hkern u1="[" u2="&#xf0;" k="66" />
<hkern u1="[" u2="&#xef;" k="-74" />
<hkern u1="[" u2="&#xee;" k="-88" />
<hkern u1="[" u2="&#xec;" k="-156" />
<hkern u1="[" u2="&#xcf;" k="-76" />
<hkern u1="[" u2="&#xce;" k="-86" />
<hkern u1="[" u2="x" k="18" />
<hkern u1="[" u2="v" k="78" />
<hkern u1="[" u2="j" k="-113" />
<hkern u1="[" u2="&#x7b;" k="47" />
<hkern u1="[" u2="&#x39;" k="14" />
<hkern u1="[" u2="&#x38;" k="33" />
<hkern u1="[" u2="&#x36;" k="68" />
<hkern u1="[" u2="&#x35;" k="23" />
<hkern u1="[" u2="&#x34;" k="63" />
<hkern u1="[" u2="&#x32;" k="12" />
<hkern u1="[" u2="&#x31;" k="61" />
<hkern u1="[" u2="&#x30;" k="43" />
<hkern u1="[" u2="&#x28;" k="18" />
<hkern u1="\" u2="&#x135;" k="6" />
<hkern u1="\" u2="&#xf0;" k="45" />
<hkern u1="\" u2="v" k="117" />
<hkern u1="\" u2="j" k="-100" />
<hkern u1="\" u2="V" k="150" />
<hkern u1="\" u2="\" k="473" />
<hkern u1="\" u2="&#x39;" k="78" />
<hkern u1="\" u2="&#x38;" k="31" />
<hkern u1="\" u2="&#x37;" k="57" />
<hkern u1="\" u2="&#x36;" k="33" />
<hkern u1="\" u2="&#x35;" k="20" />
<hkern u1="\" u2="&#x34;" k="23" />
<hkern u1="\" u2="&#x33;" k="20" />
<hkern u1="\" u2="&#x31;" k="133" />
<hkern u1="\" u2="&#x30;" k="53" />
<hkern u1="a" u2="&#x135;" k="4" />
<hkern u1="b" u2="&#x135;" k="14" />
<hkern u1="d" u2="&#x129;" k="-27" />
<hkern u1="d" u2="&#xef;" k="-27" />
<hkern u1="d" u2="&#xee;" k="-41" />
<hkern u1="d" u2="&#xec;" k="-16" />
<hkern u1="e" u2="&#x135;" k="10" />
<hkern u1="f" u2="&#x12d;" k="-55" />
<hkern u1="f" u2="&#x12b;" k="-63" />
<hkern u1="f" u2="&#x129;" k="-88" />
<hkern u1="f" u2="&#xef;" k="-106" />
<hkern u1="f" u2="&#xee;" k="-102" />
<hkern u1="f" u2="&#xec;" k="-168" />
<hkern u1="g" u2="j" k="-29" />
<hkern u1="h" u2="&#x135;" k="6" />
<hkern u1="i" u2="&#x129;" k="-20" />
<hkern u1="i" u2="&#xef;" k="-23" />
<hkern u1="i" u2="&#xee;" k="-35" />
<hkern u1="i" u2="&#xec;" k="-76" />
<hkern u1="j" u2="&#x129;" k="-20" />
<hkern u1="j" u2="&#xef;" k="-23" />
<hkern u1="j" u2="&#xee;" k="-35" />
<hkern u1="j" u2="&#xec;" k="-76" />
<hkern u1="j" u2="j" k="-25" />
<hkern u1="l" u2="&#x129;" k="-27" />
<hkern u1="l" u2="&#xef;" k="-27" />
<hkern u1="l" u2="&#xee;" k="-41" />
<hkern u1="l" u2="&#xec;" k="-16" />
<hkern u1="m" u2="&#x135;" k="6" />
<hkern u1="n" u2="&#x135;" k="6" />
<hkern u1="o" u2="&#x135;" k="16" />
<hkern u1="p" u2="&#x135;" k="14" />
<hkern u1="s" u2="&#x135;" k="6" />
<hkern u1="v" u2="&#x2122;" k="23" />
<hkern u1="v" u2="&#xf0;" k="61" />
<hkern u1="v" u2="&#x7d;" k="78" />
<hkern u1="v" u2="]" k="78" />
<hkern u1="v" u2="\" k="68" />
<hkern u1="v" u2="X" k="96" />
<hkern u1="v" u2="V" k="39" />
<hkern u1="v" u2="&#x2f;" k="117" />
<hkern u1="v" u2="&#x29;" k="88" />
<hkern u1="v" u2="&#x26;" k="29" />
<hkern u1="v" u2="&#x20;" k="51" />
<hkern u1="x" u2="&#x2122;" k="31" />
<hkern u1="x" u2="&#xf0;" k="51" />
<hkern u1="x" u2="&#x7d;" k="16" />
<hkern u1="x" u2="]" k="18" />
<hkern u1="x" u2="\" k="72" />
<hkern u1="x" u2="V" k="51" />
<hkern u1="x" u2="&#x29;" k="23" />
<hkern u1="&#x7b;" u2="&#x12e;" k="2" />
<hkern u1="&#x7b;" u2="&#x12d;" k="-31" />
<hkern u1="&#x7b;" u2="&#x12b;" k="-51" />
<hkern u1="&#x7b;" u2="&#x12a;" k="-61" />
<hkern u1="&#x7b;" u2="&#x129;" k="-74" />
<hkern u1="&#x7b;" u2="&#x128;" k="-57" />
<hkern u1="&#x7b;" u2="&#x127;" k="-12" />
<hkern u1="&#x7b;" u2="&#xf0;" k="72" />
<hkern u1="&#x7b;" u2="&#xef;" k="-76" />
<hkern u1="&#x7b;" u2="&#xee;" k="-88" />
<hkern u1="&#x7b;" u2="&#xec;" k="-158" />
<hkern u1="&#x7b;" u2="&#xcf;" k="-82" />
<hkern u1="&#x7b;" u2="&#xce;" k="-88" />
<hkern u1="&#x7b;" u2="x" k="16" />
<hkern u1="&#x7b;" u2="v" k="78" />
<hkern u1="&#x7b;" u2="j" k="-115" />
<hkern u1="&#x7b;" u2="&#x7b;" k="53" />
<hkern u1="&#x7b;" u2="&#x39;" k="14" />
<hkern u1="&#x7b;" u2="&#x38;" k="35" />
<hkern u1="&#x7b;" u2="&#x36;" k="80" />
<hkern u1="&#x7b;" u2="&#x35;" k="25" />
<hkern u1="&#x7b;" u2="&#x34;" k="72" />
<hkern u1="&#x7b;" u2="&#x32;" k="10" />
<hkern u1="&#x7b;" u2="&#x31;" k="59" />
<hkern u1="&#x7b;" u2="&#x30;" k="43" />
<hkern u1="&#x7b;" u2="&#x28;" k="20" />
<hkern u1="&#x7c;" u2="&#x12b;" k="-12" />
<hkern u1="&#x7c;" u2="&#x12a;" k="-18" />
<hkern u1="&#x7c;" u2="&#x129;" k="-35" />
<hkern u1="&#x7c;" u2="&#x128;" k="-8" />
<hkern u1="&#x7c;" u2="&#xef;" k="-37" />
<hkern u1="&#x7c;" u2="&#xee;" k="-51" />
<hkern u1="&#x7c;" u2="&#xec;" k="-96" />
<hkern u1="&#x7c;" u2="&#xcf;" k="-31" />
<hkern u1="&#x7c;" u2="&#xce;" k="-31" />
<hkern u1="&#x7c;" u2="&#xcc;" k="-72" />
<hkern u1="&#x7c;" u2="j" k="-57" />
<hkern u1="&#x7d;" u2="&#x7d;" k="53" />
<hkern u1="&#x7d;" u2="]" k="47" />
<hkern u1="&#x7d;" u2="&#x29;" k="53" />
<hkern u1="&#xa1;" u2="&#x166;" k="33" />
<hkern u1="&#xa1;" u2="j" k="-63" />
<hkern u1="&#xa1;" u2="V" k="51" />
<hkern u1="&#xa9;" u2="&#x166;" k="14" />
<hkern u1="&#xa9;" u2="X" k="49" />
<hkern u1="&#xa9;" u2="V" k="47" />
<hkern u1="&#xab;" u2="&#x129;" k="-27" />
<hkern u1="&#xab;" u2="&#xef;" k="-27" />
<hkern u1="&#xab;" u2="&#xee;" k="-43" />
<hkern u1="&#xae;" u2="&#x166;" k="14" />
<hkern u1="&#xae;" u2="X" k="49" />
<hkern u1="&#xae;" u2="V" k="47" />
<hkern u1="&#xb0;" u2="&#x36;" k="41" />
<hkern u1="&#xb0;" u2="&#x34;" k="61" />
<hkern u1="&#xb7;" u2="&#x37;" k="68" />
<hkern u1="&#xb7;" u2="&#x33;" k="29" />
<hkern u1="&#xb7;" u2="&#x32;" k="37" />
<hkern u1="&#xb7;" u2="&#x31;" k="18" />
<hkern u1="&#xbb;" u2="&#x166;" k="66" />
<hkern u1="&#xbf;" u2="&#xf0;" k="29" />
<hkern u1="&#xbf;" u2="v" k="123" />
<hkern u1="&#xbf;" u2="j" k="-78" />
<hkern u1="&#xbf;" u2="V" k="145" />
<hkern u1="&#xc0;" u2="&#x135;" k="12" />
<hkern u1="&#xc1;" u2="&#x135;" k="12" />
<hkern u1="&#xc2;" u2="&#x135;" k="12" />
<hkern u1="&#xc3;" u2="&#x135;" k="12" />
<hkern u1="&#xc4;" u2="&#x135;" k="12" />
<hkern u1="&#xc5;" u2="&#x135;" k="12" />
<hkern u1="&#xc6;" u2="&#x135;" k="8" />
<hkern u1="&#xc6;" u2="&#x129;" k="-27" />
<hkern u1="&#xc6;" u2="&#xef;" k="-29" />
<hkern u1="&#xc6;" u2="&#xee;" k="-43" />
<hkern u1="&#xc6;" u2="&#xec;" k="-16" />
<hkern u1="&#xc7;" u2="&#xee;" k="-8" />
<hkern u1="&#xc8;" u2="&#x135;" k="8" />
<hkern u1="&#xc8;" u2="&#x129;" k="-27" />
<hkern u1="&#xc8;" u2="&#xef;" k="-29" />
<hkern u1="&#xc8;" u2="&#xee;" k="-43" />
<hkern u1="&#xc8;" u2="&#xec;" k="-16" />
<hkern u1="&#xc9;" u2="&#x135;" k="8" />
<hkern u1="&#xc9;" u2="&#x129;" k="-27" />
<hkern u1="&#xc9;" u2="&#xef;" k="-29" />
<hkern u1="&#xc9;" u2="&#xee;" k="-43" />
<hkern u1="&#xc9;" u2="&#xec;" k="-16" />
<hkern u1="&#xca;" u2="&#x135;" k="8" />
<hkern u1="&#xca;" u2="&#x129;" k="-27" />
<hkern u1="&#xca;" u2="&#xef;" k="-29" />
<hkern u1="&#xca;" u2="&#xee;" k="-43" />
<hkern u1="&#xca;" u2="&#xec;" k="-16" />
<hkern u1="&#xcb;" u2="&#x135;" k="8" />
<hkern u1="&#xcb;" u2="&#x129;" k="-27" />
<hkern u1="&#xcb;" u2="&#xef;" k="-29" />
<hkern u1="&#xcb;" u2="&#xee;" k="-43" />
<hkern u1="&#xcb;" u2="&#xec;" k="-16" />
<hkern u1="&#xcc;" u2="&#x129;" k="-14" />
<hkern u1="&#xcc;" u2="&#xef;" k="-16" />
<hkern u1="&#xcc;" u2="&#xee;" k="-31" />
<hkern u1="&#xcd;" u2="&#x129;" k="-14" />
<hkern u1="&#xcd;" u2="&#xef;" k="-16" />
<hkern u1="&#xcd;" u2="&#xee;" k="-31" />
<hkern u1="&#xcd;" u2="&#x7c;" k="-72" />
<hkern u1="&#xce;" u2="&#x129;" k="-14" />
<hkern u1="&#xce;" u2="&#xef;" k="-16" />
<hkern u1="&#xce;" u2="&#xee;" k="-31" />
<hkern u1="&#xce;" u2="&#x7d;" k="-90" />
<hkern u1="&#xce;" u2="&#x7c;" k="-31" />
<hkern u1="&#xce;" u2="]" k="-88" />
<hkern u1="&#xce;" u2="\" k="-96" />
<hkern u1="&#xce;" u2="&#x29;" k="-90" />
<hkern u1="&#xcf;" u2="&#x129;" k="-14" />
<hkern u1="&#xcf;" u2="&#xef;" k="-16" />
<hkern u1="&#xcf;" u2="&#xee;" k="-31" />
<hkern u1="&#xcf;" u2="&#x7d;" k="-86" />
<hkern u1="&#xcf;" u2="&#x7c;" k="-31" />
<hkern u1="&#xcf;" u2="]" k="-82" />
<hkern u1="&#xcf;" u2="\" k="-96" />
<hkern u1="&#xcf;" u2="&#x29;" k="-84" />
<hkern u1="&#xd1;" u2="&#x129;" k="-14" />
<hkern u1="&#xd1;" u2="&#xef;" k="-16" />
<hkern u1="&#xd1;" u2="&#xee;" k="-31" />
<hkern u1="&#xd7;" u2="&#x37;" k="16" />
<hkern u1="&#xd8;" u2="&#x2a;" k="-18" />
<hkern u1="&#xd9;" u2="&#x129;" k="-23" />
<hkern u1="&#xd9;" u2="&#xef;" k="-25" />
<hkern u1="&#xd9;" u2="&#xee;" k="-39" />
<hkern u1="&#xd9;" u2="&#xec;" k="-12" />
<hkern u1="&#xda;" u2="&#x129;" k="-23" />
<hkern u1="&#xda;" u2="&#xef;" k="-25" />
<hkern u1="&#xda;" u2="&#xee;" k="-39" />
<hkern u1="&#xda;" u2="&#xec;" k="-12" />
<hkern u1="&#xdb;" u2="&#x129;" k="-23" />
<hkern u1="&#xdb;" u2="&#xef;" k="-25" />
<hkern u1="&#xdb;" u2="&#xee;" k="-39" />
<hkern u1="&#xdb;" u2="&#xec;" k="-12" />
<hkern u1="&#xdc;" u2="&#x129;" k="-23" />
<hkern u1="&#xdc;" u2="&#xef;" k="-25" />
<hkern u1="&#xdc;" u2="&#xee;" k="-39" />
<hkern u1="&#xdc;" u2="&#xec;" k="-12" />
<hkern u1="&#xdd;" u2="&#x15d;" k="117" />
<hkern u1="&#xdd;" u2="&#x159;" k="43" />
<hkern u1="&#xdd;" u2="&#x135;" k="55" />
<hkern u1="&#xdd;" u2="&#x131;" k="137" />
<hkern u1="&#xdd;" u2="&#x12d;" k="-31" />
<hkern u1="&#xdd;" u2="&#x12b;" k="-61" />
<hkern u1="&#xdd;" u2="&#x129;" k="-72" />
<hkern u1="&#xdd;" u2="&#x127;" k="-8" />
<hkern u1="&#xdd;" u2="&#xef;" k="-88" />
<hkern u1="&#xdd;" u2="&#xee;" k="-63" />
<hkern u1="&#xdd;" u2="&#xed;" k="53" />
<hkern u1="&#xdd;" u2="&#xec;" k="-80" />
<hkern u1="&#xdd;" u2="&#xe4;" k="117" />
<hkern u1="&#xdd;" u2="&#xe0;" k="121" />
<hkern u1="&#xdd;" u2="&#xdf;" k="82" />
<hkern u1="&#xde;" u2="&#x2122;" k="49" />
<hkern u1="&#xde;" u2="&#xba;" k="16" />
<hkern u1="&#xde;" u2="&#x7d;" k="76" />
<hkern u1="&#xde;" u2="x" k="25" />
<hkern u1="&#xde;" u2="v" k="4" />
<hkern u1="&#xde;" u2="]" k="70" />
<hkern u1="&#xde;" u2="\" k="70" />
<hkern u1="&#xde;" u2="X" k="119" />
<hkern u1="&#xde;" u2="V" k="53" />
<hkern u1="&#xde;" u2="&#x2f;" k="72" />
<hkern u1="&#xde;" u2="&#x2a;" k="8" />
<hkern u1="&#xde;" u2="&#x29;" k="82" />
<hkern u1="&#xdf;" u2="&#x2122;" k="33" />
<hkern u1="&#xdf;" u2="&#x17f;" k="8" />
<hkern u1="&#xdf;" u2="&#x7d;" k="47" />
<hkern u1="&#xdf;" u2="x" k="29" />
<hkern u1="&#xdf;" u2="v" k="31" />
<hkern u1="&#xdf;" u2="]" k="45" />
<hkern u1="&#xdf;" u2="\" k="66" />
<hkern u1="&#xdf;" u2="&#x2f;" k="45" />
<hkern u1="&#xdf;" u2="&#x29;" k="51" />
<hkern u1="&#xe0;" u2="&#x135;" k="4" />
<hkern u1="&#xe1;" u2="&#x135;" k="4" />
<hkern u1="&#xe1;" u2="\" k="92" />
<hkern u1="&#xe2;" u2="&#x135;" k="4" />
<hkern u1="&#xe3;" u2="&#x135;" k="4" />
<hkern u1="&#xe4;" u2="&#x135;" k="4" />
<hkern u1="&#xe5;" u2="&#x135;" k="4" />
<hkern u1="&#xe6;" u2="&#x135;" k="10" />
<hkern u1="&#xe8;" u2="&#x135;" k="10" />
<hkern u1="&#xe9;" u2="&#x135;" k="10" />
<hkern u1="&#xe9;" u2="\" k="92" />
<hkern u1="&#xea;" u2="&#x135;" k="10" />
<hkern u1="&#xeb;" u2="&#x135;" k="10" />
<hkern u1="&#xec;" u2="&#x129;" k="-20" />
<hkern u1="&#xec;" u2="&#xef;" k="-23" />
<hkern u1="&#xec;" u2="&#xee;" k="-35" />
<hkern u1="&#xec;" u2="&#xec;" k="-76" />
<hkern u1="&#xed;" u2="&#x2122;" k="-88" />
<hkern u1="&#xed;" u2="&#x201d;" k="-16" />
<hkern u1="&#xed;" u2="&#x2019;" k="-16" />
<hkern u1="&#xed;" u2="&#x17e;" k="-35" />
<hkern u1="&#xed;" u2="&#x161;" k="-41" />
<hkern u1="&#xed;" u2="&#x159;" k="-68" />
<hkern u1="&#xed;" u2="&#x142;" k="-18" />
<hkern u1="&#xed;" u2="&#x140;" k="-18" />
<hkern u1="&#xed;" u2="&#x13e;" k="-18" />
<hkern u1="&#xed;" u2="&#x13c;" k="-18" />
<hkern u1="&#xed;" u2="&#x13a;" k="-18" />
<hkern u1="&#xed;" u2="&#x137;" k="-18" />
<hkern u1="&#xed;" u2="&#x133;" k="-78" />
<hkern u1="&#xed;" u2="&#x131;" k="-78" />
<hkern u1="&#xed;" u2="&#x12f;" k="-78" />
<hkern u1="&#xed;" u2="&#x12d;" k="-78" />
<hkern u1="&#xed;" u2="&#x12b;" k="-78" />
<hkern u1="&#xed;" u2="&#x129;" k="-78" />
<hkern u1="&#xed;" u2="&#x127;" k="-18" />
<hkern u1="&#xed;" u2="&#x125;" k="-18" />
<hkern u1="&#xed;" u2="&#x10d;" k="-10" />
<hkern u1="&#xed;" u2="&#xfe;" k="-18" />
<hkern u1="&#xed;" u2="&#xef;" k="-78" />
<hkern u1="&#xed;" u2="&#xee;" k="-78" />
<hkern u1="&#xed;" u2="&#xed;" k="-2" />
<hkern u1="&#xed;" u2="&#xec;" k="-78" />
<hkern u1="&#xed;" u2="&#xdf;" k="-18" />
<hkern u1="&#xed;" u2="&#x7d;" k="-158" />
<hkern u1="&#xed;" u2="&#x7c;" k="-98" />
<hkern u1="&#xed;" u2="l" k="-18" />
<hkern u1="&#xed;" u2="k" k="-18" />
<hkern u1="&#xed;" u2="j" k="-59" />
<hkern u1="&#xed;" u2="i" k="-78" />
<hkern u1="&#xed;" u2="h" k="-18" />
<hkern u1="&#xed;" u2="b" k="-18" />
<hkern u1="&#xed;" u2="]" k="-158" />
<hkern u1="&#xed;" u2="\" k="-156" />
<hkern u1="&#xed;" u2="&#x29;" k="-160" />
<hkern u1="&#xed;" u2="&#x27;" k="-70" />
<hkern u1="&#xed;" u2="&#x22;" k="-70" />
<hkern u1="&#xed;" u2="&#x21;" k="-51" />
<hkern u1="&#xee;" u2="&#x2122;" k="-96" />
<hkern u1="&#xee;" u2="&#x203a;" k="-43" />
<hkern u1="&#xee;" u2="&#x201d;" k="-43" />
<hkern u1="&#xee;" u2="&#x201c;" k="-12" />
<hkern u1="&#xee;" u2="&#x2019;" k="-43" />
<hkern u1="&#xee;" u2="&#x2018;" k="-12" />
<hkern u1="&#xee;" u2="&#x142;" k="-39" />
<hkern u1="&#xee;" u2="&#x140;" k="-39" />
<hkern u1="&#xee;" u2="&#x13e;" k="-39" />
<hkern u1="&#xee;" u2="&#x13c;" k="-39" />
<hkern u1="&#xee;" u2="&#x13a;" k="-39" />
<hkern u1="&#xee;" u2="&#x137;" k="-39" />
<hkern u1="&#xee;" u2="&#x133;" k="-35" />
<hkern u1="&#xee;" u2="&#x131;" k="-35" />
<hkern u1="&#xee;" u2="&#x12f;" k="-35" />
<hkern u1="&#xee;" u2="&#x12d;" k="-35" />
<hkern u1="&#xee;" u2="&#x12b;" k="-35" />
<hkern u1="&#xee;" u2="&#x129;" k="-35" />
<hkern u1="&#xee;" u2="&#x127;" k="-39" />
<hkern u1="&#xee;" u2="&#x125;" k="-39" />
<hkern u1="&#xee;" u2="&#xfe;" k="-39" />
<hkern u1="&#xee;" u2="&#xef;" k="-35" />
<hkern u1="&#xee;" u2="&#xee;" k="-35" />
<hkern u1="&#xee;" u2="&#xed;" k="-35" />
<hkern u1="&#xee;" u2="&#xec;" k="-35" />
<hkern u1="&#xee;" u2="&#xdf;" k="-39" />
<hkern u1="&#xee;" u2="&#xbb;" k="-43" />
<hkern u1="&#xee;" u2="&#xba;" k="-82" />
<hkern u1="&#xee;" u2="&#xaa;" k="-59" />
<hkern u1="&#xee;" u2="&#x7d;" k="-88" />
<hkern u1="&#xee;" u2="&#x7c;" k="-49" />
<hkern u1="&#xee;" u2="l" k="-39" />
<hkern u1="&#xee;" u2="k" k="-39" />
<hkern u1="&#xee;" u2="j" k="-16" />
<hkern u1="&#xee;" u2="i" k="-35" />
<hkern u1="&#xee;" u2="h" k="-39" />
<hkern u1="&#xee;" u2="b" k="-39" />
<hkern u1="&#xee;" u2="]" k="-86" />
<hkern u1="&#xee;" u2="\" k="-12" />
<hkern u1="&#xee;" u2="&#x3f;" k="-59" />
<hkern u1="&#xee;" u2="&#x2a;" k="-86" />
<hkern u1="&#xee;" u2="&#x29;" k="-88" />
<hkern u1="&#xee;" u2="&#x27;" k="-72" />
<hkern u1="&#xee;" u2="&#x22;" k="-72" />
<hkern u1="&#xee;" u2="&#x21;" k="-68" />
<hkern u1="&#xef;" u2="&#x2122;" k="-88" />
<hkern u1="&#xef;" u2="&#x203a;" k="-27" />
<hkern u1="&#xef;" u2="&#x201d;" k="-31" />
<hkern u1="&#xef;" u2="&#x2019;" k="-31" />
<hkern u1="&#xef;" u2="&#x142;" k="-27" />
<hkern u1="&#xef;" u2="&#x140;" k="-27" />
<hkern u1="&#xef;" u2="&#x13e;" k="-27" />
<hkern u1="&#xef;" u2="&#x13c;" k="-27" />
<hkern u1="&#xef;" u2="&#x13a;" k="-27" />
<hkern u1="&#xef;" u2="&#x137;" k="-27" />
<hkern u1="&#xef;" u2="&#x133;" k="-23" />
<hkern u1="&#xef;" u2="&#x131;" k="-23" />
<hkern u1="&#xef;" u2="&#x12f;" k="-23" />
<hkern u1="&#xef;" u2="&#x12d;" k="-23" />
<hkern u1="&#xef;" u2="&#x12b;" k="-23" />
<hkern u1="&#xef;" u2="&#x129;" k="-23" />
<hkern u1="&#xef;" u2="&#x127;" k="-27" />
<hkern u1="&#xef;" u2="&#x125;" k="-27" />
<hkern u1="&#xef;" u2="&#xfe;" k="-27" />
<hkern u1="&#xef;" u2="&#xef;" k="-23" />
<hkern u1="&#xef;" u2="&#xee;" k="-23" />
<hkern u1="&#xef;" u2="&#xed;" k="-23" />
<hkern u1="&#xef;" u2="&#xec;" k="-23" />
<hkern u1="&#xef;" u2="&#xdf;" k="-27" />
<hkern u1="&#xef;" u2="&#xbb;" k="-27" />
<hkern u1="&#xef;" u2="&#xba;" k="-68" />
<hkern u1="&#xef;" u2="&#xaa;" k="-51" />
<hkern u1="&#xef;" u2="&#x7d;" k="-76" />
<hkern u1="&#xef;" u2="&#x7c;" k="-37" />
<hkern u1="&#xef;" u2="l" k="-27" />
<hkern u1="&#xef;" u2="k" k="-27" />
<hkern u1="&#xef;" u2="i" k="-23" />
<hkern u1="&#xef;" u2="h" k="-27" />
<hkern u1="&#xef;" u2="b" k="-27" />
<hkern u1="&#xef;" u2="]" k="-74" />
<hkern u1="&#xef;" u2="\" k="-76" />
<hkern u1="&#xef;" u2="&#x3f;" k="-35" />
<hkern u1="&#xef;" u2="&#x2a;" k="-57" />
<hkern u1="&#xef;" u2="&#x29;" k="-76" />
<hkern u1="&#xef;" u2="&#x27;" k="-68" />
<hkern u1="&#xef;" u2="&#x22;" k="-68" />
<hkern u1="&#xef;" u2="&#x21;" k="-55" />
<hkern u1="&#xf0;" u2="&#x2122;" k="18" />
<hkern u1="&#xf0;" u2="&#xba;" k="12" />
<hkern u1="&#xf0;" u2="&#x7d;" k="37" />
<hkern u1="&#xf0;" u2="x" k="33" />
<hkern u1="&#xf0;" u2="v" k="14" />
<hkern u1="&#xf0;" u2="]" k="35" />
<hkern u1="&#xf0;" u2="\" k="35" />
<hkern u1="&#xf0;" u2="&#x2f;" k="49" />
<hkern u1="&#xf0;" u2="&#x2a;" k="12" />
<hkern u1="&#xf0;" u2="&#x29;" k="41" />
<hkern u1="&#xf1;" u2="&#x135;" k="6" />
<hkern u1="&#xf2;" u2="&#x135;" k="16" />
<hkern u1="&#xf3;" u2="&#x135;" k="16" />
<hkern u1="&#xf4;" u2="&#x135;" k="16" />
<hkern u1="&#xf5;" u2="&#x135;" k="16" />
<hkern u1="&#xf6;" u2="&#x135;" k="16" />
<hkern u1="&#xf7;" u2="&#x39;" k="8" />
<hkern u1="&#xf7;" u2="&#x37;" k="90" />
<hkern u1="&#xf7;" u2="&#x33;" k="14" />
<hkern u1="&#xf7;" u2="&#x32;" k="41" />
<hkern u1="&#xf7;" u2="&#x31;" k="49" />
<hkern u1="&#xf8;" u2="&#x135;" k="16" />
<hkern u1="&#xfe;" u2="&#x135;" k="14" />
<hkern u1="&#x100;" u2="&#x135;" k="12" />
<hkern u1="&#x101;" u2="&#x135;" k="4" />
<hkern u1="&#x102;" u2="&#x135;" k="12" />
<hkern u1="&#x103;" u2="&#x135;" k="4" />
<hkern u1="&#x104;" u2="&#x135;" k="12" />
<hkern u1="&#x104;" u2="j" k="-125" />
<hkern u1="&#x105;" u2="&#x135;" k="4" />
<hkern u1="&#x105;" u2="j" k="-72" />
<hkern u1="&#x106;" u2="&#xee;" k="-8" />
<hkern u1="&#x107;" u2="\" k="88" />
<hkern u1="&#x108;" u2="&#xee;" k="-8" />
<hkern u1="&#x10a;" u2="&#xee;" k="-8" />
<hkern u1="&#x10c;" u2="&#xee;" k="-8" />
<hkern u1="&#x10f;" u2="&#x3b;" k="-8" />
<hkern u1="&#x111;" u2="&#x2122;" k="-37" />
<hkern u1="&#x111;" u2="&#x129;" k="-27" />
<hkern u1="&#x111;" u2="&#xef;" k="-27" />
<hkern u1="&#x111;" u2="&#xee;" k="-41" />
<hkern u1="&#x111;" u2="&#xec;" k="-16" />
<hkern u1="&#x111;" u2="&#xba;" k="-14" />
<hkern u1="&#x111;" u2="&#x7d;" k="-18" />
<hkern u1="&#x111;" u2="]" k="-14" />
<hkern u1="&#x111;" u2="&#x3f;" k="-8" />
<hkern u1="&#x111;" u2="&#x2a;" k="-35" />
<hkern u1="&#x111;" u2="&#x29;" k="-18" />
<hkern u1="&#x112;" u2="&#x135;" k="8" />
<hkern u1="&#x112;" u2="&#x129;" k="-27" />
<hkern u1="&#x112;" u2="&#xef;" k="-29" />
<hkern u1="&#x112;" u2="&#xee;" k="-43" />
<hkern u1="&#x112;" u2="&#xec;" k="-16" />
<hkern u1="&#x113;" u2="&#x135;" k="10" />
<hkern u1="&#x114;" u2="&#x135;" k="8" />
<hkern u1="&#x114;" u2="&#x129;" k="-27" />
<hkern u1="&#x114;" u2="&#xef;" k="-29" />
<hkern u1="&#x114;" u2="&#xee;" k="-43" />
<hkern u1="&#x114;" u2="&#xec;" k="-16" />
<hkern u1="&#x115;" u2="&#x135;" k="10" />
<hkern u1="&#x116;" u2="&#x135;" k="8" />
<hkern u1="&#x116;" u2="&#x129;" k="-27" />
<hkern u1="&#x116;" u2="&#xef;" k="-29" />
<hkern u1="&#x116;" u2="&#xee;" k="-43" />
<hkern u1="&#x116;" u2="&#xec;" k="-16" />
<hkern u1="&#x117;" u2="&#x135;" k="10" />
<hkern u1="&#x118;" u2="&#x135;" k="8" />
<hkern u1="&#x118;" u2="&#x129;" k="-27" />
<hkern u1="&#x118;" u2="&#xef;" k="-29" />
<hkern u1="&#x118;" u2="&#xee;" k="-43" />
<hkern u1="&#x118;" u2="&#xec;" k="-16" />
<hkern u1="&#x118;" u2="j" k="-76" />
<hkern u1="&#x119;" u2="&#x135;" k="10" />
<hkern u1="&#x11a;" u2="&#x135;" k="8" />
<hkern u1="&#x11a;" u2="&#x129;" k="-27" />
<hkern u1="&#x11a;" u2="&#xef;" k="-29" />
<hkern u1="&#x11a;" u2="&#xee;" k="-43" />
<hkern u1="&#x11a;" u2="&#xec;" k="-16" />
<hkern u1="&#x11b;" u2="&#x135;" k="10" />
<hkern u1="&#x11d;" u2="j" k="-29" />
<hkern u1="&#x11f;" u2="j" k="-29" />
<hkern u1="&#x121;" u2="j" k="-29" />
<hkern u1="&#x123;" u2="j" k="-29" />
<hkern u1="&#x124;" u2="&#x129;" k="-14" />
<hkern u1="&#x124;" u2="&#xef;" k="-16" />
<hkern u1="&#x124;" u2="&#xee;" k="-31" />
<hkern u1="&#x125;" u2="&#x135;" k="6" />
<hkern u1="&#x126;" u2="&#x129;" k="-14" />
<hkern u1="&#x126;" u2="&#xef;" k="-16" />
<hkern u1="&#x126;" u2="&#xee;" k="-31" />
<hkern u1="&#x127;" u2="&#x135;" k="6" />
<hkern u1="&#x128;" u2="&#x129;" k="-14" />
<hkern u1="&#x128;" u2="&#xef;" k="-16" />
<hkern u1="&#x128;" u2="&#xee;" k="-31" />
<hkern u1="&#x128;" u2="&#x7d;" k="2" />
<hkern u1="&#x128;" u2="&#x29;" k="2" />
<hkern u1="&#x129;" u2="&#x2122;" k="-76" />
<hkern u1="&#x129;" u2="&#x142;" k="-14" />
<hkern u1="&#x129;" u2="&#x140;" k="-14" />
<hkern u1="&#x129;" u2="&#x13e;" k="-14" />
<hkern u1="&#x129;" u2="&#x13c;" k="-14" />
<hkern u1="&#x129;" u2="&#x13a;" k="-14" />
<hkern u1="&#x129;" u2="&#x137;" k="-14" />
<hkern u1="&#x129;" u2="&#x133;" k="-18" />
<hkern u1="&#x129;" u2="&#x131;" k="-18" />
<hkern u1="&#x129;" u2="&#x12f;" k="-18" />
<hkern u1="&#x129;" u2="&#x12d;" k="-18" />
<hkern u1="&#x129;" u2="&#x12b;" k="-18" />
<hkern u1="&#x129;" u2="&#x129;" k="-18" />
<hkern u1="&#x129;" u2="&#x127;" k="-14" />
<hkern u1="&#x129;" u2="&#x125;" k="-14" />
<hkern u1="&#x129;" u2="&#xfe;" k="-14" />
<hkern u1="&#x129;" u2="&#xef;" k="-18" />
<hkern u1="&#x129;" u2="&#xee;" k="-18" />
<hkern u1="&#x129;" u2="&#xed;" k="-18" />
<hkern u1="&#x129;" u2="&#xec;" k="-18" />
<hkern u1="&#x129;" u2="&#xdf;" k="-14" />
<hkern u1="&#x129;" u2="&#xba;" k="-18" />
<hkern u1="&#x129;" u2="&#xaa;" k="-10" />
<hkern u1="&#x129;" u2="&#x7d;" k="-74" />
<hkern u1="&#x129;" u2="&#x7c;" k="-35" />
<hkern u1="&#x129;" u2="l" k="-14" />
<hkern u1="&#x129;" u2="k" k="-14" />
<hkern u1="&#x129;" u2="i" k="-18" />
<hkern u1="&#x129;" u2="h" k="-14" />
<hkern u1="&#x129;" u2="b" k="-14" />
<hkern u1="&#x129;" u2="]" k="-72" />
<hkern u1="&#x129;" u2="\" k="-76" />
<hkern u1="&#x129;" u2="&#x2a;" k="-12" />
<hkern u1="&#x129;" u2="&#x29;" k="-76" />
<hkern u1="&#x129;" u2="&#x27;" k="-55" />
<hkern u1="&#x129;" u2="&#x22;" k="-55" />
<hkern u1="&#x129;" u2="&#x21;" k="-37" />
<hkern u1="&#x12a;" u2="&#x129;" k="-14" />
<hkern u1="&#x12a;" u2="&#xef;" k="-16" />
<hkern u1="&#x12a;" u2="&#xee;" k="-31" />
<hkern u1="&#x12a;" u2="&#x7d;" k="-68" />
<hkern u1="&#x12a;" u2="&#x7c;" k="-20" />
<hkern u1="&#x12a;" u2="]" k="-63" />
<hkern u1="&#x12a;" u2="\" k="-78" />
<hkern u1="&#x12a;" u2="&#x29;" k="-66" />
<hkern u1="&#x12b;" u2="&#x2122;" k="-68" />
<hkern u1="&#x12b;" u2="&#x129;" k="-20" />
<hkern u1="&#x12b;" u2="&#xef;" k="-23" />
<hkern u1="&#x12b;" u2="&#xee;" k="-35" />
<hkern u1="&#x12b;" u2="&#xec;" k="-76" />
<hkern u1="&#x12b;" u2="&#xba;" k="-43" />
<hkern u1="&#x12b;" u2="&#xaa;" k="-27" />
<hkern u1="&#x12b;" u2="&#x7d;" k="-51" />
<hkern u1="&#x12b;" u2="&#x7c;" k="-12" />
<hkern u1="&#x12b;" u2="]" k="-49" />
<hkern u1="&#x12b;" u2="\" k="-39" />
<hkern u1="&#x12b;" u2="&#x3f;" k="-10" />
<hkern u1="&#x12b;" u2="&#x2a;" k="-39" />
<hkern u1="&#x12b;" u2="&#x29;" k="-51" />
<hkern u1="&#x12b;" u2="&#x27;" k="-43" />
<hkern u1="&#x12b;" u2="&#x22;" k="-43" />
<hkern u1="&#x12b;" u2="&#x21;" k="-31" />
<hkern u1="&#x12c;" u2="&#x129;" k="-14" />
<hkern u1="&#x12c;" u2="&#xef;" k="-16" />
<hkern u1="&#x12c;" u2="&#xee;" k="-31" />
<hkern u1="&#x12d;" u2="&#x2122;" k="-20" />
<hkern u1="&#x12d;" u2="&#x129;" k="-20" />
<hkern u1="&#x12d;" u2="&#xef;" k="-23" />
<hkern u1="&#x12d;" u2="&#xee;" k="-35" />
<hkern u1="&#x12d;" u2="&#xec;" k="-76" />
<hkern u1="&#x12d;" u2="&#x7d;" k="-31" />
<hkern u1="&#x12d;" u2="]" k="-29" />
<hkern u1="&#x12d;" u2="\" k="-33" />
<hkern u1="&#x12d;" u2="&#x29;" k="-31" />
<hkern u1="&#x12d;" u2="&#x27;" k="-10" />
<hkern u1="&#x12d;" u2="&#x22;" k="-10" />
<hkern u1="&#x12e;" u2="&#x129;" k="-14" />
<hkern u1="&#x12e;" u2="&#xef;" k="-16" />
<hkern u1="&#x12e;" u2="&#xee;" k="-31" />
<hkern u1="&#x12e;" u2="j" k="-55" />
<hkern u1="&#x12f;" u2="&#x129;" k="-20" />
<hkern u1="&#x12f;" u2="&#xef;" k="-23" />
<hkern u1="&#x12f;" u2="&#xee;" k="-35" />
<hkern u1="&#x12f;" u2="&#xec;" k="-76" />
<hkern u1="&#x12f;" u2="j" k="-66" />
<hkern u1="&#x130;" u2="&#x129;" k="-14" />
<hkern u1="&#x130;" u2="&#xef;" k="-16" />
<hkern u1="&#x130;" u2="&#xee;" k="-31" />
<hkern u1="&#x131;" u2="&#x129;" k="-20" />
<hkern u1="&#x131;" u2="&#xef;" k="-23" />
<hkern u1="&#x131;" u2="&#xee;" k="-35" />
<hkern u1="&#x131;" u2="&#xec;" k="-76" />
<hkern u1="&#x132;" u2="&#x7d;" k="8" />
<hkern u1="&#x132;" u2="]" k="8" />
<hkern u1="&#x132;" u2="X" k="10" />
<hkern u1="&#x132;" u2="&#x2f;" k="53" />
<hkern u1="&#x132;" u2="&#x29;" k="8" />
<hkern u1="&#x133;" u2="&#x129;" k="-20" />
<hkern u1="&#x133;" u2="&#xef;" k="-23" />
<hkern u1="&#x133;" u2="&#xee;" k="-35" />
<hkern u1="&#x133;" u2="&#xec;" k="-76" />
<hkern u1="&#x133;" u2="j" k="-25" />
<hkern u1="&#x134;" u2="&#x129;" k="-23" />
<hkern u1="&#x134;" u2="&#xef;" k="-25" />
<hkern u1="&#x134;" u2="&#xee;" k="-39" />
<hkern u1="&#x134;" u2="&#xec;" k="-12" />
<hkern u1="&#x135;" u2="&#x129;" k="-20" />
<hkern u1="&#x135;" u2="&#xef;" k="-23" />
<hkern u1="&#x135;" u2="&#xee;" k="-35" />
<hkern u1="&#x135;" u2="&#xec;" k="-76" />
<hkern u1="&#x135;" u2="j" k="-10" />
<hkern u1="&#x136;" u2="&#x135;" k="12" />
<hkern u1="&#x136;" u2="&#x12d;" k="-10" />
<hkern u1="&#x136;" u2="&#x12b;" k="-39" />
<hkern u1="&#x136;" u2="&#x129;" k="-51" />
<hkern u1="&#x136;" u2="&#xef;" k="-66" />
<hkern u1="&#x136;" u2="&#xee;" k="-41" />
<hkern u1="&#x136;" u2="&#xec;" k="-70" />
<hkern u1="&#x139;" u2="&#x135;" k="18" />
<hkern u1="&#x13a;" u2="&#x129;" k="-27" />
<hkern u1="&#x13a;" u2="&#xef;" k="-27" />
<hkern u1="&#x13a;" u2="&#xee;" k="-41" />
<hkern u1="&#x13a;" u2="&#xec;" k="-16" />
<hkern u1="&#x13a;" u2="&#x7c;" k="-102" />
<hkern u1="&#x13b;" u2="&#x135;" k="18" />
<hkern u1="&#x13c;" u2="&#x129;" k="-27" />
<hkern u1="&#x13c;" u2="&#xef;" k="-27" />
<hkern u1="&#x13c;" u2="&#xee;" k="-41" />
<hkern u1="&#x13c;" u2="&#xec;" k="-16" />
<hkern u1="&#x13d;" u2="&#x2122;" k="86" />
<hkern u1="&#x13d;" u2="&#x201d;" k="162" />
<hkern u1="&#x13d;" u2="&#x2019;" k="162" />
<hkern u1="&#x13d;" u2="&#x1ef2;" k="76" />
<hkern u1="&#x13d;" u2="&#x1e84;" k="61" />
<hkern u1="&#x13d;" u2="&#x1e82;" k="61" />
<hkern u1="&#x13d;" u2="&#x1e80;" k="61" />
<hkern u1="&#x13d;" u2="&#x21a;" k="104" />
<hkern u1="&#x13d;" u2="&#x178;" k="76" />
<hkern u1="&#x13d;" u2="&#x176;" k="76" />
<hkern u1="&#x13d;" u2="&#x174;" k="61" />
<hkern u1="&#x13d;" u2="&#x166;" k="104" />
<hkern u1="&#x13d;" u2="&#x164;" k="104" />
<hkern u1="&#x13d;" u2="&#x135;" k="18" />
<hkern u1="&#x13d;" u2="&#xdd;" k="76" />
<hkern u1="&#x13d;" u2="&#xba;" k="147" />
<hkern u1="&#x13d;" u2="&#xaa;" k="158" />
<hkern u1="&#x13d;" u2="\" k="117" />
<hkern u1="&#x13d;" u2="Y" k="76" />
<hkern u1="&#x13d;" u2="W" k="61" />
<hkern u1="&#x13d;" u2="V" k="82" />
<hkern u1="&#x13d;" u2="T" k="104" />
<hkern u1="&#x13d;" u2="&#x2a;" k="141" />
<hkern u1="&#x13d;" u2="&#x27;" k="106" />
<hkern u1="&#x13d;" u2="&#x22;" k="106" />
<hkern u1="&#x13e;" u2="&#x161;" k="-16" />
<hkern u1="&#x13e;" u2="&#x10d;" k="12" />
<hkern u1="&#x13e;" u2="&#xf4;" k="2" />
<hkern u1="&#x13e;" u2="&#x3b;" k="-8" />
<hkern u1="&#x13f;" u2="&#x135;" k="18" />
<hkern u1="&#x141;" u2="&#x135;" k="18" />
<hkern u1="&#x142;" u2="&#x129;" k="-27" />
<hkern u1="&#x142;" u2="&#xef;" k="-27" />
<hkern u1="&#x142;" u2="&#xee;" k="-41" />
<hkern u1="&#x142;" u2="&#xec;" k="-16" />
<hkern u1="&#x143;" u2="&#x129;" k="-14" />
<hkern u1="&#x143;" u2="&#xef;" k="-16" />
<hkern u1="&#x143;" u2="&#xee;" k="-31" />
<hkern u1="&#x144;" u2="&#x135;" k="6" />
<hkern u1="&#x145;" u2="&#x129;" k="-14" />
<hkern u1="&#x145;" u2="&#xef;" k="-16" />
<hkern u1="&#x145;" u2="&#xee;" k="-31" />
<hkern u1="&#x146;" u2="&#x135;" k="6" />
<hkern u1="&#x147;" u2="&#x129;" k="-14" />
<hkern u1="&#x147;" u2="&#xef;" k="-16" />
<hkern u1="&#x147;" u2="&#xee;" k="-31" />
<hkern u1="&#x148;" u2="&#x135;" k="6" />
<hkern u1="&#x149;" u2="&#x135;" k="6" />
<hkern u1="&#x14a;" u2="&#x129;" k="-14" />
<hkern u1="&#x14a;" u2="&#xef;" k="-16" />
<hkern u1="&#x14a;" u2="&#xee;" k="-31" />
<hkern u1="&#x14b;" u2="&#x135;" k="6" />
<hkern u1="&#x14d;" u2="&#x135;" k="16" />
<hkern u1="&#x14f;" u2="&#x135;" k="16" />
<hkern u1="&#x151;" u2="&#x2122;" k="61" />
<hkern u1="&#x151;" u2="&#x135;" k="16" />
<hkern u1="&#x151;" u2="&#x7d;" k="25" />
<hkern u1="&#x151;" u2="]" k="23" />
<hkern u1="&#x151;" u2="\" k="29" />
<hkern u1="&#x151;" u2="&#x29;" k="31" />
<hkern u1="&#x152;" u2="&#x135;" k="8" />
<hkern u1="&#x152;" u2="&#x129;" k="-27" />
<hkern u1="&#x152;" u2="&#xef;" k="-29" />
<hkern u1="&#x152;" u2="&#xee;" k="-43" />
<hkern u1="&#x152;" u2="&#xec;" k="-16" />
<hkern u1="&#x153;" u2="&#x135;" k="10" />
<hkern u1="&#x155;" u2="&#x7d;" k="-16" />
<hkern u1="&#x155;" u2="]" k="-12" />
<hkern u1="&#x155;" u2="\" k="-25" />
<hkern u1="&#x155;" u2="&#x29;" k="-18" />
<hkern u1="&#x159;" u2="&#x7d;" k="35" />
<hkern u1="&#x159;" u2="]" k="37" />
<hkern u1="&#x159;" u2="\" k="20" />
<hkern u1="&#x159;" u2="&#x29;" k="33" />
<hkern u1="&#x15b;" u2="&#x135;" k="6" />
<hkern u1="&#x15b;" u2="\" k="92" />
<hkern u1="&#x15d;" u2="&#x135;" k="6" />
<hkern u1="&#x15f;" u2="&#x135;" k="6" />
<hkern u1="&#x161;" u2="&#x135;" k="6" />
<hkern u1="&#x164;" u2="&#x159;" k="41" />
<hkern u1="&#x164;" u2="&#x135;" k="23" />
<hkern u1="&#x164;" u2="&#x131;" k="100" />
<hkern u1="&#x164;" u2="&#x12d;" k="-10" />
<hkern u1="&#x164;" u2="&#x12b;" k="-49" />
<hkern u1="&#x164;" u2="&#x129;" k="-66" />
<hkern u1="&#x164;" u2="&#x127;" k="-18" />
<hkern u1="&#x164;" u2="&#xef;" k="-72" />
<hkern u1="&#x164;" u2="&#xee;" k="-80" />
<hkern u1="&#x164;" u2="&#xed;" k="25" />
<hkern u1="&#x164;" u2="&#xec;" k="-63" />
<hkern u1="&#x164;" u2="&#xdf;" k="63" />
<hkern u1="&#x165;" u2="&#x2122;" k="-244" />
<hkern u1="&#x165;" u2="&#x17f;" k="-57" />
<hkern u1="&#x165;" u2="&#x161;" k="-59" />
<hkern u1="&#x165;" u2="&#x11b;" k="-18" />
<hkern u1="&#x165;" u2="&#x10d;" k="-14" />
<hkern u1="&#x165;" u2="&#xed;" k="-104" />
<hkern u1="&#x165;" u2="&#xe4;" k="-61" />
<hkern u1="&#x165;" u2="&#xba;" k="-152" />
<hkern u1="&#x165;" u2="&#xaa;" k="-139" />
<hkern u1="&#x165;" u2="&#x7d;" k="-217" />
<hkern u1="&#x165;" u2="&#x7c;" k="-158" />
<hkern u1="&#x165;" u2="x" k="-55" />
<hkern u1="&#x165;" u2="v" k="-72" />
<hkern u1="&#x165;" u2="j" k="-160" />
<hkern u1="&#x165;" u2="]" k="-215" />
<hkern u1="&#x165;" u2="\" k="-182" />
<hkern u1="&#x165;" u2="&#x3f;" k="-135" />
<hkern u1="&#x165;" u2="&#x3b;" k="-33" />
<hkern u1="&#x165;" u2="&#x2f;" k="53" />
<hkern u1="&#x165;" u2="&#x2a;" k="-174" />
<hkern u1="&#x165;" u2="&#x29;" k="-217" />
<hkern u1="&#x165;" u2="&#x21;" k="-180" />
<hkern u1="&#x165;" u2="&#x20;" k="-27" />
<hkern u1="&#x166;" g2="uniFB02" k="33" />
<hkern u1="&#x166;" g2="uniFB01" k="33" />
<hkern u1="&#x166;" u2="&#x2039;" k="63" />
<hkern u1="&#x166;" u2="&#x2014;" k="90" />
<hkern u1="&#x166;" u2="&#x2013;" k="90" />
<hkern u1="&#x166;" u2="&#x1ef3;" k="53" />
<hkern u1="&#x166;" u2="&#x1e85;" k="45" />
<hkern u1="&#x166;" u2="&#x1e83;" k="45" />
<hkern u1="&#x166;" u2="&#x1e81;" k="45" />
<hkern u1="&#x166;" u2="&#x21b;" k="25" />
<hkern u1="&#x166;" u2="&#x219;" k="125" />
<hkern u1="&#x166;" u2="&#x1ff;" k="135" />
<hkern u1="&#x166;" u2="&#x1fd;" k="154" />
<hkern u1="&#x166;" u2="&#x17f;" k="33" />
<hkern u1="&#x166;" u2="&#x177;" k="53" />
<hkern u1="&#x166;" u2="&#x175;" k="45" />
<hkern u1="&#x166;" u2="&#x173;" k="68" />
<hkern u1="&#x166;" u2="&#x171;" k="68" />
<hkern u1="&#x166;" u2="&#x16f;" k="68" />
<hkern u1="&#x166;" u2="&#x16d;" k="68" />
<hkern u1="&#x166;" u2="&#x16b;" k="68" />
<hkern u1="&#x166;" u2="&#x169;" k="68" />
<hkern u1="&#x166;" u2="&#x167;" k="25" />
<hkern u1="&#x166;" u2="&#x165;" k="25" />
<hkern u1="&#x166;" u2="&#x161;" k="125" />
<hkern u1="&#x166;" u2="&#x15f;" k="125" />
<hkern u1="&#x166;" u2="&#x15d;" k="125" />
<hkern u1="&#x166;" u2="&#x15b;" k="125" />
<hkern u1="&#x166;" u2="&#x159;" k="74" />
<hkern u1="&#x166;" u2="&#x157;" k="74" />
<hkern u1="&#x166;" u2="&#x155;" k="74" />
<hkern u1="&#x166;" u2="&#x153;" k="135" />
<hkern u1="&#x166;" u2="&#x151;" k="135" />
<hkern u1="&#x166;" u2="&#x14f;" k="135" />
<hkern u1="&#x166;" u2="&#x14d;" k="135" />
<hkern u1="&#x166;" u2="&#x14b;" k="74" />
<hkern u1="&#x166;" u2="&#x149;" k="74" />
<hkern u1="&#x166;" u2="&#x148;" k="74" />
<hkern u1="&#x166;" u2="&#x146;" k="74" />
<hkern u1="&#x166;" u2="&#x144;" k="74" />
<hkern u1="&#x166;" u2="&#x142;" k="10" />
<hkern u1="&#x166;" u2="&#x140;" k="10" />
<hkern u1="&#x166;" u2="&#x13e;" k="10" />
<hkern u1="&#x166;" u2="&#x13c;" k="10" />
<hkern u1="&#x166;" u2="&#x13a;" k="10" />
<hkern u1="&#x166;" u2="&#x138;" k="74" />
<hkern u1="&#x166;" u2="&#x137;" k="10" />
<hkern u1="&#x166;" u2="&#x135;" k="23" />
<hkern u1="&#x166;" u2="&#x131;" k="100" />
<hkern u1="&#x166;" u2="&#x12d;" k="-10" />
<hkern u1="&#x166;" u2="&#x12b;" k="-49" />
<hkern u1="&#x166;" u2="&#x129;" k="-66" />
<hkern u1="&#x166;" u2="&#x127;" k="10" />
<hkern u1="&#x166;" u2="&#x125;" k="10" />
<hkern u1="&#x166;" u2="&#x123;" k="125" />
<hkern u1="&#x166;" u2="&#x121;" k="125" />
<hkern u1="&#x166;" u2="&#x11f;" k="125" />
<hkern u1="&#x166;" u2="&#x11d;" k="125" />
<hkern u1="&#x166;" u2="&#x11b;" k="135" />
<hkern u1="&#x166;" u2="&#x119;" k="135" />
<hkern u1="&#x166;" u2="&#x117;" k="135" />
<hkern u1="&#x166;" u2="&#x115;" k="135" />
<hkern u1="&#x166;" u2="&#x113;" k="135" />
<hkern u1="&#x166;" u2="&#x111;" k="129" />
<hkern u1="&#x166;" u2="&#x10f;" k="129" />
<hkern u1="&#x166;" u2="&#x10d;" k="135" />
<hkern u1="&#x166;" u2="&#x10b;" k="135" />
<hkern u1="&#x166;" u2="&#x109;" k="135" />
<hkern u1="&#x166;" u2="&#x107;" k="135" />
<hkern u1="&#x166;" u2="&#x105;" k="154" />
<hkern u1="&#x166;" u2="&#x103;" k="154" />
<hkern u1="&#x166;" u2="&#x101;" k="154" />
<hkern u1="&#x166;" u2="&#xff;" k="53" />
<hkern u1="&#x166;" u2="&#xfe;" k="10" />
<hkern u1="&#x166;" u2="&#xfd;" k="53" />
<hkern u1="&#x166;" u2="&#xfc;" k="68" />
<hkern u1="&#x166;" u2="&#xfb;" k="68" />
<hkern u1="&#x166;" u2="&#xfa;" k="68" />
<hkern u1="&#x166;" u2="&#xf9;" k="68" />
<hkern u1="&#x166;" u2="&#xf8;" k="135" />
<hkern u1="&#x166;" u2="&#xf6;" k="135" />
<hkern u1="&#x166;" u2="&#xf5;" k="135" />
<hkern u1="&#x166;" u2="&#xf4;" k="135" />
<hkern u1="&#x166;" u2="&#xf3;" k="135" />
<hkern u1="&#x166;" u2="&#xf2;" k="135" />
<hkern u1="&#x166;" u2="&#xf1;" k="74" />
<hkern u1="&#x166;" u2="&#xef;" k="-72" />
<hkern u1="&#x166;" u2="&#xee;" k="-80" />
<hkern u1="&#x166;" u2="&#xed;" k="25" />
<hkern u1="&#x166;" u2="&#xec;" k="-63" />
<hkern u1="&#x166;" u2="&#xeb;" k="135" />
<hkern u1="&#x166;" u2="&#xea;" k="135" />
<hkern u1="&#x166;" u2="&#xe9;" k="135" />
<hkern u1="&#x166;" u2="&#xe8;" k="135" />
<hkern u1="&#x166;" u2="&#xe7;" k="135" />
<hkern u1="&#x166;" u2="&#xe6;" k="154" />
<hkern u1="&#x166;" u2="&#xe5;" k="154" />
<hkern u1="&#x166;" u2="&#xe4;" k="154" />
<hkern u1="&#x166;" u2="&#xe3;" k="154" />
<hkern u1="&#x166;" u2="&#xe2;" k="154" />
<hkern u1="&#x166;" u2="&#xe1;" k="154" />
<hkern u1="&#x166;" u2="&#xe0;" k="154" />
<hkern u1="&#x166;" u2="&#xdf;" k="10" />
<hkern u1="&#x166;" u2="&#xae;" k="14" />
<hkern u1="&#x166;" u2="&#xab;" k="63" />
<hkern u1="&#x166;" u2="&#xa9;" k="14" />
<hkern u1="&#x166;" u2="y" k="53" />
<hkern u1="&#x166;" u2="x" k="53" />
<hkern u1="&#x166;" u2="w" k="45" />
<hkern u1="&#x166;" u2="v" k="63" />
<hkern u1="&#x166;" u2="u" k="68" />
<hkern u1="&#x166;" u2="t" k="25" />
<hkern u1="&#x166;" u2="s" k="125" />
<hkern u1="&#x166;" u2="r" k="74" />
<hkern u1="&#x166;" u2="q" k="86" />
<hkern u1="&#x166;" u2="p" k="74" />
<hkern u1="&#x166;" u2="o" k="135" />
<hkern u1="&#x166;" u2="n" k="74" />
<hkern u1="&#x166;" u2="m" k="74" />
<hkern u1="&#x166;" u2="l" k="10" />
<hkern u1="&#x166;" u2="k" k="10" />
<hkern u1="&#x166;" u2="j" k="18" />
<hkern u1="&#x166;" u2="h" k="10" />
<hkern u1="&#x166;" u2="g" k="125" />
<hkern u1="&#x166;" u2="f" k="33" />
<hkern u1="&#x166;" u2="e" k="135" />
<hkern u1="&#x166;" u2="d" k="129" />
<hkern u1="&#x166;" u2="c" k="135" />
<hkern u1="&#x166;" u2="b" k="10" />
<hkern u1="&#x166;" u2="a" k="154" />
<hkern u1="&#x166;" u2="&#x3b;" k="27" />
<hkern u1="&#x166;" u2="&#x3a;" k="27" />
<hkern u1="&#x166;" u2="&#x2d;" k="90" />
<hkern u1="&#x168;" u2="&#x129;" k="-23" />
<hkern u1="&#x168;" u2="&#xef;" k="-25" />
<hkern u1="&#x168;" u2="&#xee;" k="-39" />
<hkern u1="&#x168;" u2="&#xec;" k="-12" />
<hkern u1="&#x16a;" u2="&#x129;" k="-23" />
<hkern u1="&#x16a;" u2="&#xef;" k="-25" />
<hkern u1="&#x16a;" u2="&#xee;" k="-39" />
<hkern u1="&#x16a;" u2="&#xec;" k="-12" />
<hkern u1="&#x16c;" u2="&#x129;" k="-23" />
<hkern u1="&#x16c;" u2="&#xef;" k="-25" />
<hkern u1="&#x16c;" u2="&#xee;" k="-39" />
<hkern u1="&#x16c;" u2="&#xec;" k="-12" />
<hkern u1="&#x16e;" u2="&#x129;" k="-23" />
<hkern u1="&#x16e;" u2="&#xef;" k="-25" />
<hkern u1="&#x16e;" u2="&#xee;" k="-39" />
<hkern u1="&#x16e;" u2="&#xec;" k="-12" />
<hkern u1="&#x170;" u2="&#x129;" k="-23" />
<hkern u1="&#x170;" u2="&#xef;" k="-25" />
<hkern u1="&#x170;" u2="&#xee;" k="-39" />
<hkern u1="&#x170;" u2="&#xec;" k="-12" />
<hkern u1="&#x171;" u2="\" k="39" />
<hkern u1="&#x172;" u2="&#x129;" k="-23" />
<hkern u1="&#x172;" u2="&#xef;" k="-25" />
<hkern u1="&#x172;" u2="&#xee;" k="-39" />
<hkern u1="&#x172;" u2="&#xec;" k="-12" />
<hkern u1="&#x173;" u2="j" k="-66" />
<hkern u1="&#x174;" u2="&#x159;" k="4" />
<hkern u1="&#x174;" u2="&#x131;" k="20" />
<hkern u1="&#x174;" u2="&#x12b;" k="-35" />
<hkern u1="&#x174;" u2="&#x129;" k="-47" />
<hkern u1="&#x174;" u2="&#xef;" k="-59" />
<hkern u1="&#x174;" u2="&#xee;" k="-61" />
<hkern u1="&#x174;" u2="&#xec;" k="-43" />
<hkern u1="&#x174;" u2="&#xdf;" k="10" />
<hkern u1="&#x176;" u2="&#x15d;" k="117" />
<hkern u1="&#x176;" u2="&#x159;" k="43" />
<hkern u1="&#x176;" u2="&#x135;" k="55" />
<hkern u1="&#x176;" u2="&#x131;" k="137" />
<hkern u1="&#x176;" u2="&#x12d;" k="-31" />
<hkern u1="&#x176;" u2="&#x12b;" k="-61" />
<hkern u1="&#x176;" u2="&#x129;" k="-72" />
<hkern u1="&#x176;" u2="&#x127;" k="-8" />
<hkern u1="&#x176;" u2="&#xef;" k="-88" />
<hkern u1="&#x176;" u2="&#xee;" k="-63" />
<hkern u1="&#x176;" u2="&#xed;" k="53" />
<hkern u1="&#x176;" u2="&#xec;" k="-80" />
<hkern u1="&#x176;" u2="&#xe4;" k="117" />
<hkern u1="&#x176;" u2="&#xe0;" k="121" />
<hkern u1="&#x176;" u2="&#xdf;" k="82" />
<hkern u1="&#x178;" u2="&#x15d;" k="117" />
<hkern u1="&#x178;" u2="&#x159;" k="43" />
<hkern u1="&#x178;" u2="&#x135;" k="55" />
<hkern u1="&#x178;" u2="&#x131;" k="137" />
<hkern u1="&#x178;" u2="&#x12d;" k="-31" />
<hkern u1="&#x178;" u2="&#x12b;" k="-61" />
<hkern u1="&#x178;" u2="&#x129;" k="-72" />
<hkern u1="&#x178;" u2="&#x127;" k="-8" />
<hkern u1="&#x178;" u2="&#xef;" k="-88" />
<hkern u1="&#x178;" u2="&#xee;" k="-63" />
<hkern u1="&#x178;" u2="&#xed;" k="53" />
<hkern u1="&#x178;" u2="&#xec;" k="-80" />
<hkern u1="&#x178;" u2="&#xe4;" k="117" />
<hkern u1="&#x178;" u2="&#xe0;" k="121" />
<hkern u1="&#x178;" u2="&#xdf;" k="82" />
<hkern u1="&#x179;" u2="&#x135;" k="8" />
<hkern u1="&#x179;" u2="&#x129;" k="-27" />
<hkern u1="&#x179;" u2="&#xef;" k="-27" />
<hkern u1="&#x179;" u2="&#xee;" k="-43" />
<hkern u1="&#x179;" u2="&#xec;" k="-14" />
<hkern u1="&#x17a;" u2="\" k="55" />
<hkern u1="&#x17b;" u2="&#x135;" k="8" />
<hkern u1="&#x17b;" u2="&#x129;" k="-27" />
<hkern u1="&#x17b;" u2="&#xef;" k="-27" />
<hkern u1="&#x17b;" u2="&#xee;" k="-43" />
<hkern u1="&#x17b;" u2="&#xec;" k="-14" />
<hkern u1="&#x17d;" u2="&#x135;" k="8" />
<hkern u1="&#x17d;" u2="&#x129;" k="-27" />
<hkern u1="&#x17d;" u2="&#xef;" k="-27" />
<hkern u1="&#x17d;" u2="&#xee;" k="-43" />
<hkern u1="&#x17d;" u2="&#xec;" k="-14" />
<hkern u1="&#x17f;" u2="&#x177;" k="-29" />
<hkern u1="&#x17f;" u2="&#x159;" k="-115" />
<hkern u1="&#x17f;" u2="&#x151;" k="-23" />
<hkern u1="&#x17f;" u2="&#x149;" k="-63" />
<hkern u1="&#x17f;" u2="&#x131;" k="-2" />
<hkern u1="&#x17f;" u2="&#x12f;" k="-117" />
<hkern u1="&#x17f;" u2="&#x12d;" k="-260" />
<hkern u1="&#x17f;" u2="&#x12b;" k="-270" />
<hkern u1="&#x17f;" u2="&#x129;" k="-272" />
<hkern u1="&#x17f;" u2="&#x11d;" k="-23" />
<hkern u1="&#x17f;" u2="&#x11b;" k="-18" />
<hkern u1="&#x17f;" u2="&#x10d;" k="-14" />
<hkern u1="&#x17f;" u2="&#x109;" k="-37" />
<hkern u1="&#x17f;" u2="&#xff;" k="-14" />
<hkern u1="&#x17f;" u2="&#xfb;" k="-10" />
<hkern u1="&#x17f;" u2="&#xf9;" k="-47" />
<hkern u1="&#x17f;" u2="&#xf6;" k="-14" />
<hkern u1="&#x17f;" u2="&#xf5;" k="-14" />
<hkern u1="&#x17f;" u2="&#xf4;" k="-29" />
<hkern u1="&#x17f;" u2="&#xf2;" k="-59" />
<hkern u1="&#x17f;" u2="&#xef;" k="-311" />
<hkern u1="&#x17f;" u2="&#xee;" k="-287" />
<hkern u1="&#x17f;" u2="&#xed;" k="-88" />
<hkern u1="&#x17f;" u2="&#xec;" k="-373" />
<hkern u1="&#x17f;" u2="&#xeb;" k="-27" />
<hkern u1="&#x17f;" u2="&#xea;" k="-41" />
<hkern u1="&#x17f;" u2="&#xe8;" k="-70" />
<hkern u1="&#x17f;" u2="&#xe4;" k="-31" />
<hkern u1="&#x17f;" u2="&#xe3;" k="-31" />
<hkern u1="&#x17f;" u2="&#xe2;" k="-45" />
<hkern u1="&#x17f;" u2="&#xe0;" k="-76" />
<hkern u1="&#x17f;" u2="j" k="-109" />
<hkern u1="&#x1fc;" u2="&#x135;" k="8" />
<hkern u1="&#x1fc;" u2="&#x129;" k="-27" />
<hkern u1="&#x1fc;" u2="&#xef;" k="-29" />
<hkern u1="&#x1fc;" u2="&#xee;" k="-43" />
<hkern u1="&#x1fc;" u2="&#xec;" k="-16" />
<hkern u1="&#x1fd;" u2="&#x135;" k="10" />
<hkern u1="&#x1fe;" u2="&#x2a;" k="-18" />
<hkern u1="&#x1ff;" u2="&#x135;" k="16" />
<hkern u1="&#x219;" u2="&#x135;" k="6" />
<hkern u1="&#x21a;" u2="&#x159;" k="41" />
<hkern u1="&#x21a;" u2="&#x135;" k="23" />
<hkern u1="&#x21a;" u2="&#x131;" k="100" />
<hkern u1="&#x21a;" u2="&#x12d;" k="-10" />
<hkern u1="&#x21a;" u2="&#x12b;" k="-49" />
<hkern u1="&#x21a;" u2="&#x129;" k="-66" />
<hkern u1="&#x21a;" u2="&#x127;" k="-18" />
<hkern u1="&#x21a;" u2="&#xef;" k="-72" />
<hkern u1="&#x21a;" u2="&#xee;" k="-80" />
<hkern u1="&#x21a;" u2="&#xed;" k="25" />
<hkern u1="&#x21a;" u2="&#xec;" k="-63" />
<hkern u1="&#x21a;" u2="&#xdf;" k="63" />
<hkern u1="&#x1e80;" u2="&#x159;" k="4" />
<hkern u1="&#x1e80;" u2="&#x131;" k="20" />
<hkern u1="&#x1e80;" u2="&#x12b;" k="-35" />
<hkern u1="&#x1e80;" u2="&#x129;" k="-47" />
<hkern u1="&#x1e80;" u2="&#xef;" k="-59" />
<hkern u1="&#x1e80;" u2="&#xee;" k="-61" />
<hkern u1="&#x1e80;" u2="&#xec;" k="-43" />
<hkern u1="&#x1e80;" u2="&#xdf;" k="10" />
<hkern u1="&#x1e82;" u2="&#x159;" k="4" />
<hkern u1="&#x1e82;" u2="&#x131;" k="20" />
<hkern u1="&#x1e82;" u2="&#x12b;" k="-35" />
<hkern u1="&#x1e82;" u2="&#x129;" k="-47" />
<hkern u1="&#x1e82;" u2="&#xef;" k="-59" />
<hkern u1="&#x1e82;" u2="&#xee;" k="-61" />
<hkern u1="&#x1e82;" u2="&#xec;" k="-43" />
<hkern u1="&#x1e82;" u2="&#xdf;" k="10" />
<hkern u1="&#x1e84;" u2="&#x159;" k="4" />
<hkern u1="&#x1e84;" u2="&#x131;" k="20" />
<hkern u1="&#x1e84;" u2="&#x12b;" k="-35" />
<hkern u1="&#x1e84;" u2="&#x129;" k="-47" />
<hkern u1="&#x1e84;" u2="&#xef;" k="-59" />
<hkern u1="&#x1e84;" u2="&#xee;" k="-61" />
<hkern u1="&#x1e84;" u2="&#xec;" k="-43" />
<hkern u1="&#x1e84;" u2="&#xdf;" k="10" />
<hkern u1="&#x1ef2;" u2="&#x15d;" k="117" />
<hkern u1="&#x1ef2;" u2="&#x159;" k="43" />
<hkern u1="&#x1ef2;" u2="&#x135;" k="55" />
<hkern u1="&#x1ef2;" u2="&#x131;" k="137" />
<hkern u1="&#x1ef2;" u2="&#x12d;" k="-31" />
<hkern u1="&#x1ef2;" u2="&#x12b;" k="-61" />
<hkern u1="&#x1ef2;" u2="&#x129;" k="-72" />
<hkern u1="&#x1ef2;" u2="&#x127;" k="-8" />
<hkern u1="&#x1ef2;" u2="&#xef;" k="-88" />
<hkern u1="&#x1ef2;" u2="&#xee;" k="-63" />
<hkern u1="&#x1ef2;" u2="&#xed;" k="53" />
<hkern u1="&#x1ef2;" u2="&#xec;" k="-80" />
<hkern u1="&#x1ef2;" u2="&#xe4;" k="117" />
<hkern u1="&#x1ef2;" u2="&#xe0;" k="121" />
<hkern u1="&#x1ef2;" u2="&#xdf;" k="82" />
<hkern u1="&#x2013;" u2="&#x166;" k="90" />
<hkern u1="&#x2013;" u2="&#x135;" k="14" />
<hkern u1="&#x2014;" u2="&#x166;" k="90" />
<hkern u1="&#x2014;" u2="&#x135;" k="14" />
<hkern u1="&#x2018;" u2="&#x12d;" k="-23" />
<hkern u1="&#x2018;" u2="&#x12b;" k="-51" />
<hkern u1="&#x2018;" u2="&#x129;" k="-63" />
<hkern u1="&#x2018;" u2="&#xef;" k="-76" />
<hkern u1="&#x2018;" u2="&#xee;" k="-76" />
<hkern u1="&#x2018;" u2="&#xec;" k="-86" />
<hkern u1="&#x2019;" u2="&#x135;" k="14" />
<hkern u1="&#x2019;" u2="&#x12d;" k="-47" />
<hkern u1="&#x2019;" u2="&#x12b;" k="-76" />
<hkern u1="&#x2019;" u2="&#x129;" k="-82" />
<hkern u1="&#x2019;" u2="&#x127;" k="-8" />
<hkern u1="&#x2019;" u2="&#xef;" k="-100" />
<hkern u1="&#x2019;" u2="&#xee;" k="-80" />
<hkern u1="&#x2019;" u2="&#xec;" k="-119" />
<hkern u1="&#x201a;" u2="&#x166;" k="117" />
<hkern u1="&#x201a;" u2="&#x135;" k="16" />
<hkern u1="&#x201c;" u2="&#x12d;" k="-23" />
<hkern u1="&#x201c;" u2="&#x12b;" k="-51" />
<hkern u1="&#x201c;" u2="&#x129;" k="-63" />
<hkern u1="&#x201c;" u2="&#xef;" k="-76" />
<hkern u1="&#x201c;" u2="&#xee;" k="-76" />
<hkern u1="&#x201c;" u2="&#xec;" k="-86" />
<hkern u1="&#x201d;" u2="&#x135;" k="14" />
<hkern u1="&#x201d;" u2="&#x12d;" k="-47" />
<hkern u1="&#x201d;" u2="&#x12b;" k="-76" />
<hkern u1="&#x201d;" u2="&#x129;" k="-82" />
<hkern u1="&#x201d;" u2="&#x127;" k="-8" />
<hkern u1="&#x201d;" u2="&#xef;" k="-100" />
<hkern u1="&#x201d;" u2="&#xee;" k="-80" />
<hkern u1="&#x201d;" u2="&#xec;" k="-119" />
<hkern u1="&#x201e;" u2="&#x166;" k="117" />
<hkern u1="&#x201e;" u2="&#x135;" k="16" />
<hkern u1="&#x2026;" u2="&#x135;" k="16" />
<hkern u1="&#x2039;" u2="&#x129;" k="-27" />
<hkern u1="&#x2039;" u2="&#xef;" k="-27" />
<hkern u1="&#x2039;" u2="&#xee;" k="-43" />
<hkern u1="&#x203a;" u2="&#x166;" k="66" />
<hkern g1="uniFB01" u2="&#x129;" k="-20" />
<hkern g1="uniFB01" u2="&#xef;" k="-23" />
<hkern g1="uniFB01" u2="&#xee;" k="-35" />
<hkern g1="uniFB01" u2="&#xec;" k="-76" />
<hkern g1="uniFB02" u2="&#x129;" k="-27" />
<hkern g1="uniFB02" u2="&#xef;" k="-27" />
<hkern g1="uniFB02" u2="&#xee;" k="-41" />
<hkern g1="uniFB02" u2="&#xec;" k="-74" />
<hkern g1="colon,semicolon" 	g2="quoteright,quotedblright" 	k="12" />
<hkern g1="colon,semicolon" 	g2="quotedbl,quotesingle" 	k="6" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="43" />
<hkern g1="hyphen,endash,emdash" 	g2="quoteright,quotedblright" 	k="53" />
<hkern g1="hyphen,endash,emdash" 	g2="quotedbl,quotesingle" 	k="70" />
<hkern g1="hyphen,endash,emdash" 	g2="one" 	k="53" />
<hkern g1="hyphen,endash,emdash" 	g2="seven" 	k="98" />
<hkern g1="hyphen,endash,emdash" 	g2="two" 	k="51" />
<hkern g1="hyphen,endash,emdash" 	g2="five" 	k="8" />
<hkern g1="hyphen,endash,emdash" 	g2="nine" 	k="10" />
<hkern g1="hyphen,endash,emdash" 	g2="three" 	k="18" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="184" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="195" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="one" 	k="133" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="seven" 	k="63" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="nine" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="eight" 	k="8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="four" 	k="12" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="182" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="hyphen,endash,emdash" 	k="43" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="six" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="zero" 	k="12" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="201" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,endash,emdash" 	k="102" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="213" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="74" />
<hkern g1="quoteright,quotedblright" 	g2="ampersand" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="at" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="copyright" 	k="35" />
<hkern g1="quoteright,quotedblright" 	g2="registered" 	k="35" />
<hkern g1="quoteright,quotedblright" 	g2="slash" 	k="141" />
<hkern g1="quoteright,quotedblright" 	g2="colon,semicolon" 	k="18" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="80" />
<hkern g1="quotedbl,quotesingle" 	g2="hyphen,endash,emdash" 	k="70" />
<hkern g1="quotedbl,quotesingle" 	g2="six" 	k="55" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="195" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="43" />
<hkern g1="quotedbl,quotesingle" 	g2="ampersand" 	k="37" />
<hkern g1="quotedbl,quotesingle" 	g2="at" 	k="12" />
<hkern g1="quotedbl,quotesingle" 	g2="copyright" 	k="8" />
<hkern g1="quotedbl,quotesingle" 	g2="registered" 	k="8" />
<hkern g1="quotedbl,quotesingle" 	g2="slash" 	k="129" />
<hkern g1="quotedbl,quotesingle" 	g2="colon,semicolon" 	k="6" />
<hkern g1="backslash" 	g2="quoteright,quotedblright" 	k="121" />
<hkern g1="backslash" 	g2="quotedbl,quotesingle" 	k="129" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle" 	k="53" />
<hkern g1="ampersand" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="at" 	g2="quotedbl,quotesingle" 	k="8" />
<hkern g1="at" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="copyright" 	g2="quotedbl,quotesingle" 	k="8" />
<hkern g1="copyright" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="registered" 	g2="quotedbl,quotesingle" 	k="8" />
<hkern g1="registered" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="eight" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="8" />
<hkern g1="five" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="8" />
<hkern g1="four" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="12" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="51" />
<hkern g1="nine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="nine" 	g2="hyphen,endash,emdash" 	k="12" />
<hkern g1="seven" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="115" />
<hkern g1="seven" 	g2="hyphen,endash,emdash" 	k="55" />
<hkern g1="six" 	g2="quotedbl,quotesingle" 	k="53" />
<hkern g1="two" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="zero" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="J,Jcircumflex" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="T,Tcaron,Tbar,uni021A" 	k="145" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="59" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="166" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="d,q,dcaron,dcroat" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="f,uniFB01,uniFB02" 	k="47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="hyphen,endash,emdash" 	k="39" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="quoteleft,quotedblleft" 	k="113" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="quoteright,quotedblright" 	k="119" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="quotedbl,quotesingle" 	k="129" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="t,tcaron,tbar,uni021B" 	k="70" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="V" 	k="115" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="asterisk" 	k="121" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="backslash" 	k="147" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="copyright" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="eth" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="longs" 	k="47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="nine" 	k="53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="one" 	k="88" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="ordfeminine" 	k="117" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="ordmasculine" 	k="123" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="question" 	k="86" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="registered" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="seven" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="space" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="trademark" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="v" 	k="80" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="zero" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="braceright" 	k="18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="bracketright" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="parenright" 	k="23" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="41" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="V" 	k="39" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="backslash" 	k="39" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="trademark" 	k="12" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="braceright" 	k="37" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="bracketright" 	k="35" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="parenright" 	k="43" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="18" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="AE,AEacute" 	k="18" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="X" 	k="45" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="slash" 	k="39" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="T,Tcaron,Tbar,uni021A" 	k="23" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="14" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="68" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="V" 	k="45" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="backslash" 	k="57" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="trademark" 	k="33" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="braceright" 	k="55" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="bracketright" 	k="53" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="parenright" 	k="59" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="33" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="AE,AEacute" 	k="35" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="X" 	k="70" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="slash" 	k="55" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="12" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="12" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="x" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="d,q,dcaron,dcroat" 	k="4" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="f,uniFB01,uniFB02" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="4" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="hyphen,endash,emdash" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="4" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="t,tcaron,tbar,uni021B" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="4" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="V" 	k="6" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="eth" 	k="6" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="longs" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="one" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="v" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="four" 	k="6" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="T,Tcaron,Tbar,uni021A" 	k="8" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="8" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="53" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="V" 	k="49" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="backslash" 	k="43" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="trademark" 	k="20" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="braceright" 	k="43" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="bracketright" 	k="39" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="parenright" 	k="47" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="33" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="AE,AEacute" 	k="33" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="X" 	k="61" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="slash" 	k="47" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="12" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="4" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="x" 	k="14" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="hyphen,endash,emdash" 	k="6" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="eth" 	k="4" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="braceright" 	k="6" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="bracketright" 	k="6" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="parenright" 	k="8" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="6" />
<hkern g1="K,uni0136" 	g2="J,Jcircumflex" 	k="20" />
<hkern g1="K,uni0136" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="72" />
<hkern g1="K,uni0136" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="41" />
<hkern g1="K,uni0136" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="8" />
<hkern g1="K,uni0136" 	g2="d,q,dcaron,dcroat" 	k="53" />
<hkern g1="K,uni0136" 	g2="f,uniFB01,uniFB02" 	k="55" />
<hkern g1="K,uni0136" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="66" />
<hkern g1="K,uni0136" 	g2="guillemotleft,guilsinglleft" 	k="90" />
<hkern g1="K,uni0136" 	g2="hyphen,endash,emdash" 	k="86" />
<hkern g1="K,uni0136" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="59" />
<hkern g1="K,uni0136" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="K,uni0136" 	g2="t,tcaron,tbar,uni021B" 	k="66" />
<hkern g1="K,uni0136" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="41" />
<hkern g1="K,uni0136" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="84" />
<hkern g1="K,uni0136" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="102" />
<hkern g1="K,uni0136" 	g2="copyright" 	k="49" />
<hkern g1="K,uni0136" 	g2="eth" 	k="53" />
<hkern g1="K,uni0136" 	g2="longs" 	k="55" />
<hkern g1="K,uni0136" 	g2="nine" 	k="14" />
<hkern g1="K,uni0136" 	g2="one" 	k="43" />
<hkern g1="K,uni0136" 	g2="registered" 	k="49" />
<hkern g1="K,uni0136" 	g2="space" 	k="8" />
<hkern g1="K,uni0136" 	g2="v" 	k="98" />
<hkern g1="K,uni0136" 	g2="zero" 	k="14" />
<hkern g1="K,uni0136" 	g2="four" 	k="10" />
<hkern g1="K,uni0136" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="14" />
<hkern g1="K,uni0136" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="23" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="16" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="T,Tcaron,Tbar,uni021A" 	k="217" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="27" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="72" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="205" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="f,uniFB01,uniFB02" 	k="72" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="10" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="guillemotleft,guilsinglleft" 	k="86" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="hyphen,endash,emdash" 	k="66" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="8" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="quoteleft,quotedblleft" 	k="219" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="quoteright,quotedblright" 	k="221" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="quotedbl,quotesingle" 	k="223" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="t,tcaron,tbar,uni021B" 	k="117" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="14" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="104" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="178" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="V" 	k="184" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="asterisk" 	k="233" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="backslash" 	k="164" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="copyright" 	k="25" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="eth" 	k="6" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="longs" 	k="72" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="nine" 	k="72" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="one" 	k="150" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="ordfeminine" 	k="233" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="ordmasculine" 	k="236" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="question" 	k="115" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="registered" 	k="25" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="seven" 	k="45" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="space" 	k="47" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="trademark" 	k="236" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="v" 	k="168" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="zero" 	k="6" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="braceright" 	k="14" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="bracketright" 	k="16" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="parenright" 	k="16" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="four" 	k="16" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="periodcentered" 	k="287" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="guillemotright,guilsinglright" 	k="12" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="20" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="16" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="66" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="V" 	k="45" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="backslash" 	k="55" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="seven" 	k="6" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="trademark" 	k="31" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="braceright" 	k="51" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="bracketright" 	k="49" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="parenright" 	k="55" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="31" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="AE,AEacute" 	k="35" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="X" 	k="63" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="slash" 	k="53" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="12" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="x" 	k="14" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="6" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="14" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="16" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="57" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="d,q,dcaron,dcroat" 	k="12" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="14" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="hyphen,endash,emdash" 	k="27" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="14" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="V" 	k="45" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="backslash" 	k="41" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="eth" 	k="25" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="trademark" 	k="16" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="braceright" 	k="12" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="bracketright" 	k="14" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="parenright" 	k="16" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="T,Tcaron,Tbar,uni021A" 	k="14" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="16" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="59" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="hyphen,endash,emdash" 	k="6" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="6" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="V" 	k="49" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="backslash" 	k="47" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="trademark" 	k="27" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="v" 	k="4" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="braceright" 	k="41" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="bracketright" 	k="39" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="parenright" 	k="45" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="23" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="AE,AEacute" 	k="20" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="X" 	k="49" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="slash" 	k="27" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="x" 	k="18" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="20" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="4" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="d,q,dcaron,dcroat" 	k="170" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="f,uniFB01,uniFB02" 	k="55" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="164" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotleft,guilsinglleft" 	k="96" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="hyphen,endash,emdash" 	k="121" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="180" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="t,tcaron,tbar,uni021B" 	k="41" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="94" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="74" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="80" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="asterisk" 	k="-10" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="copyright" 	k="23" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="eth" 	k="170" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="longs" 	k="55" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="one" 	k="12" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="registered" 	k="23" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="space" 	k="47" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="v" 	k="66" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="zero" 	k="8" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="145" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="AE,AEacute" 	k="154" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="slash" 	k="141" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="125" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="x" 	k="78" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="four" 	k="106" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="180" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="162" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotright,guilsinglright" 	k="31" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="b,h,k,germandbls,thorn,hcircumflex,hbar,uni0137" 	k="31" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij" 	k="20" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="31" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="100" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="z,zacute,zdotaccent,zcaron" 	k="96" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="ampersand" 	k="37" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="j" 	k="33" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="six" 	k="86" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="colon,semicolon" 	k="37" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="at" 	k="31" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="hyphen,endash,emdash" 	k="6" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="4" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="eth" 	k="4" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="braceright" 	k="8" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="bracketright" 	k="8" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="parenright" 	k="8" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="35" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="AE,AEacute" 	k="39" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="X" 	k="10" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="slash" 	k="57" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="27" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="x" 	k="6" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="4" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="4" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="16" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="8" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="d,q,dcaron,dcroat" 	k="35" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="35" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="hyphen,endash,emdash" 	k="29" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="37" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="18" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="eth" 	k="49" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="space" 	k="14" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="59" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="AE,AEacute" 	k="74" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="slash" 	k="74" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="53" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="x" 	k="10" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="four" 	k="10" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="41" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="29" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="20" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="z,zacute,zdotaccent,zcaron" 	k="14" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="ampersand" 	k="20" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="six" 	k="12" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="J,Jcircumflex" 	k="35" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="66" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="49" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="d,q,dcaron,dcroat" 	k="174" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="f,uniFB01,uniFB02" 	k="76" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="174" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="guillemotleft,guilsinglleft" 	k="111" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="hyphen,endash,emdash" 	k="119" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="180" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="t,tcaron,tbar,uni021B" 	k="66" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="129" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="100" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="106" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="copyright" 	k="72" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="eth" 	k="182" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="longs" 	k="76" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="nine" 	k="27" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="one" 	k="31" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="question" 	k="23" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="registered" 	k="72" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="space" 	k="57" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="trademark" 	k="-18" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="v" 	k="100" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="zero" 	k="45" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="166" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="AE,AEacute" 	k="186" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="slash" 	k="172" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="150" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="x" 	k="111" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="four" 	k="137" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="203" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="190" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="guillemotright,guilsinglright" 	k="37" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="b,h,k,germandbls,thorn,hcircumflex,hbar,uni0137" 	k="4" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="4" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="137" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="z,zacute,zdotaccent,zcaron" 	k="127" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="ampersand" 	k="72" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="six" 	k="125" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="colon,semicolon" 	k="61" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="at" 	k="88" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="eight" 	k="41" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="five" 	k="35" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="two" 	k="37" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="12" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="d,q,dcaron,dcroat" 	k="14" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="f,uniFB01,uniFB02" 	k="16" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="20" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="guillemotleft,guilsinglleft" 	k="49" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="hyphen,endash,emdash" 	k="55" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="t,tcaron,tbar,uni021B" 	k="16" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="14" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="16" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="29" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="copyright" 	k="12" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="eth" 	k="12" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="longs" 	k="16" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="registered" 	k="12" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="v" 	k="27" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="four" 	k="10" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="4" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="4" />
<hkern g1="B" 	g2="T,Tcaron,Tbar,uni021A" 	k="16" />
<hkern g1="B" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="8" />
<hkern g1="B" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="49" />
<hkern g1="B" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="20" />
<hkern g1="B" 	g2="AE,AEacute" 	k="23" />
<hkern g1="B" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="8" />
<hkern g1="F" 	g2="J,Jcircumflex" 	k="4" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="10" />
<hkern g1="F" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="6" />
<hkern g1="F" 	g2="d,q,dcaron,dcroat" 	k="18" />
<hkern g1="F" 	g2="f,uniFB01,uniFB02" 	k="14" />
<hkern g1="F" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="18" />
<hkern g1="F" 	g2="hyphen,endash,emdash" 	k="8" />
<hkern g1="F" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="18" />
<hkern g1="F" 	g2="t,tcaron,tbar,uni021B" 	k="10" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="8" />
<hkern g1="F" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="12" />
<hkern g1="F" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="16" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="80" />
<hkern g1="F" 	g2="AE,AEacute" 	k="98" />
<hkern g1="F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="72" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="41" />
<hkern g1="F" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="23" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="F" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="18" />
<hkern g1="F" 	g2="z,zacute,zdotaccent,zcaron" 	k="39" />
<hkern g1="F" 	g2="colon,semicolon" 	k="6" />
<hkern g1="IJ" 	g2="hyphen,endash,emdash" 	k="6" />
<hkern g1="IJ" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="4" />
<hkern g1="IJ" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="31" />
<hkern g1="IJ" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="IJ" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="4" />
<hkern g1="IJ" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="4" />
<hkern g1="P" 	g2="J,Jcircumflex" 	k="25" />
<hkern g1="P" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="4" />
<hkern g1="P" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="43" />
<hkern g1="P" 	g2="d,q,dcaron,dcroat" 	k="12" />
<hkern g1="P" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="4" />
<hkern g1="P" 	g2="hyphen,endash,emdash" 	k="12" />
<hkern g1="P" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="100" />
<hkern g1="P" 	g2="AE,AEacute" 	k="125" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="121" />
<hkern g1="P" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="12" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="25" />
<hkern g1="Thorn" 	g2="T,Tcaron,Tbar,uni021A" 	k="61" />
<hkern g1="Thorn" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="18" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="100" />
<hkern g1="Thorn" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="Thorn" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="Thorn" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="Thorn" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="6" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="47" />
<hkern g1="Thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="51" />
<hkern g1="Thorn" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="33" />
<hkern g1="Thorn" 	g2="z,zacute,zdotaccent,zcaron" 	k="4" />
<hkern g1="V" 	g2="J,Jcircumflex" 	k="41" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="45" />
<hkern g1="V" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="47" />
<hkern g1="V" 	g2="d,q,dcaron,dcroat" 	k="106" />
<hkern g1="V" 	g2="f,uniFB01,uniFB02" 	k="29" />
<hkern g1="V" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="104" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="59" />
<hkern g1="V" 	g2="hyphen,endash,emdash" 	k="72" />
<hkern g1="V" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="111" />
<hkern g1="V" 	g2="t,tcaron,tbar,uni021B" 	k="25" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="66" />
<hkern g1="V" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="37" />
<hkern g1="V" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="43" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="115" />
<hkern g1="V" 	g2="AE,AEacute" 	k="131" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="119" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="123" />
<hkern g1="V" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="100" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="23" />
<hkern g1="V" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="72" />
<hkern g1="V" 	g2="z,zacute,zdotaccent,zcaron" 	k="61" />
<hkern g1="V" 	g2="colon,semicolon" 	k="39" />
<hkern g1="X" 	g2="J,Jcircumflex" 	k="23" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="66" />
<hkern g1="X" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="45" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="X" 	g2="d,q,dcaron,dcroat" 	k="51" />
<hkern g1="X" 	g2="f,uniFB01,uniFB02" 	k="55" />
<hkern g1="X" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="66" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="86" />
<hkern g1="X" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="X" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="57" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="29" />
<hkern g1="X" 	g2="t,tcaron,tbar,uni021B" 	k="68" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="41" />
<hkern g1="X" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="80" />
<hkern g1="X" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="102" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="12" />
<hkern g1="X" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="23" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="T,Tcaron,Tbar,uni021A" 	k="174" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="35" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="168" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="f,uniFB01,uniFB02" 	k="8" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="quotedbl,quotesingle" 	k="43" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="t,tcaron,tbar,uni021B" 	k="8" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="16" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="37" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="V" 	k="109" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="asterisk" 	k="41" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="backslash" 	k="117" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="braceright" 	k="37" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="longs" 	k="8" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="ordfeminine" 	k="35" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="ordmasculine" 	k="45" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="parenright" 	k="43" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="question" 	k="39" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="trademark" 	k="72" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="v" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="4" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="4" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="bracketright" 	k="23" />
<hkern g1="b,p,thorn" 	g2="T,Tcaron,Tbar,uni021A" 	k="170" />
<hkern g1="b,p,thorn" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="35" />
<hkern g1="b,p,thorn" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="174" />
<hkern g1="b,p,thorn" 	g2="f,uniFB01,uniFB02" 	k="6" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="47" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="53" />
<hkern g1="b,p,thorn" 	g2="t,tcaron,tbar,uni021B" 	k="4" />
<hkern g1="b,p,thorn" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="31" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="45" />
<hkern g1="b,p,thorn" 	g2="V" 	k="106" />
<hkern g1="b,p,thorn" 	g2="asterisk" 	k="45" />
<hkern g1="b,p,thorn" 	g2="backslash" 	k="115" />
<hkern g1="b,p,thorn" 	g2="braceright" 	k="78" />
<hkern g1="b,p,thorn" 	g2="longs" 	k="6" />
<hkern g1="b,p,thorn" 	g2="ordfeminine" 	k="39" />
<hkern g1="b,p,thorn" 	g2="ordmasculine" 	k="49" />
<hkern g1="b,p,thorn" 	g2="parenright" 	k="84" />
<hkern g1="b,p,thorn" 	g2="question" 	k="41" />
<hkern g1="b,p,thorn" 	g2="trademark" 	k="78" />
<hkern g1="b,p,thorn" 	g2="v" 	k="39" />
<hkern g1="b,p,thorn" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="10" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="33" />
<hkern g1="b,p,thorn" 	g2="bracketright" 	k="70" />
<hkern g1="b,p,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="12" />
<hkern g1="b,p,thorn" 	g2="X" 	k="51" />
<hkern g1="b,p,thorn" 	g2="slash" 	k="45" />
<hkern g1="b,p,thorn" 	g2="x" 	k="37" />
<hkern g1="b,p,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="8" />
<hkern g1="b,p,thorn" 	g2="z,zacute,zdotaccent,zcaron" 	k="4" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="188" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="25" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="193" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="quotedbl,quotesingle" 	k="43" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="25" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="39" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="V" 	k="94" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="asterisk" 	k="39" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="backslash" 	k="109" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="braceright" 	k="78" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="ordfeminine" 	k="33" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="ordmasculine" 	k="45" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="parenright" 	k="84" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="question" 	k="27" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="trademark" 	k="72" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="v" 	k="33" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="4" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="bracketright" 	k="68" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="X" 	k="41" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="slash" 	k="33" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="x" 	k="27" />
<hkern g1="d,dcroat" 	g2="T,Tcaron,Tbar,uni021A" 	k="31" />
<hkern g1="d,dcroat" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="4" />
<hkern g1="dcaron,lcaron" 	g2="f,uniFB01,uniFB02" 	k="-35" />
<hkern g1="dcaron,lcaron" 	g2="quoteright,quotedblright" 	k="-74" />
<hkern g1="dcaron,lcaron" 	g2="quotedbl,quotesingle" 	k="-143" />
<hkern g1="dcaron,lcaron" 	g2="t,tcaron,tbar,uni021B" 	k="-39" />
<hkern g1="dcaron,lcaron" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="-35" />
<hkern g1="dcaron,lcaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="-35" />
<hkern g1="dcaron,lcaron" 	g2="asterisk" 	k="-127" />
<hkern g1="dcaron,lcaron" 	g2="backslash" 	k="-127" />
<hkern g1="dcaron,lcaron" 	g2="braceright" 	k="-160" />
<hkern g1="dcaron,lcaron" 	g2="longs" 	k="-33" />
<hkern g1="dcaron,lcaron" 	g2="ordfeminine" 	k="-80" />
<hkern g1="dcaron,lcaron" 	g2="ordmasculine" 	k="-98" />
<hkern g1="dcaron,lcaron" 	g2="parenright" 	k="-162" />
<hkern g1="dcaron,lcaron" 	g2="question" 	k="-78" />
<hkern g1="dcaron,lcaron" 	g2="trademark" 	k="-197" />
<hkern g1="dcaron,lcaron" 	g2="v" 	k="-43" />
<hkern g1="dcaron,lcaron" 	g2="quoteleft,quotedblleft" 	k="-12" />
<hkern g1="dcaron,lcaron" 	g2="bracketright" 	k="-158" />
<hkern g1="dcaron,lcaron" 	g2="slash" 	k="76" />
<hkern g1="dcaron,lcaron" 	g2="x" 	k="-27" />
<hkern g1="dcaron,lcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="66" />
<hkern g1="dcaron,lcaron" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="80" />
<hkern g1="dcaron,lcaron" 	g2="colon,semicolon" 	k="-27" />
<hkern g1="dcaron,lcaron" 	g2="d,q,dcaron,dcroat" 	k="68" />
<hkern g1="dcaron,lcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="63" />
<hkern g1="dcaron,lcaron" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="dcaron,lcaron" 	g2="guillemotright,guilsinglright" 	k="-43" />
<hkern g1="dcaron,lcaron" 	g2="b,h,k,germandbls,thorn,hcircumflex,hbar,uni0137" 	k="-139" />
<hkern g1="dcaron,lcaron" 	g2="hyphen,endash,emdash" 	k="63" />
<hkern g1="dcaron,lcaron" 	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij" 	k="-141" />
<hkern g1="dcaron,lcaron" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="-139" />
<hkern g1="dcaron,lcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="88" />
<hkern g1="dcaron,lcaron" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="59" />
<hkern g1="dcaron,lcaron" 	g2="bar" 	k="-98" />
<hkern g1="dcaron,lcaron" 	g2="exclam" 	k="-123" />
<hkern g1="dcaron,lcaron" 	g2="j" 	k="-117" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="186" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="35" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="203" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="f,uniFB01,uniFB02" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quotedbl,quotesingle" 	k="49" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="t,tcaron,tbar,uni021B" 	k="2" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="33" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="47" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="V" 	k="113" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="asterisk" 	k="45" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="backslash" 	k="115" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="braceright" 	k="78" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="longs" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="ordfeminine" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="ordmasculine" 	k="51" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="parenright" 	k="84" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="question" 	k="43" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="trademark" 	k="78" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="v" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="6" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="bracketright" 	k="68" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="12" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="X" 	k="47" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="slash" 	k="35" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="x" 	k="37" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="z,zacute,zdotaccent,zcaron" 	k="6" />
<hkern g1="f" 	g2="T,Tcaron,Tbar,uni021A" 	k="-4" />
<hkern g1="f" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="-25" />
<hkern g1="f" 	g2="quotedbl,quotesingle" 	k="-8" />
<hkern g1="f" 	g2="V" 	k="-10" />
<hkern g1="f" 	g2="asterisk" 	k="-10" />
<hkern g1="f" 	g2="braceright" 	k="-20" />
<hkern g1="f" 	g2="ordmasculine" 	k="-14" />
<hkern g1="f" 	g2="parenright" 	k="-25" />
<hkern g1="f" 	g2="trademark" 	k="-18" />
<hkern g1="f" 	g2="bracketright" 	k="-20" />
<hkern g1="f" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="84" />
<hkern g1="f" 	g2="slash" 	k="94" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="78" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="16" />
<hkern g1="f" 	g2="d,q,dcaron,dcroat" 	k="8" />
<hkern g1="f" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="8" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="f" 	g2="hyphen,endash,emdash" 	k="25" />
<hkern g1="f" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="f" 	g2="eth" 	k="41" />
<hkern g1="f" 	g2="space" 	k="41" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,uniFB01" 	g2="T,Tcaron,Tbar,uni021A" 	k="20" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="T,Tcaron,Tbar,uni021A" 	k="80" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="10" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="109" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="quoteright,quotedblright" 	k="6" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="V" 	k="51" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="asterisk" 	k="6" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="backslash" 	k="78" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="braceright" 	k="16" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="parenright" 	k="20" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="trademark" 	k="41" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="bracketright" 	k="18" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="23" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="d,q,dcaron,dcroat" 	k="43" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="49" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="hyphen,endash,emdash" 	k="59" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="47" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="23" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="eth" 	k="53" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="space" 	k="8" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="J,Jcircumflex" 	k="20" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="20" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="6" />
<hkern g1="l,lacute,uni013C,lslash,uniFB02" 	g2="T,Tcaron,Tbar,uni021A" 	k="31" />
<hkern g1="l,lacute,uni013C,lslash,uniFB02" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="4" />
<hkern g1="l,lacute,uni013C,lslash,uniFB02" 	g2="periodcentered" 	k="84" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="T,Tcaron,Tbar,uni021A" 	k="174" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="37" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="168" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="f,uniFB01,uniFB02" 	k="12" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="quoteright,quotedblright" 	k="37" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="quotedbl,quotesingle" 	k="47" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="t,tcaron,tbar,uni021B" 	k="10" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="18" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="39" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="V" 	k="109" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="asterisk" 	k="41" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="backslash" 	k="117" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="braceright" 	k="37" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="longs" 	k="12" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="ordfeminine" 	k="35" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="ordmasculine" 	k="45" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="parenright" 	k="43" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="question" 	k="39" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="trademark" 	k="72" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="v" 	k="35" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="4" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="bracketright" 	k="23" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="180" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="37" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="180" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quoteright,quotedblright" 	k="39" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quotedbl,quotesingle" 	k="47" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="t,tcaron,tbar,uni021B" 	k="4" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="33" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="47" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="V" 	k="111" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="asterisk" 	k="43" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="backslash" 	k="117" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="braceright" 	k="82" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="longs" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="ordfeminine" 	k="39" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="ordmasculine" 	k="47" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="parenright" 	k="90" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="question" 	k="43" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="trademark" 	k="76" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="v" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="18" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="bracketright" 	k="72" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="14" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="X" 	k="57" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="slash" 	k="47" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="x" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="8" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="z,zacute,zdotaccent,zcaron" 	k="10" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="74" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="88" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="V" 	k="31" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="backslash" 	k="47" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="braceright" 	k="78" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="parenright" 	k="86" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="trademark" 	k="18" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="43" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="bracketright" 	k="76" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="92" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="X" 	k="109" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="slash" 	k="106" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="109" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="18" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="d,q,dcaron,dcroat" 	k="8" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="8" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="hyphen,endash,emdash" 	k="33" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="8" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="eth" 	k="37" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="space" 	k="39" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="J,Jcircumflex" 	k="51" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="T,Tcaron,Tbar,uni021A" 	k="172" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="35" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="193" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="f,uniFB01,uniFB02" 	k="6" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="t,tcaron,tbar,uni021B" 	k="6" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="33" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="45" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="V" 	k="111" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="asterisk" 	k="37" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="backslash" 	k="113" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="braceright" 	k="61" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="longs" 	k="6" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="ordfeminine" 	k="31" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="ordmasculine" 	k="41" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="parenright" 	k="70" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="question" 	k="27" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="trademark" 	k="70" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="v" 	k="41" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="4" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="bracketright" 	k="53" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="X" 	k="23" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="slash" 	k="23" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="x" 	k="20" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="hyphen,endash,emdash" 	k="8" />
<hkern g1="t,tbar,uni021B" 	g2="T,Tcaron,Tbar,uni021A" 	k="72" />
<hkern g1="t,tbar,uni021B" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="102" />
<hkern g1="t,tbar,uni021B" 	g2="V" 	k="41" />
<hkern g1="t,tbar,uni021B" 	g2="backslash" 	k="68" />
<hkern g1="t,tbar,uni021B" 	g2="braceright" 	k="10" />
<hkern g1="t,tbar,uni021B" 	g2="parenright" 	k="14" />
<hkern g1="t,tbar,uni021B" 	g2="trademark" 	k="29" />
<hkern g1="t,tbar,uni021B" 	g2="bracketright" 	k="10" />
<hkern g1="t,tbar,uni021B" 	g2="guillemotleft,guilsinglleft" 	k="6" />
<hkern g1="t,tbar,uni021B" 	g2="hyphen,endash,emdash" 	k="8" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="T,Tcaron,Tbar,uni021A" 	k="100" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="20" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="137" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="quoteright,quotedblright" 	k="6" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="quotedbl,quotesingle" 	k="6" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="V" 	k="72" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="asterisk" 	k="6" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="backslash" 	k="88" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="braceright" 	k="35" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="parenright" 	k="41" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="trademark" 	k="43" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="4" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="4" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="bracketright" 	k="20" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="T,Tcaron,Tbar,uni021A" 	k="74" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="100" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="V" 	k="37" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="backslash" 	k="68" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="braceright" 	k="70" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="parenright" 	k="78" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="trademark" 	k="14" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="20" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="bracketright" 	k="68" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="53" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="X" 	k="78" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="slash" 	k="94" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="70" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="z,zacute,zdotaccent,zcaron" 	k="10" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="37" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="d,q,dcaron,dcroat" 	k="31" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="29" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="hyphen,endash,emdash" 	k="27" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="33" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="29" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="eth" 	k="45" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="space" 	k="45" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="J,Jcircumflex" 	k="23" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="ampersand" 	k="14" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="T,Tcaron,Tbar,uni021A" 	k="68" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="98" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="V" 	k="37" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="backslash" 	k="66" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="braceright" 	k="76" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="parenright" 	k="84" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="question" 	k="-10" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="trademark" 	k="16" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="37" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="bracketright" 	k="76" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="84" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="X" 	k="96" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="slash" 	k="117" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="98" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="z,zacute,zdotaccent,zcaron" 	k="14" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="47" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="d,q,dcaron,dcroat" 	k="39" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="37" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="hyphen,endash,emdash" 	k="39" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="41" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="35" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="eth" 	k="63" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="space" 	k="51" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="J,Jcircumflex" 	k="43" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="ampersand" 	k="31" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="104" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="20" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="141" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="12" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="V" 	k="72" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="backslash" 	k="88" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="braceright" 	k="16" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="parenright" 	k="23" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="trademark" 	k="45" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="v" 	k="10" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="12" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="bracketright" 	k="18" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="d,q,dcaron,dcroat" 	k="4" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="10" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="eth" 	k="12" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="J,Jcircumflex" 	k="6" />
<hkern g1="eth" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="18" />
<hkern g1="eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="8" />
<hkern g1="eth" 	g2="z,zacute,zdotaccent,zcaron" 	k="2" />
<hkern g1="germandbls" 	g2="f,uniFB01,uniFB02" 	k="8" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="6" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="6" />
<hkern g1="germandbls" 	g2="t,tcaron,tbar,uni021B" 	k="6" />
<hkern g1="germandbls" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="25" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="37" />
<hkern g1="germandbls" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="6" />
<hkern g1="germandbls" 	g2="z,zacute,zdotaccent,zcaron" 	k="8" />
<hkern g1="longs" 	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij" 	k="-131" />
<hkern g1="tcaron" 	g2="f,uniFB01,uniFB02" 	k="-59" />
<hkern g1="tcaron" 	g2="quoteright,quotedblright" 	k="-131" />
<hkern g1="tcaron" 	g2="quotedbl,quotesingle" 	k="-199" />
<hkern g1="tcaron" 	g2="t,tcaron,tbar,uni021B" 	k="-80" />
<hkern g1="tcaron" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="-61" />
<hkern g1="tcaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="-66" />
<hkern g1="tcaron" 	g2="quoteleft,quotedblleft" 	k="-59" />
<hkern g1="tcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="39" />
<hkern g1="tcaron" 	g2="z,zacute,zdotaccent,zcaron" 	k="-23" />
<hkern g1="tcaron" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="41" />
<hkern g1="tcaron" 	g2="colon,semicolon" 	k="-53" />
<hkern g1="tcaron" 	g2="d,q,dcaron,dcroat" 	k="39" />
<hkern g1="tcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="35" />
<hkern g1="tcaron" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="tcaron" 	g2="guillemotright,guilsinglright" 	k="-88" />
<hkern g1="tcaron" 	g2="b,h,k,germandbls,thorn,hcircumflex,hbar,uni0137" 	k="-182" />
<hkern g1="tcaron" 	g2="hyphen,endash,emdash" 	k="55" />
<hkern g1="tcaron" 	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij" 	k="-182" />
<hkern g1="tcaron" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="-182" />
<hkern g1="tcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="51" />
<hkern g1="tcaron" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="29" />
<hkern g1="tcaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="-27" />
<hkern g1="tcaron" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="-18" />
<hkern g1="v" 	g2="T,Tcaron,Tbar,uni021A" 	k="66" />
<hkern g1="v" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="100" />
<hkern g1="v" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="37" />
<hkern g1="v" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="80" />
<hkern g1="v" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="94" />
<hkern g1="v" 	g2="z,zacute,zdotaccent,zcaron" 	k="14" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="47" />
<hkern g1="v" 	g2="d,q,dcaron,dcroat" 	k="39" />
<hkern g1="v" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="39" />
<hkern g1="v" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="v" 	g2="hyphen,endash,emdash" 	k="37" />
<hkern g1="v" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="41" />
<hkern g1="v" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="35" />
<hkern g1="v" 	g2="J,Jcircumflex" 	k="41" />
<hkern g1="x" 	g2="T,Tcaron,Tbar,uni021A" 	k="78" />
<hkern g1="x" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="10" />
<hkern g1="x" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="109" />
<hkern g1="x" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="23" />
<hkern g1="x" 	g2="d,q,dcaron,dcroat" 	k="39" />
<hkern g1="x" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="43" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="x" 	g2="hyphen,endash,emdash" 	k="49" />
<hkern g1="x" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="43" />
<hkern g1="x" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="18" />
<hkern g1="x" 	g2="J,Jcircumflex" 	k="25" />
<hkern g1="x" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="14" />
<hkern g1="x" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="6" />
<hkern g1="x" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="6" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="61" />
<hkern g1="colon,semicolon" 	g2="V" 	k="39" />
<hkern g1="colon,semicolon" 	g2="T,Tcaron,Tbar,uni021A" 	k="37" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="37" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T,Tcaron,Tbar,uni021A" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="111" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="61" />
<hkern g1="guillemotright,guilsinglright" 	g2="T,Tcaron,Tbar,uni021A" 	k="96" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="39" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE,AEacute" 	k="47" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="49" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="86" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="J,Jcircumflex" 	k="8" />
<hkern g1="guillemotright,guilsinglright" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="8" />
<hkern g1="guillemotright,guilsinglright" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="f,uniFB01,uniFB02" 	k="6" />
<hkern g1="guillemotright,guilsinglright" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="8" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="z,zacute,zdotaccent,zcaron" 	k="12" />
<hkern g1="guillemotright,guilsinglright" 	g2="j" 	k="6" />
<hkern g1="guillemotright,guilsinglright" 	g2="v" 	k="10" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="119" />
<hkern g1="hyphen,endash,emdash" 	g2="V" 	k="72" />
<hkern g1="hyphen,endash,emdash" 	g2="T,Tcaron,Tbar,uni021A" 	k="121" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="39" />
<hkern g1="hyphen,endash,emdash" 	g2="AE,AEacute" 	k="47" />
<hkern g1="hyphen,endash,emdash" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="49" />
<hkern g1="hyphen,endash,emdash" 	g2="X" 	k="80" />
<hkern g1="hyphen,endash,emdash" 	g2="x" 	k="49" />
<hkern g1="hyphen,endash,emdash" 	g2="J,Jcircumflex" 	k="12" />
<hkern g1="hyphen,endash,emdash" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="14" />
<hkern g1="hyphen,endash,emdash" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="29" />
<hkern g1="hyphen,endash,emdash" 	g2="f,uniFB01,uniFB02" 	k="16" />
<hkern g1="hyphen,endash,emdash" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="27" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="43" />
<hkern g1="hyphen,endash,emdash" 	g2="z,zacute,zdotaccent,zcaron" 	k="23" />
<hkern g1="hyphen,endash,emdash" 	g2="j" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="v" 	k="37" />
<hkern g1="hyphen,endash,emdash" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="6" />
<hkern g1="hyphen,endash,emdash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="hyphen,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="8" />
<hkern g1="hyphen,endash,emdash" 	g2="t,tcaron,tbar,uni021B" 	k="12" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="150" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="119" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T,Tcaron,Tbar,uni021A" 	k="125" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="6" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="53" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="f,uniFB01,uniFB02" 	k="47" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="70" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="102" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v" 	k="94" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="6" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="27" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="t,tcaron,tbar,uni021B" 	k="59" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="14" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="d,q,dcaron,dcroat" 	k="8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="eth" 	k="6" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="133" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE,AEacute" 	k="150" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="59" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="8" />
<hkern g1="quoteleft,quotedblleft" 	g2="d,q,dcaron,dcroat" 	k="66" />
<hkern g1="quoteleft,quotedblleft" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="53" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="59" />
<hkern g1="quoteleft,quotedblleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="8" />
<hkern g1="quoteleft,quotedblleft" 	g2="eth" 	k="74" />
<hkern g1="quoteleft,quotedblleft" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="45" />
<hkern g1="quoteleft,quotedblleft" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="8" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="-23" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-8" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="143" />
<hkern g1="quoteright,quotedblright" 	g2="AE,AEacute" 	k="162" />
<hkern g1="quoteright,quotedblright" 	g2="x" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="z,zacute,zdotaccent,zcaron" 	k="29" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="80" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="d,q,dcaron,dcroat" 	k="86" />
<hkern g1="quoteright,quotedblright" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="74" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="80" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="29" />
<hkern g1="quoteright,quotedblright" 	g2="eth" 	k="76" />
<hkern g1="quoteright,quotedblright" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="66" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="129" />
<hkern g1="quotedbl,quotesingle" 	g2="AE,AEacute" 	k="143" />
<hkern g1="quotedbl,quotesingle" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="49" />
<hkern g1="quotedbl,quotesingle" 	g2="d,q,dcaron,dcroat" 	k="53" />
<hkern g1="quotedbl,quotesingle" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="43" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="47" />
<hkern g1="quotedbl,quotesingle" 	g2="eth" 	k="70" />
<hkern g1="quotedbl,quotesingle" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="37" />
<hkern g1="quotedbl,quotesingle" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="6" />
<hkern g1="asterisk" 	g2="T,Tcaron,Tbar,uni021A" 	k="-10" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="121" />
<hkern g1="asterisk" 	g2="AE,AEacute" 	k="135" />
<hkern g1="asterisk" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="45" />
<hkern g1="asterisk" 	g2="d,q,dcaron,dcroat" 	k="45" />
<hkern g1="asterisk" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="39" />
<hkern g1="asterisk" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="43" />
<hkern g1="asterisk" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="6" />
<hkern g1="asterisk" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="33" />
<hkern g1="asterisk" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="6" />
<hkern g1="backslash" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="172" />
<hkern g1="backslash" 	g2="T,Tcaron,Tbar,uni021A" 	k="141" />
<hkern g1="backslash" 	g2="J,Jcircumflex" 	k="20" />
<hkern g1="backslash" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="23" />
<hkern g1="backslash" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="74" />
<hkern g1="backslash" 	g2="f,uniFB01,uniFB02" 	k="72" />
<hkern g1="backslash" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="94" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="98" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="57" />
<hkern g1="backslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="14" />
<hkern g1="backslash" 	g2="t,tcaron,tbar,uni021B" 	k="94" />
<hkern g1="backslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="53" />
<hkern g1="backslash" 	g2="d,q,dcaron,dcroat" 	k="45" />
<hkern g1="backslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="47" />
<hkern g1="backslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="47" />
<hkern g1="backslash" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="23" />
<hkern g1="braceleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="18" />
<hkern g1="braceleft" 	g2="AE,AEacute" 	k="16" />
<hkern g1="braceleft" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="35" />
<hkern g1="braceleft" 	g2="f,uniFB01,uniFB02" 	k="51" />
<hkern g1="braceleft" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="70" />
<hkern g1="braceleft" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="61" />
<hkern g1="braceleft" 	g2="z,zacute,zdotaccent,zcaron" 	k="18" />
<hkern g1="braceleft" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="6" />
<hkern g1="braceleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="8" />
<hkern g1="braceleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="53" />
<hkern g1="braceleft" 	g2="t,tcaron,tbar,uni021B" 	k="51" />
<hkern g1="braceleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="51" />
<hkern g1="braceleft" 	g2="d,q,dcaron,dcroat" 	k="78" />
<hkern g1="braceleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="82" />
<hkern g1="braceleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="66" />
<hkern g1="braceleft" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="61" />
<hkern g1="braceleft" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="37" />
<hkern g1="bracketleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="20" />
<hkern g1="bracketleft" 	g2="AE,AEacute" 	k="16" />
<hkern g1="bracketleft" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="31" />
<hkern g1="bracketleft" 	g2="f,uniFB01,uniFB02" 	k="49" />
<hkern g1="bracketleft" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="68" />
<hkern g1="bracketleft" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="61" />
<hkern g1="bracketleft" 	g2="z,zacute,zdotaccent,zcaron" 	k="18" />
<hkern g1="bracketleft" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="6" />
<hkern g1="bracketleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="8" />
<hkern g1="bracketleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="47" />
<hkern g1="bracketleft" 	g2="t,tcaron,tbar,uni021B" 	k="49" />
<hkern g1="bracketleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="49" />
<hkern g1="bracketleft" 	g2="d,q,dcaron,dcroat" 	k="70" />
<hkern g1="bracketleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="72" />
<hkern g1="bracketleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="59" />
<hkern g1="bracketleft" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="53" />
<hkern g1="bracketleft" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="20" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="84" />
<hkern g1="exclamdown" 	g2="T,Tcaron,Tbar,uni021A" 	k="41" />
<hkern g1="parenleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="23" />
<hkern g1="parenleft" 	g2="AE,AEacute" 	k="18" />
<hkern g1="parenleft" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="39" />
<hkern g1="parenleft" 	g2="f,uniFB01,uniFB02" 	k="55" />
<hkern g1="parenleft" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="78" />
<hkern g1="parenleft" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="68" />
<hkern g1="parenleft" 	g2="z,zacute,zdotaccent,zcaron" 	k="23" />
<hkern g1="parenleft" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="8" />
<hkern g1="parenleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="8" />
<hkern g1="parenleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="63" />
<hkern g1="parenleft" 	g2="t,tcaron,tbar,uni021B" 	k="53" />
<hkern g1="parenleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="55" />
<hkern g1="parenleft" 	g2="d,q,dcaron,dcroat" 	k="84" />
<hkern g1="parenleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="90" />
<hkern g1="parenleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="74" />
<hkern g1="parenleft" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="70" />
<hkern g1="parenleft" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="43" />
<hkern g1="periodcentered" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="84" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="170" />
<hkern g1="questiondown" 	g2="T,Tcaron,Tbar,uni021A" 	k="139" />
<hkern g1="questiondown" 	g2="J,Jcircumflex" 	k="27" />
<hkern g1="questiondown" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="27" />
<hkern g1="questiondown" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="72" />
<hkern g1="questiondown" 	g2="f,uniFB01,uniFB02" 	k="47" />
<hkern g1="questiondown" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="96" />
<hkern g1="questiondown" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="115" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="70" />
<hkern g1="questiondown" 	g2="t,tcaron,tbar,uni021B" 	k="86" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="68" />
<hkern g1="questiondown" 	g2="d,q,dcaron,dcroat" 	k="27" />
<hkern g1="questiondown" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="33" />
<hkern g1="questiondown" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="29" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="147" />
<hkern g1="slash" 	g2="AE,AEacute" 	k="156" />
<hkern g1="slash" 	g2="J,Jcircumflex" 	k="16" />
<hkern g1="slash" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="41" />
<hkern g1="slash" 	g2="f,uniFB01,uniFB02" 	k="51" />
<hkern g1="slash" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="68" />
<hkern g1="slash" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="72" />
<hkern g1="slash" 	g2="z,zacute,zdotaccent,zcaron" 	k="82" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="121" />
<hkern g1="slash" 	g2="t,tcaron,tbar,uni021B" 	k="37" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="55" />
<hkern g1="slash" 	g2="d,q,dcaron,dcroat" 	k="115" />
<hkern g1="slash" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="113" />
<hkern g1="slash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="117" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="82" />
<hkern g1="slash" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="109" />
<hkern g1="slash" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="88" />
<hkern g1="ampersand" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="-14" />
<hkern g1="ampersand" 	g2="AE,AEacute" 	k="-10" />
<hkern g1="ampersand" 	g2="T,Tcaron,Tbar,uni021A" 	k="96" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="109" />
<hkern g1="ampersand" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="31" />
<hkern g1="ampersand" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="10" />
<hkern g1="at" 	g2="T,Tcaron,Tbar,uni021A" 	k="27" />
<hkern g1="at" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="72" />
<hkern g1="at" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="6" />
<hkern g1="copyright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="33" />
<hkern g1="copyright" 	g2="AE,AEacute" 	k="41" />
<hkern g1="copyright" 	g2="T,Tcaron,Tbar,uni021A" 	k="23" />
<hkern g1="copyright" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="72" />
<hkern g1="copyright" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="12" />
<hkern g1="registered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="33" />
<hkern g1="registered" 	g2="AE,AEacute" 	k="41" />
<hkern g1="registered" 	g2="T,Tcaron,Tbar,uni021A" 	k="23" />
<hkern g1="registered" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="72" />
<hkern g1="registered" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="12" />
<hkern g1="eight" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="47" />
<hkern g1="five" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="8" />
<hkern g1="four" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="76" />
<hkern g1="four" 	g2="T,Tcaron,Tbar,uni021A" 	k="55" />
<hkern g1="four" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="8" />
<hkern g1="nine" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="29" />
<hkern g1="nine" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="72" />
<hkern g1="nine" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="nine" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="8" />
<hkern g1="seven" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="100" />
<hkern g1="six" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="90" />
<hkern g1="six" 	g2="T,Tcaron,Tbar,uni021A" 	k="78" />
<hkern g1="six" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="8" />
<hkern g1="two" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="37" />
<hkern g1="zero" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="45" />
<hkern g1="zero" 	g2="T,Tcaron,Tbar,uni021A" 	k="8" />
<hkern g1="zero" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="14" />
<hkern g1="space" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="51" />
<hkern g1="space" 	g2="AE,AEacute" 	k="53" />
<hkern g1="space" 	g2="T,Tcaron,Tbar,uni021A" 	k="47" />
<hkern g1="space" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="57" />
<hkern g1="space" 	g2="f,uniFB01,uniFB02" 	k="35" />
<hkern g1="space" 	g2="t,tcaron,tbar,uni021B" 	k="39" />
<hkern g1="space" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="45" />
<hkern g1="space" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="53" />
<hkern g1="space" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="14" />
<hkern g1="uni0403,uni0413" 	g2="uni0409,uni041B" 	k="74" />
<hkern g1="uni0403,uni0413" 	g2="uni0404,uni041E,uni0421" 	k="18" />
<hkern g1="uni0403,uni0413" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="94" />
<hkern g1="uni0403,uni0413" 	g2="uni043B,uni0459" 	k="195" />
<hkern g1="uni0403,uni0413" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="219" />
<hkern g1="uni0403,uni0413" 	g2="uni0456,uni0457" 	k="8" />
<hkern g1="uni0403,uni0413" 	g2="uni0452,uni045B" 	k="-31" />
<hkern g1="uni0403,uni0413" 	g2="guillemotleft,guilsinglleft" 	k="94" />
<hkern g1="uni0403,uni0413" 	g2="hyphen,endash,emdash" 	k="129" />
<hkern g1="uni0403,uni0413" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="182" />
<hkern g1="uni0403,uni0413" 	g2="asterisk" 	k="-14" />
<hkern g1="uni0403,uni0413" 	g2="slash" 	k="164" />
<hkern g1="uni0403,uni0413" 	g2="uni0443,uni045E" 	k="68" />
<hkern g1="uni0403,uni0413" 	g2="colon,semicolon" 	k="45" />
<hkern g1="uni0403,uni0413" 	g2="guillemotright,guilsinglright" 	k="23" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="uni0404,uni041E,uni0421" 	k="12" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="4" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="hyphen,endash,emdash" 	k="12" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="uni0443,uni045E" 	k="20" />
<hkern g1="uni040C,uni041A" 	g2="uni0404,uni041E,uni0421" 	k="72" />
<hkern g1="uni040C,uni041A" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="59" />
<hkern g1="uni040C,uni041A" 	g2="guillemotleft,guilsinglleft" 	k="90" />
<hkern g1="uni040C,uni041A" 	g2="hyphen,endash,emdash" 	k="86" />
<hkern g1="uni040C,uni041A" 	g2="uni0443,uni045E" 	k="102" />
<hkern g1="uni040C,uni041A" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="uni0409,uni041B" 	k="18" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="uni043B,uni0459" 	k="25" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="slash" 	k="53" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="uni040E,uni0423" 	k="25" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="backslash" 	k="55" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="braceright" 	k="51" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="bracketright" 	k="49" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="parenright" 	k="55" />
<hkern g1="uni040E,uni0423" 	g2="uni0409,uni041B" 	k="80" />
<hkern g1="uni040E,uni0423" 	g2="uni0404,uni041E,uni0421" 	k="41" />
<hkern g1="uni040E,uni0423" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="86" />
<hkern g1="uni040E,uni0423" 	g2="uni043B,uni0459" 	k="178" />
<hkern g1="uni040E,uni0423" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="158" />
<hkern g1="uni040E,uni0423" 	g2="uni0452,uni045B" 	k="-39" />
<hkern g1="uni040E,uni0423" 	g2="guillemotleft,guilsinglleft" 	k="76" />
<hkern g1="uni040E,uni0423" 	g2="hyphen,endash,emdash" 	k="88" />
<hkern g1="uni040E,uni0423" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="145" />
<hkern g1="uni040E,uni0423" 	g2="slash" 	k="168" />
<hkern g1="uni040E,uni0423" 	g2="uni0443,uni045E" 	k="37" />
<hkern g1="uni040E,uni0423" 	g2="colon,semicolon" 	k="39" />
<hkern g1="uni040E,uni0423" 	g2="guillemotright,guilsinglright" 	k="18" />
<hkern g1="uni040E,uni0423" 	g2="braceright" 	k="-6" />
<hkern g1="uni040E,uni0423" 	g2="parenright" 	k="-6" />
<hkern g1="uni0426,uni0429" 	g2="uni0404,uni041E,uni0421" 	k="6" />
<hkern g1="uni0426,uni0429" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="4" />
<hkern g1="uni0426,uni0429" 	g2="uni0452,uni045B" 	k="6" />
<hkern g1="uni0426,uni0429" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="uni0426,uni0429" 	g2="hyphen,endash,emdash" 	k="8" />
<hkern g1="uni0426,uni0429" 	g2="asterisk" 	k="43" />
<hkern g1="uni0426,uni0429" 	g2="uni0443,uni045E" 	k="61" />
<hkern g1="uni0426,uni0429" 	g2="quoteleft,quotedblleft" 	k="43" />
<hkern g1="uni0426,uni0429" 	g2="backslash" 	k="49" />
<hkern g1="uni0426,uni0429" 	g2="quoteright,quotedblright" 	k="43" />
<hkern g1="uni0426,uni0429" 	g2="quotedbl,quotesingle" 	k="43" />
<hkern g1="uni0426,uni0429" 	g2="question" 	k="29" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="uni0452,uni045B" 	k="14" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="asterisk" 	k="84" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="slash" 	k="25" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="uni0443,uni045E" 	k="41" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="quoteleft,quotedblleft" 	k="80" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="uni040E,uni0423" 	k="29" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="backslash" 	k="123" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="braceright" 	k="76" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="bracketright" 	k="66" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="parenright" 	k="82" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="quoteright,quotedblright" 	k="88" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="quotedbl,quotesingle" 	k="102" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="question" 	k="59" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="hyphen,endash,emdash" 	k="6" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="6" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="braceright" 	k="6" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="bracketright" 	k="6" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="parenright" 	k="8" />
<hkern g1="uni0433,uni0453" 	g2="uni043B,uni0459" 	k="63" />
<hkern g1="uni0433,uni0453" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="14" />
<hkern g1="uni0433,uni0453" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="141" />
<hkern g1="uni0433,uni0453" 	g2="backslash" 	k="47" />
<hkern g1="uni0433,uni0453" 	g2="braceright" 	k="78" />
<hkern g1="uni0433,uni0453" 	g2="bracketright" 	k="76" />
<hkern g1="uni0433,uni0453" 	g2="parenright" 	k="86" />
<hkern g1="uni0433,uni0453" 	g2="slash" 	k="123" />
<hkern g1="uni0433,uni0453" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="uni0433,uni0453" 	g2="hyphen,endash,emdash" 	k="39" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="uni043B,uni0459" 	k="10" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="backslash" 	k="115" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="braceright" 	k="78" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="bracketright" 	k="68" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="parenright" 	k="84" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="slash" 	k="35" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="uni0443,uni045E" 	k="49" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="uni0452,uni045B" 	k="18" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="quotedbl,quotesingle" 	k="49" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="asterisk" 	k="45" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="question" 	k="43" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="backslash" 	k="88" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="braceright" 	k="37" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="bracketright" 	k="20" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="parenright" 	k="43" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="uni0452,uni045B" 	k="6" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="quoteright,quotedblright" 	k="6" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="quotedbl,quotesingle" 	k="6" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="asterisk" 	k="6" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="uni043A,uni045C" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="45" />
<hkern g1="uni043A,uni045C" 	g2="backslash" 	k="76" />
<hkern g1="uni043A,uni045C" 	g2="braceright" 	k="16" />
<hkern g1="uni043A,uni045C" 	g2="bracketright" 	k="18" />
<hkern g1="uni043A,uni045C" 	g2="parenright" 	k="20" />
<hkern g1="uni043A,uni045C" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="uni043A,uni045C" 	g2="hyphen,endash,emdash" 	k="57" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="uni043B,uni0459" 	k="16" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="8" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="backslash" 	k="117" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="braceright" 	k="82" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="bracketright" 	k="72" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="parenright" 	k="90" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="slash" 	k="47" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="uni0443,uni045E" 	k="47" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="uni0452,uni045B" 	k="20" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="quoteright,quotedblright" 	k="39" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="quotedbl,quotesingle" 	k="47" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="asterisk" 	k="43" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="question" 	k="43" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="uni0440,uni0444" 	g2="uni043B,uni0459" 	k="16" />
<hkern g1="uni0440,uni0444" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="8" />
<hkern g1="uni0440,uni0444" 	g2="backslash" 	k="115" />
<hkern g1="uni0440,uni0444" 	g2="braceright" 	k="78" />
<hkern g1="uni0440,uni0444" 	g2="bracketright" 	k="70" />
<hkern g1="uni0440,uni0444" 	g2="parenright" 	k="86" />
<hkern g1="uni0440,uni0444" 	g2="slash" 	k="43" />
<hkern g1="uni0440,uni0444" 	g2="uni0443,uni045E" 	k="45" />
<hkern g1="uni0440,uni0444" 	g2="uni0452,uni045B" 	k="18" />
<hkern g1="uni0440,uni0444" 	g2="quoteright,quotedblright" 	k="25" />
<hkern g1="uni0440,uni0444" 	g2="quotedbl,quotesingle" 	k="45" />
<hkern g1="uni0440,uni0444" 	g2="asterisk" 	k="39" />
<hkern g1="uni0440,uni0444" 	g2="question" 	k="37" />
<hkern g1="uni0440,uni0444" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="uni0443,uni045E" 	g2="uni043B,uni0459" 	k="74" />
<hkern g1="uni0443,uni045E" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="41" />
<hkern g1="uni0443,uni045E" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="98" />
<hkern g1="uni0443,uni045E" 	g2="backslash" 	k="66" />
<hkern g1="uni0443,uni045E" 	g2="braceright" 	k="76" />
<hkern g1="uni0443,uni045E" 	g2="bracketright" 	k="76" />
<hkern g1="uni0443,uni045E" 	g2="parenright" 	k="84" />
<hkern g1="uni0443,uni045E" 	g2="slash" 	k="117" />
<hkern g1="uni0443,uni045E" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="uni0443,uni045E" 	g2="hyphen,endash,emdash" 	k="39" />
<hkern g1="uni0443,uni045E" 	g2="question" 	k="-10" />
<hkern g1="uni0446,uni0449" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="6" />
<hkern g1="uni0446,uni0449" 	g2="backslash" 	k="121" />
<hkern g1="uni0446,uni0449" 	g2="guillemotleft,guilsinglleft" 	k="23" />
<hkern g1="uni0446,uni0449" 	g2="hyphen,endash,emdash" 	k="29" />
<hkern g1="uni0446,uni0449" 	g2="uni0443,uni045E" 	k="51" />
<hkern g1="uni0446,uni0449" 	g2="uni0452,uni045B" 	k="18" />
<hkern g1="uni0446,uni0449" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="uni0446,uni0449" 	g2="quotedbl,quotesingle" 	k="47" />
<hkern g1="uni0446,uni0449" 	g2="asterisk" 	k="49" />
<hkern g1="uni0446,uni0449" 	g2="question" 	k="41" />
<hkern g1="uni0446,uni0449" 	g2="quoteleft,quotedblleft" 	k="37" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="backslash" 	k="141" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="braceright" 	k="78" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="bracketright" 	k="68" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="parenright" 	k="84" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="slash" 	k="29" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="uni0443,uni045E" 	k="72" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="uni0452,uni045B" 	k="23" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="quoteright,quotedblright" 	k="109" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="quotedbl,quotesingle" 	k="125" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="asterisk" 	k="109" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="question" 	k="92" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="quoteleft,quotedblleft" 	k="102" />
<hkern g1="guillemotright,guilsinglright" 	g2="uni0409,uni041B" 	k="43" />
<hkern g1="guillemotright,guilsinglright" 	g2="uni040E,uni0423" 	k="63" />
<hkern g1="guillemotright,guilsinglright" 	g2="uni043B,uni0459" 	k="35" />
<hkern g1="guillemotright,guilsinglright" 	g2="uni0443,uni045E" 	k="10" />
<hkern g1="hyphen,endash,emdash" 	g2="uni0409,uni041B" 	k="39" />
<hkern g1="hyphen,endash,emdash" 	g2="uni040E,uni0423" 	k="61" />
<hkern g1="hyphen,endash,emdash" 	g2="uni043B,uni0459" 	k="37" />
<hkern g1="hyphen,endash,emdash" 	g2="uni0443,uni045E" 	k="43" />
<hkern g1="hyphen,endash,emdash" 	g2="uni0452,uni045B" 	k="14" />
<hkern g1="hyphen,endash,emdash" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="6" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni040E,uni0423" 	k="6" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0443,uni045E" 	k="104" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0452,uni045B" 	k="14" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="6" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0404,uni041E,uni0421" 	k="14" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="8" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0409,uni041B" 	k="63" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni043B,uni0459" 	k="63" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0452,uni045B" 	k="-6" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0404,uni041E,uni0421" 	k="8" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="59" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="8" />
<hkern g1="quoteright,quotedblright" 	g2="uni0409,uni041B" 	k="63" />
<hkern g1="quoteright,quotedblright" 	g2="uni040E,uni0423" 	k="-16" />
<hkern g1="quoteright,quotedblright" 	g2="uni043B,uni0459" 	k="80" />
<hkern g1="quoteright,quotedblright" 	g2="uni0443,uni045E" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="uni0452,uni045B" 	k="-23" />
<hkern g1="quoteright,quotedblright" 	g2="uni0404,uni041E,uni0421" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="80" />
<hkern g1="quoteright,quotedblright" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="uni0409,uni041B" 	k="63" />
<hkern g1="quotedbl,quotesingle" 	g2="uni043B,uni0459" 	k="57" />
<hkern g1="quotedbl,quotesingle" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="47" />
<hkern g1="quotedbl,quotesingle" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="6" />
<hkern g1="asterisk" 	g2="uni0409,uni041B" 	k="61" />
<hkern g1="asterisk" 	g2="uni043B,uni0459" 	k="55" />
<hkern g1="asterisk" 	g2="uni0452,uni045B" 	k="-45" />
<hkern g1="asterisk" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="43" />
<hkern g1="asterisk" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="6" />
<hkern g1="backslash" 	g2="uni0443,uni045E" 	k="45" />
<hkern g1="backslash" 	g2="uni0452,uni045B" 	k="35" />
<hkern g1="backslash" 	g2="uni0404,uni041E,uni0421" 	k="53" />
<hkern g1="backslash" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="47" />
<hkern g1="braceleft" 	g2="uni0409,uni041B" 	k="10" />
<hkern g1="braceleft" 	g2="uni043B,uni0459" 	k="18" />
<hkern g1="braceleft" 	g2="uni0443,uni045E" 	k="14" />
<hkern g1="braceleft" 	g2="uni0452,uni045B" 	k="-16" />
<hkern g1="braceleft" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="6" />
<hkern g1="braceleft" 	g2="uni0404,uni041E,uni0421" 	k="51" />
<hkern g1="braceleft" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="82" />
<hkern g1="braceleft" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="37" />
<hkern g1="bracketleft" 	g2="uni0409,uni041B" 	k="12" />
<hkern g1="bracketleft" 	g2="uni043B,uni0459" 	k="18" />
<hkern g1="bracketleft" 	g2="uni0443,uni045E" 	k="14" />
<hkern g1="bracketleft" 	g2="uni0452,uni045B" 	k="-14" />
<hkern g1="bracketleft" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="6" />
<hkern g1="bracketleft" 	g2="uni0404,uni041E,uni0421" 	k="49" />
<hkern g1="bracketleft" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="72" />
<hkern g1="bracketleft" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="20" />
<hkern g1="parenleft" 	g2="uni0409,uni041B" 	k="12" />
<hkern g1="parenleft" 	g2="uni043B,uni0459" 	k="20" />
<hkern g1="parenleft" 	g2="uni0443,uni045E" 	k="14" />
<hkern g1="parenleft" 	g2="uni0452,uni045B" 	k="-18" />
<hkern g1="parenleft" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="8" />
<hkern g1="parenleft" 	g2="uni0404,uni041E,uni0421" 	k="55" />
<hkern g1="parenleft" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="90" />
<hkern g1="parenleft" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="43" />
<hkern g1="slash" 	g2="uni0409,uni041B" 	k="70" />
<hkern g1="slash" 	g2="uni043B,uni0459" 	k="133" />
<hkern g1="slash" 	g2="uni0443,uni045E" 	k="72" />
<hkern g1="slash" 	g2="uni0404,uni041E,uni0421" 	k="55" />
<hkern g1="slash" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="117" />
<hkern g1="slash" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="88" />
</font>
</defs></svg> 