<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20161116 at Fri Jan 17 23:48:27 2014
 By <PERSON>
Copyright \(c\) 2010 by Ryoichi Tsunekawa. All rights reserved.
</metadata>
<defs>
<font id="BebasNeueRegular" horiz-adv-x="387" >
  <font-face 
    font-family="Bebas Neue Regular"
    font-weight="400"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 5 0 0 0 0 0 0 0"
    ascent="750"
    descent="-250"
    x-height="700"
    cap-height="700"
    bbox="-79 -179 831 887"
    underline-thickness="50"
    underline-position="-110"
    unicode-range="U+000D-F6C3"
  />
<missing-glyph horiz-adv-x="548" 
d="M50 700h448v-700h-448v700zM232 358l-110 -281h55l92 236l92 -236h62l-110 281l103 264h-55l-86 -218l-85 218h-61z" />
    <glyph glyph-name=".notdef" horiz-adv-x="548" 
d="M50 700h448v-700h-448v700zM232 358l-110 -281h55l92 236l92 -236h62l-110 281l103 264h-55l-86 -218l-85 218h-61z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="155" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="155" 
 />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="422" 
d="M157 737h-68l86 100h70l86 -100h-68l-53 59zM119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="422" 
d="M182 827v-90h-68v90h68zM306 827v-90h-68v90h68zM119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="422" 
d="M190 840l70 -103h-57l-93 103h80zM119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="422" 
d="M316 806v-56h-212v56h212zM119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="422" 
d="M373 0q-18 -15 -27.5 -35t-8.5 -34q1 -16 12 -23t25 -7t23 2.5t17 6.5v-41q-15 -7 -29 -9.5t-33 -2.5q-33 0 -53.5 13.5t-21.5 42.5q-1 21 12 42t36 38l-29 148h-177l-27 -141h-72l134 700h113l134 -700h-28zM129 208h156l-79 404z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="422" 
d="M210 883q31 0 52 -21t21 -52q0 -32 -21 -52.5t-52 -20.5q-32 0 -53 20.5t-21 52.5q0 31 21 52t53 21zM210 846q-16 0 -26.5 -10.5t-10.5 -25.5q0 -16 10.5 -26t26.5 -10q15 0 25.5 10t10.5 26q0 15 -10.5 25.5t-25.5 10.5zM119 141l-27 -141h-72l134 700h113l134 -700
h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="422" 
d="M161 826q15 0 27.5 -4.5t23 -10.5t20.5 -10.5t21 -4.5q13 1 22.5 7.5t17.5 22.5l39 -27q-12 -29 -30 -43t-41 -14q-15 0 -27 4.5t-22.5 10t-20.5 10t-22 4.5q-14 0 -24 -6.5t-18 -23.5l-39 27q12 31 31 44t42 14zM119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141
h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="585" 
d="M516 390v-70h-161v-249h198v-71h-275v142h-143l-45 -142h-74l218 700h319v-71h-198v-239h161zM153 210h125v392z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="585" 
d="M410 844l-93 -103h-54l70 103h77zM516 390v-70h-161v-249h198v-71h-275v142h-143l-45 -142h-74l218 700h319v-71h-198v-239h161zM153 210h125v392z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="392" 
d="M197 700q82 0 117 -38.5t35 -116.5v-33q0 -56 -18 -90.5t-64 -48.5q51 -14 72.5 -53t21.5 -97v-59q0 -79 -39 -121.5t-121 -42.5h-161v700h157zM178 403q47 0 70.5 19t23.5 74v41q0 47 -17.5 69.5t-59.5 22.5h-77v-226h60zM201 71q83 0 83 95v62q0 59 -23.5 81.5
t-74.5 22.5h-68v-261h83z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="380" 
d="M191 707q81 0 119.5 -48t38.5 -128v-63h-74v67q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v91h74v-87q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v362q0 40 9.5 72.5t29 55.5t49 35.5
t69.5 12.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="380" 
d="M293 845l-93 -103h-55l70 103h78zM191 707q81 0 119.5 -48t38.5 -128v-63h-74v67q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v91h74v-87q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v362
q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="380" 
d="M141 841l52 -60l53 60h69l-87 -100h-69l-86 100h68zM191 707q81 0 119.5 -48t38.5 -128v-63h-74v67q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v91h74v-87q0 -80 -38.5 -128t-119.5 -48
q-80 0 -118.5 48t-38.5 128v362q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="380" 
d="M191 707q81 0 119.5 -48t38.5 -128v-63h-74v67q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v91h74v-87q0 -75 -34.5 -122.5t-106.5 -52.5v-31q35 0 54.5 -13.5t19.5 -37.5q0 -30 -25 -47t-70 -17
q-44 0 -68 16.5t-24 45.5v8h51v-7q0 -26 41 -29q43 0 43 32q0 30 -49 30h-9v50q-71 5 -104.5 53t-33.5 122v362q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="380" 
d="M140 740h-68l86 101h69l87 -101h-68l-53 59zM191 707q81 0 119.5 -48t38.5 -128v-63h-74v67q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v91h74v-87q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48
t-38.5 128v362q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="380" 
d="M225 828v-90h-68v90h68zM191 707q81 0 119.5 -48t38.5 -128v-63h-74v67q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v91h74v-87q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v362
q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="396" 
d="M204 700q82 0 120.5 -46.5t38.5 -127.5v-353q0 -81 -38.5 -127t-120.5 -46h-164v700h164zM203 71q42 0 62.5 25.5t20.5 73.5v360q0 48 -20.5 73.5t-63.5 25.5h-84v-558h85z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="396" 
d="M204 700q82 0 120.5 -46.5t38.5 -127.5v-353q0 -81 -38.5 -127t-120.5 -46h-164v318h-41v64h41v318h164zM196 318h-78v-247h85q42 0 62.5 25.5t20.5 73.5v360q0 48 -20.5 73.5t-63.5 25.5h-84v-247h78v-64z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="396" 
d="M135 839l53 -60l53 60h69l-87 -100h-69l-87 100h68zM204 700q82 0 120.5 -46.5t38.5 -127.5v-353q0 -81 -38.5 -127t-120.5 -46h-164v700h164zM203 71q42 0 62.5 25.5t20.5 73.5v360q0 48 -20.5 73.5t-63.5 25.5h-84v-558h85z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="396" 
d="M204 700q82 0 120.5 -46.5t38.5 -127.5v-353q0 -81 -38.5 -127t-120.5 -46h-164v318h-39v63h39v319h164zM197 318h-79v-247h85q42 0 62.5 25.5t20.5 73.5v360q0 48 -20.5 73.5t-63.5 25.5h-84v-248h79v-63z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="360" 
d="M289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="360" 
d="M283 842l-93 -103h-54l70 103h77zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="360" 
d="M287 840q-1 -48 -28.5 -74.5t-77.5 -26.5q-48 0 -75 26.5t-28 74.5h47q2 -29 17.5 -40.5t38.5 -11.5q25 0 41 11.5t18 40.5h47zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="360" 
d="M131 839l52 -60l54 60h68l-87 -100h-69l-86 100h68zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="360" 
d="M130 739h-68l86 101h69l87 -101h-68l-54 60zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="360" 
d="M155 829v-90h-68v90h68zM279 829v-90h-68v90h68zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="360" 
d="M218 830v-90h-68v90h68zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="360" 
d="M167 845l69 -103h-57l-93 103h81zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="360" 
d="M286 807v-55h-209v55h209zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="360" 
d="M289 390v-70h-171v-249h210v-71h-33q-19 -15 -28 -35t-9 -34q1 -16 12 -23t26 -7q14 0 22.5 2.5t16.5 6.5v-41q-15 -7 -29 -9.5t-32 -2.5q-34 0 -54.5 13.5t-21.5 42.5q0 24 16.5 47t44.5 40h-219v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="340" 
d="M279 374v-71h-161v-303h-78v700h278v-71h-200v-255h161z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="383" 
d="M191 707q81 0 119.5 -48t38.5 -128v-60h-74v64q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v140h-72v70h146v-206q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v362q0 40 9.5 72.5t29 55.5
t49 35.5t69.5 12.5z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="383" 
d="M296 841q-1 -48 -28.5 -74.5t-76.5 -26.5t-76 26.5t-28 74.5h47q2 -29 17.5 -40.5t39.5 -11.5t40 11.5t18 40.5h47zM191 707q81 0 119.5 -48t38.5 -128v-60h-74v64q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5
t19.5 74.5v140h-72v70h146v-206q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v362q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="383" 
d="M139 742h-68l86 100h69l87 -100h-68l-54 59zM191 707q81 0 119.5 -48t38.5 -128v-60h-74v64q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v140h-72v70h146v-206q0 -80 -38.5 -128t-119.5 -48
q-80 0 -118.5 48t-38.5 128v362q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="383" 
d="M228 830v-90h-68v90h68zM191 707q81 0 119.5 -48t38.5 -128v-60h-74v64q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v140h-72v70h146v-206q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128
v362q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="408" 
d="M118 315v-315h-78v700h78v-315h172v315h78v-700h-78v315h-172z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="415" 
d="M372 514v-514h-79v315h-172v-315h-78v514h-36v58h36v128h78v-128h172v128h79v-128h36v-58h-36zM293 514h-172v-129h172v129z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="408" 
d="M151 741h-68l87 101h69l86 -101h-68l-53 59zM118 315v-315h-78v700h78v-315h172v315h78v-700h-78v315h-172z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="158" 
d="M118 700v-700h-78v700h78z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="412" 
d="M118 700v-700h-78v700h78zM376 173q0 -50 -11.5 -83t-32 -53t-48 -28.5t-59.5 -9.5h-24.5t-23.5 1v71q10 -1 19.5 -1h18.5q40 2 61.5 23.5t21.5 74.5v532h78v-527z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="158" 
d="M100 842h78l-93 -103h-54zM40 700h78v-700h-78v700z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="158" 
d="M183 838q0 -48 -28 -75t-77 -27t-75.5 27t-27.5 75h47q2 -30 17.5 -41.5t38.5 -11.5q24 0 40 11.5t18 41.5h47zM40 700h78v-700h-78v700z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="158" 
d="M113 839l87 -100h-68l-53 59l-53 -59h-68l86 100h69zM40 700h78v-700h-78v700z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="158" 
d="M51 829v-90h-68v90h68zM175 829v-90h-68v90h68zM118 700v-700h-78v700h78z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="158" 
d="M113 829v-90h-68v90h68zM118 700v-700h-78v700h78z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="158" 
d="M-25 842h81l70 -103h-58zM40 700h78v-700h-78v700z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="158" 
d="M178 806v-56h-199v56h199zM118 700v-700h-78v700h78z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="158" 
d="M104 0q-19 -15 -28 -35t-8 -34q0 -16 11 -23t26 -7q14 0 22.5 2.5t17.5 6.5v-41q-15 -7 -29.5 -9.5t-32.5 -2.5q-33 0 -54 13.5t-21 42.5q-1 23 15 46t46 41h-29v700h78v-700h-14z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="158" 
d="M201 802q-22 -58 -71 -58q-15 0 -26.5 4.5t-22.5 10t-21 10t-22 4.5q-14 0 -24 -6t-18 -23l-39 26q13 31 32 44.5t42 13.5q15 0 27 -4.5t22.5 -10t21 -10t21.5 -4.5q13 0 22.5 6.5t17.5 22.5zM118 700v-700h-78v700h78z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="255" 
d="M20 71q10 -1 20 -1h19q38 2 60 23.5t22 74.5v532h77v-527q0 -49 -11.5 -82t-31.5 -53.5t-47.5 -29t-58.5 -9.5q-14 0 -25 -0.5t-24 1.5v71z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="255" 
d="M114 739h-68l86 101h69l87 -101h-68l-53 60zM218 173q0 -49 -11.5 -82t-31.5 -53.5t-47.5 -29t-58.5 -9.5q-14 0 -25 -0.5t-24 1.5v71q10 -1 20 -1h19q38 2 60 23.5t22 74.5v532h77v-527z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="409" 
d="M159 304l-41 -69v-235h-78v700h78v-338l186 338h79l-177 -319l183 -381h-80z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="337" 
d="M40 0v700h78v-629h200v-71h-278z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="337" 
d="M206 842l-93 -103h-54l70 103h77zM40 0v700h78v-629h200v-71h-278z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="337" 
d="M40 0v700h78v-629h200v-71h-278zM242 603h-29v97h75v-89l-38 -96h-33z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="337" 
d="M40 0v700h78v-629h200v-71h-278zM272 400v-97h-75v97h75z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="348" 
d="M128 291v-220h201v-71h-278v208l-51 -50v72l51 50v420h77v-337l109 113v-72z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="523" 
d="M298 1h-75l-116 557v-558h-68v700h109l115 -559l111 559h109v-700h-74v563z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="409" 
d="M109 0h-70v700h99l162 -506v506h69v-700h-81l-179 566v-566z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="409" 
d="M306 842l-93 -102h-54l70 102h77zM109 0h-70v700h99l162 -506v506h69v-700h-81l-179 567v-567z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="409" 
d="M152 842l53 -60l53 60h68l-86 -101h-69l-87 101h68zM109 0h-70v700h99l162 -506v506h69v-700h-81l-179 566v-566z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="409" 
d="M327 804q-12 -30 -30 -44t-41 -14q-15 0 -27 4.5t-22.5 10t-20.5 10t-22 4.5q-14 0 -24 -6t-18 -23l-39 26q12 31 31 44.5t43 13.5q15 0 27 -4.5t22.5 -10t20.5 -10t21 -4.5q26 0 40 29zM109 0h-70v700h99l162 -506v506h69v-700h-81l-179 566v-566z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="389" 
d="M34 531q0 81 39.5 128.5t120.5 47.5t121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362zM111 165q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370q0 48 -20 74.5t-63 26.5q-42 0 -62.5 -26.5t-20.5 -74.5v-370z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="389" 
d="M293 845l-93 -103h-54l70 103h77zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370
q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="389" 
d="M299 841q-1 -48 -28.5 -74.5t-77.5 -26.5q-48 0 -75 26.5t-28 74.5h47q2 -29 17.5 -40.5t38.5 -11.5q25 0 41 11.5t18 40.5h47zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636
q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="389" 
d="M141 741h-68l86 101h69l87 -101h-68l-54 59zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5
v370q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="389" 
d="M166 831v-90h-68v90h68zM290 831v-90h-68v90h68zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5
t20 74.5v370q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="389" 
d="M174 845l69 -103h-57l-93 103h81zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370
q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="389" 
d="M230 845l-80 -103h-49l57 103h72zM338 845l-81 -103h-49l57 103h73zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5
q43 0 63 26.5t20 74.5v370q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="389" 
d="M299 806v-55h-208v55h208zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370q0 48 -20 74.5
t-63 26.5z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="389" 
d="M325 646q30 -45 30 -115v-362q0 -80 -40 -128t-121 -48q-63 0 -102 31l-19 -50l-34 13l25 67q-30 45 -30 115v362q0 81 39.5 128.5t120.5 47.5q63 0 103 -31l18 49l35 -13zM266 594q-20 42 -72 42q-42 0 -62.5 -26.5t-20.5 -74.5v-355zM194 64q43 0 63 26.5t20 74.5v353
l-155 -413q20 -41 72 -41z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="389" 
d="M219 845h78l-93 -103h-55zM325 646q30 -45 30 -115v-362q0 -80 -40 -128t-121 -48q-63 0 -102 31l-19 -50l-34 13l25 67q-30 45 -30 115v362q0 81 39.5 128.5t120.5 47.5q63 0 103 -31l18 49l35 -13zM266 594q-20 42 -72 42q-42 0 -62.5 -26.5t-20.5 -74.5v-355zM194 64
q43 0 63 26.5t20 74.5v353l-155 -413q20 -41 72 -41z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="389" 
d="M318 802q-11 -30 -29.5 -43.5t-41.5 -13.5q-15 0 -26.5 4.5t-22.5 10t-21 10t-22 4.5q-14 0 -24 -6.5t-18 -23.5l-39 27q13 31 32 44t42 14q15 0 27 -4.5t22.5 -10.5t21 -10.5t21.5 -4.5q13 1 22.5 7.5t17.5 22.5zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128
t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="577" 
d="M508 390v-70h-161v-249h198v-71h-357q-81 0 -118 47.5t-37 126.5v352q0 79 37 126.5t118 47.5h357v-71h-198v-239h161zM189 629q-42 0 -60 -27t-18 -72v-360q0 -45 18 -72t60 -27h81v558h-81z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="374" 
d="M193 700q83 0 120 -45.5t37 -125.5v-84q0 -84 -39.5 -126.5t-123.5 -42.5h-69v-276h-78v700h153zM187 346q44 0 65 21.5t21 72.5v93q0 48 -18.5 72t-61.5 24h-75v-283h69z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="377" 
d="M193 599q83 0 120 -45.5t37 -126.5v-83q0 -85 -39.5 -127.5t-123.5 -42.5h-69v-174h-78v700h78v-101h75zM187 245q44 0 65 21.5t21 72.5v93q0 48 -18.5 72t-61.5 24h-75v-283h69z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="402" 
d="M194 707q81 0 121 -47.5t40 -128.5v-362q0 -67 -27 -109q6 -13 16 -17.5t27 -4.5h10v-69h-13q-64 0 -89 45q-17 -10 -38 -15.5t-47 -5.5q-81 0 -120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5
t62.5 -26.5q43 0 63 26.5t20 74.5v370q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="394" 
d="M196 700q83 0 119.5 -41t36.5 -119v-57q0 -58 -21 -94t-66 -50q48 -14 68 -51.5t20 -94.5v-109q0 -23 2 -44.5t12 -39.5h-79q-7 15 -10 32.5t-3 52.5v110q0 59 -26.5 81.5t-75.5 22.5h-55v-299h-78v700h156zM176 370q47 0 73 19.5t26 76.5v67q0 48 -18.5 72t-62.5 24h-76
v-259h58z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="394" 
d="M287 845l-93 -103h-54l69 103h78zM196 700q83 0 119.5 -41t36.5 -119v-57q0 -58 -21 -94t-66 -50q48 -14 68 -51.5t20 -94.5v-109q0 -23 2 -44.5t12 -39.5h-79q-7 15 -10 32.5t-3 52.5v110q0 59 -26.5 81.5t-75.5 22.5h-55v-299h-78v700h156zM176 370q47 0 73 19.5
t26 76.5v67q0 48 -18.5 72t-62.5 24h-76v-259h58z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="394" 
d="M133 842l53 -60l53 60h68l-87 -100h-69l-86 100h68zM196 700q83 0 119.5 -41t36.5 -119v-57q0 -58 -21 -94t-66 -50q48 -14 68 -51.5t20 -94.5v-109q0 -23 2 -44.5t12 -39.5h-79q-7 15 -10 32.5t-3 52.5v110q0 59 -26.5 81.5t-75.5 22.5h-55v-299h-78v700h156zM176 370
q47 0 73 19.5t26 76.5v67q0 48 -18.5 72t-62.5 24h-76v-259h58z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="372" 
d="M187 707q80 0 118 -47.5t38 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -80 -38.5 -127.5t-119.5 -47.5t-119 47.5t-38 127.5v40h73v-44q0 -48 20 -74t62 -26t62 26t20 74
q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="372" 
d="M289 845l-93 -103h-54l69 103h78zM187 707q80 0 118 -47.5t38 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -80 -38.5 -127.5t-119.5 -47.5t-119 47.5t-38 127.5v40h73v-44
q0 -48 20 -74t62 -26t62 26t20 74q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="372" 
d="M135 842l52 -60l53 60h69l-87 -100h-69l-86 100h68zM187 707q80 0 118 -47.5t38 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -80 -38.5 -127.5t-119.5 -47.5t-119 47.5t-38 127.5v40
h73v-44q0 -48 20 -74t62 -26t62 26t20 74q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="372" 
d="M201 -37q35 0 54.5 -13.5t19.5 -37.5q0 -30 -25 -47t-70 -17q-44 0 -68 16.5t-24 45.5v8h51v-7q0 -26 41 -29q43 0 43 32q0 30 -49 30h-9v50q-71 6 -103.5 52.5t-32.5 121.5v40h73v-44q0 -48 20 -74t62 -26t62 26t20 74q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5
t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47q80 0 118 -47.5t38 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -76 -35 -122.5t-108 -51.5v-31z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="372" 
d="M135 741h-68l87 101h69l86 -101h-68l-53 59zM187 707q80 0 118 -47.5t38 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -80 -38.5 -127.5t-119.5 -47.5t-119 47.5t-38 127.5v40h73v-44
q0 -48 20 -74t62 -26t62 26t20 74q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="363" 
d="M142 0v629h-129v71h336v-71h-129v-629h-78z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="372" 
d="M296 476v-58h-71v-418h-78v418h-71v58h71v153h-129v71h336v-71h-129v-153h71z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="372" 
d="M133 840l52 -60l54 60h68l-87 -101h-69l-86 101h68zM147 0v629h-129v71h336v-71h-129v-629h-78z" />
    <glyph glyph-name="U" unicode="U" 
d="M114 165q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" 
d="M296 845l-93 -103h-54l70 103h77zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" 
d="M299 838q-1 -48 -28.5 -75t-76.5 -27t-76 27t-28 75h47q2 -30 17.5 -41.5t39.5 -11.5t40 11.5t18 41.5h47zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" 
d="M142 741h-68l86 101h69l87 -101h-68l-54 59zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" 
d="M167 830v-90h-69v90h69zM291 830v-90h-68v90h68zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" 
d="M173 842l70 -103h-58l-93 103h81zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" 
d="M229 844l-80 -103h-49l57 103h72zM337 844l-81 -103h-48l56 103h73zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" 
d="M298 806v-56h-205v56h205zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" 
d="M350 169q0 -25 -3 -48.5t-11.5 -45t-23 -39.5t-37.5 -31q-20 -16 -30 -37.5t-9 -36.5q0 -16 11.5 -23t25.5 -7t23 2.5t17 6.5v-41q-15 -7 -29 -9.5t-33 -2.5q-33 0 -54 13.5t-21 42.5q-1 23 13.5 44.5t39.5 38.5q-15 -4 -36 -4q-80 0 -118 48.5t-38 128.5v531h77v-535
q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" 
d="M196 881q31 0 52.5 -21t21.5 -52t-21.5 -52t-52.5 -21t-52 21t-21 52t21 52t52 21zM196 844q-15 0 -25.5 -10t-10.5 -26t10.5 -26t25.5 -10t26 10t11 26t-11 26t-26 10zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5
t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" 
d="M317 802q-12 -30 -30.5 -43.5t-41.5 -13.5q-15 0 -26.5 4.5t-22 10t-20.5 10t-22 4.5q-14 0 -24 -6.5t-18 -23.5l-39 27q12 31 31 44t42 14q15 0 27 -4.5t22.5 -10.5t21 -10.5t21.5 -4.5q13 1 22.5 7.5t17.5 22.5zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5
t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="418" 
d="M99 700l114 -609l113 609h72l-131 -700h-115l-132 700h79z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="583" 
d="M226 0h-107l-96 700h76l82 -596l78 595h77l77 -596l79 597h68l-95 -700h-103l-67 475z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="583" 
d="M391 842l-93 -103h-55l70 103h78zM226 0h-107l-96 700h76l82 -596l78 595h77l77 -596l79 597h68l-95 -700h-103l-67 475z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="583" 
d="M240 740h-68l86 101h69l87 -101h-68l-53 59zM226 0h-107l-96 700h76l82 -596l78 595h77l77 -596l79 597h68l-95 -700h-103l-67 475z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="583" 
d="M265 831v-90h-68v90h68zM389 831v-90h-68v90h68zM226 0h-107l-96 700h76l82 -596l78 595h77l77 -596l79 597h68l-95 -700h-103l-67 475z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="583" 
d="M277 845l69 -103h-57l-93 103h81zM226 0h-107l-96 700h76l82 -596l78 595h77l77 -596l79 597h68l-95 -700h-103l-67 475z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="435" 
d="M214 302l-117 -302h-74l141 361l-132 339h81l108 -280l109 280h73l-131 -339l140 -361h-82z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="406" 
d="M164 251l-148 449h81l109 -344l110 344h74l-149 -449v-251h-77v251z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="406" 
d="M307 842l-93 -103h-54l70 103h77zM164 251l-148 449h81l109 -344l110 344h74l-149 -449v-251h-77v251z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="406" 
d="M153 740h-68l86 100h69l87 -100h-68l-53 59zM164 251l-148 449h81l109 -344l110 344h74l-149 -449v-251h-77v251z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="406" 
d="M177 829v-90h-68v90h68zM301 829v-90h-68v90h68zM164 251l-148 449h81l109 -344l110 344h74l-149 -449v-251h-77v251z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="406" 
d="M186 845l69 -103h-57l-93 103h81zM164 251l-148 449h81l109 -344l110 344h74l-149 -449v-251h-77v251z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="379" 
d="M347 0h-318v70l240 559h-229v71h309v-70l-241 -559h239v-71z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="379" 
d="M297 845l-93 -103h-54l69 103h78zM347 0h-318v70l240 559h-229v71h309v-70l-241 -559h239v-71z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="379" 
d="M141 842l52 -60l54 60h68l-87 -101h-69l-86 101h68zM347 0h-318v70l240 559h-229v71h309v-70l-241 -559h239v-71z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="379" 
d="M231 832v-90h-68v90h68zM347 0h-318v70l240 559h-229v71h309v-70l-241 -559h239v-71z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="383" 
d="M191 707q81 0 119.5 -48t38.5 -128v-60h-74v64q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v140h-72v70h146v-206q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v362q0 40 9.5 72.5t29 55.5
t49 35.5t69.5 12.5zM183 -121h-25v88h66v-81l-35 -63h-29z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="409" 
d="M159 304l-41 -69v-235h-78v700h78v-338l186 338h79l-177 -319l183 -381h-80zM197 -121h-25v88h66v-81l-35 -63h-29z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="337" 
d="M40 0v700h78v-629h200v-71h-278zM174 -122h-25v87h66v-81l-35 -63h-29z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="409" 
d="M109 0h-70v700h99l162 -506v506h69v-700h-81l-179 566v-566zM196 -121h-25v88h65v-81l-35 -63h-28z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="394" 
d="M196 700q83 0 119.5 -41t36.5 -119v-57q0 -58 -21 -94t-66 -50q48 -14 68 -51.5t20 -94.5v-109q0 -23 2 -44.5t12 -39.5h-79q-7 15 -10 32.5t-3 52.5v110q0 59 -26.5 81.5t-75.5 22.5h-55v-299h-78v700h156zM176 370q47 0 73 19.5t26 76.5v67q0 48 -18.5 72t-62.5 24h-76
v-259h58zM188 -120h-24v87h65v-81l-35 -63h-29z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="372" 
d="M204 0v-38q29 0 48 -13.5t19 -42.5q0 -20 -7.5 -34.5t-19.5 -23.5t-28 -12.5t-33 -3.5q-16 0 -31.5 4t-27.5 12t-19.5 20.5t-7.5 29.5v8h47v-8q0 -17 12.5 -23t25.5 -6q16 0 28.5 8t12.5 26q0 20 -13.5 26.5t-33.5 6.5h-9v64h-20v629h-129v71h336v-71h-129v-629h-21z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="372" 
d="M187 707q80 0 118 -47.5t38 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -80 -38.5 -127.5t-119.5 -47.5t-119 47.5t-38 127.5v40h73v-44q0 -48 20 -74t62 -26t62 26t20 74
q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47zM176 -121h-25v88h66v-81l-35 -63h-29z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="372" 
d="M147 0v629h-129v71h336v-71h-129v-629h-78zM178 -121h-25v88h66v-81l-35 -63h-29z" />
    <glyph glyph-name="uni1E02" unicode="&#x1e02;" horiz-adv-x="392" 
d="M212 830v-90h-68v90h68zM197 700q82 0 117 -38.5t35 -116.5v-33q0 -56 -18 -90.5t-64 -48.5q51 -14 72.5 -53t21.5 -97v-59q0 -79 -39 -121.5t-121 -42.5h-161v700h157zM178 403q47 0 70.5 19t23.5 74v41q0 47 -17.5 69.5t-59.5 22.5h-77v-226h60zM201 71q83 0 83 95v62
q0 59 -23.5 81.5t-74.5 22.5h-68v-261h83z" />
    <glyph glyph-name="uni1E0A" unicode="&#x1e0a;" horiz-adv-x="396" 
d="M219 830v-90h-68v90h68zM204 700q82 0 120.5 -46.5t38.5 -127.5v-353q0 -81 -38.5 -127t-120.5 -46h-164v700h164zM203 71q42 0 62.5 25.5t20.5 73.5v360q0 48 -20.5 73.5t-63.5 25.5h-84v-558h85z" />
    <glyph glyph-name="uni1E1E" unicode="&#x1e1e;" horiz-adv-x="340" 
d="M211 829v-90h-68v90h68zM279 374v-71h-161v-303h-78v700h278v-71h-200v-255h161z" />
    <glyph glyph-name="uni1E40" unicode="&#x1e40;" horiz-adv-x="523" 
d="M295 832v-91h-68v91h68zM298 1h-75l-116 557v-558h-68v700h109l115 -559l111 559h109v-700h-74v563z" />
    <glyph glyph-name="uni1E56" unicode="&#x1e56;" horiz-adv-x="371" 
d="M214 830v-90h-68v90h68zM193 700q83 0 120 -45.5t37 -125.5v-84q0 -84 -39.5 -126.5t-123.5 -42.5h-69v-276h-78v700h153zM187 346q44 0 65 21.5t21 72.5v93q0 48 -18.5 72t-61.5 24h-75v-283h69z" />
    <glyph glyph-name="uni1E60" unicode="&#x1e60;" horiz-adv-x="372" 
d="M220 830v-90h-68v90h68zM187 707q80 0 118 -47.5t38 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -80 -38.5 -127.5t-119.5 -47.5t-119 47.5t-38 127.5v40h73v-44q0 -48 20 -74
t62 -26t62 26t20 74q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47z" />
    <glyph glyph-name="uni1E6A" unicode="&#x1e6a;" horiz-adv-x="372" 
d="M220 830v-90h-68v90h68zM147 0v629h-129v71h336v-71h-129v-629h-78z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="422" 
d="M119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="422" 
d="M305 840l-92 -103h-55l70 103h77zM119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="422" 
d="M314 834q0 -47 -28 -74t-77 -27t-75.5 27t-27.5 74h47q2 -29 17.5 -40.5t38.5 -11.5q24 0 40 11.5t18 40.5h47zM119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="422" 
d="M157 737h-68l86 100h70l86 -100h-68l-53 59zM119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="422" 
d="M182 827v-90h-68v90h68zM306 827v-90h-68v90h68zM119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="422" 
d="M190 840l70 -103h-57l-93 103h80zM119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="422" 
d="M316 806v-56h-212v56h212zM119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="422" 
d="M373 0q-18 -15 -27.5 -35t-8.5 -34q1 -16 12 -23t25 -7t23 2.5t17 6.5v-41q-15 -7 -29 -9.5t-33 -2.5q-33 0 -53.5 13.5t-21.5 42.5q-1 21 12 42t36 38l-29 148h-177l-27 -141h-72l134 700h113l134 -700h-28zM129 208h156l-79 404z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="422" 
d="M210 883q31 0 52 -21t21 -52q0 -32 -21 -52.5t-52 -20.5q-32 0 -53 20.5t-21 52.5q0 31 21 52t53 21zM210 846q-16 0 -26.5 -10.5t-10.5 -25.5q0 -16 10.5 -26t26.5 -10q15 0 25.5 10t10.5 26q0 15 -10.5 25.5t-25.5 10.5zM119 141l-27 -141h-72l134 700h113l134 -700
h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="422" 
d="M161 826q15 0 27.5 -4.5t23 -10.5t20.5 -10.5t21 -4.5q13 1 22.5 7.5t17.5 22.5l39 -27q-12 -29 -30 -43t-41 -14q-15 0 -27 4.5t-22.5 10t-20.5 10t-22 4.5q-14 0 -24 -6.5t-18 -23.5l-39 27q12 31 31 44t42 14zM119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141
h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="585" 
d="M516 390v-70h-161v-249h198v-71h-275v142h-143l-45 -142h-74l218 700h319v-71h-198v-239h161zM153 210h125v392z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="585" 
d="M410 844l-93 -103h-54l70 103h77zM516 390v-70h-161v-249h198v-71h-275v142h-143l-45 -142h-74l218 700h319v-71h-198v-239h161zM153 210h125v392z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="392" 
d="M197 700q82 0 117 -38.5t35 -116.5v-33q0 -56 -18 -90.5t-64 -48.5q51 -14 72.5 -53t21.5 -97v-59q0 -79 -39 -121.5t-121 -42.5h-161v700h157zM178 403q47 0 70.5 19t23.5 74v41q0 47 -17.5 69.5t-59.5 22.5h-77v-226h60zM201 71q83 0 83 95v62q0 59 -23.5 81.5
t-74.5 22.5h-68v-261h83z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="380" 
d="M191 707q81 0 119.5 -48t38.5 -128v-63h-74v67q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v91h74v-87q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v362q0 40 9.5 72.5t29 55.5t49 35.5
t69.5 12.5z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="380" 
d="M293 845l-93 -103h-55l70 103h78zM191 707q81 0 119.5 -48t38.5 -128v-63h-74v67q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v91h74v-87q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v362
q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="380" 
d="M141 841l52 -60l53 60h69l-87 -100h-69l-86 100h68zM191 707q81 0 119.5 -48t38.5 -128v-63h-74v67q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v91h74v-87q0 -80 -38.5 -128t-119.5 -48
q-80 0 -118.5 48t-38.5 128v362q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="380" 
d="M191 707q81 0 119.5 -48t38.5 -128v-63h-74v67q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v91h74v-87q0 -75 -34.5 -122.5t-106.5 -52.5v-31q35 0 54.5 -13.5t19.5 -37.5q0 -30 -25 -47t-70 -17
q-44 0 -68 16.5t-24 45.5v8h51v-7q0 -26 41 -29q43 0 43 32q0 30 -49 30h-9v50q-71 5 -104.5 53t-33.5 122v362q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="380" 
d="M140 740h-68l86 101h69l87 -101h-68l-53 59zM191 707q81 0 119.5 -48t38.5 -128v-63h-74v67q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v91h74v-87q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48
t-38.5 128v362q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="380" 
d="M225 828v-90h-68v90h68zM191 707q81 0 119.5 -48t38.5 -128v-63h-74v67q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v91h74v-87q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v362
q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="396" 
d="M204 700q82 0 120.5 -46.5t38.5 -127.5v-353q0 -81 -38.5 -127t-120.5 -46h-164v700h164zM203 71q42 0 62.5 25.5t20.5 73.5v360q0 48 -20.5 73.5t-63.5 25.5h-84v-558h85z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="396" 
d="M204 700q82 0 120.5 -46.5t38.5 -127.5v-353q0 -81 -38.5 -127t-120.5 -46h-164v318h-39v63h39v319h164zM197 318h-79v-247h85q42 0 62.5 25.5t20.5 73.5v360q0 48 -20.5 73.5t-63.5 25.5h-84v-248h79v-63z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="396" 
d="M135 839l53 -60l53 60h69l-87 -100h-69l-87 100h68zM204 700q82 0 120.5 -46.5t38.5 -127.5v-353q0 -81 -38.5 -127t-120.5 -46h-164v700h164zM203 71q42 0 62.5 25.5t20.5 73.5v360q0 48 -20.5 73.5t-63.5 25.5h-84v-558h85z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="396" 
d="M204 700q82 0 120.5 -46.5t38.5 -127.5v-353q0 -81 -38.5 -127t-120.5 -46h-164v318h-39v63h39v319h164zM197 318h-79v-247h85q42 0 62.5 25.5t20.5 73.5v360q0 48 -20.5 73.5t-63.5 25.5h-84v-248h79v-63z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="360" 
d="M289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="360" 
d="M283 842l-93 -103h-54l70 103h77zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="360" 
d="M287 840q-1 -48 -28.5 -74.5t-77.5 -26.5q-48 0 -75 26.5t-28 74.5h47q2 -29 17.5 -40.5t38.5 -11.5q25 0 41 11.5t18 40.5h47zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="360" 
d="M131 839l52 -60l54 60h68l-87 -100h-69l-86 100h68zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="360" 
d="M130 739h-68l86 101h69l87 -101h-68l-54 60zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="360" 
d="M155 829v-90h-68v90h68zM279 829v-90h-68v90h68zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="360" 
d="M218 830v-90h-68v90h68zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="360" 
d="M167 845l69 -103h-57l-93 103h81zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="360" 
d="M286 807v-55h-209v55h209zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="360" 
d="M289 390v-70h-171v-249h210v-71h-33q-19 -15 -28 -35t-9 -34q1 -16 12 -23t26 -7q14 0 22.5 2.5t16.5 6.5v-41q-15 -7 -29 -9.5t-32 -2.5q-34 0 -54.5 13.5t-21.5 42.5q0 24 16.5 47t44.5 40h-219v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="340" 
d="M279 374v-71h-161v-303h-78v700h278v-71h-200v-255h161z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="383" 
d="M191 707q81 0 119.5 -48t38.5 -128v-60h-74v64q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v140h-72v70h146v-206q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v362q0 40 9.5 72.5t29 55.5
t49 35.5t69.5 12.5z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="383" 
d="M296 841q-1 -48 -28.5 -74.5t-76.5 -26.5t-76 26.5t-28 74.5h47q2 -29 17.5 -40.5t39.5 -11.5t40 11.5t18 40.5h47zM191 707q81 0 119.5 -48t38.5 -128v-60h-74v64q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5
t19.5 74.5v140h-72v70h146v-206q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v362q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="383" 
d="M139 742h-68l86 100h69l87 -100h-68l-54 59zM191 707q81 0 119.5 -48t38.5 -128v-60h-74v64q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v140h-72v70h146v-206q0 -80 -38.5 -128t-119.5 -48
q-80 0 -118.5 48t-38.5 128v362q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="383" 
d="M228 830v-90h-68v90h68zM191 707q81 0 119.5 -48t38.5 -128v-60h-74v64q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v140h-72v70h146v-206q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128
v362q0 40 9.5 72.5t29 55.5t49 35.5t69.5 12.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="408" 
d="M118 315v-315h-78v700h78v-315h172v315h78v-700h-78v315h-172z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="415" 
d="M372 514v-514h-79v315h-172v-315h-78v514h-36v58h36v128h78v-128h172v128h79v-128h36v-58h-36zM293 514h-172v-129h172v129z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="408" 
d="M151 741h-68l87 101h69l86 -101h-68l-53 59zM118 315v-315h-78v700h78v-315h172v315h78v-700h-78v315h-172z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="158" 
d="M118 700v-700h-78v700h78z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="158" 
d="M118 700v-700h-78v700h78z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="158" 
d="M100 842h78l-93 -103h-54zM40 700h78v-700h-78v700z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="158" 
d="M183 838q0 -48 -28 -75t-77 -27t-75.5 27t-27.5 75h47q2 -30 17.5 -41.5t38.5 -11.5q24 0 40 11.5t18 41.5h47zM40 700h78v-700h-78v700z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="158" 
d="M113 839l87 -100h-68l-53 59l-53 -59h-68l86 100h69zM40 700h78v-700h-78v700z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="158" 
d="M51 829v-90h-68v90h68zM175 829v-90h-68v90h68zM118 700v-700h-78v700h78z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="158" 
d="M-25 842h81l70 -103h-58zM40 700h78v-700h-78v700z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="412" 
d="M118 700v-700h-78v700h78zM376 173q0 -50 -11.5 -83t-32 -53t-48 -28.5t-59.5 -9.5h-24.5t-23.5 1v71q10 -1 19.5 -1h18.5q40 2 61.5 23.5t21.5 74.5v532h78v-527z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="158" 
d="M178 806v-56h-199v56h199zM118 700v-700h-78v700h78z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="158" 
d="M104 0q-19 -15 -28 -35t-8 -34q0 -16 11 -23t26 -7q14 0 22.5 2.5t17.5 6.5v-41q-15 -7 -29.5 -9.5t-32.5 -2.5q-33 0 -54 13.5t-21 42.5q-1 23 15 46t46 41h-29v700h78v-700h-14z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="158" 
d="M201 802q-22 -58 -71 -58q-15 0 -26.5 4.5t-22.5 10t-21 10t-22 4.5q-14 0 -24 -6t-18 -23l-39 26q13 31 32 44.5t42 13.5q15 0 27 -4.5t22.5 -10t21 -10t21.5 -4.5q13 0 22.5 6.5t17.5 22.5zM118 700v-700h-78v700h78z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="255" 
d="M20 71q10 -1 20 -1h19q38 2 60 23.5t22 74.5v532h77v-527q0 -49 -11.5 -82t-31.5 -53.5t-47.5 -29t-58.5 -9.5q-14 0 -25 -0.5t-24 1.5v71z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="255" 
d="M20 71q10 -1 20 -1h19q38 2 60 23.5t22 74.5v532h77v-527q0 -49 -11.5 -82t-31.5 -53.5t-47.5 -29t-58.5 -9.5q-14 0 -25 -0.5t-24 1.5v71z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="255" 
d="M114 739h-68l86 101h69l87 -101h-68l-53 60zM218 173q0 -49 -11.5 -82t-31.5 -53.5t-47.5 -29t-58.5 -9.5q-14 0 -25 -0.5t-24 1.5v71q10 -1 20 -1h19q38 2 60 23.5t22 74.5v532h77v-527z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="409" 
d="M159 304l-41 -69v-235h-78v700h78v-338l186 338h79l-177 -319l183 -381h-80z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="409" 
d="M159 304l-41 -69v-235h-78v700h78v-338l186 338h79l-177 -319l183 -381h-80z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="337" 
d="M40 0v700h78v-629h200v-71h-278z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="337" 
d="M203 842l-93 -103h-54l69 103h78zM40 0v700h78v-629h200v-71h-278z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="337" 
d="M40 0v700h78v-629h200v-71h-278zM242 603h-29v97h75v-89l-38 -96h-33z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="337" 
d="M40 0v700h78v-629h200v-71h-278zM272 400v-97h-75v97h75z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="348" 
d="M128 291v-220h201v-71h-278v208l-51 -50v72l51 50v420h77v-337l109 113v-72z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="523" 
d="M298 1h-75l-116 557v-558h-68v700h109l115 -559l111 559h109v-700h-74v563z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="409" 
d="M109 0h-70v700h99l162 -506v506h69v-700h-81l-179 566v-566z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="409" 
d="M306 842l-93 -102h-54l70 102h77zM109 0h-70v700h99l162 -506v506h69v-700h-81l-179 567v-567z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="503" 
d="M40 603h-29v97h75v-89l-38 -96h-33zM204 0h-70v700h99l162 -506v506h69v-700h-81l-179 566v-566z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="409" 
d="M152 842l53 -60l53 60h68l-86 -101h-69l-87 101h68zM109 0h-70v700h99l162 -506v506h69v-700h-81l-179 566v-566z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="409" 
d="M327 804q-12 -30 -30 -44t-41 -14q-15 0 -27 4.5t-22.5 10t-20.5 10t-22 4.5q-14 0 -24 -6t-18 -23l-39 26q12 31 31 44.5t43 13.5q15 0 27 -4.5t22.5 -10t20.5 -10t21 -4.5q26 0 40 29zM109 0h-70v700h99l162 -506v506h69v-700h-81l-179 566v-566z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="389" 
d="M34 531q0 81 39.5 128.5t120.5 47.5t121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362zM111 165q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370q0 48 -20 74.5t-63 26.5q-42 0 -62.5 -26.5t-20.5 -74.5v-370z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="389" 
d="M293 845l-93 -103h-54l70 103h77zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370
q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="389" 
d="M299 841q-1 -48 -28.5 -74.5t-77.5 -26.5q-48 0 -75 26.5t-28 74.5h47q2 -29 17.5 -40.5t38.5 -11.5q25 0 41 11.5t18 40.5h47zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636
q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="389" 
d="M141 741h-68l86 101h69l87 -101h-68l-54 59zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5
v370q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="389" 
d="M166 831v-90h-68v90h68zM290 831v-90h-68v90h68zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5
t20 74.5v370q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="389" 
d="M174 845l69 -103h-57l-93 103h81zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370
q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="389" 
d="M230 845l-80 -103h-49l57 103h72zM338 845l-81 -103h-49l57 103h73zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5
q43 0 63 26.5t20 74.5v370q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="389" 
d="M299 806v-55h-208v55h208zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370q0 48 -20 74.5
t-63 26.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="389" 
d="M325 646q30 -45 30 -115v-362q0 -80 -40 -128t-121 -48q-63 0 -102 31l-19 -50l-34 13l25 67q-30 45 -30 115v362q0 81 39.5 128.5t120.5 47.5q63 0 103 -31l18 49l35 -13zM266 594q-20 42 -72 42q-42 0 -62.5 -26.5t-20.5 -74.5v-355zM194 64q43 0 63 26.5t20 74.5v353
l-155 -413q20 -41 72 -41z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="389" 
d="M219 845h78l-93 -103h-55zM325 646q30 -45 30 -115v-362q0 -80 -40 -128t-121 -48q-63 0 -102 31l-19 -50l-34 13l25 67q-30 45 -30 115v362q0 81 39.5 128.5t120.5 47.5q63 0 103 -31l18 49l35 -13zM266 594q-20 42 -72 42q-42 0 -62.5 -26.5t-20.5 -74.5v-355zM194 64
q43 0 63 26.5t20 74.5v353l-155 -413q20 -41 72 -41z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="389" 
d="M318 802q-11 -30 -29.5 -43.5t-41.5 -13.5q-15 0 -26.5 4.5t-22.5 10t-21 10t-22 4.5q-14 0 -24 -6.5t-18 -23.5l-39 27q13 31 32 44t42 14q15 0 27 -4.5t22.5 -10.5t21 -10.5t21.5 -4.5q13 1 22.5 7.5t17.5 22.5zM194 707q81 0 121 -47.5t40 -128.5v-362q0 -80 -40 -128
t-121 -48t-120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="577" 
d="M508 390v-70h-161v-249h198v-71h-357q-81 0 -118 47.5t-37 126.5v352q0 79 37 126.5t118 47.5h357v-71h-198v-239h161zM189 629q-42 0 -60 -27t-18 -72v-360q0 -45 18 -72t60 -27h81v558h-81z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="371" 
d="M193 700q83 0 120 -45.5t37 -125.5v-84q0 -84 -39.5 -126.5t-123.5 -42.5h-69v-276h-78v700h153zM187 346q44 0 65 21.5t21 72.5v93q0 48 -18.5 72t-61.5 24h-75v-283h69z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="377" 
d="M193 599q83 0 120 -45.5t37 -126.5v-83q0 -85 -39.5 -127.5t-123.5 -42.5h-69v-174h-78v700h78v-101h75zM187 245q44 0 65 21.5t21 72.5v93q0 48 -18.5 72t-61.5 24h-75v-283h69z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="392" 
d="M194 707q81 0 121 -47.5t40 -128.5v-362q0 -67 -27 -109q6 -13 16 -17.5t27 -4.5h10v-69h-13q-64 0 -89 45q-17 -10 -38 -15.5t-47 -5.5q-81 0 -120.5 48t-39.5 128v362q0 81 39.5 128.5t120.5 47.5zM194 636q-42 0 -62.5 -26.5t-20.5 -74.5v-370q0 -48 20.5 -74.5
t62.5 -26.5q43 0 63 26.5t20 74.5v370q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="394" 
d="M196 700q83 0 119.5 -41t36.5 -119v-57q0 -58 -21 -94t-66 -50q48 -14 68 -51.5t20 -94.5v-109q0 -23 2 -44.5t12 -39.5h-79q-7 15 -10 32.5t-3 52.5v110q0 59 -26.5 81.5t-75.5 22.5h-55v-299h-78v700h156zM176 370q47 0 73 19.5t26 76.5v67q0 48 -18.5 72t-62.5 24h-76
v-259h58z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="394" 
d="M287 845l-93 -103h-54l69 103h78zM196 700q83 0 119.5 -41t36.5 -119v-57q0 -58 -21 -94t-66 -50q48 -14 68 -51.5t20 -94.5v-109q0 -23 2 -44.5t12 -39.5h-79q-7 15 -10 32.5t-3 52.5v110q0 59 -26.5 81.5t-75.5 22.5h-55v-299h-78v700h156zM176 370q47 0 73 19.5
t26 76.5v67q0 48 -18.5 72t-62.5 24h-76v-259h58z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="394" 
d="M133 842l53 -60l53 60h68l-87 -100h-69l-86 100h68zM196 700q83 0 119.5 -41t36.5 -119v-57q0 -58 -21 -94t-66 -50q48 -14 68 -51.5t20 -94.5v-109q0 -23 2 -44.5t12 -39.5h-79q-7 15 -10 32.5t-3 52.5v110q0 59 -26.5 81.5t-75.5 22.5h-55v-299h-78v700h156zM176 370
q47 0 73 19.5t26 76.5v67q0 48 -18.5 72t-62.5 24h-76v-259h58z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="372" 
d="M187 707q80 0 118 -47.5t38 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -80 -38.5 -127.5t-119.5 -47.5t-119 47.5t-38 127.5v40h73v-44q0 -48 20 -74t62 -26t62 26t20 74
q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="372" 
d="M289 845l-93 -103h-54l69 103h78zM187 707q80 0 118 -47.5t38 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -80 -38.5 -127.5t-119.5 -47.5t-119 47.5t-38 127.5v40h73v-44
q0 -48 20 -74t62 -26t62 26t20 74q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="372" 
d="M134 842l53 -60l53 60h68l-87 -100h-69l-86 100h68zM187 707q80 0 118 -47.5t38 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -80 -38.5 -127.5t-119.5 -47.5t-119 47.5t-38 127.5v40
h73v-44q0 -48 20 -74t62 -26t62 26t20 74q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="372" 
d="M201 -37q35 0 54.5 -13.5t19.5 -37.5q0 -30 -25 -47t-70 -17q-44 0 -68 16.5t-24 45.5v8h51v-7q0 -26 41 -29q43 0 43 32q0 30 -49 30h-9v50q-71 6 -103.5 52.5t-32.5 121.5v40h73v-44q0 -48 20 -74t62 -26t62 26t20 74q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5
t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47q80 0 118 -47.5t38 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -76 -35 -122.5t-108 -51.5v-31z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="372" 
d="M135 741h-68l87 101h69l86 -101h-68l-53 59zM187 707q80 0 118 -47.5t38 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -80 -38.5 -127.5t-119.5 -47.5t-119 47.5t-38 127.5v40h73v-44
q0 -48 20 -74t62 -26t62 26t20 74q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="729" 
d="M185 707q81 0 118.5 -47.5t37.5 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -80 -38.5 -127.5t-119.5 -47.5t-119 47.5t-38 127.5v40h73v-44q0 -48 20 -74t62 -26q43 0 62.5 26
t19.5 74q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47zM546 707q80 0 117.5 -47.5t37.5 -127.5v-19h-73v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56 -48t56 -51.5t43.5 -63.5t17.5 -84.5
q0 -80 -38 -127.5t-119 -47.5t-119.5 47.5t-38.5 127.5v40h74v-44q0 -48 20 -74t62 -26t62 26t20 74q0 39 -17.5 67.5t-43.5 53t-56.5 48t-56.5 51.5t-43.5 63.5t-17.5 84.5q0 81 38.5 128t117.5 47z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="360" 
d="M141 0v629h-129v71h335v-71h-129v-629h-77z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="372" 
d="M296 476v-58h-71v-418h-78v418h-71v58h71v153h-129v71h336v-71h-129v-153h71z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="372" 
d="M133 840l52 -60l54 60h68l-87 -101h-69l-86 101h68zM147 0v629h-129v71h336v-71h-129v-629h-78z" />
    <glyph glyph-name="u" unicode="u" 
d="M114 165q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" 
d="M296 845l-93 -103h-54l70 103h77zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" 
d="M299 838q-1 -48 -28.5 -75t-76.5 -27t-76 27t-28 75h47q2 -30 17.5 -41.5t39.5 -11.5t40 11.5t18 41.5h47zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" 
d="M142 741h-68l86 101h69l87 -101h-68l-54 59zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" 
d="M167 830v-90h-69v90h69zM291 830v-90h-68v90h68zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" 
d="M173 842l70 -103h-58l-93 103h81zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" 
d="M229 844l-80 -103h-49l57 103h72zM337 844l-81 -103h-48l56 103h73zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" 
d="M298 806v-56h-205v56h205zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="383" 
d="M191 707q81 0 119.5 -48t38.5 -128v-60h-74v64q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v140h-72v70h146v-206q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v362q0 40 9.5 72.5t29 55.5
t49 35.5t69.5 12.5zM183 -121h-24v88h65v-81l-35 -63h-28z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="409" 
d="M159 304l-41 -69v-235h-78v700h78v-338l186 338h79l-177 -319l183 -381h-80zM197 -121h-25v88h66v-81l-35 -63h-29z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="337" 
d="M40 0v700h78v-629h200v-71h-278zM177 -121h-24v88h65v-81l-35 -63h-28z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" horiz-adv-x="409" 
d="M109 0h-70v700h99l162 -506v506h69v-700h-81l-179 566v-566zM196 -121h-25v88h65v-81l-35 -63h-28z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="394" 
d="M196 700q83 0 119.5 -41t36.5 -119v-57q0 -58 -21 -94t-66 -50q48 -14 68 -51.5t20 -94.5v-109q0 -23 2 -44.5t12 -39.5h-79q-7 15 -10 32.5t-3 52.5v110q0 59 -26.5 81.5t-75.5 22.5h-55v-299h-78v700h156zM176 370q47 0 73 19.5t26 76.5v67q0 48 -18.5 72t-62.5 24h-76
v-259h58zM189 -121h-25v88h66v-81l-35 -63h-29z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="372" 
d="M204 0v-38q29 0 48 -13.5t19 -42.5q0 -20 -7.5 -34.5t-19.5 -23.5t-28 -12.5t-33 -3.5q-16 0 -31.5 4t-27.5 12t-19.5 20.5t-7.5 29.5v8h47v-8q0 -17 12.5 -23t25.5 -6q16 0 28.5 8t12.5 26q0 20 -13.5 26.5t-33.5 6.5h-9v64h-20v629h-129v71h336v-71h-129v-629h-21z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="372" 
d="M187 707q80 0 118 -47.5t38 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -80 -38.5 -127.5t-119.5 -47.5t-119 47.5t-38 127.5v40h73v-44q0 -48 20 -74t62 -26t62 26t20 74
q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47zM176 -121h-25v88h66v-81l-35 -63h-29z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="372" 
d="M147 0v629h-129v71h336v-71h-129v-629h-78zM178 -121h-25v88h66v-81l-35 -63h-29z" />
    <glyph glyph-name="uni1E03" unicode="&#x1e03;" horiz-adv-x="392" 
d="M212 830v-90h-68v90h68zM197 700q82 0 117 -38.5t35 -116.5v-33q0 -56 -18 -90.5t-64 -48.5q51 -14 72.5 -53t21.5 -97v-59q0 -79 -39 -121.5t-121 -42.5h-161v700h157zM178 403q47 0 70.5 19t23.5 74v41q0 47 -17.5 69.5t-59.5 22.5h-77v-226h60zM201 71q83 0 83 95v62
q0 59 -23.5 81.5t-74.5 22.5h-68v-261h83z" />
    <glyph glyph-name="uni1E0B" unicode="&#x1e0b;" horiz-adv-x="396" 
d="M219 830v-90h-68v90h68zM204 700q82 0 120.5 -46.5t38.5 -127.5v-353q0 -81 -38.5 -127t-120.5 -46h-164v700h164zM203 71q42 0 62.5 25.5t20.5 73.5v360q0 48 -20.5 73.5t-63.5 25.5h-84v-558h85z" />
    <glyph glyph-name="uni1E1F" unicode="&#x1e1f;" horiz-adv-x="340" 
d="M214 830v-90h-68v90h68zM279 374v-71h-161v-303h-78v700h278v-71h-200v-255h161z" />
    <glyph glyph-name="uni1E41" unicode="&#x1e41;" horiz-adv-x="523" 
d="M295 832v-90h-68v90h68zM298 1h-75l-116 557v-558h-68v700h109l115 -559l111 559h109v-700h-74v563z" />
    <glyph glyph-name="uni1E57" unicode="&#x1e57;" horiz-adv-x="371" 
d="M217 830v-90h-68v90h68zM193 700q83 0 120 -45.5t37 -125.5v-84q0 -84 -39.5 -126.5t-123.5 -42.5h-69v-276h-78v700h153zM187 346q44 0 65 21.5t21 72.5v93q0 48 -18.5 72t-61.5 24h-75v-283h69z" />
    <glyph glyph-name="uni1E61" unicode="&#x1e61;" horiz-adv-x="372" 
d="M223 833v-90h-68v90h68zM187 707q80 0 118 -47.5t38 -127.5v-19h-74v23q0 48 -19 74t-61 26t-61 -26t-19 -73q0 -39 17.5 -68t43.5 -53.5t56.5 -48t56.5 -51.5t43.5 -63.5t17.5 -84.5q0 -80 -38.5 -127.5t-119.5 -47.5t-119 47.5t-38 127.5v40h73v-44q0 -48 20 -74
t62 -26t62 26t20 74q0 39 -17.5 67.5t-43.5 53t-56 48t-56 51.5t-43.5 63.5t-17.5 84.5q0 81 38 128t117 47z" />
    <glyph glyph-name="uni1E6B" unicode="&#x1e6b;" horiz-adv-x="372" 
d="M219 830v-90h-68v90h68zM146 0v629h-129v71h336v-71h-129v-629h-78z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" 
d="M350 169q0 -25 -3 -48.5t-11.5 -45t-23 -39.5t-37.5 -31q-20 -16 -30 -37.5t-9 -36.5q0 -16 11.5 -23t25.5 -7t23 2.5t17 6.5v-41q-15 -7 -29 -9.5t-33 -2.5q-33 0 -54 13.5t-21 42.5q-1 23 13.5 44.5t39.5 38.5q-15 -4 -36 -4q-80 0 -118 48.5t-38 128.5v531h77v-535
q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="uring" unicode="&#x16f;" 
d="M196 881q31 0 52.5 -21t21.5 -52t-21.5 -52t-52.5 -21t-52 21t-21 52t21 52t52 21zM196 844q-15 0 -25.5 -10t-10.5 -26t10.5 -26t25.5 -10t26 10t11 26t-11 26t-26 10zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5t-38 128.5v531h77v-535q0 -48 19.5 -74.5
t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="utilde" unicode="&#x169;" 
d="M317 802q-12 -30 -30.5 -43.5t-41.5 -13.5q-15 0 -26.5 4.5t-22 10t-20.5 10t-22 4.5q-14 0 -24 -6.5t-18 -23.5l-39 27q12 31 31 44t42 14q15 0 27 -4.5t22.5 -10.5t21 -10.5t21.5 -4.5q13 1 22.5 7.5t17.5 22.5zM350 169q0 -80 -38 -128.5t-119 -48.5q-80 0 -118 48.5
t-38 128.5v531h77v-535q0 -48 19.5 -74.5t62.5 -26.5q42 0 61.5 26.5t19.5 74.5v535h73v-531z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="418" 
d="M99 700l114 -609l113 609h72l-131 -700h-115l-132 700h79z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="583" 
d="M226 0h-107l-96 700h76l82 -596l78 595h77l77 -596l79 597h68l-95 -700h-103l-67 475z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="583" 
d="M391 842l-93 -103h-55l70 103h78zM226 0h-107l-96 700h76l82 -596l78 595h77l77 -596l79 597h68l-95 -700h-103l-67 475z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="583" 
d="M240 740h-68l86 101h69l87 -101h-68l-53 59zM226 0h-107l-96 700h76l82 -596l78 595h77l77 -596l79 597h68l-95 -700h-103l-67 475z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="583" 
d="M265 831v-90h-68v90h68zM389 831v-90h-68v90h68zM226 0h-107l-96 700h76l82 -596l78 595h77l77 -596l79 597h68l-95 -700h-103l-67 475z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="583" 
d="M277 845l69 -103h-57l-93 103h81zM226 0h-107l-96 700h76l82 -596l78 595h77l77 -596l79 597h68l-95 -700h-103l-67 475z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="435" 
d="M214 302l-117 -302h-74l141 361l-132 339h81l108 -280l109 280h73l-131 -339l140 -361h-82z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="406" 
d="M164 251l-148 449h81l109 -344l110 344h74l-149 -449v-251h-77v251z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="406" 
d="M307 842l-93 -103h-54l70 103h77zM164 251l-148 449h81l109 -344l110 344h74l-149 -449v-251h-77v251z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="406" 
d="M153 740h-68l86 100h69l87 -100h-68l-53 59zM164 251l-148 449h81l109 -344l110 344h74l-149 -449v-251h-77v251z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="406" 
d="M177 829v-90h-68v90h68zM301 829v-90h-68v90h68zM164 251l-148 449h81l109 -344l110 344h74l-149 -449v-251h-77v251z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="406" 
d="M186 845l69 -103h-57l-93 103h81zM164 251l-148 449h81l109 -344l110 344h74l-149 -449v-251h-77v251z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="379" 
d="M347 0h-318v70l240 559h-229v71h309v-70l-241 -559h239v-71z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="379" 
d="M297 845l-93 -103h-54l69 103h78zM347 0h-318v70l240 559h-229v71h309v-70l-241 -559h239v-71z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="379" 
d="M141 842l52 -60l54 60h68l-87 -101h-69l-86 101h68zM347 0h-318v70l240 559h-229v71h309v-70l-241 -559h239v-71z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="379" 
d="M231 832v-90h-68v90h68zM347 0h-318v70l240 559h-229v71h309v-70l-241 -559h239v-71z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="275" 
d="M147 705q51 0 71 -30t20 -80v-261h-43l-3 55q-9 -28 -28.5 -44t-49.5 -16q-41 0 -61 25.5t-20 73.5v4q0 36 13 58.5t34.5 36t49.5 19t58 6.5v50q0 26 -9.5 41.5t-33.5 15.5q-50 0 -50 -61v-18h-47v15q0 51 23.5 80.5t75.5 29.5zM188 514q-49 -2 -76.5 -21t-27.5 -54v-7
q0 -56 49 -56q26 0 39 16.5t16 42.5v79zM238 277v-50h-204v50h204z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="277" 
d="M138 705q52 0 77.5 -30.5t25.5 -81.5v-153q0 -51 -25.5 -81.5t-77.5 -30.5t-77 30.5t-25 81.5v153q0 51 25 81.5t77 30.5zM138 659q-27 0 -39.5 -16.5t-12.5 -46.5v-159q0 -30 12.5 -46.5t39.5 -16.5t39.5 16.5t12.5 46.5v159q0 30 -12.5 46.5t-39.5 16.5zM241 278v-50
h-204v50h204z" />
    <glyph glyph-name="uni0410" unicode="&#x410;" horiz-adv-x="422" 
d="M119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="uni0411" unicode="&#x411;" horiz-adv-x="401" 
d="M200 411q82 0 123.5 -39.5t41.5 -124.5v-77q0 -82 -39 -126t-122 -44h-163v700h295v-71h-218v-218h82zM204 71q43 0 63 22.5t20 71.5v87q0 51 -22 70t-65 19h-82v-270h86z" />
    <glyph glyph-name="uni0412" unicode="&#x412;" horiz-adv-x="392" 
d="M197 700q82 0 117 -38.5t35 -116.5v-33q0 -56 -18 -90.5t-64 -48.5q51 -14 72.5 -53t21.5 -97v-59q0 -79 -39 -121.5t-121 -42.5h-161v700h157zM178 403q47 0 70.5 19t23.5 74v41q0 47 -17.5 69.5t-59.5 22.5h-77v-226h60zM201 71q83 0 83 95v62q0 59 -23.5 81.5
t-74.5 22.5h-68v-261h83z" />
    <glyph glyph-name="uni0413" unicode="&#x413;" horiz-adv-x="356" 
d="M118 629v-629h-77v700h279v-71h-202z" />
    <glyph glyph-name="uni0414" unicode="&#x414;" horiz-adv-x="489" 
d="M385 -96v96h-286v-96h-75v165h45q18 20 23.5 45t7.5 57l27 529h281v-631h53v-165h-76zM176 172q-2 -38 -8.5 -62t-23.5 -41h185v560h-130z" />
    <glyph glyph-name="uni0415" unicode="&#x415;" horiz-adv-x="360" 
d="M289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="uni0401" unicode="&#x401;" horiz-adv-x="360" 
d="M155 831v-90h-68v90h68zM280 831v-90h-69v90h69zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="uni0416" unicode="&#x416;" horiz-adv-x="613" 
d="M382 330l-37 -66v-264h-77v264l-36 67l-134 -331h-80l160 403l-156 297h78l168 -322v322h77v-322l169 322h78l-155 -295l159 -405h-80z" />
    <glyph glyph-name="uni0417" unicode="&#x417;" 
d="M193 707q81 0 119.5 -48t38.5 -128v-18q0 -54 -21.5 -91.5t-64.5 -53.5q45 -15 65.5 -53t20.5 -91v-55q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v68h73v-72q0 -48 20 -74.5t62 -26.5q43 0 62.5 27t19.5 80v55q0 104 -90 104h-33v70h37q39 0 62.5 25
t23.5 71v32q0 54 -19.5 81t-62.5 27q-42 0 -62 -26.5t-20 -74.5v-47h-72v44q0 81 37.5 128t118.5 47z" />
    <glyph glyph-name="uni0418" unicode="&#x418;" horiz-adv-x="409" 
d="M225 325l-104 -325h-81v700h69v-522l72 221l102 301h87v-700h-70v557z" />
    <glyph glyph-name="uni0419" unicode="&#x419;" horiz-adv-x="410" 
d="M317 844q0 -46 -28.5 -72t-79.5 -26t-78.5 26t-29.5 72h49q2 -29 18 -40t41 -11t41.5 11t18.5 40h48zM227 331l-106 -331h-81v700h69v-522l66 205l108 317h87v-700h-70v557z" />
    <glyph glyph-name="uni041A" unicode="&#x41a;" horiz-adv-x="409" 
d="M159 304l-41 -69v-235h-78v700h78v-338l186 338h79l-177 -319l183 -381h-80z" />
    <glyph glyph-name="uni041B" unicode="&#x41b;" horiz-adv-x="444" 
d="M189 629l-16 -455q-2 -42 -9 -74t-23 -54.5t-42.5 -34.5t-66.5 -12h-13v71q24 0 39 6t23.5 18.5t11.5 31.5t4 44l19 530h285v-700h-78v629h-134z" />
    <glyph glyph-name="uni041C" unicode="&#x41c;" horiz-adv-x="523" 
d="M298 1h-75l-116 557v-558h-68v700h109l115 -559l111 559h109v-700h-74v563z" />
    <glyph glyph-name="uni041D" unicode="&#x41d;" horiz-adv-x="408" 
d="M118 315v-315h-78v700h78v-315h172v315h78v-700h-78v315h-172z" />
    <glyph glyph-name="uni041E" unicode="&#x41e;" horiz-adv-x="389" 
d="M34 531q0 81 39.5 128.5t120.5 47.5t121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362zM111 165q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370q0 48 -20 74.5t-63 26.5q-42 0 -62.5 -26.5t-20.5 -74.5v-370z" />
    <glyph glyph-name="uni041F" unicode="&#x41f;" horiz-adv-x="408" 
d="M118 629v-629h-78v700h328v-700h-78v629h-172z" />
    <glyph glyph-name="uni0420" unicode="&#x420;" horiz-adv-x="371" 
d="M193 700q83 0 120 -45.5t37 -125.5v-84q0 -84 -39.5 -126.5t-123.5 -42.5h-69v-276h-78v700h153zM187 346q44 0 65 21.5t21 72.5v93q0 48 -18.5 72t-61.5 24h-75v-283h69z" />
    <glyph glyph-name="uni0421" unicode="&#x421;" horiz-adv-x="380" 
d="M191 707q81 0 119.5 -48t38.5 -128v-63h-74v67q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v91h74v-87q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v362q0 40 9.5 72.5t29 55.5t49 35.5
t69.5 12.5z" />
    <glyph glyph-name="uni0422" unicode="&#x422;" horiz-adv-x="372" 
d="M147 0v629h-129v71h336v-71h-129v-629h-78z" />
    <glyph glyph-name="uni0423" unicode="&#x423;" horiz-adv-x="417" 
d="M250 154q-9 -39 -21.5 -69t-33.5 -50t-52.5 -29t-79.5 -7v70q45 -2 67.5 13.5t32.5 55.5l16 46l-161 516h78l71 -243l48 -176l41 176l63 243h81z" />
    <glyph glyph-name="uni0424" unicode="&#x424;" horiz-adv-x="569" 
d="M371 618q85 0 123.5 -45.5t38.5 -129.5v-191q0 -84 -38.5 -129.5t-123.5 -45.5h-49v-77h-77v77h-49q-85 0 -123.5 45.5t-38.5 129.5v191q0 84 38.5 129.5t123.5 45.5h49v82h77v-82h49zM196 549q-45 0 -65 -25t-20 -76v-201q0 -51 20 -76.5t65 -25.5h49v404h-49zM371 145
q86 0 86 102v201q0 51 -20.5 76t-65.5 25h-49v-404h49z" />
    <glyph glyph-name="uni0425" unicode="&#x425;" horiz-adv-x="435" 
d="M214 302l-117 -302h-74l141 361l-132 339h81l108 -280l109 280h73l-131 -339l140 -361h-82z" />
    <glyph glyph-name="uni0427" unicode="&#x427;" horiz-adv-x="401" 
d="M283 310q-19 -27 -48 -41t-61 -14q-35 0 -61.5 12.5t-43.5 35t-25.5 53t-8.5 65.5v279h78v-273q0 -45 22 -73t64 -28q72 0 84 82v292h77v-700h-77v310z" />
    <glyph glyph-name="uni0426" unicode="&#x426;" horiz-adv-x="455" 
d="M352 -94v94h-305v700h77v-629h172v629h79v-631h53v-163h-76z" />
    <glyph glyph-name="uni0428" unicode="&#x428;" horiz-adv-x="581" 
d="M118 700v-629h134v629h78v-629h134v629h77v-700h-501v700h78z" />
    <glyph glyph-name="uni0429" unicode="&#x429;" horiz-adv-x="622" 
d="M520 -94v94h-480v700h78v-629h134v629h77v-629h135v629h77v-631h54v-163h-75z" />
    <glyph glyph-name="uni042F" unicode="&#x42f;" horiz-adv-x="394" 
d="M221 299q-49 0 -75.5 -22.5t-26.5 -81.5v-110q0 -35 -3 -52.5t-10 -32.5h-79q9 18 11.5 39.5t2.5 44.5v109q0 57 19.5 94.5t67.5 51.5q-86 28 -86 144v57q0 78 36.5 119t119.5 41h156v-700h-78v299h-55zM200 629q-44 0 -62.5 -24t-18.5 -72v-67q0 -57 26 -76.5t72 -19.5
h59v259h-76z" />
    <glyph glyph-name="uni042C" unicode="&#x42c;" horiz-adv-x="381" 
d="M187 424q84 0 123.5 -42.5t39.5 -126.5v-84q0 -80 -37.5 -125.5t-119.5 -45.5h-153v700h78v-276h69zM193 71q43 0 61.5 24t18.5 72v93q0 51 -21 72.5t-65 21.5h-69v-283h75z" />
    <glyph glyph-name="uni042A" unicode="&#x42a;" horiz-adv-x="438" 
d="M245 424q163 0 163 -169v-84q0 -80 -37.5 -125.5t-119.5 -45.5h-153v630h-88v70h165v-276h70zM251 71q43 0 61 24t18 72v93q0 51 -20.5 72.5t-64.5 21.5h-70v-283h76z" />
    <glyph glyph-name="uni042B" unicode="&#x42b;" horiz-adv-x="532" 
d="M187 424q84 0 123.5 -42.5t39.5 -126.5v-84q0 -80 -37.5 -125.5t-119.5 -45.5h-153v700h78v-276h69zM492 700v-700h-77v700h77zM193 71q43 0 61.5 24t18.5 72v93q0 51 -21 72.5t-65 21.5h-69v-283h75z" />
    <glyph glyph-name="uni042D" unicode="&#x42d;" horiz-adv-x="380" 
d="M188 707q40 0 69.5 -12.5t49 -35.5t29 -55.5t9.5 -72.5v-362q0 -80 -38.5 -128t-118.5 -48t-118.5 48t-38.5 128v76h73v-80q0 -48 20 -74.5t62 -26.5t62 26.5t20 74.5v162h-122v70h122v138q0 48 -20 74.5t-62 26.5t-62 -26.5t-20 -74.5v-56h-72v52q0 40 9.5 72.5
t28.5 55.5t48.5 35.5t69.5 12.5z" />
    <glyph glyph-name="uni042E" unicode="&#x42e;" horiz-adv-x="575" 
d="M380 707q81 0 120.5 -47.5t39.5 -128.5v-362q0 -80 -39.5 -128t-120.5 -48t-121 48t-40 128v146h-101v-315h-78v700h78v-315h101v146q0 81 40 128.5t121 47.5zM380 636q-43 0 -63 -26.5t-20 -74.5v-370q0 -48 20 -74.5t63 -26.5q42 0 62.5 26.5t20.5 74.5v370
q0 48 -20.5 74.5t-62.5 26.5z" />
    <glyph glyph-name="uni0430" unicode="&#x430;" horiz-adv-x="422" 
d="M119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="uni0431" unicode="&#x431;" horiz-adv-x="401" 
d="M200 411q82 0 123.5 -39.5t41.5 -124.5v-77q0 -82 -39 -126t-122 -44h-163v700h295v-71h-218v-218h82zM204 71q43 0 63 22.5t20 71.5v87q0 51 -22 70t-65 19h-82v-270h86z" />
    <glyph glyph-name="uni0432" unicode="&#x432;" horiz-adv-x="392" 
d="M197 700q82 0 117 -38.5t35 -116.5v-33q0 -56 -18 -90.5t-64 -48.5q51 -14 72.5 -53t21.5 -97v-59q0 -79 -39 -121.5t-121 -42.5h-161v700h157zM178 403q47 0 70.5 19t23.5 74v41q0 47 -17.5 69.5t-59.5 22.5h-77v-226h60zM201 71q83 0 83 95v62q0 59 -23.5 81.5
t-74.5 22.5h-68v-261h83z" />
    <glyph glyph-name="uni0433" unicode="&#x433;" horiz-adv-x="356" 
d="M118 629v-629h-77v700h279v-71h-202z" />
    <glyph glyph-name="uni0434" unicode="&#x434;" horiz-adv-x="489" 
d="M385 -96v96h-286v-96h-75v165h45q18 20 23.5 45t7.5 57l27 529h281v-631h53v-165h-76zM176 172q-2 -38 -8.5 -62t-23.5 -41h185v560h-130z" />
    <glyph glyph-name="uni0435" unicode="&#x435;" horiz-adv-x="360" 
d="M289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="uni0451" unicode="&#x451;" horiz-adv-x="360" 
d="M155 831v-90h-68v90h68zM280 831v-90h-69v90h69zM289 390v-70h-171v-249h210v-71h-288v700h288v-71h-210v-239h171z" />
    <glyph glyph-name="uni0436" unicode="&#x436;" horiz-adv-x="613" 
d="M382 330l-37 -66v-264h-77v264l-36 67l-134 -331h-80l160 403l-156 297h78l168 -322v322h77v-322l169 322h78l-155 -295l159 -405h-80z" />
    <glyph glyph-name="uni0437" unicode="&#x437;" 
d="M193 707q81 0 119.5 -48t38.5 -128v-18q0 -54 -21.5 -91.5t-64.5 -53.5q45 -15 65.5 -53t20.5 -91v-55q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v68h73v-72q0 -48 20 -74.5t62 -26.5q43 0 62.5 27t19.5 80v55q0 104 -90 104h-33v70h37q39 0 62.5 25
t23.5 71v32q0 54 -19.5 81t-62.5 27q-42 0 -62 -26.5t-20 -74.5v-47h-72v44q0 81 37.5 128t118.5 47z" />
    <glyph glyph-name="uni0438" unicode="&#x438;" horiz-adv-x="409" 
d="M224 323l-103 -323h-81v700h69v-522l62 193l112 329h87v-700h-70v557z" />
    <glyph glyph-name="uni0439" unicode="&#x439;" horiz-adv-x="410" 
d="M317 843q0 -46 -28.5 -72t-79.5 -26t-78.5 26t-29.5 72h49q2 -29 18 -40t41 -11t41.5 11t18.5 40h48zM230 337l-109 -337h-81v700h69v-522l65 202l109 320h87v-700h-70v557z" />
    <glyph glyph-name="uni043A" unicode="&#x43a;" horiz-adv-x="409" 
d="M159 304l-41 -69v-235h-78v700h78v-338l186 338h79l-177 -319l183 -381h-80z" />
    <glyph glyph-name="uni043B" unicode="&#x43b;" horiz-adv-x="444" 
d="M189 629l-16 -455q-2 -42 -9 -74t-23 -54.5t-42.5 -34.5t-66.5 -12h-13v71q24 0 39 6t23.5 18.5t11.5 31.5t4 44l19 530h285v-700h-78v629h-134z" />
    <glyph glyph-name="uni043C" unicode="&#x43c;" horiz-adv-x="523" 
d="M298 1h-75l-116 557v-558h-68v700h109l115 -559l111 559h109v-700h-74v563z" />
    <glyph glyph-name="uni043D" unicode="&#x43d;" horiz-adv-x="408" 
d="M118 315v-315h-78v700h78v-315h172v315h78v-700h-78v315h-172z" />
    <glyph glyph-name="uni043E" unicode="&#x43e;" horiz-adv-x="389" 
d="M34 531q0 81 39.5 128.5t120.5 47.5t121 -47.5t40 -128.5v-362q0 -80 -40 -128t-121 -48t-120.5 48t-39.5 128v362zM111 165q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370q0 48 -20 74.5t-63 26.5q-42 0 -62.5 -26.5t-20.5 -74.5v-370z" />
    <glyph glyph-name="uni043F" unicode="&#x43f;" horiz-adv-x="408" 
d="M118 629v-629h-78v700h328v-700h-78v629h-172z" />
    <glyph glyph-name="uni0440" unicode="&#x440;" horiz-adv-x="371" 
d="M193 700q83 0 120 -45.5t37 -125.5v-84q0 -84 -39.5 -126.5t-123.5 -42.5h-69v-276h-78v700h153zM187 346q44 0 65 21.5t21 72.5v93q0 48 -18.5 72t-61.5 24h-75v-283h69z" />
    <glyph glyph-name="uni0441" unicode="&#x441;" horiz-adv-x="380" 
d="M191 707q81 0 119.5 -48t38.5 -128v-63h-74v67q0 48 -19.5 74.5t-62.5 26.5q-42 0 -62 -26.5t-20 -74.5v-370q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v91h74v-87q0 -80 -38.5 -128t-119.5 -48q-80 0 -118.5 48t-38.5 128v362q0 40 9.5 72.5t29 55.5t49 35.5
t69.5 12.5z" />
    <glyph glyph-name="uni0442" unicode="&#x442;" horiz-adv-x="372" 
d="M147 0v629h-129v71h336v-71h-129v-629h-78z" />
    <glyph glyph-name="uni0443" unicode="&#x443;" horiz-adv-x="417" 
d="M250 154q-9 -39 -21.5 -69t-33.5 -50t-52.5 -29t-79.5 -7v70q45 -2 67.5 13.5t32.5 55.5l16 46l-161 516h78l71 -243l48 -176l41 176l63 243h81z" />
    <glyph glyph-name="uni0444" unicode="&#x444;" horiz-adv-x="569" 
d="M371 618q85 0 123.5 -45.5t38.5 -129.5v-191q0 -84 -38.5 -129.5t-123.5 -45.5h-49v-77h-77v77h-49q-85 0 -123.5 45.5t-38.5 129.5v191q0 84 38.5 129.5t123.5 45.5h49v82h77v-82h49zM196 549q-45 0 -65 -25t-20 -76v-201q0 -51 20 -76.5t65 -25.5h49v404h-49zM371 145
q86 0 86 102v201q0 51 -20.5 76t-65.5 25h-49v-404h49z" />
    <glyph glyph-name="uni0445" unicode="&#x445;" horiz-adv-x="435" 
d="M214 302l-117 -302h-74l141 361l-132 339h81l108 -280l109 280h73l-131 -339l140 -361h-82z" />
    <glyph glyph-name="uni0447" unicode="&#x447;" horiz-adv-x="401" 
d="M283 310q-19 -27 -48 -41t-61 -14q-35 0 -61.5 12.5t-43.5 35t-25.5 53t-8.5 65.5v279h78v-273q0 -45 22 -73t64 -28q72 0 84 82v292h77v-700h-77v310z" />
    <glyph glyph-name="uni0446" unicode="&#x446;" horiz-adv-x="455" 
d="M352 -94v94h-305v700h77v-629h172v629h79v-631h53v-163h-76z" />
    <glyph glyph-name="uni0448" unicode="&#x448;" horiz-adv-x="581" 
d="M118 700v-629h134v629h78v-629h134v629h77v-700h-501v700h78z" />
    <glyph glyph-name="uni0449" unicode="&#x449;" horiz-adv-x="622" 
d="M520 -94v94h-480v700h78v-629h134v629h77v-629h135v629h77v-631h54v-163h-75z" />
    <glyph glyph-name="uni044C" unicode="&#x44c;" horiz-adv-x="381" 
d="M187 424q84 0 123.5 -42.5t39.5 -126.5v-84q0 -80 -37.5 -125.5t-119.5 -45.5h-153v700h78v-276h69zM193 71q43 0 61.5 24t18.5 72v93q0 51 -21 72.5t-65 21.5h-69v-283h75z" />
    <glyph glyph-name="uni044A" unicode="&#x44a;" horiz-adv-x="438" 
d="M245 424q163 0 163 -169v-84q0 -80 -37.5 -125.5t-119.5 -45.5h-153v630h-88v70h165v-276h70zM251 71q43 0 61 24t18 72v93q0 51 -20.5 72.5t-64.5 21.5h-70v-283h76z" />
    <glyph glyph-name="uni044B" unicode="&#x44b;" horiz-adv-x="532" 
d="M187 424q84 0 123.5 -42.5t39.5 -126.5v-84q0 -80 -37.5 -125.5t-119.5 -45.5h-153v700h78v-276h69zM492 700v-700h-77v700h77zM193 71q43 0 61.5 24t18.5 72v93q0 51 -21 72.5t-65 21.5h-69v-283h75z" />
    <glyph glyph-name="uni044D" unicode="&#x44d;" horiz-adv-x="380" 
d="M188 707q40 0 69.5 -12.5t49 -35.5t29 -55.5t9.5 -72.5v-362q0 -80 -38.5 -128t-118.5 -48t-118.5 48t-38.5 128v76h73v-80q0 -48 20 -74.5t62 -26.5t62 26.5t20 74.5v162h-122v70h122v138q0 48 -20 74.5t-62 26.5t-62 -26.5t-20 -74.5v-56h-72v52q0 40 9.5 72.5
t28.5 55.5t48.5 35.5t69.5 12.5z" />
    <glyph glyph-name="uni044E" unicode="&#x44e;" horiz-adv-x="575" 
d="M380 707q81 0 120.5 -47.5t39.5 -128.5v-362q0 -80 -39.5 -128t-120.5 -48t-121 48t-40 128v146h-101v-315h-78v700h78v-315h101v146q0 81 40 128.5t121 47.5zM380 636q-43 0 -63 -26.5t-20 -74.5v-370q0 -48 20 -74.5t63 -26.5q42 0 62.5 26.5t20.5 74.5v370
q0 48 -20.5 74.5t-62.5 26.5z" />
    <glyph glyph-name="uni044F" unicode="&#x44f;" horiz-adv-x="394" 
d="M221 299q-49 0 -75.5 -22.5t-26.5 -81.5v-110q0 -35 -3 -52.5t-10 -32.5h-79q9 18 11.5 39.5t2.5 44.5v109q0 57 19.5 94.5t67.5 51.5q-86 28 -86 144v57q0 78 36.5 119t119.5 41h156v-700h-78v299h-55zM200 629q-44 0 -62.5 -24t-18.5 -72v-67q0 -57 26 -76.5t72 -19.5
h59v259h-76z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="389" 
d="M34 531q0 81 39.5 128.5t120.5 47.5t121 -47.5t40 -128.5v-361q0 -81 -40 -129t-121 -48t-120.5 48t-39.5 129v361zM111 165q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370q0 48 -20 75t-63 27q-42 0 -62.5 -27t-20.5 -75v-370z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="338" 
d="M183 573q-20 -12 -44.5 -20t-52.5 -10v64q28 1 48 9t33.5 20.5t22.5 29t16 34.5h54v-700h-77v573z" />
    <glyph glyph-name="two" unicode="2" 
d="M197 707q82 0 120 -48t38 -129q0 -53 -17.5 -95.5t-44 -79t-57 -68.5t-57 -64t-44.5 -66.5t-18 -74.5v-11h230v-71h-307v60q0 56 17.5 99t44 78.5t57 67t57 64.5t44 71t17.5 87q0 54 -19 82t-63 28q-43 0 -62.5 -28t-19.5 -75v-62h-73v57q0 81 38 129.5t119 48.5z" />
    <glyph glyph-name="three" unicode="3" 
d="M193 707q81 0 119.5 -47.5t38.5 -128.5v-18q0 -54 -21.5 -91t-63.5 -53q45 -17 65 -54.5t20 -90.5v-54q0 -81 -38.5 -129t-119.5 -48q-80 0 -118.5 48.5t-38.5 128.5v48h73v-53q0 -48 20 -74.5t62 -26.5q43 0 62.5 27t19.5 81v54q0 101 -88 104h-42v70h46q39 2 61.5 26
t22.5 70v32q0 54 -19.5 81.5t-62.5 27.5q-42 0 -62 -27t-20 -75v-36h-73v32q0 81 38 128.5t119 47.5z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="400" 
d="M324 141v-141h-76v141h-226v71l217 488h85v-488h56v-71h-56zM94 212h154v342z" />
    <glyph glyph-name="five" unicode="5" 
d="M118 415q34 58 104 58q68 0 99 -45.5t31 -120.5v-137q0 -80 -39 -128.5t-119 -48.5t-118.5 48.5t-38.5 128.5v47h73v-52q0 -48 20 -74.5t62 -26.5t62 26.5t20 74.5v136q0 48 -20 74.5t-62 26.5q-32 0 -53 -18.5t-26 -56.5v-16h-73l19 389h276v-70h-206z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="389" 
d="M196 707q81 0 119.5 -47t38.5 -128v-13h-73v18q0 48 -20 74t-63 26q-45 0 -65 -28t-20 -85v-157q14 36 42.5 55.5t70.5 19.5q68 0 99 -45t31 -121v-106q0 -81 -39.5 -129t-120.5 -48t-121 48t-40 129v357q0 84 38.5 132t122.5 48zM196 371q-43 0 -63 -26.5t-20 -74.5
v-105q0 -48 20 -74.5t63 -26.5q42 0 62.5 26.5t20.5 74.5v105q0 48 -20.5 74.5t-62.5 26.5z" />
    <glyph glyph-name="seven" unicode="7" 
d="M273 630h-238v70h317v-68l-180 -632h-78z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="392" 
d="M196 707q81 0 123 -46t42 -127v-22q0 -54 -19 -91t-61 -53q44 -17 62 -56t18 -92v-52q0 -81 -41.5 -128t-123.5 -47t-123.5 47t-41.5 128v52q0 54 18 92t63 56q-43 17 -62 53.5t-19 90.5v22q0 81 42 127t123 46zM196 637q-42 0 -65 -25.5t-23 -79.5v-34q0 -51 23 -74.5
t65 -23.5t65 23.5t23 74.5v34q0 54 -23 79.5t-65 25.5zM196 330q-88 0 -88 -101v-59q0 -54 23 -80t65 -26t64.5 26t23.5 80v59q0 101 -88 101z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="389" 
d="M193 707q81 0 121 -47.5t40 -128.5v-357q0 -83 -39 -132t-123 -49q-81 0 -119.5 47.5t-38.5 128.5v13h73v-18q0 -48 20.5 -74t62.5 -26q45 0 65.5 28t20.5 84v157q-29 -74 -113 -74q-68 0 -99 44.5t-31 120.5v107q0 81 39.5 128.5t120.5 47.5zM193 636q-42 0 -62.5 -26.5
t-20.5 -74.5v-105q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v105q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="zero.alt" horiz-adv-x="389" 
d="M34 531q0 81 39.5 128.5t120.5 47.5t121 -47.5t40 -128.5v-361q0 -81 -40 -129t-121 -48t-120.5 48t-39.5 129v361zM111 165q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v370q0 48 -20 75t-63 27q-42 0 -62.5 -27t-20.5 -75v-370z" />
    <glyph glyph-name="one.alt" horiz-adv-x="255" 
d="M123 573q-20 -12 -44.5 -20t-52.5 -10v64q28 1 48 9t33.5 20.5t22.5 29t16 34.5h54v-700h-77v573z" />
    <glyph glyph-name="two.alt" horiz-adv-x="368" 
d="M188 707q82 0 119.5 -48t37.5 -129q0 -54 -17.5 -96.5t-44 -79t-57 -68.5t-57 -64.5t-44.5 -67.5t-18 -76v-7h231v-71h-307v60q0 56 17.5 99t44 78.5t57 67t57 64.5t44 71t17.5 87q0 54 -19.5 82t-62.5 28t-62.5 -28t-19.5 -75v-62h-73v57q0 81 38 129.5t119 48.5z" />
    <glyph glyph-name="three.alt" horiz-adv-x="377" 
d="M189 707q81 0 119 -47.5t38 -128.5v-18q0 -54 -21.5 -91t-63.5 -53q45 -17 65 -54.5t20 -90.5v-54q0 -80 -38.5 -128.5t-118.5 -48.5q-81 0 -119.5 48t-38.5 129v48h74v-53q0 -48 19.5 -74.5t62.5 -26.5q82 0 82 108v54q0 101 -89 104h-42v70h47q39 2 61.5 26t22.5 70v32
q0 109 -82 109q-43 0 -62.5 -27t-19.5 -75v-36h-74v32q0 81 38.5 128.5t119.5 47.5z" />
    <glyph glyph-name="four.alt" horiz-adv-x="400" 
d="M324 141v-141h-77v141h-225v71l217 488h85v-488h56v-71h-56zM94 212h153v339z" />
    <glyph glyph-name="five.alt" horiz-adv-x="378" 
d="M113 415q34 58 105 58q67 0 98 -45.5t31 -120.5v-137q0 -81 -38.5 -129t-119.5 -48q-80 0 -118.5 48.5t-38.5 128.5v47h73v-52q0 -48 20 -74.5t62 -26.5q43 0 62.5 26.5t19.5 74.5v136q0 48 -19.5 74.5t-62.5 26.5q-32 0 -52.5 -18.5t-25.5 -56.5v-16h-74l20 389h276v-70
h-207z" />
    <glyph glyph-name="six.alt" horiz-adv-x="386" 
d="M195 707q81 0 119.5 -47t38.5 -128v-13h-74v18q0 48 -19.5 74t-62.5 26q-86 0 -86 -113v-157q15 36 43.5 55.5t70.5 19.5q67 0 98.5 -45t31.5 -121v-106q0 -81 -40 -129t-121 -48t-120.5 48t-39.5 129v357q0 83 38.5 131.5t122.5 48.5zM194 371q-42 0 -62.5 -26.5
t-20.5 -74.5v-105q0 -48 20.5 -74.5t62.5 -26.5q43 0 63 26.5t20 74.5v105q0 48 -20 74.5t-63 26.5z" />
    <glyph glyph-name="seven.alt" horiz-adv-x="349" 
d="M252 630h-232v70h309v-68l-172 -632h-78z" />
    <glyph glyph-name="eight.alt" horiz-adv-x="393" 
d="M196 707q82 0 124 -46t42 -127v-22q0 -54 -19 -91t-61 -53q44 -17 62 -56t18 -92v-52q0 -81 -41.5 -128t-124.5 -47q-82 0 -123.5 47t-41.5 128v52q0 54 18.5 92t62.5 56q-42 17 -61.5 53.5t-19.5 90.5v22q0 81 42 127t123 46zM196 637q-41 0 -64 -25.5t-23 -79.5v-34
q0 -51 22.5 -74.5t64.5 -23.5t65 23.5t23 74.5v34q0 54 -23 79.5t-65 25.5zM196 330q-87 0 -87 -101v-59q0 -54 22.5 -80t64.5 -26q88 0 88 106v59q0 101 -88 101z" />
    <glyph glyph-name="nine.alt" horiz-adv-x="386" 
d="M192 707q81 0 120.5 -47.5t39.5 -128.5v-357q0 -83 -38.5 -132t-122.5 -49q-81 0 -119.5 47.5t-38.5 128.5v13h73v-18q0 -48 20.5 -74t62.5 -26q86 0 86 112v157q-31 -74 -114 -74q-67 0 -98.5 44.5t-31.5 120.5v107q0 81 40 128.5t121 47.5zM192 636q-43 0 -63 -26.5
t-20 -74.5v-105q0 -48 20 -74.5t63 -26.5q42 0 62.5 26.5t20.5 74.5v105q0 48 -20.5 74.5t-62.5 26.5z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="164" 
d="M244 700l-276 -700h-47l276 700h47z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="599" 
d="M118 623q-25 -15 -61 -18v37q36 1 51.5 18t24.5 40h35v-444h-50v367zM439 700l-276 -700h-47l276 700h47zM466 450q52 0 76 -31t24 -82q0 -34 -11 -60.5t-27.5 -49.5t-36 -43.5t-36.5 -40.5t-28.5 -42t-11.5 -48v-7h146v-46h-196v38q0 36 11 63t28 49.5t36.5 42.5
t36.5 41.5t28 45.5t11 56q0 68 -52 68q-26 0 -38.5 -17.5t-12.5 -47.5v-38h-48v35q0 51 24 82.5t77 31.5z" />
    <glyph glyph-name="uni2153" unicode="&#x2153;" horiz-adv-x="596" 
d="M118 623q-25 -15 -61 -18v37q36 1 51.5 18t24.5 40h35v-444h-50v367zM439 700l-276 -700h-47l276 700h47zM458 449q52 0 76.5 -30.5t24.5 -82.5v-11q0 -70 -53 -91q28 -11 40.5 -35t12.5 -57v-34q0 -52 -24.5 -82.5t-76.5 -30.5t-76 30.5t-24 82.5v30h48v-34
q0 -29 12.5 -46t38.5 -17q51 0 51 68v34q0 65 -55 65h-27v46h29q25 0 39.5 16t13.5 45v20q0 68 -51 68q-26 0 -38.5 -17t-12.5 -46v-23h-48v19q0 52 24 82.5t76 30.5z" />
    <glyph glyph-name="uni2154" unicode="&#x2154;" horiz-adv-x="618" 
d="M142 705q52 0 76 -31t24 -82q0 -34 -11.5 -61t-28 -50t-36.5 -44t-36.5 -41.5t-27.5 -42t-11 -47.5v-5h146v-46h-196v38q0 36 11 63.5t28 50t36.5 42.5t36.5 41t28 45.5t11 55.5q0 68 -52 68q-26 0 -38.5 -17.5t-12.5 -47.5v-38h-48v36q0 51 24 82t77 31zM200 0h-47
l276 700h47zM481 449q51 0 75.5 -30.5t24.5 -82.5v-11q0 -70 -53 -91q28 -11 40.5 -35t12.5 -57v-34q0 -52 -24.5 -82.5t-75.5 -30.5q-52 0 -76.5 30.5t-24.5 82.5v30h48v-34q0 -29 12.5 -46t38.5 -17q51 0 51 68v34q0 65 -55 65h-27v46h29q25 0 39.5 16t13.5 45v20
q0 68 -51 68q-26 0 -38.5 -17t-12.5 -46v-23h-48v19q0 52 24.5 82.5t76.5 30.5z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="577" 
d="M118 623q-25 -15 -61 -18v37q36 1 51.5 18t24.5 40h35v-444h-50v367zM183 0h-47l276 700h47zM516 0h-50v88h-143v47l137 309h56v-309h34v-47h-34v-88zM466 135v212l-97 -212h97z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="602" 
d="M138 705q51 0 75.5 -30.5t24.5 -82.5v-11q0 -70 -52 -91q28 -11 40 -35t12 -57v-34q0 -52 -24.5 -82.5t-75.5 -30.5q-52 0 -76.5 30.5t-24.5 82.5v30h48v-34q0 -29 12.5 -46t38.5 -17q52 0 52 68v34q0 65 -55 65h-27v46h29q24 0 38.5 16t14.5 45v20q0 68 -52 68
q-26 0 -38.5 -17t-12.5 -46v-23h-48v19q0 52 24.5 82.5t76.5 30.5zM444 700h47l-276 -700h-47zM491 88h-143v47l137 309h56v-309h34v-47h-34v-88h-50v88zM394 135h97v212z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="601" 
d="M118 623q-25 -15 -61 -18v37q36 1 51.5 18t24.5 40h35v-444h-50v367zM116 0l276 700h47l-276 -700h-47zM462 449q51 0 78 -29.5t27 -80.5v-15q0 -32 -11.5 -56t-36.5 -35q26 -12 37 -37t11 -57v-33q0 -51 -26.5 -81t-78.5 -30q-53 0 -79.5 30t-26.5 81v33q0 32 11.5 57
t37.5 37q-26 11 -37.5 35t-11.5 56v15q0 51 27 80.5t79 29.5zM462 403q-26 0 -40.5 -15.5t-14.5 -50.5v-21q0 -62 55 -62q26 0 40.5 15t14.5 47v21q0 35 -14.5 50.5t-40.5 15.5zM462 208q-55 0 -55 -63v-38q0 -66 55 -66t55 66v38q0 63 -55 63z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="626" 
d="M138 705q51 0 75.5 -30.5t24.5 -82.5v-11q0 -70 -52 -91q28 -11 40 -35t12 -57v-34q0 -52 -24.5 -82.5t-75.5 -30.5q-52 0 -76.5 30.5t-24.5 82.5v30h48v-34q0 -29 12.5 -46t38.5 -17q52 0 52 68v34q0 65 -55 65h-27v46h29q24 0 38.5 16t14.5 45v20q0 68 -52 68
q-26 0 -38.5 -17t-12.5 -46v-23h-48v19q0 52 24.5 82.5t76.5 30.5zM473 700l-276 -700h-47l276 700h47zM487 449q52 0 78.5 -29.5t26.5 -80.5v-15q0 -32 -11 -56t-37 -35q26 -12 37 -37t11 -57v-33q0 -51 -26.5 -81t-78.5 -30t-78.5 30t-26.5 81v33q0 32 11 57t37 37
q-26 11 -37 35t-11 56v15q0 51 26.5 80.5t78.5 29.5zM487 403q-26 0 -40.5 -15.5t-14.5 -50.5v-21q0 -62 55 -62q26 0 40.5 15t14.5 47v21q0 35 -14.5 50.5t-40.5 15.5zM487 208q-55 0 -55 -63v-38q0 -66 55 -66t55 66v38q0 63 -55 63z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="626" 
d="M425 700h47l-276 -700h-47zM91 520q21 35 66 35q42 0 62 -29t20 -77v-87q0 -51 -24.5 -82t-76.5 -31q-51 0 -75.5 31t-24.5 82v30h48v-33q0 -30 12.5 -46.5t38.5 -16.5t38.5 16.5t12.5 46.5v86q0 29 -12.5 46t-38.5 17q-19 0 -33 -11.5t-16 -35.5v-10h-48l13 248h175v-46
h-131zM486 449q52 0 79 -29.5t27 -80.5v-15q0 -32 -11.5 -56t-37.5 -35q26 -12 37.5 -37t11.5 -57v-33q0 -51 -26.5 -81t-79.5 -30q-52 0 -78.5 30t-26.5 81v33q0 32 11 57t37 37q-25 11 -36.5 35t-11.5 56v15q0 51 27 80.5t78 29.5zM486 403q-26 0 -40.5 -15.5t-14.5 -50.5
v-21q0 -62 55 -62q26 0 40.5 15t14.5 47v21q0 35 -14.5 50.5t-40.5 15.5zM486 208q-55 0 -55 -63v-38q0 -66 55 -66t55 66v38q0 63 -55 63z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="594" 
d="M187 654h-150v46h202v-44l-115 -400h-50zM144 0h-47l276 700h47zM454 449q52 0 79 -29.5t27 -80.5v-15q0 -32 -11.5 -56t-37.5 -35q26 -12 37.5 -37t11.5 -57v-33q0 -51 -26.5 -81t-79.5 -30q-52 0 -78.5 30t-26.5 81v33q0 32 11 57t37 37q-26 11 -37 35t-11 56v15
q0 51 26.5 80.5t78.5 29.5zM454 403q-26 0 -40.5 -15.5t-14.5 -50.5v-21q0 -62 55 -62q26 0 40.5 15t14.5 47v21q0 35 -14.5 50.5t-40.5 15.5zM454 208q-55 0 -55 -63v-38q0 -66 55 -66t55 66v38q0 63 -55 63z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="277" 
d="M36 271q0 52 25 82.5t77 30.5t77.5 -30.5t25.5 -82.5v-228q0 -52 -25.5 -82.5t-77.5 -30.5t-77 30.5t-25 82.5v228zM86 39q0 -29 12.5 -46t39.5 -17t39.5 17t12.5 46v236q0 29 -12.5 46t-39.5 17t-39.5 -17t-12.5 -46v-236z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="189" 
d="M91 302q-25 -15 -61 -18v37q36 1 51.5 18t24.5 40h35v-444h-50v367z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="275" 
d="M142 385q52 0 76 -31t24 -82q0 -34 -11.5 -61t-28.5 -50.5t-36.5 -44t-36.5 -41t-28 -42.5t-10 -48v-4h146v-46h-196v38q0 36 11 63t28 49.5t36.5 42.5t36.5 41.5t28 45.5t11 56q0 68 -52 68q-26 0 -38.5 -17.5t-12.5 -47.5v-38h-48v35q0 51 24 82.5t77 31.5z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="275" 
d="M138 384q51 0 75.5 -30.5t24.5 -82.5v-11q0 -70 -52 -91q27 -11 39.5 -35t12.5 -57v-34q0 -52 -24.5 -82.5t-75.5 -30.5q-52 0 -76.5 30.5t-24.5 82.5v30h48v-34q0 -29 12.5 -46t38.5 -17q52 0 52 68v34q0 65 -55 65h-27v46h29q24 0 38.5 16t14.5 45v20q0 68 -52 68
q-26 0 -38.5 -17t-12.5 -46v-23h-48v19q0 52 24.5 82.5t76.5 30.5z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="283" 
d="M222 22v-89h-50v89h-143v46l138 309h55v-309h35v-46h-35zM75 68h97v212z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="275" 
d="M86 40q0 -30 12.5 -46.5t38.5 -16.5t38.5 16.5t12.5 46.5v86q0 29 -12.5 46t-38.5 17q-19 0 -33 -11.5t-16 -35.5v-10h-48l13 248h175v-46h-131l-6 -134q21 35 66 35q42 0 62 -28.5t20 -76.5v-88q0 -51 -24.5 -81.5t-76.5 -30.5q-51 0 -75.5 30.5t-24.5 81.5v31h48v-33z
" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="277" 
d="M140 384q52 0 76.5 -30.5t24.5 -81.5v-8h-48v11q0 30 -12.5 46.5t-38.5 16.5q-29 0 -41.5 -17.5t-12.5 -53.5v-96q20 45 71 45q43 0 63 -29t20 -77v-67q0 -51 -25 -82t-77 -31t-77.5 31t-25.5 82v226q0 53 24.5 84t78.5 31zM140 170q-27 0 -39.5 -17t-12.5 -47v-67
q0 -30 12.5 -46.5t39.5 -16.5q26 0 39 16.5t13 46.5v67q0 30 -13 47t-39 17z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="275" 
d="M187 335h-150v46h202v-44l-115 -400h-50z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="279" 
d="M139 384q52 0 79 -29.5t27 -80.5v-15q0 -32 -11.5 -56t-37.5 -35q26 -12 37.5 -37t11.5 -57v-33q0 -51 -26.5 -81t-79.5 -30q-52 0 -78.5 30t-26.5 81v33q0 32 11 57t37 37q-25 11 -36.5 35t-11.5 56v15q0 51 27 80.5t78 29.5zM139 338q-26 0 -40.5 -15.5t-14.5 -50.5
v-21q0 -62 55 -62q26 0 40.5 15t14.5 47v21q0 35 -14.5 50.5t-40.5 15.5zM139 143q-55 0 -55 -63v-38q0 -66 55 -66t55 66v38q0 63 -55 63z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="277" 
d="M137 384q52 0 77 -31t25 -82v-226q0 -53 -24.5 -84t-78.5 -31q-51 0 -76 30.5t-25 81.5v8h48v-11q0 -30 13 -46.5t39 -16.5q54 0 54 71v96q-20 -45 -72 -45q-42 0 -62 29t-20 77v67q0 51 25 82t77 31zM137 338q-26 0 -39 -16.5t-13 -46.5v-67q0 -30 13 -47t39 -17t39 17
t13 47v67q0 30 -13 46.5t-39 16.5z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="277" 
d="M36 687q0 52 25 82.5t77 30.5t77.5 -30.5t25.5 -82.5v-228q0 -52 -25.5 -82.5t-77.5 -30.5t-77 30.5t-25 82.5v228zM86 455q0 -29 12.5 -46t39.5 -17t39.5 17t12.5 46v236q0 29 -12.5 46t-39.5 17t-39.5 -17t-12.5 -46v-236z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="189" 
d="M30 737q36 1 51.5 18t24.5 40h35v-444h-50v367q-25 -15 -61 -18v37z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="275" 
d="M142 801q52 0 76 -31t24 -82q0 -34 -11 -61t-27.5 -50t-36 -43t-36.5 -40.5t-28.5 -42.5t-11.5 -48v-6h146v-46h-196v38q0 36 11 63t28 49.5t36.5 42.5t36.5 41.5t28 45.5t11 56q0 68 -52 68q-26 0 -38.5 -17.5t-12.5 -47.5v-38h-48v35q0 51 24 82.5t77 31.5z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="275" 
d="M138 800q51 0 75.5 -30.5t24.5 -82.5v-11q0 -70 -52 -91q28 -11 40 -35t12 -57v-34q0 -52 -24.5 -82.5t-75.5 -30.5q-52 0 -76.5 30.5t-24.5 82.5v30h48v-34q0 -29 12.5 -46t38.5 -17q52 0 52 68v34q0 65 -55 65h-27v46h29q24 0 38.5 16t14.5 45v20q0 68 -52 68
q-26 0 -38.5 -17t-12.5 -46v-23h-48v19q0 52 24.5 82.5t76.5 30.5z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="283" 
d="M222 439v-88h-50v88h-143v47l138 309h55v-309h35v-47h-35zM75 486h97v212z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="275" 
d="M86 454q0 -30 12.5 -46.5t38.5 -16.5t38.5 16.5t12.5 46.5v86q0 29 -12.5 46t-38.5 17q-19 0 -33 -11.5t-16 -35.5v-10h-48l13 248h175v-46h-131l-6 -133q21 35 66 35q42 0 62 -29t20 -77v-87q0 -51 -24.5 -82t-76.5 -31q-51 0 -75.5 31t-24.5 82v30h48v-33z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="277" 
d="M140 800q52 0 76.5 -30.5t24.5 -81.5v-8h-48v11q0 30 -12.5 46.5t-38.5 16.5q-29 0 -41.5 -17.5t-12.5 -53.5v-96q20 45 71 45q43 0 63 -29t20 -77v-67q0 -51 -25 -82t-77 -31t-77.5 31t-25.5 82v226q0 53 24.5 84t78.5 31zM140 586q-27 0 -39.5 -17t-12.5 -47v-67
q0 -30 12.5 -46.5t39.5 -16.5q26 0 39 16.5t13 46.5v67q0 30 -13 47t-39 17z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="275" 
d="M187 749h-150v46h202v-44l-115 -400h-50z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="279" 
d="M139 800q52 0 79 -29.5t27 -80.5v-15q0 -32 -11.5 -56t-37.5 -35q26 -12 37.5 -37t11.5 -57v-33q0 -51 -26.5 -81t-79.5 -30q-52 0 -78.5 30t-26.5 81v33q0 32 11 57t37 37q-25 11 -36.5 35t-11.5 56v15q0 51 27 80.5t78 29.5zM139 754q-26 0 -40.5 -15.5t-14.5 -50.5
v-21q0 -62 55 -62q26 0 40.5 15t14.5 47v21q0 35 -14.5 50.5t-40.5 15.5zM139 559q-55 0 -55 -63v-38q0 -66 55 -66t55 66v38q0 63 -55 63z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="277" 
d="M137 800q52 0 77 -31t25 -82v-226q0 -53 -24.5 -84t-78.5 -31q-51 0 -76 30.5t-25 81.5v8h48v-11q0 -30 13 -46.5t39 -16.5q54 0 54 71v96q-20 -45 -72 -45q-42 0 -62 29t-20 77v67q0 51 25 82t77 31zM137 754q-26 0 -39 -16.5t-13 -46.5v-67q0 -30 13 -47t39 -17t39 17
t13 47v67q0 30 -13 46.5t-39 16.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="395" 
d="M221 510l106 -117l-51 -38l-79 139l-78 -139l-51 38l105 117l-155 32l19 61l146 -64l-18 158h64l-17 -158l146 63l19 -61z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="376" 
d="M84 700l276 -700h-68l-276 700h68z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="328" 
d="M164 455q22 0 41 -8.5t33 -22.5t22.5 -33t8.5 -41t-8.5 -41t-22.5 -33.5t-33 -22.5t-41 -8t-41 8t-33.5 22.5t-22.5 33.5t-8 41t8 41t22.5 33t33.5 22.5t41 8.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="155" 
d="M115 478v-97h-75v97h75zM115 97v-97h-75v97h75z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="160" 
d="M74 0h-29v97h75v-90l-39 -95h-32z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="379" 
d="M115 97v-97h-75v97h75zM227 97v-97h-75v97h75zM339 97v-97h-75v97h75z" />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="158" 
d="M40 371v329h78v-329l-10 -216h-58zM116 97v-97h-75v97h75z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="158" 
d="M116 700v-96h-75v96h75zM40 309l10 236h58l10 -236v-309h-78v309z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="412" 
d="M327 472l-21 -205h56l-6 -56h-56l-22 -211h-64l22 211h-91l-23 -211h-64l23 211h-58l6 56h57l22 205h-59l6 56h58l18 172h64l-18 -172h92l18 172h64l-18 -172h56l-6 -56h-56zM263 472h-91l-22 -205h92z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="155" 
d="M115 97v-97h-75v97h75z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="155" 
d="M115 403v-97h-75v97h75z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="361" 
d="M182 707q81 0 118.5 -48t37.5 -129q0 -42 -10.5 -74t-25.5 -58.5t-33.5 -49t-33.5 -45.5t-25.5 -49.5t-10.5 -59.5q0 -18 5 -38h-70q-2 9 -3.5 18.5t-1.5 21.5q0 38 9.5 67t24.5 53t31.5 46t31.5 46.5t24.5 54.5t9.5 71q0 48 -18.5 75.5t-61.5 27.5t-61.5 -28t-18.5 -75
v-62h-73v57q0 40 9 72.5t28 56t48.5 36.5t69.5 13zM205 97v-97h-74v97h74z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="361" 
d="M231 700v-97h-75v97h75zM227 544q6 -18 6 -40q0 -38 -10 -67t-24.5 -53t-31.5 -46t-32 -46.5t-24.5 -55t-9.5 -70.5q0 -48 18.5 -75.5t61.5 -27.5t61.5 28t18.5 75v62h74v-57q0 -80 -37.5 -129t-118.5 -49t-118 48t-37 129q0 42 10 74t25.5 58.5t33.5 49t33.5 45.5
t25.5 49t10 60q0 22 -4 38h69z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="265" 
d="M115 700l-11 -189h-54l-10 189h75zM225 700l-11 -189h-54l-10 189h75z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="151" 
d="M113 700l-11 -189h-54l-10 189h75z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="160" 
d="M120 478v-97h-75v97h75zM74 0h-29v97h75v-90l-39 -95h-32z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="376" 
d="M360 700l-276 -700h-68l276 700h68z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="507" 
d="M490 -10v-57h-477v57h477z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="257" 
d="M214 636q-32 0 -43.5 -18.5t-13.5 -58.5l-6 -113q-1 -16 -3.5 -31t-9.5 -27.5t-19.5 -22.5t-32.5 -15q20 -5 32.5 -15t19.5 -22.5t9.5 -27.5t3.5 -31l6 -113q2 -40 13.5 -58.5t43.5 -18.5h19v-64h-46q-48 0 -72 28t-28 79l-7 136q-2 37 -12.5 54t-47.5 20v66
q37 3 47.5 20t12.5 54l7 136q4 51 28 79t72 28h46v-64h-19z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="257" 
d="M70 700q47 0 71.5 -28t27.5 -79l8 -136q1 -18 3.5 -31.5t8.5 -22.5t17.5 -14t29.5 -6v-66q-18 -2 -29.5 -6.5t-17.5 -13.5t-8.5 -22.5t-3.5 -31.5l-8 -136q-3 -51 -27.5 -79t-71.5 -28h-46v64h19q32 0 43.5 18.5t13.5 58.5l6 113q1 16 3.5 31t9.5 27.5t19.5 22.5t32.5 15
q-20 5 -32.5 15t-19.5 22.5t-9.5 27.5t-3.5 31l-6 113q-2 40 -13.5 58.5t-43.5 18.5h-19v64h46z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="242" 
d="M214 64v-64h-169v700h169v-64h-92v-572h92z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="242" 
d="M119 64v572h-91v64h169v-700h-169v64h91z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="241" 
d="M194 636q-41 0 -56.5 -22t-15.5 -68v-392q0 -46 15 -68t57 -22h23v-64h-28q-80 0 -112 37.5t-32 116.5v392q0 79 32 116.5t112 37.5h28v-64h-23z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="241" 
d="M52 700q80 0 112 -37.5t32 -116.5v-392q0 -79 -32 -116.5t-112 -37.5h-28v64h23q42 0 57 22t15 68v392q0 46 -15.5 68t-56.5 22h-23v64h28z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="833" 
d="M13 382h807v-64h-807v64z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="458" 
d="M13 382h432v-64h-432v64z" />
    <glyph glyph-name="figuredash" unicode="&#x2012;" horiz-adv-x="425" 
d="M13 382h399v-64h-399v64z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="265" 
d="M37 385h191v-70h-191v70z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="265" 
d="M37 385h191v-70h-191v70z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="320" 
d="M24 351l63 251h71l-63 -251l63 -261h-71zM162 351l64 251h70l-63 -251l63 -261h-70z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="320" 
d="M87 351l-63 251h70l64 -251l-64 -261h-70zM225 351l-63 251h70l64 -251l-64 -261h-70z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="183" 
d="M24 351l62 257h74l-62 -257l62 -268h-74z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="183" 
d="M85 351l-61 257h73l63 -257l-63 -267h-73z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="274" 
d="M81 -88h-32l25 88h-29v97h75v-90zM196 -88h-33l25 88h-29v97h75v-90z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="271" 
d="M78 700h33l-25 -88h29v-97h-75v90zM190 700h32l-24 -88h28v-97h-74v90z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="271" 
d="M76 515h-32l24 88h-28v97h74v-90zM188 515h-33l25 88h-29v97h75v-90z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="160" 
d="M78 700h33l-25 -88h29v-97h-75v90z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="160" 
d="M81 515h-32l25 88h-29v97h75v-89z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="160" 
d="M81 -88h-32l25 88h-29v97h75v-90z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="422" 
d="M314 834q0 -47 -28 -74t-77 -27t-75.5 27t-27.5 74h47q2 -29 17.5 -40.5t38.5 -11.5q24 0 40 11.5t18 40.5h47zM119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="155" 
 />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="397" 
d="M57 531q0 40 9 72.5t28 55.5t48 35.5t69 12.5t69 -12.5t48 -35.5t28 -55.5t9 -72.5v-45h-73v49q0 47 -18.5 74t-60.5 27t-60.5 -27t-18.5 -74v-108h174v-40h-174v-69h174v-41h-174v-112q0 -47 18.5 -74t60.5 -27t60.5 27t18.5 74v54h73v-50q0 -79 -37 -127.5t-117 -48.5
t-117 48.5t-37 127.5v108h-44v41h44v69h-44v40h44v104z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M225 661q66 -8 96.5 -53.5t30.5 -117.5v-40h-73v44q0 48 -20 73.5t-62 25.5q-43 0 -62.5 -26.5t-19.5 -74.5v-284q0 -47 19.5 -73.5t62.5 -26.5q42 0 62 26t20 74v67h73v-62q0 -72 -30.5 -118.5t-96.5 -54.5v-50h-64v50q-64 9 -94 55.5t-30 117.5v274q0 71 30 117.5
t94 55.5v50h64v-49z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="372" 
d="M286 418q22 -30 22 -68q0 -39 -22 -69l52 -51l-32 -31l-51 50q-14 -10 -32 -15.5t-37 -5.5q-37 0 -69 21l-51 -50l-31 31l51 51q-22 30 -22 69q0 40 22 68l-51 52l31 31l52 -51q30 22 68 22t69 -22l51 51l32 -31zM186 424q-31 0 -52.5 -21.5t-21.5 -52.5t21.5 -52.5
t52.5 -21.5t53 21.5t22 52.5t-22 52.5t-53 21.5z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M226 697q64 -9 94 -55t30 -117v-19h-73v24q0 47 -19.5 73.5t-61.5 26.5t-61 -26.5t-19 -73.5q0 -39 17.5 -67t43.5 -51t56.5 -44.5t56.5 -48t43.5 -61t17.5 -83.5q0 -71 -30.5 -117.5t-94.5 -55.5v-53h-64v53q-66 9 -96 55t-30 118v43h73v-48q0 -48 20 -73.5t62 -25.5
q43 0 62.5 25.5t19.5 73.5q0 39 -17.5 67t-43.5 51t-56 44.5t-56 48t-43.5 61t-17.5 83.5q0 72 30 117t93 55v56h64v-56z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="391" 
d="M200 707q40 0 69.5 -13t48.5 -36.5t28 -56t9 -72.5v-56h-74v60q0 48 -18 75.5t-61 27.5t-61.5 -27t-18.5 -75q0 -46 8.5 -79t20 -60t24 -52.5t20.5 -56.5h134v-61h-129q1 -41 -15 -77.5t-54 -76.5h221v-71h-319v68q20 8 38.5 22.5t32 34.5t21 45t5.5 55h-83v61h73
q-9 29 -22 54t-25 52t-20.5 60t-8.5 78q0 81 37.5 129t118.5 48z" />
    <glyph glyph-name="uni20B9" unicode="&#x20b9;" horiz-adv-x="388" 
d="M245 639q26 -16 37.5 -43.5t13.5 -58.5h61v-61h-61q-5 -70 -49 -111.5t-116 -41.5h-17l242 -323h-84l-241 323v60h80q56 0 82 23.5t27 69.5h-188v61h188q0 50 -27.5 76t-81.5 26h-79v61h325v-61h-112z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="414" 
d="M246 232v-59h100v-40h-100v-133h-78v133h-100v40h100v59h-100v41h94l-144 427h81l111 -342l112 342h74l-145 -427h95v-41h-100z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="396" 
d="M125 492q21 0 41 -10t38.5 -21.5t36.5 -21t35 -9.5q19 1 32 14.5t32 41.5l38 -38q-28 -43 -51.5 -59.5t-51.5 -17.5q-21 0 -40 9.5t-37.5 21t-36.5 21t-36 9.5q-20 0 -34.5 -13t-34.5 -42l-38 35q29 44 54 61.5t53 18.5zM125 328q21 0 41 -9.5t38.5 -21t36.5 -21t35 -9.5
q19 0 32 14t32 41l38 -37q-28 -43 -51.5 -60t-51.5 -17q-21 0 -40 9.5t-37.5 21t-36.5 21t-36 9.5q-20 0 -34.5 -13.5t-34.5 -41.5l-38 35q29 44 54 61.5t53 17.5z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="396" 
d="M125 410q21 0 41 -9.5t38.5 -21t36 -21t34.5 -9.5q19 0 32.5 13.5t32.5 41.5l38 -38q-28 -43 -51.5 -59.5t-51.5 -17.5q-21 0 -40.5 9.5t-38 21t-36 21.5t-35.5 10q-20 0 -34.5 -13.5t-34.5 -42.5l-38 36q29 44 53.5 61.5t53.5 17.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="350" 
d="M212 527v-97h-75v97h75zM320 378v-56h-290v56h290zM212 270v-97h-75v97h75z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="350" 
d="M313 456v-56h-276v56h276zM313 314v-56h-276v56h276z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="383" 
d="M40 252l237 98l-237 98v57l310 -127v-56l-310 -127v57z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="583" 
d="M418 483q62 0 97 -31.5t35 -93.5t-35 -93t-97 -31h-13q-40 0 -68 13t-41 42q-14 -31 -43.5 -43t-68.5 -12h-12q-62 0 -97.5 31t-35.5 93t35.5 93.5t97.5 31.5h12q39 0 68.5 -12.5t43.5 -43.5q13 30 41 43t68 13h13zM173 429q-43 0 -61 -19.5t-18 -51.5t18 -50.5t61 -19.5
h18q40 0 59 18.5t19 51.5t-19 52t-59 19h-18zM397 429q-44 0 -60.5 -19.5t-16.5 -51.5t16.5 -50.5t60.5 -19.5h18q40 0 60 18.5t20 51.5t-20 52t-60 19h-18z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="383" 
d="M343 195l-310 127v56l310 127v-57l-237 -98l237 -98v-57z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="388" 
d="M30 322v56h328v-191h-56v135h-272z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="392" 
d="M362 378v-56h-332v56h332z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="366" 
d="M289 206l-106 106l-106 -106l-38 38l105 106l-105 105l38 38l106 -105l106 105l38 -38l-106 -105l106 -106z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="616" 
d="M136 704q51 0 75.5 -31t24.5 -82v-221q0 -51 -24.5 -82t-75.5 -31t-75.5 31t-24.5 82v221q0 51 24.5 82t75.5 31zM467 700l-276 -700h-47l276 700h47zM136 658q-27 0 -38.5 -17.5t-11.5 -46.5v-228q0 -29 11.5 -46t38.5 -17q26 0 38 17t12 46v228q0 29 -12 46.5t-38 17.5
zM480 442q52 0 76 -30.5t24 -81.5v-220q0 -51 -24 -82t-76 -31q-51 0 -75.5 31t-24.5 82v220q0 51 24.5 81.5t75.5 30.5zM480 396q-26 0 -38 -17t-12 -46v-227q0 -29 12 -46.5t38 -17.5t38 17.5t12 46.5v227q0 29 -12 46t-38 17z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="867" 
d="M136 704q51 0 75.5 -31t24.5 -82v-221q0 -51 -24.5 -82t-75.5 -31t-75.5 31t-24.5 82v221q0 51 24.5 82t75.5 31zM467 700l-276 -700h-47l276 700h47zM136 658q-27 0 -38.5 -17.5t-11.5 -46.5v-228q0 -29 11.5 -46t38.5 -17q26 0 38 17t12 46v228q0 29 -12 46.5t-38 17.5
zM480 442q52 0 76 -30.5t24 -81.5v-220q0 -51 -24 -82t-76 -31q-51 0 -75.5 31t-24.5 82v220q0 51 24.5 81.5t75.5 30.5zM731 442q52 0 76 -30.5t24 -81.5v-220q0 -51 -24 -82t-76 -31q-51 0 -75.5 31t-24.5 82v220q0 51 24.5 81.5t75.5 30.5zM480 396q-26 0 -38 -17
t-12 -46v-227q0 -29 12 -46.5t38 -17.5t38 17.5t12 46.5v227q0 29 -12 46t-38 17zM731 396q-26 0 -37.5 -17t-11.5 -46v-227q0 -29 11.5 -46.5t37.5 -17.5t38 17.5t12 46.5v227q0 29 -12 46t-38 17z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="392" 
d="M224 322v-141h-56v141h-138v56h138v139h56v-139h138v-56h-138z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="392" 
d="M168 237v99h-131v56h131v125h56v-125h131v-56h-131v-99h131v-56h-318v56h131z" />
    <glyph glyph-name="uni00B5" unicode="&#xb5;" horiz-adv-x="398" 
d="M286 72q-14 -36 -41 -57.5t-60 -21.5q-26 0 -43 13t-25 33v-131h-78v592h78v-334q0 -48 19.5 -74.5t61.5 -26.5t61.5 26.5t19.5 74.5v334h78v-500h-71v72z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="408" 
d="M208 629q-45 0 -67.5 -24t-22.5 -75v-35q0 -45 21.5 -69t59.5 -26h79v78h77v-78h34v-70h-34v-241q0 -26 2 -47.5t13 -41.5h-79q-6 13 -9 25.5t-4 35.5q-15 -34 -43.5 -51t-66.5 -17q-66 0 -96.5 43t-30.5 118v68q0 52 19.5 90.5t63.5 55.5q-43 15 -63 52.5t-20 90.5v16
q0 84 40.5 128.5t124.5 44.5h113v-71h-111zM205 330q-87 -3 -87 -106v-64q0 -47 20 -71.5t61 -24.5q34 0 55.5 19.5t23.5 60.5v186h-73z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="403" 
d="M90 390h-63l146 310h57l146 -310h-63l-112 239z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="689" 
d="M391 735q75 0 127.5 -22t84.5 -60.5t46.5 -91t14.5 -114.5q0 -74 -15.5 -132t-41 -98t-58.5 -61t-68 -21q-74 0 -79 68q-15 -33 -40.5 -51t-62.5 -17q-56 1 -77.5 43.5t-14.5 109.5l11 100q7 69 37.5 108.5t86.5 37.5q34 0 55.5 -17t30.5 -49l7 62h68l-30 -287
q-2 -17 4 -30t25 -13q29 0 48.5 24.5t31.5 61t17 79.5t5 80q0 52 -12.5 94.5t-39 72.5t-67 46t-96.5 16q-75 0 -130 -29.5t-91.5 -81.5t-54 -123.5t-17.5 -155.5q0 -65 15 -116.5t46 -87t79 -54.5t113 -19q53 0 101 13t96 50l-6 -69q-46 -32 -94 -43t-101 -11
q-83 0 -142.5 24.5t-98 69t-57 107.5t-18.5 140q0 89 21.5 170t66.5 142.5t113 98t161 36.5zM362 466q-35 1 -53.5 -21.5t-23.5 -63.5l-9 -90q-5 -39 8.5 -64t48.5 -26t53.5 23.5t23.5 65.5l10 96q4 35 -10.5 57.5t-47.5 22.5z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="373" 
d="M222 744v-788h-64v788h64z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="374" 
d="M158 744h64v-334h-64v334zM158 290h64v-334h-64v334z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="742" 
d="M371 704q74 0 137 -26.5t109 -74t72 -112.5t26 -141q0 -77 -26 -141.5t-72 -112t-109 -74t-137 -26.5t-137 26.5t-109 74t-72 112t-26 141.5q0 76 26 141t72 112.5t109 74t137 26.5zM371 654q-63 0 -117 -23t-93 -63.5t-61 -96.5t-22 -121t22 -121t61 -96.5t93 -63.5
t117 -23t117 23t93 63.5t61 96.5t22 121t-22 121t-61 96.5t-93 63.5t-117 23zM367 570q56 0 82.5 -33.5t26.5 -90.5v-30h-52v34q0 32 -13.5 50.5t-42.5 18.5t-42.5 -18.5t-13.5 -50.5v-200q0 -32 13.5 -50.5t42.5 -18.5t42.5 18.5t13.5 50.5v49h52v-46q0 -56 -26.5 -89.5
t-82.5 -33.5q-57 0 -83 33.5t-26 89.5v193q0 57 26 90.5t83 33.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="394" 
d="M229 513h136v-61h-136v-517h-64v517h-135v61h135v187h64v-187z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="394" 
d="M229 513h136v-61h-136v-268h136v-61h-136v-188h-64v188h-135v61h135v268h-135v61h135v187h64v-187z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="219" 
d="M109 708q36 0 61.5 -25t25.5 -61t-25.5 -61t-61.5 -25q-18 0 -33.5 6.5t-27 18.5t-18.5 27.5t-7 33.5t7 33.5t18.5 27.5t27 18.5t33.5 6.5zM109 671q-20 0 -34.5 -14t-14.5 -35q0 -20 14.5 -34.5t34.5 -14.5t35 14.5t15 34.5q0 21 -15 35t-35 14z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="396" 
d="M203 643v-706h-57l1 340q-66 2 -97 47t-31 121v86q0 80 35 124.5t116 44.5h180v-763h-57v706h-90z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="742" 
d="M371 704q74 0 137 -26.5t109 -74t72 -112.5t26 -141q0 -77 -26 -141.5t-72 -112t-109 -74t-137 -26.5t-137 26.5t-109 74t-72 112t-26 141.5q0 76 26 141t72 112.5t109 74t137 26.5zM371 654q-63 0 -117 -23t-93 -63.5t-61 -96.5t-22 -121t22 -121t61 -96.5t93 -63.5
t117 -23t117 23t93 63.5t61 96.5t22 121t-22 121t-61 96.5t-93 63.5t-117 23zM376 564q57 0 83 -28.5t26 -83.5v-10q0 -39 -13.5 -64.5t-44.5 -36.5q32 -11 45 -36.5t13 -63.5v-46q0 -17 1.5 -31.5t9.5 -27.5h-57q-5 11 -6.5 22.5t-1.5 36.5v46q0 42 -19 57t-54 15h-37v-177
h-54v428h109zM366 364q31 0 47.5 14t16.5 52v16q0 33 -12.5 50.5t-44.5 17.5h-52v-150h45z" />
    <glyph glyph-name="section" unicode="&#xa7;" 
d="M194 707q72 0 113 -36t41 -96v-27h-74v17q0 35 -21.5 56t-56.5 21q-34 0 -57.5 -17.5t-23.5 -53.5q-1 -25 16.5 -41.5t43.5 -30t56.5 -26.5t56.5 -33t43.5 -49t17.5 -73q0 -43 -22.5 -75.5t-60.5 -51.5q17 -9 32.5 -21t27 -27.5t18 -35.5t6.5 -45q-1 -59 -43.5 -95.5
t-112.5 -36.5q-73 0 -115 35.5t-42 96.5v41h74v-31q0 -37 23 -57t58 -20q32 0 55.5 17.5t24.5 52.5q0 26 -17 42.5t-43 29.5t-56.5 26.5t-56.5 32.5t-43.5 48t-17.5 73t24.5 76.5t62.5 49.5q-18 10 -34 22t-28 27.5t-19 35.5t-7 46q1 60 43 96.5t114 36.5zM191 412
q-32 -5 -55.5 -30.5t-23.5 -61.5q0 -20 7.5 -35t20 -26.5t28.5 -20.5t33 -16q32 8 53 33t21 61q0 37 -25 58.5t-59 37.5z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="583" 
d="M94 344v310h-82v46h213v-46h-81v-310h-50zM436 346h-48l-73 277v-279h-45v356h70l73 -278l70 278h70v-356h-48v281z" />
    <glyph glyph-name="uni2117" unicode="&#x2117;" horiz-adv-x="742" 
d="M371 704q74 0 137 -26.5t109 -74t72 -112.5t26 -141t-26 -141t-72 -112t-109 -74t-137 -27t-137 27t-109 74t-72 112t-26 141t26 141t72 112.5t109 74t137 26.5zM371 654q-63 0 -117 -23t-93 -63.5t-61 -96.5t-22 -121t22 -121t61 -96.5t93 -63.5t117 -23t117 23t93 63.5
t61 96.5t22 121t-22 121t-61 96.5t-93 63.5t-117 23zM393 555q58 0 83 -29.5t25 -83.5v-46q0 -57 -26.5 -85t-85.5 -28h-51v-156h-54v428h109zM386 334q32 0 46.5 16.5t14.5 51.5v34q0 32 -12.5 50.5t-44.5 18.5h-52v-171h48z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="223" 
d="M178 848l-93 -103h-54l70 103h77z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="252" 
d="M231 834q-1 -47 -28.5 -74t-77.5 -27q-49 0 -75.5 27t-27.5 74h47q2 -29 17.5 -40.5t38.5 -11.5q25 0 40.5 11.5t17.5 40.5h48z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="250" 
d="M72 848l53 -60l53 60h68l-86 -100h-69l-87 100h68z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="249" 
d="M143 -39q34 0 54 -10t20 -39q0 -19 -7.5 -31t-20.5 -18.5t-30 -9t-36 -2.5q-18 0 -35 3t-29.5 9.5t-20 18t-7.5 28.5v8h53v-7q0 -17 12.5 -23t26.5 -7q16 0 28 7.5t12 25.5q0 20 -13.5 26t-33.5 6h-8v63h35v-48z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="250" 
d="M72 748h-68l87 100h69l86 -100h-68l-53 59z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="219" 
d="M103 831v-90h-68v90h68zM184 831v-90h-68v90h68z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="198" 
d="M133 833v-90h-68v90h68z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="223" 
d="M124 848l69 -103h-57l-93 103h81z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="242" 
d="M126 848l-80 -103h-50l58 103h72zM233 848l-80 -103h-49l57 103h72z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="251" 
d="M232 800v-56h-212v56h212z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="227" 
d="M145 3q-21 -16 -31 -36.5t-9 -35.5q0 -16 11.5 -23t25.5 -7t23 2.5t17 6.5v-41q-15 -7 -29.5 -9.5t-32.5 -2.5q-33 0 -54 13.5t-21 42.5q-1 25 16.5 48.5t49.5 41.5h34z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="234" 
d="M117 887q31 0 52 -21t21 -52t-21 -52t-52 -21t-52.5 21t-21.5 52t21.5 52t52.5 21zM117 850q-16 0 -26.5 -10t-10.5 -26t10.5 -26t26.5 -10q15 0 25.5 10t10.5 26t-10.5 26t-25.5 10z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="256" 
d="M250 802q-11 -30 -29.5 -44t-41.5 -14q-15 0 -26.5 4.5t-22.5 10t-21 10t-22 4.5q-14 0 -24 -6t-18 -23l-39 26q13 31 32 44.5t42 13.5q15 0 27 -4.5t22.5 -10t21 -10t21.5 -4.5q13 0 22.5 6.5t17.5 22.5z" />
    <glyph glyph-name="commaaccentcomb" unicode="&#xf6c3;" horiz-adv-x="0" 
d="M102 -177h-29l23 56h-25v88h66v-81z" />
    <glyph glyph-name="uni02BC" unicode="&#x2bc;" horiz-adv-x="160" 
d="M81 515h-32l25 88h-29v97h75v-89z" />
    <glyph glyph-name="Edotaaccent" horiz-adv-x="405" 
 />
    <glyph glyph-name="edotaaccent" horiz-adv-x="405" 
 />
    <glyph glyph-name="lcute" horiz-adv-x="405" 
 />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="422" 
d="M119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="422" 
d="M305 840l-92 -103h-55l70 103h77zM119 141l-27 -141h-72l134 700h113l134 -700h-78l-27 141h-177zM129 208h156l-79 404z" />
    <hkern u1="&#xc2;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc2;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc2;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc2;" u2="v" k="25" />
    <hkern u1="&#xc2;" u2="q" k="1" />
    <hkern u1="&#xc2;" u2="&#x153;" k="1" />
    <hkern u1="&#xc2;" u2="V" k="25" />
    <hkern u1="&#xc2;" u2="Q" k="1" />
    <hkern u1="&#xc2;" u2="&#x152;" k="1" />
    <hkern u1="&#xc4;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc4;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc4;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc4;" u2="v" k="25" />
    <hkern u1="&#xc4;" u2="q" k="1" />
    <hkern u1="&#xc4;" u2="&#x153;" k="1" />
    <hkern u1="&#xc4;" u2="V" k="25" />
    <hkern u1="&#xc4;" u2="Q" k="1" />
    <hkern u1="&#xc4;" u2="&#x152;" k="1" />
    <hkern u1="&#xc0;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc0;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc0;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc0;" u2="v" k="25" />
    <hkern u1="&#xc0;" u2="q" k="1" />
    <hkern u1="&#xc0;" u2="&#x153;" k="1" />
    <hkern u1="&#xc0;" u2="V" k="25" />
    <hkern u1="&#xc0;" u2="Q" k="1" />
    <hkern u1="&#xc0;" u2="&#x152;" k="1" />
    <hkern u1="&#x100;" u2="&#x2018;" k="60" />
    <hkern u1="&#x100;" u2="&#x201d;" k="60" />
    <hkern u1="&#x100;" u2="&#x201c;" k="60" />
    <hkern u1="&#x100;" u2="&#x3f;" k="30" />
    <hkern u1="&#x100;" u2="v" k="25" />
    <hkern u1="&#x100;" u2="q" k="1" />
    <hkern u1="&#x100;" u2="&#x153;" k="1" />
    <hkern u1="&#x100;" u2="V" k="25" />
    <hkern u1="&#x100;" u2="Q" k="1" />
    <hkern u1="&#x100;" u2="&#x152;" k="1" />
    <hkern u1="&#x104;" u2="&#x2018;" k="60" />
    <hkern u1="&#x104;" u2="&#x201d;" k="60" />
    <hkern u1="&#x104;" u2="&#x201c;" k="60" />
    <hkern u1="&#x104;" u2="&#x3f;" k="30" />
    <hkern u1="&#x104;" u2="v" k="25" />
    <hkern u1="&#x104;" u2="q" k="1" />
    <hkern u1="&#x104;" u2="&#x153;" k="1" />
    <hkern u1="&#x104;" u2="V" k="25" />
    <hkern u1="&#x104;" u2="Q" k="1" />
    <hkern u1="&#x104;" u2="&#x152;" k="1" />
    <hkern u1="&#xc5;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc5;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc5;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc5;" u2="v" k="25" />
    <hkern u1="&#xc5;" u2="q" k="1" />
    <hkern u1="&#xc5;" u2="&#x153;" k="1" />
    <hkern u1="&#xc5;" u2="V" k="25" />
    <hkern u1="&#xc5;" u2="Q" k="1" />
    <hkern u1="&#xc5;" u2="&#x152;" k="1" />
    <hkern u1="&#xc3;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc3;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc3;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc3;" u2="v" k="25" />
    <hkern u1="&#xc3;" u2="q" k="1" />
    <hkern u1="&#xc3;" u2="&#x153;" k="1" />
    <hkern u1="&#xc3;" u2="V" k="25" />
    <hkern u1="&#xc3;" u2="Q" k="1" />
    <hkern u1="&#xc3;" u2="&#x152;" k="1" />
    <hkern u1="B" u2="&#x201a;" k="5" />
    <hkern u1="B" u2="&#x2018;" k="10" />
    <hkern u1="B" u2="&#x201d;" k="10" />
    <hkern u1="B" u2="&#x201c;" k="10" />
    <hkern u1="B" u2="&#x201e;" k="5" />
    <hkern u1="B" u2="&#x2e;" k="5" />
    <hkern u1="B" u2="&#x2026;" k="5" />
    <hkern u1="B" u2="&#x2c;" k="5" />
    <hkern u1="B" u2="x" k="3" />
    <hkern u1="B" u2="v" k="4" />
    <hkern u1="B" u2="X" k="3" />
    <hkern u1="B" u2="V" k="4" />
    <hkern u1="C" u2="&#x201a;" k="6" />
    <hkern u1="C" u2="&#x2018;" k="6" />
    <hkern u1="C" u2="&#x201d;" k="6" />
    <hkern u1="C" u2="&#x201c;" k="6" />
    <hkern u1="C" u2="&#x201e;" k="6" />
    <hkern u1="C" u2="&#x2e;" k="6" />
    <hkern u1="C" u2="&#x2026;" k="6" />
    <hkern u1="C" u2="&#x2c;" k="6" />
    <hkern u1="C" u2="x" k="11" />
    <hkern u1="C" u2="X" k="11" />
    <hkern u1="&#x106;" u2="&#x201a;" k="6" />
    <hkern u1="&#x106;" u2="&#x2018;" k="6" />
    <hkern u1="&#x106;" u2="&#x201d;" k="6" />
    <hkern u1="&#x106;" u2="&#x201c;" k="6" />
    <hkern u1="&#x106;" u2="&#x201e;" k="6" />
    <hkern u1="&#x106;" u2="&#x2e;" k="6" />
    <hkern u1="&#x106;" u2="&#x2026;" k="6" />
    <hkern u1="&#x106;" u2="&#x2c;" k="6" />
    <hkern u1="&#x106;" u2="x" k="11" />
    <hkern u1="&#x106;" u2="X" k="11" />
    <hkern u1="&#x10c;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10c;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10c;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10c;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10c;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10c;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10c;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10c;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10c;" u2="x" k="11" />
    <hkern u1="&#x10c;" u2="X" k="11" />
    <hkern u1="&#xc7;" u2="&#x201a;" k="6" />
    <hkern u1="&#xc7;" u2="&#x2018;" k="6" />
    <hkern u1="&#xc7;" u2="&#x201d;" k="6" />
    <hkern u1="&#xc7;" u2="&#x201c;" k="6" />
    <hkern u1="&#xc7;" u2="&#x201e;" k="6" />
    <hkern u1="&#xc7;" u2="&#x2e;" k="6" />
    <hkern u1="&#xc7;" u2="&#x2026;" k="6" />
    <hkern u1="&#xc7;" u2="&#x2c;" k="6" />
    <hkern u1="&#xc7;" u2="x" k="11" />
    <hkern u1="&#xc7;" u2="X" k="11" />
    <hkern u1="&#x108;" u2="&#x201a;" k="6" />
    <hkern u1="&#x108;" u2="&#x2018;" k="6" />
    <hkern u1="&#x108;" u2="&#x201d;" k="6" />
    <hkern u1="&#x108;" u2="&#x201c;" k="6" />
    <hkern u1="&#x108;" u2="&#x201e;" k="6" />
    <hkern u1="&#x108;" u2="&#x2e;" k="6" />
    <hkern u1="&#x108;" u2="&#x2026;" k="6" />
    <hkern u1="&#x108;" u2="&#x2c;" k="6" />
    <hkern u1="&#x108;" u2="x" k="11" />
    <hkern u1="&#x108;" u2="X" k="11" />
    <hkern u1="&#x10a;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10a;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10a;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10a;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10a;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10a;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10a;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10a;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10a;" u2="x" k="11" />
    <hkern u1="&#x10a;" u2="X" k="11" />
    <hkern u1="D" u2="&#x201a;" k="10" />
    <hkern u1="D" u2="&#x2018;" k="10" />
    <hkern u1="D" u2="&#x201d;" k="10" />
    <hkern u1="D" u2="&#x201c;" k="10" />
    <hkern u1="D" u2="&#x201e;" k="10" />
    <hkern u1="D" u2="&#x2e;" k="10" />
    <hkern u1="D" u2="&#x2026;" k="10" />
    <hkern u1="D" u2="&#x2c;" k="10" />
    <hkern u1="D" u2="x" k="15" />
    <hkern u1="D" u2="v" k="1" />
    <hkern u1="D" u2="X" k="15" />
    <hkern u1="D" u2="V" k="1" />
    <hkern u1="&#xd0;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd0;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd0;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd0;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd0;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd0;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd0;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd0;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd0;" u2="x" k="15" />
    <hkern u1="&#xd0;" u2="v" k="1" />
    <hkern u1="&#xd0;" u2="X" k="15" />
    <hkern u1="&#xd0;" u2="V" k="1" />
    <hkern u1="&#x10e;" u2="&#x201a;" k="10" />
    <hkern u1="&#x10e;" u2="&#x2018;" k="10" />
    <hkern u1="&#x10e;" u2="&#x201d;" k="10" />
    <hkern u1="&#x10e;" u2="&#x201c;" k="10" />
    <hkern u1="&#x10e;" u2="&#x201e;" k="10" />
    <hkern u1="&#x10e;" u2="&#x2e;" k="10" />
    <hkern u1="&#x10e;" u2="&#x2026;" k="10" />
    <hkern u1="&#x10e;" u2="&#x2c;" k="10" />
    <hkern u1="&#x10e;" u2="x" k="15" />
    <hkern u1="&#x10e;" u2="v" k="1" />
    <hkern u1="&#x10e;" u2="X" k="15" />
    <hkern u1="&#x10e;" u2="V" k="1" />
    <hkern u1="&#x110;" u2="&#x201a;" k="10" />
    <hkern u1="&#x110;" u2="&#x2018;" k="10" />
    <hkern u1="&#x110;" u2="&#x201d;" k="10" />
    <hkern u1="&#x110;" u2="&#x201c;" k="10" />
    <hkern u1="&#x110;" u2="&#x201e;" k="10" />
    <hkern u1="&#x110;" u2="&#x2e;" k="10" />
    <hkern u1="&#x110;" u2="&#x2026;" k="10" />
    <hkern u1="&#x110;" u2="&#x2c;" k="10" />
    <hkern u1="&#x110;" u2="x" k="15" />
    <hkern u1="&#x110;" u2="v" k="1" />
    <hkern u1="&#x110;" u2="X" k="15" />
    <hkern u1="&#x110;" u2="V" k="1" />
    <hkern u1="F" u2="&#x201a;" k="70" />
    <hkern u1="F" u2="&#x201e;" k="70" />
    <hkern u1="F" u2="&#x29;" k="-5" />
    <hkern u1="F" u2="]" k="-5" />
    <hkern u1="F" u2="&#x7d;" k="-5" />
    <hkern u1="F" u2="&#x2e;" k="70" />
    <hkern u1="F" u2="&#x2026;" k="70" />
    <hkern u1="F" u2="&#x2c;" k="70" />
    <hkern u1="G" u2="&#x201a;" k="10" />
    <hkern u1="G" u2="&#x2018;" k="10" />
    <hkern u1="G" u2="&#x201d;" k="10" />
    <hkern u1="G" u2="&#x201c;" k="10" />
    <hkern u1="G" u2="&#x201e;" k="10" />
    <hkern u1="G" u2="&#x2e;" k="10" />
    <hkern u1="G" u2="&#x2026;" k="10" />
    <hkern u1="G" u2="&#x2c;" k="10" />
    <hkern u1="G" u2="x" k="15" />
    <hkern u1="G" u2="v" k="1" />
    <hkern u1="G" u2="X" k="15" />
    <hkern u1="G" u2="V" k="1" />
    <hkern u1="&#x11e;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11e;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11e;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11e;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11e;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11e;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11e;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11e;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11e;" u2="x" k="15" />
    <hkern u1="&#x11e;" u2="v" k="1" />
    <hkern u1="&#x11e;" u2="X" k="15" />
    <hkern u1="&#x11e;" u2="V" k="1" />
    <hkern u1="&#x11c;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11c;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11c;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11c;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11c;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11c;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11c;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11c;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11c;" u2="x" k="15" />
    <hkern u1="&#x11c;" u2="v" k="1" />
    <hkern u1="&#x11c;" u2="X" k="15" />
    <hkern u1="&#x11c;" u2="V" k="1" />
    <hkern u1="&#x120;" u2="&#x201a;" k="10" />
    <hkern u1="&#x120;" u2="&#x2018;" k="10" />
    <hkern u1="&#x120;" u2="&#x201d;" k="10" />
    <hkern u1="&#x120;" u2="&#x201c;" k="10" />
    <hkern u1="&#x120;" u2="&#x201e;" k="10" />
    <hkern u1="&#x120;" u2="&#x2e;" k="10" />
    <hkern u1="&#x120;" u2="&#x2026;" k="10" />
    <hkern u1="&#x120;" u2="&#x2c;" k="10" />
    <hkern u1="&#x120;" u2="x" k="15" />
    <hkern u1="&#x120;" u2="v" k="1" />
    <hkern u1="&#x120;" u2="X" k="15" />
    <hkern u1="&#x120;" u2="V" k="1" />
    <hkern u1="J" u2="&#x201a;" k="6" />
    <hkern u1="J" u2="&#x201e;" k="6" />
    <hkern u1="J" u2="&#x2e;" k="6" />
    <hkern u1="J" u2="&#x2026;" k="6" />
    <hkern u1="J" u2="&#x2c;" k="6" />
    <hkern u1="&#x134;" u2="&#x201a;" k="6" />
    <hkern u1="&#x134;" u2="&#x201e;" k="6" />
    <hkern u1="&#x134;" u2="&#x2e;" k="6" />
    <hkern u1="&#x134;" u2="&#x2026;" k="6" />
    <hkern u1="&#x134;" u2="&#x2c;" k="6" />
    <hkern u1="K" u2="q" k="7" />
    <hkern u1="K" u2="&#x153;" k="7" />
    <hkern u1="K" u2="Q" k="7" />
    <hkern u1="K" u2="&#x152;" k="7" />
    <hkern u1="L" u2="&#x2018;" k="75" />
    <hkern u1="L" u2="&#x201d;" k="75" />
    <hkern u1="L" u2="&#x201c;" k="75" />
    <hkern u1="L" u2="&#x29;" k="-8" />
    <hkern u1="L" u2="]" k="-8" />
    <hkern u1="L" u2="&#x7d;" k="-8" />
    <hkern u1="L" u2="&#x3f;" k="30" />
    <hkern u1="L" u2="v" k="33" />
    <hkern u1="L" u2="V" k="33" />
    <hkern u1="&#x139;" u2="&#x2018;" k="75" />
    <hkern u1="&#x139;" u2="&#x201d;" k="75" />
    <hkern u1="&#x139;" u2="&#x201c;" k="75" />
    <hkern u1="&#x139;" u2="&#x29;" k="-8" />
    <hkern u1="&#x139;" u2="]" k="-8" />
    <hkern u1="&#x139;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x139;" u2="&#x3f;" k="30" />
    <hkern u1="&#x139;" u2="v" k="33" />
    <hkern u1="&#x139;" u2="V" k="33" />
    <hkern u1="&#x13f;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13f;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13f;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13f;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13f;" u2="]" k="-8" />
    <hkern u1="&#x13f;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13f;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13f;" u2="v" k="33" />
    <hkern u1="&#x13f;" u2="V" k="33" />
    <hkern u1="&#x141;" u2="&#x2018;" k="75" />
    <hkern u1="&#x141;" u2="&#x201d;" k="75" />
    <hkern u1="&#x141;" u2="&#x201c;" k="75" />
    <hkern u1="&#x141;" u2="&#x29;" k="-8" />
    <hkern u1="&#x141;" u2="]" k="-8" />
    <hkern u1="&#x141;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x141;" u2="&#x3f;" k="30" />
    <hkern u1="&#x141;" u2="v" k="33" />
    <hkern u1="&#x141;" u2="V" k="33" />
    <hkern u1="O" u2="&#x201a;" k="10" />
    <hkern u1="O" u2="&#x2018;" k="10" />
    <hkern u1="O" u2="&#x201d;" k="10" />
    <hkern u1="O" u2="&#x201c;" k="10" />
    <hkern u1="O" u2="&#x201e;" k="10" />
    <hkern u1="O" u2="&#x2e;" k="10" />
    <hkern u1="O" u2="&#x2026;" k="10" />
    <hkern u1="O" u2="&#x2c;" k="10" />
    <hkern u1="O" u2="x" k="15" />
    <hkern u1="O" u2="v" k="1" />
    <hkern u1="O" u2="X" k="15" />
    <hkern u1="O" u2="V" k="1" />
    <hkern u1="&#xd3;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd3;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd3;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd3;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd3;" u2="x" k="15" />
    <hkern u1="&#xd3;" u2="v" k="1" />
    <hkern u1="&#xd3;" u2="X" k="15" />
    <hkern u1="&#xd3;" u2="V" k="1" />
    <hkern u1="&#x14e;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14e;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14e;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14e;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14e;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14e;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14e;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14e;" u2="&#x2c;" k="10" />
    <hkern u1="&#x14e;" u2="x" k="15" />
    <hkern u1="&#x14e;" u2="v" k="1" />
    <hkern u1="&#x14e;" u2="X" k="15" />
    <hkern u1="&#x14e;" u2="V" k="1" />
    <hkern u1="&#xd4;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd4;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd4;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd4;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd4;" u2="x" k="15" />
    <hkern u1="&#xd4;" u2="v" k="1" />
    <hkern u1="&#xd4;" u2="X" k="15" />
    <hkern u1="&#xd4;" u2="V" k="1" />
    <hkern u1="&#xd6;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd6;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd6;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd6;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd6;" u2="x" k="15" />
    <hkern u1="&#xd6;" u2="v" k="1" />
    <hkern u1="&#xd6;" u2="X" k="15" />
    <hkern u1="&#xd6;" u2="V" k="1" />
    <hkern u1="&#xd2;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd2;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd2;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd2;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd2;" u2="x" k="15" />
    <hkern u1="&#xd2;" u2="v" k="1" />
    <hkern u1="&#xd2;" u2="X" k="15" />
    <hkern u1="&#xd2;" u2="V" k="1" />
    <hkern u1="&#x150;" u2="&#x201a;" k="10" />
    <hkern u1="&#x150;" u2="&#x2018;" k="10" />
    <hkern u1="&#x150;" u2="&#x201d;" k="10" />
    <hkern u1="&#x150;" u2="&#x201c;" k="10" />
    <hkern u1="&#x150;" u2="&#x201e;" k="10" />
    <hkern u1="&#x150;" u2="&#x2e;" k="10" />
    <hkern u1="&#x150;" u2="&#x2026;" k="10" />
    <hkern u1="&#x150;" u2="&#x2c;" k="10" />
    <hkern u1="&#x150;" u2="x" k="15" />
    <hkern u1="&#x150;" u2="v" k="1" />
    <hkern u1="&#x150;" u2="X" k="15" />
    <hkern u1="&#x150;" u2="V" k="1" />
    <hkern u1="&#x14c;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14c;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14c;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14c;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14c;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14c;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14c;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14c;" u2="&#x2c;" k="10" />
    <hkern u1="&#x14c;" u2="x" k="15" />
    <hkern u1="&#x14c;" u2="v" k="1" />
    <hkern u1="&#x14c;" u2="X" k="15" />
    <hkern u1="&#x14c;" u2="V" k="1" />
    <hkern u1="&#xd8;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd8;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd8;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd8;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd8;" u2="x" k="15" />
    <hkern u1="&#xd8;" u2="v" k="1" />
    <hkern u1="&#xd8;" u2="X" k="15" />
    <hkern u1="&#xd8;" u2="V" k="1" />
    <hkern u1="&#x1fe;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x2c;" k="10" />
    <hkern u1="&#x1fe;" u2="x" k="15" />
    <hkern u1="&#x1fe;" u2="v" k="1" />
    <hkern u1="&#x1fe;" u2="X" k="15" />
    <hkern u1="&#x1fe;" u2="V" k="1" />
    <hkern u1="&#xd5;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd5;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd5;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd5;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd5;" u2="x" k="15" />
    <hkern u1="&#xd5;" u2="v" k="1" />
    <hkern u1="&#xd5;" u2="X" k="15" />
    <hkern u1="&#xd5;" u2="V" k="1" />
    <hkern u1="P" u2="&#x201a;" k="80" />
    <hkern u1="P" u2="&#x201e;" k="80" />
    <hkern u1="P" u2="&#x2e;" k="80" />
    <hkern u1="P" u2="&#x2026;" k="80" />
    <hkern u1="P" u2="&#x2c;" k="80" />
    <hkern u1="P" u2="x" k="11" />
    <hkern u1="P" u2="X" k="11" />
    <hkern u1="&#xde;" u2="&#xc1;" k="2" />
    <hkern u1="&#xde;" u2="A" k="2" />
    <hkern u1="&#xde;" u2="&#x102;" k="2" />
    <hkern u1="&#xde;" u2="&#x201a;" k="80" />
    <hkern u1="&#xde;" u2="&#x201e;" k="80" />
    <hkern u1="&#xde;" u2="&#x2e;" k="80" />
    <hkern u1="&#xde;" u2="&#x2026;" k="80" />
    <hkern u1="&#xde;" u2="&#x2c;" k="80" />
    <hkern u1="&#xde;" u2="&#x17c;" k="10" />
    <hkern u1="&#xde;" u2="&#x17e;" k="10" />
    <hkern u1="&#xde;" u2="&#x17a;" k="10" />
    <hkern u1="&#xde;" u2="z" k="10" />
    <hkern u1="&#xde;" u2="&#x1ef3;" k="9" />
    <hkern u1="&#xde;" u2="&#xff;" k="9" />
    <hkern u1="&#xde;" u2="&#x177;" k="9" />
    <hkern u1="&#xde;" u2="&#xfd;" k="9" />
    <hkern u1="&#xde;" u2="y" k="9" />
    <hkern u1="&#xde;" u2="x" k="20" />
    <hkern u1="&#xde;" u2="&#x135;" k="8" />
    <hkern u1="&#xde;" u2="&#x237;" k="8" />
    <hkern u1="&#xde;" u2="j" k="8" />
    <hkern u1="&#xde;" u2="&#x133;" k="8" />
    <hkern u1="&#xde;" u2="&#x1fd;" k="20" />
    <hkern u1="&#xde;" u2="&#xe6;" k="20" />
    <hkern u1="&#xde;" u2="&#xe3;" k="2" />
    <hkern u1="&#xde;" u2="&#xe5;" k="2" />
    <hkern u1="&#xde;" u2="&#x105;" k="2" />
    <hkern u1="&#xde;" u2="&#x101;" k="2" />
    <hkern u1="&#xde;" u2="&#xe0;" k="2" />
    <hkern u1="&#xde;" u2="&#xe4;" k="2" />
    <hkern u1="&#xde;" u2="&#xe2;" k="2" />
    <hkern u1="&#xde;" u2="&#x103;" k="2" />
    <hkern u1="&#xde;" u2="&#xe1;" k="2" />
    <hkern u1="&#xde;" u2="a" k="2" />
    <hkern u1="&#xde;" u2="&#x17b;" k="10" />
    <hkern u1="&#xde;" u2="&#x17d;" k="10" />
    <hkern u1="&#xde;" u2="&#x179;" k="10" />
    <hkern u1="&#xde;" u2="Z" k="10" />
    <hkern u1="&#xde;" u2="&#x1ef2;" k="9" />
    <hkern u1="&#xde;" u2="&#x178;" k="9" />
    <hkern u1="&#xde;" u2="&#x176;" k="9" />
    <hkern u1="&#xde;" u2="&#xdd;" k="9" />
    <hkern u1="&#xde;" u2="Y" k="9" />
    <hkern u1="&#xde;" u2="X" k="20" />
    <hkern u1="&#xde;" u2="&#x134;" k="8" />
    <hkern u1="&#xde;" u2="J" k="8" />
    <hkern u1="&#xde;" u2="&#x132;" k="8" />
    <hkern u1="&#xde;" u2="&#x1fc;" k="20" />
    <hkern u1="&#xde;" u2="&#xc6;" k="20" />
    <hkern u1="&#xde;" u2="&#xc3;" k="2" />
    <hkern u1="&#xde;" u2="&#xc5;" k="2" />
    <hkern u1="&#xde;" u2="&#x104;" k="2" />
    <hkern u1="&#xde;" u2="&#x100;" k="2" />
    <hkern u1="&#xde;" u2="&#xc0;" k="2" />
    <hkern u1="&#xde;" u2="&#xc4;" k="2" />
    <hkern u1="&#xde;" u2="&#xc2;" k="2" />
    <hkern u1="Q" u2="&#x2bc;" k="10" />
    <hkern u1="Q" u2="&#x2019;" k="10" />
    <hkern u1="Q" u2="&#x2018;" k="10" />
    <hkern u1="Q" u2="&#x201d;" k="10" />
    <hkern u1="Q" u2="&#x201c;" k="10" />
    <hkern u1="Q" u2="&#x1ef3;" k="13" />
    <hkern u1="Q" u2="&#xff;" k="13" />
    <hkern u1="Q" u2="&#x177;" k="13" />
    <hkern u1="Q" u2="&#xfd;" k="13" />
    <hkern u1="Q" u2="y" k="13" />
    <hkern u1="Q" u2="v" k="1" />
    <hkern u1="Q" u2="&#x1ef2;" k="13" />
    <hkern u1="Q" u2="&#x178;" k="13" />
    <hkern u1="Q" u2="&#x176;" k="13" />
    <hkern u1="Q" u2="&#xdd;" k="13" />
    <hkern u1="Q" u2="Y" k="13" />
    <hkern u1="Q" u2="V" k="1" />
    <hkern u1="R" u2="v" k="2" />
    <hkern u1="R" u2="V" k="2" />
    <hkern u1="&#x154;" u2="v" k="2" />
    <hkern u1="&#x154;" u2="V" k="2" />
    <hkern u1="&#x158;" u2="v" k="2" />
    <hkern u1="&#x158;" u2="V" k="2" />
    <hkern u1="S" u2="&#x201a;" k="5" />
    <hkern u1="S" u2="&#x2018;" k="5" />
    <hkern u1="S" u2="&#x201d;" k="5" />
    <hkern u1="S" u2="&#x201c;" k="5" />
    <hkern u1="S" u2="&#x201e;" k="5" />
    <hkern u1="S" u2="&#x2e;" k="5" />
    <hkern u1="S" u2="&#x2026;" k="5" />
    <hkern u1="S" u2="&#x2c;" k="5" />
    <hkern u1="S" u2="x" k="8" />
    <hkern u1="S" u2="v" k="1" />
    <hkern u1="S" u2="X" k="8" />
    <hkern u1="S" u2="V" k="1" />
    <hkern u1="&#x15a;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15a;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15a;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15a;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15a;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15a;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15a;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15a;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15a;" u2="x" k="8" />
    <hkern u1="&#x15a;" u2="v" k="1" />
    <hkern u1="&#x15a;" u2="X" k="8" />
    <hkern u1="&#x15a;" u2="V" k="1" />
    <hkern u1="&#x160;" u2="&#x201a;" k="5" />
    <hkern u1="&#x160;" u2="&#x2018;" k="5" />
    <hkern u1="&#x160;" u2="&#x201d;" k="5" />
    <hkern u1="&#x160;" u2="&#x201c;" k="5" />
    <hkern u1="&#x160;" u2="&#x201e;" k="5" />
    <hkern u1="&#x160;" u2="&#x2e;" k="5" />
    <hkern u1="&#x160;" u2="&#x2026;" k="5" />
    <hkern u1="&#x160;" u2="&#x2c;" k="5" />
    <hkern u1="&#x160;" u2="x" k="8" />
    <hkern u1="&#x160;" u2="v" k="1" />
    <hkern u1="&#x160;" u2="X" k="8" />
    <hkern u1="&#x160;" u2="V" k="1" />
    <hkern u1="&#x15e;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15e;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15e;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15e;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15e;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15e;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15e;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15e;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15e;" u2="x" k="8" />
    <hkern u1="&#x15e;" u2="v" k="1" />
    <hkern u1="&#x15e;" u2="X" k="8" />
    <hkern u1="&#x15e;" u2="V" k="1" />
    <hkern u1="&#x15c;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15c;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15c;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15c;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15c;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15c;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15c;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15c;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15c;" u2="x" k="8" />
    <hkern u1="&#x15c;" u2="v" k="1" />
    <hkern u1="&#x15c;" u2="X" k="8" />
    <hkern u1="&#x15c;" u2="V" k="1" />
    <hkern u1="T" u2="&#x201a;" k="60" />
    <hkern u1="T" u2="&#x201e;" k="60" />
    <hkern u1="T" u2="&#x29;" k="-8" />
    <hkern u1="T" u2="]" k="-8" />
    <hkern u1="T" u2="&#x7d;" k="-8" />
    <hkern u1="T" u2="&#x3b;" k="35" />
    <hkern u1="T" u2="&#x2e;" k="60" />
    <hkern u1="T" u2="&#x2026;" k="60" />
    <hkern u1="T" u2="&#x2c;" k="60" />
    <hkern u1="T" u2="&#x3a;" k="35" />
    <hkern u1="&#x166;" u2="&#x201a;" k="60" />
    <hkern u1="&#x166;" u2="&#x201e;" k="60" />
    <hkern u1="&#x166;" u2="&#x29;" k="-8" />
    <hkern u1="&#x166;" u2="]" k="-8" />
    <hkern u1="&#x166;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x166;" u2="&#x3b;" k="35" />
    <hkern u1="&#x166;" u2="&#x2e;" k="60" />
    <hkern u1="&#x166;" u2="&#x2026;" k="60" />
    <hkern u1="&#x166;" u2="&#x2c;" k="60" />
    <hkern u1="&#x166;" u2="&#x3a;" k="35" />
    <hkern u1="&#x164;" u2="&#x201a;" k="60" />
    <hkern u1="&#x164;" u2="&#x201e;" k="60" />
    <hkern u1="&#x164;" u2="&#x29;" k="-8" />
    <hkern u1="&#x164;" u2="]" k="-8" />
    <hkern u1="&#x164;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x164;" u2="&#x3b;" k="35" />
    <hkern u1="&#x164;" u2="&#x2e;" k="60" />
    <hkern u1="&#x164;" u2="&#x2026;" k="60" />
    <hkern u1="&#x164;" u2="&#x2c;" k="60" />
    <hkern u1="&#x164;" u2="&#x3a;" k="35" />
    <hkern u1="U" u2="&#x201a;" k="6" />
    <hkern u1="U" u2="&#x201e;" k="6" />
    <hkern u1="U" u2="&#x2e;" k="6" />
    <hkern u1="U" u2="&#x2026;" k="6" />
    <hkern u1="U" u2="&#x2c;" k="6" />
    <hkern u1="&#xda;" u2="&#x201a;" k="6" />
    <hkern u1="&#xda;" u2="&#x201e;" k="6" />
    <hkern u1="&#xda;" u2="&#x2e;" k="6" />
    <hkern u1="&#xda;" u2="&#x2026;" k="6" />
    <hkern u1="&#xda;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16c;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16c;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16c;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16c;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16c;" u2="&#x2c;" k="6" />
    <hkern u1="&#xdb;" u2="&#x201a;" k="6" />
    <hkern u1="&#xdb;" u2="&#x201e;" k="6" />
    <hkern u1="&#xdb;" u2="&#x2e;" k="6" />
    <hkern u1="&#xdb;" u2="&#x2026;" k="6" />
    <hkern u1="&#xdb;" u2="&#x2c;" k="6" />
    <hkern u1="&#xdc;" u2="&#x201a;" k="6" />
    <hkern u1="&#xdc;" u2="&#x201e;" k="6" />
    <hkern u1="&#xdc;" u2="&#x2e;" k="6" />
    <hkern u1="&#xdc;" u2="&#x2026;" k="6" />
    <hkern u1="&#xdc;" u2="&#x2c;" k="6" />
    <hkern u1="&#xd9;" u2="&#x201a;" k="6" />
    <hkern u1="&#xd9;" u2="&#x201e;" k="6" />
    <hkern u1="&#xd9;" u2="&#x2e;" k="6" />
    <hkern u1="&#xd9;" u2="&#x2026;" k="6" />
    <hkern u1="&#xd9;" u2="&#x2c;" k="6" />
    <hkern u1="&#x170;" u2="&#x201a;" k="6" />
    <hkern u1="&#x170;" u2="&#x201e;" k="6" />
    <hkern u1="&#x170;" u2="&#x2e;" k="6" />
    <hkern u1="&#x170;" u2="&#x2026;" k="6" />
    <hkern u1="&#x170;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16a;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16a;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16a;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16a;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16a;" u2="&#x2c;" k="6" />
    <hkern u1="&#x172;" u2="&#x201a;" k="6" />
    <hkern u1="&#x172;" u2="&#x201e;" k="6" />
    <hkern u1="&#x172;" u2="&#x2e;" k="6" />
    <hkern u1="&#x172;" u2="&#x2026;" k="6" />
    <hkern u1="&#x172;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16e;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16e;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16e;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16e;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16e;" u2="&#x2c;" k="6" />
    <hkern u1="&#x168;" u2="&#x201a;" k="6" />
    <hkern u1="&#x168;" u2="&#x201e;" k="6" />
    <hkern u1="&#x168;" u2="&#x2e;" k="6" />
    <hkern u1="&#x168;" u2="&#x2026;" k="6" />
    <hkern u1="&#x168;" u2="&#x2c;" k="6" />
    <hkern u1="V" u2="&#xc1;" k="25" />
    <hkern u1="V" u2="A" k="25" />
    <hkern u1="V" u2="&#x102;" k="25" />
    <hkern u1="V" u2="&#x201a;" k="60" />
    <hkern u1="V" u2="&#x201e;" k="60" />
    <hkern u1="V" u2="&#xad;" k="10" />
    <hkern u1="V" u2="&#x2d;" k="10" />
    <hkern u1="V" u2="&#x29;" k="-3" />
    <hkern u1="V" u2="]" k="-3" />
    <hkern u1="V" u2="&#x7d;" k="-3" />
    <hkern u1="V" u2="&#x2e;" k="60" />
    <hkern u1="V" u2="&#x2026;" k="60" />
    <hkern u1="V" u2="&#x2c;" k="60" />
    <hkern u1="V" u2="&#x123;" k="1" />
    <hkern u1="V" u2="q" k="1" />
    <hkern u1="V" u2="&#x153;" k="1" />
    <hkern u1="V" u2="&#xf5;" k="1" />
    <hkern u1="V" u2="&#x1ff;" k="1" />
    <hkern u1="V" u2="&#xf8;" k="1" />
    <hkern u1="V" u2="&#x14d;" k="1" />
    <hkern u1="V" u2="&#x151;" k="1" />
    <hkern u1="V" u2="&#xf2;" k="1" />
    <hkern u1="V" u2="&#xf6;" k="1" />
    <hkern u1="V" u2="&#xf4;" k="1" />
    <hkern u1="V" u2="&#x14f;" k="1" />
    <hkern u1="V" u2="&#xf3;" k="1" />
    <hkern u1="V" u2="o" k="1" />
    <hkern u1="V" u2="&#x135;" k="25" />
    <hkern u1="V" u2="&#x237;" k="25" />
    <hkern u1="V" u2="j" k="25" />
    <hkern u1="V" u2="&#x133;" k="25" />
    <hkern u1="V" u2="&#x121;" k="1" />
    <hkern u1="V" u2="&#x11d;" k="1" />
    <hkern u1="V" u2="&#x11f;" k="1" />
    <hkern u1="V" u2="g" k="1" />
    <hkern u1="V" u2="&#x10b;" k="1" />
    <hkern u1="V" u2="&#x109;" k="1" />
    <hkern u1="V" u2="&#xe7;" k="1" />
    <hkern u1="V" u2="&#x10d;" k="1" />
    <hkern u1="V" u2="&#x107;" k="1" />
    <hkern u1="V" u2="c" k="1" />
    <hkern u1="V" u2="&#x1fd;" k="46" />
    <hkern u1="V" u2="&#xe6;" k="46" />
    <hkern u1="V" u2="&#xe3;" k="25" />
    <hkern u1="V" u2="&#xe5;" k="25" />
    <hkern u1="V" u2="&#x105;" k="25" />
    <hkern u1="V" u2="&#x101;" k="25" />
    <hkern u1="V" u2="&#xe0;" k="25" />
    <hkern u1="V" u2="&#xe4;" k="25" />
    <hkern u1="V" u2="&#xe2;" k="25" />
    <hkern u1="V" u2="&#x103;" k="25" />
    <hkern u1="V" u2="&#xe1;" k="25" />
    <hkern u1="V" u2="a" k="25" />
    <hkern u1="V" u2="&#x122;" k="1" />
    <hkern u1="V" u2="Q" k="1" />
    <hkern u1="V" u2="&#x152;" k="1" />
    <hkern u1="V" u2="&#xd5;" k="1" />
    <hkern u1="V" u2="&#x1fe;" k="1" />
    <hkern u1="V" u2="&#xd8;" k="1" />
    <hkern u1="V" u2="&#x14c;" k="1" />
    <hkern u1="V" u2="&#x150;" k="1" />
    <hkern u1="V" u2="&#xd2;" k="1" />
    <hkern u1="V" u2="&#xd6;" k="1" />
    <hkern u1="V" u2="&#xd4;" k="1" />
    <hkern u1="V" u2="&#x14e;" k="1" />
    <hkern u1="V" u2="&#xd3;" k="1" />
    <hkern u1="V" u2="O" k="1" />
    <hkern u1="V" u2="&#x134;" k="25" />
    <hkern u1="V" u2="J" k="25" />
    <hkern u1="V" u2="&#x132;" k="25" />
    <hkern u1="V" u2="&#x120;" k="1" />
    <hkern u1="V" u2="&#x11c;" k="1" />
    <hkern u1="V" u2="&#x11e;" k="1" />
    <hkern u1="V" u2="G" k="1" />
    <hkern u1="V" u2="&#x10a;" k="1" />
    <hkern u1="V" u2="&#x108;" k="1" />
    <hkern u1="V" u2="&#xc7;" k="1" />
    <hkern u1="V" u2="&#x10c;" k="1" />
    <hkern u1="V" u2="&#x106;" k="1" />
    <hkern u1="V" u2="C" k="1" />
    <hkern u1="V" u2="&#x1fc;" k="46" />
    <hkern u1="V" u2="&#xc6;" k="46" />
    <hkern u1="V" u2="&#xc3;" k="25" />
    <hkern u1="V" u2="&#xc5;" k="25" />
    <hkern u1="V" u2="&#x104;" k="25" />
    <hkern u1="V" u2="&#x100;" k="25" />
    <hkern u1="V" u2="&#xc0;" k="25" />
    <hkern u1="V" u2="&#xc4;" k="25" />
    <hkern u1="V" u2="&#xc2;" k="25" />
    <hkern u1="W" u2="&#x201a;" k="40" />
    <hkern u1="W" u2="&#x201e;" k="40" />
    <hkern u1="W" u2="&#x29;" k="-3" />
    <hkern u1="W" u2="]" k="-3" />
    <hkern u1="W" u2="&#x7d;" k="-3" />
    <hkern u1="W" u2="&#x2e;" k="40" />
    <hkern u1="W" u2="&#x2026;" k="40" />
    <hkern u1="W" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e82;" u2="]" k="-3" />
    <hkern u1="&#x1e82;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e82;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x2c;" k="40" />
    <hkern u1="&#x174;" u2="&#x201a;" k="40" />
    <hkern u1="&#x174;" u2="&#x201e;" k="40" />
    <hkern u1="&#x174;" u2="&#x29;" k="-3" />
    <hkern u1="&#x174;" u2="]" k="-3" />
    <hkern u1="&#x174;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x174;" u2="&#x2e;" k="40" />
    <hkern u1="&#x174;" u2="&#x2026;" k="40" />
    <hkern u1="&#x174;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e84;" u2="]" k="-3" />
    <hkern u1="&#x1e84;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e84;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e80;" u2="]" k="-3" />
    <hkern u1="&#x1e80;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e80;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x2c;" k="40" />
    <hkern u1="X" u2="&#x2bc;" k="2" />
    <hkern u1="X" u2="&#x2019;" k="2" />
    <hkern u1="X" u2="&#x2018;" k="2" />
    <hkern u1="X" u2="&#x201d;" k="2" />
    <hkern u1="X" u2="&#x201c;" k="2" />
    <hkern u1="X" u2="&#xad;" k="15" />
    <hkern u1="X" u2="&#x2d;" k="15" />
    <hkern u1="X" u2="&#x1e61;" k="8" />
    <hkern u1="X" u2="&#x219;" k="8" />
    <hkern u1="X" u2="&#x123;" k="15" />
    <hkern u1="X" u2="&#xdf;" k="8" />
    <hkern u1="X" u2="&#x15d;" k="8" />
    <hkern u1="X" u2="&#x15f;" k="8" />
    <hkern u1="X" u2="&#x161;" k="8" />
    <hkern u1="X" u2="&#x15b;" k="8" />
    <hkern u1="X" u2="s" k="8" />
    <hkern u1="X" u2="q" k="15" />
    <hkern u1="X" u2="&#x153;" k="15" />
    <hkern u1="X" u2="&#xf5;" k="15" />
    <hkern u1="X" u2="&#x1ff;" k="15" />
    <hkern u1="X" u2="&#xf8;" k="15" />
    <hkern u1="X" u2="&#x14d;" k="15" />
    <hkern u1="X" u2="&#x151;" k="15" />
    <hkern u1="X" u2="&#xf2;" k="15" />
    <hkern u1="X" u2="&#xf6;" k="15" />
    <hkern u1="X" u2="&#xf4;" k="15" />
    <hkern u1="X" u2="&#x14f;" k="15" />
    <hkern u1="X" u2="&#xf3;" k="15" />
    <hkern u1="X" u2="o" k="15" />
    <hkern u1="X" u2="&#x121;" k="15" />
    <hkern u1="X" u2="&#x11d;" k="15" />
    <hkern u1="X" u2="&#x11f;" k="15" />
    <hkern u1="X" u2="g" k="15" />
    <hkern u1="X" u2="&#x10b;" k="15" />
    <hkern u1="X" u2="&#x109;" k="15" />
    <hkern u1="X" u2="&#xe7;" k="15" />
    <hkern u1="X" u2="&#x10d;" k="15" />
    <hkern u1="X" u2="&#x107;" k="15" />
    <hkern u1="X" u2="c" k="15" />
    <hkern u1="X" u2="&#x1e60;" k="8" />
    <hkern u1="X" u2="&#x218;" k="8" />
    <hkern u1="X" u2="&#x122;" k="15" />
    <hkern u1="X" u2="&#x15c;" k="8" />
    <hkern u1="X" u2="&#x15e;" k="8" />
    <hkern u1="X" u2="&#x160;" k="8" />
    <hkern u1="X" u2="&#x15a;" k="8" />
    <hkern u1="X" u2="S" k="8" />
    <hkern u1="X" u2="Q" k="15" />
    <hkern u1="X" u2="&#x152;" k="15" />
    <hkern u1="X" u2="&#xd5;" k="15" />
    <hkern u1="X" u2="&#x1fe;" k="15" />
    <hkern u1="X" u2="&#xd8;" k="15" />
    <hkern u1="X" u2="&#x14c;" k="15" />
    <hkern u1="X" u2="&#x150;" k="15" />
    <hkern u1="X" u2="&#xd2;" k="15" />
    <hkern u1="X" u2="&#xd6;" k="15" />
    <hkern u1="X" u2="&#xd4;" k="15" />
    <hkern u1="X" u2="&#x14e;" k="15" />
    <hkern u1="X" u2="&#xd3;" k="15" />
    <hkern u1="X" u2="O" k="15" />
    <hkern u1="X" u2="&#x120;" k="15" />
    <hkern u1="X" u2="&#x11c;" k="15" />
    <hkern u1="X" u2="&#x11e;" k="15" />
    <hkern u1="X" u2="G" k="15" />
    <hkern u1="X" u2="&#x10a;" k="15" />
    <hkern u1="X" u2="&#x108;" k="15" />
    <hkern u1="X" u2="&#xc7;" k="15" />
    <hkern u1="X" u2="&#x10c;" k="15" />
    <hkern u1="X" u2="&#x106;" k="15" />
    <hkern u1="X" u2="C" k="15" />
    <hkern u1="Y" u2="&#x201a;" k="80" />
    <hkern u1="Y" u2="&#x201e;" k="80" />
    <hkern u1="Y" u2="&#x29;" k="-5" />
    <hkern u1="Y" u2="]" k="-5" />
    <hkern u1="Y" u2="&#x7d;" k="-5" />
    <hkern u1="Y" u2="&#x3b;" k="20" />
    <hkern u1="Y" u2="&#x2e;" k="80" />
    <hkern u1="Y" u2="&#x2026;" k="80" />
    <hkern u1="Y" u2="&#x2c;" k="80" />
    <hkern u1="Y" u2="&#x3a;" k="20" />
    <hkern u1="Y" u2="q" k="15" />
    <hkern u1="Y" u2="&#x153;" k="15" />
    <hkern u1="Y" u2="Q" k="15" />
    <hkern u1="Y" u2="&#x152;" k="15" />
    <hkern u1="&#xdd;" u2="&#x201a;" k="80" />
    <hkern u1="&#xdd;" u2="&#x201e;" k="80" />
    <hkern u1="&#xdd;" u2="&#x29;" k="-5" />
    <hkern u1="&#xdd;" u2="]" k="-5" />
    <hkern u1="&#xdd;" u2="&#x7d;" k="-5" />
    <hkern u1="&#xdd;" u2="&#x3b;" k="20" />
    <hkern u1="&#xdd;" u2="&#x2e;" k="80" />
    <hkern u1="&#xdd;" u2="&#x2026;" k="80" />
    <hkern u1="&#xdd;" u2="&#x2c;" k="80" />
    <hkern u1="&#xdd;" u2="&#x3a;" k="20" />
    <hkern u1="&#xdd;" u2="q" k="15" />
    <hkern u1="&#xdd;" u2="&#x153;" k="15" />
    <hkern u1="&#xdd;" u2="Q" k="15" />
    <hkern u1="&#xdd;" u2="&#x152;" k="15" />
    <hkern u1="&#x176;" u2="&#x201a;" k="80" />
    <hkern u1="&#x176;" u2="&#x201e;" k="80" />
    <hkern u1="&#x176;" u2="&#x29;" k="-5" />
    <hkern u1="&#x176;" u2="]" k="-5" />
    <hkern u1="&#x176;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x176;" u2="&#x3b;" k="20" />
    <hkern u1="&#x176;" u2="&#x2e;" k="80" />
    <hkern u1="&#x176;" u2="&#x2026;" k="80" />
    <hkern u1="&#x176;" u2="&#x2c;" k="80" />
    <hkern u1="&#x176;" u2="&#x3a;" k="20" />
    <hkern u1="&#x176;" u2="q" k="15" />
    <hkern u1="&#x176;" u2="&#x153;" k="15" />
    <hkern u1="&#x176;" u2="Q" k="15" />
    <hkern u1="&#x176;" u2="&#x152;" k="15" />
    <hkern u1="&#x178;" u2="&#x201a;" k="80" />
    <hkern u1="&#x178;" u2="&#x201e;" k="80" />
    <hkern u1="&#x178;" u2="&#x29;" k="-5" />
    <hkern u1="&#x178;" u2="]" k="-5" />
    <hkern u1="&#x178;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x178;" u2="&#x3b;" k="20" />
    <hkern u1="&#x178;" u2="&#x2e;" k="80" />
    <hkern u1="&#x178;" u2="&#x2026;" k="80" />
    <hkern u1="&#x178;" u2="&#x2c;" k="80" />
    <hkern u1="&#x178;" u2="&#x3a;" k="20" />
    <hkern u1="&#x178;" u2="q" k="15" />
    <hkern u1="&#x178;" u2="&#x153;" k="15" />
    <hkern u1="&#x178;" u2="Q" k="15" />
    <hkern u1="&#x178;" u2="&#x152;" k="15" />
    <hkern u1="&#x1ef2;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1ef2;" u2="]" k="-5" />
    <hkern u1="&#x1ef2;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1ef2;" u2="&#x3b;" k="20" />
    <hkern u1="&#x1ef2;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x3a;" k="20" />
    <hkern u1="&#x1ef2;" u2="q" k="15" />
    <hkern u1="&#x1ef2;" u2="&#x153;" k="15" />
    <hkern u1="&#x1ef2;" u2="Q" k="15" />
    <hkern u1="&#x1ef2;" u2="&#x152;" k="15" />
    <hkern u1="&#x122;" u2="&#x201a;" k="10" />
    <hkern u1="&#x122;" u2="&#x2018;" k="10" />
    <hkern u1="&#x122;" u2="&#x201d;" k="10" />
    <hkern u1="&#x122;" u2="&#x201c;" k="10" />
    <hkern u1="&#x122;" u2="&#x201e;" k="10" />
    <hkern u1="&#x122;" u2="&#x2e;" k="10" />
    <hkern u1="&#x122;" u2="&#x2026;" k="10" />
    <hkern u1="&#x122;" u2="&#x2c;" k="10" />
    <hkern u1="&#x122;" u2="x" k="15" />
    <hkern u1="&#x122;" u2="v" k="1" />
    <hkern u1="&#x122;" u2="X" k="15" />
    <hkern u1="&#x122;" u2="V" k="1" />
    <hkern u1="&#x136;" u2="q" k="7" />
    <hkern u1="&#x136;" u2="&#x153;" k="7" />
    <hkern u1="&#x136;" u2="Q" k="7" />
    <hkern u1="&#x136;" u2="&#x152;" k="7" />
    <hkern u1="&#x13b;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13b;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13b;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13b;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13b;" u2="]" k="-8" />
    <hkern u1="&#x13b;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13b;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13b;" u2="v" k="33" />
    <hkern u1="&#x13b;" u2="V" k="33" />
    <hkern u1="&#x156;" u2="v" k="2" />
    <hkern u1="&#x156;" u2="V" k="2" />
    <hkern u1="&#x162;" u2="&#x201a;" k="60" />
    <hkern u1="&#x162;" u2="&#x201e;" k="60" />
    <hkern u1="&#x162;" u2="&#x29;" k="-8" />
    <hkern u1="&#x162;" u2="]" k="-8" />
    <hkern u1="&#x162;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x162;" u2="&#x3b;" k="35" />
    <hkern u1="&#x162;" u2="&#x2e;" k="60" />
    <hkern u1="&#x162;" u2="&#x2026;" k="60" />
    <hkern u1="&#x162;" u2="&#x2c;" k="60" />
    <hkern u1="&#x162;" u2="&#x3a;" k="35" />
    <hkern u1="&#x218;" u2="&#x201a;" k="5" />
    <hkern u1="&#x218;" u2="&#x2018;" k="5" />
    <hkern u1="&#x218;" u2="&#x201d;" k="5" />
    <hkern u1="&#x218;" u2="&#x201c;" k="5" />
    <hkern u1="&#x218;" u2="&#x201e;" k="5" />
    <hkern u1="&#x218;" u2="&#x2e;" k="5" />
    <hkern u1="&#x218;" u2="&#x2026;" k="5" />
    <hkern u1="&#x218;" u2="&#x2c;" k="5" />
    <hkern u1="&#x218;" u2="x" k="8" />
    <hkern u1="&#x218;" u2="v" k="1" />
    <hkern u1="&#x218;" u2="X" k="8" />
    <hkern u1="&#x218;" u2="V" k="1" />
    <hkern u1="&#x21a;" u2="&#x201a;" k="60" />
    <hkern u1="&#x21a;" u2="&#x201e;" k="60" />
    <hkern u1="&#x21a;" u2="&#x29;" k="-8" />
    <hkern u1="&#x21a;" u2="]" k="-8" />
    <hkern u1="&#x21a;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x21a;" u2="&#x3b;" k="35" />
    <hkern u1="&#x21a;" u2="&#x2e;" k="60" />
    <hkern u1="&#x21a;" u2="&#x2026;" k="60" />
    <hkern u1="&#x21a;" u2="&#x2c;" k="60" />
    <hkern u1="&#x21a;" u2="&#x3a;" k="35" />
    <hkern u1="&#x1e02;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e02;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e02;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e02;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e02;" u2="x" k="3" />
    <hkern u1="&#x1e02;" u2="v" k="4" />
    <hkern u1="&#x1e02;" u2="X" k="3" />
    <hkern u1="&#x1e02;" u2="V" k="4" />
    <hkern u1="&#x1e0a;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x2c;" k="10" />
    <hkern u1="&#x1e0a;" u2="x" k="15" />
    <hkern u1="&#x1e0a;" u2="v" k="1" />
    <hkern u1="&#x1e0a;" u2="X" k="15" />
    <hkern u1="&#x1e0a;" u2="V" k="1" />
    <hkern u1="&#x1e1e;" u2="&#x201a;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x201e;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1e1e;" u2="]" k="-5" />
    <hkern u1="&#x1e1e;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1e1e;" u2="&#x2e;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x2026;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x2c;" k="70" />
    <hkern u1="&#x1e56;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1e56;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1e56;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1e56;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1e56;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1e56;" u2="x" k="11" />
    <hkern u1="&#x1e56;" u2="X" k="11" />
    <hkern u1="&#x1e60;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x2018;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x201d;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x201c;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e60;" u2="x" k="8" />
    <hkern u1="&#x1e60;" u2="v" k="1" />
    <hkern u1="&#x1e60;" u2="X" k="8" />
    <hkern u1="&#x1e60;" u2="V" k="1" />
    <hkern u1="&#x1e6a;" u2="&#x201a;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x201e;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x29;" k="-8" />
    <hkern u1="&#x1e6a;" u2="]" k="-8" />
    <hkern u1="&#x1e6a;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x1e6a;" u2="&#x3b;" k="35" />
    <hkern u1="&#x1e6a;" u2="&#x2e;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x2026;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x2c;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x3a;" k="35" />
    <hkern u1="a" u2="&#x2018;" k="60" />
    <hkern u1="a" u2="&#x201d;" k="60" />
    <hkern u1="a" u2="&#x201c;" k="60" />
    <hkern u1="a" u2="&#x3f;" k="30" />
    <hkern u1="a" u2="v" k="25" />
    <hkern u1="a" u2="q" k="1" />
    <hkern u1="a" u2="&#x153;" k="1" />
    <hkern u1="a" u2="V" k="25" />
    <hkern u1="a" u2="Q" k="1" />
    <hkern u1="a" u2="&#x152;" k="1" />
    <hkern u1="&#xe1;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe1;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe1;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe1;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe1;" u2="v" k="25" />
    <hkern u1="&#xe1;" u2="q" k="1" />
    <hkern u1="&#xe1;" u2="&#x153;" k="1" />
    <hkern u1="&#xe1;" u2="V" k="25" />
    <hkern u1="&#xe1;" u2="Q" k="1" />
    <hkern u1="&#xe1;" u2="&#x152;" k="1" />
    <hkern u1="&#x103;" u2="&#x2018;" k="60" />
    <hkern u1="&#x103;" u2="&#x201d;" k="60" />
    <hkern u1="&#x103;" u2="&#x201c;" k="60" />
    <hkern u1="&#x103;" u2="&#x3f;" k="30" />
    <hkern u1="&#x103;" u2="v" k="25" />
    <hkern u1="&#x103;" u2="q" k="1" />
    <hkern u1="&#x103;" u2="&#x153;" k="1" />
    <hkern u1="&#x103;" u2="V" k="25" />
    <hkern u1="&#x103;" u2="Q" k="1" />
    <hkern u1="&#x103;" u2="&#x152;" k="1" />
    <hkern u1="&#xe2;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe2;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe2;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe2;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe2;" u2="v" k="25" />
    <hkern u1="&#xe2;" u2="q" k="1" />
    <hkern u1="&#xe2;" u2="&#x153;" k="1" />
    <hkern u1="&#xe2;" u2="V" k="25" />
    <hkern u1="&#xe2;" u2="Q" k="1" />
    <hkern u1="&#xe2;" u2="&#x152;" k="1" />
    <hkern u1="&#xe4;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe4;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe4;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe4;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe4;" u2="v" k="25" />
    <hkern u1="&#xe4;" u2="q" k="1" />
    <hkern u1="&#xe4;" u2="&#x153;" k="1" />
    <hkern u1="&#xe4;" u2="V" k="25" />
    <hkern u1="&#xe4;" u2="Q" k="1" />
    <hkern u1="&#xe4;" u2="&#x152;" k="1" />
    <hkern u1="&#xe0;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe0;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe0;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe0;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe0;" u2="v" k="25" />
    <hkern u1="&#xe0;" u2="q" k="1" />
    <hkern u1="&#xe0;" u2="&#x153;" k="1" />
    <hkern u1="&#xe0;" u2="V" k="25" />
    <hkern u1="&#xe0;" u2="Q" k="1" />
    <hkern u1="&#xe0;" u2="&#x152;" k="1" />
    <hkern u1="&#x101;" u2="&#x2018;" k="60" />
    <hkern u1="&#x101;" u2="&#x201d;" k="60" />
    <hkern u1="&#x101;" u2="&#x201c;" k="60" />
    <hkern u1="&#x101;" u2="&#x3f;" k="30" />
    <hkern u1="&#x101;" u2="v" k="25" />
    <hkern u1="&#x101;" u2="q" k="1" />
    <hkern u1="&#x101;" u2="&#x153;" k="1" />
    <hkern u1="&#x101;" u2="V" k="25" />
    <hkern u1="&#x101;" u2="Q" k="1" />
    <hkern u1="&#x101;" u2="&#x152;" k="1" />
    <hkern u1="&#x105;" u2="&#x2018;" k="60" />
    <hkern u1="&#x105;" u2="&#x201d;" k="60" />
    <hkern u1="&#x105;" u2="&#x201c;" k="60" />
    <hkern u1="&#x105;" u2="&#x3f;" k="30" />
    <hkern u1="&#x105;" u2="v" k="25" />
    <hkern u1="&#x105;" u2="q" k="1" />
    <hkern u1="&#x105;" u2="&#x153;" k="1" />
    <hkern u1="&#x105;" u2="V" k="25" />
    <hkern u1="&#x105;" u2="Q" k="1" />
    <hkern u1="&#x105;" u2="&#x152;" k="1" />
    <hkern u1="&#xe5;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe5;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe5;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe5;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe5;" u2="v" k="25" />
    <hkern u1="&#xe5;" u2="q" k="1" />
    <hkern u1="&#xe5;" u2="&#x153;" k="1" />
    <hkern u1="&#xe5;" u2="V" k="25" />
    <hkern u1="&#xe5;" u2="Q" k="1" />
    <hkern u1="&#xe5;" u2="&#x152;" k="1" />
    <hkern u1="&#xe3;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe3;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe3;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe3;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe3;" u2="v" k="25" />
    <hkern u1="&#xe3;" u2="q" k="1" />
    <hkern u1="&#xe3;" u2="&#x153;" k="1" />
    <hkern u1="&#xe3;" u2="V" k="25" />
    <hkern u1="&#xe3;" u2="Q" k="1" />
    <hkern u1="&#xe3;" u2="&#x152;" k="1" />
    <hkern u1="b" u2="&#x201a;" k="5" />
    <hkern u1="b" u2="&#x2018;" k="10" />
    <hkern u1="b" u2="&#x201d;" k="10" />
    <hkern u1="b" u2="&#x201c;" k="10" />
    <hkern u1="b" u2="&#x201e;" k="5" />
    <hkern u1="b" u2="&#x2e;" k="5" />
    <hkern u1="b" u2="&#x2026;" k="5" />
    <hkern u1="b" u2="&#x2c;" k="5" />
    <hkern u1="b" u2="x" k="3" />
    <hkern u1="b" u2="v" k="4" />
    <hkern u1="b" u2="X" k="3" />
    <hkern u1="b" u2="V" k="4" />
    <hkern u1="c" u2="&#x201a;" k="6" />
    <hkern u1="c" u2="&#x2018;" k="6" />
    <hkern u1="c" u2="&#x201d;" k="6" />
    <hkern u1="c" u2="&#x201c;" k="6" />
    <hkern u1="c" u2="&#x201e;" k="6" />
    <hkern u1="c" u2="&#x2e;" k="6" />
    <hkern u1="c" u2="&#x2026;" k="6" />
    <hkern u1="c" u2="&#x2c;" k="6" />
    <hkern u1="c" u2="x" k="11" />
    <hkern u1="c" u2="X" k="11" />
    <hkern u1="&#x107;" u2="&#x201a;" k="6" />
    <hkern u1="&#x107;" u2="&#x2018;" k="6" />
    <hkern u1="&#x107;" u2="&#x201d;" k="6" />
    <hkern u1="&#x107;" u2="&#x201c;" k="6" />
    <hkern u1="&#x107;" u2="&#x201e;" k="6" />
    <hkern u1="&#x107;" u2="&#x2e;" k="6" />
    <hkern u1="&#x107;" u2="&#x2026;" k="6" />
    <hkern u1="&#x107;" u2="&#x2c;" k="6" />
    <hkern u1="&#x107;" u2="x" k="11" />
    <hkern u1="&#x107;" u2="X" k="11" />
    <hkern u1="&#x10d;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10d;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10d;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10d;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10d;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10d;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10d;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10d;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10d;" u2="x" k="11" />
    <hkern u1="&#x10d;" u2="X" k="11" />
    <hkern u1="&#xe7;" u2="&#x201a;" k="6" />
    <hkern u1="&#xe7;" u2="&#x2018;" k="6" />
    <hkern u1="&#xe7;" u2="&#x201d;" k="6" />
    <hkern u1="&#xe7;" u2="&#x201c;" k="6" />
    <hkern u1="&#xe7;" u2="&#x201e;" k="6" />
    <hkern u1="&#xe7;" u2="&#x2e;" k="6" />
    <hkern u1="&#xe7;" u2="&#x2026;" k="6" />
    <hkern u1="&#xe7;" u2="&#x2c;" k="6" />
    <hkern u1="&#xe7;" u2="x" k="11" />
    <hkern u1="&#xe7;" u2="X" k="11" />
    <hkern u1="&#x109;" u2="&#x201a;" k="6" />
    <hkern u1="&#x109;" u2="&#x2018;" k="6" />
    <hkern u1="&#x109;" u2="&#x201d;" k="6" />
    <hkern u1="&#x109;" u2="&#x201c;" k="6" />
    <hkern u1="&#x109;" u2="&#x201e;" k="6" />
    <hkern u1="&#x109;" u2="&#x2e;" k="6" />
    <hkern u1="&#x109;" u2="&#x2026;" k="6" />
    <hkern u1="&#x109;" u2="&#x2c;" k="6" />
    <hkern u1="&#x109;" u2="x" k="11" />
    <hkern u1="&#x109;" u2="X" k="11" />
    <hkern u1="&#x10b;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10b;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10b;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10b;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10b;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10b;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10b;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10b;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10b;" u2="x" k="11" />
    <hkern u1="&#x10b;" u2="X" k="11" />
    <hkern u1="d" u2="&#x201a;" k="10" />
    <hkern u1="d" u2="&#x2018;" k="10" />
    <hkern u1="d" u2="&#x201d;" k="10" />
    <hkern u1="d" u2="&#x201c;" k="10" />
    <hkern u1="d" u2="&#x201e;" k="10" />
    <hkern u1="d" u2="&#x2e;" k="10" />
    <hkern u1="d" u2="&#x2026;" k="10" />
    <hkern u1="d" u2="&#x2c;" k="10" />
    <hkern u1="d" u2="x" k="15" />
    <hkern u1="d" u2="v" k="1" />
    <hkern u1="d" u2="X" k="15" />
    <hkern u1="d" u2="V" k="1" />
    <hkern u1="&#xf0;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf0;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf0;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf0;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf0;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf0;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf0;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf0;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf0;" u2="x" k="15" />
    <hkern u1="&#xf0;" u2="v" k="1" />
    <hkern u1="&#xf0;" u2="X" k="15" />
    <hkern u1="&#xf0;" u2="V" k="1" />
    <hkern u1="&#x10f;" u2="&#x201a;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2018;" k="10" />
    <hkern u1="&#x10f;" u2="&#x201d;" k="10" />
    <hkern u1="&#x10f;" u2="&#x201c;" k="10" />
    <hkern u1="&#x10f;" u2="&#x201e;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2e;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2026;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2c;" k="10" />
    <hkern u1="&#x10f;" u2="x" k="15" />
    <hkern u1="&#x10f;" u2="v" k="1" />
    <hkern u1="&#x10f;" u2="X" k="15" />
    <hkern u1="&#x10f;" u2="V" k="1" />
    <hkern u1="&#x111;" u2="&#x201a;" k="10" />
    <hkern u1="&#x111;" u2="&#x2018;" k="10" />
    <hkern u1="&#x111;" u2="&#x201d;" k="10" />
    <hkern u1="&#x111;" u2="&#x201c;" k="10" />
    <hkern u1="&#x111;" u2="&#x201e;" k="10" />
    <hkern u1="&#x111;" u2="&#x2e;" k="10" />
    <hkern u1="&#x111;" u2="&#x2026;" k="10" />
    <hkern u1="&#x111;" u2="&#x2c;" k="10" />
    <hkern u1="&#x111;" u2="x" k="15" />
    <hkern u1="&#x111;" u2="v" k="1" />
    <hkern u1="&#x111;" u2="X" k="15" />
    <hkern u1="&#x111;" u2="V" k="1" />
    <hkern u1="f" u2="&#x201a;" k="70" />
    <hkern u1="f" u2="&#x201e;" k="70" />
    <hkern u1="f" u2="&#x29;" k="-5" />
    <hkern u1="f" u2="]" k="-5" />
    <hkern u1="f" u2="&#x7d;" k="-5" />
    <hkern u1="f" u2="&#x2e;" k="70" />
    <hkern u1="f" u2="&#x2026;" k="70" />
    <hkern u1="f" u2="&#x2c;" k="70" />
    <hkern u1="g" u2="&#x201a;" k="10" />
    <hkern u1="g" u2="&#x2018;" k="10" />
    <hkern u1="g" u2="&#x201d;" k="10" />
    <hkern u1="g" u2="&#x201c;" k="10" />
    <hkern u1="g" u2="&#x201e;" k="10" />
    <hkern u1="g" u2="&#x2e;" k="10" />
    <hkern u1="g" u2="&#x2026;" k="10" />
    <hkern u1="g" u2="&#x2c;" k="10" />
    <hkern u1="g" u2="x" k="15" />
    <hkern u1="g" u2="v" k="1" />
    <hkern u1="g" u2="X" k="15" />
    <hkern u1="g" u2="V" k="1" />
    <hkern u1="&#x11f;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11f;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11f;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11f;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11f;" u2="x" k="15" />
    <hkern u1="&#x11f;" u2="v" k="1" />
    <hkern u1="&#x11f;" u2="X" k="15" />
    <hkern u1="&#x11f;" u2="V" k="1" />
    <hkern u1="&#x11d;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11d;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11d;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11d;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11d;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11d;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11d;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11d;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11d;" u2="x" k="15" />
    <hkern u1="&#x11d;" u2="v" k="1" />
    <hkern u1="&#x11d;" u2="X" k="15" />
    <hkern u1="&#x11d;" u2="V" k="1" />
    <hkern u1="&#x121;" u2="&#x201a;" k="10" />
    <hkern u1="&#x121;" u2="&#x2018;" k="10" />
    <hkern u1="&#x121;" u2="&#x201d;" k="10" />
    <hkern u1="&#x121;" u2="&#x201c;" k="10" />
    <hkern u1="&#x121;" u2="&#x201e;" k="10" />
    <hkern u1="&#x121;" u2="&#x2e;" k="10" />
    <hkern u1="&#x121;" u2="&#x2026;" k="10" />
    <hkern u1="&#x121;" u2="&#x2c;" k="10" />
    <hkern u1="&#x121;" u2="x" k="15" />
    <hkern u1="&#x121;" u2="v" k="1" />
    <hkern u1="&#x121;" u2="X" k="15" />
    <hkern u1="&#x121;" u2="V" k="1" />
    <hkern u1="j" u2="&#x201a;" k="6" />
    <hkern u1="j" u2="&#x201e;" k="6" />
    <hkern u1="j" u2="&#x2e;" k="6" />
    <hkern u1="j" u2="&#x2026;" k="6" />
    <hkern u1="j" u2="&#x2c;" k="6" />
    <hkern u1="&#x237;" u2="&#x201a;" k="6" />
    <hkern u1="&#x237;" u2="&#x201e;" k="6" />
    <hkern u1="&#x237;" u2="&#x2e;" k="6" />
    <hkern u1="&#x237;" u2="&#x2026;" k="6" />
    <hkern u1="&#x237;" u2="&#x2c;" k="6" />
    <hkern u1="&#x135;" u2="&#x201a;" k="6" />
    <hkern u1="&#x135;" u2="&#x201e;" k="6" />
    <hkern u1="&#x135;" u2="&#x2e;" k="6" />
    <hkern u1="&#x135;" u2="&#x2026;" k="6" />
    <hkern u1="&#x135;" u2="&#x2c;" k="6" />
    <hkern u1="k" u2="q" k="7" />
    <hkern u1="k" u2="&#x153;" k="7" />
    <hkern u1="k" u2="Q" k="7" />
    <hkern u1="k" u2="&#x152;" k="7" />
    <hkern u1="&#x138;" u2="q" k="7" />
    <hkern u1="&#x138;" u2="&#x153;" k="7" />
    <hkern u1="&#x138;" u2="Q" k="7" />
    <hkern u1="&#x138;" u2="&#x152;" k="7" />
    <hkern u1="l" u2="&#x2018;" k="75" />
    <hkern u1="l" u2="&#x201d;" k="75" />
    <hkern u1="l" u2="&#x201c;" k="75" />
    <hkern u1="l" u2="&#x29;" k="-8" />
    <hkern u1="l" u2="]" k="-8" />
    <hkern u1="l" u2="&#x7d;" k="-8" />
    <hkern u1="l" u2="&#x3f;" k="30" />
    <hkern u1="l" u2="v" k="33" />
    <hkern u1="l" u2="V" k="33" />
    <hkern u1="&#x13a;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13a;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13a;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13a;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13a;" u2="]" k="-8" />
    <hkern u1="&#x13a;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13a;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13a;" u2="v" k="33" />
    <hkern u1="&#x13a;" u2="V" k="33" />
    <hkern u1="&#x140;" u2="&#x2018;" k="75" />
    <hkern u1="&#x140;" u2="&#x201d;" k="75" />
    <hkern u1="&#x140;" u2="&#x201c;" k="75" />
    <hkern u1="&#x140;" u2="&#x29;" k="-8" />
    <hkern u1="&#x140;" u2="]" k="-8" />
    <hkern u1="&#x140;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x140;" u2="&#x3f;" k="30" />
    <hkern u1="&#x140;" u2="v" k="33" />
    <hkern u1="&#x140;" u2="V" k="33" />
    <hkern u1="&#x142;" u2="&#x2018;" k="75" />
    <hkern u1="&#x142;" u2="&#x201d;" k="75" />
    <hkern u1="&#x142;" u2="&#x201c;" k="75" />
    <hkern u1="&#x142;" u2="&#x29;" k="-8" />
    <hkern u1="&#x142;" u2="]" k="-8" />
    <hkern u1="&#x142;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x142;" u2="&#x3f;" k="30" />
    <hkern u1="&#x142;" u2="v" k="33" />
    <hkern u1="&#x142;" u2="V" k="33" />
    <hkern u1="o" u2="&#x201a;" k="10" />
    <hkern u1="o" u2="&#x2018;" k="10" />
    <hkern u1="o" u2="&#x201d;" k="10" />
    <hkern u1="o" u2="&#x201c;" k="10" />
    <hkern u1="o" u2="&#x201e;" k="10" />
    <hkern u1="o" u2="&#x2e;" k="10" />
    <hkern u1="o" u2="&#x2026;" k="10" />
    <hkern u1="o" u2="&#x2c;" k="10" />
    <hkern u1="o" u2="x" k="15" />
    <hkern u1="o" u2="v" k="1" />
    <hkern u1="o" u2="X" k="15" />
    <hkern u1="o" u2="V" k="1" />
    <hkern u1="&#xf3;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf3;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf3;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf3;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf3;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf3;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf3;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf3;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf3;" u2="x" k="15" />
    <hkern u1="&#xf3;" u2="v" k="1" />
    <hkern u1="&#xf3;" u2="X" k="15" />
    <hkern u1="&#xf3;" u2="V" k="1" />
    <hkern u1="&#x14f;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14f;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14f;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14f;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2c;" k="10" />
    <hkern u1="&#x14f;" u2="x" k="15" />
    <hkern u1="&#x14f;" u2="v" k="1" />
    <hkern u1="&#x14f;" u2="X" k="15" />
    <hkern u1="&#x14f;" u2="V" k="1" />
    <hkern u1="&#xf4;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf4;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf4;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf4;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf4;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf4;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf4;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf4;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf4;" u2="x" k="15" />
    <hkern u1="&#xf4;" u2="v" k="1" />
    <hkern u1="&#xf4;" u2="X" k="15" />
    <hkern u1="&#xf4;" u2="V" k="1" />
    <hkern u1="&#xf6;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf6;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf6;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf6;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf6;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf6;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf6;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf6;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf6;" u2="x" k="15" />
    <hkern u1="&#xf6;" u2="v" k="1" />
    <hkern u1="&#xf6;" u2="X" k="15" />
    <hkern u1="&#xf6;" u2="V" k="1" />
    <hkern u1="&#xf2;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf2;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf2;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf2;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf2;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf2;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf2;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf2;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf2;" u2="x" k="15" />
    <hkern u1="&#xf2;" u2="v" k="1" />
    <hkern u1="&#xf2;" u2="X" k="15" />
    <hkern u1="&#xf2;" u2="V" k="1" />
    <hkern u1="&#x151;" u2="&#x201a;" k="10" />
    <hkern u1="&#x151;" u2="&#x2018;" k="10" />
    <hkern u1="&#x151;" u2="&#x201d;" k="10" />
    <hkern u1="&#x151;" u2="&#x201c;" k="10" />
    <hkern u1="&#x151;" u2="&#x201e;" k="10" />
    <hkern u1="&#x151;" u2="&#x2e;" k="10" />
    <hkern u1="&#x151;" u2="&#x2026;" k="10" />
    <hkern u1="&#x151;" u2="&#x2c;" k="10" />
    <hkern u1="&#x151;" u2="x" k="15" />
    <hkern u1="&#x151;" u2="v" k="1" />
    <hkern u1="&#x151;" u2="X" k="15" />
    <hkern u1="&#x151;" u2="V" k="1" />
    <hkern u1="&#x14d;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14d;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14d;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14d;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2c;" k="10" />
    <hkern u1="&#x14d;" u2="x" k="15" />
    <hkern u1="&#x14d;" u2="v" k="1" />
    <hkern u1="&#x14d;" u2="X" k="15" />
    <hkern u1="&#x14d;" u2="V" k="1" />
    <hkern u1="&#xf8;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf8;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf8;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf8;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf8;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf8;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf8;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf8;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf8;" u2="x" k="15" />
    <hkern u1="&#xf8;" u2="v" k="1" />
    <hkern u1="&#xf8;" u2="X" k="15" />
    <hkern u1="&#xf8;" u2="V" k="1" />
    <hkern u1="&#x1ff;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2c;" k="10" />
    <hkern u1="&#x1ff;" u2="x" k="15" />
    <hkern u1="&#x1ff;" u2="v" k="1" />
    <hkern u1="&#x1ff;" u2="X" k="15" />
    <hkern u1="&#x1ff;" u2="V" k="1" />
    <hkern u1="&#xf5;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf5;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf5;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf5;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf5;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf5;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf5;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf5;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf5;" u2="x" k="15" />
    <hkern u1="&#xf5;" u2="v" k="1" />
    <hkern u1="&#xf5;" u2="X" k="15" />
    <hkern u1="&#xf5;" u2="V" k="1" />
    <hkern u1="p" u2="&#x201a;" k="80" />
    <hkern u1="p" u2="&#x201e;" k="80" />
    <hkern u1="p" u2="&#x2e;" k="80" />
    <hkern u1="p" u2="&#x2026;" k="80" />
    <hkern u1="p" u2="&#x2c;" k="80" />
    <hkern u1="p" u2="x" k="11" />
    <hkern u1="p" u2="X" k="11" />
    <hkern u1="&#xfe;" u2="&#xc1;" k="2" />
    <hkern u1="&#xfe;" u2="A" k="2" />
    <hkern u1="&#xfe;" u2="&#x102;" k="2" />
    <hkern u1="&#xfe;" u2="&#x201a;" k="80" />
    <hkern u1="&#xfe;" u2="&#x201e;" k="80" />
    <hkern u1="&#xfe;" u2="&#x2e;" k="80" />
    <hkern u1="&#xfe;" u2="&#x2026;" k="80" />
    <hkern u1="&#xfe;" u2="&#x2c;" k="80" />
    <hkern u1="&#xfe;" u2="&#x17c;" k="10" />
    <hkern u1="&#xfe;" u2="&#x17e;" k="10" />
    <hkern u1="&#xfe;" u2="&#x17a;" k="10" />
    <hkern u1="&#xfe;" u2="z" k="10" />
    <hkern u1="&#xfe;" u2="&#x1ef3;" k="9" />
    <hkern u1="&#xfe;" u2="&#xff;" k="9" />
    <hkern u1="&#xfe;" u2="&#x177;" k="9" />
    <hkern u1="&#xfe;" u2="&#xfd;" k="9" />
    <hkern u1="&#xfe;" u2="y" k="9" />
    <hkern u1="&#xfe;" u2="x" k="20" />
    <hkern u1="&#xfe;" u2="&#x135;" k="8" />
    <hkern u1="&#xfe;" u2="&#x237;" k="8" />
    <hkern u1="&#xfe;" u2="j" k="8" />
    <hkern u1="&#xfe;" u2="&#x133;" k="8" />
    <hkern u1="&#xfe;" u2="&#x1fd;" k="20" />
    <hkern u1="&#xfe;" u2="&#xe6;" k="20" />
    <hkern u1="&#xfe;" u2="&#xe3;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe5;" k="2" />
    <hkern u1="&#xfe;" u2="&#x105;" k="2" />
    <hkern u1="&#xfe;" u2="&#x101;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe0;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe4;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe2;" k="2" />
    <hkern u1="&#xfe;" u2="&#x103;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe1;" k="2" />
    <hkern u1="&#xfe;" u2="a" k="2" />
    <hkern u1="&#xfe;" u2="&#x17b;" k="10" />
    <hkern u1="&#xfe;" u2="&#x17d;" k="10" />
    <hkern u1="&#xfe;" u2="&#x179;" k="10" />
    <hkern u1="&#xfe;" u2="Z" k="10" />
    <hkern u1="&#xfe;" u2="&#x1ef2;" k="9" />
    <hkern u1="&#xfe;" u2="&#x178;" k="9" />
    <hkern u1="&#xfe;" u2="&#x176;" k="9" />
    <hkern u1="&#xfe;" u2="&#xdd;" k="9" />
    <hkern u1="&#xfe;" u2="Y" k="9" />
    <hkern u1="&#xfe;" u2="X" k="20" />
    <hkern u1="&#xfe;" u2="&#x134;" k="8" />
    <hkern u1="&#xfe;" u2="J" k="8" />
    <hkern u1="&#xfe;" u2="&#x132;" k="8" />
    <hkern u1="&#xfe;" u2="&#x1fc;" k="20" />
    <hkern u1="&#xfe;" u2="&#xc6;" k="20" />
    <hkern u1="&#xfe;" u2="&#xc3;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc5;" k="2" />
    <hkern u1="&#xfe;" u2="&#x104;" k="2" />
    <hkern u1="&#xfe;" u2="&#x100;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc0;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc4;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc2;" k="2" />
    <hkern u1="q" u2="&#x2bc;" k="10" />
    <hkern u1="q" u2="&#x2019;" k="10" />
    <hkern u1="q" u2="&#x2018;" k="10" />
    <hkern u1="q" u2="&#x201d;" k="10" />
    <hkern u1="q" u2="&#x201c;" k="10" />
    <hkern u1="q" u2="&#x1ef3;" k="13" />
    <hkern u1="q" u2="&#xff;" k="13" />
    <hkern u1="q" u2="&#x177;" k="13" />
    <hkern u1="q" u2="&#xfd;" k="13" />
    <hkern u1="q" u2="y" k="13" />
    <hkern u1="q" u2="v" k="1" />
    <hkern u1="q" u2="&#x1ef2;" k="13" />
    <hkern u1="q" u2="&#x178;" k="13" />
    <hkern u1="q" u2="&#x176;" k="13" />
    <hkern u1="q" u2="&#xdd;" k="13" />
    <hkern u1="q" u2="Y" k="13" />
    <hkern u1="q" u2="V" k="1" />
    <hkern u1="r" u2="v" k="2" />
    <hkern u1="r" u2="V" k="2" />
    <hkern u1="&#x155;" u2="v" k="2" />
    <hkern u1="&#x155;" u2="V" k="2" />
    <hkern u1="&#x159;" u2="v" k="2" />
    <hkern u1="&#x159;" u2="V" k="2" />
    <hkern u1="s" u2="&#x201a;" k="5" />
    <hkern u1="s" u2="&#x2018;" k="5" />
    <hkern u1="s" u2="&#x201d;" k="5" />
    <hkern u1="s" u2="&#x201c;" k="5" />
    <hkern u1="s" u2="&#x201e;" k="5" />
    <hkern u1="s" u2="&#x2e;" k="5" />
    <hkern u1="s" u2="&#x2026;" k="5" />
    <hkern u1="s" u2="&#x2c;" k="5" />
    <hkern u1="s" u2="x" k="8" />
    <hkern u1="s" u2="v" k="1" />
    <hkern u1="s" u2="X" k="8" />
    <hkern u1="s" u2="V" k="1" />
    <hkern u1="&#x15b;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15b;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15b;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15b;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15b;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15b;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15b;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15b;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15b;" u2="x" k="8" />
    <hkern u1="&#x15b;" u2="v" k="1" />
    <hkern u1="&#x15b;" u2="X" k="8" />
    <hkern u1="&#x15b;" u2="V" k="1" />
    <hkern u1="&#x161;" u2="&#x201a;" k="5" />
    <hkern u1="&#x161;" u2="&#x2018;" k="5" />
    <hkern u1="&#x161;" u2="&#x201d;" k="5" />
    <hkern u1="&#x161;" u2="&#x201c;" k="5" />
    <hkern u1="&#x161;" u2="&#x201e;" k="5" />
    <hkern u1="&#x161;" u2="&#x2e;" k="5" />
    <hkern u1="&#x161;" u2="&#x2026;" k="5" />
    <hkern u1="&#x161;" u2="&#x2c;" k="5" />
    <hkern u1="&#x161;" u2="x" k="8" />
    <hkern u1="&#x161;" u2="v" k="1" />
    <hkern u1="&#x161;" u2="X" k="8" />
    <hkern u1="&#x161;" u2="V" k="1" />
    <hkern u1="&#x15f;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15f;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15f;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15f;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15f;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15f;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15f;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15f;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15f;" u2="x" k="8" />
    <hkern u1="&#x15f;" u2="v" k="1" />
    <hkern u1="&#x15f;" u2="X" k="8" />
    <hkern u1="&#x15f;" u2="V" k="1" />
    <hkern u1="&#x15d;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15d;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15d;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15d;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15d;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15d;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15d;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15d;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15d;" u2="x" k="8" />
    <hkern u1="&#x15d;" u2="v" k="1" />
    <hkern u1="&#x15d;" u2="X" k="8" />
    <hkern u1="&#x15d;" u2="V" k="1" />
    <hkern u1="&#xdf;" u2="&#x201a;" k="5" />
    <hkern u1="&#xdf;" u2="&#x2018;" k="5" />
    <hkern u1="&#xdf;" u2="&#x201d;" k="5" />
    <hkern u1="&#xdf;" u2="&#x201c;" k="5" />
    <hkern u1="&#xdf;" u2="&#x201e;" k="5" />
    <hkern u1="&#xdf;" u2="&#x2e;" k="5" />
    <hkern u1="&#xdf;" u2="&#x2026;" k="5" />
    <hkern u1="&#xdf;" u2="&#x2c;" k="5" />
    <hkern u1="&#xdf;" u2="x" k="8" />
    <hkern u1="&#xdf;" u2="v" k="1" />
    <hkern u1="&#xdf;" u2="X" k="8" />
    <hkern u1="&#xdf;" u2="V" k="1" />
    <hkern u1="t" u2="&#x201a;" k="60" />
    <hkern u1="t" u2="&#x201e;" k="60" />
    <hkern u1="t" u2="&#x29;" k="-8" />
    <hkern u1="t" u2="]" k="-8" />
    <hkern u1="t" u2="&#x7d;" k="-8" />
    <hkern u1="t" u2="&#x3b;" k="35" />
    <hkern u1="t" u2="&#x2e;" k="60" />
    <hkern u1="t" u2="&#x2026;" k="60" />
    <hkern u1="t" u2="&#x2c;" k="60" />
    <hkern u1="t" u2="&#x3a;" k="35" />
    <hkern u1="&#x167;" u2="&#x201a;" k="60" />
    <hkern u1="&#x167;" u2="&#x201e;" k="60" />
    <hkern u1="&#x167;" u2="&#x29;" k="-8" />
    <hkern u1="&#x167;" u2="]" k="-8" />
    <hkern u1="&#x167;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x167;" u2="&#x3b;" k="35" />
    <hkern u1="&#x167;" u2="&#x2e;" k="60" />
    <hkern u1="&#x167;" u2="&#x2026;" k="60" />
    <hkern u1="&#x167;" u2="&#x2c;" k="60" />
    <hkern u1="&#x167;" u2="&#x3a;" k="35" />
    <hkern u1="&#x165;" u2="&#x201a;" k="60" />
    <hkern u1="&#x165;" u2="&#x201e;" k="60" />
    <hkern u1="&#x165;" u2="&#x29;" k="-8" />
    <hkern u1="&#x165;" u2="]" k="-8" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x165;" u2="&#x3b;" k="35" />
    <hkern u1="&#x165;" u2="&#x2e;" k="60" />
    <hkern u1="&#x165;" u2="&#x2026;" k="60" />
    <hkern u1="&#x165;" u2="&#x2c;" k="60" />
    <hkern u1="&#x165;" u2="&#x3a;" k="35" />
    <hkern u1="u" u2="&#x201a;" k="6" />
    <hkern u1="u" u2="&#x201e;" k="6" />
    <hkern u1="u" u2="&#x2e;" k="6" />
    <hkern u1="u" u2="&#x2026;" k="6" />
    <hkern u1="u" u2="&#x2c;" k="6" />
    <hkern u1="&#xfa;" u2="&#x201a;" k="6" />
    <hkern u1="&#xfa;" u2="&#x201e;" k="6" />
    <hkern u1="&#xfa;" u2="&#x2e;" k="6" />
    <hkern u1="&#xfa;" u2="&#x2026;" k="6" />
    <hkern u1="&#xfa;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16d;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16d;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16d;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16d;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16d;" u2="&#x2c;" k="6" />
    <hkern u1="&#xfb;" u2="&#x201a;" k="6" />
    <hkern u1="&#xfb;" u2="&#x201e;" k="6" />
    <hkern u1="&#xfb;" u2="&#x2e;" k="6" />
    <hkern u1="&#xfb;" u2="&#x2026;" k="6" />
    <hkern u1="&#xfb;" u2="&#x2c;" k="6" />
    <hkern u1="&#xfc;" u2="&#x201a;" k="6" />
    <hkern u1="&#xfc;" u2="&#x201e;" k="6" />
    <hkern u1="&#xfc;" u2="&#x2e;" k="6" />
    <hkern u1="&#xfc;" u2="&#x2026;" k="6" />
    <hkern u1="&#xfc;" u2="&#x2c;" k="6" />
    <hkern u1="&#xf9;" u2="&#x201a;" k="6" />
    <hkern u1="&#xf9;" u2="&#x201e;" k="6" />
    <hkern u1="&#xf9;" u2="&#x2e;" k="6" />
    <hkern u1="&#xf9;" u2="&#x2026;" k="6" />
    <hkern u1="&#xf9;" u2="&#x2c;" k="6" />
    <hkern u1="&#x171;" u2="&#x201a;" k="6" />
    <hkern u1="&#x171;" u2="&#x201e;" k="6" />
    <hkern u1="&#x171;" u2="&#x2e;" k="6" />
    <hkern u1="&#x171;" u2="&#x2026;" k="6" />
    <hkern u1="&#x171;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16b;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16b;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16b;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16b;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16b;" u2="&#x2c;" k="6" />
    <hkern u1="&#x123;" u2="&#x201a;" k="10" />
    <hkern u1="&#x123;" u2="&#x2018;" k="10" />
    <hkern u1="&#x123;" u2="&#x201d;" k="10" />
    <hkern u1="&#x123;" u2="&#x201c;" k="10" />
    <hkern u1="&#x123;" u2="&#x201e;" k="10" />
    <hkern u1="&#x123;" u2="&#x2e;" k="10" />
    <hkern u1="&#x123;" u2="&#x2026;" k="10" />
    <hkern u1="&#x123;" u2="&#x2c;" k="10" />
    <hkern u1="&#x123;" u2="x" k="15" />
    <hkern u1="&#x123;" u2="v" k="1" />
    <hkern u1="&#x123;" u2="X" k="15" />
    <hkern u1="&#x123;" u2="V" k="1" />
    <hkern u1="&#x137;" u2="q" k="7" />
    <hkern u1="&#x137;" u2="&#x153;" k="7" />
    <hkern u1="&#x137;" u2="Q" k="7" />
    <hkern u1="&#x137;" u2="&#x152;" k="7" />
    <hkern u1="&#x13c;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13c;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13c;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13c;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13c;" u2="]" k="-8" />
    <hkern u1="&#x13c;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13c;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13c;" u2="v" k="33" />
    <hkern u1="&#x13c;" u2="V" k="33" />
    <hkern u1="&#x157;" u2="v" k="2" />
    <hkern u1="&#x157;" u2="V" k="2" />
    <hkern u1="&#x163;" u2="&#x201a;" k="60" />
    <hkern u1="&#x163;" u2="&#x201e;" k="60" />
    <hkern u1="&#x163;" u2="&#x29;" k="-8" />
    <hkern u1="&#x163;" u2="]" k="-8" />
    <hkern u1="&#x163;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x163;" u2="&#x3b;" k="35" />
    <hkern u1="&#x163;" u2="&#x2e;" k="60" />
    <hkern u1="&#x163;" u2="&#x2026;" k="60" />
    <hkern u1="&#x163;" u2="&#x2c;" k="60" />
    <hkern u1="&#x163;" u2="&#x3a;" k="35" />
    <hkern u1="&#x219;" u2="&#x201a;" k="5" />
    <hkern u1="&#x219;" u2="&#x2018;" k="5" />
    <hkern u1="&#x219;" u2="&#x201d;" k="5" />
    <hkern u1="&#x219;" u2="&#x201c;" k="5" />
    <hkern u1="&#x219;" u2="&#x201e;" k="5" />
    <hkern u1="&#x219;" u2="&#x2e;" k="5" />
    <hkern u1="&#x219;" u2="&#x2026;" k="5" />
    <hkern u1="&#x219;" u2="&#x2c;" k="5" />
    <hkern u1="&#x219;" u2="x" k="8" />
    <hkern u1="&#x219;" u2="v" k="1" />
    <hkern u1="&#x219;" u2="X" k="8" />
    <hkern u1="&#x219;" u2="V" k="1" />
    <hkern u1="&#x21b;" u2="&#x201a;" k="60" />
    <hkern u1="&#x21b;" u2="&#x201e;" k="60" />
    <hkern u1="&#x21b;" u2="&#x29;" k="-8" />
    <hkern u1="&#x21b;" u2="]" k="-8" />
    <hkern u1="&#x21b;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x21b;" u2="&#x3b;" k="35" />
    <hkern u1="&#x21b;" u2="&#x2e;" k="60" />
    <hkern u1="&#x21b;" u2="&#x2026;" k="60" />
    <hkern u1="&#x21b;" u2="&#x2c;" k="60" />
    <hkern u1="&#x21b;" u2="&#x3a;" k="35" />
    <hkern u1="&#x1e03;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e03;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e03;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e03;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e03;" u2="x" k="3" />
    <hkern u1="&#x1e03;" u2="v" k="4" />
    <hkern u1="&#x1e03;" u2="X" k="3" />
    <hkern u1="&#x1e03;" u2="V" k="4" />
    <hkern u1="&#x1e0b;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x2c;" k="10" />
    <hkern u1="&#x1e0b;" u2="x" k="15" />
    <hkern u1="&#x1e0b;" u2="v" k="1" />
    <hkern u1="&#x1e0b;" u2="X" k="15" />
    <hkern u1="&#x1e0b;" u2="V" k="1" />
    <hkern u1="&#x1e1f;" u2="&#x201a;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x201e;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1e1f;" u2="]" k="-5" />
    <hkern u1="&#x1e1f;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1e1f;" u2="&#x2e;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x2026;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x2c;" k="70" />
    <hkern u1="&#x1e57;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1e57;" u2="x" k="11" />
    <hkern u1="&#x1e57;" u2="X" k="11" />
    <hkern u1="&#x1e61;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x2018;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x201d;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x201c;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e61;" u2="x" k="8" />
    <hkern u1="&#x1e61;" u2="v" k="1" />
    <hkern u1="&#x1e61;" u2="X" k="8" />
    <hkern u1="&#x1e61;" u2="V" k="1" />
    <hkern u1="&#x1e6b;" u2="&#x201a;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x201e;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x29;" k="-8" />
    <hkern u1="&#x1e6b;" u2="]" k="-8" />
    <hkern u1="&#x1e6b;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x1e6b;" u2="&#x3b;" k="35" />
    <hkern u1="&#x1e6b;" u2="&#x2e;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x2026;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x2c;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x3a;" k="35" />
    <hkern u1="&#x173;" u2="&#x201a;" k="6" />
    <hkern u1="&#x173;" u2="&#x201e;" k="6" />
    <hkern u1="&#x173;" u2="&#x2e;" k="6" />
    <hkern u1="&#x173;" u2="&#x2026;" k="6" />
    <hkern u1="&#x173;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16f;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16f;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16f;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16f;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16f;" u2="&#x2c;" k="6" />
    <hkern u1="&#x169;" u2="&#x201a;" k="6" />
    <hkern u1="&#x169;" u2="&#x201e;" k="6" />
    <hkern u1="&#x169;" u2="&#x2e;" k="6" />
    <hkern u1="&#x169;" u2="&#x2026;" k="6" />
    <hkern u1="&#x169;" u2="&#x2c;" k="6" />
    <hkern u1="v" u2="&#xc1;" k="25" />
    <hkern u1="v" u2="A" k="25" />
    <hkern u1="v" u2="&#x102;" k="25" />
    <hkern u1="v" u2="&#x201a;" k="60" />
    <hkern u1="v" u2="&#x201e;" k="60" />
    <hkern u1="v" u2="&#xad;" k="10" />
    <hkern u1="v" u2="&#x2d;" k="10" />
    <hkern u1="v" u2="&#x29;" k="-3" />
    <hkern u1="v" u2="]" k="-3" />
    <hkern u1="v" u2="&#x7d;" k="-3" />
    <hkern u1="v" u2="&#x2e;" k="60" />
    <hkern u1="v" u2="&#x2026;" k="60" />
    <hkern u1="v" u2="&#x2c;" k="60" />
    <hkern u1="v" u2="&#x123;" k="1" />
    <hkern u1="v" u2="q" k="1" />
    <hkern u1="v" u2="&#x153;" k="1" />
    <hkern u1="v" u2="&#xf5;" k="1" />
    <hkern u1="v" u2="&#x1ff;" k="1" />
    <hkern u1="v" u2="&#xf8;" k="1" />
    <hkern u1="v" u2="&#x14d;" k="1" />
    <hkern u1="v" u2="&#x151;" k="1" />
    <hkern u1="v" u2="&#xf2;" k="1" />
    <hkern u1="v" u2="&#xf6;" k="1" />
    <hkern u1="v" u2="&#xf4;" k="1" />
    <hkern u1="v" u2="&#x14f;" k="1" />
    <hkern u1="v" u2="&#xf3;" k="1" />
    <hkern u1="v" u2="o" k="1" />
    <hkern u1="v" u2="&#x135;" k="25" />
    <hkern u1="v" u2="&#x237;" k="25" />
    <hkern u1="v" u2="j" k="25" />
    <hkern u1="v" u2="&#x133;" k="25" />
    <hkern u1="v" u2="&#x121;" k="1" />
    <hkern u1="v" u2="&#x11d;" k="1" />
    <hkern u1="v" u2="&#x11f;" k="1" />
    <hkern u1="v" u2="g" k="1" />
    <hkern u1="v" u2="&#x10b;" k="1" />
    <hkern u1="v" u2="&#x109;" k="1" />
    <hkern u1="v" u2="&#xe7;" k="1" />
    <hkern u1="v" u2="&#x10d;" k="1" />
    <hkern u1="v" u2="&#x107;" k="1" />
    <hkern u1="v" u2="c" k="1" />
    <hkern u1="v" u2="&#x1fd;" k="46" />
    <hkern u1="v" u2="&#xe6;" k="46" />
    <hkern u1="v" u2="&#xe3;" k="25" />
    <hkern u1="v" u2="&#xe5;" k="25" />
    <hkern u1="v" u2="&#x105;" k="25" />
    <hkern u1="v" u2="&#x101;" k="25" />
    <hkern u1="v" u2="&#xe0;" k="25" />
    <hkern u1="v" u2="&#xe4;" k="25" />
    <hkern u1="v" u2="&#xe2;" k="25" />
    <hkern u1="v" u2="&#x103;" k="25" />
    <hkern u1="v" u2="&#xe1;" k="25" />
    <hkern u1="v" u2="a" k="25" />
    <hkern u1="v" u2="&#x122;" k="1" />
    <hkern u1="v" u2="Q" k="1" />
    <hkern u1="v" u2="&#x152;" k="1" />
    <hkern u1="v" u2="&#xd5;" k="1" />
    <hkern u1="v" u2="&#x1fe;" k="1" />
    <hkern u1="v" u2="&#xd8;" k="1" />
    <hkern u1="v" u2="&#x14c;" k="1" />
    <hkern u1="v" u2="&#x150;" k="1" />
    <hkern u1="v" u2="&#xd2;" k="1" />
    <hkern u1="v" u2="&#xd6;" k="1" />
    <hkern u1="v" u2="&#xd4;" k="1" />
    <hkern u1="v" u2="&#x14e;" k="1" />
    <hkern u1="v" u2="&#xd3;" k="1" />
    <hkern u1="v" u2="O" k="1" />
    <hkern u1="v" u2="&#x134;" k="25" />
    <hkern u1="v" u2="J" k="25" />
    <hkern u1="v" u2="&#x132;" k="25" />
    <hkern u1="v" u2="&#x120;" k="1" />
    <hkern u1="v" u2="&#x11c;" k="1" />
    <hkern u1="v" u2="&#x11e;" k="1" />
    <hkern u1="v" u2="G" k="1" />
    <hkern u1="v" u2="&#x10a;" k="1" />
    <hkern u1="v" u2="&#x108;" k="1" />
    <hkern u1="v" u2="&#xc7;" k="1" />
    <hkern u1="v" u2="&#x10c;" k="1" />
    <hkern u1="v" u2="&#x106;" k="1" />
    <hkern u1="v" u2="C" k="1" />
    <hkern u1="v" u2="&#x1fc;" k="46" />
    <hkern u1="v" u2="&#xc6;" k="46" />
    <hkern u1="v" u2="&#xc3;" k="25" />
    <hkern u1="v" u2="&#xc5;" k="25" />
    <hkern u1="v" u2="&#x104;" k="25" />
    <hkern u1="v" u2="&#x100;" k="25" />
    <hkern u1="v" u2="&#xc0;" k="25" />
    <hkern u1="v" u2="&#xc4;" k="25" />
    <hkern u1="v" u2="&#xc2;" k="25" />
    <hkern u1="w" u2="&#x201a;" k="40" />
    <hkern u1="w" u2="&#x201e;" k="40" />
    <hkern u1="w" u2="&#x29;" k="-3" />
    <hkern u1="w" u2="]" k="-3" />
    <hkern u1="w" u2="&#x7d;" k="-3" />
    <hkern u1="w" u2="&#x2e;" k="40" />
    <hkern u1="w" u2="&#x2026;" k="40" />
    <hkern u1="w" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e83;" u2="]" k="-3" />
    <hkern u1="&#x1e83;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e83;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x2c;" k="40" />
    <hkern u1="&#x175;" u2="&#x201a;" k="40" />
    <hkern u1="&#x175;" u2="&#x201e;" k="40" />
    <hkern u1="&#x175;" u2="&#x29;" k="-3" />
    <hkern u1="&#x175;" u2="]" k="-3" />
    <hkern u1="&#x175;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x175;" u2="&#x2e;" k="40" />
    <hkern u1="&#x175;" u2="&#x2026;" k="40" />
    <hkern u1="&#x175;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e85;" u2="]" k="-3" />
    <hkern u1="&#x1e85;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e85;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e81;" u2="]" k="-3" />
    <hkern u1="&#x1e81;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e81;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x2c;" k="40" />
    <hkern u1="x" u2="&#x2bc;" k="2" />
    <hkern u1="x" u2="&#x2019;" k="2" />
    <hkern u1="x" u2="&#x2018;" k="2" />
    <hkern u1="x" u2="&#x201d;" k="2" />
    <hkern u1="x" u2="&#x201c;" k="2" />
    <hkern u1="x" u2="&#xad;" k="15" />
    <hkern u1="x" u2="&#x2d;" k="15" />
    <hkern u1="x" u2="&#x1e61;" k="8" />
    <hkern u1="x" u2="&#x219;" k="8" />
    <hkern u1="x" u2="&#x123;" k="15" />
    <hkern u1="x" u2="&#xdf;" k="8" />
    <hkern u1="x" u2="&#x15d;" k="8" />
    <hkern u1="x" u2="&#x15f;" k="8" />
    <hkern u1="x" u2="&#x161;" k="8" />
    <hkern u1="x" u2="&#x15b;" k="8" />
    <hkern u1="x" u2="s" k="8" />
    <hkern u1="x" u2="q" k="15" />
    <hkern u1="x" u2="&#x153;" k="15" />
    <hkern u1="x" u2="&#xf5;" k="15" />
    <hkern u1="x" u2="&#x1ff;" k="15" />
    <hkern u1="x" u2="&#xf8;" k="15" />
    <hkern u1="x" u2="&#x14d;" k="15" />
    <hkern u1="x" u2="&#x151;" k="15" />
    <hkern u1="x" u2="&#xf2;" k="15" />
    <hkern u1="x" u2="&#xf6;" k="15" />
    <hkern u1="x" u2="&#xf4;" k="15" />
    <hkern u1="x" u2="&#x14f;" k="15" />
    <hkern u1="x" u2="&#xf3;" k="15" />
    <hkern u1="x" u2="o" k="15" />
    <hkern u1="x" u2="&#x121;" k="15" />
    <hkern u1="x" u2="&#x11d;" k="15" />
    <hkern u1="x" u2="&#x11f;" k="15" />
    <hkern u1="x" u2="g" k="15" />
    <hkern u1="x" u2="&#x10b;" k="15" />
    <hkern u1="x" u2="&#x109;" k="15" />
    <hkern u1="x" u2="&#xe7;" k="15" />
    <hkern u1="x" u2="&#x10d;" k="15" />
    <hkern u1="x" u2="&#x107;" k="15" />
    <hkern u1="x" u2="c" k="15" />
    <hkern u1="x" u2="&#x1e60;" k="8" />
    <hkern u1="x" u2="&#x218;" k="8" />
    <hkern u1="x" u2="&#x122;" k="15" />
    <hkern u1="x" u2="&#x15c;" k="8" />
    <hkern u1="x" u2="&#x15e;" k="8" />
    <hkern u1="x" u2="&#x160;" k="8" />
    <hkern u1="x" u2="&#x15a;" k="8" />
    <hkern u1="x" u2="S" k="8" />
    <hkern u1="x" u2="Q" k="15" />
    <hkern u1="x" u2="&#x152;" k="15" />
    <hkern u1="x" u2="&#xd5;" k="15" />
    <hkern u1="x" u2="&#x1fe;" k="15" />
    <hkern u1="x" u2="&#xd8;" k="15" />
    <hkern u1="x" u2="&#x14c;" k="15" />
    <hkern u1="x" u2="&#x150;" k="15" />
    <hkern u1="x" u2="&#xd2;" k="15" />
    <hkern u1="x" u2="&#xd6;" k="15" />
    <hkern u1="x" u2="&#xd4;" k="15" />
    <hkern u1="x" u2="&#x14e;" k="15" />
    <hkern u1="x" u2="&#xd3;" k="15" />
    <hkern u1="x" u2="O" k="15" />
    <hkern u1="x" u2="&#x120;" k="15" />
    <hkern u1="x" u2="&#x11c;" k="15" />
    <hkern u1="x" u2="&#x11e;" k="15" />
    <hkern u1="x" u2="G" k="15" />
    <hkern u1="x" u2="&#x10a;" k="15" />
    <hkern u1="x" u2="&#x108;" k="15" />
    <hkern u1="x" u2="&#xc7;" k="15" />
    <hkern u1="x" u2="&#x10c;" k="15" />
    <hkern u1="x" u2="&#x106;" k="15" />
    <hkern u1="x" u2="C" k="15" />
    <hkern u1="y" u2="&#x201a;" k="80" />
    <hkern u1="y" u2="&#x201e;" k="80" />
    <hkern u1="y" u2="&#x29;" k="-5" />
    <hkern u1="y" u2="]" k="-5" />
    <hkern u1="y" u2="&#x7d;" k="-5" />
    <hkern u1="y" u2="&#x3b;" k="20" />
    <hkern u1="y" u2="&#x2e;" k="80" />
    <hkern u1="y" u2="&#x2026;" k="80" />
    <hkern u1="y" u2="&#x2c;" k="80" />
    <hkern u1="y" u2="&#x3a;" k="20" />
    <hkern u1="y" u2="q" k="15" />
    <hkern u1="y" u2="&#x153;" k="15" />
    <hkern u1="y" u2="Q" k="15" />
    <hkern u1="y" u2="&#x152;" k="15" />
    <hkern u1="&#xfd;" u2="&#x201a;" k="80" />
    <hkern u1="&#xfd;" u2="&#x201e;" k="80" />
    <hkern u1="&#xfd;" u2="&#x29;" k="-5" />
    <hkern u1="&#xfd;" u2="]" k="-5" />
    <hkern u1="&#xfd;" u2="&#x7d;" k="-5" />
    <hkern u1="&#xfd;" u2="&#x3b;" k="20" />
    <hkern u1="&#xfd;" u2="&#x2e;" k="80" />
    <hkern u1="&#xfd;" u2="&#x2026;" k="80" />
    <hkern u1="&#xfd;" u2="&#x2c;" k="80" />
    <hkern u1="&#xfd;" u2="&#x3a;" k="20" />
    <hkern u1="&#xfd;" u2="q" k="15" />
    <hkern u1="&#xfd;" u2="&#x153;" k="15" />
    <hkern u1="&#xfd;" u2="Q" k="15" />
    <hkern u1="&#xfd;" u2="&#x152;" k="15" />
    <hkern u1="&#x177;" u2="&#x201a;" k="80" />
    <hkern u1="&#x177;" u2="&#x201e;" k="80" />
    <hkern u1="&#x177;" u2="&#x29;" k="-5" />
    <hkern u1="&#x177;" u2="]" k="-5" />
    <hkern u1="&#x177;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x177;" u2="&#x3b;" k="20" />
    <hkern u1="&#x177;" u2="&#x2e;" k="80" />
    <hkern u1="&#x177;" u2="&#x2026;" k="80" />
    <hkern u1="&#x177;" u2="&#x2c;" k="80" />
    <hkern u1="&#x177;" u2="&#x3a;" k="20" />
    <hkern u1="&#x177;" u2="q" k="15" />
    <hkern u1="&#x177;" u2="&#x153;" k="15" />
    <hkern u1="&#x177;" u2="Q" k="15" />
    <hkern u1="&#x177;" u2="&#x152;" k="15" />
    <hkern u1="&#xff;" u2="&#x201a;" k="80" />
    <hkern u1="&#xff;" u2="&#x201e;" k="80" />
    <hkern u1="&#xff;" u2="&#x29;" k="-5" />
    <hkern u1="&#xff;" u2="]" k="-5" />
    <hkern u1="&#xff;" u2="&#x7d;" k="-5" />
    <hkern u1="&#xff;" u2="&#x3b;" k="20" />
    <hkern u1="&#xff;" u2="&#x2e;" k="80" />
    <hkern u1="&#xff;" u2="&#x2026;" k="80" />
    <hkern u1="&#xff;" u2="&#x2c;" k="80" />
    <hkern u1="&#xff;" u2="&#x3a;" k="20" />
    <hkern u1="&#xff;" u2="q" k="15" />
    <hkern u1="&#xff;" u2="&#x153;" k="15" />
    <hkern u1="&#xff;" u2="Q" k="15" />
    <hkern u1="&#xff;" u2="&#x152;" k="15" />
    <hkern u1="&#x1ef3;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1ef3;" u2="]" k="-5" />
    <hkern u1="&#x1ef3;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1ef3;" u2="&#x3b;" k="20" />
    <hkern u1="&#x1ef3;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x3a;" k="20" />
    <hkern u1="&#x1ef3;" u2="q" k="15" />
    <hkern u1="&#x1ef3;" u2="&#x153;" k="15" />
    <hkern u1="&#x1ef3;" u2="Q" k="15" />
    <hkern u1="&#x1ef3;" u2="&#x152;" k="15" />
    <hkern g1="seven.alt" g2="seven.alt" k="-4" />
    <hkern g1="seven.alt" g2="four.alt" k="40" />
    <hkern u1="&#x3a;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x3a;" u2="&#xff;" k="20" />
    <hkern u1="&#x3a;" u2="&#x177;" k="20" />
    <hkern u1="&#x3a;" u2="&#xfd;" k="20" />
    <hkern u1="&#x3a;" u2="y" k="20" />
    <hkern u1="&#x3a;" u2="&#x1e6b;" k="35" />
    <hkern u1="&#x3a;" u2="&#x21b;" k="35" />
    <hkern u1="&#x3a;" u2="&#x163;" k="35" />
    <hkern u1="&#x3a;" u2="&#x165;" k="35" />
    <hkern u1="&#x3a;" u2="&#x167;" k="35" />
    <hkern u1="&#x3a;" u2="t" k="35" />
    <hkern u1="&#x3a;" u2="&#x1e6a;" k="35" />
    <hkern u1="&#x3a;" u2="&#x21a;" k="35" />
    <hkern u1="&#x3a;" u2="&#x162;" k="35" />
    <hkern u1="&#x3a;" u2="&#x1ef2;" k="20" />
    <hkern u1="&#x3a;" u2="&#x178;" k="20" />
    <hkern u1="&#x3a;" u2="&#x176;" k="20" />
    <hkern u1="&#x3a;" u2="&#xdd;" k="20" />
    <hkern u1="&#x3a;" u2="Y" k="20" />
    <hkern u1="&#x3a;" u2="&#x164;" k="35" />
    <hkern u1="&#x3a;" u2="&#x166;" k="35" />
    <hkern u1="&#x3a;" u2="T" k="35" />
    <hkern u1="&#x2c;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x2c;" u2="&#xff;" k="80" />
    <hkern u1="&#x2c;" u2="&#x177;" k="80" />
    <hkern u1="&#x2c;" u2="&#xfd;" k="80" />
    <hkern u1="&#x2c;" u2="y" k="80" />
    <hkern u1="&#x2c;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x2c;" u2="&#x175;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x2c;" u2="w" k="40" />
    <hkern u1="&#x2c;" u2="v" k="60" />
    <hkern u1="&#x2c;" u2="&#x169;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16f;" k="6" />
    <hkern u1="&#x2c;" u2="&#x173;" k="6" />
    <hkern u1="&#x2c;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x2c;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2c;" u2="&#x21b;" k="60" />
    <hkern u1="&#x2c;" u2="&#x219;" k="5" />
    <hkern u1="&#x2c;" u2="&#x163;" k="60" />
    <hkern u1="&#x2c;" u2="&#x123;" k="10" />
    <hkern u1="&#x2c;" u2="&#x16b;" k="6" />
    <hkern u1="&#x2c;" u2="&#x171;" k="6" />
    <hkern u1="&#x2c;" u2="&#xf9;" k="6" />
    <hkern u1="&#x2c;" u2="&#xfc;" k="6" />
    <hkern u1="&#x2c;" u2="&#xfb;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16d;" k="6" />
    <hkern u1="&#x2c;" u2="&#xfa;" k="6" />
    <hkern u1="&#x2c;" u2="u" k="6" />
    <hkern u1="&#x2c;" u2="&#x165;" k="60" />
    <hkern u1="&#x2c;" u2="&#x167;" k="60" />
    <hkern u1="&#x2c;" u2="t" k="60" />
    <hkern u1="&#x2c;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2c;" u2="&#x161;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2c;" u2="s" k="5" />
    <hkern u1="&#x2c;" u2="q" k="10" />
    <hkern u1="&#x2c;" u2="&#x153;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2c;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2c;" u2="&#x151;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2c;" u2="o" k="10" />
    <hkern u1="&#x2c;" u2="&#x121;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2c;" u2="g" k="10" />
    <hkern u1="&#x2c;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2c;" u2="&#x109;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2c;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2c;" u2="&#x107;" k="10" />
    <hkern u1="&#x2c;" u2="c" k="10" />
    <hkern u1="&#x2c;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x2c;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2c;" u2="&#x21a;" k="60" />
    <hkern u1="&#x2c;" u2="&#x218;" k="5" />
    <hkern u1="&#x2c;" u2="&#x162;" k="60" />
    <hkern u1="&#x2c;" u2="&#x122;" k="10" />
    <hkern u1="&#x2c;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x2c;" u2="&#x178;" k="80" />
    <hkern u1="&#x2c;" u2="&#x176;" k="80" />
    <hkern u1="&#x2c;" u2="&#xdd;" k="80" />
    <hkern u1="&#x2c;" u2="Y" k="80" />
    <hkern u1="&#x2c;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x2c;" u2="&#x174;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x2c;" u2="W" k="40" />
    <hkern u1="&#x2c;" u2="V" k="60" />
    <hkern u1="&#x2c;" u2="&#x168;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16e;" k="6" />
    <hkern u1="&#x2c;" u2="&#x172;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16a;" k="6" />
    <hkern u1="&#x2c;" u2="&#x170;" k="6" />
    <hkern u1="&#x2c;" u2="&#xd9;" k="6" />
    <hkern u1="&#x2c;" u2="&#xdc;" k="6" />
    <hkern u1="&#x2c;" u2="&#xdb;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16c;" k="6" />
    <hkern u1="&#x2c;" u2="&#xda;" k="6" />
    <hkern u1="&#x2c;" u2="U" k="6" />
    <hkern u1="&#x2c;" u2="&#x164;" k="60" />
    <hkern u1="&#x2c;" u2="&#x166;" k="60" />
    <hkern u1="&#x2c;" u2="T" k="60" />
    <hkern u1="&#x2c;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2c;" u2="&#x160;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2c;" u2="S" k="5" />
    <hkern u1="&#x2c;" u2="Q" k="10" />
    <hkern u1="&#x2c;" u2="&#x152;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2c;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2c;" u2="&#x150;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2c;" u2="O" k="10" />
    <hkern u1="&#x2c;" u2="&#x120;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2c;" u2="G" k="10" />
    <hkern u1="&#x2c;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2c;" u2="&#x108;" k="10" />
    <hkern u1="&#x2c;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2c;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2c;" u2="&#x106;" k="10" />
    <hkern u1="&#x2c;" u2="C" k="10" />
    <hkern u1="&#x2026;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x2026;" u2="&#xff;" k="80" />
    <hkern u1="&#x2026;" u2="&#x177;" k="80" />
    <hkern u1="&#x2026;" u2="&#xfd;" k="80" />
    <hkern u1="&#x2026;" u2="y" k="80" />
    <hkern u1="&#x2026;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x2026;" u2="&#x175;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x2026;" u2="w" k="40" />
    <hkern u1="&#x2026;" u2="v" k="60" />
    <hkern u1="&#x2026;" u2="&#x169;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16f;" k="6" />
    <hkern u1="&#x2026;" u2="&#x173;" k="6" />
    <hkern u1="&#x2026;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x2026;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2026;" u2="&#x21b;" k="60" />
    <hkern u1="&#x2026;" u2="&#x219;" k="5" />
    <hkern u1="&#x2026;" u2="&#x163;" k="60" />
    <hkern u1="&#x2026;" u2="&#x123;" k="10" />
    <hkern u1="&#x2026;" u2="&#x16b;" k="6" />
    <hkern u1="&#x2026;" u2="&#x171;" k="6" />
    <hkern u1="&#x2026;" u2="&#xf9;" k="6" />
    <hkern u1="&#x2026;" u2="&#xfc;" k="6" />
    <hkern u1="&#x2026;" u2="&#xfb;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16d;" k="6" />
    <hkern u1="&#x2026;" u2="&#xfa;" k="6" />
    <hkern u1="&#x2026;" u2="u" k="6" />
    <hkern u1="&#x2026;" u2="&#x165;" k="60" />
    <hkern u1="&#x2026;" u2="&#x167;" k="60" />
    <hkern u1="&#x2026;" u2="t" k="60" />
    <hkern u1="&#x2026;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2026;" u2="&#x161;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2026;" u2="s" k="5" />
    <hkern u1="&#x2026;" u2="q" k="10" />
    <hkern u1="&#x2026;" u2="&#x153;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2026;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2026;" u2="&#x151;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2026;" u2="o" k="10" />
    <hkern u1="&#x2026;" u2="&#x121;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2026;" u2="g" k="10" />
    <hkern u1="&#x2026;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2026;" u2="&#x109;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2026;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2026;" u2="&#x107;" k="10" />
    <hkern u1="&#x2026;" u2="c" k="10" />
    <hkern u1="&#x2026;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x2026;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2026;" u2="&#x21a;" k="60" />
    <hkern u1="&#x2026;" u2="&#x218;" k="5" />
    <hkern u1="&#x2026;" u2="&#x162;" k="60" />
    <hkern u1="&#x2026;" u2="&#x122;" k="10" />
    <hkern u1="&#x2026;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x2026;" u2="&#x178;" k="80" />
    <hkern u1="&#x2026;" u2="&#x176;" k="80" />
    <hkern u1="&#x2026;" u2="&#xdd;" k="80" />
    <hkern u1="&#x2026;" u2="Y" k="80" />
    <hkern u1="&#x2026;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x2026;" u2="&#x174;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x2026;" u2="W" k="40" />
    <hkern u1="&#x2026;" u2="V" k="60" />
    <hkern u1="&#x2026;" u2="&#x168;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16e;" k="6" />
    <hkern u1="&#x2026;" u2="&#x172;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16a;" k="6" />
    <hkern u1="&#x2026;" u2="&#x170;" k="6" />
    <hkern u1="&#x2026;" u2="&#xd9;" k="6" />
    <hkern u1="&#x2026;" u2="&#xdc;" k="6" />
    <hkern u1="&#x2026;" u2="&#xdb;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16c;" k="6" />
    <hkern u1="&#x2026;" u2="&#xda;" k="6" />
    <hkern u1="&#x2026;" u2="U" k="6" />
    <hkern u1="&#x2026;" u2="&#x164;" k="60" />
    <hkern u1="&#x2026;" u2="&#x166;" k="60" />
    <hkern u1="&#x2026;" u2="T" k="60" />
    <hkern u1="&#x2026;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2026;" u2="&#x160;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2026;" u2="S" k="5" />
    <hkern u1="&#x2026;" u2="Q" k="10" />
    <hkern u1="&#x2026;" u2="&#x152;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2026;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2026;" u2="&#x150;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2026;" u2="O" k="10" />
    <hkern u1="&#x2026;" u2="&#x120;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2026;" u2="G" k="10" />
    <hkern u1="&#x2026;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2026;" u2="&#x108;" k="10" />
    <hkern u1="&#x2026;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2026;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2026;" u2="&#x106;" k="10" />
    <hkern u1="&#x2026;" u2="C" k="10" />
    <hkern u1="&#x2e;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x2e;" u2="&#xff;" k="80" />
    <hkern u1="&#x2e;" u2="&#x177;" k="80" />
    <hkern u1="&#x2e;" u2="&#xfd;" k="80" />
    <hkern u1="&#x2e;" u2="y" k="80" />
    <hkern u1="&#x2e;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x2e;" u2="&#x175;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x2e;" u2="w" k="40" />
    <hkern u1="&#x2e;" u2="v" k="60" />
    <hkern u1="&#x2e;" u2="&#x169;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16f;" k="6" />
    <hkern u1="&#x2e;" u2="&#x173;" k="6" />
    <hkern u1="&#x2e;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x2e;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2e;" u2="&#x21b;" k="60" />
    <hkern u1="&#x2e;" u2="&#x219;" k="5" />
    <hkern u1="&#x2e;" u2="&#x163;" k="60" />
    <hkern u1="&#x2e;" u2="&#x123;" k="10" />
    <hkern u1="&#x2e;" u2="&#x16b;" k="6" />
    <hkern u1="&#x2e;" u2="&#x171;" k="6" />
    <hkern u1="&#x2e;" u2="&#xf9;" k="6" />
    <hkern u1="&#x2e;" u2="&#xfc;" k="6" />
    <hkern u1="&#x2e;" u2="&#xfb;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16d;" k="6" />
    <hkern u1="&#x2e;" u2="&#xfa;" k="6" />
    <hkern u1="&#x2e;" u2="u" k="6" />
    <hkern u1="&#x2e;" u2="&#x165;" k="60" />
    <hkern u1="&#x2e;" u2="&#x167;" k="60" />
    <hkern u1="&#x2e;" u2="t" k="60" />
    <hkern u1="&#x2e;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2e;" u2="&#x161;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2e;" u2="s" k="5" />
    <hkern u1="&#x2e;" u2="q" k="10" />
    <hkern u1="&#x2e;" u2="&#x153;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2e;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2e;" u2="&#x151;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2e;" u2="o" k="10" />
    <hkern u1="&#x2e;" u2="&#x121;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2e;" u2="g" k="10" />
    <hkern u1="&#x2e;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2e;" u2="&#x109;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2e;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2e;" u2="&#x107;" k="10" />
    <hkern u1="&#x2e;" u2="c" k="10" />
    <hkern u1="&#x2e;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x2e;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2e;" u2="&#x21a;" k="60" />
    <hkern u1="&#x2e;" u2="&#x218;" k="5" />
    <hkern u1="&#x2e;" u2="&#x162;" k="60" />
    <hkern u1="&#x2e;" u2="&#x122;" k="10" />
    <hkern u1="&#x2e;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x2e;" u2="&#x178;" k="80" />
    <hkern u1="&#x2e;" u2="&#x176;" k="80" />
    <hkern u1="&#x2e;" u2="&#xdd;" k="80" />
    <hkern u1="&#x2e;" u2="Y" k="80" />
    <hkern u1="&#x2e;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x2e;" u2="&#x174;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x2e;" u2="W" k="40" />
    <hkern u1="&#x2e;" u2="V" k="60" />
    <hkern u1="&#x2e;" u2="&#x168;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16e;" k="6" />
    <hkern u1="&#x2e;" u2="&#x172;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16a;" k="6" />
    <hkern u1="&#x2e;" u2="&#x170;" k="6" />
    <hkern u1="&#x2e;" u2="&#xd9;" k="6" />
    <hkern u1="&#x2e;" u2="&#xdc;" k="6" />
    <hkern u1="&#x2e;" u2="&#xdb;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16c;" k="6" />
    <hkern u1="&#x2e;" u2="&#xda;" k="6" />
    <hkern u1="&#x2e;" u2="U" k="6" />
    <hkern u1="&#x2e;" u2="&#x164;" k="60" />
    <hkern u1="&#x2e;" u2="&#x166;" k="60" />
    <hkern u1="&#x2e;" u2="T" k="60" />
    <hkern u1="&#x2e;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2e;" u2="&#x160;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2e;" u2="S" k="5" />
    <hkern u1="&#x2e;" u2="Q" k="10" />
    <hkern u1="&#x2e;" u2="&#x152;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2e;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2e;" u2="&#x150;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2e;" u2="O" k="10" />
    <hkern u1="&#x2e;" u2="&#x120;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2e;" u2="G" k="10" />
    <hkern u1="&#x2e;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2e;" u2="&#x108;" k="10" />
    <hkern u1="&#x2e;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2e;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2e;" u2="&#x106;" k="10" />
    <hkern u1="&#x2e;" u2="C" k="10" />
    <hkern u1="&#xbf;" u2="&#x1ef3;" k="30" />
    <hkern u1="&#xbf;" u2="&#xff;" k="30" />
    <hkern u1="&#xbf;" u2="&#x177;" k="30" />
    <hkern u1="&#xbf;" u2="&#xfd;" k="30" />
    <hkern u1="&#xbf;" u2="y" k="30" />
    <hkern u1="&#xbf;" u2="&#x1e81;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e85;" k="20" />
    <hkern u1="&#xbf;" u2="&#x175;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e83;" k="20" />
    <hkern u1="&#xbf;" u2="w" k="20" />
    <hkern u1="&#xbf;" u2="v" k="30" />
    <hkern u1="&#xbf;" u2="&#x1e6b;" k="42" />
    <hkern u1="&#xbf;" u2="&#x21b;" k="42" />
    <hkern u1="&#xbf;" u2="&#x163;" k="42" />
    <hkern u1="&#xbf;" u2="&#x165;" k="42" />
    <hkern u1="&#xbf;" u2="&#x167;" k="42" />
    <hkern u1="&#xbf;" u2="t" k="42" />
    <hkern u1="&#xbf;" u2="&#x1e6a;" k="42" />
    <hkern u1="&#xbf;" u2="&#x21a;" k="42" />
    <hkern u1="&#xbf;" u2="&#x162;" k="42" />
    <hkern u1="&#xbf;" u2="&#x1ef2;" k="30" />
    <hkern u1="&#xbf;" u2="&#x178;" k="30" />
    <hkern u1="&#xbf;" u2="&#x176;" k="30" />
    <hkern u1="&#xbf;" u2="&#xdd;" k="30" />
    <hkern u1="&#xbf;" u2="Y" k="30" />
    <hkern u1="&#xbf;" u2="&#x1e80;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e84;" k="20" />
    <hkern u1="&#xbf;" u2="&#x174;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e82;" k="20" />
    <hkern u1="&#xbf;" u2="W" k="20" />
    <hkern u1="&#xbf;" u2="V" k="30" />
    <hkern u1="&#xbf;" u2="&#x164;" k="42" />
    <hkern u1="&#xbf;" u2="&#x166;" k="42" />
    <hkern u1="&#xbf;" u2="T" k="42" />
    <hkern u1="&#x22;" g2="four.alt" k="40" />
    <hkern u1="&#x27;" g2="four.alt" k="40" />
    <hkern u1="&#x3b;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x3b;" u2="&#xff;" k="20" />
    <hkern u1="&#x3b;" u2="&#x177;" k="20" />
    <hkern u1="&#x3b;" u2="&#xfd;" k="20" />
    <hkern u1="&#x3b;" u2="y" k="20" />
    <hkern u1="&#x3b;" u2="&#x1e6b;" k="35" />
    <hkern u1="&#x3b;" u2="&#x21b;" k="35" />
    <hkern u1="&#x3b;" u2="&#x163;" k="35" />
    <hkern u1="&#x3b;" u2="&#x165;" k="35" />
    <hkern u1="&#x3b;" u2="&#x167;" k="35" />
    <hkern u1="&#x3b;" u2="t" k="35" />
    <hkern u1="&#x3b;" u2="&#x1e6a;" k="35" />
    <hkern u1="&#x3b;" u2="&#x21a;" k="35" />
    <hkern u1="&#x3b;" u2="&#x162;" k="35" />
    <hkern u1="&#x3b;" u2="&#x1ef2;" k="20" />
    <hkern u1="&#x3b;" u2="&#x178;" k="20" />
    <hkern u1="&#x3b;" u2="&#x176;" k="20" />
    <hkern u1="&#x3b;" u2="&#xdd;" k="20" />
    <hkern u1="&#x3b;" u2="Y" k="20" />
    <hkern u1="&#x3b;" u2="&#x164;" k="35" />
    <hkern u1="&#x3b;" u2="&#x166;" k="35" />
    <hkern u1="&#x3b;" u2="T" k="35" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="180" />
    <hkern u1="&#x7b;" u2="&#x1ef3;" k="-5" />
    <hkern u1="&#x7b;" u2="&#xff;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x177;" k="-5" />
    <hkern u1="&#x7b;" u2="&#xfd;" k="-5" />
    <hkern u1="&#x7b;" u2="y" k="-5" />
    <hkern u1="&#x7b;" u2="&#x1e81;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e85;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x175;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e83;" k="-3" />
    <hkern u1="&#x7b;" u2="w" k="-3" />
    <hkern u1="&#x7b;" u2="v" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e6b;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x21b;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x163;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x165;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x167;" k="-8" />
    <hkern u1="&#x7b;" u2="t" k="-8" />
    <hkern u1="&#x7b;" u2="&#x1e6a;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x21a;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x162;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x1ef2;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x178;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x176;" k="-5" />
    <hkern u1="&#x7b;" u2="&#xdd;" k="-5" />
    <hkern u1="&#x7b;" u2="Y" k="-5" />
    <hkern u1="&#x7b;" u2="&#x1e80;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e84;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x174;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e82;" k="-3" />
    <hkern u1="&#x7b;" u2="W" k="-3" />
    <hkern u1="&#x7b;" u2="V" k="-3" />
    <hkern u1="&#x7b;" u2="&#x164;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x166;" k="-8" />
    <hkern u1="&#x7b;" u2="T" k="-8" />
    <hkern u1="[" u2="&#x1ef3;" k="-5" />
    <hkern u1="[" u2="&#xff;" k="-5" />
    <hkern u1="[" u2="&#x177;" k="-5" />
    <hkern u1="[" u2="&#xfd;" k="-5" />
    <hkern u1="[" u2="y" k="-5" />
    <hkern u1="[" u2="&#x1e81;" k="-3" />
    <hkern u1="[" u2="&#x1e85;" k="-3" />
    <hkern u1="[" u2="&#x175;" k="-3" />
    <hkern u1="[" u2="&#x1e83;" k="-3" />
    <hkern u1="[" u2="w" k="-3" />
    <hkern u1="[" u2="v" k="-3" />
    <hkern u1="[" u2="&#x1e6b;" k="-8" />
    <hkern u1="[" u2="&#x21b;" k="-8" />
    <hkern u1="[" u2="&#x163;" k="-8" />
    <hkern u1="[" u2="&#x165;" k="-8" />
    <hkern u1="[" u2="&#x167;" k="-8" />
    <hkern u1="[" u2="t" k="-8" />
    <hkern u1="[" u2="&#x1e6a;" k="-8" />
    <hkern u1="[" u2="&#x21a;" k="-8" />
    <hkern u1="[" u2="&#x162;" k="-8" />
    <hkern u1="[" u2="&#x1ef2;" k="-5" />
    <hkern u1="[" u2="&#x178;" k="-5" />
    <hkern u1="[" u2="&#x176;" k="-5" />
    <hkern u1="[" u2="&#xdd;" k="-5" />
    <hkern u1="[" u2="Y" k="-5" />
    <hkern u1="[" u2="&#x1e80;" k="-3" />
    <hkern u1="[" u2="&#x1e84;" k="-3" />
    <hkern u1="[" u2="&#x174;" k="-3" />
    <hkern u1="[" u2="&#x1e82;" k="-3" />
    <hkern u1="[" u2="W" k="-3" />
    <hkern u1="[" u2="V" k="-3" />
    <hkern u1="[" u2="&#x164;" k="-8" />
    <hkern u1="[" u2="&#x166;" k="-8" />
    <hkern u1="[" u2="T" k="-8" />
    <hkern u1="&#x28;" u2="&#x1ef3;" k="-5" />
    <hkern u1="&#x28;" u2="&#xff;" k="-5" />
    <hkern u1="&#x28;" u2="&#x177;" k="-5" />
    <hkern u1="&#x28;" u2="&#xfd;" k="-5" />
    <hkern u1="&#x28;" u2="y" k="-5" />
    <hkern u1="&#x28;" u2="&#x1e81;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e85;" k="-3" />
    <hkern u1="&#x28;" u2="&#x175;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e83;" k="-3" />
    <hkern u1="&#x28;" u2="w" k="-3" />
    <hkern u1="&#x28;" u2="v" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e6b;" k="-8" />
    <hkern u1="&#x28;" u2="&#x21b;" k="-8" />
    <hkern u1="&#x28;" u2="&#x163;" k="-8" />
    <hkern u1="&#x28;" u2="&#x165;" k="-8" />
    <hkern u1="&#x28;" u2="&#x167;" k="-8" />
    <hkern u1="&#x28;" u2="t" k="-8" />
    <hkern u1="&#x28;" u2="&#x1e6a;" k="-8" />
    <hkern u1="&#x28;" u2="&#x21a;" k="-8" />
    <hkern u1="&#x28;" u2="&#x162;" k="-8" />
    <hkern u1="&#x28;" u2="&#x1ef2;" k="-5" />
    <hkern u1="&#x28;" u2="&#x178;" k="-5" />
    <hkern u1="&#x28;" u2="&#x176;" k="-5" />
    <hkern u1="&#x28;" u2="&#xdd;" k="-5" />
    <hkern u1="&#x28;" u2="Y" k="-5" />
    <hkern u1="&#x28;" u2="&#x1e80;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e84;" k="-3" />
    <hkern u1="&#x28;" u2="&#x174;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e82;" k="-3" />
    <hkern u1="&#x28;" u2="W" k="-3" />
    <hkern u1="&#x28;" u2="V" k="-3" />
    <hkern u1="&#x28;" u2="&#x164;" k="-8" />
    <hkern u1="&#x28;" u2="&#x166;" k="-8" />
    <hkern u1="&#x28;" u2="T" k="-8" />
    <hkern u1="&#x2d;" u2="x" k="15" />
    <hkern u1="&#x2d;" u2="v" k="10" />
    <hkern u1="&#x2d;" u2="X" k="15" />
    <hkern u1="&#x2d;" u2="V" k="10" />
    <hkern u1="&#xad;" u2="x" k="15" />
    <hkern u1="&#xad;" u2="v" k="10" />
    <hkern u1="&#xad;" u2="X" k="15" />
    <hkern u1="&#xad;" u2="V" k="10" />
    <hkern u1="&#x201e;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x201e;" u2="&#xff;" k="80" />
    <hkern u1="&#x201e;" u2="&#x177;" k="80" />
    <hkern u1="&#x201e;" u2="&#xfd;" k="80" />
    <hkern u1="&#x201e;" u2="y" k="80" />
    <hkern u1="&#x201e;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x201e;" u2="&#x175;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x201e;" u2="w" k="40" />
    <hkern u1="&#x201e;" u2="v" k="60" />
    <hkern u1="&#x201e;" u2="&#x169;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16f;" k="6" />
    <hkern u1="&#x201e;" u2="&#x173;" k="6" />
    <hkern u1="&#x201e;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x201e;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201e;" u2="&#x21b;" k="60" />
    <hkern u1="&#x201e;" u2="&#x219;" k="5" />
    <hkern u1="&#x201e;" u2="&#x163;" k="60" />
    <hkern u1="&#x201e;" u2="&#x123;" k="10" />
    <hkern u1="&#x201e;" u2="&#x16b;" k="6" />
    <hkern u1="&#x201e;" u2="&#x171;" k="6" />
    <hkern u1="&#x201e;" u2="&#xf9;" k="6" />
    <hkern u1="&#x201e;" u2="&#xfc;" k="6" />
    <hkern u1="&#x201e;" u2="&#xfb;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16d;" k="6" />
    <hkern u1="&#x201e;" u2="&#xfa;" k="6" />
    <hkern u1="&#x201e;" u2="u" k="6" />
    <hkern u1="&#x201e;" u2="&#x165;" k="60" />
    <hkern u1="&#x201e;" u2="&#x167;" k="60" />
    <hkern u1="&#x201e;" u2="t" k="60" />
    <hkern u1="&#x201e;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201e;" u2="&#x161;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201e;" u2="s" k="5" />
    <hkern u1="&#x201e;" u2="q" k="10" />
    <hkern u1="&#x201e;" u2="&#x153;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201e;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201e;" u2="&#x151;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201e;" u2="o" k="10" />
    <hkern u1="&#x201e;" u2="&#x121;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201e;" u2="g" k="10" />
    <hkern u1="&#x201e;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201e;" u2="&#x109;" k="10" />
    <hkern u1="&#x201e;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201e;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201e;" u2="&#x107;" k="10" />
    <hkern u1="&#x201e;" u2="c" k="10" />
    <hkern u1="&#x201e;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x201e;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201e;" u2="&#x21a;" k="60" />
    <hkern u1="&#x201e;" u2="&#x218;" k="5" />
    <hkern u1="&#x201e;" u2="&#x162;" k="60" />
    <hkern u1="&#x201e;" u2="&#x122;" k="10" />
    <hkern u1="&#x201e;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x201e;" u2="&#x178;" k="80" />
    <hkern u1="&#x201e;" u2="&#x176;" k="80" />
    <hkern u1="&#x201e;" u2="&#xdd;" k="80" />
    <hkern u1="&#x201e;" u2="Y" k="80" />
    <hkern u1="&#x201e;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x201e;" u2="&#x174;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x201e;" u2="W" k="40" />
    <hkern u1="&#x201e;" u2="V" k="60" />
    <hkern u1="&#x201e;" u2="&#x168;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16e;" k="6" />
    <hkern u1="&#x201e;" u2="&#x172;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16a;" k="6" />
    <hkern u1="&#x201e;" u2="&#x170;" k="6" />
    <hkern u1="&#x201e;" u2="&#xd9;" k="6" />
    <hkern u1="&#x201e;" u2="&#xdc;" k="6" />
    <hkern u1="&#x201e;" u2="&#xdb;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16c;" k="6" />
    <hkern u1="&#x201e;" u2="&#xda;" k="6" />
    <hkern u1="&#x201e;" u2="U" k="6" />
    <hkern u1="&#x201e;" u2="&#x164;" k="60" />
    <hkern u1="&#x201e;" u2="&#x166;" k="60" />
    <hkern u1="&#x201e;" u2="T" k="60" />
    <hkern u1="&#x201e;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201e;" u2="&#x160;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201e;" u2="S" k="5" />
    <hkern u1="&#x201e;" u2="Q" k="10" />
    <hkern u1="&#x201e;" u2="&#x152;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201e;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201e;" u2="&#x150;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201e;" u2="O" k="10" />
    <hkern u1="&#x201e;" u2="&#x120;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201e;" u2="G" k="10" />
    <hkern u1="&#x201e;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201e;" u2="&#x108;" k="10" />
    <hkern u1="&#x201e;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201e;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201e;" u2="&#x106;" k="10" />
    <hkern u1="&#x201e;" u2="C" k="10" />
    <hkern u1="&#x201c;" u2="&#xc1;" k="60" />
    <hkern u1="&#x201c;" u2="A" k="60" />
    <hkern u1="&#x201c;" u2="&#x102;" k="60" />
    <hkern u1="&#x201c;" u2="&#x17c;" k="4" />
    <hkern u1="&#x201c;" u2="&#x17e;" k="4" />
    <hkern u1="&#x201c;" u2="&#x17a;" k="4" />
    <hkern u1="&#x201c;" u2="z" k="4" />
    <hkern u1="&#x201c;" u2="x" k="2" />
    <hkern u1="&#x201c;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201c;" u2="&#x219;" k="5" />
    <hkern u1="&#x201c;" u2="&#x123;" k="10" />
    <hkern u1="&#x201c;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201c;" u2="&#x161;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201c;" u2="s" k="5" />
    <hkern u1="&#x201c;" u2="q" k="10" />
    <hkern u1="&#x201c;" u2="&#x153;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201c;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201c;" u2="&#x151;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201c;" u2="o" k="10" />
    <hkern u1="&#x201c;" u2="&#x135;" k="40" />
    <hkern u1="&#x201c;" u2="&#x237;" k="40" />
    <hkern u1="&#x201c;" u2="j" k="40" />
    <hkern u1="&#x201c;" u2="&#x133;" k="40" />
    <hkern u1="&#x201c;" u2="&#x121;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201c;" u2="g" k="10" />
    <hkern u1="&#x201c;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201c;" u2="&#x109;" k="10" />
    <hkern u1="&#x201c;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201c;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201c;" u2="&#x107;" k="10" />
    <hkern u1="&#x201c;" u2="c" k="10" />
    <hkern u1="&#x201c;" u2="&#xe3;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe5;" k="60" />
    <hkern u1="&#x201c;" u2="&#x105;" k="60" />
    <hkern u1="&#x201c;" u2="&#x101;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe0;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe4;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe2;" k="60" />
    <hkern u1="&#x201c;" u2="&#x103;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe1;" k="60" />
    <hkern u1="&#x201c;" u2="a" k="60" />
    <hkern u1="&#x201c;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201c;" u2="&#x218;" k="5" />
    <hkern u1="&#x201c;" u2="&#x122;" k="10" />
    <hkern u1="&#x201c;" u2="&#x17b;" k="4" />
    <hkern u1="&#x201c;" u2="&#x17d;" k="4" />
    <hkern u1="&#x201c;" u2="&#x179;" k="4" />
    <hkern u1="&#x201c;" u2="Z" k="4" />
    <hkern u1="&#x201c;" u2="X" k="2" />
    <hkern u1="&#x201c;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201c;" u2="&#x160;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201c;" u2="S" k="5" />
    <hkern u1="&#x201c;" u2="Q" k="10" />
    <hkern u1="&#x201c;" u2="&#x152;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201c;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201c;" u2="&#x150;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201c;" u2="O" k="10" />
    <hkern u1="&#x201c;" u2="&#x134;" k="40" />
    <hkern u1="&#x201c;" u2="J" k="40" />
    <hkern u1="&#x201c;" u2="&#x132;" k="40" />
    <hkern u1="&#x201c;" u2="&#x120;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201c;" u2="G" k="10" />
    <hkern u1="&#x201c;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201c;" u2="&#x108;" k="10" />
    <hkern u1="&#x201c;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201c;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201c;" u2="&#x106;" k="10" />
    <hkern u1="&#x201c;" u2="C" k="10" />
    <hkern u1="&#x201c;" u2="&#xc3;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc5;" k="60" />
    <hkern u1="&#x201c;" u2="&#x104;" k="60" />
    <hkern u1="&#x201c;" u2="&#x100;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc0;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc4;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc2;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc1;" k="60" />
    <hkern u1="&#x201d;" u2="A" k="60" />
    <hkern u1="&#x201d;" u2="&#x102;" k="60" />
    <hkern u1="&#x201d;" u2="&#x17c;" k="4" />
    <hkern u1="&#x201d;" u2="&#x17e;" k="4" />
    <hkern u1="&#x201d;" u2="&#x17a;" k="4" />
    <hkern u1="&#x201d;" u2="z" k="4" />
    <hkern u1="&#x201d;" u2="x" k="2" />
    <hkern u1="&#x201d;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201d;" u2="&#x219;" k="5" />
    <hkern u1="&#x201d;" u2="&#x123;" k="10" />
    <hkern u1="&#x201d;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201d;" u2="&#x161;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201d;" u2="s" k="5" />
    <hkern u1="&#x201d;" u2="q" k="10" />
    <hkern u1="&#x201d;" u2="&#x153;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201d;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201d;" u2="&#x151;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201d;" u2="o" k="10" />
    <hkern u1="&#x201d;" u2="&#x135;" k="40" />
    <hkern u1="&#x201d;" u2="&#x237;" k="40" />
    <hkern u1="&#x201d;" u2="j" k="40" />
    <hkern u1="&#x201d;" u2="&#x133;" k="40" />
    <hkern u1="&#x201d;" u2="&#x121;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201d;" u2="g" k="10" />
    <hkern u1="&#x201d;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201d;" u2="&#x109;" k="10" />
    <hkern u1="&#x201d;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201d;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201d;" u2="&#x107;" k="10" />
    <hkern u1="&#x201d;" u2="c" k="10" />
    <hkern u1="&#x201d;" u2="&#xe3;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe5;" k="60" />
    <hkern u1="&#x201d;" u2="&#x105;" k="60" />
    <hkern u1="&#x201d;" u2="&#x101;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe0;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe4;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe2;" k="60" />
    <hkern u1="&#x201d;" u2="&#x103;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe1;" k="60" />
    <hkern u1="&#x201d;" u2="a" k="60" />
    <hkern u1="&#x201d;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201d;" u2="&#x218;" k="5" />
    <hkern u1="&#x201d;" u2="&#x122;" k="10" />
    <hkern u1="&#x201d;" u2="&#x17b;" k="4" />
    <hkern u1="&#x201d;" u2="&#x17d;" k="4" />
    <hkern u1="&#x201d;" u2="&#x179;" k="4" />
    <hkern u1="&#x201d;" u2="Z" k="4" />
    <hkern u1="&#x201d;" u2="X" k="2" />
    <hkern u1="&#x201d;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201d;" u2="&#x160;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201d;" u2="S" k="5" />
    <hkern u1="&#x201d;" u2="Q" k="10" />
    <hkern u1="&#x201d;" u2="&#x152;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201d;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201d;" u2="&#x150;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201d;" u2="O" k="10" />
    <hkern u1="&#x201d;" u2="&#x134;" k="40" />
    <hkern u1="&#x201d;" u2="J" k="40" />
    <hkern u1="&#x201d;" u2="&#x132;" k="40" />
    <hkern u1="&#x201d;" u2="&#x120;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201d;" u2="G" k="10" />
    <hkern u1="&#x201d;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201d;" u2="&#x108;" k="10" />
    <hkern u1="&#x201d;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201d;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201d;" u2="&#x106;" k="10" />
    <hkern u1="&#x201d;" u2="C" k="10" />
    <hkern u1="&#x201d;" u2="&#xc3;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc5;" k="60" />
    <hkern u1="&#x201d;" u2="&#x104;" k="60" />
    <hkern u1="&#x201d;" u2="&#x100;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc0;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc4;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc2;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc1;" k="60" />
    <hkern u1="&#x2018;" u2="A" k="60" />
    <hkern u1="&#x2018;" u2="&#x102;" k="60" />
    <hkern u1="&#x2018;" u2="&#x17c;" k="4" />
    <hkern u1="&#x2018;" u2="&#x17e;" k="4" />
    <hkern u1="&#x2018;" u2="&#x17a;" k="4" />
    <hkern u1="&#x2018;" u2="z" k="4" />
    <hkern u1="&#x2018;" u2="x" k="2" />
    <hkern u1="&#x2018;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2018;" u2="&#x219;" k="5" />
    <hkern u1="&#x2018;" u2="&#x123;" k="10" />
    <hkern u1="&#x2018;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2018;" u2="&#x161;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2018;" u2="s" k="5" />
    <hkern u1="&#x2018;" u2="q" k="10" />
    <hkern u1="&#x2018;" u2="&#x153;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2018;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2018;" u2="&#x151;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2018;" u2="o" k="10" />
    <hkern u1="&#x2018;" u2="&#x135;" k="40" />
    <hkern u1="&#x2018;" u2="&#x237;" k="40" />
    <hkern u1="&#x2018;" u2="j" k="40" />
    <hkern u1="&#x2018;" u2="&#x133;" k="40" />
    <hkern u1="&#x2018;" u2="&#x121;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2018;" u2="g" k="10" />
    <hkern u1="&#x2018;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2018;" u2="&#x109;" k="10" />
    <hkern u1="&#x2018;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2018;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2018;" u2="&#x107;" k="10" />
    <hkern u1="&#x2018;" u2="c" k="10" />
    <hkern u1="&#x2018;" u2="&#xe3;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe5;" k="60" />
    <hkern u1="&#x2018;" u2="&#x105;" k="60" />
    <hkern u1="&#x2018;" u2="&#x101;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe0;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe4;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe2;" k="60" />
    <hkern u1="&#x2018;" u2="&#x103;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe1;" k="60" />
    <hkern u1="&#x2018;" u2="a" k="60" />
    <hkern u1="&#x2018;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2018;" u2="&#x218;" k="5" />
    <hkern u1="&#x2018;" u2="&#x122;" k="10" />
    <hkern u1="&#x2018;" u2="&#x17b;" k="4" />
    <hkern u1="&#x2018;" u2="&#x17d;" k="4" />
    <hkern u1="&#x2018;" u2="&#x179;" k="4" />
    <hkern u1="&#x2018;" u2="Z" k="4" />
    <hkern u1="&#x2018;" u2="X" k="2" />
    <hkern u1="&#x2018;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2018;" u2="&#x160;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2018;" u2="S" k="5" />
    <hkern u1="&#x2018;" u2="Q" k="10" />
    <hkern u1="&#x2018;" u2="&#x152;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2018;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2018;" u2="&#x150;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2018;" u2="O" k="10" />
    <hkern u1="&#x2018;" u2="&#x134;" k="40" />
    <hkern u1="&#x2018;" u2="J" k="40" />
    <hkern u1="&#x2018;" u2="&#x132;" k="40" />
    <hkern u1="&#x2018;" u2="&#x120;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2018;" u2="G" k="10" />
    <hkern u1="&#x2018;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2018;" u2="&#x108;" k="10" />
    <hkern u1="&#x2018;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2018;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2018;" u2="&#x106;" k="10" />
    <hkern u1="&#x2018;" u2="C" k="10" />
    <hkern u1="&#x2018;" u2="&#xc3;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc5;" k="60" />
    <hkern u1="&#x2018;" u2="&#x104;" k="60" />
    <hkern u1="&#x2018;" u2="&#x100;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc0;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc4;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc2;" k="60" />
    <hkern u1="&#x2019;" u2="x" k="2" />
    <hkern u1="&#x2019;" u2="q" k="10" />
    <hkern u1="&#x2019;" u2="&#x153;" k="10" />
    <hkern u1="&#x2019;" u2="X" k="2" />
    <hkern u1="&#x2019;" u2="Q" k="10" />
    <hkern u1="&#x2019;" u2="&#x152;" k="10" />
    <hkern u1="&#x201a;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x201a;" u2="&#xff;" k="80" />
    <hkern u1="&#x201a;" u2="&#x177;" k="80" />
    <hkern u1="&#x201a;" u2="&#xfd;" k="80" />
    <hkern u1="&#x201a;" u2="y" k="80" />
    <hkern u1="&#x201a;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x201a;" u2="&#x175;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x201a;" u2="w" k="40" />
    <hkern u1="&#x201a;" u2="v" k="60" />
    <hkern u1="&#x201a;" u2="&#x169;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16f;" k="6" />
    <hkern u1="&#x201a;" u2="&#x173;" k="6" />
    <hkern u1="&#x201a;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x201a;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201a;" u2="&#x21b;" k="60" />
    <hkern u1="&#x201a;" u2="&#x219;" k="5" />
    <hkern u1="&#x201a;" u2="&#x163;" k="60" />
    <hkern u1="&#x201a;" u2="&#x123;" k="10" />
    <hkern u1="&#x201a;" u2="&#x16b;" k="6" />
    <hkern u1="&#x201a;" u2="&#x171;" k="6" />
    <hkern u1="&#x201a;" u2="&#xf9;" k="6" />
    <hkern u1="&#x201a;" u2="&#xfc;" k="6" />
    <hkern u1="&#x201a;" u2="&#xfb;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16d;" k="6" />
    <hkern u1="&#x201a;" u2="&#xfa;" k="6" />
    <hkern u1="&#x201a;" u2="u" k="6" />
    <hkern u1="&#x201a;" u2="&#x165;" k="60" />
    <hkern u1="&#x201a;" u2="&#x167;" k="60" />
    <hkern u1="&#x201a;" u2="t" k="60" />
    <hkern u1="&#x201a;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201a;" u2="&#x161;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201a;" u2="s" k="5" />
    <hkern u1="&#x201a;" u2="q" k="10" />
    <hkern u1="&#x201a;" u2="&#x153;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201a;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201a;" u2="&#x151;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201a;" u2="o" k="10" />
    <hkern u1="&#x201a;" u2="&#x121;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201a;" u2="g" k="10" />
    <hkern u1="&#x201a;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201a;" u2="&#x109;" k="10" />
    <hkern u1="&#x201a;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201a;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201a;" u2="&#x107;" k="10" />
    <hkern u1="&#x201a;" u2="c" k="10" />
    <hkern u1="&#x201a;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x201a;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201a;" u2="&#x21a;" k="60" />
    <hkern u1="&#x201a;" u2="&#x218;" k="5" />
    <hkern u1="&#x201a;" u2="&#x162;" k="60" />
    <hkern u1="&#x201a;" u2="&#x122;" k="10" />
    <hkern u1="&#x201a;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x201a;" u2="&#x178;" k="80" />
    <hkern u1="&#x201a;" u2="&#x176;" k="80" />
    <hkern u1="&#x201a;" u2="&#xdd;" k="80" />
    <hkern u1="&#x201a;" u2="Y" k="80" />
    <hkern u1="&#x201a;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x201a;" u2="&#x174;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x201a;" u2="W" k="40" />
    <hkern u1="&#x201a;" u2="V" k="60" />
    <hkern u1="&#x201a;" u2="&#x168;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16e;" k="6" />
    <hkern u1="&#x201a;" u2="&#x172;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16a;" k="6" />
    <hkern u1="&#x201a;" u2="&#x170;" k="6" />
    <hkern u1="&#x201a;" u2="&#xd9;" k="6" />
    <hkern u1="&#x201a;" u2="&#xdc;" k="6" />
    <hkern u1="&#x201a;" u2="&#xdb;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16c;" k="6" />
    <hkern u1="&#x201a;" u2="&#xda;" k="6" />
    <hkern u1="&#x201a;" u2="U" k="6" />
    <hkern u1="&#x201a;" u2="&#x164;" k="60" />
    <hkern u1="&#x201a;" u2="&#x166;" k="60" />
    <hkern u1="&#x201a;" u2="T" k="60" />
    <hkern u1="&#x201a;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201a;" u2="&#x160;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201a;" u2="S" k="5" />
    <hkern u1="&#x201a;" u2="Q" k="10" />
    <hkern u1="&#x201a;" u2="&#x152;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201a;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201a;" u2="&#x150;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201a;" u2="O" k="10" />
    <hkern u1="&#x201a;" u2="&#x120;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201a;" u2="G" k="10" />
    <hkern u1="&#x201a;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201a;" u2="&#x108;" k="10" />
    <hkern u1="&#x201a;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201a;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201a;" u2="&#x106;" k="10" />
    <hkern u1="&#x201a;" u2="C" k="10" />
    <hkern u1="&#x102;" u2="&#x2018;" k="60" />
    <hkern u1="&#x102;" u2="&#x201d;" k="60" />
    <hkern u1="&#x102;" u2="&#x201c;" k="60" />
    <hkern u1="&#x102;" u2="&#x3f;" k="30" />
    <hkern u1="&#x102;" u2="v" k="25" />
    <hkern u1="&#x102;" u2="q" k="1" />
    <hkern u1="&#x102;" u2="&#x153;" k="1" />
    <hkern u1="&#x102;" u2="V" k="25" />
    <hkern u1="&#x102;" u2="Q" k="1" />
    <hkern u1="&#x102;" u2="&#x152;" k="1" />
    <hkern u1="&#x2bc;" u2="x" k="2" />
    <hkern u1="&#x2bc;" u2="q" k="10" />
    <hkern u1="&#x2bc;" u2="&#x153;" k="10" />
    <hkern u1="&#x2bc;" u2="X" k="2" />
    <hkern u1="&#x2bc;" u2="Q" k="10" />
    <hkern u1="&#x2bc;" u2="&#x152;" k="10" />
    <hkern u1="A" u2="&#x2018;" k="60" />
    <hkern u1="A" u2="&#x201d;" k="60" />
    <hkern u1="A" u2="&#x201c;" k="60" />
    <hkern u1="A" u2="&#x3f;" k="30" />
    <hkern u1="A" u2="v" k="25" />
    <hkern u1="A" u2="q" k="1" />
    <hkern u1="A" u2="&#x153;" k="1" />
    <hkern u1="A" u2="V" k="25" />
    <hkern u1="A" u2="Q" k="1" />
    <hkern u1="A" u2="&#x152;" k="1" />
    <hkern u1="&#xc1;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc1;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc1;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc1;" u2="v" k="25" />
    <hkern u1="&#xc1;" u2="q" k="1" />
    <hkern u1="&#xc1;" u2="&#x153;" k="1" />
    <hkern u1="&#xc1;" u2="V" k="25" />
    <hkern u1="&#xc1;" u2="Q" k="1" />
    <hkern u1="&#xc1;" u2="&#x152;" k="1" />
    <hkern g1="hyphen,uni00AD"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="10" />
    <hkern g1="hyphen,uni00AD"
	g2="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	k="32" />
    <hkern g1="hyphen,uni00AD"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="4" />
    <hkern g1="hyphen,uni00AD"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="18" />
    <hkern g1="hyphen,uni00AD"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="10" />
    <hkern g1="hyphen,uni00AD"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="10" />
    <hkern g1="hyphen,uni00AD"
	g2="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	k="32" />
    <hkern g1="hyphen,uni00AD"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="4" />
    <hkern g1="hyphen,uni00AD"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="18" />
    <hkern g1="hyphen,uni00AD"
	g2="z,zacute,zcaron,zdotaccent"
	k="10" />
    <hkern g1="B,uni1E02"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="1" />
    <hkern g1="B,uni1E02"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="B,uni1E02"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="1" />
    <hkern g1="B,uni1E02"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="B,uni1E02"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="11" />
    <hkern g1="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="11" />
    <hkern g1="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	g2="quoteright,uni02BC"
	k="6" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="1" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="1" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="F,uni1E1E"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="13" />
    <hkern g1="F,uni1E1E"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="10" />
    <hkern g1="F,uni1E1E"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="13" />
    <hkern g1="F,uni1E1E"
	g2="z,zacute,zcaron,zdotaccent"
	k="10" />
    <hkern g1="F,uni1E1E"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="F,uni1E1E"
	g2="ae,aeacute"
	k="40" />
    <hkern g1="F,uni1E1E"
	g2="IJ,J,Jcircumflex"
	k="20" />
    <hkern g1="F,uni1E1E"
	g2="ij,j,uni0237,jcircumflex"
	k="20" />
    <hkern g1="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="1" />
    <hkern g1="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="1" />
    <hkern g1="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="J,Jcircumflex"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="J,Jcircumflex"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="K,uni0136"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	k="7" />
    <hkern g1="K,uni0136"
	g2="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	k="7" />
    <hkern g1="K,uni0136"
	g2="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	k="7" />
    <hkern g1="K,uni0136"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	k="5" />
    <hkern g1="K,uni0136"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde"
	k="4" />
    <hkern g1="K,uni0136"
	g2="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	k="7" />
    <hkern g1="K,uni0136"
	g2="g,gbreve,gcircumflex,gdotaccent,uni0123"
	k="7" />
    <hkern g1="K,uni0136"
	g2="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	k="7" />
    <hkern g1="K,uni0136"
	g2="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	k="5" />
    <hkern g1="K,uni0136"
	g2="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde"
	k="4" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	k="50" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="17" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="62" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	k="50" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="17" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="62" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="quoteright,uni02BC"
	k="75" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="IJ,J,Jcircumflex"
	k="-5" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="ij,j,uni0237,jcircumflex"
	k="-5" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="hyphen,uni00AD"
	k="40" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="1" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="1" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="P,uni1E56"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="13" />
    <hkern g1="P,uni1E56"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="2" />
    <hkern g1="P,uni1E56"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="13" />
    <hkern g1="P,uni1E56"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="2" />
    <hkern g1="P,uni1E56"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="P,uni1E56"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="P,uni1E56"
	g2="IJ,J,Jcircumflex"
	k="18" />
    <hkern g1="P,uni1E56"
	g2="ij,j,uni0237,jcircumflex"
	k="18" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="10" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="7" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="7" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	g2="quoteright,uni02BC"
	k="5" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="38" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="38" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	g2="AE,AEacute"
	k="50" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	g2="ae,aeacute"
	k="50" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	g2="IJ,J,Jcircumflex"
	k="30" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	g2="ij,j,uni0237,jcircumflex"
	k="30" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	g2="hyphen,uni00AD"
	k="32" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="15" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="15" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="AE,AEacute"
	k="25" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="ae,aeacute"
	k="25" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="IJ,J,Jcircumflex"
	k="13" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="ij,j,uni0237,jcircumflex"
	k="13" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="hyphen,uni00AD"
	k="4" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="49" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="49" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="AE,AEacute"
	k="63" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ae,aeacute"
	k="63" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="IJ,J,Jcircumflex"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ij,j,uni0237,jcircumflex"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	k="7" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="g,gbreve,gcircumflex,gdotaccent,uni0123"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	k="7" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,uni00AD"
	k="18" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="hyphen,uni00AD"
	k="15" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	k="38" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="15" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="49" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	k="38" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="15" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="49" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="quoteright,uni02BC"
	k="60" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	k="1" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	k="1" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	k="1" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	k="1" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="g,gbreve,gcircumflex,gdotaccent,uni0123"
	k="1" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	k="1" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="hyphen,uni00AD"
	k="10" />
    <hkern g1="b,uni1E03"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="1" />
    <hkern g1="b,uni1E03"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="b,uni1E03"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="1" />
    <hkern g1="b,uni1E03"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="b,uni1E03"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="11" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="11" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="quoteright,uni02BC"
	k="6" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="1" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="1" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="f,uni1E1F"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="13" />
    <hkern g1="f,uni1E1F"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="10" />
    <hkern g1="f,uni1E1F"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="13" />
    <hkern g1="f,uni1E1F"
	g2="z,zacute,zcaron,zdotaccent"
	k="10" />
    <hkern g1="f,uni1E1F"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="f,uni1E1F"
	g2="ae,aeacute"
	k="40" />
    <hkern g1="f,uni1E1F"
	g2="IJ,J,Jcircumflex"
	k="20" />
    <hkern g1="f,uni1E1F"
	g2="ij,j,uni0237,jcircumflex"
	k="20" />
    <hkern g1="g,gbreve,gcircumflex,gdotaccent,uni0123"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="1" />
    <hkern g1="g,gbreve,gcircumflex,gdotaccent,uni0123"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="g,gbreve,gcircumflex,gdotaccent,uni0123"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="1" />
    <hkern g1="g,gbreve,gcircumflex,gdotaccent,uni0123"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="g,gbreve,gcircumflex,gdotaccent,uni0123"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="g,gbreve,gcircumflex,gdotaccent,uni0123"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="g,gbreve,gcircumflex,gdotaccent,uni0123"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="j,uni0237,jcircumflex"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="j,uni0237,jcircumflex"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	k="7" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	k="7" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	k="7" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	k="5" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde"
	k="4" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	k="7" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="g,gbreve,gcircumflex,gdotaccent,uni0123"
	k="7" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	k="7" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	k="5" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde"
	k="4" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	k="50" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="17" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="62" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	k="50" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="17" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="62" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="quoteright,uni02BC"
	k="75" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="IJ,J,Jcircumflex"
	k="-5" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="ij,j,uni0237,jcircumflex"
	k="-5" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="hyphen,uni00AD"
	k="40" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="1" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="1" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="p,uni1E57"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="13" />
    <hkern g1="p,uni1E57"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="2" />
    <hkern g1="p,uni1E57"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="13" />
    <hkern g1="p,uni1E57"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="2" />
    <hkern g1="p,uni1E57"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="p,uni1E57"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="p,uni1E57"
	g2="IJ,J,Jcircumflex"
	k="18" />
    <hkern g1="p,uni1E57"
	g2="ij,j,uni0237,jcircumflex"
	k="18" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="10" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="7" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="7" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	g2="quoteright,uni02BC"
	k="5" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="38" />
    <hkern g1="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="38" />
    <hkern g1="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	g2="AE,AEacute"
	k="50" />
    <hkern g1="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	g2="ae,aeacute"
	k="50" />
    <hkern g1="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	g2="IJ,J,Jcircumflex"
	k="30" />
    <hkern g1="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	g2="ij,j,uni0237,jcircumflex"
	k="30" />
    <hkern g1="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	g2="hyphen,uni00AD"
	k="32" />
    <hkern g1="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="15" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="15" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="AE,AEacute"
	k="25" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="ae,aeacute"
	k="25" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="IJ,J,Jcircumflex"
	k="13" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="ij,j,uni0237,jcircumflex"
	k="13" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="hyphen,uni00AD"
	k="4" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="49" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="49" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="AE,AEacute"
	k="63" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="ae,aeacute"
	k="63" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="IJ,J,Jcircumflex"
	k="35" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="ij,j,uni0237,jcircumflex"
	k="35" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	k="15" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	k="15" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	k="15" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	k="7" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	k="15" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="g,gbreve,gcircumflex,gdotaccent,uni0123"
	k="15" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	k="15" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	k="7" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="hyphen,uni00AD"
	k="18" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="hyphen,uni00AD"
	k="15" />
    <hkern g1="quoteright,uni02BC"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="60" />
    <hkern g1="quoteright,uni02BC"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="4" />
    <hkern g1="quoteright,uni02BC"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="60" />
    <hkern g1="quoteright,uni02BC"
	g2="z,zacute,zcaron,zdotaccent"
	k="4" />
    <hkern g1="quoteright,uni02BC"
	g2="IJ,J,Jcircumflex"
	k="40" />
    <hkern g1="quoteright,uni02BC"
	g2="ij,j,uni0237,jcircumflex"
	k="40" />
    <hkern g1="quoteright,uni02BC"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	k="10" />
    <hkern g1="quoteright,uni02BC"
	g2="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	k="10" />
    <hkern g1="quoteright,uni02BC"
	g2="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	k="10" />
    <hkern g1="quoteright,uni02BC"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	k="5" />
    <hkern g1="quoteright,uni02BC"
	g2="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	k="10" />
    <hkern g1="quoteright,uni02BC"
	g2="g,gbreve,gcircumflex,gdotaccent,uni0123"
	k="10" />
    <hkern g1="quoteright,uni02BC"
	g2="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	k="10" />
    <hkern g1="quoteright,uni02BC"
	g2="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	k="5" />
  </font>
</defs></svg>
