<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20161116 at Fri Jan 17 23:45:07 2014
 By <PERSON>
Copyright \(c\) 2010 by Ryoichi Tsunekawa. All rights reserved.
</metadata>
<defs>
<font id="BebasNeueLight" horiz-adv-x="376" >
  <font-face 
    font-family="Bebas Neue Light"
    font-weight="300"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 5 0 0 0 0 0 0 0"
    ascent="750"
    descent="-250"
    x-height="700"
    cap-height="700"
    bbox="-47 -179 801 875"
    underline-thickness="50"
    underline-position="-110"
    unicode-range="U+000D-F6C3"
  />
<missing-glyph horiz-adv-x="548" 
d="M50 700h448v-700h-448v700zM265 361l-140 -298h18l132 281l132 -281h19l-139 298l129 274h-17l-123 -259l-123 259h-18z" />
    <glyph glyph-name=".notdef" horiz-adv-x="548" 
d="M50 700h448v-700h-448v700zM265 361l-140 -298h18l132 281l132 -281h19l-139 298l129 274h-17l-123 -259l-123 259h-18z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="155" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="155" 
 />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="447" 
d="M154 737h-24l82 82h18l83 -82h-25l-67 66zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="447" 
d="M193 817v-80h-20v80h20zM270 817v-80h-21v80h21zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="447" 
d="M181 826l72 -89h-20l-78 89h26zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="447" 
d="M312 784v-17h-181v17h181zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="447" 
d="M407 0q-9 -7 -18 -17.5t-16 -22.5t-11.5 -24.5t-3.5 -23.5q1 -17 12.5 -25t28.5 -8q28 0 46 15v-18q-22 -13 -51 -13q-22 1 -38 11.5t-17 34.5q-2 23 12.5 48.5t35.5 43.5l-40 164h-249l-41 -165h-20l169 701h31l173 -701h-3zM102 185h241l-122 490z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="447" 
d="M221 863q26 0 44 -17t18 -43q0 -27 -18 -44.5t-44 -17.5t-44.5 17.5t-18.5 44.5q0 26 18.5 43t44.5 17zM221 848q-20 0 -34 -12.5t-14 -32.5q0 -21 14 -34t34 -13q19 0 33 13t14 34q0 20 -14 32.5t-33 12.5zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249z
M102 185h241l-122 490z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="447" 
d="M176 805q14 0 26 -6t23.5 -13t22.5 -13t22 -5q13 1 21.5 10.5t13.5 20.5l15 -9q-8 -16 -19.5 -27.5t-28.5 -12.5q-13 -1 -25 5t-23.5 13t-23 13t-23.5 6q-14 0 -24 -9t-17 -22l-14 9q9 17 22 28t32 12zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249z
M102 185h241l-122 490z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="592" 
d="M520 365v-20h-176v-324h210v-21h-232v168h-199l-66 -168h-21l272 700h246v-21h-210v-314h176zM130 189h192v485z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="592" 
d="M413 837l-77 -89h-20l72 89h25zM520 365v-20h-176v-324h210v-21h-232v168h-199l-66 -168h-21l272 700h246v-21h-210v-314h176zM130 189h192v485z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="367" 
d="M180 700q76 0 105.5 -37.5t29.5 -107.5v-45q0 -59 -20.5 -93.5t-79.5 -43.5q62 -9 88 -49t26 -101v-62q0 -74 -34.5 -117.5t-113.5 -43.5h-141v700h140zM153 382q36 0 62.5 5t44 19t25.5 38.5t8 62.5v47q0 62 -23.5 93.5t-89.5 31.5h-118v-297h91zM181 21q69 0 97.5 37
t28.5 103v63q0 76 -36 107t-108 31h-101v-341h119z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="370" 
d="M185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-54h-22v55q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49t6.5 60v77h22v-76
q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="370" 
d="M258 839l-78 -89h-20l72 89h26zM185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-54h-22v55q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5
t21 49t6.5 60v77h22v-76q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="370" 
d="M124 829l68 -67l67 67h25l-83 -82h-18l-82 82h23zM185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-54h-22v55q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5
q34 0 58 12.5t38.5 33.5t21 49t6.5 60v77h22v-76q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="370" 
d="M185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-54h-22v55q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49t6.5 60v77h22v-76
q0 -35 -7.5 -67t-24.5 -56t-43.5 -38.5t-64.5 -15.5v-34q15 0 28.5 -2.5t24 -8.5t16.5 -16t6 -26q0 -17 -7.5 -28t-19.5 -18t-27 -10t-30 -3t-30 3t-27 10t-20 18.5t-8 27.5v4h17v-3q0 -13 6.5 -22t16.5 -14.5t22 -8.5t23 -3q10 0 22 2.5t21.5 8t16 14.5t6.5 22
q0 14 -6 22.5t-15.5 13t-21 6t-23.5 1.5h-5v44q-38 1 -64.5 16t-43 39t-24 55.5t-7.5 66.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="370" 
d="M123 746h-24l82 82h18l83 -82h-25l-67 66zM185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-54h-22v55q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5
t38.5 33.5t21 49t6.5 60v77h22v-76q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="370" 
d="M195 821v-80h-20v80h20zM185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-54h-22v55q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49
t6.5 60v77h22v-76q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="D" unicode="D" 
d="M186 700q40 0 69 -13.5t47.5 -36.5t27 -55.5t8.5 -69.5v-351q0 -37 -8.5 -69t-27 -55t-47 -36.5t-68.5 -13.5h-147v700h146zM186 21q35 0 60 11.5t40.5 32t22.5 48t7 60.5v353q0 33 -7 61t-23 48.5t-40.5 32t-59.5 11.5h-124v-658h124z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" 
d="M186 700q40 0 69 -13.5t47.5 -36.5t27 -55.5t8.5 -69.5v-351q0 -37 -8.5 -69t-27 -55t-47 -36.5t-68.5 -13.5h-147v341h-50v19h50v340h146zM159 341h-97v-320h124q35 0 60 11.5t40.5 32t22.5 48t7 60.5v353q0 33 -7 61t-23 48.5t-40.5 32t-59.5 11.5h-124v-319h97v-19z
" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" 
d="M110 823l68 -66l67 66h25l-83 -82h-18l-82 82h23zM186 700q40 0 69 -13.5t47.5 -36.5t27 -55.5t8.5 -69.5v-351q0 -37 -8.5 -69t-27 -55t-47 -36.5t-68.5 -13.5h-147v700h146zM186 21q35 0 60 11.5t40.5 32t22.5 48t7 60.5v353q0 33 -7 61t-23 48.5t-40.5 32t-59.5 11.5
h-124v-658h124z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" 
d="M186 700q40 0 69 -13.5t47.5 -36.5t27 -55.5t8.5 -69.5v-351q0 -37 -8.5 -69t-27 -55t-47 -36.5t-68.5 -13.5h-147v339h-45v19h45v342h146zM160 339h-98v-318h124q35 0 60 11.5t40.5 32t22.5 48t7 60.5v353q0 33 -7 61t-23 48.5t-40.5 32t-59.5 11.5h-124v-321h98v-19z
" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="345" 
d="M268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="345" 
d="M243 830l-78 -89h-20l72 89h26zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="345" 
d="M264 826q-2 -38 -28.5 -58t-63.5 -20q-36 0 -63 20t-28 58h17q1 -32 23 -46.5t51 -14.5t51 14.5t24 46.5h17zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="345" 
d="M110 824l67 -66l67 66h25l-83 -82h-18l-82 82h24zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="345" 
d="M105 743h-23l82 82h18l83 -82h-25l-67 67zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="345" 
d="M145 822v-81h-20v81h20zM221 822v-81h-20v81h20zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="345" 
d="M187 824v-80h-21v80h21zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="345" 
d="M142 840l73 -89h-21l-78 89h26zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="345" 
d="M256 787v-17h-172v17h172zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="345" 
d="M268 365v-20h-206v-324h245v-21h-16q-9 -7 -18.5 -17.5t-17 -22.5t-12 -24.5t-3.5 -23.5q2 -17 13.5 -25t27.5 -8q13 0 25.5 3.5t21.5 11.5v-18q-22 -13 -52 -13q-22 1 -37.5 11.5t-17.5 34.5q-1 12 3 24.5t11 24.5t16 23t19 19h-230v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="333" 
d="M258 348v-21h-196v-327h-22v700h257v-21h-235v-331h196z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="371" 
d="M185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-46h-22v47q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49t6.5 60v159h-109v20h131
v-178q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="371" 
d="M279 829q-2 -38 -29 -58t-63 -20t-63 20t-28 58h17q1 -32 23 -46.5t51 -14.5t51 14.5t24 46.5h17zM185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-46h-22v47q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358
q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49t6.5 60v159h-109v20h131v-178q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="371" 
d="M119 751h-23l82 82h18l82 -82h-24l-67 67zM185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-46h-22v47q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5
t38.5 33.5t21 49t6.5 60v159h-109v20h131v-178q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="371" 
d="M204 826v-80h-20v80h20zM185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-46h-22v47q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49
t6.5 60v159h-109v20h131v-178q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="377" 
d="M62 340v-340h-22v700h22v-340h253v340h22v-700h-22v340h-253z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="378" 
d="M337 534v-534h-22v340h-252v-340h-22v534h-40v17h40v149h22v-149h252v149h22v-149h38v-17h-38zM315 534h-252v-174h252v174z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="377" 
d="M121 748h-24l82 82h18l83 -82h-25l-67 67zM62 340v-340h-22v700h22v-340h253v340h22v-700h-22v340h-253z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="102" 
d="M62 700v-700h-22v700h22z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="354" 
d="M62 700v-700h-22v700h22zM315 171q0 -89 -36 -128t-103 -43h-38v21h37q64 5 91 40t27 109v530h22v-529z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="102" 
d="M93 831h25l-77 -89h-21zM40 700h22v-700h-22v700z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="102" 
d="M143 820q-2 -38 -29 -58t-63 -20t-63 20t-28 58h17q1 -32 23 -46.5t51 -14.5t51 14.5t24 46.5h17zM40 700h22v-700h-22v700z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="102" 
d="M60 823l82 -82h-24l-67 67l-68 -67h-23l82 82h18zM40 700h22v-700h-22v700z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="102" 
d="M23 822v-80h-20v80h20zM99 822v-80h-20v80h20zM62 700v-700h-22v700h22z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="102" 
d="M61 822v-80h-20v80h20zM62 700v-700h-22v700h22z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="102" 
d="M-24 830h26l72 -89h-21zM40 700h22v-700h-22v700z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="102" 
d="M124 784v-17h-146v17h146zM62 700v-700h-22v700h22z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="102" 
d="M59 0q-9 -7 -18 -17.5t-15.5 -22.5t-10.5 -24.5t-3 -23.5q2 -17 13.5 -25t27.5 -8q29 0 47 15v-18q-22 -13 -52 -13q-22 1 -37.5 11.5t-17.5 34.5q-2 23 11.5 47t36.5 44h-2l1 700h22v-700h-3z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="102" 
d="M151 796q-8 -16 -19.5 -27.5t-28.5 -12.5q-13 0 -25 5.5t-23.5 13t-23 13t-23.5 5.5q-14 0 -23.5 -9t-17.5 -22l-14 9q9 17 22.5 28t31.5 12q14 0 26.5 -5.5t24 -13t22.5 -13t22 -5.5q12 1 20.5 10.5t14.5 20.5zM62 700v-700h-22v700h22z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="252" 
d="M36 21q11 -1 21.5 -0.5t20.5 1.5q59 5 86 39.5t27 108.5v530h22v-529q0 -87 -34.5 -126.5t-98.5 -43.5q-11 -1 -21.5 -1.5t-22.5 0.5v21z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="252" 
d="M132 743h-24l82 82h18l83 -82h-24l-67 67zM213 171q0 -87 -34.5 -126.5t-98.5 -43.5q-11 -1 -21.5 -1.5t-22.5 0.5v21q11 -1 21.5 -0.5t20.5 1.5q59 5 86 39.5t27 108.5v530h22v-529z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="401" 
d="M115 348l-53 -80v-268h-22v700h22v-395l260 395h24l-217 -331l235 -369h-24z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="333" 
d="M40 0v700h22v-679h235v-21h-257z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="333" 
d="M134 830l-77 -89h-20l72 89h25zM40 0v700h22v-679h235v-21h-257z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="333" 
d="M40 0v700h22v-679h235v-21h-257zM223 619h-6v81h22v-80l-22 -68h-11z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="333" 
d="M40 0v700h22v-679h235v-21h-257zM197 395v-81h-21v81h21z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="347" 
d="M76 336v-315h235v-21h-257v313l-54 -47v23l54 48v363h22v-340l87 82v-23z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="492" 
d="M258 2h-25l-173 665v-667h-20v700h34l172 -664l172 664h34v-700h-22v667z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="378" 
d="M61 0h-21v700h31l246 -655v655h21v-700h-27l-250 668v-668z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="378" 
d="M264 833l-77 -89h-21l73 89h25zM61 1h-21v700h31l246 -655v655h21v-700h-27l-250 668v-668z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="378" 
d="M123 830l68 -66l67 66h24l-82 -82h-18l-82 82h23zM61 0h-21v700h31l246 -655v655h21v-700h-27l-250 668v-668z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="378" 
d="M289 801q-8 -16 -19.5 -27t-28.5 -12q-13 -1 -25 5t-23 13t-22.5 12.5t-23.5 5.5q-14 0 -24 -9t-18 -22l-14 9q9 17 22.5 28t31.5 12q14 0 26.5 -5.5t24 -12.5t22.5 -13t22 -5q12 0 20.5 9.5t14.5 20.5zM61 0h-21v700h31l246 -655v655h21v-700h-27l-250 668v-668z" />
    <glyph glyph-name="O" unicode="O" 
d="M39 528q0 88 37.5 132.5t110.5 44.5q75 0 112.5 -44.5t37.5 -132.5v-356q0 -88 -37.5 -132.5t-112.5 -44.5q-73 0 -110.5 44.5t-37.5 132.5v356zM61 171q0 -156 126 -156q128 0 128 156v358q0 156 -128 156q-126 0 -126 -156v-358z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" 
d="M254 839l-77 -89h-20l72 89h25zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358
q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" 
d="M279 829q-2 -38 -28.5 -58t-63.5 -20q-35 0 -62 20t-28 58h16q1 -32 23.5 -46.5t50.5 -14.5q29 0 51.5 14.5t24.5 46.5h16zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356
q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" 
d="M120 748h-24l82 82h18l83 -82h-25l-67 67zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5
t-6.5 -60.5v-358q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" 
d="M159 829v-81h-20v81h20zM236 829v-81h-21v81h21zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5
t-6.5 -60.5v-358q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" 
d="M146 839l72 -89h-21l-77 89h26zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358
q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" 
d="M227 839l-75 -89h-20l70 89h25zM294 839l-75 -89h-19l70 89h24zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5
t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" 
d="M274 785v-18h-170v18h170zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358
q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" 
d="M311 644q26 -44 26 -116v-356q0 -88 -37.5 -132.5t-112.5 -44.5q-73 0 -111 46l-19 -48l-16 5l23 60q-25 44 -25 114v356q0 88 37.5 132.5t110.5 44.5q74 0 112 -45l17 44l16 -5zM61 171q0 -50 14 -86l215 551q-33 49 -103 49q-126 0 -126 -156v-358zM315 529
q0 52 -15 88l-215 -552q32 -50 102 -50q128 0 128 156v358z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" 
d="M238 839h25l-77 -89h-20zM311 644q26 -44 26 -116v-356q0 -88 -37.5 -132.5t-112.5 -44.5q-73 0 -111 46l-19 -48l-16 5l23 60q-25 44 -25 114v356q0 88 37.5 132.5t110.5 44.5q74 0 112 -45l17 44l16 -5zM61 171q0 -50 14 -86l215 551q-33 49 -103 49q-126 0 -126 -156
v-358zM315 529q0 52 -15 88l-215 -552q32 -50 102 -50q128 0 128 156v358z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" 
d="M292 798q-8 -16 -19.5 -27.5t-28.5 -12.5q-13 -1 -25 5t-23.5 13t-23 13t-23.5 6q-14 0 -24 -9t-17 -22l-14 9q9 17 22.5 28t31.5 11q14 0 26 -5.5t23.5 -12.5t22.5 -13t22 -5q13 1 21.5 10.5t13.5 20.5zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356
q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358
q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="561" 
d="M489 365v-20h-177v-324h211v-21h-346q-39 0 -65 14.5t-42.5 38.5t-23.5 55.5t-7 66.5v350q0 34 7 66t23.5 56t42.5 38.5t65 14.5h346v-21h-211v-314h177zM178 679q-33 0 -55.5 -13t-36 -34t-19.5 -48.5t-6 -57.5v-352q0 -30 6 -57.5t19.5 -48.5t36 -34t55.5 -13h112v658
h-112z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="360" 
d="M178 700q40 0 68 -12.5t45 -35t24.5 -53.5t7.5 -68v-70q0 -84 -38.5 -124t-122.5 -40h-100v-297h-22v700h138zM162 318q74 0 106.5 33.5t32.5 108.5v72q0 32 -6 59t-20.5 46.5t-38 30.5t-58.5 11h-116v-361h100z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="377" 
d="M178 562q40 0 68 -12.5t45 -35t24.5 -53.5t7.5 -68v-70q0 -84 -38.5 -124t-122.5 -40h-100v-159h-22v700h22v-138h116zM162 180q74 0 106.5 33.5t32.5 108.5v72q0 32 -6 59t-20.5 46.5t-38 30.5t-58.5 11h-116v-361h100z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="412" 
d="M188 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -39 -9 -72t-29 -57q11 -19 27 -26t37 -7h11v-20h-11q-26 0 -45.5 9t-32.5 30q-17 -16 -41.5 -25t-56.5 -9q-40 0 -68.5 14t-46.5 38t-26 56t-8 69v356q0 37 8 69t26 56t46.5 38t68.5 14zM188 685
q-35 0 -59 -12.5t-39 -33.5t-22 -49.5t-7 -60.5v-358q0 -32 7 -60.5t22 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="380" 
d="M179 700q78 0 111 -42.5t33 -114.5v-63q0 -65 -26.5 -100.5t-84.5 -46.5q59 -9 85 -45.5t26 -94.5v-110q0 -20 3.5 -43t15.5 -40h-24q-11 17 -14 38.5t-3 44.5v110q0 39 -11 64t-31.5 39.5t-48.5 20.5t-62 6h-86v-323h-22v700h139zM148 344q36 0 64.5 5.5t48 20.5
t30 40.5t10.5 66.5v64q0 64 -27.5 101t-95.5 37h-116v-335h86z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="380" 
d="M242 839l-78 -89h-20l72 89h26zM179 700q78 0 111 -42.5t33 -114.5v-63q0 -65 -26.5 -100.5t-84.5 -46.5q59 -9 85 -45.5t26 -94.5v-110q0 -20 3.5 -43t15.5 -40h-24q-11 17 -14 38.5t-3 44.5v110q0 39 -11 64t-31.5 39.5t-48.5 20.5t-62 6h-86v-323h-22v700h139z
M148 344q36 0 64.5 5.5t48 20.5t30 40.5t10.5 66.5v64q0 64 -27.5 101t-95.5 37h-116v-335h86z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="380" 
d="M103 831l68 -66l67 66h25l-83 -82h-18l-82 82h23zM179 700q78 0 111 -42.5t33 -114.5v-63q0 -65 -26.5 -100.5t-84.5 -46.5q59 -9 85 -45.5t26 -94.5v-110q0 -20 3.5 -43t15.5 -40h-24q-11 17 -14 38.5t-3 44.5v110q0 39 -11 64t-31.5 39.5t-48.5 20.5t-62 6h-86v-323
h-22v700h139zM148 344q36 0 64.5 5.5t48 20.5t30 40.5t10.5 66.5v64q0 64 -27.5 101t-95.5 37h-116v-335h86z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="369" 
d="M185 705q40 0 67.5 -13.5t44.5 -37.5t25 -55.5t8 -67.5v-13h-22v14q0 31 -6.5 59t-20.5 49t-37.5 33t-57.5 12q-35 0 -58.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -46t64.5 -47t49.5 -59t20 -80.5q0 -36 -8 -68t-25.5 -55.5t-45.5 -37
t-68 -13.5t-67.5 13.5t-45 37t-25.5 55.5t-8 68v34h21v-35q0 -32 6.5 -59.5t21.5 -48t38.5 -32.5t58.5 -12q34 0 58 12t39 32.5t21.5 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64.5 45.5t-64.5 47t-49.5 59.5t-20 81.5q0 77 34 124.5t110 48.5z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="369" 
d="M261 839l-77 -89h-21l73 89h25zM185 705q40 0 67.5 -13.5t44.5 -37.5t25 -55.5t8 -67.5v-13h-22v14q0 31 -6.5 59t-20.5 49t-37.5 33t-57.5 12q-35 0 -58.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -46t64.5 -47t49.5 -59t20 -80.5
q0 -36 -8 -68t-25.5 -55.5t-45.5 -37t-68 -13.5t-67.5 13.5t-45 37t-25.5 55.5t-8 68v34h21v-35q0 -32 6.5 -59.5t21.5 -48t38.5 -32.5t58.5 -12q34 0 58 12t39 32.5t21.5 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64.5 45.5t-64.5 47t-49.5 59.5t-20 81.5q0 77 34 124.5
t110 48.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="369" 
d="M120 831l68 -66l67 66h24l-82 -82h-19l-81 82h23zM185 705q40 0 67.5 -13.5t44.5 -37.5t25 -55.5t8 -67.5v-13h-22v14q0 31 -6.5 59t-20.5 49t-37.5 33t-57.5 12q-35 0 -58.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -46t64.5 -47t49.5 -59
t20 -80.5q0 -36 -8 -68t-25.5 -55.5t-45.5 -37t-68 -13.5t-67.5 13.5t-45 37t-25.5 55.5t-8 68v34h21v-35q0 -32 6.5 -59.5t21.5 -48t38.5 -32.5t58.5 -12q34 0 58 12t39 32.5t21.5 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64.5 45.5t-64.5 47t-49.5 59.5t-20 81.5
q0 77 34 124.5t110 48.5z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="369" 
d="M185 705q40 0 67.5 -13.5t44.5 -37.5t25 -55.5t8 -67.5v-13h-22v14q0 31 -6.5 59t-20.5 49t-37.5 33t-57.5 12q-35 0 -58.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -46t64.5 -47t49.5 -59t20 -80.5q0 -35 -7.5 -66t-24 -54.5t-42.5 -38
t-63 -15.5v-31q14 0 27.5 -3t24 -9.5t16.5 -17t6 -26.5q0 -17 -7.5 -28.5t-19.5 -18.5t-27 -10t-30 -3t-30 3.5t-27 10.5t-20 18.5t-8 27.5v4h16v-3q0 -13 7 -22t17 -14t22 -7.5t23 -2.5t23 2t21.5 7.5t16 14t6.5 21.5q0 14 -6 22.5t-15.5 13t-21 6t-23.5 1.5h-6v44
q-39 1 -65.5 15t-43.5 37.5t-24.5 54.5t-7.5 67v34h21v-35q0 -32 6.5 -59.5t21.5 -48t38.5 -32.5t58.5 -12q34 0 58 12t39 32.5t21.5 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64.5 45.5t-64.5 47t-49.5 59.5t-20 81.5q0 77 34 124.5t110 48.5z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="369" 
d="M124 748h-24l82 82h18l83 -82h-25l-67 67zM185 705q40 0 67.5 -13.5t44.5 -37.5t25 -55.5t8 -67.5v-13h-22v14q0 31 -6.5 59t-20.5 49t-37.5 33t-57.5 12q-35 0 -58.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -46t64.5 -47t49.5 -59
t20 -80.5q0 -36 -8 -68t-25.5 -55.5t-45.5 -37t-68 -13.5t-67.5 13.5t-45 37t-25.5 55.5t-8 68v34h21v-35q0 -32 6.5 -59.5t21.5 -48t38.5 -32.5t58.5 -12q34 0 58 12t39 32.5t21.5 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64.5 45.5t-64.5 47t-49.5 59.5t-20 81.5
q0 77 34 124.5t110 48.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="374" 
d="M175 0v679h-153v21h328v-21h-153v-679h-22z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="400" 
d="M291 457v-18h-80v-439h-22v439h-80v18h80v222h-153v21h328v-21h-153v-222h80z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="400" 
d="M132 825l68 -66l67 66h24l-82 -82h-19l-81 82h23zM189 0v679h-153v21h328v-21h-153v-679h-22z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="367" 
d="M61 172q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="367" 
d="M259 839l-77 -89h-21l73 89h25zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="367" 
d="M277 820q-2 -38 -28.5 -58t-63.5 -20q-36 0 -63 20t-28 58h17q1 -32 23 -46.5t51 -14.5t51 14.5t24 46.5h17zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5
t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="367" 
d="M117 748h-23l82 82h18l83 -82h-25l-67 67zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="367" 
d="M155 826v-80h-20v80h20zM232 826v-80h-21v80h21zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="367" 
d="M141 831l72 -89h-21l-77 89h26zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="367" 
d="M222 837l-75 -89h-20l70 89h25zM289 837l-75 -89h-19l70 89h24zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21
v-527z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="367" 
d="M269 784v-17h-163v17h163zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="367" 
d="M328 173q0 -59 -21 -107t-75 -65q-9 -7 -18 -18t-16 -23t-11 -24.5t-3 -23.5q2 -17 13.5 -25t27.5 -8q29 0 47 15v-18q-22 -13 -51 -13q-23 1 -38.5 11.5t-17.5 34.5q-1 23 13 47t32 41q-6 -1 -12.5 -1.5t-13.5 -0.5q-40 0 -67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527
h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="367" 
d="M189 859q26 0 44.5 -17.5t18.5 -43.5t-18.5 -43.5t-44.5 -17.5t-44 17.5t-18 43.5t18 43.5t44 17.5zM189 844q-20 0 -33.5 -13t-13.5 -33t13.5 -33.5t33.5 -13.5t34 13.5t14 33.5t-14 33t-34 13zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5
t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="367" 
d="M284 798q-8 -16 -19.5 -27.5t-28.5 -12.5q-13 -1 -25 5t-23 13t-22.5 13t-23.5 6q-14 0 -24 -9t-18 -22l-14 9q9 17 22.5 28t31.5 11q14 0 26.5 -5.5t24 -12.5t22.5 -13t22 -5q12 1 20.5 10.5t14.5 20.5zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5
t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="446" 
d="M60 700l164 -675l164 675h21l-168 -700h-33l-171 700h23z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="618" 
d="M195 0h-32l-126 700h22l122 -670l121 666h27l114 -662l118 666h20l-124 -700h-29l-113 648z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="618" 
d="M376 831l-77 -89h-21l73 89h25zM195 0h-32l-126 700h22l122 -670l121 666h27l114 -662l118 666h20l-124 -700h-29l-113 648z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="618" 
d="M245 746h-24l82 82h18l83 -82h-24l-67 66zM195 0h-32l-126 700h22l122 -670l121 666h27l114 -662l118 666h20l-124 -700h-29l-113 648z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="618" 
d="M286 829v-81h-20v81h20zM363 829v-81h-21v81h21zM195 0h-32l-126 700h22l122 -670l121 666h27l114 -662l118 666h20l-124 -700h-29l-113 648z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="618" 
d="M281 839l72 -89h-20l-78 89h26zM195 0h-32l-126 700h22l122 -670l121 666h27l114 -662l118 666h20l-124 -700h-29l-113 648z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="444" 
d="M221 344l-161 -344h-23l171 364l-159 336h23l151 -317l151 317h22l-159 -336l170 -364h-24z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="422" 
d="M199 283l-163 417h22l153 -387l153 387h23l-166 -417v-283h-22v283z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="422" 
d="M285 830l-78 -89h-20l73 89h25zM199 283l-163 417h22l153 -387l153 387h23l-166 -417v-283h-22v283z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="422" 
d="M144 745h-24l82 82h18l83 -82h-24l-67 66zM199 283l-163 417h22l153 -387l153 387h23l-166 -417v-283h-22v283z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="422" 
d="M183 822v-80h-21v80h21zM259 822v-80h-20v80h20zM199 283l-163 417h22l153 -387l153 387h23l-166 -417v-283h-22v283z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="422" 
d="M169 839l73 -89h-21l-77 89h25zM199 283l-163 417h22l153 -387l153 387h23l-166 -417v-283h-22v283z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="395" 
d="M351 0h-313v21l294 658h-280v21h304v-22l-295 -657h290v-21z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="395" 
d="M285 839l-78 -89h-20l72 89h26zM351 0h-313v21l294 658h-280v21h304v-22l-295 -657h290v-21z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="395" 
d="M138 830l68 -66l67 66h25l-83 -82h-18l-82 82h23zM351 0h-313v21l294 658h-280v21h304v-22l-295 -657h290v-21z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="395" 
d="M225 829v-80h-20v80h20zM351 0h-313v21l294 658h-280v21h304v-22l-295 -657h290v-21z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="371" 
d="M185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-46h-22v47q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49t6.5 60v159h-109v20h131
v-178q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14zM175 -114v80h19v-78l-21 -63h-10z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="401" 
d="M115 348l-53 -80v-268h-22v700h22v-395l260 395h24l-217 -331l235 -369h-24zM178 -114v80h20v-78l-21 -63h-11z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="333" 
d="M40 0v700h22v-679h235v-21h-257zM166 -118v80h19v-79l-21 -62h-10z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="378" 
d="M61 0h-21v700h31l246 -655v655h21v-700h-27l-250 668v-668zM178 -114v80h19v-78l-21 -63h-10z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="380" 
d="M179 700q78 0 111 -42.5t33 -114.5v-63q0 -65 -26.5 -100.5t-84.5 -46.5q59 -9 85 -45.5t26 -94.5v-110q0 -20 3.5 -43t15.5 -40h-24q-11 17 -14 38.5t-3 44.5v110q0 39 -11 64t-31.5 39.5t-48.5 20.5t-62 6h-86v-323h-22v700h139zM148 344q36 0 64.5 5.5t48 20.5
t30 40.5t10.5 66.5v64q0 64 -27.5 101t-95.5 37h-116v-335h86zM175 -113v80h19v-78l-21 -63h-10z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="400" 
d="M208 0v-33q13 0 26.5 -3t23.5 -9.5t16.5 -17.5t6.5 -27q0 -17 -8 -29t-20 -19.5t-26.5 -11t-28.5 -3.5t-29 3.5t-27 11t-20 19.5t-8 28v4h15v-4q0 -13 6.5 -21.5t17 -14t22.5 -8t23 -2.5q10 0 22 2.5t21.5 8t16 14.5t6.5 22q0 14 -6 22.5t-16 13t-21.5 6t-23.5 1.5h-5v47
h-3v679h-153v21h328v-21h-153v-679h-3z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="369" 
d="M185 705q40 0 67.5 -13.5t44.5 -37.5t25 -55.5t8 -67.5v-13h-22v14q0 31 -6.5 59t-20.5 49t-37.5 33t-57.5 12q-35 0 -58.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -46t64.5 -47t49.5 -59t20 -80.5q0 -36 -8 -68t-25.5 -55.5t-45.5 -37
t-68 -13.5t-67.5 13.5t-45 37t-25.5 55.5t-8 68v34h21v-35q0 -32 6.5 -59.5t21.5 -48t38.5 -32.5t58.5 -12q34 0 58 12t39 32.5t21.5 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64.5 45.5t-64.5 47t-49.5 59.5t-20 81.5q0 77 34 124.5t110 48.5zM174 -114v80h20v-78l-21 -63h-11
z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="400" 
d="M189 0v679h-153v21h328v-21h-153v-679h-22zM190 -114v80h20v-78l-21 -63h-11z" />
    <glyph glyph-name="uni1E02" unicode="&#x1e02;" horiz-adv-x="367" 
d="M174 824v-80h-20v80h20zM180 700q76 0 105.5 -37.5t29.5 -107.5v-45q0 -59 -20.5 -93.5t-79.5 -43.5q62 -9 88 -49t26 -101v-62q0 -74 -34.5 -117.5t-113.5 -43.5h-141v700h140zM153 382q36 0 62.5 5t44 19t25.5 38.5t8 62.5v47q0 62 -23.5 93.5t-89.5 31.5h-118v-297h91
zM181 21q69 0 97.5 37t28.5 103v63q0 76 -36 107t-108 31h-101v-341h119z" />
    <glyph glyph-name="uni1E0A" unicode="&#x1e0a;" 
d="M180 824v-80h-21v80h21zM186 700q40 0 69 -13.5t47.5 -36.5t27 -55.5t8.5 -69.5v-351q0 -37 -8.5 -69t-27 -55t-47 -36.5t-68.5 -13.5h-147v700h146zM186 21q35 0 60 11.5t40.5 32t22.5 48t7 60.5v353q0 33 -7 61t-23 48.5t-40.5 32t-59.5 11.5h-124v-658h124z" />
    <glyph glyph-name="uni1E1E" unicode="&#x1e1e;" horiz-adv-x="333" 
d="M178 823v-80h-20v80h20zM258 348v-21h-196v-327h-22v700h257v-21h-235v-331h196z" />
    <glyph glyph-name="uni1E40" unicode="&#x1e40;" horiz-adv-x="492" 
d="M256 830v-82h-21v82h21zM258 2h-25l-173 665v-667h-20v700h34l172 -664l172 664h34v-700h-22v667z" />
    <glyph glyph-name="uni1E56" unicode="&#x1e56;" horiz-adv-x="359" 
d="M174 824v-80h-21v80h21zM178 700q40 0 68 -12.5t45 -35t24.5 -53.5t7.5 -68v-70q0 -84 -38.5 -124t-122.5 -40h-100v-297h-22v700h138zM162 318q74 0 106.5 33.5t32.5 108.5v72q0 32 -6 59t-20.5 46.5t-38 30.5t-58.5 11h-116v-361h100z" />
    <glyph glyph-name="uni1E60" unicode="&#x1e60;" horiz-adv-x="369" 
d="M195 825v-80h-21v80h21zM185 705q40 0 67.5 -13.5t44.5 -37.5t25 -55.5t8 -67.5v-13h-22v14q0 31 -6.5 59t-20.5 49t-37.5 33t-57.5 12q-35 0 -58.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -46t64.5 -47t49.5 -59t20 -80.5q0 -36 -8 -68
t-25.5 -55.5t-45.5 -37t-68 -13.5t-67.5 13.5t-45 37t-25.5 55.5t-8 68v34h21v-35q0 -32 6.5 -59.5t21.5 -48t38.5 -32.5t58.5 -12q34 0 58 12t39 32.5t21.5 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64.5 45.5t-64.5 47t-49.5 59.5t-20 81.5q0 77 34 124.5t110 48.5z" />
    <glyph glyph-name="uni1E6A" unicode="&#x1e6a;" horiz-adv-x="400" 
d="M210 826v-80h-20v80h20zM189 0v679h-153v21h328v-21h-153v-679h-22z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="447" 
d="M98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="447" 
d="M279 827l-78 -89h-20l72 89h26zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="447" 
d="M313 811q-2 -38 -28.5 -58t-63.5 -20q-35 0 -62 20t-28 58h16q1 -31 23.5 -46t50.5 -15q29 0 51.5 15t24.5 46h16zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="447" 
d="M154 737h-24l82 82h18l83 -82h-25l-67 66zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="447" 
d="M193 817v-80h-20v80h20zM270 817v-80h-21v80h21zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="447" 
d="M181 826l72 -89h-20l-78 89h26zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="447" 
d="M312 784v-17h-181v17h181zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="447" 
d="M406 0q-9 -7 -18 -17.5t-16 -22.5t-11.5 -24.5t-3.5 -23.5q1 -17 13 -24.5t29 -7.5q13 0 25 3t21 11v-18q-22 -13 -51 -13q-22 1 -38 11.5t-17 34.5q-2 23 12.5 48.5t35.5 43.5l-40 164h-249l-41 -165h-20l169 701h31l173 -701h-4zM102 185h241l-122 490z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="447" 
d="M221 863q26 0 44 -17t18 -43q0 -27 -18 -44.5t-44 -17.5t-44.5 17.5t-18.5 44.5q0 26 18.5 43t44.5 17zM221 848q-20 0 -34 -12.5t-14 -32.5q0 -21 14 -34t34 -13q19 0 33 13t14 34q0 20 -14 32.5t-33 12.5zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249z
M102 185h241l-122 490z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="447" 
d="M176 805q14 0 26.5 -6t24 -13t22.5 -13t22 -5q13 1 21.5 10t13.5 20l14 -8q-8 -16 -19.5 -27.5t-28.5 -12.5q-13 -1 -25 5t-23.5 13t-23.5 13t-24 6q-14 0 -24 -8.5t-17 -21.5l-13 8q9 17 22 28t32 12zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249z
M102 185h241l-122 490z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="592" 
d="M520 365v-20h-176v-324h210v-21h-232v168h-199l-66 -168h-21l272 700h246v-21h-210v-314h176zM130 189h192v485z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="592" 
d="M413 837l-77 -89h-20l72 89h25zM520 365v-20h-176v-324h210v-21h-232v168h-199l-66 -168h-21l272 700h246v-21h-210v-314h176zM130 189h192v485z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="367" 
d="M180 700q76 0 105.5 -37.5t29.5 -107.5v-45q0 -59 -20.5 -93.5t-79.5 -43.5q62 -9 88 -49t26 -101v-62q0 -74 -34.5 -117.5t-113.5 -43.5h-141v700h140zM153 382q36 0 62.5 5t44 19t25.5 38.5t8 62.5v47q0 62 -23.5 93.5t-89.5 31.5h-118v-297h91zM181 21q69 0 97.5 37
t28.5 103v63q0 76 -36 107t-108 31h-101v-341h119z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="370" 
d="M185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-54h-22v55q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49t6.5 60v77h22v-76
q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="370" 
d="M258 839l-78 -89h-20l72 89h26zM185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-54h-22v55q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5
t21 49t6.5 60v77h22v-76q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="370" 
d="M124 829l68 -67l67 67h25l-83 -82h-18l-82 82h23zM185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-54h-22v55q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5
q34 0 58 12.5t38.5 33.5t21 49t6.5 60v77h22v-76q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="370" 
d="M185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-54h-22v55q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49t6.5 60v77h22v-76
q0 -35 -7.5 -67t-24.5 -56t-43.5 -38.5t-64.5 -15.5v-30q29 -1 52 -13.5t23 -43.5q0 -17 -7.5 -28.5t-19.5 -19t-27 -10.5t-30 -3t-30 3.5t-27 11t-20 19t-8 27.5v4h16v-3q0 -13 6.5 -22t17 -14.5t22.5 -8t23 -2.5q10 0 22 2.5t21.5 8t16 14t6.5 21.5q0 14 -6 22.5t-15.5 13
t-21 6t-23.5 1.5h-5v44q-38 1 -64.5 16t-43 39t-24 55.5t-7.5 66.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="370" 
d="M123 746h-24l82 82h18l83 -82h-25l-67 66zM185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-54h-22v55q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5
t38.5 33.5t21 49t6.5 60v77h22v-76q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="370" 
d="M195 821v-80h-20v80h20zM185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-54h-22v55q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49
t6.5 60v77h22v-76q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="d" unicode="d" 
d="M186 700q40 0 69 -13.5t47.5 -36.5t27 -55.5t8.5 -69.5v-351q0 -37 -8.5 -69t-27 -55t-47 -36.5t-68.5 -13.5h-147v700h146zM186 21q35 0 60 11.5t40.5 32t22.5 48t7 60.5v353q0 33 -7 61t-23 48.5t-40.5 32t-59.5 11.5h-124v-658h124z" />
    <glyph glyph-name="eth" unicode="&#xf0;" 
d="M186 700q40 0 69 -13.5t47.5 -36.5t27 -55.5t8.5 -69.5v-351q0 -37 -8.5 -69t-27 -55t-47 -36.5t-68.5 -13.5h-147v339h-45v19h45v342h146zM160 339h-98v-318h124q35 0 60 11.5t40.5 32t22.5 48t7 60.5v353q0 33 -7 61t-23 48.5t-40.5 32t-59.5 11.5h-124v-321h98v-19z
" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" 
d="M110 823l68 -66l67 66h25l-83 -82h-18l-82 82h23zM186 700q40 0 69 -13.5t47.5 -36.5t27 -55.5t8.5 -69.5v-351q0 -37 -8.5 -69t-27 -55t-47 -36.5t-68.5 -13.5h-147v700h146zM186 21q35 0 60 11.5t40.5 32t22.5 48t7 60.5v353q0 33 -7 61t-23 48.5t-40.5 32t-59.5 11.5
h-124v-658h124z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" 
d="M186 700q40 0 69 -13.5t47.5 -36.5t27 -55.5t8.5 -69.5v-351q0 -37 -8.5 -69t-27 -55t-47 -36.5t-68.5 -13.5h-147v339h-45v19h45v342h146zM160 339h-98v-318h124q35 0 60 11.5t40.5 32t22.5 48t7 60.5v353q0 33 -7 61t-23 48.5t-40.5 32t-59.5 11.5h-124v-321h98v-19z
" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="345" 
d="M268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="345" 
d="M243 830l-78 -89h-20l72 89h26zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="345" 
d="M264 826q-2 -38 -28.5 -58t-63.5 -20q-36 0 -63 20t-28 58h17q1 -32 23 -46.5t51 -14.5t51 14.5t24 46.5h17zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="345" 
d="M110 824l67 -66l67 66h25l-83 -82h-18l-82 82h24zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="345" 
d="M105 743h-23l82 82h18l83 -82h-25l-67 67zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="345" 
d="M145 822v-81h-20v81h20zM221 822v-81h-20v81h20zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="345" 
d="M187 824v-80h-21v80h21zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="345" 
d="M142 840l73 -89h-21l-78 89h26zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="345" 
d="M256 787v-17h-172v17h172zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="345" 
d="M268 365v-20h-206v-324h245v-21h-18q-9 -7 -18.5 -17.5t-16.5 -22.5t-11 -24.5t-3 -23.5q2 -17 13.5 -25t27.5 -8q13 0 25.5 3.5t21.5 11.5v-18q-22 -13 -52 -13q-22 1 -37.5 11.5t-17.5 34.5q-1 12 3 24.5t11 24.5t16 23t18 19h-229v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="333" 
d="M258 348v-21h-196v-327h-22v700h257v-21h-235v-331h196z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="371" 
d="M185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-46h-22v47q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49t6.5 60v159h-109v20h131
v-178q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="371" 
d="M279 829q-2 -38 -29 -58t-63 -20t-63 20t-28 58h17q1 -32 23 -46.5t51 -14.5t51 14.5t24 46.5h17zM185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-46h-22v47q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358
q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49t6.5 60v159h-109v20h131v-178q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="371" 
d="M119 751h-23l82 82h18l82 -82h-24l-67 67zM185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-46h-22v47q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5
t38.5 33.5t21 49t6.5 60v159h-109v20h131v-178q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="371" 
d="M204 826v-80h-20v80h20zM185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-46h-22v47q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49
t6.5 60v159h-109v20h131v-178q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="377" 
d="M62 340v-340h-22v700h22v-340h253v340h22v-700h-22v340h-253z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="378" 
d="M337 534v-534h-22v340h-252v-340h-22v534h-40v17h40v149h22v-149h252v149h22v-149h38v-17h-38zM315 534h-252v-174h252v174z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="377" 
d="M121 748h-24l82 82h18l83 -82h-25l-67 67zM62 340v-340h-22v700h22v-340h253v340h22v-700h-22v340h-253z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="102" 
d="M62 700v-700h-22v700h22z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="102" 
d="M62 700v-700h-22v700h22z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="102" 
d="M93 831h25l-77 -89h-21zM40 700h22v-700h-22v700z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="102" 
d="M143 820q-2 -38 -29 -58t-63 -20t-63 20t-28 58h17q1 -32 23 -46.5t51 -14.5t51 14.5t24 46.5h17zM40 700h22v-700h-22v700z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="102" 
d="M60 823l82 -82h-24l-67 67l-68 -67h-23l82 82h18zM40 700h22v-700h-22v700z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="102" 
d="M23 822v-80h-20v80h20zM99 822v-80h-20v80h20zM62 700v-700h-22v700h22z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="102" 
d="M-24 830h26l72 -89h-21zM40 700h22v-700h-22v700z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="354" 
d="M62 700v-700h-22v700h22zM315 171q0 -89 -36 -128t-103 -43h-38v21h37q64 5 91 40t27 109v530h22v-529z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="102" 
d="M124 784v-17h-146v17h146zM62 700v-700h-22v700h22z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="102" 
d="M61 0q-9 -7 -18 -17.5t-16 -22.5t-11.5 -24.5t-3.5 -23.5q2 -17 13.5 -25t27.5 -8q29 0 47 15v-18q-22 -13 -52 -13q-22 1 -37.5 11.5t-17.5 34.5q-2 23 12 47t37 44h-3l1 700h22v-700h-1z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="102" 
d="M151 796q-8 -16 -19.5 -27.5t-28.5 -12.5q-13 0 -25 5.5t-23.5 13t-23 13t-23.5 5.5q-14 0 -23.5 -9t-17.5 -22l-14 9q9 17 22.5 28t31.5 12q14 0 26.5 -5.5t24 -13t22.5 -13t22 -5.5q12 1 20.5 10.5t14.5 20.5zM62 700v-700h-22v700h22z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="252" 
d="M36 21q11 -1 21.5 -0.5t20.5 1.5q59 5 86 39.5t27 108.5v530h22v-529q0 -87 -34.5 -126.5t-98.5 -43.5q-11 -1 -21.5 -1.5t-22.5 0.5v21z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="252" 
d="M36 21q11 -1 21.5 -0.5t20.5 1.5q59 5 86 39.5t27 108.5v530h22v-529q0 -87 -34.5 -126.5t-98.5 -43.5q-11 -1 -21.5 -1.5t-22.5 0.5v21z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="252" 
d="M132 743h-24l82 82h18l83 -82h-24l-67 67zM213 171q0 -87 -34.5 -126.5t-98.5 -43.5q-11 -1 -21.5 -1.5t-22.5 0.5v21q11 -1 21.5 -0.5t20.5 1.5q59 5 86 39.5t27 108.5v530h22v-529z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="401" 
d="M115 348l-53 -80v-268h-22v700h22v-395l260 395h24l-217 -331l235 -369h-24z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="401" 
d="M115 348l-53 -80v-268h-22v700h22v-395l260 395h24l-217 -331l235 -369h-24z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="333" 
d="M40 0v700h22v-679h235v-21h-257z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="333" 
d="M126 832l-78 -89h-20l72 89h26zM40 0v700h22v-679h235v-21h-257z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="333" 
d="M40 0v700h22v-679h235v-21h-257zM217 619v81h22v-80l-22 -68h-11z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="333" 
d="M40 0v700h22v-679h235v-21h-257zM197 395v-81h-21v81h21z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="347" 
d="M76 336v-315h235v-21h-257v313l-54 -47v23l54 48v363h22v-340l87 82v-23z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="492" 
d="M258 2h-25l-173 665v-667h-20v700h34l172 -664l172 664h34v-700h-22v667z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="378" 
d="M61 0h-21v700h31l246 -655v655h21v-700h-27l-250 668v-668z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="378" 
d="M264 833l-77 -89h-21l73 89h25zM61 1h-21v700h31l246 -655v655h21v-700h-27l-250 668v-668z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="436" 
d="M23 619v81h21v-80l-21 -68h-12zM118 0h-20v700h31l246 -655v655h21v-700h-27l-251 668v-668z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="378" 
d="M123 830l68 -66l67 66h24l-82 -82h-18l-82 82h23zM61 0h-21v700h31l246 -655v655h21v-700h-27l-250 668v-668z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="378" 
d="M289 801q-8 -16 -19.5 -27t-28.5 -12q-13 -1 -25 5t-23 13t-22.5 12.5t-23.5 5.5q-14 0 -24 -9t-18 -22l-14 9q9 17 22.5 28t31.5 12q14 0 26.5 -5.5t24 -12.5t22.5 -13t22 -5q12 0 20.5 9.5t14.5 20.5zM61 0h-21v700h31l246 -655v655h21v-700h-27l-250 668v-668z" />
    <glyph glyph-name="o" unicode="o" 
d="M39 528q0 37 8 69t26 56t46 38t68 14t68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356zM61 171q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358
q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5t-59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" 
d="M254 839l-77 -89h-20l72 89h25zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358
q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" 
d="M279 829q-2 -38 -28.5 -58t-63.5 -20q-35 0 -62 20t-28 58h16q1 -32 23.5 -46.5t50.5 -14.5q29 0 51.5 14.5t24.5 46.5h16zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356
q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" 
d="M120 748h-24l82 82h18l83 -82h-25l-67 67zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5
t-6.5 -60.5v-358q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" 
d="M159 829v-81h-20v81h20zM236 829v-81h-21v81h21zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5
t-6.5 -60.5v-358q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" 
d="M146 839l72 -89h-21l-77 89h26zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358
q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" 
d="M227 839l-75 -89h-20l70 89h25zM294 839l-75 -89h-19l70 89h24zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5
t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" 
d="M274 785v-18h-170v18h170zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358
q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" 
d="M311 644q26 -44 26 -116v-356q0 -88 -37.5 -132.5t-112.5 -44.5q-73 0 -111 46l-19 -48l-16 5l23 60q-25 44 -25 114v356q0 88 37.5 132.5t110.5 44.5q74 0 112 -45l17 44l16 -5zM61 171q0 -50 14 -86l215 551q-33 49 -103 49q-126 0 -126 -156v-358zM315 529
q0 52 -15 88l-215 -552q32 -50 102 -50q128 0 128 156v358z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" 
d="M238 839h25l-77 -89h-20zM311 644q26 -44 26 -116v-356q0 -88 -37.5 -132.5t-112.5 -44.5q-73 0 -111 46l-19 -48l-16 5l23 60q-25 44 -25 114v356q0 88 37.5 132.5t110.5 44.5q74 0 112 -45l17 44l16 -5zM61 171q0 -50 14 -86l215 551q-33 49 -103 49q-126 0 -126 -156
v-358zM315 529q0 52 -15 88l-215 -552q32 -50 102 -50q128 0 128 156v358z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" 
d="M292 798q-8 -16 -19.5 -27.5t-28.5 -12.5q-13 -1 -25 5t-23.5 13t-23 13t-23.5 6q-14 0 -24 -9t-17 -22l-14 9q9 17 22.5 28t31.5 11q14 0 26 -5.5t23.5 -12.5t22.5 -13t22 -5q13 1 21.5 10.5t13.5 20.5zM187 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356
q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356q0 37 8 69t26 56t46 38t68 14zM187 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358
q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="561" 
d="M489 365v-20h-177v-324h211v-21h-346q-39 0 -65 14.5t-42.5 38.5t-23.5 55.5t-7 66.5v350q0 34 7 66t23.5 56t42.5 38.5t65 14.5h346v-21h-211v-314h177zM178 679q-33 0 -55.5 -13t-36 -34t-19.5 -48.5t-6 -57.5v-352q0 -30 6 -57.5t19.5 -48.5t36 -34t55.5 -13h112v658
h-112z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="359" 
d="M178 700q40 0 68 -12.5t45 -35t24.5 -53.5t7.5 -68v-70q0 -84 -38.5 -124t-122.5 -40h-100v-297h-22v700h138zM162 318q74 0 106.5 33.5t32.5 108.5v72q0 32 -6 59t-20.5 46.5t-38 30.5t-58.5 11h-116v-361h100z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="377" 
d="M178 562q40 0 68 -12.5t45 -35t24.5 -53.5t7.5 -68v-70q0 -84 -38.5 -124t-122.5 -40h-100v-159h-22v700h22v-138h116zM162 180q74 0 106.5 33.5t32.5 108.5v72q0 32 -6 59t-20.5 46.5t-38 30.5t-58.5 11h-116v-361h100z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="386" 
d="M188 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -39 -9 -72t-29 -57q11 -19 27 -26t37 -7h11v-20h-11q-26 0 -45.5 9t-32.5 30q-17 -16 -41.5 -25t-56.5 -9q-40 0 -68.5 14t-46.5 38t-26 56t-8 69v356q0 37 8 69t26 56t46.5 38t68.5 14zM188 685
q-35 0 -59 -12.5t-39 -33.5t-22 -49.5t-7 -60.5v-358q0 -32 7 -60.5t22 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="380" 
d="M179 700q78 0 111 -42.5t33 -114.5v-63q0 -65 -26.5 -100.5t-84.5 -46.5q59 -9 85 -45.5t26 -94.5v-110q0 -20 3.5 -43t15.5 -40h-24q-11 17 -14 38.5t-3 44.5v110q0 39 -11 64t-31.5 39.5t-48.5 20.5t-62 6h-86v-323h-22v700h139zM148 344q36 0 64.5 5.5t48 20.5
t30 40.5t10.5 66.5v64q0 64 -27.5 101t-95.5 37h-116v-335h86z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="380" 
d="M242 839l-78 -89h-20l72 89h26zM179 700q78 0 111 -42.5t33 -114.5v-63q0 -65 -26.5 -100.5t-84.5 -46.5q59 -9 85 -45.5t26 -94.5v-110q0 -20 3.5 -43t15.5 -40h-24q-11 17 -14 38.5t-3 44.5v110q0 39 -11 64t-31.5 39.5t-48.5 20.5t-62 6h-86v-323h-22v700h139z
M148 344q36 0 64.5 5.5t48 20.5t30 40.5t10.5 66.5v64q0 64 -27.5 101t-95.5 37h-116v-335h86z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="380" 
d="M103 831l68 -66l67 66h25l-83 -82h-18l-82 82h23zM179 700q78 0 111 -42.5t33 -114.5v-63q0 -65 -26.5 -100.5t-84.5 -46.5q59 -9 85 -45.5t26 -94.5v-110q0 -20 3.5 -43t15.5 -40h-24q-11 17 -14 38.5t-3 44.5v110q0 39 -11 64t-31.5 39.5t-48.5 20.5t-62 6h-86v-323
h-22v700h139zM148 344q36 0 64.5 5.5t48 20.5t30 40.5t10.5 66.5v64q0 64 -27.5 101t-95.5 37h-116v-335h86z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="369" 
d="M185 705q40 0 67.5 -13.5t44.5 -37.5t25 -55.5t8 -67.5v-13h-22v14q0 31 -6.5 59t-20.5 49t-37.5 33t-57.5 12q-35 0 -58.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -46t64.5 -47t49.5 -59t20 -80.5q0 -36 -8 -68t-25.5 -55.5t-45.5 -37
t-68 -13.5t-67.5 13.5t-45 37t-25.5 55.5t-8 68v34h21v-35q0 -32 6.5 -59.5t21.5 -48t38.5 -32.5t58.5 -12q34 0 58 12t39 32.5t21.5 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64.5 45.5t-64.5 47t-49.5 59.5t-20 81.5q0 77 34 124.5t110 48.5z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="369" 
d="M261 839l-77 -89h-21l73 89h25zM185 705q40 0 67.5 -13.5t44.5 -37.5t25 -55.5t8 -67.5v-13h-22v14q0 31 -6.5 59t-20.5 49t-37.5 33t-57.5 12q-35 0 -58.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -46t64.5 -47t49.5 -59t20 -80.5
q0 -36 -8 -68t-25.5 -55.5t-45.5 -37t-68 -13.5t-67.5 13.5t-45 37t-25.5 55.5t-8 68v34h21v-35q0 -32 6.5 -59.5t21.5 -48t38.5 -32.5t58.5 -12q34 0 58 12t39 32.5t21.5 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64.5 45.5t-64.5 47t-49.5 59.5t-20 81.5q0 77 34 124.5
t110 48.5z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="369" 
d="M120 831l68 -66l67 66h24l-83 -82h-18l-82 82h24zM185 705q40 0 67.5 -13.5t44.5 -37.5t25 -55.5t8 -67.5v-13h-22v14q0 31 -6.5 59t-20.5 49t-37.5 33t-57.5 12q-35 0 -58.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -46t64.5 -47t49.5 -59
t20 -80.5q0 -36 -8 -68t-25.5 -55.5t-45.5 -37t-68 -13.5t-67.5 13.5t-45 37t-25.5 55.5t-8 68v34h21v-35q0 -32 6.5 -59.5t21.5 -48t38.5 -32.5t58.5 -12q34 0 58 12t39 32.5t21.5 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64.5 45.5t-64.5 47t-49.5 59.5t-20 81.5
q0 77 34 124.5t110 48.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="369" 
d="M185 705q40 0 67.5 -13.5t44.5 -37.5t25 -55.5t8 -67.5v-13h-22v14q0 31 -6.5 59t-20.5 49t-37.5 33t-57.5 12q-35 0 -58.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -46t64.5 -47t49.5 -59t20 -80.5q0 -35 -7.5 -66t-24 -54.5t-42.5 -38
t-63 -15.5v-31q14 0 27.5 -3t24 -9.5t16.5 -17t6 -26.5q0 -17 -7.5 -28.5t-19.5 -18.5t-27 -10t-30 -3t-30 3.5t-27 10.5t-20 18.5t-8 27.5v4h16v-3q0 -13 7 -22t17 -14t22 -7.5t23 -2.5t23 2t21.5 7.5t16 14t6.5 21.5q0 14 -6 22.5t-15.5 13t-21 6t-23.5 1.5h-6v44
q-39 1 -65.5 15t-43.5 37.5t-24.5 54.5t-7.5 67v34h21v-35q0 -32 6.5 -59.5t21.5 -48t38.5 -32.5t58.5 -12q34 0 58 12t39 32.5t21.5 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64.5 45.5t-64.5 47t-49.5 59.5t-20 81.5q0 77 34 124.5t110 48.5z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="369" 
d="M124 748h-24l82 82h18l83 -82h-25l-67 67zM185 705q40 0 67.5 -13.5t44.5 -37.5t25 -55.5t8 -67.5v-13h-22v14q0 31 -6.5 59t-20.5 49t-37.5 33t-57.5 12q-35 0 -58.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -46t64.5 -47t49.5 -59
t20 -80.5q0 -36 -8 -68t-25.5 -55.5t-45.5 -37t-68 -13.5t-67.5 13.5t-45 37t-25.5 55.5t-8 68v34h21v-35q0 -32 6.5 -59.5t21.5 -48t38.5 -32.5t58.5 -12q34 0 58 12t39 32.5t21.5 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64.5 45.5t-64.5 47t-49.5 59.5t-20 81.5
q0 77 34 124.5t110 48.5z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="698" 
d="M181 705q39 0 66.5 -13.5t45 -37.5t25 -55.5t7.5 -67.5v-13h-21v14q0 31 -6.5 59t-20.5 49t-38 33t-58 12t-57.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64 -46t64 -47t49.5 -59t20 -80.5q0 -36 -8 -68t-25.5 -55.5t-45 -37t-67.5 -13.5
t-67.5 13.5t-45 37t-25.5 55.5t-8 68v34h21v-35q0 -32 6.5 -59.5t21 -48t38.5 -32.5t59 -12q34 0 58 12t38.5 32.5t21 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64 45.5t-64 47t-49.5 59.5t-20 81.5q0 38 8 70t25.5 54.5t45 35.5t65.5 13zM518 705q39 0 66.5 -13.5t45 -37.5
t25 -55.5t7.5 -67.5v-13h-21v14q0 31 -6.5 59t-20.5 49t-38 33t-58 12t-57.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64 -46t64 -47t49.5 -59t20 -80.5q0 -36 -8 -68t-25.5 -55.5t-45 -37t-67.5 -13.5t-67.5 13.5t-45 37t-25.5 55.5t-8 68v34h21
v-35q0 -32 6.5 -59.5t21 -48t38.5 -32.5t59 -12q34 0 58 12t38.5 32.5t21 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64 45.5t-64 47t-49.5 59.5t-20 81.5q0 38 8 70t25.5 54.5t45 35.5t65.5 13z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="367" 
d="M171 0v679h-152v21h327v-21h-153v-679h-22z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="400" 
d="M291 457v-18h-80v-439h-22v439h-80v18h80v222h-153v21h328v-21h-153v-222h80z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="400" 
d="M132 825l68 -66l67 66h24l-82 -82h-19l-81 82h23zM189 0v679h-153v21h328v-21h-153v-679h-22z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="367" 
d="M61 172q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="367" 
d="M259 839l-77 -89h-21l73 89h25zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="367" 
d="M277 820q-2 -38 -28.5 -58t-63.5 -20q-36 0 -63 20t-28 58h17q1 -32 23 -46.5t51 -14.5t51 14.5t24 46.5h17zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5
t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="367" 
d="M117 748h-23l82 82h18l83 -82h-25l-67 67zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="367" 
d="M155 826v-80h-20v80h20zM232 826v-80h-21v80h21zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="367" 
d="M141 831l72 -89h-21l-77 89h26zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="367" 
d="M222 837l-75 -89h-20l70 89h25zM289 837l-75 -89h-19l70 89h24zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21
v-527z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="367" 
d="M269 784v-17h-163v17h163zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="371" 
d="M185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-46h-22v47q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49t6.5 60v159h-109v20h131
v-178q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14zM175 -114v80h20v-78l-21 -63h-11z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="401" 
d="M115 348l-53 -80v-268h-22v700h22v-395l260 395h24l-217 -331l235 -369h-24zM178 -114v80h20v-78l-21 -63h-11z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="333" 
d="M40 0v700h22v-679h235v-21h-257zM174 -114v80h20v-78l-21 -63h-11z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" horiz-adv-x="378" 
d="M61 0h-21v700h31l246 -655v655h21v-700h-27l-250 668v-668zM178 -114v80h19v-78l-21 -63h-10z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="380" 
d="M179 700q78 0 111 -42.5t33 -114.5v-63q0 -65 -26.5 -100.5t-84.5 -46.5q59 -9 85 -45.5t26 -94.5v-110q0 -20 3.5 -43t15.5 -40h-24q-11 17 -14 38.5t-3 44.5v110q0 39 -11 64t-31.5 39.5t-48.5 20.5t-62 6h-86v-323h-22v700h139zM148 344q36 0 64.5 5.5t48 20.5
t30 40.5t10.5 66.5v64q0 64 -27.5 101t-95.5 37h-116v-335h86zM176 -114v80h20v-78l-21 -63h-11z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="400" 
d="M208 0v-34q29 -1 51 -12.5t22 -43.5q0 -17 -8 -29t-20 -19.5t-26.5 -11t-28.5 -3.5t-29 3.5t-27 11t-20 19.5t-8 28v4h15v-4q0 -13 6.5 -21.5t17 -14t22.5 -8t23 -2.5q10 0 22 2.5t21.5 8t16 14.5t6.5 22q0 14 -6.5 22.5t-16 13t-21.5 6t-24 1.5h-5v47h-2v679h-153v21
h328v-21h-153v-679h-3z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="369" 
d="M185 705q40 0 67.5 -13.5t44.5 -37.5t25 -55.5t8 -67.5v-13h-22v14q0 31 -6.5 59t-20.5 49t-37.5 33t-57.5 12q-35 0 -58.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -46t64.5 -47t49.5 -59t20 -80.5q0 -36 -8 -68t-25.5 -55.5t-45.5 -37
t-68 -13.5t-67.5 13.5t-45 37t-25.5 55.5t-8 68v34h21v-35q0 -32 6.5 -59.5t21.5 -48t38.5 -32.5t58.5 -12q34 0 58 12t39 32.5t21.5 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64.5 45.5t-64.5 47t-49.5 59.5t-20 81.5q0 77 34 124.5t110 48.5zM174 -114v80h20v-78l-21 -63h-11
z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="400" 
d="M189 0v679h-153v21h328v-21h-153v-679h-22zM190 -114v80h20v-78l-21 -63h-11z" />
    <glyph glyph-name="uni1E03" unicode="&#x1e03;" horiz-adv-x="367" 
d="M174 824v-80h-20v80h20zM180 700q76 0 105.5 -37.5t29.5 -107.5v-45q0 -59 -20.5 -93.5t-79.5 -43.5q62 -9 88 -49t26 -101v-62q0 -74 -34.5 -117.5t-113.5 -43.5h-141v700h140zM153 382q36 0 62.5 5t44 19t25.5 38.5t8 62.5v47q0 62 -23.5 93.5t-89.5 31.5h-118v-297h91
zM181 21q69 0 97.5 37t28.5 103v63q0 76 -36 107t-108 31h-101v-341h119z" />
    <glyph glyph-name="uni1E0B" unicode="&#x1e0b;" 
d="M180 824v-80h-21v80h21zM186 700q40 0 69 -13.5t47.5 -36.5t27 -55.5t8.5 -69.5v-351q0 -37 -8.5 -69t-27 -55t-47 -36.5t-68.5 -13.5h-147v700h146zM186 21q35 0 60 11.5t40.5 32t22.5 48t7 60.5v353q0 33 -7 61t-23 48.5t-40.5 32t-59.5 11.5h-124v-658h124z" />
    <glyph glyph-name="uni1E1F" unicode="&#x1e1f;" horiz-adv-x="333" 
d="M187 824v-80h-20v80h20zM258 348v-21h-196v-327h-22v700h257v-21h-235v-331h196z" />
    <glyph glyph-name="uni1E41" unicode="&#x1e41;" horiz-adv-x="492" 
d="M256 831v-80h-21v80h21zM258 2h-25l-173 665v-667h-20v700h34l172 -664l172 664h34v-700h-22v667z" />
    <glyph glyph-name="uni1E57" unicode="&#x1e57;" horiz-adv-x="359" 
d="M181 824v-80h-21v80h21zM178 700q40 0 68 -12.5t45 -35t24.5 -53.5t7.5 -68v-70q0 -84 -38.5 -124t-122.5 -40h-100v-297h-22v700h138zM162 318q74 0 106.5 33.5t32.5 108.5v72q0 32 -6 59t-20.5 46.5t-38 30.5t-58.5 11h-116v-361h100z" />
    <glyph glyph-name="uni1E61" unicode="&#x1e61;" horiz-adv-x="369" 
d="M202 833v-80h-20v80h20zM185 705q40 0 67.5 -13.5t44.5 -37.5t25 -55.5t8 -67.5v-13h-22v14q0 31 -6.5 59t-20.5 49t-37.5 33t-57.5 12q-35 0 -58.5 -12t-37.5 -33t-20.5 -48.5t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -46t64.5 -47t49.5 -59t20 -80.5q0 -36 -8 -68
t-25.5 -55.5t-45.5 -37t-68 -13.5t-67.5 13.5t-45 37t-25.5 55.5t-8 68v34h21v-35q0 -32 6.5 -59.5t21.5 -48t38.5 -32.5t58.5 -12q34 0 58 12t39 32.5t21.5 48t6.5 59.5q0 44 -20 75t-49.5 55.5t-64.5 45.5t-64.5 47t-49.5 59.5t-20 81.5q0 77 34 124.5t110 48.5z" />
    <glyph glyph-name="uni1E6B" unicode="&#x1e6b;" horiz-adv-x="400" 
d="M207 824v-80h-21v80h21zM185 0v679h-152v21h327v-21h-153v-679h-22z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="367" 
d="M328 173q0 -59 -21 -107t-75 -65q-9 -7 -18 -18t-16 -23t-11 -24.5t-3 -23.5q2 -17 13.5 -25t27.5 -8q29 0 47 15v-18q-22 -13 -51 -13q-23 1 -38.5 11.5t-17.5 34.5q-1 23 13 47t32 41q-6 -1 -12.5 -1.5t-13.5 -0.5q-40 0 -67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527
h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="367" 
d="M189 859q26 0 44.5 -17.5t18.5 -43.5t-18.5 -43.5t-44.5 -17.5t-44 17.5t-18 43.5t18 43.5t44 17.5zM189 844q-20 0 -33.5 -13t-13.5 -33t13.5 -33.5t33.5 -13.5t34 13.5t14 33.5t-14 33t-34 13zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5
t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="367" 
d="M284 798q-8 -16 -19.5 -27.5t-28.5 -12.5q-13 -1 -25 5t-23 13t-22.5 13t-23.5 6q-14 0 -24 -9t-18 -22l-14 9q9 17 22.5 28t31.5 11q14 0 26.5 -5.5t24 -12.5t22.5 -13t22 -5q12 1 20.5 10.5t14.5 20.5zM328 173q0 -36 -7.5 -68.5t-24.5 -56.5t-44.5 -38.5t-67.5 -14.5
t-67.5 14.5t-45 38.5t-25 56.5t-7.5 68.5v527h22v-528q0 -32 6 -60t20.5 -50t38 -34.5t58.5 -12.5t58.5 12.5t38 34.5t20.5 50t6 60v528h21v-527z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="446" 
d="M60 700l164 -675l164 675h21l-168 -700h-33l-171 700h23z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="618" 
d="M195 0h-32l-126 700h22l122 -670l121 666h27l114 -662l118 666h20l-124 -700h-29l-113 648z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="618" 
d="M376 831l-77 -89h-21l73 89h25zM195 0h-32l-126 700h22l122 -670l121 666h27l114 -662l118 666h20l-124 -700h-29l-113 648z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="618" 
d="M245 746h-24l82 82h18l83 -82h-24l-67 66zM195 0h-32l-126 700h22l122 -670l121 666h27l114 -662l118 666h20l-124 -700h-29l-113 648z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="618" 
d="M286 829v-81h-20v81h20zM363 829v-81h-21v81h21zM195 0h-32l-126 700h22l122 -670l121 666h27l114 -662l118 666h20l-124 -700h-29l-113 648z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="618" 
d="M281 839l72 -89h-20l-78 89h26zM195 0h-32l-126 700h22l122 -670l121 666h27l114 -662l118 666h20l-124 -700h-29l-113 648z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="444" 
d="M221 344l-161 -344h-23l171 364l-159 336h23l151 -317l151 317h22l-159 -336l170 -364h-24z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="422" 
d="M199 283l-163 417h22l153 -387l153 387h23l-166 -417v-283h-22v283z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="422" 
d="M285 830l-78 -89h-20l73 89h25zM199 283l-163 417h22l153 -387l153 387h23l-166 -417v-283h-22v283z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="422" 
d="M144 745h-24l82 82h18l83 -82h-24l-67 66zM199 283l-163 417h22l153 -387l153 387h23l-166 -417v-283h-22v283z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="422" 
d="M183 822v-80h-21v80h21zM259 822v-80h-20v80h20zM199 283l-163 417h22l153 -387l153 387h23l-166 -417v-283h-22v283z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="422" 
d="M169 839l73 -89h-21l-77 89h25zM199 283l-163 417h22l153 -387l153 387h23l-166 -417v-283h-22v283z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="395" 
d="M351 0h-313v21l294 658h-280v21h304v-22l-295 -657h290v-21z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="395" 
d="M285 839l-78 -89h-20l72 89h26zM351 0h-313v21l294 658h-280v21h304v-22l-295 -657h290v-21z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="395" 
d="M138 830l68 -66l67 66h25l-83 -82h-18l-82 82h23zM351 0h-313v21l294 658h-280v21h304v-22l-295 -657h290v-21z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="395" 
d="M225 829v-80h-20v80h20zM351 0h-313v21l294 658h-280v21h304v-22l-295 -657h290v-21z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="271" 
d="M146 705q25 0 41.5 -8.5t26 -23t13.5 -34.5t4 -42v-260h-15l-1 67q-11 -32 -33 -52t-57 -20q-44 0 -65 25.5t-21 68.5v3q0 36 16.5 58t42.5 34t56.5 16.5t59.5 4.5v56q0 19 -3 35.5t-10.5 29t-20.5 19.5t-34 7q-43 0 -59.5 -26t-16.5 -66v-9h-16v9q0 48 20.5 78t71.5 30z
M214 527q-26 0 -53.5 -4t-51 -14t-38.5 -29t-15 -49v-4q0 -36 17.5 -57.5t54.5 -21.5q21 0 36 8.5t25.5 23t16.5 32.5t8 36v79zM231 261v-17h-190v17h190z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="271" 
d="M136 705q26 0 44 -9t29.5 -24.5t17 -36t5.5 -44.5v-149q0 -24 -5.5 -44.5t-17 -36t-29.5 -24.5t-44 -9t-44.5 9t-30 24.5t-17 36t-5.5 44.5v149q0 24 5.5 44.5t17 36t30 24.5t44.5 9zM136 689q-44 0 -62 -28.5t-18 -68.5v-151q0 -40 18 -68.5t62 -28.5q43 0 61 28.5
t18 68.5v151q0 40 -18 68.5t-61 28.5zM232 262v-17h-191v17h191z" />
    <glyph glyph-name="uni0410" unicode="&#x410;" horiz-adv-x="447" 
d="M98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="uni0411" unicode="&#x411;" 
d="M175 395q81 0 121.5 -33t40.5 -117v-81q0 -78 -35 -121t-116 -43h-146v700h273v-21h-251v-284h113zM186 21q71 0 100 37t29 105v83q0 75 -35 101.5t-105 26.5h-113v-353h124z" />
    <glyph glyph-name="uni0412" unicode="&#x412;" horiz-adv-x="367" 
d="M180 700q76 0 105.5 -37.5t29.5 -107.5v-45q0 -29 -4 -52.5t-15 -41t-29.5 -29t-48.5 -15.5q62 -9 86.5 -48.5t24.5 -100.5v-62q0 -74 -34.5 -117.5t-113.5 -43.5h-141v700h140zM153 382q36 0 62.5 5t44 19t25.5 38.5t8 62.5v47q0 62 -23.5 93.5t-89.5 31.5h-118v-297h91
zM181 21q69 0 97.5 37t28.5 103v63q0 76 -36 107t-108 31h-101v-341h119z" />
    <glyph glyph-name="uni0413" unicode="&#x413;" horiz-adv-x="339" 
d="M62 679v-679h-22v700h260v-21h-238z" />
    <glyph glyph-name="uni0414" unicode="&#x414;" horiz-adv-x="490" 
d="M430 -98v98h-371v-98h-22v119h50q23 23 29 54t8 66l29 559h237v-679h62v-119h-22zM146 140q-2 -35 -7 -65t-27 -54h256v658h-194z" />
    <glyph glyph-name="uni0415" unicode="&#x415;" horiz-adv-x="345" 
d="M268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="uni0401" unicode="&#x401;" horiz-adv-x="345" 
d="M146 829v-81h-20v81h20zM222 829v-81h-20v81h20zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="uni0416" unicode="&#x416;" horiz-adv-x="630" 
d="M380 365l-55 -89v-276h-22v276l-53 89l-191 -365h-23l200 385l-189 315h23l190 -315l43 -74v389h22v-389l44 76l190 313h24l-189 -315l199 -385h-23z" />
    <glyph glyph-name="uni0417" unicode="&#x417;" horiz-adv-x="371" 
d="M186 705q40 0 68 -14t45 -38t25 -56t8 -69v-18q0 -51 -27 -90.5t-75 -53.5q51 -12 76.5 -50.5t25.5 -89.5v-54q0 -37 -8 -69t-25 -56t-45 -38t-68 -14t-68 14t-45.5 38t-25.5 56t-8 69v51h22v-52q0 -32 6.5 -60.5t21 -49.5t38 -33.5t58.5 -12.5t59 12.5t38.5 34t21 50
t6.5 60.5v54q0 56 -32.5 92.5t-88.5 36.5h-45v20h46q26 0 48 11t38 29t25 42t9 50v21q0 33 -6.5 61.5t-21 49.5t-38.5 33.5t-59 12.5t-59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-30h-20v30q0 36 8 68t25 56t45 38t68 14z" />
    <glyph glyph-name="uni0418" unicode="&#x418;" horiz-adv-x="378" 
d="M190 332l-123 -332h-27v700h21v-658l247 658h30v-700h-21v666z" />
    <glyph glyph-name="uni0419" unicode="&#x419;" horiz-adv-x="378" 
d="M291 823q-2 -35 -29.5 -55t-65.5 -20t-66 19.5t-29 55.5h18q1 -30 24 -44t53 -14q29 0 52 14t25 44h18zM197 349l-130 -349h-27v700h21v-658l247 658h30v-700h-21v666z" />
    <glyph glyph-name="uni041A" unicode="&#x41a;" horiz-adv-x="401" 
d="M115 348l-53 -80v-268h-22v700h22v-395l260 395h24l-217 -331l235 -369h-24z" />
    <glyph glyph-name="uni041B" unicode="&#x41b;" horiz-adv-x="434" 
d="M172 679l-20 -510q-2 -31 -5 -61t-13.5 -54t-32 -39t-58.5 -16h-7v21q34 0 52.5 13.5t27.5 34.5t11 47.5t3 53.5l21 531h243v-700h-23v679h-199z" />
    <glyph glyph-name="uni041C" unicode="&#x41c;" horiz-adv-x="492" 
d="M258 2h-25l-173 665v-667h-20v700h34l172 -664l172 664h34v-700h-22v667z" />
    <glyph glyph-name="uni041D" unicode="&#x41d;" horiz-adv-x="377" 
d="M62 340v-340h-22v700h22v-340h253v340h22v-700h-22v340h-253z" />
    <glyph glyph-name="uni041E" unicode="&#x41e;" 
d="M39 528q0 37 8 69t26 56t46 38t68 14t68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356zM61 171q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358
q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5t-59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358z" />
    <glyph glyph-name="uni041F" unicode="&#x41f;" horiz-adv-x="377" 
d="M62 679v-679h-22v700h297v-700h-22v679h-253z" />
    <glyph glyph-name="uni0420" unicode="&#x420;" horiz-adv-x="359" 
d="M178 700q40 0 68 -12.5t45 -35t24.5 -53.5t7.5 -68v-70q0 -84 -38.5 -124t-122.5 -40h-100v-297h-22v700h138zM162 318q74 0 106.5 33.5t32.5 108.5v72q0 32 -6 59t-20.5 46.5t-38 30.5t-58.5 11h-116v-361h100z" />
    <glyph glyph-name="uni0421" unicode="&#x421;" horiz-adv-x="370" 
d="M185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-54h-22v55q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49t6.5 60v77h22v-76
q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="uni0422" unicode="&#x422;" horiz-adv-x="400" 
d="M189 0v679h-153v21h328v-21h-153v-679h-22z" />
    <glyph glyph-name="uni0423" unicode="&#x423;" horiz-adv-x="433" 
d="M192 112q-8 -26 -17.5 -47t-24 -35.5t-35 -22t-51.5 -7.5v20q25 0 41.5 6.5t28.5 18.5t20 29t16 38l40 114l-174 474h23l162 -445l67 197l85 248h24z" />
    <glyph glyph-name="uni0424" unicode="&#x424;" horiz-adv-x="546" 
d="M346 620q87 0 124 -47.5t37 -129.5v-191q0 -83 -37 -130t-124 -47h-63v-75h-21v75h-63q-44 0 -74.5 12t-49.5 35t-27.5 56t-8.5 74v191q0 41 8.5 74t27.5 56t49.5 35t74.5 12h63v80h21v-80h63zM199 600q-38 0 -64.5 -10.5t-43 -31t-23.5 -49.5t-7 -65v-193q0 -36 7 -65
t23.5 -49.5t43 -31t64.5 -10.5h63v505h-63zM346 95q76 0 107.5 41.5t31.5 114.5v193q0 73 -31 114.5t-108 41.5h-63v-505h63z" />
    <glyph glyph-name="uni0425" unicode="&#x425;" horiz-adv-x="444" 
d="M221 344l-161 -344h-23l171 364l-159 336h23l151 -317l151 317h22l-159 -336l170 -364h-24z" />
    <glyph glyph-name="uni0427" unicode="&#x427;" horiz-adv-x="377" 
d="M315 357q-41 -81 -134 -81q-38 0 -65 14.5t-44 38.5t-25 55.5t-8 66.5v249h22v-248q0 -31 6.5 -59t21.5 -49.5t38.5 -34.5t57.5 -13q51 0 82.5 25t47.5 73v306h22v-700h-22v357z" />
    <glyph glyph-name="uni0426" unicode="&#x426;" horiz-adv-x="455" 
d="M394 -94v94h-336v700h21v-679h252v679h23v-679h62v-115h-22z" />
    <glyph glyph-name="uni0428" unicode="&#x428;" horiz-adv-x="550" 
d="M62 700v-679h202v679h22v-679h202v679h22v-700h-470v700h22z" />
    <glyph glyph-name="uni0429" unicode="&#x429;" horiz-adv-x="610" 
d="M550 -94v94h-510v700h22v-679h201v679h22v-679h203l-1 679h22v-679h63v-115h-22z" />
    <glyph glyph-name="uni042F" unicode="&#x42f;" horiz-adv-x="380" 
d="M232 323q-34 0 -62 -6t-48.5 -20.5t-31.5 -39.5t-11 -64v-110q0 -23 -3 -44.5t-14 -38.5h-24q12 17 15.5 40t3.5 43v110q0 58 26 94.5t85 45.5q-58 11 -84.5 46.5t-26.5 100.5v63q0 72 33 114.5t111 42.5h139v-700h-22v323h-86zM202 679q-68 0 -95.5 -37t-27.5 -101v-64
q0 -41 10.5 -66.5t30 -40.5t48 -20.5t64.5 -5.5h86v335h-116z" />
    <glyph glyph-name="uni042C" unicode="&#x42c;" horiz-adv-x="361" 
d="M161 403q85 0 123.5 -40t38.5 -124v-70q0 -37 -7.5 -68t-24.5 -53.5t-45 -35t-69 -12.5h-137v700h22v-297h99zM177 21q35 0 59 11t38.5 30.5t20.5 46.5t6 59v72q0 75 -33 108.5t-107 33.5h-99v-361h115z" />
    <glyph glyph-name="uni042A" unicode="&#x42a;" horiz-adv-x="432" 
d="M233 403q84 0 122.5 -40t38.5 -124v-70q0 -37 -7.5 -68t-24.5 -53.5t-45 -35t-68 -12.5h-138v679h-101v21h123v-297h100zM249 21q35 0 58.5 11t38 30.5t20.5 46.5t6 59v72q0 75 -33 108.5t-106 33.5h-100v-361h116z" />
    <glyph glyph-name="uni042B" unicode="&#x42b;" horiz-adv-x="452" 
d="M161 403q85 0 123.5 -40t38.5 -124v-70q0 -37 -7.5 -68t-24.5 -53.5t-45 -35t-69 -12.5h-137v700h22v-297h99zM412 700v-700h-22v700h22zM177 21q35 0 59 11t38.5 30.5t20.5 46.5t6 59v72q0 75 -33 108.5t-107 33.5h-99v-361h115z" />
    <glyph glyph-name="uni042D" unicode="&#x42d;" horiz-adv-x="369" 
d="M184 705q40 0 67.5 -14t45 -38t25.5 -56.5t8 -68.5v-356q0 -36 -8 -68.5t-25.5 -56.5t-45 -38t-67.5 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v63h22v-64q0 -32 6.5 -60t20.5 -49t37.5 -33.5t58.5 -12.5t59 12.5t38.5 33.5t21 49t6.5 60v181h-158v20h158v157
q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-59 12.5t-58.5 -12.5t-37.5 -33.5t-20.5 -49.5t-6.5 -60.5v-40h-21v39q0 36 7.5 68.5t25 56.5t45 38t67.5 14z" />
    <glyph glyph-name="uni042E" unicode="&#x42e;" horiz-adv-x="535" 
d="M346 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v168h-136v-340h-22v700h22v-340h136v168q0 37 8 69t26 56t46 38t68 14zM346 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5
v-358q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="uni0430" unicode="&#x430;" horiz-adv-x="447" 
d="M98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="uni0431" unicode="&#x431;" 
d="M175 395q81 0 121.5 -33t40.5 -117v-81q0 -78 -35 -121t-116 -43h-146v700h273v-21h-251v-284h113zM186 21q71 0 100 37t29 105v83q0 75 -35 101.5t-105 26.5h-113v-353h124z" />
    <glyph glyph-name="uni0432" unicode="&#x432;" horiz-adv-x="367" 
d="M180 700q76 0 105.5 -37.5t29.5 -107.5v-45q0 -59 -20.5 -93.5t-79.5 -43.5q62 -9 88 -49t26 -101v-62q0 -74 -34.5 -117.5t-113.5 -43.5h-141v700h140zM153 382q36 0 62.5 5t44 19t25.5 38.5t8 62.5v47q0 62 -23.5 93.5t-89.5 31.5h-118v-297h91zM181 21q69 0 97.5 37
t28.5 103v63q0 76 -36 107t-108 31h-101v-341h119z" />
    <glyph glyph-name="uni0433" unicode="&#x433;" horiz-adv-x="339" 
d="M62 679v-679h-22v700h260v-21h-238z" />
    <glyph glyph-name="uni0434" unicode="&#x434;" horiz-adv-x="490" 
d="M430 -98v98h-371v-98h-22v119h50q23 23 29 54t8 66l29 559h237v-679h62v-119h-22zM146 140q-2 -35 -7 -65t-27 -54h256v658h-194z" />
    <glyph glyph-name="uni0435" unicode="&#x435;" horiz-adv-x="345" 
d="M268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="uni0451" unicode="&#x451;" horiz-adv-x="345" 
d="M146 829v-81h-20v81h20zM222 829v-81h-20v81h20zM268 365v-20h-206v-324h245v-21h-267v700h267v-21h-245v-314h206z" />
    <glyph glyph-name="uni0436" unicode="&#x436;" horiz-adv-x="630" 
d="M380 365l-55 -90v-275h-22v276l-53 89l-191 -365h-23l200 385l-189 315h23l190 -316l43 -75v391h22v-391l42 74l192 317h24l-189 -315l199 -385h-23z" />
    <glyph glyph-name="uni0437" unicode="&#x437;" horiz-adv-x="371" 
d="M186 705q40 0 68 -14t45 -38t25 -56t8 -69v-18q0 -51 -27 -90.5t-75 -53.5q51 -12 76.5 -50.5t25.5 -89.5v-54q0 -37 -8 -69t-25 -56t-45 -38t-68 -14t-68 14t-45.5 38t-25.5 56t-8 69v51h22v-52q0 -32 6.5 -60.5t21 -49.5t38 -33.5t58.5 -12.5t59 12.5t38.5 34t21 50
t6.5 60.5v54q0 56 -32.5 92.5t-88.5 36.5h-45v20h46q26 0 48 11t38 29t25 42t9 50v21q0 33 -6.5 61.5t-21 49.5t-38.5 33.5t-59 12.5t-59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-30h-20v30q0 36 8 68t25 56t45 38t68 14z" />
    <glyph glyph-name="uni0438" unicode="&#x438;" horiz-adv-x="378" 
d="M189 326l-122 -326h-27v700h21v-658l97 260l150 398h30v-700h-21v666z" />
    <glyph glyph-name="uni0439" unicode="&#x439;" horiz-adv-x="378" 
d="M291 820q-2 -36 -29.5 -56t-65.5 -20t-66 19.5t-29 56.5h18q1 -31 24 -44.5t53 -13.5q29 0 52 13.5t25 44.5h18zM204 364l-137 -364h-27v700h21v-658l106 285l141 373h30v-700h-21v666z" />
    <glyph glyph-name="uni043A" unicode="&#x43a;" horiz-adv-x="401" 
d="M115 348l-53 -80v-268h-22v700h22v-395l260 395h24l-217 -331l235 -369h-24z" />
    <glyph glyph-name="uni043B" unicode="&#x43b;" horiz-adv-x="434" 
d="M172 679l-20 -510q-2 -31 -5 -61t-13.5 -54t-32 -39t-58.5 -16h-7v21q34 0 52.5 13.5t27.5 34.5t11 47.5t3 53.5l21 531h243v-700h-23v679h-199z" />
    <glyph glyph-name="uni043C" unicode="&#x43c;" horiz-adv-x="492" 
d="M258 2h-25l-173 665v-667h-20v700h34l172 -664l172 664h34v-700h-22v667z" />
    <glyph glyph-name="uni043D" unicode="&#x43d;" horiz-adv-x="377" 
d="M62 340v-340h-22v700h22v-340h253v340h22v-700h-22v340h-253z" />
    <glyph glyph-name="uni043E" unicode="&#x43e;" 
d="M39 528q0 37 8 69t26 56t46 38t68 14t68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v356zM61 171q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358
q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5t-59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5v-358z" />
    <glyph glyph-name="uni043F" unicode="&#x43f;" horiz-adv-x="377" 
d="M62 679v-679h-22v700h297v-700h-22v679h-253z" />
    <glyph glyph-name="uni0440" unicode="&#x440;" horiz-adv-x="359" 
d="M178 700q40 0 68 -12.5t45 -35t24.5 -53.5t7.5 -68v-70q0 -84 -38.5 -124t-122.5 -40h-100v-297h-22v700h138zM162 318q74 0 106.5 33.5t32.5 108.5v72q0 32 -6 59t-20.5 46.5t-38 30.5t-58.5 11h-116v-361h100z" />
    <glyph glyph-name="uni0441" unicode="&#x441;" horiz-adv-x="370" 
d="M185 705q40 0 68 -14t45.5 -38t25.5 -56.5t8 -68.5v-54h-22v55q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-58 12.5q-35 0 -59 -12.5t-38.5 -33.5t-21 -49.5t-6.5 -60.5v-358q0 -32 6.5 -60t21 -49t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49t6.5 60v77h22v-76
q0 -36 -8 -68.5t-25.5 -56.5t-45.5 -38t-68 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v356q0 36 8 68.5t25.5 56.5t45 38t67.5 14z" />
    <glyph glyph-name="uni0442" unicode="&#x442;" horiz-adv-x="400" 
d="M189 0v679h-153v21h328v-21h-153v-679h-22z" />
    <glyph glyph-name="uni0443" unicode="&#x443;" horiz-adv-x="433" 
d="M192 112q-8 -26 -17.5 -47t-24 -35.5t-35 -22t-51.5 -7.5v20q25 0 41.5 6.5t28.5 18.5t20 29t16 38l40 114l-174 474h23l162 -445l67 197l85 248h24z" />
    <glyph glyph-name="uni0444" unicode="&#x444;" horiz-adv-x="546" 
d="M346 620q87 0 124 -47.5t37 -129.5v-191q0 -83 -37 -130t-124 -47h-63v-75h-21v75h-63q-44 0 -74.5 12t-49.5 35t-27.5 56t-8.5 74v191q0 41 8.5 74t27.5 56t49.5 35t74.5 12h63v80h21v-80h63zM199 600q-38 0 -64.5 -10.5t-43 -31t-23.5 -49.5t-7 -65v-193q0 -36 7 -65
t23.5 -49.5t43 -31t64.5 -10.5h63v505h-63zM346 95q76 0 107.5 41.5t31.5 114.5v193q0 73 -31 114.5t-108 41.5h-63v-505h63z" />
    <glyph glyph-name="uni0445" unicode="&#x445;" horiz-adv-x="444" 
d="M221 344l-161 -344h-23l171 364l-159 336h23l151 -317l151 317h22l-159 -336l170 -364h-24z" />
    <glyph glyph-name="uni0447" unicode="&#x447;" horiz-adv-x="377" 
d="M315 355q-40 -79 -134 -79q-38 0 -65 14.5t-44 38.5t-25 55.5t-8 66.5v249h22v-248q0 -31 6.5 -59t21.5 -49.5t38.5 -34.5t57.5 -13q51 0 82.5 25t47.5 73v306h22v-700h-22v355z" />
    <glyph glyph-name="uni0446" unicode="&#x446;" horiz-adv-x="455" 
d="M394 -94v94h-336v700h21v-679h252v679h23v-679h62v-115h-22z" />
    <glyph glyph-name="uni0448" unicode="&#x448;" horiz-adv-x="550" 
d="M62 700v-679h202v679h22v-679h202v679h22v-700h-470v700h22z" />
    <glyph glyph-name="uni0449" unicode="&#x449;" horiz-adv-x="610" 
d="M550 -94v94h-510v700h22v-679h201v679h22v-679h203l-1 679h22v-679h63v-115h-22z" />
    <glyph glyph-name="uni044C" unicode="&#x44c;" horiz-adv-x="361" 
d="M161 403q85 0 123.5 -40t38.5 -124v-70q0 -37 -7.5 -68t-24.5 -53.5t-45 -35t-69 -12.5h-137v700h22v-297h99zM177 21q35 0 59 11t38.5 30.5t20.5 46.5t6 59v72q0 75 -33 108.5t-107 33.5h-99v-361h115z" />
    <glyph glyph-name="uni044A" unicode="&#x44a;" horiz-adv-x="432" 
d="M233 403q84 0 122.5 -40t38.5 -124v-70q0 -37 -7.5 -68t-24.5 -53.5t-45 -35t-68 -12.5h-138v679h-101v21h123v-297h100zM249 21q35 0 58.5 11t38 30.5t20.5 46.5t6 59v72q0 75 -33 108.5t-106 33.5h-100v-361h116z" />
    <glyph glyph-name="uni044B" unicode="&#x44b;" horiz-adv-x="452" 
d="M161 403q85 0 123.5 -40t38.5 -124v-70q0 -37 -7.5 -68t-24.5 -53.5t-45 -35t-69 -12.5h-137v700h22v-297h99zM412 700v-700h-22v700h22zM177 21q35 0 59 11t38.5 30.5t20.5 46.5t6 59v72q0 75 -33 108.5t-107 33.5h-99v-361h115z" />
    <glyph glyph-name="uni044D" unicode="&#x44d;" horiz-adv-x="369" 
d="M184 705q40 0 67.5 -14t45 -38t25.5 -56.5t8 -68.5v-356q0 -36 -8 -68.5t-25.5 -56.5t-45 -38t-67.5 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v63h22v-64q0 -32 6.5 -60t20.5 -49t37.5 -33.5t58.5 -12.5t59 12.5t38.5 33.5t21 49t6.5 60v181h-158v20h158v157
q0 32 -6.5 60.5t-21 49.5t-38.5 33.5t-59 12.5t-58.5 -12.5t-37.5 -33.5t-20.5 -49.5t-6.5 -60.5v-40h-21v39q0 36 7.5 68.5t25 56.5t45 38t67.5 14z" />
    <glyph glyph-name="uni044E" unicode="&#x44e;" horiz-adv-x="535" 
d="M346 705q40 0 68.5 -14t46.5 -38t26.5 -56t8.5 -69v-356q0 -37 -8.5 -69t-26.5 -56t-46.5 -38t-68.5 -14t-68 14t-46 38t-26 56t-8 69v168h-136v-340h-22v700h22v-340h136v168q0 37 8 69t26 56t46 38t68 14zM346 685q-35 0 -59 -12.5t-39 -33.5t-21.5 -49.5t-6.5 -60.5
v-358q0 -32 6.5 -60.5t21.5 -49.5t39 -33.5t59 -12.5t59.5 12.5t39.5 33.5t22 49.5t7 60.5v358q0 32 -7 60.5t-22 49.5t-39.5 33.5t-59.5 12.5z" />
    <glyph glyph-name="uni044F" unicode="&#x44f;" horiz-adv-x="380" 
d="M232 323q-34 0 -62 -6t-48.5 -20.5t-31.5 -39.5t-11 -64v-110q0 -23 -3 -44.5t-14 -38.5h-24q12 17 15.5 40t3.5 43v110q0 58 26 94.5t85 45.5q-58 11 -84.5 46.5t-26.5 100.5v63q0 72 33 114.5t111 42.5h139v-700h-22v323h-86zM202 679q-68 0 -95.5 -37t-27.5 -101v-64
q0 -41 10.5 -66.5t30 -40.5t48 -20.5t64.5 -5.5h86v335h-116z" />
    <glyph glyph-name="zero" unicode="0" 
d="M39 528q0 37 8 69.5t26 56.5t46.5 37.5t68.5 13.5t68.5 -13.5t46.5 -37.5t26 -56.5t8 -69.5v-356q0 -37 -8 -69t-26 -56t-46.5 -38t-68.5 -14t-68.5 14t-46.5 38t-26 56t-8 69v356zM61 172q0 -33 7 -61.5t22 -49.5t39 -33.5t59 -12.5t59 12.5t39 33.5t22 49.5t7 61.5v357
q0 32 -7 60.5t-22 49.5t-39 33.5t-59 12.5t-59 -12.5t-39 -33.5t-22 -49.5t-7 -60.5v-357z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="238" 
d="M165 658q-34 -69 -117 -78v20q47 5 75.5 32.5t42.5 68.5h21v-701h-22v658z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="370" 
d="M186 705q41 0 69 -14t45 -38.5t24.5 -57t7.5 -69.5q0 -49 -19.5 -88t-49.5 -75t-64.5 -70.5t-64.5 -74t-50.5 -86.5t-21.5 -107v-4h266v-21h-288v15q0 65 20 115.5t50 91.5t65 76t65 69.5t50 72.5t20 85q0 33 -6 62t-20 51t-38.5 34.5t-60.5 12.5q-35 0 -59 -13
t-38.5 -35.5t-20.5 -51.5t-6 -60v-38h-21v37q0 36 7.5 69t25 58t45 39.5t68.5 14.5z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="371" 
d="M186 705q40 0 68 -14t45 -38t25 -56t8 -69v-17q0 -51 -26 -90.5t-74 -54.5q51 -13 75.5 -51t24.5 -89v-54q0 -37 -8 -69t-25 -56t-45 -38t-68 -14t-68 14t-45.5 38t-25.5 56t-8 69v33h22v-33q0 -32 6.5 -60.5t21 -50t38 -34t58.5 -12.5t59 12.5t38.5 34t21 50t6.5 61.5
v53q0 57 -29.5 91.5t-86.5 37.5h-51v20h52q54 5 84.5 42t30.5 91v20q0 33 -6.5 61.5t-21 49.5t-38.5 33.5t-59 12.5t-58.5 -12.5t-38 -33.5t-21 -49.5t-6.5 -60.5v-21h-22v20q0 37 8 69t25.5 56t45.5 38t68 14z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="406" 
d="M308 165v-165h-22v165h-249v22l244 514h27v-515h61v-21h-61zM58 186h228v476z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="371" 
d="M72 391q20 32 49 47t70 15q39 0 65.5 -14t43.5 -38t24.5 -55.5t7.5 -67.5v-106q0 -36 -8 -68.5t-25.5 -56.5t-45 -38t-67.5 -14t-68 14t-45.5 38t-25.5 56.5t-8 68.5v30h22v-30q0 -32 6.5 -60.5t21 -49.5t38 -33.5t58.5 -12.5t59 12.5t38.5 33.5t21 49.5t6.5 60.5v105
q0 31 -6.5 59.5t-21 49.5t-38.5 33.5t-59 12.5q-43 0 -72.5 -22t-43.5 -62v-8h-21l18 361h253v-21h-233z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="377" 
d="M189 705q40 0 67.5 -13.5t45 -36.5t25.5 -54.5t8 -68.5v-5h-21v6q0 32 -6.5 59.5t-21 48t-38.5 32.5t-59 12t-59.5 -12.5t-39.5 -33.5t-22 -50t-7 -62v-193q21 45 51.5 64t81.5 19q39 0 66.5 -13.5t44.5 -37.5t25 -56t8 -68v-70q0 -37 -8 -69t-26 -56t-46.5 -38
t-68.5 -14q-41 0 -69.5 14t-46.5 38t-26 56t-8 69v356q0 37 8 69.5t26 56.5t46.5 37.5t69.5 13.5zM189 396q-35 0 -59.5 -12.5t-39.5 -34t-22 -49.5t-7 -59v-69q0 -33 7 -61.5t22 -49.5t39.5 -33t59.5 -12t59 12t39 33t22 49.5t7 61.5v69q0 32 -7 60t-22 49t-39 33.5
t-59 12.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="371" 
d="M309 680h-270v21h293v-21l-209 -680h-23z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="385" 
d="M192 705q78 0 116 -46t38 -121v-23q0 -55 -23 -96t-78 -53q57 -13 79 -54t22 -98v-47q0 -38 -9 -69.5t-28 -54t-48 -35.5t-69 -13t-69 13t-48 35.5t-28 54t-9 69.5v47q0 59 22.5 98.5t81.5 53.5q-57 13 -80.5 52.5t-23.5 96.5v23q0 75 38 121t116 46zM192 685
q-67 0 -99.5 -40.5t-32.5 -106.5v-25q0 -67 33 -102.5t99 -35.5q67 0 99.5 35.5t32.5 102.5v25q0 66 -32.5 106.5t-99.5 40.5zM192 355q-68 0 -100 -35t-32 -104v-49q0 -67 32 -109.5t100 -42.5q69 0 100.5 42.5t31.5 109.5v49q0 69 -31.5 104t-100.5 35z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="377" 
d="M188 706q40 0 68.5 -14t46.5 -38t26 -56.5t8 -69.5v-355q0 -37 -8 -69.5t-26 -56.5t-46.5 -38t-68.5 -14t-68 13.5t-46 37t-26.5 55.5t-8.5 69v5h21v-6q0 -32 7 -60t22 -49t39.5 -33t59.5 -12t59 12.5t39 34t22 50.5t7 62v195q-14 -43 -47.5 -64t-84.5 -21
q-39 0 -66.5 13.5t-44.5 37.5t-25 56t-8 68v69q0 37 8 69.5t26 56.5t46.5 38t68.5 14zM188 685q-35 0 -59 -12.5t-39 -33.5t-22 -49.5t-7 -60.5v-69q0 -32 7 -60.5t22 -49.5t39 -33.5t59 -12.5t59 12.5t39 33.5t22 49.5t7 60.5v69q0 32 -7 60.5t-22 49.5t-39 33.5t-59 12.5z
" />
    <glyph glyph-name="zero.alt" 
d="M39 528q0 37 8 69.5t26 56.5t46.5 37.5t68.5 13.5t68.5 -13.5t46.5 -37.5t26 -56.5t8 -69.5v-356q0 -37 -8 -69t-26 -56t-46.5 -38t-68.5 -14t-68.5 14t-46.5 38t-26 56t-8 69v356zM61 172q0 -33 7 -61.5t22 -49.5t39 -33.5t59 -12.5t59 12.5t39 33.5t22 49.5t7 61.5v357
q0 32 -7 60.5t-22 49.5t-39 33.5t-59 12.5t-59 -12.5t-39 -33.5t-22 -49.5t-7 -60.5v-357z" />
    <glyph glyph-name="one.alt" horiz-adv-x="216" 
d="M37 596q47 5 75 34.5t42 70.5h22v-701h-22v659q-17 -35 -46.5 -55.5t-70.5 -25.5v18z" />
    <glyph glyph-name="two.alt" horiz-adv-x="367" 
d="M184 705q41 0 69 -14t45 -38.5t24.5 -57t7.5 -69.5q0 -49 -19.5 -88t-49 -75t-64.5 -70.5t-65 -74t-50.5 -86.5t-21.5 -107v-4h266v-21h-288v15q0 65 20 115.5t50 91.5t65 76t65 69.5t50 72.5t20 85q0 33 -6 62t-20 51t-38.5 34.5t-60.5 12.5q-35 0 -58.5 -13t-38 -35.5
t-20.5 -51.5t-6 -60v-38h-22v37q0 36 7.5 69t25 58t45 39.5t68.5 14.5z" />
    <glyph glyph-name="three.alt" horiz-adv-x="370" 
d="M185 705q40 0 68 -14t45 -38t25 -56t8 -69v-17q0 -51 -26 -90.5t-74 -54.5q51 -13 75.5 -51t24.5 -89v-54q0 -37 -8 -69t-25 -56t-45 -38t-68 -14t-68 14t-45.5 38t-25.5 56t-8 69v33h22v-33q0 -32 6.5 -60.5t21 -50t38 -34t58.5 -12.5t59 12.5t38.5 34t21 50t6.5 61.5
v53q0 57 -29.5 91.5t-86.5 37.5h-51v20h52q54 5 84.5 42t30.5 91v20q0 33 -6.5 61.5t-21 49.5t-38.5 33.5t-59 12.5t-58.5 -12.5t-38 -33.5t-21 -49.5t-6.5 -60.5v-21h-22v20q0 37 8 69t25.5 56t45.5 38t68 14z" />
    <glyph glyph-name="four.alt" horiz-adv-x="406" 
d="M308 165v-165h-22v165h-249v22l244 514h27v-515h61v-21h-61zM58 186h228v476z" />
    <glyph glyph-name="five.alt" horiz-adv-x="370" 
d="M71 392q23 34 50.5 47.5t68.5 13.5q39 0 65.5 -14t43.5 -38t24.5 -55.5t7.5 -67.5v-106q0 -36 -8 -68.5t-25.5 -56.5t-45 -38t-67.5 -14t-67.5 14t-45 38t-25.5 56.5t-8 68.5v30h21v-30q0 -32 6.5 -60.5t21 -49.5t38.5 -33.5t59 -12.5q34 0 58 12.5t38.5 33.5t21 49.5
t6.5 60.5v105q0 31 -6.5 59.5t-21 49.5t-38.5 33.5t-58 12.5q-43 0 -72.5 -22t-43.5 -62v-8h-22l19 361h252v-21h-233z" />
    <glyph glyph-name="six.alt" horiz-adv-x="377" 
d="M189 705q40 0 67.5 -13.5t45 -36.5t25.5 -54.5t8 -68.5v-5h-21v6q0 32 -6.5 59.5t-21 48t-38.5 32.5t-59 12t-59.5 -12.5t-39.5 -33.5t-22 -50t-7 -62v-193q21 45 51.5 64t81.5 19q39 0 66.5 -13.5t44.5 -37.5t25 -56t8 -68v-70q0 -37 -8 -69t-26 -56t-46.5 -38
t-68.5 -14q-41 0 -69.5 14t-46.5 38t-26 56t-8 69v356q0 37 8 69.5t26 56.5t46.5 37.5t69.5 13.5zM189 396q-35 0 -59.5 -12.5t-39.5 -34t-22 -49.5t-7 -59v-69q0 -33 7 -61.5t22 -49.5t39.5 -33t59.5 -12t59 12t39 33t22 49.5t7 61.5v69q0 32 -7 60t-22 49t-39 33.5
t-59 12.5z" />
    <glyph glyph-name="seven.alt" horiz-adv-x="365" 
d="M305 680h-269v21h292v-21l-208 -680h-23z" />
    <glyph glyph-name="eight.alt" horiz-adv-x="385" 
d="M192 705q78 0 116 -46t38 -121v-23q0 -55 -23 -96t-78 -53q57 -13 79 -54t22 -98v-47q0 -38 -9 -69.5t-28 -54t-48 -35.5t-69 -13t-69 13t-48 35.5t-28 54t-9 69.5v47q0 59 23 98.5t81 53.5q-57 13 -80.5 52.5t-23.5 96.5v23q0 75 38 121t116 46zM192 685
q-67 0 -99.5 -40.5t-32.5 -106.5v-25q0 -67 33 -102.5t99 -35.5q67 0 99.5 35.5t32.5 102.5v25q0 66 -32 106.5t-100 40.5zM192 355q-68 0 -100 -35t-32 -104v-49q0 -67 32 -109.5t100 -42.5q69 0 100.5 42.5t31.5 109.5v49q0 69 -31.5 104t-100.5 35z" />
    <glyph glyph-name="nine.alt" horiz-adv-x="377" 
d="M188 706q40 0 68.5 -14t46.5 -38t26 -56.5t8 -69.5v-355q0 -37 -8 -69.5t-26 -56.5t-46.5 -38t-68.5 -14t-68 13.5t-46 37t-26.5 55.5t-8.5 69v5h21v-6q0 -32 7 -60t22 -49t39.5 -33t59.5 -12t59 12.5t39 34t22 50.5t7 62v195q-14 -43 -47.5 -64t-84.5 -21
q-39 0 -66.5 13.5t-44.5 37.5t-25 56t-8 68v69q0 37 8 69.5t26 56.5t46.5 38t68.5 14zM188 685q-35 0 -59 -12.5t-39 -33.5t-22 -49.5t-7 -60.5v-69q0 -32 7 -60.5t22 -49.5t39 -33.5t59 -12.5t59 12.5t39 33.5t22 49.5t7 60.5v69q0 32 -7 60.5t-22 49.5t-39 33.5t-59 12.5z
" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="330" 
d="M311 700l-276 -700h-16l276 700h16z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="552" 
d="M115 670q-24 -41 -71 -47l-1 15q29 2 47 19t26 43h17v-444h-18v414zM384 700l-276 -700h-17l276 700h17zM419 449q27 0 45 -9t29 -25t15.5 -36.5t4.5 -44.5q0 -31 -12.5 -56t-31 -47.5t-40.5 -44t-41 -46t-32 -54t-14 -67.5v-3h167v-16h-184v11q0 41 12.5 73t31.5 58
t41.5 48t41.5 43.5t31.5 45.5t12.5 54q0 20 -3.5 38.5t-12.5 32t-24 21.5t-37 8t-37 -8t-24 -22t-13 -32.5t-4 -38.5v-24h-16v24q0 24 5 45t16 37t29 25.5t44 9.5z" />
    <glyph glyph-name="uni2153" unicode="&#x2153;" horiz-adv-x="543" 
d="M115 670q-24 -41 -71 -47l-1 15q29 2 47 19t26 43h17v-444h-18v414zM384 700l-276 -700h-17l276 700h17zM410 449q25 0 43 -9t29.5 -24.5t16.5 -36t5 -44.5v-11q0 -31 -15 -56t-44 -36q30 -10 44.5 -34t14.5 -55v-34q0 -24 -5 -44.5t-16.5 -36t-29.5 -24.5t-43 -9
q-26 0 -44 9t-29.5 24.5t-16.5 36t-5 44.5v21h17v-22q0 -20 4 -37.5t13 -31t23.5 -21t36.5 -7.5q43 0 60.5 28.5t17.5 69.5v34q0 34 -19 57t-54 23h-32v16h33q16 0 29.5 7t23 18.5t14.5 26.5t5 31v13q0 40 -17.5 69t-60.5 29q-22 0 -36.5 -8t-23.5 -21t-13 -31t-4 -37v-14
h-17v13q0 24 5 44.5t16.5 36t29.5 24.5t44 9z" />
    <glyph glyph-name="uni2154" unicode="&#x2154;" horiz-adv-x="603" 
d="M135 705q26 0 44 -9t29 -25t16 -36.5t5 -44.5q0 -31 -12.5 -56t-31 -47.5t-41 -44.5t-41.5 -46.5t-32 -54t-14 -66.5v-3h168v-16h-185v11q0 41 12.5 73t32 58t41.5 48t41.5 43.5t32 45.5t12.5 54q0 20 -4 38.5t-13 32t-24 21.5t-37 8t-37 -8.5t-24 -22.5t-12.5 -32
t-3.5 -38v-24h-17v24q0 24 5 45t16 37t29.5 25.5t44.5 9.5zM170 0h-16l276 700h16zM469 449q26 0 44 -9t29.5 -24.5t16.5 -36t5 -44.5v-11q0 -31 -15 -56t-44 -36q30 -10 44.5 -34t14.5 -55v-34q0 -24 -5 -44.5t-16.5 -36t-29.5 -24.5t-44 -9t-44 9t-29 24.5t-16 36t-5 44.5
v21h17v-22q0 -20 4 -37.5t13 -31t23.5 -21t36.5 -7.5q43 0 60.5 28.5t17.5 69.5v34q0 34 -19 57t-54 23h-32v16h33q16 0 29.5 7t23 18.5t14.5 26.5t5 31v13q0 40 -17.5 69t-60.5 29q-22 0 -36.5 -8t-23.5 -21t-13 -31t-4 -37v-14h-17v13q0 24 5 44.5t16 36t29 24.5t44 9z
" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="491" 
d="M115 670q-24 -41 -71 -47l-1 15q29 2 47 19t26 43h17v-444h-18v414zM111 0h-16l276 700h16zM416 0h-17v102h-159v16l155 326h21v-326h37v-16h-37v-102zM399 118v295l-141 -295h141z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="559" 
d="M134 705q25 0 43 -9t29.5 -24.5t16.5 -36t5 -44.5v-11q0 -31 -15 -56t-44 -36q31 -10 45 -34t14 -55v-34q0 -24 -5 -44.5t-16.5 -36t-29.5 -24.5t-43 -9q-26 0 -44 9t-29 24.5t-16 36t-5 44.5v21h16v-22q0 -20 4 -37.5t13 -31t24 -21t37 -7.5q43 0 60 28.5t17 69.5v34
q0 34 -18.5 57t-53.5 23h-32v16h32q16 0 29.5 7t23 18.5t14.5 26.5t5 31v13q0 40 -17 69t-60 29q-22 0 -37 -8t-24 -21t-13 -31t-4 -37v-14h-16v13q0 24 5 44.5t16 36t29 24.5t44 9zM441 700h16l-276 -700h-16zM467 102h-158v16l155 326h20v-326h38v-16h-38v-102h-17v102z
M327 118h140v295z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="557" 
d="M115 670q-24 -41 -71 -47l-1 15q29 2 47 19t26 43h17v-444h-18v414zM91 0l276 700h17l-276 -700h-17zM418 449q51 0 75.5 -30t24.5 -78v-14q0 -33 -13 -59t-45 -37q32 -10 45 -36t13 -59v-31q0 -48 -24 -79t-76 -31q-51 0 -75 31t-24 79v31q0 33 13 59t45 36
q-32 11 -45 37t-13 59v14q0 48 24.5 78t74.5 30zM418 433q-42 0 -62 -25t-20 -67v-16q0 -42 20 -64t62 -22t62 22t20 64v16q0 42 -20 67t-62 25zM418 223q-82 0 -82 -86v-32q0 -42 19.5 -68t62.5 -26q44 0 63 26t19 68v32q0 42 -19.5 64t-62.5 22z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="625" 
d="M134 705q25 0 43 -9t29.5 -24.5t16.5 -36t5 -44.5v-11q0 -31 -15 -56t-44 -36q31 -10 45 -34t14 -55v-34q0 -24 -5 -44.5t-16.5 -36t-29.5 -24.5t-43 -9q-26 0 -44 9t-29 24.5t-16 36t-5 44.5v21h16v-22q0 -20 4 -37.5t13 -31t24 -21t37 -7.5q43 0 60 28.5t17 69.5v34
q0 34 -18.5 57t-53.5 23h-32v16h32q16 0 29.5 7t23 18.5t14.5 26.5t5 31v13q0 40 -17 69t-60 29q-22 0 -37 -8t-24 -21t-13 -31t-4 -37v-14h-16v13q0 24 5 44.5t16 36t29 24.5t44 9zM454 700l-276 -700h-17l276 700h17zM487 449q50 0 74.5 -30t24.5 -78v-14q0 -33 -13 -59
t-45 -37q33 -10 45.5 -36t12.5 -59v-31q0 -48 -24 -79t-75 -31t-75 31t-24 79v31q0 33 12.5 59t45.5 36q-32 11 -45 37t-13 59v14q0 48 24.5 78t74.5 30zM487 433q-42 0 -62 -25t-20 -67v-16q0 -42 20 -64t62 -22t62 22t20 64v16q0 42 -20 67t-62 25zM487 223
q-43 0 -62.5 -22t-19.5 -64v-32q0 -42 19.5 -68t62.5 -26t62.5 26t19.5 68v32q0 42 -19.5 64t-62.5 22z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="623" 
d="M64 503q12 20 31 30t42 10q25 0 42 -9t28 -24.5t16 -36t5 -43.5v-67q0 -23 -5 -44t-16.5 -36.5t-29.5 -24.5t-43 -9q-26 0 -43.5 9t-29 24.5t-16.5 36.5t-5 44v19h16v-20q0 -39 17.5 -67.5t60.5 -28.5t60 28.5t17 67.5v67q0 40 -17 68.5t-60 28.5q-23 0 -44.5 -14
t-26.5 -38v-5h-17l11 231h161v-17h-146zM436 700h16l-276 -700h-16zM485 449q51 0 75.5 -30t24.5 -78v-14q0 -33 -13 -59t-45 -37q32 -10 45 -36t13 -59v-31q0 -48 -24.5 -79t-75.5 -31t-75 31t-24 79v31q0 33 13 59t45 36q-32 11 -45 37t-13 59v14q0 48 24.5 78t74.5 30z
M485 433q-42 0 -62 -25t-20 -67v-16q0 -42 20 -64t62 -22t62 22t20 64v16q0 42 -20 67t-62 25zM485 223q-82 0 -82 -86v-32q0 -42 19.5 -68t62.5 -26t62.5 26t19.5 68v32q0 42 -19.5 64t-62.5 22z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="536" 
d="M209 684h-170v16h188v-15l-133 -429h-18zM86 0h-17l276 700h17zM398 449q51 0 75 -30t24 -78v-14q0 -33 -13 -59t-45 -37q33 -10 45.5 -36t12.5 -59v-31q0 -48 -24 -79t-75 -31t-75 31t-24 79v31q0 33 13 59t45 36q-32 11 -45 37t-13 59v14q0 48 24.5 78t74.5 30z
M398 433q-42 0 -62 -25t-20 -67v-16q0 -42 20 -64t62 -22t62 22t20 64v16q0 42 -20 67t-62 25zM398 223q-43 0 -62.5 -22t-19.5 -64v-32q0 -42 19.5 -68t62.5 -26t62.5 26t19.5 68v32q0 42 -19.5 64t-62.5 22z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="271" 
d="M39 270q0 24 5.5 44.5t17 36t30 24.5t44.5 9t44 -9t29.5 -24.5t17 -36t5.5 -44.5v-226q0 -24 -5.5 -44.5t-17 -36t-29.5 -24.5t-44 -9t-44.5 9t-30 24.5t-17 36t-5.5 44.5v226zM56 43q0 -40 18 -68.5t62 -28.5q43 0 61 28.5t18 68.5v228q0 40 -18 68.5t-61 28.5
q-44 0 -62 -28.5t-18 -68.5v-228z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="169" 
d="M111 349q-25 -41 -72 -47l-1 15q29 2 47 19t26 43h17v-444h-17v414z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="268" 
d="M135 384q26 0 44 -9t29 -25t16 -36.5t5 -44.5q0 -31 -12.5 -56t-31 -47.5t-41 -44t-41.5 -46.5t-32 -54t-14 -67v-3h168v-16h-185v11q0 41 12.5 73t32 58t41.5 48t41.5 43.5t32 45.5t12.5 54q0 20 -4 38.5t-13 32t-24 21.5t-37 8t-37 -8t-24 -22t-12.5 -32.5t-3.5 -38.5
v-24h-17v24q0 24 5 45t16 37t29.5 25.5t44.5 9.5z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="268" 
d="M134 384q25 0 43 -9t29.5 -24.5t16.5 -36t5 -44.5v-11q0 -31 -15 -56.5t-44 -36.5q29 -8 44 -32.5t15 -55.5v-34q0 -24 -5 -44.5t-16.5 -36t-29.5 -24.5t-43 -9q-26 0 -44 9t-29 24.5t-16 36t-5 44.5v21h16v-22q0 -20 4 -37.5t13 -31t24 -21t37 -7.5q43 0 60 28.5
t17 69.5v34q0 34 -18.5 57t-53.5 23h-32v16h32q16 0 29.5 7t23 18.5t14.5 26.5t5 31v13q0 40 -17 69t-60 29q-22 0 -37 -8t-24 -21t-13 -31t-4 -37v-14h-16v13q0 24 5 44.5t16 36t29 24.5t44 9z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="289" 
d="M214 33v-102h-17v102h-159v16l155 326h21v-326h37v-16h-37zM55 49h142v294z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="268" 
d="M56 46q0 -40 17.5 -68.5t60.5 -28.5t60 28.5t17 68.5v67q0 39 -17 68t-60 29q-23 0 -44.5 -14t-26.5 -38v-5h-17l11 230h161v-16h-146l-8 -180q12 20 31 29.5t42 9.5q25 0 42 -9t28 -24.5t16 -36t5 -43.5v-67q0 -23 -5 -44t-16.5 -36.5t-29.5 -24.5t-43 -9q-26 0 -43.5 9
t-29 24.5t-16.5 36.5t-5 44v20h16v-20z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="271" 
d="M136 384q52 0 73 -32.5t21 -79.5v-3h-16v4q0 40 -17 67.5t-61 27.5q-22 0 -37 -7.5t-24.5 -21t-13.5 -31.5t-4 -38v-123q21 54 82 54q25 0 42.5 -9t29 -24.5t16.5 -36t5 -43.5v-44q0 -24 -5.5 -44.5t-17 -36t-29.5 -24.5t-44 -9t-44 9t-29.5 24.5t-17 36t-5.5 44.5v226
q0 24 5 44.5t16.5 36t30 24.5t44.5 9zM136 185q-22 0 -37 -7.5t-24.5 -21t-13.5 -31.5t-4 -38v-44q0 -20 4 -37.5t13.5 -31t24.5 -21t37 -7.5q43 0 61 28.5t18 68.5v44q0 20 -4 38t-13.5 31.5t-24.5 21t-37 7.5z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="267" 
d="M209 367h-170v16h188v-15l-133 -429h-18z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="276" 
d="M138 384q51 0 75.5 -30t24.5 -78v-14q0 -33 -13 -59t-45 -37q32 -10 45 -36t13 -59v-31q0 -48 -24.5 -79t-75.5 -31t-75 31t-24 79v31q0 33 13 59t45 36q-32 11 -45 37t-13 59v14q0 48 24.5 78t74.5 30zM138 368q-42 0 -62 -25t-20 -67v-16q0 -42 20 -64t62 -22t62 22
t20 64v16q0 42 -20 67t-62 25zM138 158q-82 0 -82 -86v-32q0 -42 19.5 -68t62.5 -26t62.5 26t19.5 68v32q0 42 -19.5 64t-62.5 22z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="271" 
d="M135 384q26 0 44.5 -9t30 -24.5t17 -36t5.5 -44.5v-226q0 -24 -5.5 -44.5t-17 -36t-30 -24.5t-44.5 -9q-51 0 -73.5 32t-22.5 80v4h17v-5q0 -40 17.5 -67.5t61.5 -27.5q43 0 61 28.5t18 69.5v122q-21 -53 -82 -53q-50 0 -71.5 32.5t-21.5 79.5v45q0 24 5.5 44.5t17 36
t29.5 24.5t44 9zM135 368q-22 0 -37 -7.5t-24.5 -21t-13.5 -31t-4 -37.5v-45q0 -20 4 -37.5t13.5 -31t24.5 -21t37 -7.5q43 0 61 28.5t18 68.5v45q0 40 -18 68.5t-61 28.5z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="271" 
d="M39 686q0 24 5.5 44.5t17 36t30 24.5t44.5 9t44 -9t29.5 -24.5t17 -36t5.5 -44.5v-226q0 -24 -5.5 -44.5t-17 -36t-29.5 -24.5t-44 -9t-44.5 9t-30 24.5t-17 36t-5.5 44.5v226zM56 459q0 -40 18 -68.5t62 -28.5q43 0 61 28.5t18 68.5v228q0 40 -18 68.5t-61 28.5
q-44 0 -62 -28.5t-18 -68.5v-228z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="169" 
d="M38 733q29 2 47 19t26 43h17v-444h-17v414q-25 -41 -72 -47z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="268" 
d="M135 800q26 0 44 -9t29 -25t16 -36.5t5 -44.5q0 -31 -12.5 -56t-31 -47.5t-40.5 -44t-41 -46.5t-32.5 -54t-14.5 -67v-3h168v-16h-185v11q0 41 12.5 73t32 58t41.5 48t41.5 43.5t32 45.5t12.5 54q0 20 -4 38.5t-13 32t-24 21.5t-37 8t-37 -8t-24 -22t-12.5 -32.5
t-3.5 -38.5v-24h-17v24q0 24 5 45t16 37t29.5 25.5t44.5 9.5z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="268" 
d="M134 800q25 0 43 -9t29.5 -24.5t16.5 -36t5 -44.5v-11q0 -31 -15 -56t-44 -36q31 -10 45 -34t14 -55v-34q0 -24 -5 -44.5t-16.5 -36t-29.5 -24.5t-43 -9q-26 0 -44 9t-29 24.5t-16 36t-5 44.5v21h16v-22q0 -20 4 -37.5t13 -31t24 -21t37 -7.5q43 0 60 28.5t17 69.5v34
q0 34 -18.5 57t-53.5 23h-32v16h32q16 0 29.5 7t23 18.5t14.5 26.5t5 31v13q0 40 -17 69t-60 29q-22 0 -37 -8t-24 -21t-13 -31t-4 -37v-14h-16v13q0 24 5 44.5t16 36t29 24.5t44 9z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="289" 
d="M214 453v-102h-17v102h-159v16l155 326h21v-326h37v-16h-37zM55 469h142v295z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="268" 
d="M56 457q0 -39 17.5 -67.5t60.5 -28.5t60 28.5t17 67.5v67q0 40 -17 68.5t-60 28.5q-23 0 -44.5 -14t-26.5 -38v-5h-17l11 231h161v-17h-146l-8 -180q12 20 31 30t42 10q25 0 42 -9t28 -24.5t16 -36t5 -43.5v-67q0 -23 -5 -44t-16.5 -36.5t-29.5 -24.5t-43 -9
q-26 0 -43.5 9t-29 24.5t-16.5 36.5t-5 44v19h16v-20z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="271" 
d="M136 800q52 0 73 -32.5t21 -79.5v-3h-16v4q0 40 -17 67.5t-61 27.5q-22 0 -37 -7.5t-24.5 -21t-13.5 -31.5t-4 -38v-123q21 54 82 54q25 0 42.5 -9t29 -24.5t16.5 -36t5 -43.5v-44q0 -24 -5.5 -44.5t-17 -36t-29.5 -24.5t-44 -9t-44 9t-29.5 24.5t-17 36t-5.5 44.5v226
q0 24 5 44.5t16.5 36t30 24.5t44.5 9zM136 601q-22 0 -37 -7.5t-24.5 -21t-13.5 -31.5t-4 -38v-44q0 -20 4 -37.5t13.5 -31t24.5 -21t37 -7.5q43 0 61 28.5t18 68.5v44q0 20 -4 38t-13.5 31.5t-24.5 21t-37 7.5z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="267" 
d="M209 779h-170v16h188v-15l-133 -429h-18z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="276" 
d="M138 800q51 0 75.5 -30t24.5 -78v-14q0 -33 -13 -59t-45 -37q32 -10 45 -36t13 -59v-31q0 -48 -24.5 -79t-75.5 -31t-75 31t-24 79v31q0 33 13 59t45 36q-32 11 -45 37t-13 59v14q0 48 24.5 78t74.5 30zM138 784q-42 0 -62 -25t-20 -67v-16q0 -42 20 -64t62 -22t62 22
t20 64v16q0 42 -20 67t-62 25zM138 574q-82 0 -82 -86v-32q0 -42 19.5 -68t62.5 -26t62.5 26t19.5 68v32q0 42 -19.5 64t-62.5 22z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="271" 
d="M135 800q26 0 44.5 -9t30 -24.5t17 -36t5.5 -44.5v-226q0 -24 -5.5 -44.5t-17 -36t-30 -24.5t-44.5 -9q-51 0 -73.5 32t-22.5 80v4h17v-5q0 -40 17.5 -67.5t61.5 -27.5q43 0 61 28.5t18 69.5v122q-21 -53 -82 -53q-50 0 -71.5 32.5t-21.5 79.5v45q0 24 5.5 44.5t17 36
t29.5 24.5t44 9zM135 784q-22 0 -37 -7.5t-24.5 -21t-13.5 -31t-4 -37.5v-45q0 -20 4 -37.5t13.5 -31t24.5 -21t37 -7.5q43 0 61 28.5t18 68.5v45q0 40 -18 68.5t-61 28.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="393" 
d="M206 533l87 -119l-15 -11l-82 122l-81 -121l-16 10l87 119l-142 38l6 19l140 -45l-4 148h20l-3 -148l140 42l6 -19z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="368" 
d="M56 700l276 -700h-20l-276 700h20z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="213" 
d="M106 397q20 0 33.5 -13.5t13.5 -33.5t-13.5 -33.5t-33.5 -13.5q-19 0 -32.5 13.5t-13.5 33.5t13.5 33.5t32.5 13.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="102" 
d="M62 462v-81h-22v81h22zM62 81v-81h-22v81h22z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="115" 
d="M53 0v81h22v-80l-22 -68h-11z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="210" 
d="M62 81v-81h-22v81h22zM115 81v-81h-21v81h21zM170 81v-81h-21v81h21z" />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="102" 
d="M40 308v393h22v-393l-3 -153h-16zM62 80v-80h-22v80h22z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="102" 
d="M62 701v-81h-22v81h22zM40 339l3 207h16l3 -207v-339h-22v339z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="415" 
d="M312 492l-25 -244h64l-1 -17h-65l-24 -231h-20l25 231h-138l-24 -231h-20l25 231h-72l1 17h73l25 244h-73l1 17h74l19 192h20l-20 -192h137l20 192h20l-20 -192h64l-1 -17h-65zM293 492h-138l-25 -244h137z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="102" 
d="M62 81v-81h-22v81h22z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="102" 
d="M62 404v-81h-22v81h22z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="364" 
d="M182 705q41 0 69 -14t44.5 -38.5t23.5 -57t7 -69.5t-11.5 -66t-28 -53.5t-36.5 -47.5t-36.5 -48t-28 -53.5t-11.5 -64.5q0 -18 5 -36h-20q-5 20 -5 37q0 38 11 67.5t28 54.5t36 48t36 47.5t28 52t11 63.5q0 32 -5.5 60.5t-19.5 50.5t-37.5 34.5t-59.5 12.5
q-35 0 -59 -13.5t-38 -35.5t-20 -51t-6 -60v-38h-21v37q0 36 7.5 69t24 57.5t44 39.5t68.5 15zM179 80v-80h-22v80h22z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="364" 
d="M206 700v-80h-21v80h21zM205 543q5 -20 5 -37q0 -38 -11.5 -67.5t-28 -55t-36 -48t-36.5 -47t-28 -52.5t-11 -63q0 -32 5.5 -60.5t20 -50.5t38 -34.5t59.5 -12.5q35 0 58.5 13.5t38 35.5t20.5 51t6 60v38h21v-37q0 -36 -7.5 -69t-24.5 -57.5t-44.5 -39.5t-67.5 -15
q-41 0 -69 14t-45 38.5t-24 57t-7 69.5t11.5 66t28 53.5t36.5 47.5t36.5 48t28 53.5t11.5 64.5q0 18 -5 36h21z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="161" 
d="M62 701l-2 -161h-18l-2 161h22zM121 701l-2 -161h-17l-2 161h21z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="101" 
d="M61 701l-2 -161h-18l-1 161h21z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="115" 
d="M75 462v-81h-22v81h22zM53 0v81h22v-80l-22 -68h-11z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="368" 
d="M332 700l-276 -700h-20l276 700h20z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="518" 
d="M474 -10v-18h-439v18h439z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="256" 
d="M194 681q-20 0 -32 -7t-18.5 -18.5t-9 -27.5t-3.5 -34l-7 -136q-1 -19 -3 -36t-9 -31t-19.5 -24.5t-33.5 -16.5q21 -6 33.5 -16.5t19.5 -24t9 -31t3 -36.5l7 -135q1 -18 3.5 -34t9 -28t18.5 -18.5t32 -6.5h25v-20h-30q-23 0 -38 8t-23.5 21.5t-12.5 32t-5 39.5l-7 139
q-1 21 -3 37t-8.5 28.5t-19.5 20.5t-35 12v24q22 4 35 12.5t19.5 20.5t8.5 28.5t3 37.5l7 139q1 21 5 39t12.5 32t23.5 22t38 8h30v-20h-25z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="256" 
d="M67 701q24 0 38.5 -8t23.5 -22t12.5 -32t4.5 -39l7 -139q1 -21 3 -37.5t8.5 -28.5t19.5 -20.5t36 -12.5v-24q-23 -4 -36 -12t-19.5 -20.5t-8.5 -28.5t-3 -37l-7 -139q-1 -21 -4.5 -39.5t-12.5 -32t-23.5 -21.5t-38.5 -8h-30v20h25q20 0 32 6.5t18.5 18.5t9 28t3.5 34
l7 135q1 19 3 36.5t9 31t19.5 24t33.5 16.5q-21 6 -33.5 16.5t-19.5 24.5t-9 31t-3 36l-7 136q-1 18 -3.5 34t-9 27.5t-18.5 18.5t-32 7h-25v20h30z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="225" 
d="M188 20v-20h-147v701h147v-20h-125v-661h125z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="225" 
d="M163 20v661h-125v20h147v-701h-147v20h125z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="224" 
d="M170 681q-33 0 -54 -10t-32.5 -28t-16 -43t-4.5 -55v-389q0 -30 4 -55t16 -43t33 -28t54 -10h17v-20h-18q-39 0 -63.5 11.5t-39 32t-20 49t-5.5 63.5v389q0 35 5.5 63.5t20 49t39.5 32t63 11.5h18v-20h-17z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="224" 
d="M54 701q38 0 63 -11.5t39.5 -32t20.5 -49t6 -63.5v-389q0 -35 -5.5 -63.5t-20 -49t-39.5 -32t-64 -11.5h-17v20h17q33 0 54 10t32.5 28t16 43t4.5 55v389q0 30 -4.5 55t-16.5 43t-32.5 28t-53.5 10h-17v20h17z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="547" 
d="M35 360h477v-19h-477v19z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="386" 
d="M35 360h316v-19h-316v19z" />
    <glyph glyph-name="figuredash" unicode="&#x2012;" horiz-adv-x="474" 
d="M35 360h404v-19h-404v19z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="256" 
d="M39 360h178v-20h-178v20z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="256" 
d="M39 360h178v-20h-178v20z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="259" 
d="M37 351l67 240h20l-66 -240l66 -241h-20zM135 351l66 240h21l-66 -240l66 -241h-21z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="259" 
d="M103 351l-66 240h21l66 -240l-66 -241h-21zM201 351l-66 240h21l66 -240l-66 -241h-21z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="162" 
d="M37 350l66 241h22l-66 -241l66 -242h-22z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="162" 
d="M103 351l-66 241h21l67 -241l-67 -242h-21z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="174" 
d="M53 -67h-11l11 67v81h22v-80zM113 -67h-12l12 67v81h21v-80z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="167" 
d="M62 700h11l-11 -67v-81h-22v80zM114 700h12l-12 -67v-81h-21v80z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="167" 
d="M52 552h-12l12 67v81h21v-80zM104 552h-11l11 67v81h22v-80z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="115" 
d="M62 700h11l-11 -67v-81h-22v80z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="115" 
d="M53 552h-11l11 67v81h22v-80z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="115" 
d="M53 -67h-11l11 67v81h22v-80z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="447" 
d="M313 811q-2 -38 -28.5 -58t-63.5 -20q-35 0 -62 20t-28 58h16q1 -31 23.5 -46t50.5 -15q29 0 51.5 15t24.5 46h16zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="155" 
 />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="398" 
d="M73 528q0 36 7.5 68t24.5 56.5t44.5 38.5t67.5 14q39 0 66 -14t44 -38.5t24.5 -56.5t7.5 -68v-33h-21v34q0 31 -6 59.5t-20.5 50t-37.5 34t-57 12.5t-57.5 -12.5t-37.5 -34t-20.5 -50t-6.5 -59.5v-115h207v-16h-207v-92h207v-16h-207v-119q0 -31 6 -59t20.5 -49.5
t37.5 -34t58 -12.5q34 0 57 12.5t37.5 34t20.5 49.5t6 59v40h21v-39q0 -36 -7.5 -68t-24.5 -56t-44 -38.5t-66 -14.5q-40 0 -67.5 14.5t-44.5 38.5t-24.5 56t-7.5 68v118h-44v16h44v92h-44v16h44v114z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="370" 
d="M195 656q37 -2 63 -15.5t42 -36t23.5 -53t7.5 -65.5v-25h-21v26q0 32 -6.5 59t-21 47t-38.5 31.5t-59 11.5t-58.5 -12.5t-37.5 -33.5t-20.5 -49.5t-6.5 -59.5v-260q0 -31 6.5 -59t20.5 -49.5t37.5 -34t58.5 -12.5t59 12t38.5 32.5t21 48.5t6.5 60v45h21v-44
q0 -35 -7.5 -66t-23.5 -54.5t-42 -38t-63 -15.5v-48h-20v48q-37 1 -63 16t-42 39t-23 55t-7 66v258q0 35 7 66t23 55t42 38.5t63 16.5v46h20v-46z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="332" 
d="M248 421q27 -30 27 -71t-27 -71l45 -44l-12 -11l-44 43q-14 -12 -32.5 -19t-39.5 -7q-20 0 -38 7t-32 19l-45 -43l-11 11l44 43q-26 32 -26 72q0 21 7 39t19 32l-44 44l11 11l45 -44q30 27 70 27q42 0 72 -27l44 44l12 -11zM165 442q-19 0 -35.5 -7t-29 -19.5t-20 -29.5
t-7.5 -36t7.5 -36t20 -29.5t29 -19.5t35.5 -7t36 7t30 19.5t20 29.5t7 36t-7 36t-20 29.5t-30 19.5t-36 7z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="370" 
d="M196 704q37 -2 63 -16.5t42 -38t23 -54.5t7 -65v-13h-21v14q0 31 -6.5 58.5t-21 48.5t-38 33t-57.5 12t-57.5 -12t-38 -32.5t-21 -48t-6.5 -58.5q0 -45 20 -76t49.5 -55.5t64.5 -45.5t64.5 -46.5t49.5 -58.5t20 -80q0 -35 -7.5 -66t-23.5 -54t-42 -37.5t-63 -16.5v-57
h-19v57q-38 1 -64 15.5t-42.5 38t-24 54.5t-7.5 66v35h22v-36q0 -32 6.5 -59.5t21 -48t38 -32.5t58.5 -12t59 12t38.5 32.5t21 48t6.5 59.5q0 44 -20 74.5t-49.5 55t-64.5 45.5t-64.5 46.5t-49.5 59t-20 81.5q0 74 31.5 121t103.5 52v63h19v-63z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="383" 
d="M196 705q40 0 67.5 -15t44 -40t23.5 -58t7 -69v-32h-21v33q0 31 -5.5 60t-19.5 51.5t-37.5 36t-58.5 13.5q-36 0 -59.5 -12.5t-37.5 -34.5t-20 -50.5t-6 -60.5q0 -45 9.5 -77.5t22.5 -61t27.5 -56t23.5 -62.5h157v-19h-153q4 -23 2.5 -47t-12 -52t-30.5 -60t-53 -71h276
v-21h-304v20q21 22 41.5 48.5t35.5 55.5t22 61t0 66h-87v19h84q-10 35 -24.5 62.5t-27.5 55.5t-22.5 60.5t-9.5 77.5q0 37 7.5 69.5t24.5 57t44.5 38.5t68.5 14z" />
    <glyph glyph-name="uni20B9" unicode="&#x20b9;" horiz-adv-x="374" 
d="M175 681q52 -20 74 -66.5t22 -98.5h65v-19h-65q-1 -34 -11.5 -63t-30.5 -50t-51.5 -33t-74.5 -12h-38l256 -339h-25l-258 341v17h62q39 0 67 10.5t46 29.5t27 44t10 55h-208v19h208q0 35 -9.5 65.5t-28 52.5t-47 34.5t-65.5 12.5h-58v19h294v-19h-161z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="444" 
d="M233 260v-83h115v-15h-115v-162h-22v162h-115v15h115v83h-115v15h113l-173 425h24l162 -399l164 399h21l-173 -425h114v-15h-115z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="397" 
d="M123 469q23 1 44 -9t40.5 -22t38.5 -22t39 -9q22 2 36.5 17.5t25.5 35.5l14 -10q-14 -26 -31.5 -43t-43.5 -19q-22 -1 -42.5 9t-40 22t-39.5 22t-41 9q-24 -1 -41.5 -16t-31.5 -36l-14 10q17 27 37.5 43.5t49.5 17.5zM123 310q23 0 44 -10t40.5 -22t38.5 -21.5t39 -8.5
q22 1 36.5 17t25.5 36l14 -10q-14 -25 -31.5 -42.5t-43.5 -19.5q-22 -1 -42.5 9t-40 22t-39.5 21.5t-41 9.5q-24 -1 -41.5 -15.5t-31.5 -36.5l-14 10q17 26 37.5 43t49.5 18z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="397" 
d="M123 389q23 1 43.5 -9t40 -21.5t38.5 -21.5t39 -9q22 1 36.5 17t26.5 36l14 -11q-14 -25 -32 -42t-44 -19q-22 -1 -42.5 8.5t-40 21.5t-39.5 22t-41 10q-24 0 -41.5 -15t-31.5 -38l-13 10q17 27 37.5 44t49.5 17z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="271" 
d="M145 478v-81h-21v81h21zM233 359v-17h-195v17h195zM145 304v-82h-21v82h21z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="271" 
d="M232 431v-17h-193v17h193zM232 324v-17h-193v17h193z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="361" 
d="M40 222l259 128l-259 128v20l282 -139v-18l-282 -139v20z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="553" 
d="M397 480q52 0 85 -27t33 -81q0 -55 -33 -81.5t-85 -26.5h-15q-38 0 -65.5 14t-38.5 50q-11 -36 -39.5 -50t-66.5 -14h-14q-53 0 -85.5 26.5t-32.5 81.5q0 54 32.5 81t85.5 27h14q38 0 66.5 -14.5t39.5 -49.5q11 35 38.5 49.5t65.5 14.5h15zM158 463q-46 0 -73 -22.5
t-27 -68.5t27 -68t73 -23h16q95 0 95 91t-95 91h-16zM380 463q-95 0 -95 -91q0 -89 95 -91h16q45 0 73 22.5t28 68.5t-28 68.5t-73 22.5h-16z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="361" 
d="M321 202l-282 139v18l282 139v-20l-259 -128l259 -128v-20z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="374" 
d="M38 342v17h298v-151h-18v134h-280z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="385" 
d="M347 359v-18h-309v18h309z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="315" 
d="M263 231l-105 106l-106 -106l-12 13l105 105l-105 105l12 13l106 -105l105 105l12 -13l-105 -105l105 -105z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="597" 
d="M129 701q25 0 42.5 -9.5t28.5 -25t15.5 -36.5t4.5 -43v-206q0 -23 -4.5 -43.5t-15.5 -36t-28.5 -25t-42.5 -9.5t-42.5 9.5t-28 25t-15 36t-4.5 43.5v206q0 22 4.5 43t15 36.5t28 25t42.5 9.5zM444 701l-276 -701h-17l276 701h17zM129 685q-21 0 -35.5 -8.5t-22.5 -22
t-11.5 -31t-3.5 -36.5v-207q0 -18 3.5 -35.5t11.5 -31t22.5 -22t35.5 -8.5t35.5 8.5t22.5 22t11.5 31t3.5 35.5v207q0 19 -3.5 36.5t-11.5 31t-22.5 22t-35.5 8.5zM468 431q25 0 42.5 -9.5t28 -25.5t15 -36.5t4.5 -42.5v-203q0 -23 -4.5 -43.5t-15 -36.5t-28 -25.5
t-42.5 -9.5q-26 0 -43 9.5t-28 25.5t-15.5 36.5t-4.5 43.5v203q0 22 4.5 42.5t15.5 36.5t28 25.5t43 9.5zM468 414q-21 0 -35.5 -8t-22.5 -21.5t-11.5 -31t-3.5 -36.5v-204q0 -18 3.5 -35.5t11.5 -31t22.5 -22t35.5 -8.5t35.5 8.5t22.5 22t11.5 31t3.5 35.5v204
q0 19 -3.5 36.5t-11.5 31t-22.5 21.5t-35.5 8z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="841" 
d="M129 701q25 0 42.5 -9.5t28.5 -25t15.5 -36.5t4.5 -43v-206q0 -23 -4.5 -43.5t-15.5 -36t-28.5 -25t-42.5 -9.5t-42.5 9.5t-28 25t-15 36t-4.5 43.5v206q0 22 4.5 43t15 36.5t28 25t42.5 9.5zM444 701l-276 -701h-17l276 701h17zM129 685q-21 0 -35.5 -8.5t-22.5 -22
t-11.5 -31t-3.5 -36.5v-207q0 -18 3.5 -35.5t11.5 -31t22.5 -22t35.5 -8.5t35.5 8.5t22.5 22t11.5 31t3.5 35.5v207q0 19 -3.5 36.5t-11.5 31t-22.5 22t-35.5 8.5zM468 431q25 0 42.5 -9.5t28 -25.5t15 -36.5t4.5 -42.5v-203q0 -23 -4.5 -43.5t-15 -36.5t-28 -25.5
t-42.5 -9.5q-26 0 -43 9.5t-28 25.5t-15.5 36.5t-4.5 43.5v203q0 22 4.5 42.5t15.5 36.5t28 25.5t43 9.5zM711 431q25 0 42.5 -9.5t28 -25.5t15 -36.5t4.5 -42.5v-203q0 -23 -4.5 -43.5t-15 -36.5t-28 -25.5t-42.5 -9.5t-42.5 9.5t-28 25.5t-15 36.5t-4.5 43.5v203
q0 22 4.5 42.5t15 36.5t28 25.5t42.5 9.5zM468 414q-21 0 -35.5 -8t-22.5 -21.5t-11.5 -31t-3.5 -36.5v-204q0 -18 3.5 -35.5t11.5 -31t22.5 -22t35.5 -8.5t35.5 8.5t22.5 22t11.5 31t3.5 35.5v204q0 19 -3.5 36.5t-11.5 31t-22.5 21.5t-35.5 8zM711 414q-21 0 -35 -8
t-22.5 -21.5t-12 -31t-3.5 -36.5v-204q0 -18 3.5 -35.5t12 -31t22.5 -22t35 -8.5q22 0 36 8.5t22 22t11.5 31t3.5 35.5v204q0 19 -3.5 36.5t-11.5 31t-22 21.5t-36 8z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="385" 
d="M202 341v-149h-18v149h-146v18h146v147h18v-147h145v-18h-145z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="385" 
d="M184 211v132h-145v19h145v144h18v-144h144v-19h-144v-132h144v-19h-307v19h145z" />
    <glyph glyph-name="uni00B5" unicode="&#xb5;" horiz-adv-x="400" 
d="M323 106q-12 -50 -44 -78.5t-83 -28.5t-81 27.5t-40 70.5l1 -188h-22v591h22v-325q0 -32 6.5 -60.5t21 -49.5t37.5 -33.5t57 -12.5t57.5 12.5t38 33.5t21 49.5t6.5 60.5v325h22v-500h-21z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="399" 
d="M209 679q-75 0 -111 -38.5t-36 -113.5v-23q0 -50 27.5 -87.5t79.5 -41.5h130v78h22v-78h42v-20h-42v-260q0 -26 2.5 -49t17.5 -46h-24q-10 17 -13.5 34t-3.5 37q-16 -38 -49 -57t-74 -19q-36 0 -62 12t-42.5 34t-24.5 51.5t-8 64.5v64q0 49 24 89.5t73 55.5
q-50 12 -73.5 52.5t-23.5 88.5v19q0 84 41.5 129t127.5 45h89v-21h-89zM175 355q-54 -5 -83.5 -42t-29.5 -91v-64q0 -30 6.5 -56.5t21 -45.5t37.5 -30t55 -11q51 0 84 30t33 82v228h-124z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="414" 
d="M59 391h-21l159 309h20l159 -309h-21l-148 287z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="688" 
d="M389 726q68 0 117.5 -19.5t81.5 -55.5t47.5 -87.5t15.5 -115.5q0 -29 -3.5 -62.5t-11.5 -66.5t-20.5 -63.5t-30.5 -54t-42 -38t-55 -14.5q-32 0 -46.5 21t-15.5 50q-15 -38 -42.5 -60t-69.5 -22q-32 0 -52 13.5t-31 35t-14 49t0 55.5l10 98q3 29 12 55t24.5 45.5t38.5 31
t53 11.5q76 -2 94 -74l7 68h21l-30 -284q-1 -12 0 -25t6 -23.5t14.5 -17.5t24.5 -7q42 0 69.5 30.5t43 74t21.5 91t6 82.5q0 61 -14.5 109t-44.5 81.5t-76 51.5t-109 18q-89 0 -152 -35.5t-102.5 -95t-57.5 -136t-18 -157.5q0 -146 69.5 -226t218.5 -80q47 0 94 12.5
t85 42.5l-1 -23q-41 -28 -86 -39.5t-93 -11.5q-159 0 -233 86t-74 240q0 84 19.5 163.5t61.5 141.5t108.5 99.5t161.5 37.5zM359 511q-27 0 -47 -10t-33.5 -27t-21 -39.5t-10.5 -47.5l-10 -98q-2 -23 0 -46.5t11 -42.5t26.5 -30.5t45.5 -11.5t48 11.5t33 30t20.5 42.5
t9.5 49l11 97q3 24 0.5 46t-12 39t-27 27.5t-44.5 10.5z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="157" 
d="M89 709v-717h-20v717h20z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="158" 
d="M69 708h21v-299h-21v299zM69 291h21v-299h-21v299z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="752" 
d="M376 697q74 0 136 -26.5t107 -73t70 -110t25 -137.5t-25 -137.5t-70 -110t-107 -73t-136 -26.5t-136 26.5t-107 73t-70 110t-25 137.5t25 137.5t70 110t107 73t136 26.5zM376 680q-71 0 -129.5 -25.5t-101.5 -70t-66.5 -104.5t-23.5 -130t23.5 -130.5t66.5 -105
t101.5 -70t129.5 -25.5q70 0 129 25.5t102 70t66.5 105t23.5 130.5t-23.5 130t-66.5 104.5t-102 70t-129 25.5zM372 569q27 0 46.5 -9.5t31.5 -26.5t17.5 -39.5t5.5 -47.5v-19h-17v20q0 43 -18.5 74t-65.5 31t-66 -31t-19 -74v-194q0 -43 19 -74.5t66 -31.5q24 0 40 8.5
t25.5 23t14 34t4.5 40.5v29h17v-28q0 -26 -5.5 -48.5t-17.5 -39t-31.5 -26.5t-46.5 -10q-28 0 -47.5 10t-32 26.5t-18 39t-5.5 48.5v192q0 25 5.5 47.5t18 39.5t32 26.5t47.5 9.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="392" 
d="M206 492h147v-19h-147v-538h-20v538h-148v19h148v208h20v-208z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="392" 
d="M206 492h147v-19h-147v-309h147v-20h-147v-209h-20v209h-148v20h148v309h-148v19h148v208h20v-208z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="203" 
d="M102 708q26 0 45 -18t19 -45t-19 -45.5t-45 -18.5q-27 0 -46 18.5t-19 45.5t19 45t46 18zM102 693q-21 0 -35.5 -13.5t-14.5 -34.5q0 -22 14.5 -35.5t35.5 -13.5q20 0 35 13.5t15 35.5q0 21 -15 34.5t-35 13.5z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="396" 
d="M171 682v-743h-18v359q-72 4 -99 45t-27 119v75q0 36 5.5 66t19.5 51.5t39.5 33.5t65.5 12h184v-761h-18v743h-152z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="752" 
d="M376 697q74 0 136 -26.5t107 -73t70 -110t25 -137.5t-25 -137.5t-70 -110t-107 -73t-136 -26.5t-136 26.5t-107 73t-70 110t-25 137.5t25 137.5t70 110t107 73t136 26.5zM376 680q-71 0 -129.5 -25.5t-101.5 -70t-66.5 -104.5t-23.5 -130t23.5 -130.5t66.5 -105
t101.5 -70t129.5 -25.5q70 0 129 25.5t102 70t66.5 105t23.5 130.5t-23.5 130t-66.5 104.5t-102 70t-129 25.5zM379 564q55 0 78.5 -30t23.5 -81v-14q0 -38 -15 -65t-53 -37q37 -10 53 -34.5t16 -62.5v-46q0 -14 2 -30.5t11 -27.5h-20q-7 13 -9 27.5t-2 30.5v46
q0 27 -8 44.5t-22.5 27.5t-34 13.5t-43.5 3.5h-56v-193h-18v428h97zM367 346q48 0 72 20t24 71v15q0 45 -18.5 70t-65.5 25h-79v-201h67z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="371" 
d="M189 705q28 0 53.5 -9t44.5 -25t30 -39t11 -52v-12h-21v10q0 25 -9.5 45t-25.5 34t-37.5 21.5t-45.5 7.5q-23 0 -45.5 -5.5t-40 -17.5t-28.5 -31t-12 -45q-1 -34 18.5 -55.5t49.5 -38t65 -31t65 -33.5t50 -46t20 -69q0 -26 -8.5 -47t-23 -38t-34 -29.5t-41.5 -21.5
q22 -10 41.5 -21.5t34.5 -26.5t24 -35t8 -46q-1 -28 -14 -49t-33 -35.5t-46 -22t-52 -7.5q-30 0 -56.5 8t-46.5 23.5t-32 39t-12 54.5v27h21v-25q0 -27 10.5 -47t28 -33.5t40 -20t47.5 -6.5q21 0 42.5 6t39.5 17.5t29.5 29t12.5 41.5q1 35 -19 57.5t-50 39t-65 30.5
t-65 32.5t-50 45.5t-20 69q0 26 10 47.5t26.5 38t38.5 28.5t45 18q-24 11 -46 23t-39 27.5t-27 35.5t-9 47q1 30 13.5 52t33 36.5t46.5 21.5t55 7zM187 444q-23 -4 -45 -13.5t-39.5 -25t-28.5 -36.5t-11 -46q0 -29 13 -50t33.5 -36.5t45 -27t47.5 -21.5q21 7 40.5 18
t34.5 26.5t24 35.5t9 45q0 27 -10.5 46.5t-28 35t-39.5 27t-45 22.5z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="577" 
d="M112 344v340h-96v16h208v-16h-95v-340h-17zM415 350h-18l-107 322v-328h-16v356h24l108 -325l109 325h24v-356h-17v328z" />
    <glyph glyph-name="uni2117" unicode="&#x2117;" horiz-adv-x="752" 
d="M376 698q74 0 136 -26.5t107 -73.5t70 -110.5t25 -136.5q0 -74 -25 -137.5t-70 -110.5t-107 -73.5t-136 -26.5t-136 26.5t-107 73.5t-70 110.5t-25 137.5q0 73 25 136.5t70 110.5t107 73.5t136 26.5zM376 681q-71 0 -129.5 -25.5t-101.5 -70t-66.5 -104.5t-23.5 -130
t23.5 -130.5t66.5 -105t101.5 -70t129.5 -25.5q70 0 129 25.5t102 70t66.5 105t23.5 130.5t-23.5 130t-66.5 104.5t-102 70t-129 25.5zM396 556q57 0 79 -33t22 -84v-27q0 -58 -26.5 -86t-85.5 -28h-68v-170h-18v428h97zM384 315q51 0 73 23.5t22 74.5v25q0 45 -17.5 73
t-65.5 28h-79v-224h67z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="177" 
d="M136 848l-77 -89h-21l73 89h25z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="256" 
d="M219 811q-1 -38 -28 -58t-63 -20t-63 20t-28 58h16q2 -31 24 -46t51 -15q28 0 50.5 15t24.5 46h16z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="250" 
d="M57 848l68 -67l67 67h24l-82 -82h-18l-82 82h23z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="246" 
d="M133 -39q15 0 28.5 -2.5t24 -8.5t16.5 -16t6 -26q0 -17 -7.5 -28t-19.5 -18t-27 -10t-30 -3t-30 3t-27.5 10t-20.5 18.5t-8 27.5v4h18v-3q0 -13 6.5 -22t16.5 -14.5t22 -8.5t23 -3q10 0 22 2.5t21.5 8t16 14.5t6.5 22q0 14 -6 22.5t-15.5 13t-21 6t-23.5 1.5h-5v51h14
v-41z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="250" 
d="M57 766h-23l82 82h18l82 -82h-24l-67 66z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="167" 
d="M74 829v-81h-20v81h20zM114 829v-81h-21v81h21z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="109" 
d="M65 833v-80h-21v80h21z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="177" 
d="M66 848l73 -89h-21l-78 89h26z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="229" 
d="M127 848l-75 -89h-20l70 89h25zM194 848l-75 -89h-19l70 89h24z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="254" 
d="M217 766v-17h-181v17h181z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="188" 
d="M108 0q-9 -7 -18 -17.5t-16 -22.5t-11 -24.5t-3 -23.5q1 -17 12.5 -25t28.5 -8q13 0 25.5 3.5t20.5 11.5v-18q-22 -13 -51 -13q-22 1 -37.5 11.5t-17.5 34.5q-1 12 3 24.5t11 24.5t16.5 23t18.5 19h18z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="206" 
d="M103 875q26 0 44 -17.5t18 -43.5t-18 -44t-44 -18t-44 18t-18 44t18 43.5t44 17.5zM103 860q-20 0 -34 -13t-14 -33q0 -21 14 -34t34 -13t34 13t14 34q0 20 -14 33t-34 13z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="266" 
d="M232 796q-8 -16 -19.5 -27.5t-28.5 -12.5q-13 0 -25 5.5t-23 13t-22.5 13t-23.5 5.5q-14 0 -24 -9t-18 -22l-14 9q9 17 22.5 28t31.5 12q14 0 26.5 -5.5t24 -13t22.5 -13t22 -5.5q12 1 20.5 10.5t14.5 20.5z" />
    <glyph glyph-name="commaaccentcomb" unicode="&#xf6c3;" horiz-adv-x="0" 
d="M57 -175h-11l17 61h-5v80h20v-78z" />
    <glyph glyph-name="uni02BC" unicode="&#x2bc;" horiz-adv-x="115" 
d="M53 552h-11l17 67h-6v81h22v-80z" />
    <glyph glyph-name="Edotaaccent" horiz-adv-x="72" 
 />
    <glyph glyph-name="edotaaccent" horiz-adv-x="72" 
 />
    <glyph glyph-name="lcute" horiz-adv-x="72" 
 />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="447" 
d="M98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="447" 
d="M279 827l-78 -89h-20l72 89h26zM98 165l-41 -165h-20l169 701h31l173 -701h-22l-41 165h-249zM102 185h241l-122 490z" />
    <hkern u1="&#xc2;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc2;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc2;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc2;" u2="v" k="25" />
    <hkern u1="&#xc2;" u2="q" k="1" />
    <hkern u1="&#xc2;" u2="&#x153;" k="1" />
    <hkern u1="&#xc2;" u2="V" k="25" />
    <hkern u1="&#xc2;" u2="Q" k="1" />
    <hkern u1="&#xc2;" u2="&#x152;" k="1" />
    <hkern u1="&#xc4;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc4;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc4;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc4;" u2="v" k="25" />
    <hkern u1="&#xc4;" u2="q" k="1" />
    <hkern u1="&#xc4;" u2="&#x153;" k="1" />
    <hkern u1="&#xc4;" u2="V" k="25" />
    <hkern u1="&#xc4;" u2="Q" k="1" />
    <hkern u1="&#xc4;" u2="&#x152;" k="1" />
    <hkern u1="&#xc0;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc0;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc0;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc0;" u2="v" k="25" />
    <hkern u1="&#xc0;" u2="q" k="1" />
    <hkern u1="&#xc0;" u2="&#x153;" k="1" />
    <hkern u1="&#xc0;" u2="V" k="25" />
    <hkern u1="&#xc0;" u2="Q" k="1" />
    <hkern u1="&#xc0;" u2="&#x152;" k="1" />
    <hkern u1="&#x100;" u2="&#x2018;" k="60" />
    <hkern u1="&#x100;" u2="&#x201d;" k="60" />
    <hkern u1="&#x100;" u2="&#x201c;" k="60" />
    <hkern u1="&#x100;" u2="&#x3f;" k="30" />
    <hkern u1="&#x100;" u2="v" k="25" />
    <hkern u1="&#x100;" u2="q" k="1" />
    <hkern u1="&#x100;" u2="&#x153;" k="1" />
    <hkern u1="&#x100;" u2="V" k="25" />
    <hkern u1="&#x100;" u2="Q" k="1" />
    <hkern u1="&#x100;" u2="&#x152;" k="1" />
    <hkern u1="&#x104;" u2="&#x2018;" k="60" />
    <hkern u1="&#x104;" u2="&#x201d;" k="60" />
    <hkern u1="&#x104;" u2="&#x201c;" k="60" />
    <hkern u1="&#x104;" u2="&#x3f;" k="30" />
    <hkern u1="&#x104;" u2="v" k="25" />
    <hkern u1="&#x104;" u2="q" k="1" />
    <hkern u1="&#x104;" u2="&#x153;" k="1" />
    <hkern u1="&#x104;" u2="V" k="25" />
    <hkern u1="&#x104;" u2="Q" k="1" />
    <hkern u1="&#x104;" u2="&#x152;" k="1" />
    <hkern u1="&#xc5;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc5;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc5;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc5;" u2="v" k="25" />
    <hkern u1="&#xc5;" u2="q" k="1" />
    <hkern u1="&#xc5;" u2="&#x153;" k="1" />
    <hkern u1="&#xc5;" u2="V" k="25" />
    <hkern u1="&#xc5;" u2="Q" k="1" />
    <hkern u1="&#xc5;" u2="&#x152;" k="1" />
    <hkern u1="&#xc3;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc3;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc3;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc3;" u2="v" k="25" />
    <hkern u1="&#xc3;" u2="q" k="1" />
    <hkern u1="&#xc3;" u2="&#x153;" k="1" />
    <hkern u1="&#xc3;" u2="V" k="25" />
    <hkern u1="&#xc3;" u2="Q" k="1" />
    <hkern u1="&#xc3;" u2="&#x152;" k="1" />
    <hkern u1="B" u2="&#x201a;" k="5" />
    <hkern u1="B" u2="&#x2018;" k="10" />
    <hkern u1="B" u2="&#x201d;" k="10" />
    <hkern u1="B" u2="&#x201c;" k="10" />
    <hkern u1="B" u2="&#x201e;" k="5" />
    <hkern u1="B" u2="&#x2e;" k="5" />
    <hkern u1="B" u2="&#x2026;" k="5" />
    <hkern u1="B" u2="&#x2c;" k="5" />
    <hkern u1="B" u2="x" k="3" />
    <hkern u1="B" u2="v" k="4" />
    <hkern u1="B" u2="X" k="3" />
    <hkern u1="B" u2="V" k="4" />
    <hkern u1="C" u2="&#x201a;" k="6" />
    <hkern u1="C" u2="&#x2018;" k="6" />
    <hkern u1="C" u2="&#x201d;" k="6" />
    <hkern u1="C" u2="&#x201c;" k="6" />
    <hkern u1="C" u2="&#x201e;" k="6" />
    <hkern u1="C" u2="&#x2e;" k="6" />
    <hkern u1="C" u2="&#x2026;" k="6" />
    <hkern u1="C" u2="&#x2c;" k="6" />
    <hkern u1="C" u2="x" k="11" />
    <hkern u1="C" u2="X" k="11" />
    <hkern u1="&#x106;" u2="&#x201a;" k="6" />
    <hkern u1="&#x106;" u2="&#x2018;" k="6" />
    <hkern u1="&#x106;" u2="&#x201d;" k="6" />
    <hkern u1="&#x106;" u2="&#x201c;" k="6" />
    <hkern u1="&#x106;" u2="&#x201e;" k="6" />
    <hkern u1="&#x106;" u2="&#x2e;" k="6" />
    <hkern u1="&#x106;" u2="&#x2026;" k="6" />
    <hkern u1="&#x106;" u2="&#x2c;" k="6" />
    <hkern u1="&#x106;" u2="x" k="11" />
    <hkern u1="&#x106;" u2="X" k="11" />
    <hkern u1="&#x10c;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10c;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10c;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10c;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10c;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10c;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10c;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10c;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10c;" u2="x" k="11" />
    <hkern u1="&#x10c;" u2="X" k="11" />
    <hkern u1="&#xc7;" u2="&#x201a;" k="6" />
    <hkern u1="&#xc7;" u2="&#x2018;" k="6" />
    <hkern u1="&#xc7;" u2="&#x201d;" k="6" />
    <hkern u1="&#xc7;" u2="&#x201c;" k="6" />
    <hkern u1="&#xc7;" u2="&#x201e;" k="6" />
    <hkern u1="&#xc7;" u2="&#x2e;" k="6" />
    <hkern u1="&#xc7;" u2="&#x2026;" k="6" />
    <hkern u1="&#xc7;" u2="&#x2c;" k="6" />
    <hkern u1="&#xc7;" u2="x" k="11" />
    <hkern u1="&#xc7;" u2="X" k="11" />
    <hkern u1="&#x108;" u2="&#x201a;" k="6" />
    <hkern u1="&#x108;" u2="&#x2018;" k="6" />
    <hkern u1="&#x108;" u2="&#x201d;" k="6" />
    <hkern u1="&#x108;" u2="&#x201c;" k="6" />
    <hkern u1="&#x108;" u2="&#x201e;" k="6" />
    <hkern u1="&#x108;" u2="&#x2e;" k="6" />
    <hkern u1="&#x108;" u2="&#x2026;" k="6" />
    <hkern u1="&#x108;" u2="&#x2c;" k="6" />
    <hkern u1="&#x108;" u2="x" k="11" />
    <hkern u1="&#x108;" u2="X" k="11" />
    <hkern u1="&#x10a;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10a;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10a;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10a;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10a;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10a;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10a;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10a;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10a;" u2="x" k="11" />
    <hkern u1="&#x10a;" u2="X" k="11" />
    <hkern u1="D" u2="&#x201a;" k="10" />
    <hkern u1="D" u2="&#x2018;" k="10" />
    <hkern u1="D" u2="&#x201d;" k="10" />
    <hkern u1="D" u2="&#x201c;" k="10" />
    <hkern u1="D" u2="&#x201e;" k="10" />
    <hkern u1="D" u2="&#x2e;" k="10" />
    <hkern u1="D" u2="&#x2026;" k="10" />
    <hkern u1="D" u2="&#x2c;" k="10" />
    <hkern u1="D" u2="x" k="15" />
    <hkern u1="D" u2="v" k="1" />
    <hkern u1="D" u2="X" k="15" />
    <hkern u1="D" u2="V" k="1" />
    <hkern u1="&#xd0;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd0;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd0;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd0;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd0;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd0;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd0;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd0;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd0;" u2="x" k="15" />
    <hkern u1="&#xd0;" u2="v" k="1" />
    <hkern u1="&#xd0;" u2="X" k="15" />
    <hkern u1="&#xd0;" u2="V" k="1" />
    <hkern u1="&#x10e;" u2="&#x201a;" k="10" />
    <hkern u1="&#x10e;" u2="&#x2018;" k="10" />
    <hkern u1="&#x10e;" u2="&#x201d;" k="10" />
    <hkern u1="&#x10e;" u2="&#x201c;" k="10" />
    <hkern u1="&#x10e;" u2="&#x201e;" k="10" />
    <hkern u1="&#x10e;" u2="&#x2e;" k="10" />
    <hkern u1="&#x10e;" u2="&#x2026;" k="10" />
    <hkern u1="&#x10e;" u2="&#x2c;" k="10" />
    <hkern u1="&#x10e;" u2="x" k="15" />
    <hkern u1="&#x10e;" u2="v" k="1" />
    <hkern u1="&#x10e;" u2="X" k="15" />
    <hkern u1="&#x10e;" u2="V" k="1" />
    <hkern u1="&#x110;" u2="&#x201a;" k="10" />
    <hkern u1="&#x110;" u2="&#x2018;" k="10" />
    <hkern u1="&#x110;" u2="&#x201d;" k="10" />
    <hkern u1="&#x110;" u2="&#x201c;" k="10" />
    <hkern u1="&#x110;" u2="&#x201e;" k="10" />
    <hkern u1="&#x110;" u2="&#x2e;" k="10" />
    <hkern u1="&#x110;" u2="&#x2026;" k="10" />
    <hkern u1="&#x110;" u2="&#x2c;" k="10" />
    <hkern u1="&#x110;" u2="x" k="15" />
    <hkern u1="&#x110;" u2="v" k="1" />
    <hkern u1="&#x110;" u2="X" k="15" />
    <hkern u1="&#x110;" u2="V" k="1" />
    <hkern u1="F" u2="&#x201a;" k="70" />
    <hkern u1="F" u2="&#x201e;" k="70" />
    <hkern u1="F" u2="&#x29;" k="-5" />
    <hkern u1="F" u2="]" k="-5" />
    <hkern u1="F" u2="&#x7d;" k="-5" />
    <hkern u1="F" u2="&#x2e;" k="70" />
    <hkern u1="F" u2="&#x2026;" k="70" />
    <hkern u1="F" u2="&#x2c;" k="70" />
    <hkern u1="G" u2="&#x201a;" k="10" />
    <hkern u1="G" u2="&#x2018;" k="10" />
    <hkern u1="G" u2="&#x201d;" k="10" />
    <hkern u1="G" u2="&#x201c;" k="10" />
    <hkern u1="G" u2="&#x201e;" k="10" />
    <hkern u1="G" u2="&#x2e;" k="10" />
    <hkern u1="G" u2="&#x2026;" k="10" />
    <hkern u1="G" u2="&#x2c;" k="10" />
    <hkern u1="G" u2="x" k="15" />
    <hkern u1="G" u2="v" k="1" />
    <hkern u1="G" u2="X" k="15" />
    <hkern u1="G" u2="V" k="1" />
    <hkern u1="&#x11e;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11e;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11e;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11e;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11e;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11e;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11e;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11e;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11e;" u2="x" k="15" />
    <hkern u1="&#x11e;" u2="v" k="1" />
    <hkern u1="&#x11e;" u2="X" k="15" />
    <hkern u1="&#x11e;" u2="V" k="1" />
    <hkern u1="&#x11c;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11c;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11c;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11c;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11c;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11c;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11c;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11c;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11c;" u2="x" k="15" />
    <hkern u1="&#x11c;" u2="v" k="1" />
    <hkern u1="&#x11c;" u2="X" k="15" />
    <hkern u1="&#x11c;" u2="V" k="1" />
    <hkern u1="&#x120;" u2="&#x201a;" k="10" />
    <hkern u1="&#x120;" u2="&#x2018;" k="10" />
    <hkern u1="&#x120;" u2="&#x201d;" k="10" />
    <hkern u1="&#x120;" u2="&#x201c;" k="10" />
    <hkern u1="&#x120;" u2="&#x201e;" k="10" />
    <hkern u1="&#x120;" u2="&#x2e;" k="10" />
    <hkern u1="&#x120;" u2="&#x2026;" k="10" />
    <hkern u1="&#x120;" u2="&#x2c;" k="10" />
    <hkern u1="&#x120;" u2="x" k="15" />
    <hkern u1="&#x120;" u2="v" k="1" />
    <hkern u1="&#x120;" u2="X" k="15" />
    <hkern u1="&#x120;" u2="V" k="1" />
    <hkern u1="J" u2="&#x201a;" k="6" />
    <hkern u1="J" u2="&#x201e;" k="6" />
    <hkern u1="J" u2="&#x2e;" k="6" />
    <hkern u1="J" u2="&#x2026;" k="6" />
    <hkern u1="J" u2="&#x2c;" k="6" />
    <hkern u1="&#x134;" u2="&#x201a;" k="6" />
    <hkern u1="&#x134;" u2="&#x201e;" k="6" />
    <hkern u1="&#x134;" u2="&#x2e;" k="6" />
    <hkern u1="&#x134;" u2="&#x2026;" k="6" />
    <hkern u1="&#x134;" u2="&#x2c;" k="6" />
    <hkern u1="K" u2="q" k="7" />
    <hkern u1="K" u2="&#x153;" k="7" />
    <hkern u1="K" u2="Q" k="7" />
    <hkern u1="K" u2="&#x152;" k="7" />
    <hkern u1="L" u2="&#x2018;" k="75" />
    <hkern u1="L" u2="&#x201d;" k="75" />
    <hkern u1="L" u2="&#x201c;" k="75" />
    <hkern u1="L" u2="&#x29;" k="-8" />
    <hkern u1="L" u2="]" k="-8" />
    <hkern u1="L" u2="&#x7d;" k="-8" />
    <hkern u1="L" u2="&#x3f;" k="30" />
    <hkern u1="L" u2="v" k="33" />
    <hkern u1="L" u2="V" k="33" />
    <hkern u1="&#x139;" u2="&#x2018;" k="75" />
    <hkern u1="&#x139;" u2="&#x201d;" k="75" />
    <hkern u1="&#x139;" u2="&#x201c;" k="75" />
    <hkern u1="&#x139;" u2="&#x29;" k="-8" />
    <hkern u1="&#x139;" u2="]" k="-8" />
    <hkern u1="&#x139;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x139;" u2="&#x3f;" k="30" />
    <hkern u1="&#x139;" u2="v" k="33" />
    <hkern u1="&#x139;" u2="V" k="33" />
    <hkern u1="&#x13f;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13f;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13f;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13f;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13f;" u2="]" k="-8" />
    <hkern u1="&#x13f;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13f;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13f;" u2="v" k="33" />
    <hkern u1="&#x13f;" u2="V" k="33" />
    <hkern u1="&#x141;" u2="&#x2018;" k="75" />
    <hkern u1="&#x141;" u2="&#x201d;" k="75" />
    <hkern u1="&#x141;" u2="&#x201c;" k="75" />
    <hkern u1="&#x141;" u2="&#x29;" k="-8" />
    <hkern u1="&#x141;" u2="]" k="-8" />
    <hkern u1="&#x141;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x141;" u2="&#x3f;" k="30" />
    <hkern u1="&#x141;" u2="v" k="33" />
    <hkern u1="&#x141;" u2="V" k="33" />
    <hkern u1="O" u2="&#x201a;" k="10" />
    <hkern u1="O" u2="&#x2018;" k="10" />
    <hkern u1="O" u2="&#x201d;" k="10" />
    <hkern u1="O" u2="&#x201c;" k="10" />
    <hkern u1="O" u2="&#x201e;" k="10" />
    <hkern u1="O" u2="&#x2e;" k="10" />
    <hkern u1="O" u2="&#x2026;" k="10" />
    <hkern u1="O" u2="&#x2c;" k="10" />
    <hkern u1="O" u2="x" k="15" />
    <hkern u1="O" u2="v" k="1" />
    <hkern u1="O" u2="X" k="15" />
    <hkern u1="O" u2="V" k="1" />
    <hkern u1="&#xd3;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd3;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd3;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd3;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd3;" u2="x" k="15" />
    <hkern u1="&#xd3;" u2="v" k="1" />
    <hkern u1="&#xd3;" u2="X" k="15" />
    <hkern u1="&#xd3;" u2="V" k="1" />
    <hkern u1="&#x14e;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14e;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14e;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14e;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14e;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14e;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14e;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14e;" u2="&#x2c;" k="10" />
    <hkern u1="&#x14e;" u2="x" k="15" />
    <hkern u1="&#x14e;" u2="v" k="1" />
    <hkern u1="&#x14e;" u2="X" k="15" />
    <hkern u1="&#x14e;" u2="V" k="1" />
    <hkern u1="&#xd4;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd4;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd4;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd4;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd4;" u2="x" k="15" />
    <hkern u1="&#xd4;" u2="v" k="1" />
    <hkern u1="&#xd4;" u2="X" k="15" />
    <hkern u1="&#xd4;" u2="V" k="1" />
    <hkern u1="&#xd6;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd6;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd6;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd6;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd6;" u2="x" k="15" />
    <hkern u1="&#xd6;" u2="v" k="1" />
    <hkern u1="&#xd6;" u2="X" k="15" />
    <hkern u1="&#xd6;" u2="V" k="1" />
    <hkern u1="&#xd2;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd2;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd2;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd2;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd2;" u2="x" k="15" />
    <hkern u1="&#xd2;" u2="v" k="1" />
    <hkern u1="&#xd2;" u2="X" k="15" />
    <hkern u1="&#xd2;" u2="V" k="1" />
    <hkern u1="&#x150;" u2="&#x201a;" k="10" />
    <hkern u1="&#x150;" u2="&#x2018;" k="10" />
    <hkern u1="&#x150;" u2="&#x201d;" k="10" />
    <hkern u1="&#x150;" u2="&#x201c;" k="10" />
    <hkern u1="&#x150;" u2="&#x201e;" k="10" />
    <hkern u1="&#x150;" u2="&#x2e;" k="10" />
    <hkern u1="&#x150;" u2="&#x2026;" k="10" />
    <hkern u1="&#x150;" u2="&#x2c;" k="10" />
    <hkern u1="&#x150;" u2="x" k="15" />
    <hkern u1="&#x150;" u2="v" k="1" />
    <hkern u1="&#x150;" u2="X" k="15" />
    <hkern u1="&#x150;" u2="V" k="1" />
    <hkern u1="&#x14c;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14c;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14c;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14c;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14c;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14c;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14c;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14c;" u2="&#x2c;" k="10" />
    <hkern u1="&#x14c;" u2="x" k="15" />
    <hkern u1="&#x14c;" u2="v" k="1" />
    <hkern u1="&#x14c;" u2="X" k="15" />
    <hkern u1="&#x14c;" u2="V" k="1" />
    <hkern u1="&#xd8;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd8;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd8;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd8;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd8;" u2="x" k="15" />
    <hkern u1="&#xd8;" u2="v" k="1" />
    <hkern u1="&#xd8;" u2="X" k="15" />
    <hkern u1="&#xd8;" u2="V" k="1" />
    <hkern u1="&#x1fe;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x2c;" k="10" />
    <hkern u1="&#x1fe;" u2="x" k="15" />
    <hkern u1="&#x1fe;" u2="v" k="1" />
    <hkern u1="&#x1fe;" u2="X" k="15" />
    <hkern u1="&#x1fe;" u2="V" k="1" />
    <hkern u1="&#xd5;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd5;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd5;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd5;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd5;" u2="x" k="15" />
    <hkern u1="&#xd5;" u2="v" k="1" />
    <hkern u1="&#xd5;" u2="X" k="15" />
    <hkern u1="&#xd5;" u2="V" k="1" />
    <hkern u1="P" u2="&#x201a;" k="80" />
    <hkern u1="P" u2="&#x201e;" k="80" />
    <hkern u1="P" u2="&#x2e;" k="80" />
    <hkern u1="P" u2="&#x2026;" k="80" />
    <hkern u1="P" u2="&#x2c;" k="80" />
    <hkern u1="P" u2="x" k="11" />
    <hkern u1="P" u2="X" k="11" />
    <hkern u1="&#xde;" u2="&#xc1;" k="2" />
    <hkern u1="&#xde;" u2="A" k="2" />
    <hkern u1="&#xde;" u2="&#x102;" k="2" />
    <hkern u1="&#xde;" u2="&#x201a;" k="80" />
    <hkern u1="&#xde;" u2="&#x201e;" k="80" />
    <hkern u1="&#xde;" u2="&#x2e;" k="80" />
    <hkern u1="&#xde;" u2="&#x2026;" k="80" />
    <hkern u1="&#xde;" u2="&#x2c;" k="80" />
    <hkern u1="&#xde;" u2="&#x17c;" k="10" />
    <hkern u1="&#xde;" u2="&#x17e;" k="10" />
    <hkern u1="&#xde;" u2="&#x17a;" k="10" />
    <hkern u1="&#xde;" u2="z" k="10" />
    <hkern u1="&#xde;" u2="&#x1ef3;" k="9" />
    <hkern u1="&#xde;" u2="&#xff;" k="9" />
    <hkern u1="&#xde;" u2="&#x177;" k="9" />
    <hkern u1="&#xde;" u2="&#xfd;" k="9" />
    <hkern u1="&#xde;" u2="y" k="9" />
    <hkern u1="&#xde;" u2="x" k="20" />
    <hkern u1="&#xde;" u2="&#x135;" k="8" />
    <hkern u1="&#xde;" u2="&#x237;" k="8" />
    <hkern u1="&#xde;" u2="j" k="8" />
    <hkern u1="&#xde;" u2="&#x133;" k="8" />
    <hkern u1="&#xde;" u2="&#x1fd;" k="20" />
    <hkern u1="&#xde;" u2="&#xe6;" k="20" />
    <hkern u1="&#xde;" u2="&#xe3;" k="2" />
    <hkern u1="&#xde;" u2="&#xe5;" k="2" />
    <hkern u1="&#xde;" u2="&#x105;" k="2" />
    <hkern u1="&#xde;" u2="&#x101;" k="2" />
    <hkern u1="&#xde;" u2="&#xe0;" k="2" />
    <hkern u1="&#xde;" u2="&#xe4;" k="2" />
    <hkern u1="&#xde;" u2="&#xe2;" k="2" />
    <hkern u1="&#xde;" u2="&#x103;" k="2" />
    <hkern u1="&#xde;" u2="&#xe1;" k="2" />
    <hkern u1="&#xde;" u2="a" k="2" />
    <hkern u1="&#xde;" u2="&#x17b;" k="10" />
    <hkern u1="&#xde;" u2="&#x17d;" k="10" />
    <hkern u1="&#xde;" u2="&#x179;" k="10" />
    <hkern u1="&#xde;" u2="Z" k="10" />
    <hkern u1="&#xde;" u2="&#x1ef2;" k="9" />
    <hkern u1="&#xde;" u2="&#x178;" k="9" />
    <hkern u1="&#xde;" u2="&#x176;" k="9" />
    <hkern u1="&#xde;" u2="&#xdd;" k="9" />
    <hkern u1="&#xde;" u2="Y" k="9" />
    <hkern u1="&#xde;" u2="X" k="20" />
    <hkern u1="&#xde;" u2="&#x134;" k="8" />
    <hkern u1="&#xde;" u2="J" k="8" />
    <hkern u1="&#xde;" u2="&#x132;" k="8" />
    <hkern u1="&#xde;" u2="&#x1fc;" k="20" />
    <hkern u1="&#xde;" u2="&#xc6;" k="20" />
    <hkern u1="&#xde;" u2="&#xc3;" k="2" />
    <hkern u1="&#xde;" u2="&#xc5;" k="2" />
    <hkern u1="&#xde;" u2="&#x104;" k="2" />
    <hkern u1="&#xde;" u2="&#x100;" k="2" />
    <hkern u1="&#xde;" u2="&#xc0;" k="2" />
    <hkern u1="&#xde;" u2="&#xc4;" k="2" />
    <hkern u1="&#xde;" u2="&#xc2;" k="2" />
    <hkern u1="Q" u2="&#x2bc;" k="10" />
    <hkern u1="Q" u2="&#x2019;" k="10" />
    <hkern u1="Q" u2="&#x2018;" k="10" />
    <hkern u1="Q" u2="&#x201d;" k="10" />
    <hkern u1="Q" u2="&#x201c;" k="10" />
    <hkern u1="Q" u2="&#x1ef3;" k="13" />
    <hkern u1="Q" u2="&#xff;" k="13" />
    <hkern u1="Q" u2="&#x177;" k="13" />
    <hkern u1="Q" u2="&#xfd;" k="13" />
    <hkern u1="Q" u2="y" k="13" />
    <hkern u1="Q" u2="v" k="1" />
    <hkern u1="Q" u2="&#x1ef2;" k="13" />
    <hkern u1="Q" u2="&#x178;" k="13" />
    <hkern u1="Q" u2="&#x176;" k="13" />
    <hkern u1="Q" u2="&#xdd;" k="13" />
    <hkern u1="Q" u2="Y" k="13" />
    <hkern u1="Q" u2="V" k="1" />
    <hkern u1="R" u2="v" k="2" />
    <hkern u1="R" u2="V" k="2" />
    <hkern u1="&#x154;" u2="v" k="2" />
    <hkern u1="&#x154;" u2="V" k="2" />
    <hkern u1="&#x158;" u2="v" k="2" />
    <hkern u1="&#x158;" u2="V" k="2" />
    <hkern u1="S" u2="&#x201a;" k="5" />
    <hkern u1="S" u2="&#x2018;" k="5" />
    <hkern u1="S" u2="&#x201d;" k="5" />
    <hkern u1="S" u2="&#x201c;" k="5" />
    <hkern u1="S" u2="&#x201e;" k="5" />
    <hkern u1="S" u2="&#x2e;" k="5" />
    <hkern u1="S" u2="&#x2026;" k="5" />
    <hkern u1="S" u2="&#x2c;" k="5" />
    <hkern u1="S" u2="x" k="8" />
    <hkern u1="S" u2="v" k="1" />
    <hkern u1="S" u2="X" k="8" />
    <hkern u1="S" u2="V" k="1" />
    <hkern u1="&#x15a;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15a;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15a;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15a;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15a;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15a;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15a;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15a;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15a;" u2="x" k="8" />
    <hkern u1="&#x15a;" u2="v" k="1" />
    <hkern u1="&#x15a;" u2="X" k="8" />
    <hkern u1="&#x15a;" u2="V" k="1" />
    <hkern u1="&#x160;" u2="&#x201a;" k="5" />
    <hkern u1="&#x160;" u2="&#x2018;" k="5" />
    <hkern u1="&#x160;" u2="&#x201d;" k="5" />
    <hkern u1="&#x160;" u2="&#x201c;" k="5" />
    <hkern u1="&#x160;" u2="&#x201e;" k="5" />
    <hkern u1="&#x160;" u2="&#x2e;" k="5" />
    <hkern u1="&#x160;" u2="&#x2026;" k="5" />
    <hkern u1="&#x160;" u2="&#x2c;" k="5" />
    <hkern u1="&#x160;" u2="x" k="8" />
    <hkern u1="&#x160;" u2="v" k="1" />
    <hkern u1="&#x160;" u2="X" k="8" />
    <hkern u1="&#x160;" u2="V" k="1" />
    <hkern u1="&#x15e;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15e;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15e;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15e;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15e;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15e;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15e;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15e;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15e;" u2="x" k="8" />
    <hkern u1="&#x15e;" u2="v" k="1" />
    <hkern u1="&#x15e;" u2="X" k="8" />
    <hkern u1="&#x15e;" u2="V" k="1" />
    <hkern u1="&#x15c;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15c;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15c;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15c;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15c;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15c;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15c;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15c;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15c;" u2="x" k="8" />
    <hkern u1="&#x15c;" u2="v" k="1" />
    <hkern u1="&#x15c;" u2="X" k="8" />
    <hkern u1="&#x15c;" u2="V" k="1" />
    <hkern u1="T" u2="&#x201a;" k="60" />
    <hkern u1="T" u2="&#x201e;" k="60" />
    <hkern u1="T" u2="&#x29;" k="-8" />
    <hkern u1="T" u2="]" k="-8" />
    <hkern u1="T" u2="&#x7d;" k="-8" />
    <hkern u1="T" u2="&#x3b;" k="35" />
    <hkern u1="T" u2="&#x2e;" k="60" />
    <hkern u1="T" u2="&#x2026;" k="60" />
    <hkern u1="T" u2="&#x2c;" k="60" />
    <hkern u1="T" u2="&#x3a;" k="35" />
    <hkern u1="&#x166;" u2="&#x201a;" k="60" />
    <hkern u1="&#x166;" u2="&#x201e;" k="60" />
    <hkern u1="&#x166;" u2="&#x29;" k="-8" />
    <hkern u1="&#x166;" u2="]" k="-8" />
    <hkern u1="&#x166;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x166;" u2="&#x3b;" k="35" />
    <hkern u1="&#x166;" u2="&#x2e;" k="60" />
    <hkern u1="&#x166;" u2="&#x2026;" k="60" />
    <hkern u1="&#x166;" u2="&#x2c;" k="60" />
    <hkern u1="&#x166;" u2="&#x3a;" k="35" />
    <hkern u1="&#x164;" u2="&#x201a;" k="60" />
    <hkern u1="&#x164;" u2="&#x201e;" k="60" />
    <hkern u1="&#x164;" u2="&#x29;" k="-8" />
    <hkern u1="&#x164;" u2="]" k="-8" />
    <hkern u1="&#x164;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x164;" u2="&#x3b;" k="35" />
    <hkern u1="&#x164;" u2="&#x2e;" k="60" />
    <hkern u1="&#x164;" u2="&#x2026;" k="60" />
    <hkern u1="&#x164;" u2="&#x2c;" k="60" />
    <hkern u1="&#x164;" u2="&#x3a;" k="35" />
    <hkern u1="U" u2="&#x201a;" k="6" />
    <hkern u1="U" u2="&#x201e;" k="6" />
    <hkern u1="U" u2="&#x2e;" k="6" />
    <hkern u1="U" u2="&#x2026;" k="6" />
    <hkern u1="U" u2="&#x2c;" k="6" />
    <hkern u1="&#xda;" u2="&#x201a;" k="6" />
    <hkern u1="&#xda;" u2="&#x201e;" k="6" />
    <hkern u1="&#xda;" u2="&#x2e;" k="6" />
    <hkern u1="&#xda;" u2="&#x2026;" k="6" />
    <hkern u1="&#xda;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16c;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16c;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16c;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16c;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16c;" u2="&#x2c;" k="6" />
    <hkern u1="&#xdb;" u2="&#x201a;" k="6" />
    <hkern u1="&#xdb;" u2="&#x201e;" k="6" />
    <hkern u1="&#xdb;" u2="&#x2e;" k="6" />
    <hkern u1="&#xdb;" u2="&#x2026;" k="6" />
    <hkern u1="&#xdb;" u2="&#x2c;" k="6" />
    <hkern u1="&#xdc;" u2="&#x201a;" k="6" />
    <hkern u1="&#xdc;" u2="&#x201e;" k="6" />
    <hkern u1="&#xdc;" u2="&#x2e;" k="6" />
    <hkern u1="&#xdc;" u2="&#x2026;" k="6" />
    <hkern u1="&#xdc;" u2="&#x2c;" k="6" />
    <hkern u1="&#xd9;" u2="&#x201a;" k="6" />
    <hkern u1="&#xd9;" u2="&#x201e;" k="6" />
    <hkern u1="&#xd9;" u2="&#x2e;" k="6" />
    <hkern u1="&#xd9;" u2="&#x2026;" k="6" />
    <hkern u1="&#xd9;" u2="&#x2c;" k="6" />
    <hkern u1="&#x170;" u2="&#x201a;" k="6" />
    <hkern u1="&#x170;" u2="&#x201e;" k="6" />
    <hkern u1="&#x170;" u2="&#x2e;" k="6" />
    <hkern u1="&#x170;" u2="&#x2026;" k="6" />
    <hkern u1="&#x170;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16a;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16a;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16a;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16a;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16a;" u2="&#x2c;" k="6" />
    <hkern u1="&#x172;" u2="&#x201a;" k="6" />
    <hkern u1="&#x172;" u2="&#x201e;" k="6" />
    <hkern u1="&#x172;" u2="&#x2e;" k="6" />
    <hkern u1="&#x172;" u2="&#x2026;" k="6" />
    <hkern u1="&#x172;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16e;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16e;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16e;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16e;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16e;" u2="&#x2c;" k="6" />
    <hkern u1="&#x168;" u2="&#x201a;" k="6" />
    <hkern u1="&#x168;" u2="&#x201e;" k="6" />
    <hkern u1="&#x168;" u2="&#x2e;" k="6" />
    <hkern u1="&#x168;" u2="&#x2026;" k="6" />
    <hkern u1="&#x168;" u2="&#x2c;" k="6" />
    <hkern u1="V" u2="&#xc1;" k="25" />
    <hkern u1="V" u2="A" k="25" />
    <hkern u1="V" u2="&#x102;" k="25" />
    <hkern u1="V" u2="&#x201a;" k="60" />
    <hkern u1="V" u2="&#x201e;" k="60" />
    <hkern u1="V" u2="&#xad;" k="10" />
    <hkern u1="V" u2="&#x2d;" k="10" />
    <hkern u1="V" u2="&#x29;" k="-3" />
    <hkern u1="V" u2="]" k="-3" />
    <hkern u1="V" u2="&#x7d;" k="-3" />
    <hkern u1="V" u2="&#x2e;" k="60" />
    <hkern u1="V" u2="&#x2026;" k="60" />
    <hkern u1="V" u2="&#x2c;" k="60" />
    <hkern u1="V" u2="&#x123;" k="1" />
    <hkern u1="V" u2="q" k="1" />
    <hkern u1="V" u2="&#x153;" k="1" />
    <hkern u1="V" u2="&#xf5;" k="1" />
    <hkern u1="V" u2="&#x1ff;" k="1" />
    <hkern u1="V" u2="&#xf8;" k="1" />
    <hkern u1="V" u2="&#x14d;" k="1" />
    <hkern u1="V" u2="&#x151;" k="1" />
    <hkern u1="V" u2="&#xf2;" k="1" />
    <hkern u1="V" u2="&#xf6;" k="1" />
    <hkern u1="V" u2="&#xf4;" k="1" />
    <hkern u1="V" u2="&#x14f;" k="1" />
    <hkern u1="V" u2="&#xf3;" k="1" />
    <hkern u1="V" u2="o" k="1" />
    <hkern u1="V" u2="&#x135;" k="25" />
    <hkern u1="V" u2="&#x237;" k="25" />
    <hkern u1="V" u2="j" k="25" />
    <hkern u1="V" u2="&#x133;" k="25" />
    <hkern u1="V" u2="&#x121;" k="1" />
    <hkern u1="V" u2="&#x11d;" k="1" />
    <hkern u1="V" u2="&#x11f;" k="1" />
    <hkern u1="V" u2="g" k="1" />
    <hkern u1="V" u2="&#x10b;" k="1" />
    <hkern u1="V" u2="&#x109;" k="1" />
    <hkern u1="V" u2="&#xe7;" k="1" />
    <hkern u1="V" u2="&#x10d;" k="1" />
    <hkern u1="V" u2="&#x107;" k="1" />
    <hkern u1="V" u2="c" k="1" />
    <hkern u1="V" u2="&#x1fd;" k="46" />
    <hkern u1="V" u2="&#xe6;" k="46" />
    <hkern u1="V" u2="&#xe3;" k="25" />
    <hkern u1="V" u2="&#xe5;" k="25" />
    <hkern u1="V" u2="&#x105;" k="25" />
    <hkern u1="V" u2="&#x101;" k="25" />
    <hkern u1="V" u2="&#xe0;" k="25" />
    <hkern u1="V" u2="&#xe4;" k="25" />
    <hkern u1="V" u2="&#xe2;" k="25" />
    <hkern u1="V" u2="&#x103;" k="25" />
    <hkern u1="V" u2="&#xe1;" k="25" />
    <hkern u1="V" u2="a" k="25" />
    <hkern u1="V" u2="&#x122;" k="1" />
    <hkern u1="V" u2="Q" k="1" />
    <hkern u1="V" u2="&#x152;" k="1" />
    <hkern u1="V" u2="&#xd5;" k="1" />
    <hkern u1="V" u2="&#x1fe;" k="1" />
    <hkern u1="V" u2="&#xd8;" k="1" />
    <hkern u1="V" u2="&#x14c;" k="1" />
    <hkern u1="V" u2="&#x150;" k="1" />
    <hkern u1="V" u2="&#xd2;" k="1" />
    <hkern u1="V" u2="&#xd6;" k="1" />
    <hkern u1="V" u2="&#xd4;" k="1" />
    <hkern u1="V" u2="&#x14e;" k="1" />
    <hkern u1="V" u2="&#xd3;" k="1" />
    <hkern u1="V" u2="O" k="1" />
    <hkern u1="V" u2="&#x134;" k="25" />
    <hkern u1="V" u2="J" k="25" />
    <hkern u1="V" u2="&#x132;" k="25" />
    <hkern u1="V" u2="&#x120;" k="1" />
    <hkern u1="V" u2="&#x11c;" k="1" />
    <hkern u1="V" u2="&#x11e;" k="1" />
    <hkern u1="V" u2="G" k="1" />
    <hkern u1="V" u2="&#x10a;" k="1" />
    <hkern u1="V" u2="&#x108;" k="1" />
    <hkern u1="V" u2="&#xc7;" k="1" />
    <hkern u1="V" u2="&#x10c;" k="1" />
    <hkern u1="V" u2="&#x106;" k="1" />
    <hkern u1="V" u2="C" k="1" />
    <hkern u1="V" u2="&#x1fc;" k="46" />
    <hkern u1="V" u2="&#xc6;" k="46" />
    <hkern u1="V" u2="&#xc3;" k="25" />
    <hkern u1="V" u2="&#xc5;" k="25" />
    <hkern u1="V" u2="&#x104;" k="25" />
    <hkern u1="V" u2="&#x100;" k="25" />
    <hkern u1="V" u2="&#xc0;" k="25" />
    <hkern u1="V" u2="&#xc4;" k="25" />
    <hkern u1="V" u2="&#xc2;" k="25" />
    <hkern u1="W" u2="&#x201a;" k="40" />
    <hkern u1="W" u2="&#x201e;" k="40" />
    <hkern u1="W" u2="&#x29;" k="-3" />
    <hkern u1="W" u2="]" k="-3" />
    <hkern u1="W" u2="&#x7d;" k="-3" />
    <hkern u1="W" u2="&#x2e;" k="40" />
    <hkern u1="W" u2="&#x2026;" k="40" />
    <hkern u1="W" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e82;" u2="]" k="-3" />
    <hkern u1="&#x1e82;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e82;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x2c;" k="40" />
    <hkern u1="&#x174;" u2="&#x201a;" k="40" />
    <hkern u1="&#x174;" u2="&#x201e;" k="40" />
    <hkern u1="&#x174;" u2="&#x29;" k="-3" />
    <hkern u1="&#x174;" u2="]" k="-3" />
    <hkern u1="&#x174;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x174;" u2="&#x2e;" k="40" />
    <hkern u1="&#x174;" u2="&#x2026;" k="40" />
    <hkern u1="&#x174;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e84;" u2="]" k="-3" />
    <hkern u1="&#x1e84;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e84;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e80;" u2="]" k="-3" />
    <hkern u1="&#x1e80;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e80;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x2c;" k="40" />
    <hkern u1="X" u2="&#x2bc;" k="2" />
    <hkern u1="X" u2="&#x2019;" k="2" />
    <hkern u1="X" u2="&#x2018;" k="2" />
    <hkern u1="X" u2="&#x201d;" k="2" />
    <hkern u1="X" u2="&#x201c;" k="2" />
    <hkern u1="X" u2="&#xad;" k="15" />
    <hkern u1="X" u2="&#x2d;" k="15" />
    <hkern u1="X" u2="&#x1e61;" k="8" />
    <hkern u1="X" u2="&#x219;" k="8" />
    <hkern u1="X" u2="&#x123;" k="15" />
    <hkern u1="X" u2="&#xdf;" k="8" />
    <hkern u1="X" u2="&#x15d;" k="8" />
    <hkern u1="X" u2="&#x15f;" k="8" />
    <hkern u1="X" u2="&#x161;" k="8" />
    <hkern u1="X" u2="&#x15b;" k="8" />
    <hkern u1="X" u2="s" k="8" />
    <hkern u1="X" u2="q" k="15" />
    <hkern u1="X" u2="&#x153;" k="15" />
    <hkern u1="X" u2="&#xf5;" k="15" />
    <hkern u1="X" u2="&#x1ff;" k="15" />
    <hkern u1="X" u2="&#xf8;" k="15" />
    <hkern u1="X" u2="&#x14d;" k="15" />
    <hkern u1="X" u2="&#x151;" k="15" />
    <hkern u1="X" u2="&#xf2;" k="15" />
    <hkern u1="X" u2="&#xf6;" k="15" />
    <hkern u1="X" u2="&#xf4;" k="15" />
    <hkern u1="X" u2="&#x14f;" k="15" />
    <hkern u1="X" u2="&#xf3;" k="15" />
    <hkern u1="X" u2="o" k="15" />
    <hkern u1="X" u2="&#x121;" k="15" />
    <hkern u1="X" u2="&#x11d;" k="15" />
    <hkern u1="X" u2="&#x11f;" k="15" />
    <hkern u1="X" u2="g" k="15" />
    <hkern u1="X" u2="&#x10b;" k="15" />
    <hkern u1="X" u2="&#x109;" k="15" />
    <hkern u1="X" u2="&#xe7;" k="15" />
    <hkern u1="X" u2="&#x10d;" k="15" />
    <hkern u1="X" u2="&#x107;" k="15" />
    <hkern u1="X" u2="c" k="15" />
    <hkern u1="X" u2="&#x1e60;" k="8" />
    <hkern u1="X" u2="&#x218;" k="8" />
    <hkern u1="X" u2="&#x122;" k="15" />
    <hkern u1="X" u2="&#x15c;" k="8" />
    <hkern u1="X" u2="&#x15e;" k="8" />
    <hkern u1="X" u2="&#x160;" k="8" />
    <hkern u1="X" u2="&#x15a;" k="8" />
    <hkern u1="X" u2="S" k="8" />
    <hkern u1="X" u2="Q" k="15" />
    <hkern u1="X" u2="&#x152;" k="15" />
    <hkern u1="X" u2="&#xd5;" k="15" />
    <hkern u1="X" u2="&#x1fe;" k="15" />
    <hkern u1="X" u2="&#xd8;" k="15" />
    <hkern u1="X" u2="&#x14c;" k="15" />
    <hkern u1="X" u2="&#x150;" k="15" />
    <hkern u1="X" u2="&#xd2;" k="15" />
    <hkern u1="X" u2="&#xd6;" k="15" />
    <hkern u1="X" u2="&#xd4;" k="15" />
    <hkern u1="X" u2="&#x14e;" k="15" />
    <hkern u1="X" u2="&#xd3;" k="15" />
    <hkern u1="X" u2="O" k="15" />
    <hkern u1="X" u2="&#x120;" k="15" />
    <hkern u1="X" u2="&#x11c;" k="15" />
    <hkern u1="X" u2="&#x11e;" k="15" />
    <hkern u1="X" u2="G" k="15" />
    <hkern u1="X" u2="&#x10a;" k="15" />
    <hkern u1="X" u2="&#x108;" k="15" />
    <hkern u1="X" u2="&#xc7;" k="15" />
    <hkern u1="X" u2="&#x10c;" k="15" />
    <hkern u1="X" u2="&#x106;" k="15" />
    <hkern u1="X" u2="C" k="15" />
    <hkern u1="Y" u2="&#x201a;" k="80" />
    <hkern u1="Y" u2="&#x201e;" k="80" />
    <hkern u1="Y" u2="&#x29;" k="-5" />
    <hkern u1="Y" u2="]" k="-5" />
    <hkern u1="Y" u2="&#x7d;" k="-5" />
    <hkern u1="Y" u2="&#x3b;" k="20" />
    <hkern u1="Y" u2="&#x2e;" k="80" />
    <hkern u1="Y" u2="&#x2026;" k="80" />
    <hkern u1="Y" u2="&#x2c;" k="80" />
    <hkern u1="Y" u2="&#x3a;" k="20" />
    <hkern u1="Y" u2="q" k="15" />
    <hkern u1="Y" u2="&#x153;" k="15" />
    <hkern u1="Y" u2="Q" k="15" />
    <hkern u1="Y" u2="&#x152;" k="15" />
    <hkern u1="&#xdd;" u2="&#x201a;" k="80" />
    <hkern u1="&#xdd;" u2="&#x201e;" k="80" />
    <hkern u1="&#xdd;" u2="&#x29;" k="-5" />
    <hkern u1="&#xdd;" u2="]" k="-5" />
    <hkern u1="&#xdd;" u2="&#x7d;" k="-5" />
    <hkern u1="&#xdd;" u2="&#x3b;" k="20" />
    <hkern u1="&#xdd;" u2="&#x2e;" k="80" />
    <hkern u1="&#xdd;" u2="&#x2026;" k="80" />
    <hkern u1="&#xdd;" u2="&#x2c;" k="80" />
    <hkern u1="&#xdd;" u2="&#x3a;" k="20" />
    <hkern u1="&#xdd;" u2="q" k="15" />
    <hkern u1="&#xdd;" u2="&#x153;" k="15" />
    <hkern u1="&#xdd;" u2="Q" k="15" />
    <hkern u1="&#xdd;" u2="&#x152;" k="15" />
    <hkern u1="&#x176;" u2="&#x201a;" k="80" />
    <hkern u1="&#x176;" u2="&#x201e;" k="80" />
    <hkern u1="&#x176;" u2="&#x29;" k="-5" />
    <hkern u1="&#x176;" u2="]" k="-5" />
    <hkern u1="&#x176;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x176;" u2="&#x3b;" k="20" />
    <hkern u1="&#x176;" u2="&#x2e;" k="80" />
    <hkern u1="&#x176;" u2="&#x2026;" k="80" />
    <hkern u1="&#x176;" u2="&#x2c;" k="80" />
    <hkern u1="&#x176;" u2="&#x3a;" k="20" />
    <hkern u1="&#x176;" u2="q" k="15" />
    <hkern u1="&#x176;" u2="&#x153;" k="15" />
    <hkern u1="&#x176;" u2="Q" k="15" />
    <hkern u1="&#x176;" u2="&#x152;" k="15" />
    <hkern u1="&#x178;" u2="&#x201a;" k="80" />
    <hkern u1="&#x178;" u2="&#x201e;" k="80" />
    <hkern u1="&#x178;" u2="&#x29;" k="-5" />
    <hkern u1="&#x178;" u2="]" k="-5" />
    <hkern u1="&#x178;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x178;" u2="&#x3b;" k="20" />
    <hkern u1="&#x178;" u2="&#x2e;" k="80" />
    <hkern u1="&#x178;" u2="&#x2026;" k="80" />
    <hkern u1="&#x178;" u2="&#x2c;" k="80" />
    <hkern u1="&#x178;" u2="&#x3a;" k="20" />
    <hkern u1="&#x178;" u2="q" k="15" />
    <hkern u1="&#x178;" u2="&#x153;" k="15" />
    <hkern u1="&#x178;" u2="Q" k="15" />
    <hkern u1="&#x178;" u2="&#x152;" k="15" />
    <hkern u1="&#x1ef2;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1ef2;" u2="]" k="-5" />
    <hkern u1="&#x1ef2;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1ef2;" u2="&#x3b;" k="20" />
    <hkern u1="&#x1ef2;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x3a;" k="20" />
    <hkern u1="&#x1ef2;" u2="q" k="15" />
    <hkern u1="&#x1ef2;" u2="&#x153;" k="15" />
    <hkern u1="&#x1ef2;" u2="Q" k="15" />
    <hkern u1="&#x1ef2;" u2="&#x152;" k="15" />
    <hkern u1="&#x122;" u2="&#x201a;" k="10" />
    <hkern u1="&#x122;" u2="&#x2018;" k="10" />
    <hkern u1="&#x122;" u2="&#x201d;" k="10" />
    <hkern u1="&#x122;" u2="&#x201c;" k="10" />
    <hkern u1="&#x122;" u2="&#x201e;" k="10" />
    <hkern u1="&#x122;" u2="&#x2e;" k="10" />
    <hkern u1="&#x122;" u2="&#x2026;" k="10" />
    <hkern u1="&#x122;" u2="&#x2c;" k="10" />
    <hkern u1="&#x122;" u2="x" k="15" />
    <hkern u1="&#x122;" u2="v" k="1" />
    <hkern u1="&#x122;" u2="X" k="15" />
    <hkern u1="&#x122;" u2="V" k="1" />
    <hkern u1="&#x136;" u2="q" k="7" />
    <hkern u1="&#x136;" u2="&#x153;" k="7" />
    <hkern u1="&#x136;" u2="Q" k="7" />
    <hkern u1="&#x136;" u2="&#x152;" k="7" />
    <hkern u1="&#x13b;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13b;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13b;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13b;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13b;" u2="]" k="-8" />
    <hkern u1="&#x13b;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13b;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13b;" u2="v" k="33" />
    <hkern u1="&#x13b;" u2="V" k="33" />
    <hkern u1="&#x156;" u2="v" k="2" />
    <hkern u1="&#x156;" u2="V" k="2" />
    <hkern u1="&#x162;" u2="&#x201a;" k="60" />
    <hkern u1="&#x162;" u2="&#x201e;" k="60" />
    <hkern u1="&#x162;" u2="&#x29;" k="-8" />
    <hkern u1="&#x162;" u2="]" k="-8" />
    <hkern u1="&#x162;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x162;" u2="&#x3b;" k="35" />
    <hkern u1="&#x162;" u2="&#x2e;" k="60" />
    <hkern u1="&#x162;" u2="&#x2026;" k="60" />
    <hkern u1="&#x162;" u2="&#x2c;" k="60" />
    <hkern u1="&#x162;" u2="&#x3a;" k="35" />
    <hkern u1="&#x218;" u2="&#x201a;" k="5" />
    <hkern u1="&#x218;" u2="&#x2018;" k="5" />
    <hkern u1="&#x218;" u2="&#x201d;" k="5" />
    <hkern u1="&#x218;" u2="&#x201c;" k="5" />
    <hkern u1="&#x218;" u2="&#x201e;" k="5" />
    <hkern u1="&#x218;" u2="&#x2e;" k="5" />
    <hkern u1="&#x218;" u2="&#x2026;" k="5" />
    <hkern u1="&#x218;" u2="&#x2c;" k="5" />
    <hkern u1="&#x218;" u2="x" k="8" />
    <hkern u1="&#x218;" u2="v" k="1" />
    <hkern u1="&#x218;" u2="X" k="8" />
    <hkern u1="&#x218;" u2="V" k="1" />
    <hkern u1="&#x21a;" u2="&#x201a;" k="60" />
    <hkern u1="&#x21a;" u2="&#x201e;" k="60" />
    <hkern u1="&#x21a;" u2="&#x29;" k="-8" />
    <hkern u1="&#x21a;" u2="]" k="-8" />
    <hkern u1="&#x21a;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x21a;" u2="&#x3b;" k="35" />
    <hkern u1="&#x21a;" u2="&#x2e;" k="60" />
    <hkern u1="&#x21a;" u2="&#x2026;" k="60" />
    <hkern u1="&#x21a;" u2="&#x2c;" k="60" />
    <hkern u1="&#x21a;" u2="&#x3a;" k="35" />
    <hkern u1="&#x1e02;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e02;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e02;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e02;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e02;" u2="x" k="3" />
    <hkern u1="&#x1e02;" u2="v" k="4" />
    <hkern u1="&#x1e02;" u2="X" k="3" />
    <hkern u1="&#x1e02;" u2="V" k="4" />
    <hkern u1="&#x1e0a;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x2c;" k="10" />
    <hkern u1="&#x1e0a;" u2="x" k="15" />
    <hkern u1="&#x1e0a;" u2="v" k="1" />
    <hkern u1="&#x1e0a;" u2="X" k="15" />
    <hkern u1="&#x1e0a;" u2="V" k="1" />
    <hkern u1="&#x1e1e;" u2="&#x201a;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x201e;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1e1e;" u2="]" k="-5" />
    <hkern u1="&#x1e1e;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1e1e;" u2="&#x2e;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x2026;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x2c;" k="70" />
    <hkern u1="&#x1e56;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1e56;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1e56;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1e56;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1e56;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1e56;" u2="x" k="11" />
    <hkern u1="&#x1e56;" u2="X" k="11" />
    <hkern u1="&#x1e60;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x2018;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x201d;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x201c;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e60;" u2="x" k="8" />
    <hkern u1="&#x1e60;" u2="v" k="1" />
    <hkern u1="&#x1e60;" u2="X" k="8" />
    <hkern u1="&#x1e60;" u2="V" k="1" />
    <hkern u1="&#x1e6a;" u2="&#x201a;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x201e;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x29;" k="-8" />
    <hkern u1="&#x1e6a;" u2="]" k="-8" />
    <hkern u1="&#x1e6a;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x1e6a;" u2="&#x3b;" k="35" />
    <hkern u1="&#x1e6a;" u2="&#x2e;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x2026;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x2c;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x3a;" k="35" />
    <hkern u1="a" u2="&#x2018;" k="60" />
    <hkern u1="a" u2="&#x201d;" k="60" />
    <hkern u1="a" u2="&#x201c;" k="60" />
    <hkern u1="a" u2="&#x3f;" k="30" />
    <hkern u1="a" u2="v" k="25" />
    <hkern u1="a" u2="q" k="1" />
    <hkern u1="a" u2="&#x153;" k="1" />
    <hkern u1="a" u2="V" k="25" />
    <hkern u1="a" u2="Q" k="1" />
    <hkern u1="a" u2="&#x152;" k="1" />
    <hkern u1="&#xe1;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe1;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe1;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe1;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe1;" u2="v" k="25" />
    <hkern u1="&#xe1;" u2="q" k="1" />
    <hkern u1="&#xe1;" u2="&#x153;" k="1" />
    <hkern u1="&#xe1;" u2="V" k="25" />
    <hkern u1="&#xe1;" u2="Q" k="1" />
    <hkern u1="&#xe1;" u2="&#x152;" k="1" />
    <hkern u1="&#x103;" u2="&#x2018;" k="60" />
    <hkern u1="&#x103;" u2="&#x201d;" k="60" />
    <hkern u1="&#x103;" u2="&#x201c;" k="60" />
    <hkern u1="&#x103;" u2="&#x3f;" k="30" />
    <hkern u1="&#x103;" u2="v" k="25" />
    <hkern u1="&#x103;" u2="q" k="1" />
    <hkern u1="&#x103;" u2="&#x153;" k="1" />
    <hkern u1="&#x103;" u2="V" k="25" />
    <hkern u1="&#x103;" u2="Q" k="1" />
    <hkern u1="&#x103;" u2="&#x152;" k="1" />
    <hkern u1="&#xe2;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe2;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe2;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe2;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe2;" u2="v" k="25" />
    <hkern u1="&#xe2;" u2="q" k="1" />
    <hkern u1="&#xe2;" u2="&#x153;" k="1" />
    <hkern u1="&#xe2;" u2="V" k="25" />
    <hkern u1="&#xe2;" u2="Q" k="1" />
    <hkern u1="&#xe2;" u2="&#x152;" k="1" />
    <hkern u1="&#xe4;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe4;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe4;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe4;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe4;" u2="v" k="25" />
    <hkern u1="&#xe4;" u2="q" k="1" />
    <hkern u1="&#xe4;" u2="&#x153;" k="1" />
    <hkern u1="&#xe4;" u2="V" k="25" />
    <hkern u1="&#xe4;" u2="Q" k="1" />
    <hkern u1="&#xe4;" u2="&#x152;" k="1" />
    <hkern u1="&#xe0;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe0;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe0;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe0;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe0;" u2="v" k="25" />
    <hkern u1="&#xe0;" u2="q" k="1" />
    <hkern u1="&#xe0;" u2="&#x153;" k="1" />
    <hkern u1="&#xe0;" u2="V" k="25" />
    <hkern u1="&#xe0;" u2="Q" k="1" />
    <hkern u1="&#xe0;" u2="&#x152;" k="1" />
    <hkern u1="&#x101;" u2="&#x2018;" k="60" />
    <hkern u1="&#x101;" u2="&#x201d;" k="60" />
    <hkern u1="&#x101;" u2="&#x201c;" k="60" />
    <hkern u1="&#x101;" u2="&#x3f;" k="30" />
    <hkern u1="&#x101;" u2="v" k="25" />
    <hkern u1="&#x101;" u2="q" k="1" />
    <hkern u1="&#x101;" u2="&#x153;" k="1" />
    <hkern u1="&#x101;" u2="V" k="25" />
    <hkern u1="&#x101;" u2="Q" k="1" />
    <hkern u1="&#x101;" u2="&#x152;" k="1" />
    <hkern u1="&#x105;" u2="&#x2018;" k="60" />
    <hkern u1="&#x105;" u2="&#x201d;" k="60" />
    <hkern u1="&#x105;" u2="&#x201c;" k="60" />
    <hkern u1="&#x105;" u2="&#x3f;" k="30" />
    <hkern u1="&#x105;" u2="v" k="25" />
    <hkern u1="&#x105;" u2="q" k="1" />
    <hkern u1="&#x105;" u2="&#x153;" k="1" />
    <hkern u1="&#x105;" u2="V" k="25" />
    <hkern u1="&#x105;" u2="Q" k="1" />
    <hkern u1="&#x105;" u2="&#x152;" k="1" />
    <hkern u1="&#xe5;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe5;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe5;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe5;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe5;" u2="v" k="25" />
    <hkern u1="&#xe5;" u2="q" k="1" />
    <hkern u1="&#xe5;" u2="&#x153;" k="1" />
    <hkern u1="&#xe5;" u2="V" k="25" />
    <hkern u1="&#xe5;" u2="Q" k="1" />
    <hkern u1="&#xe5;" u2="&#x152;" k="1" />
    <hkern u1="&#xe3;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe3;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe3;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe3;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe3;" u2="v" k="25" />
    <hkern u1="&#xe3;" u2="q" k="1" />
    <hkern u1="&#xe3;" u2="&#x153;" k="1" />
    <hkern u1="&#xe3;" u2="V" k="25" />
    <hkern u1="&#xe3;" u2="Q" k="1" />
    <hkern u1="&#xe3;" u2="&#x152;" k="1" />
    <hkern u1="b" u2="&#x201a;" k="5" />
    <hkern u1="b" u2="&#x2018;" k="10" />
    <hkern u1="b" u2="&#x201d;" k="10" />
    <hkern u1="b" u2="&#x201c;" k="10" />
    <hkern u1="b" u2="&#x201e;" k="5" />
    <hkern u1="b" u2="&#x2e;" k="5" />
    <hkern u1="b" u2="&#x2026;" k="5" />
    <hkern u1="b" u2="&#x2c;" k="5" />
    <hkern u1="b" u2="x" k="3" />
    <hkern u1="b" u2="v" k="4" />
    <hkern u1="b" u2="X" k="3" />
    <hkern u1="b" u2="V" k="4" />
    <hkern u1="c" u2="&#x201a;" k="6" />
    <hkern u1="c" u2="&#x2018;" k="6" />
    <hkern u1="c" u2="&#x201d;" k="6" />
    <hkern u1="c" u2="&#x201c;" k="6" />
    <hkern u1="c" u2="&#x201e;" k="6" />
    <hkern u1="c" u2="&#x2e;" k="6" />
    <hkern u1="c" u2="&#x2026;" k="6" />
    <hkern u1="c" u2="&#x2c;" k="6" />
    <hkern u1="c" u2="x" k="11" />
    <hkern u1="c" u2="X" k="11" />
    <hkern u1="&#x107;" u2="&#x201a;" k="6" />
    <hkern u1="&#x107;" u2="&#x2018;" k="6" />
    <hkern u1="&#x107;" u2="&#x201d;" k="6" />
    <hkern u1="&#x107;" u2="&#x201c;" k="6" />
    <hkern u1="&#x107;" u2="&#x201e;" k="6" />
    <hkern u1="&#x107;" u2="&#x2e;" k="6" />
    <hkern u1="&#x107;" u2="&#x2026;" k="6" />
    <hkern u1="&#x107;" u2="&#x2c;" k="6" />
    <hkern u1="&#x107;" u2="x" k="11" />
    <hkern u1="&#x107;" u2="X" k="11" />
    <hkern u1="&#x10d;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10d;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10d;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10d;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10d;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10d;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10d;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10d;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10d;" u2="x" k="11" />
    <hkern u1="&#x10d;" u2="X" k="11" />
    <hkern u1="&#xe7;" u2="&#x201a;" k="6" />
    <hkern u1="&#xe7;" u2="&#x2018;" k="6" />
    <hkern u1="&#xe7;" u2="&#x201d;" k="6" />
    <hkern u1="&#xe7;" u2="&#x201c;" k="6" />
    <hkern u1="&#xe7;" u2="&#x201e;" k="6" />
    <hkern u1="&#xe7;" u2="&#x2e;" k="6" />
    <hkern u1="&#xe7;" u2="&#x2026;" k="6" />
    <hkern u1="&#xe7;" u2="&#x2c;" k="6" />
    <hkern u1="&#xe7;" u2="x" k="11" />
    <hkern u1="&#xe7;" u2="X" k="11" />
    <hkern u1="&#x109;" u2="&#x201a;" k="6" />
    <hkern u1="&#x109;" u2="&#x2018;" k="6" />
    <hkern u1="&#x109;" u2="&#x201d;" k="6" />
    <hkern u1="&#x109;" u2="&#x201c;" k="6" />
    <hkern u1="&#x109;" u2="&#x201e;" k="6" />
    <hkern u1="&#x109;" u2="&#x2e;" k="6" />
    <hkern u1="&#x109;" u2="&#x2026;" k="6" />
    <hkern u1="&#x109;" u2="&#x2c;" k="6" />
    <hkern u1="&#x109;" u2="x" k="11" />
    <hkern u1="&#x109;" u2="X" k="11" />
    <hkern u1="&#x10b;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10b;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10b;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10b;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10b;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10b;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10b;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10b;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10b;" u2="x" k="11" />
    <hkern u1="&#x10b;" u2="X" k="11" />
    <hkern u1="d" u2="&#x201a;" k="10" />
    <hkern u1="d" u2="&#x2018;" k="10" />
    <hkern u1="d" u2="&#x201d;" k="10" />
    <hkern u1="d" u2="&#x201c;" k="10" />
    <hkern u1="d" u2="&#x201e;" k="10" />
    <hkern u1="d" u2="&#x2e;" k="10" />
    <hkern u1="d" u2="&#x2026;" k="10" />
    <hkern u1="d" u2="&#x2c;" k="10" />
    <hkern u1="d" u2="x" k="15" />
    <hkern u1="d" u2="v" k="1" />
    <hkern u1="d" u2="X" k="15" />
    <hkern u1="d" u2="V" k="1" />
    <hkern u1="&#xf0;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf0;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf0;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf0;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf0;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf0;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf0;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf0;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf0;" u2="x" k="15" />
    <hkern u1="&#xf0;" u2="v" k="1" />
    <hkern u1="&#xf0;" u2="X" k="15" />
    <hkern u1="&#xf0;" u2="V" k="1" />
    <hkern u1="&#x10f;" u2="&#x201a;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2018;" k="10" />
    <hkern u1="&#x10f;" u2="&#x201d;" k="10" />
    <hkern u1="&#x10f;" u2="&#x201c;" k="10" />
    <hkern u1="&#x10f;" u2="&#x201e;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2e;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2026;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2c;" k="10" />
    <hkern u1="&#x10f;" u2="x" k="15" />
    <hkern u1="&#x10f;" u2="v" k="1" />
    <hkern u1="&#x10f;" u2="X" k="15" />
    <hkern u1="&#x10f;" u2="V" k="1" />
    <hkern u1="&#x111;" u2="&#x201a;" k="10" />
    <hkern u1="&#x111;" u2="&#x2018;" k="10" />
    <hkern u1="&#x111;" u2="&#x201d;" k="10" />
    <hkern u1="&#x111;" u2="&#x201c;" k="10" />
    <hkern u1="&#x111;" u2="&#x201e;" k="10" />
    <hkern u1="&#x111;" u2="&#x2e;" k="10" />
    <hkern u1="&#x111;" u2="&#x2026;" k="10" />
    <hkern u1="&#x111;" u2="&#x2c;" k="10" />
    <hkern u1="&#x111;" u2="x" k="15" />
    <hkern u1="&#x111;" u2="v" k="1" />
    <hkern u1="&#x111;" u2="X" k="15" />
    <hkern u1="&#x111;" u2="V" k="1" />
    <hkern u1="f" u2="&#x201a;" k="70" />
    <hkern u1="f" u2="&#x201e;" k="70" />
    <hkern u1="f" u2="&#x29;" k="-5" />
    <hkern u1="f" u2="]" k="-5" />
    <hkern u1="f" u2="&#x7d;" k="-5" />
    <hkern u1="f" u2="&#x2e;" k="70" />
    <hkern u1="f" u2="&#x2026;" k="70" />
    <hkern u1="f" u2="&#x2c;" k="70" />
    <hkern u1="g" u2="&#x201a;" k="10" />
    <hkern u1="g" u2="&#x2018;" k="10" />
    <hkern u1="g" u2="&#x201d;" k="10" />
    <hkern u1="g" u2="&#x201c;" k="10" />
    <hkern u1="g" u2="&#x201e;" k="10" />
    <hkern u1="g" u2="&#x2e;" k="10" />
    <hkern u1="g" u2="&#x2026;" k="10" />
    <hkern u1="g" u2="&#x2c;" k="10" />
    <hkern u1="g" u2="x" k="15" />
    <hkern u1="g" u2="v" k="1" />
    <hkern u1="g" u2="X" k="15" />
    <hkern u1="g" u2="V" k="1" />
    <hkern u1="&#x11f;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11f;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11f;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11f;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11f;" u2="x" k="15" />
    <hkern u1="&#x11f;" u2="v" k="1" />
    <hkern u1="&#x11f;" u2="X" k="15" />
    <hkern u1="&#x11f;" u2="V" k="1" />
    <hkern u1="&#x11d;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11d;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11d;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11d;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11d;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11d;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11d;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11d;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11d;" u2="x" k="15" />
    <hkern u1="&#x11d;" u2="v" k="1" />
    <hkern u1="&#x11d;" u2="X" k="15" />
    <hkern u1="&#x11d;" u2="V" k="1" />
    <hkern u1="&#x121;" u2="&#x201a;" k="10" />
    <hkern u1="&#x121;" u2="&#x2018;" k="10" />
    <hkern u1="&#x121;" u2="&#x201d;" k="10" />
    <hkern u1="&#x121;" u2="&#x201c;" k="10" />
    <hkern u1="&#x121;" u2="&#x201e;" k="10" />
    <hkern u1="&#x121;" u2="&#x2e;" k="10" />
    <hkern u1="&#x121;" u2="&#x2026;" k="10" />
    <hkern u1="&#x121;" u2="&#x2c;" k="10" />
    <hkern u1="&#x121;" u2="x" k="15" />
    <hkern u1="&#x121;" u2="v" k="1" />
    <hkern u1="&#x121;" u2="X" k="15" />
    <hkern u1="&#x121;" u2="V" k="1" />
    <hkern u1="j" u2="&#x201a;" k="6" />
    <hkern u1="j" u2="&#x201e;" k="6" />
    <hkern u1="j" u2="&#x2e;" k="6" />
    <hkern u1="j" u2="&#x2026;" k="6" />
    <hkern u1="j" u2="&#x2c;" k="6" />
    <hkern u1="&#x237;" u2="&#x201a;" k="6" />
    <hkern u1="&#x237;" u2="&#x201e;" k="6" />
    <hkern u1="&#x237;" u2="&#x2e;" k="6" />
    <hkern u1="&#x237;" u2="&#x2026;" k="6" />
    <hkern u1="&#x237;" u2="&#x2c;" k="6" />
    <hkern u1="&#x135;" u2="&#x201a;" k="6" />
    <hkern u1="&#x135;" u2="&#x201e;" k="6" />
    <hkern u1="&#x135;" u2="&#x2e;" k="6" />
    <hkern u1="&#x135;" u2="&#x2026;" k="6" />
    <hkern u1="&#x135;" u2="&#x2c;" k="6" />
    <hkern u1="k" u2="q" k="7" />
    <hkern u1="k" u2="&#x153;" k="7" />
    <hkern u1="k" u2="Q" k="7" />
    <hkern u1="k" u2="&#x152;" k="7" />
    <hkern u1="&#x138;" u2="q" k="7" />
    <hkern u1="&#x138;" u2="&#x153;" k="7" />
    <hkern u1="&#x138;" u2="Q" k="7" />
    <hkern u1="&#x138;" u2="&#x152;" k="7" />
    <hkern u1="l" u2="&#x2018;" k="75" />
    <hkern u1="l" u2="&#x201d;" k="75" />
    <hkern u1="l" u2="&#x201c;" k="75" />
    <hkern u1="l" u2="&#x29;" k="-8" />
    <hkern u1="l" u2="]" k="-8" />
    <hkern u1="l" u2="&#x7d;" k="-8" />
    <hkern u1="l" u2="&#x3f;" k="30" />
    <hkern u1="l" u2="v" k="33" />
    <hkern u1="l" u2="V" k="33" />
    <hkern u1="&#x13a;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13a;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13a;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13a;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13a;" u2="]" k="-8" />
    <hkern u1="&#x13a;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13a;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13a;" u2="v" k="33" />
    <hkern u1="&#x13a;" u2="V" k="33" />
    <hkern u1="&#x140;" u2="&#x2018;" k="75" />
    <hkern u1="&#x140;" u2="&#x201d;" k="75" />
    <hkern u1="&#x140;" u2="&#x201c;" k="75" />
    <hkern u1="&#x140;" u2="&#x29;" k="-8" />
    <hkern u1="&#x140;" u2="]" k="-8" />
    <hkern u1="&#x140;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x140;" u2="&#x3f;" k="30" />
    <hkern u1="&#x140;" u2="v" k="33" />
    <hkern u1="&#x140;" u2="V" k="33" />
    <hkern u1="&#x142;" u2="&#x2018;" k="75" />
    <hkern u1="&#x142;" u2="&#x201d;" k="75" />
    <hkern u1="&#x142;" u2="&#x201c;" k="75" />
    <hkern u1="&#x142;" u2="&#x29;" k="-8" />
    <hkern u1="&#x142;" u2="]" k="-8" />
    <hkern u1="&#x142;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x142;" u2="&#x3f;" k="30" />
    <hkern u1="&#x142;" u2="v" k="33" />
    <hkern u1="&#x142;" u2="V" k="33" />
    <hkern u1="o" u2="&#x201a;" k="10" />
    <hkern u1="o" u2="&#x2018;" k="10" />
    <hkern u1="o" u2="&#x201d;" k="10" />
    <hkern u1="o" u2="&#x201c;" k="10" />
    <hkern u1="o" u2="&#x201e;" k="10" />
    <hkern u1="o" u2="&#x2e;" k="10" />
    <hkern u1="o" u2="&#x2026;" k="10" />
    <hkern u1="o" u2="&#x2c;" k="10" />
    <hkern u1="o" u2="x" k="15" />
    <hkern u1="o" u2="v" k="1" />
    <hkern u1="o" u2="X" k="15" />
    <hkern u1="o" u2="V" k="1" />
    <hkern u1="&#xf3;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf3;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf3;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf3;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf3;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf3;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf3;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf3;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf3;" u2="x" k="15" />
    <hkern u1="&#xf3;" u2="v" k="1" />
    <hkern u1="&#xf3;" u2="X" k="15" />
    <hkern u1="&#xf3;" u2="V" k="1" />
    <hkern u1="&#x14f;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14f;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14f;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14f;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2c;" k="10" />
    <hkern u1="&#x14f;" u2="x" k="15" />
    <hkern u1="&#x14f;" u2="v" k="1" />
    <hkern u1="&#x14f;" u2="X" k="15" />
    <hkern u1="&#x14f;" u2="V" k="1" />
    <hkern u1="&#xf4;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf4;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf4;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf4;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf4;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf4;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf4;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf4;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf4;" u2="x" k="15" />
    <hkern u1="&#xf4;" u2="v" k="1" />
    <hkern u1="&#xf4;" u2="X" k="15" />
    <hkern u1="&#xf4;" u2="V" k="1" />
    <hkern u1="&#xf6;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf6;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf6;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf6;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf6;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf6;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf6;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf6;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf6;" u2="x" k="15" />
    <hkern u1="&#xf6;" u2="v" k="1" />
    <hkern u1="&#xf6;" u2="X" k="15" />
    <hkern u1="&#xf6;" u2="V" k="1" />
    <hkern u1="&#xf2;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf2;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf2;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf2;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf2;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf2;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf2;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf2;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf2;" u2="x" k="15" />
    <hkern u1="&#xf2;" u2="v" k="1" />
    <hkern u1="&#xf2;" u2="X" k="15" />
    <hkern u1="&#xf2;" u2="V" k="1" />
    <hkern u1="&#x151;" u2="&#x201a;" k="10" />
    <hkern u1="&#x151;" u2="&#x2018;" k="10" />
    <hkern u1="&#x151;" u2="&#x201d;" k="10" />
    <hkern u1="&#x151;" u2="&#x201c;" k="10" />
    <hkern u1="&#x151;" u2="&#x201e;" k="10" />
    <hkern u1="&#x151;" u2="&#x2e;" k="10" />
    <hkern u1="&#x151;" u2="&#x2026;" k="10" />
    <hkern u1="&#x151;" u2="&#x2c;" k="10" />
    <hkern u1="&#x151;" u2="x" k="15" />
    <hkern u1="&#x151;" u2="v" k="1" />
    <hkern u1="&#x151;" u2="X" k="15" />
    <hkern u1="&#x151;" u2="V" k="1" />
    <hkern u1="&#x14d;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14d;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14d;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14d;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2c;" k="10" />
    <hkern u1="&#x14d;" u2="x" k="15" />
    <hkern u1="&#x14d;" u2="v" k="1" />
    <hkern u1="&#x14d;" u2="X" k="15" />
    <hkern u1="&#x14d;" u2="V" k="1" />
    <hkern u1="&#xf8;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf8;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf8;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf8;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf8;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf8;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf8;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf8;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf8;" u2="x" k="15" />
    <hkern u1="&#xf8;" u2="v" k="1" />
    <hkern u1="&#xf8;" u2="X" k="15" />
    <hkern u1="&#xf8;" u2="V" k="1" />
    <hkern u1="&#x1ff;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2c;" k="10" />
    <hkern u1="&#x1ff;" u2="x" k="15" />
    <hkern u1="&#x1ff;" u2="v" k="1" />
    <hkern u1="&#x1ff;" u2="X" k="15" />
    <hkern u1="&#x1ff;" u2="V" k="1" />
    <hkern u1="&#xf5;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf5;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf5;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf5;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf5;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf5;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf5;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf5;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf5;" u2="x" k="15" />
    <hkern u1="&#xf5;" u2="v" k="1" />
    <hkern u1="&#xf5;" u2="X" k="15" />
    <hkern u1="&#xf5;" u2="V" k="1" />
    <hkern u1="p" u2="&#x201a;" k="80" />
    <hkern u1="p" u2="&#x201e;" k="80" />
    <hkern u1="p" u2="&#x2e;" k="80" />
    <hkern u1="p" u2="&#x2026;" k="80" />
    <hkern u1="p" u2="&#x2c;" k="80" />
    <hkern u1="p" u2="x" k="11" />
    <hkern u1="p" u2="X" k="11" />
    <hkern u1="&#xfe;" u2="&#xc1;" k="2" />
    <hkern u1="&#xfe;" u2="A" k="2" />
    <hkern u1="&#xfe;" u2="&#x102;" k="2" />
    <hkern u1="&#xfe;" u2="&#x201a;" k="80" />
    <hkern u1="&#xfe;" u2="&#x201e;" k="80" />
    <hkern u1="&#xfe;" u2="&#x2e;" k="80" />
    <hkern u1="&#xfe;" u2="&#x2026;" k="80" />
    <hkern u1="&#xfe;" u2="&#x2c;" k="80" />
    <hkern u1="&#xfe;" u2="&#x17c;" k="10" />
    <hkern u1="&#xfe;" u2="&#x17e;" k="10" />
    <hkern u1="&#xfe;" u2="&#x17a;" k="10" />
    <hkern u1="&#xfe;" u2="z" k="10" />
    <hkern u1="&#xfe;" u2="&#x1ef3;" k="9" />
    <hkern u1="&#xfe;" u2="&#xff;" k="9" />
    <hkern u1="&#xfe;" u2="&#x177;" k="9" />
    <hkern u1="&#xfe;" u2="&#xfd;" k="9" />
    <hkern u1="&#xfe;" u2="y" k="9" />
    <hkern u1="&#xfe;" u2="x" k="20" />
    <hkern u1="&#xfe;" u2="&#x135;" k="8" />
    <hkern u1="&#xfe;" u2="&#x237;" k="8" />
    <hkern u1="&#xfe;" u2="j" k="8" />
    <hkern u1="&#xfe;" u2="&#x133;" k="8" />
    <hkern u1="&#xfe;" u2="&#x1fd;" k="20" />
    <hkern u1="&#xfe;" u2="&#xe6;" k="20" />
    <hkern u1="&#xfe;" u2="&#xe3;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe5;" k="2" />
    <hkern u1="&#xfe;" u2="&#x105;" k="2" />
    <hkern u1="&#xfe;" u2="&#x101;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe0;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe4;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe2;" k="2" />
    <hkern u1="&#xfe;" u2="&#x103;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe1;" k="2" />
    <hkern u1="&#xfe;" u2="a" k="2" />
    <hkern u1="&#xfe;" u2="&#x17b;" k="10" />
    <hkern u1="&#xfe;" u2="&#x17d;" k="10" />
    <hkern u1="&#xfe;" u2="&#x179;" k="10" />
    <hkern u1="&#xfe;" u2="Z" k="10" />
    <hkern u1="&#xfe;" u2="&#x1ef2;" k="9" />
    <hkern u1="&#xfe;" u2="&#x178;" k="9" />
    <hkern u1="&#xfe;" u2="&#x176;" k="9" />
    <hkern u1="&#xfe;" u2="&#xdd;" k="9" />
    <hkern u1="&#xfe;" u2="Y" k="9" />
    <hkern u1="&#xfe;" u2="X" k="20" />
    <hkern u1="&#xfe;" u2="&#x134;" k="8" />
    <hkern u1="&#xfe;" u2="J" k="8" />
    <hkern u1="&#xfe;" u2="&#x132;" k="8" />
    <hkern u1="&#xfe;" u2="&#x1fc;" k="20" />
    <hkern u1="&#xfe;" u2="&#xc6;" k="20" />
    <hkern u1="&#xfe;" u2="&#xc3;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc5;" k="2" />
    <hkern u1="&#xfe;" u2="&#x104;" k="2" />
    <hkern u1="&#xfe;" u2="&#x100;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc0;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc4;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc2;" k="2" />
    <hkern u1="q" u2="&#x2bc;" k="10" />
    <hkern u1="q" u2="&#x2019;" k="10" />
    <hkern u1="q" u2="&#x2018;" k="10" />
    <hkern u1="q" u2="&#x201d;" k="10" />
    <hkern u1="q" u2="&#x201c;" k="10" />
    <hkern u1="q" u2="&#x1ef3;" k="13" />
    <hkern u1="q" u2="&#xff;" k="13" />
    <hkern u1="q" u2="&#x177;" k="13" />
    <hkern u1="q" u2="&#xfd;" k="13" />
    <hkern u1="q" u2="y" k="13" />
    <hkern u1="q" u2="v" k="1" />
    <hkern u1="q" u2="&#x1ef2;" k="13" />
    <hkern u1="q" u2="&#x178;" k="13" />
    <hkern u1="q" u2="&#x176;" k="13" />
    <hkern u1="q" u2="&#xdd;" k="13" />
    <hkern u1="q" u2="Y" k="13" />
    <hkern u1="q" u2="V" k="1" />
    <hkern u1="r" u2="v" k="2" />
    <hkern u1="r" u2="V" k="2" />
    <hkern u1="&#x155;" u2="v" k="2" />
    <hkern u1="&#x155;" u2="V" k="2" />
    <hkern u1="&#x159;" u2="v" k="2" />
    <hkern u1="&#x159;" u2="V" k="2" />
    <hkern u1="s" u2="&#x201a;" k="5" />
    <hkern u1="s" u2="&#x2018;" k="5" />
    <hkern u1="s" u2="&#x201d;" k="5" />
    <hkern u1="s" u2="&#x201c;" k="5" />
    <hkern u1="s" u2="&#x201e;" k="5" />
    <hkern u1="s" u2="&#x2e;" k="5" />
    <hkern u1="s" u2="&#x2026;" k="5" />
    <hkern u1="s" u2="&#x2c;" k="5" />
    <hkern u1="s" u2="x" k="8" />
    <hkern u1="s" u2="v" k="1" />
    <hkern u1="s" u2="X" k="8" />
    <hkern u1="s" u2="V" k="1" />
    <hkern u1="&#x15b;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15b;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15b;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15b;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15b;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15b;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15b;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15b;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15b;" u2="x" k="8" />
    <hkern u1="&#x15b;" u2="v" k="1" />
    <hkern u1="&#x15b;" u2="X" k="8" />
    <hkern u1="&#x15b;" u2="V" k="1" />
    <hkern u1="&#x161;" u2="&#x201a;" k="5" />
    <hkern u1="&#x161;" u2="&#x2018;" k="5" />
    <hkern u1="&#x161;" u2="&#x201d;" k="5" />
    <hkern u1="&#x161;" u2="&#x201c;" k="5" />
    <hkern u1="&#x161;" u2="&#x201e;" k="5" />
    <hkern u1="&#x161;" u2="&#x2e;" k="5" />
    <hkern u1="&#x161;" u2="&#x2026;" k="5" />
    <hkern u1="&#x161;" u2="&#x2c;" k="5" />
    <hkern u1="&#x161;" u2="x" k="8" />
    <hkern u1="&#x161;" u2="v" k="1" />
    <hkern u1="&#x161;" u2="X" k="8" />
    <hkern u1="&#x161;" u2="V" k="1" />
    <hkern u1="&#x15f;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15f;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15f;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15f;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15f;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15f;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15f;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15f;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15f;" u2="x" k="8" />
    <hkern u1="&#x15f;" u2="v" k="1" />
    <hkern u1="&#x15f;" u2="X" k="8" />
    <hkern u1="&#x15f;" u2="V" k="1" />
    <hkern u1="&#x15d;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15d;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15d;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15d;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15d;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15d;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15d;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15d;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15d;" u2="x" k="8" />
    <hkern u1="&#x15d;" u2="v" k="1" />
    <hkern u1="&#x15d;" u2="X" k="8" />
    <hkern u1="&#x15d;" u2="V" k="1" />
    <hkern u1="&#xdf;" u2="&#x201a;" k="5" />
    <hkern u1="&#xdf;" u2="&#x2018;" k="5" />
    <hkern u1="&#xdf;" u2="&#x201d;" k="5" />
    <hkern u1="&#xdf;" u2="&#x201c;" k="5" />
    <hkern u1="&#xdf;" u2="&#x201e;" k="5" />
    <hkern u1="&#xdf;" u2="&#x2e;" k="5" />
    <hkern u1="&#xdf;" u2="&#x2026;" k="5" />
    <hkern u1="&#xdf;" u2="&#x2c;" k="5" />
    <hkern u1="&#xdf;" u2="x" k="8" />
    <hkern u1="&#xdf;" u2="v" k="1" />
    <hkern u1="&#xdf;" u2="X" k="8" />
    <hkern u1="&#xdf;" u2="V" k="1" />
    <hkern u1="t" u2="&#x201a;" k="60" />
    <hkern u1="t" u2="&#x201e;" k="60" />
    <hkern u1="t" u2="&#x29;" k="-8" />
    <hkern u1="t" u2="]" k="-8" />
    <hkern u1="t" u2="&#x7d;" k="-8" />
    <hkern u1="t" u2="&#x3b;" k="35" />
    <hkern u1="t" u2="&#x2e;" k="60" />
    <hkern u1="t" u2="&#x2026;" k="60" />
    <hkern u1="t" u2="&#x2c;" k="60" />
    <hkern u1="t" u2="&#x3a;" k="35" />
    <hkern u1="&#x167;" u2="&#x201a;" k="60" />
    <hkern u1="&#x167;" u2="&#x201e;" k="60" />
    <hkern u1="&#x167;" u2="&#x29;" k="-8" />
    <hkern u1="&#x167;" u2="]" k="-8" />
    <hkern u1="&#x167;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x167;" u2="&#x3b;" k="35" />
    <hkern u1="&#x167;" u2="&#x2e;" k="60" />
    <hkern u1="&#x167;" u2="&#x2026;" k="60" />
    <hkern u1="&#x167;" u2="&#x2c;" k="60" />
    <hkern u1="&#x167;" u2="&#x3a;" k="35" />
    <hkern u1="&#x165;" u2="&#x201a;" k="60" />
    <hkern u1="&#x165;" u2="&#x201e;" k="60" />
    <hkern u1="&#x165;" u2="&#x29;" k="-8" />
    <hkern u1="&#x165;" u2="]" k="-8" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x165;" u2="&#x3b;" k="35" />
    <hkern u1="&#x165;" u2="&#x2e;" k="60" />
    <hkern u1="&#x165;" u2="&#x2026;" k="60" />
    <hkern u1="&#x165;" u2="&#x2c;" k="60" />
    <hkern u1="&#x165;" u2="&#x3a;" k="35" />
    <hkern u1="u" u2="&#x201a;" k="6" />
    <hkern u1="u" u2="&#x201e;" k="6" />
    <hkern u1="u" u2="&#x2e;" k="6" />
    <hkern u1="u" u2="&#x2026;" k="6" />
    <hkern u1="u" u2="&#x2c;" k="6" />
    <hkern u1="&#xfa;" u2="&#x201a;" k="6" />
    <hkern u1="&#xfa;" u2="&#x201e;" k="6" />
    <hkern u1="&#xfa;" u2="&#x2e;" k="6" />
    <hkern u1="&#xfa;" u2="&#x2026;" k="6" />
    <hkern u1="&#xfa;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16d;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16d;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16d;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16d;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16d;" u2="&#x2c;" k="6" />
    <hkern u1="&#xfb;" u2="&#x201a;" k="6" />
    <hkern u1="&#xfb;" u2="&#x201e;" k="6" />
    <hkern u1="&#xfb;" u2="&#x2e;" k="6" />
    <hkern u1="&#xfb;" u2="&#x2026;" k="6" />
    <hkern u1="&#xfb;" u2="&#x2c;" k="6" />
    <hkern u1="&#xfc;" u2="&#x201a;" k="6" />
    <hkern u1="&#xfc;" u2="&#x201e;" k="6" />
    <hkern u1="&#xfc;" u2="&#x2e;" k="6" />
    <hkern u1="&#xfc;" u2="&#x2026;" k="6" />
    <hkern u1="&#xfc;" u2="&#x2c;" k="6" />
    <hkern u1="&#xf9;" u2="&#x201a;" k="6" />
    <hkern u1="&#xf9;" u2="&#x201e;" k="6" />
    <hkern u1="&#xf9;" u2="&#x2e;" k="6" />
    <hkern u1="&#xf9;" u2="&#x2026;" k="6" />
    <hkern u1="&#xf9;" u2="&#x2c;" k="6" />
    <hkern u1="&#x171;" u2="&#x201a;" k="6" />
    <hkern u1="&#x171;" u2="&#x201e;" k="6" />
    <hkern u1="&#x171;" u2="&#x2e;" k="6" />
    <hkern u1="&#x171;" u2="&#x2026;" k="6" />
    <hkern u1="&#x171;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16b;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16b;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16b;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16b;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16b;" u2="&#x2c;" k="6" />
    <hkern u1="&#x123;" u2="&#x201a;" k="10" />
    <hkern u1="&#x123;" u2="&#x2018;" k="10" />
    <hkern u1="&#x123;" u2="&#x201d;" k="10" />
    <hkern u1="&#x123;" u2="&#x201c;" k="10" />
    <hkern u1="&#x123;" u2="&#x201e;" k="10" />
    <hkern u1="&#x123;" u2="&#x2e;" k="10" />
    <hkern u1="&#x123;" u2="&#x2026;" k="10" />
    <hkern u1="&#x123;" u2="&#x2c;" k="10" />
    <hkern u1="&#x123;" u2="x" k="15" />
    <hkern u1="&#x123;" u2="v" k="1" />
    <hkern u1="&#x123;" u2="X" k="15" />
    <hkern u1="&#x123;" u2="V" k="1" />
    <hkern u1="&#x137;" u2="q" k="7" />
    <hkern u1="&#x137;" u2="&#x153;" k="7" />
    <hkern u1="&#x137;" u2="Q" k="7" />
    <hkern u1="&#x137;" u2="&#x152;" k="7" />
    <hkern u1="&#x13c;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13c;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13c;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13c;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13c;" u2="]" k="-8" />
    <hkern u1="&#x13c;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13c;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13c;" u2="v" k="33" />
    <hkern u1="&#x13c;" u2="V" k="33" />
    <hkern u1="&#x157;" u2="v" k="2" />
    <hkern u1="&#x157;" u2="V" k="2" />
    <hkern u1="&#x163;" u2="&#x201a;" k="60" />
    <hkern u1="&#x163;" u2="&#x201e;" k="60" />
    <hkern u1="&#x163;" u2="&#x29;" k="-8" />
    <hkern u1="&#x163;" u2="]" k="-8" />
    <hkern u1="&#x163;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x163;" u2="&#x3b;" k="35" />
    <hkern u1="&#x163;" u2="&#x2e;" k="60" />
    <hkern u1="&#x163;" u2="&#x2026;" k="60" />
    <hkern u1="&#x163;" u2="&#x2c;" k="60" />
    <hkern u1="&#x163;" u2="&#x3a;" k="35" />
    <hkern u1="&#x219;" u2="&#x201a;" k="5" />
    <hkern u1="&#x219;" u2="&#x2018;" k="5" />
    <hkern u1="&#x219;" u2="&#x201d;" k="5" />
    <hkern u1="&#x219;" u2="&#x201c;" k="5" />
    <hkern u1="&#x219;" u2="&#x201e;" k="5" />
    <hkern u1="&#x219;" u2="&#x2e;" k="5" />
    <hkern u1="&#x219;" u2="&#x2026;" k="5" />
    <hkern u1="&#x219;" u2="&#x2c;" k="5" />
    <hkern u1="&#x219;" u2="x" k="8" />
    <hkern u1="&#x219;" u2="v" k="1" />
    <hkern u1="&#x219;" u2="X" k="8" />
    <hkern u1="&#x219;" u2="V" k="1" />
    <hkern u1="&#x21b;" u2="&#x201a;" k="60" />
    <hkern u1="&#x21b;" u2="&#x201e;" k="60" />
    <hkern u1="&#x21b;" u2="&#x29;" k="-8" />
    <hkern u1="&#x21b;" u2="]" k="-8" />
    <hkern u1="&#x21b;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x21b;" u2="&#x3b;" k="35" />
    <hkern u1="&#x21b;" u2="&#x2e;" k="60" />
    <hkern u1="&#x21b;" u2="&#x2026;" k="60" />
    <hkern u1="&#x21b;" u2="&#x2c;" k="60" />
    <hkern u1="&#x21b;" u2="&#x3a;" k="35" />
    <hkern u1="&#x1e03;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e03;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e03;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e03;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e03;" u2="x" k="3" />
    <hkern u1="&#x1e03;" u2="v" k="4" />
    <hkern u1="&#x1e03;" u2="X" k="3" />
    <hkern u1="&#x1e03;" u2="V" k="4" />
    <hkern u1="&#x1e0b;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x2c;" k="10" />
    <hkern u1="&#x1e0b;" u2="x" k="15" />
    <hkern u1="&#x1e0b;" u2="v" k="1" />
    <hkern u1="&#x1e0b;" u2="X" k="15" />
    <hkern u1="&#x1e0b;" u2="V" k="1" />
    <hkern u1="&#x1e1f;" u2="&#x201a;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x201e;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1e1f;" u2="]" k="-5" />
    <hkern u1="&#x1e1f;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1e1f;" u2="&#x2e;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x2026;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x2c;" k="70" />
    <hkern u1="&#x1e57;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1e57;" u2="x" k="11" />
    <hkern u1="&#x1e57;" u2="X" k="11" />
    <hkern u1="&#x1e61;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x2018;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x201d;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x201c;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e61;" u2="x" k="8" />
    <hkern u1="&#x1e61;" u2="v" k="1" />
    <hkern u1="&#x1e61;" u2="X" k="8" />
    <hkern u1="&#x1e61;" u2="V" k="1" />
    <hkern u1="&#x1e6b;" u2="&#x201a;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x201e;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x29;" k="-8" />
    <hkern u1="&#x1e6b;" u2="]" k="-8" />
    <hkern u1="&#x1e6b;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x1e6b;" u2="&#x3b;" k="35" />
    <hkern u1="&#x1e6b;" u2="&#x2e;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x2026;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x2c;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x3a;" k="35" />
    <hkern u1="&#x173;" u2="&#x201a;" k="6" />
    <hkern u1="&#x173;" u2="&#x201e;" k="6" />
    <hkern u1="&#x173;" u2="&#x2e;" k="6" />
    <hkern u1="&#x173;" u2="&#x2026;" k="6" />
    <hkern u1="&#x173;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16f;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16f;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16f;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16f;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16f;" u2="&#x2c;" k="6" />
    <hkern u1="&#x169;" u2="&#x201a;" k="6" />
    <hkern u1="&#x169;" u2="&#x201e;" k="6" />
    <hkern u1="&#x169;" u2="&#x2e;" k="6" />
    <hkern u1="&#x169;" u2="&#x2026;" k="6" />
    <hkern u1="&#x169;" u2="&#x2c;" k="6" />
    <hkern u1="v" u2="&#xc1;" k="25" />
    <hkern u1="v" u2="A" k="25" />
    <hkern u1="v" u2="&#x102;" k="25" />
    <hkern u1="v" u2="&#x201a;" k="60" />
    <hkern u1="v" u2="&#x201e;" k="60" />
    <hkern u1="v" u2="&#xad;" k="10" />
    <hkern u1="v" u2="&#x2d;" k="10" />
    <hkern u1="v" u2="&#x29;" k="-3" />
    <hkern u1="v" u2="]" k="-3" />
    <hkern u1="v" u2="&#x7d;" k="-3" />
    <hkern u1="v" u2="&#x2e;" k="60" />
    <hkern u1="v" u2="&#x2026;" k="60" />
    <hkern u1="v" u2="&#x2c;" k="60" />
    <hkern u1="v" u2="&#x123;" k="1" />
    <hkern u1="v" u2="q" k="1" />
    <hkern u1="v" u2="&#x153;" k="1" />
    <hkern u1="v" u2="&#xf5;" k="1" />
    <hkern u1="v" u2="&#x1ff;" k="1" />
    <hkern u1="v" u2="&#xf8;" k="1" />
    <hkern u1="v" u2="&#x14d;" k="1" />
    <hkern u1="v" u2="&#x151;" k="1" />
    <hkern u1="v" u2="&#xf2;" k="1" />
    <hkern u1="v" u2="&#xf6;" k="1" />
    <hkern u1="v" u2="&#xf4;" k="1" />
    <hkern u1="v" u2="&#x14f;" k="1" />
    <hkern u1="v" u2="&#xf3;" k="1" />
    <hkern u1="v" u2="o" k="1" />
    <hkern u1="v" u2="&#x135;" k="25" />
    <hkern u1="v" u2="&#x237;" k="25" />
    <hkern u1="v" u2="j" k="25" />
    <hkern u1="v" u2="&#x133;" k="25" />
    <hkern u1="v" u2="&#x121;" k="1" />
    <hkern u1="v" u2="&#x11d;" k="1" />
    <hkern u1="v" u2="&#x11f;" k="1" />
    <hkern u1="v" u2="g" k="1" />
    <hkern u1="v" u2="&#x10b;" k="1" />
    <hkern u1="v" u2="&#x109;" k="1" />
    <hkern u1="v" u2="&#xe7;" k="1" />
    <hkern u1="v" u2="&#x10d;" k="1" />
    <hkern u1="v" u2="&#x107;" k="1" />
    <hkern u1="v" u2="c" k="1" />
    <hkern u1="v" u2="&#x1fd;" k="46" />
    <hkern u1="v" u2="&#xe6;" k="46" />
    <hkern u1="v" u2="&#xe3;" k="25" />
    <hkern u1="v" u2="&#xe5;" k="25" />
    <hkern u1="v" u2="&#x105;" k="25" />
    <hkern u1="v" u2="&#x101;" k="25" />
    <hkern u1="v" u2="&#xe0;" k="25" />
    <hkern u1="v" u2="&#xe4;" k="25" />
    <hkern u1="v" u2="&#xe2;" k="25" />
    <hkern u1="v" u2="&#x103;" k="25" />
    <hkern u1="v" u2="&#xe1;" k="25" />
    <hkern u1="v" u2="a" k="25" />
    <hkern u1="v" u2="&#x122;" k="1" />
    <hkern u1="v" u2="Q" k="1" />
    <hkern u1="v" u2="&#x152;" k="1" />
    <hkern u1="v" u2="&#xd5;" k="1" />
    <hkern u1="v" u2="&#x1fe;" k="1" />
    <hkern u1="v" u2="&#xd8;" k="1" />
    <hkern u1="v" u2="&#x14c;" k="1" />
    <hkern u1="v" u2="&#x150;" k="1" />
    <hkern u1="v" u2="&#xd2;" k="1" />
    <hkern u1="v" u2="&#xd6;" k="1" />
    <hkern u1="v" u2="&#xd4;" k="1" />
    <hkern u1="v" u2="&#x14e;" k="1" />
    <hkern u1="v" u2="&#xd3;" k="1" />
    <hkern u1="v" u2="O" k="1" />
    <hkern u1="v" u2="&#x134;" k="25" />
    <hkern u1="v" u2="J" k="25" />
    <hkern u1="v" u2="&#x132;" k="25" />
    <hkern u1="v" u2="&#x120;" k="1" />
    <hkern u1="v" u2="&#x11c;" k="1" />
    <hkern u1="v" u2="&#x11e;" k="1" />
    <hkern u1="v" u2="G" k="1" />
    <hkern u1="v" u2="&#x10a;" k="1" />
    <hkern u1="v" u2="&#x108;" k="1" />
    <hkern u1="v" u2="&#xc7;" k="1" />
    <hkern u1="v" u2="&#x10c;" k="1" />
    <hkern u1="v" u2="&#x106;" k="1" />
    <hkern u1="v" u2="C" k="1" />
    <hkern u1="v" u2="&#x1fc;" k="46" />
    <hkern u1="v" u2="&#xc6;" k="46" />
    <hkern u1="v" u2="&#xc3;" k="25" />
    <hkern u1="v" u2="&#xc5;" k="25" />
    <hkern u1="v" u2="&#x104;" k="25" />
    <hkern u1="v" u2="&#x100;" k="25" />
    <hkern u1="v" u2="&#xc0;" k="25" />
    <hkern u1="v" u2="&#xc4;" k="25" />
    <hkern u1="v" u2="&#xc2;" k="25" />
    <hkern u1="w" u2="&#x201a;" k="40" />
    <hkern u1="w" u2="&#x201e;" k="40" />
    <hkern u1="w" u2="&#x29;" k="-3" />
    <hkern u1="w" u2="]" k="-3" />
    <hkern u1="w" u2="&#x7d;" k="-3" />
    <hkern u1="w" u2="&#x2e;" k="40" />
    <hkern u1="w" u2="&#x2026;" k="40" />
    <hkern u1="w" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e83;" u2="]" k="-3" />
    <hkern u1="&#x1e83;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e83;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x2c;" k="40" />
    <hkern u1="&#x175;" u2="&#x201a;" k="40" />
    <hkern u1="&#x175;" u2="&#x201e;" k="40" />
    <hkern u1="&#x175;" u2="&#x29;" k="-3" />
    <hkern u1="&#x175;" u2="]" k="-3" />
    <hkern u1="&#x175;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x175;" u2="&#x2e;" k="40" />
    <hkern u1="&#x175;" u2="&#x2026;" k="40" />
    <hkern u1="&#x175;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e85;" u2="]" k="-3" />
    <hkern u1="&#x1e85;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e85;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e81;" u2="]" k="-3" />
    <hkern u1="&#x1e81;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e81;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x2c;" k="40" />
    <hkern u1="x" u2="&#x2bc;" k="2" />
    <hkern u1="x" u2="&#x2019;" k="2" />
    <hkern u1="x" u2="&#x2018;" k="2" />
    <hkern u1="x" u2="&#x201d;" k="2" />
    <hkern u1="x" u2="&#x201c;" k="2" />
    <hkern u1="x" u2="&#xad;" k="15" />
    <hkern u1="x" u2="&#x2d;" k="15" />
    <hkern u1="x" u2="&#x1e61;" k="8" />
    <hkern u1="x" u2="&#x219;" k="8" />
    <hkern u1="x" u2="&#x123;" k="15" />
    <hkern u1="x" u2="&#xdf;" k="8" />
    <hkern u1="x" u2="&#x15d;" k="8" />
    <hkern u1="x" u2="&#x15f;" k="8" />
    <hkern u1="x" u2="&#x161;" k="8" />
    <hkern u1="x" u2="&#x15b;" k="8" />
    <hkern u1="x" u2="s" k="8" />
    <hkern u1="x" u2="q" k="15" />
    <hkern u1="x" u2="&#x153;" k="15" />
    <hkern u1="x" u2="&#xf5;" k="15" />
    <hkern u1="x" u2="&#x1ff;" k="15" />
    <hkern u1="x" u2="&#xf8;" k="15" />
    <hkern u1="x" u2="&#x14d;" k="15" />
    <hkern u1="x" u2="&#x151;" k="15" />
    <hkern u1="x" u2="&#xf2;" k="15" />
    <hkern u1="x" u2="&#xf6;" k="15" />
    <hkern u1="x" u2="&#xf4;" k="15" />
    <hkern u1="x" u2="&#x14f;" k="15" />
    <hkern u1="x" u2="&#xf3;" k="15" />
    <hkern u1="x" u2="o" k="15" />
    <hkern u1="x" u2="&#x121;" k="15" />
    <hkern u1="x" u2="&#x11d;" k="15" />
    <hkern u1="x" u2="&#x11f;" k="15" />
    <hkern u1="x" u2="g" k="15" />
    <hkern u1="x" u2="&#x10b;" k="15" />
    <hkern u1="x" u2="&#x109;" k="15" />
    <hkern u1="x" u2="&#xe7;" k="15" />
    <hkern u1="x" u2="&#x10d;" k="15" />
    <hkern u1="x" u2="&#x107;" k="15" />
    <hkern u1="x" u2="c" k="15" />
    <hkern u1="x" u2="&#x1e60;" k="8" />
    <hkern u1="x" u2="&#x218;" k="8" />
    <hkern u1="x" u2="&#x122;" k="15" />
    <hkern u1="x" u2="&#x15c;" k="8" />
    <hkern u1="x" u2="&#x15e;" k="8" />
    <hkern u1="x" u2="&#x160;" k="8" />
    <hkern u1="x" u2="&#x15a;" k="8" />
    <hkern u1="x" u2="S" k="8" />
    <hkern u1="x" u2="Q" k="15" />
    <hkern u1="x" u2="&#x152;" k="15" />
    <hkern u1="x" u2="&#xd5;" k="15" />
    <hkern u1="x" u2="&#x1fe;" k="15" />
    <hkern u1="x" u2="&#xd8;" k="15" />
    <hkern u1="x" u2="&#x14c;" k="15" />
    <hkern u1="x" u2="&#x150;" k="15" />
    <hkern u1="x" u2="&#xd2;" k="15" />
    <hkern u1="x" u2="&#xd6;" k="15" />
    <hkern u1="x" u2="&#xd4;" k="15" />
    <hkern u1="x" u2="&#x14e;" k="15" />
    <hkern u1="x" u2="&#xd3;" k="15" />
    <hkern u1="x" u2="O" k="15" />
    <hkern u1="x" u2="&#x120;" k="15" />
    <hkern u1="x" u2="&#x11c;" k="15" />
    <hkern u1="x" u2="&#x11e;" k="15" />
    <hkern u1="x" u2="G" k="15" />
    <hkern u1="x" u2="&#x10a;" k="15" />
    <hkern u1="x" u2="&#x108;" k="15" />
    <hkern u1="x" u2="&#xc7;" k="15" />
    <hkern u1="x" u2="&#x10c;" k="15" />
    <hkern u1="x" u2="&#x106;" k="15" />
    <hkern u1="x" u2="C" k="15" />
    <hkern u1="y" u2="&#x201a;" k="80" />
    <hkern u1="y" u2="&#x201e;" k="80" />
    <hkern u1="y" u2="&#x29;" k="-5" />
    <hkern u1="y" u2="]" k="-5" />
    <hkern u1="y" u2="&#x7d;" k="-5" />
    <hkern u1="y" u2="&#x3b;" k="20" />
    <hkern u1="y" u2="&#x2e;" k="80" />
    <hkern u1="y" u2="&#x2026;" k="80" />
    <hkern u1="y" u2="&#x2c;" k="80" />
    <hkern u1="y" u2="&#x3a;" k="20" />
    <hkern u1="y" u2="q" k="15" />
    <hkern u1="y" u2="&#x153;" k="15" />
    <hkern u1="y" u2="Q" k="15" />
    <hkern u1="y" u2="&#x152;" k="15" />
    <hkern u1="&#xfd;" u2="&#x201a;" k="80" />
    <hkern u1="&#xfd;" u2="&#x201e;" k="80" />
    <hkern u1="&#xfd;" u2="&#x29;" k="-5" />
    <hkern u1="&#xfd;" u2="]" k="-5" />
    <hkern u1="&#xfd;" u2="&#x7d;" k="-5" />
    <hkern u1="&#xfd;" u2="&#x3b;" k="20" />
    <hkern u1="&#xfd;" u2="&#x2e;" k="80" />
    <hkern u1="&#xfd;" u2="&#x2026;" k="80" />
    <hkern u1="&#xfd;" u2="&#x2c;" k="80" />
    <hkern u1="&#xfd;" u2="&#x3a;" k="20" />
    <hkern u1="&#xfd;" u2="q" k="15" />
    <hkern u1="&#xfd;" u2="&#x153;" k="15" />
    <hkern u1="&#xfd;" u2="Q" k="15" />
    <hkern u1="&#xfd;" u2="&#x152;" k="15" />
    <hkern u1="&#x177;" u2="&#x201a;" k="80" />
    <hkern u1="&#x177;" u2="&#x201e;" k="80" />
    <hkern u1="&#x177;" u2="&#x29;" k="-5" />
    <hkern u1="&#x177;" u2="]" k="-5" />
    <hkern u1="&#x177;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x177;" u2="&#x3b;" k="20" />
    <hkern u1="&#x177;" u2="&#x2e;" k="80" />
    <hkern u1="&#x177;" u2="&#x2026;" k="80" />
    <hkern u1="&#x177;" u2="&#x2c;" k="80" />
    <hkern u1="&#x177;" u2="&#x3a;" k="20" />
    <hkern u1="&#x177;" u2="q" k="15" />
    <hkern u1="&#x177;" u2="&#x153;" k="15" />
    <hkern u1="&#x177;" u2="Q" k="15" />
    <hkern u1="&#x177;" u2="&#x152;" k="15" />
    <hkern u1="&#xff;" u2="&#x201a;" k="80" />
    <hkern u1="&#xff;" u2="&#x201e;" k="80" />
    <hkern u1="&#xff;" u2="&#x29;" k="-5" />
    <hkern u1="&#xff;" u2="]" k="-5" />
    <hkern u1="&#xff;" u2="&#x7d;" k="-5" />
    <hkern u1="&#xff;" u2="&#x3b;" k="20" />
    <hkern u1="&#xff;" u2="&#x2e;" k="80" />
    <hkern u1="&#xff;" u2="&#x2026;" k="80" />
    <hkern u1="&#xff;" u2="&#x2c;" k="80" />
    <hkern u1="&#xff;" u2="&#x3a;" k="20" />
    <hkern u1="&#xff;" u2="q" k="15" />
    <hkern u1="&#xff;" u2="&#x153;" k="15" />
    <hkern u1="&#xff;" u2="Q" k="15" />
    <hkern u1="&#xff;" u2="&#x152;" k="15" />
    <hkern u1="&#x1ef3;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1ef3;" u2="]" k="-5" />
    <hkern u1="&#x1ef3;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1ef3;" u2="&#x3b;" k="20" />
    <hkern u1="&#x1ef3;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x3a;" k="20" />
    <hkern u1="&#x1ef3;" u2="q" k="15" />
    <hkern u1="&#x1ef3;" u2="&#x153;" k="15" />
    <hkern u1="&#x1ef3;" u2="Q" k="15" />
    <hkern u1="&#x1ef3;" u2="&#x152;" k="15" />
    <hkern g1="seven.alt" g2="seven.alt" k="-4" />
    <hkern g1="seven.alt" g2="four.alt" k="40" />
    <hkern u1="&#x3a;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x3a;" u2="&#xff;" k="20" />
    <hkern u1="&#x3a;" u2="&#x177;" k="20" />
    <hkern u1="&#x3a;" u2="&#xfd;" k="20" />
    <hkern u1="&#x3a;" u2="y" k="20" />
    <hkern u1="&#x3a;" u2="&#x1e6b;" k="35" />
    <hkern u1="&#x3a;" u2="&#x21b;" k="35" />
    <hkern u1="&#x3a;" u2="&#x163;" k="35" />
    <hkern u1="&#x3a;" u2="&#x165;" k="35" />
    <hkern u1="&#x3a;" u2="&#x167;" k="35" />
    <hkern u1="&#x3a;" u2="t" k="35" />
    <hkern u1="&#x3a;" u2="&#x1e6a;" k="35" />
    <hkern u1="&#x3a;" u2="&#x21a;" k="35" />
    <hkern u1="&#x3a;" u2="&#x162;" k="35" />
    <hkern u1="&#x3a;" u2="&#x1ef2;" k="20" />
    <hkern u1="&#x3a;" u2="&#x178;" k="20" />
    <hkern u1="&#x3a;" u2="&#x176;" k="20" />
    <hkern u1="&#x3a;" u2="&#xdd;" k="20" />
    <hkern u1="&#x3a;" u2="Y" k="20" />
    <hkern u1="&#x3a;" u2="&#x164;" k="35" />
    <hkern u1="&#x3a;" u2="&#x166;" k="35" />
    <hkern u1="&#x3a;" u2="T" k="35" />
    <hkern u1="&#x2c;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x2c;" u2="&#xff;" k="80" />
    <hkern u1="&#x2c;" u2="&#x177;" k="80" />
    <hkern u1="&#x2c;" u2="&#xfd;" k="80" />
    <hkern u1="&#x2c;" u2="y" k="80" />
    <hkern u1="&#x2c;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x2c;" u2="&#x175;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x2c;" u2="w" k="40" />
    <hkern u1="&#x2c;" u2="v" k="60" />
    <hkern u1="&#x2c;" u2="&#x169;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16f;" k="6" />
    <hkern u1="&#x2c;" u2="&#x173;" k="6" />
    <hkern u1="&#x2c;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x2c;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2c;" u2="&#x21b;" k="60" />
    <hkern u1="&#x2c;" u2="&#x219;" k="5" />
    <hkern u1="&#x2c;" u2="&#x163;" k="60" />
    <hkern u1="&#x2c;" u2="&#x123;" k="10" />
    <hkern u1="&#x2c;" u2="&#x16b;" k="6" />
    <hkern u1="&#x2c;" u2="&#x171;" k="6" />
    <hkern u1="&#x2c;" u2="&#xf9;" k="6" />
    <hkern u1="&#x2c;" u2="&#xfc;" k="6" />
    <hkern u1="&#x2c;" u2="&#xfb;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16d;" k="6" />
    <hkern u1="&#x2c;" u2="&#xfa;" k="6" />
    <hkern u1="&#x2c;" u2="u" k="6" />
    <hkern u1="&#x2c;" u2="&#x165;" k="60" />
    <hkern u1="&#x2c;" u2="&#x167;" k="60" />
    <hkern u1="&#x2c;" u2="t" k="60" />
    <hkern u1="&#x2c;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2c;" u2="&#x161;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2c;" u2="s" k="5" />
    <hkern u1="&#x2c;" u2="q" k="10" />
    <hkern u1="&#x2c;" u2="&#x153;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2c;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2c;" u2="&#x151;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2c;" u2="o" k="10" />
    <hkern u1="&#x2c;" u2="&#x121;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2c;" u2="g" k="10" />
    <hkern u1="&#x2c;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2c;" u2="&#x109;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2c;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2c;" u2="&#x107;" k="10" />
    <hkern u1="&#x2c;" u2="c" k="10" />
    <hkern u1="&#x2c;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x2c;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2c;" u2="&#x21a;" k="60" />
    <hkern u1="&#x2c;" u2="&#x218;" k="5" />
    <hkern u1="&#x2c;" u2="&#x162;" k="60" />
    <hkern u1="&#x2c;" u2="&#x122;" k="10" />
    <hkern u1="&#x2c;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x2c;" u2="&#x178;" k="80" />
    <hkern u1="&#x2c;" u2="&#x176;" k="80" />
    <hkern u1="&#x2c;" u2="&#xdd;" k="80" />
    <hkern u1="&#x2c;" u2="Y" k="80" />
    <hkern u1="&#x2c;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x2c;" u2="&#x174;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x2c;" u2="W" k="40" />
    <hkern u1="&#x2c;" u2="V" k="60" />
    <hkern u1="&#x2c;" u2="&#x168;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16e;" k="6" />
    <hkern u1="&#x2c;" u2="&#x172;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16a;" k="6" />
    <hkern u1="&#x2c;" u2="&#x170;" k="6" />
    <hkern u1="&#x2c;" u2="&#xd9;" k="6" />
    <hkern u1="&#x2c;" u2="&#xdc;" k="6" />
    <hkern u1="&#x2c;" u2="&#xdb;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16c;" k="6" />
    <hkern u1="&#x2c;" u2="&#xda;" k="6" />
    <hkern u1="&#x2c;" u2="U" k="6" />
    <hkern u1="&#x2c;" u2="&#x164;" k="60" />
    <hkern u1="&#x2c;" u2="&#x166;" k="60" />
    <hkern u1="&#x2c;" u2="T" k="60" />
    <hkern u1="&#x2c;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2c;" u2="&#x160;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2c;" u2="S" k="5" />
    <hkern u1="&#x2c;" u2="Q" k="10" />
    <hkern u1="&#x2c;" u2="&#x152;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2c;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2c;" u2="&#x150;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2c;" u2="O" k="10" />
    <hkern u1="&#x2c;" u2="&#x120;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2c;" u2="G" k="10" />
    <hkern u1="&#x2c;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2c;" u2="&#x108;" k="10" />
    <hkern u1="&#x2c;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2c;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2c;" u2="&#x106;" k="10" />
    <hkern u1="&#x2c;" u2="C" k="10" />
    <hkern u1="&#x2026;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x2026;" u2="&#xff;" k="80" />
    <hkern u1="&#x2026;" u2="&#x177;" k="80" />
    <hkern u1="&#x2026;" u2="&#xfd;" k="80" />
    <hkern u1="&#x2026;" u2="y" k="80" />
    <hkern u1="&#x2026;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x2026;" u2="&#x175;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x2026;" u2="w" k="40" />
    <hkern u1="&#x2026;" u2="v" k="60" />
    <hkern u1="&#x2026;" u2="&#x169;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16f;" k="6" />
    <hkern u1="&#x2026;" u2="&#x173;" k="6" />
    <hkern u1="&#x2026;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x2026;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2026;" u2="&#x21b;" k="60" />
    <hkern u1="&#x2026;" u2="&#x219;" k="5" />
    <hkern u1="&#x2026;" u2="&#x163;" k="60" />
    <hkern u1="&#x2026;" u2="&#x123;" k="10" />
    <hkern u1="&#x2026;" u2="&#x16b;" k="6" />
    <hkern u1="&#x2026;" u2="&#x171;" k="6" />
    <hkern u1="&#x2026;" u2="&#xf9;" k="6" />
    <hkern u1="&#x2026;" u2="&#xfc;" k="6" />
    <hkern u1="&#x2026;" u2="&#xfb;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16d;" k="6" />
    <hkern u1="&#x2026;" u2="&#xfa;" k="6" />
    <hkern u1="&#x2026;" u2="u" k="6" />
    <hkern u1="&#x2026;" u2="&#x165;" k="60" />
    <hkern u1="&#x2026;" u2="&#x167;" k="60" />
    <hkern u1="&#x2026;" u2="t" k="60" />
    <hkern u1="&#x2026;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2026;" u2="&#x161;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2026;" u2="s" k="5" />
    <hkern u1="&#x2026;" u2="q" k="10" />
    <hkern u1="&#x2026;" u2="&#x153;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2026;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2026;" u2="&#x151;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2026;" u2="o" k="10" />
    <hkern u1="&#x2026;" u2="&#x121;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2026;" u2="g" k="10" />
    <hkern u1="&#x2026;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2026;" u2="&#x109;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2026;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2026;" u2="&#x107;" k="10" />
    <hkern u1="&#x2026;" u2="c" k="10" />
    <hkern u1="&#x2026;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x2026;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2026;" u2="&#x21a;" k="60" />
    <hkern u1="&#x2026;" u2="&#x218;" k="5" />
    <hkern u1="&#x2026;" u2="&#x162;" k="60" />
    <hkern u1="&#x2026;" u2="&#x122;" k="10" />
    <hkern u1="&#x2026;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x2026;" u2="&#x178;" k="80" />
    <hkern u1="&#x2026;" u2="&#x176;" k="80" />
    <hkern u1="&#x2026;" u2="&#xdd;" k="80" />
    <hkern u1="&#x2026;" u2="Y" k="80" />
    <hkern u1="&#x2026;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x2026;" u2="&#x174;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x2026;" u2="W" k="40" />
    <hkern u1="&#x2026;" u2="V" k="60" />
    <hkern u1="&#x2026;" u2="&#x168;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16e;" k="6" />
    <hkern u1="&#x2026;" u2="&#x172;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16a;" k="6" />
    <hkern u1="&#x2026;" u2="&#x170;" k="6" />
    <hkern u1="&#x2026;" u2="&#xd9;" k="6" />
    <hkern u1="&#x2026;" u2="&#xdc;" k="6" />
    <hkern u1="&#x2026;" u2="&#xdb;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16c;" k="6" />
    <hkern u1="&#x2026;" u2="&#xda;" k="6" />
    <hkern u1="&#x2026;" u2="U" k="6" />
    <hkern u1="&#x2026;" u2="&#x164;" k="60" />
    <hkern u1="&#x2026;" u2="&#x166;" k="60" />
    <hkern u1="&#x2026;" u2="T" k="60" />
    <hkern u1="&#x2026;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2026;" u2="&#x160;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2026;" u2="S" k="5" />
    <hkern u1="&#x2026;" u2="Q" k="10" />
    <hkern u1="&#x2026;" u2="&#x152;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2026;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2026;" u2="&#x150;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2026;" u2="O" k="10" />
    <hkern u1="&#x2026;" u2="&#x120;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2026;" u2="G" k="10" />
    <hkern u1="&#x2026;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2026;" u2="&#x108;" k="10" />
    <hkern u1="&#x2026;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2026;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2026;" u2="&#x106;" k="10" />
    <hkern u1="&#x2026;" u2="C" k="10" />
    <hkern u1="&#x2e;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x2e;" u2="&#xff;" k="80" />
    <hkern u1="&#x2e;" u2="&#x177;" k="80" />
    <hkern u1="&#x2e;" u2="&#xfd;" k="80" />
    <hkern u1="&#x2e;" u2="y" k="80" />
    <hkern u1="&#x2e;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x2e;" u2="&#x175;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x2e;" u2="w" k="40" />
    <hkern u1="&#x2e;" u2="v" k="60" />
    <hkern u1="&#x2e;" u2="&#x169;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16f;" k="6" />
    <hkern u1="&#x2e;" u2="&#x173;" k="6" />
    <hkern u1="&#x2e;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x2e;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2e;" u2="&#x21b;" k="60" />
    <hkern u1="&#x2e;" u2="&#x219;" k="5" />
    <hkern u1="&#x2e;" u2="&#x163;" k="60" />
    <hkern u1="&#x2e;" u2="&#x123;" k="10" />
    <hkern u1="&#x2e;" u2="&#x16b;" k="6" />
    <hkern u1="&#x2e;" u2="&#x171;" k="6" />
    <hkern u1="&#x2e;" u2="&#xf9;" k="6" />
    <hkern u1="&#x2e;" u2="&#xfc;" k="6" />
    <hkern u1="&#x2e;" u2="&#xfb;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16d;" k="6" />
    <hkern u1="&#x2e;" u2="&#xfa;" k="6" />
    <hkern u1="&#x2e;" u2="u" k="6" />
    <hkern u1="&#x2e;" u2="&#x165;" k="60" />
    <hkern u1="&#x2e;" u2="&#x167;" k="60" />
    <hkern u1="&#x2e;" u2="t" k="60" />
    <hkern u1="&#x2e;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2e;" u2="&#x161;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2e;" u2="s" k="5" />
    <hkern u1="&#x2e;" u2="q" k="10" />
    <hkern u1="&#x2e;" u2="&#x153;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2e;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2e;" u2="&#x151;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2e;" u2="o" k="10" />
    <hkern u1="&#x2e;" u2="&#x121;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2e;" u2="g" k="10" />
    <hkern u1="&#x2e;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2e;" u2="&#x109;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2e;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2e;" u2="&#x107;" k="10" />
    <hkern u1="&#x2e;" u2="c" k="10" />
    <hkern u1="&#x2e;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x2e;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2e;" u2="&#x21a;" k="60" />
    <hkern u1="&#x2e;" u2="&#x218;" k="5" />
    <hkern u1="&#x2e;" u2="&#x162;" k="60" />
    <hkern u1="&#x2e;" u2="&#x122;" k="10" />
    <hkern u1="&#x2e;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x2e;" u2="&#x178;" k="80" />
    <hkern u1="&#x2e;" u2="&#x176;" k="80" />
    <hkern u1="&#x2e;" u2="&#xdd;" k="80" />
    <hkern u1="&#x2e;" u2="Y" k="80" />
    <hkern u1="&#x2e;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x2e;" u2="&#x174;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x2e;" u2="W" k="40" />
    <hkern u1="&#x2e;" u2="V" k="60" />
    <hkern u1="&#x2e;" u2="&#x168;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16e;" k="6" />
    <hkern u1="&#x2e;" u2="&#x172;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16a;" k="6" />
    <hkern u1="&#x2e;" u2="&#x170;" k="6" />
    <hkern u1="&#x2e;" u2="&#xd9;" k="6" />
    <hkern u1="&#x2e;" u2="&#xdc;" k="6" />
    <hkern u1="&#x2e;" u2="&#xdb;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16c;" k="6" />
    <hkern u1="&#x2e;" u2="&#xda;" k="6" />
    <hkern u1="&#x2e;" u2="U" k="6" />
    <hkern u1="&#x2e;" u2="&#x164;" k="60" />
    <hkern u1="&#x2e;" u2="&#x166;" k="60" />
    <hkern u1="&#x2e;" u2="T" k="60" />
    <hkern u1="&#x2e;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2e;" u2="&#x160;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2e;" u2="S" k="5" />
    <hkern u1="&#x2e;" u2="Q" k="10" />
    <hkern u1="&#x2e;" u2="&#x152;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2e;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2e;" u2="&#x150;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2e;" u2="O" k="10" />
    <hkern u1="&#x2e;" u2="&#x120;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2e;" u2="G" k="10" />
    <hkern u1="&#x2e;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2e;" u2="&#x108;" k="10" />
    <hkern u1="&#x2e;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2e;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2e;" u2="&#x106;" k="10" />
    <hkern u1="&#x2e;" u2="C" k="10" />
    <hkern u1="&#xbf;" u2="&#x1ef3;" k="30" />
    <hkern u1="&#xbf;" u2="&#xff;" k="30" />
    <hkern u1="&#xbf;" u2="&#x177;" k="30" />
    <hkern u1="&#xbf;" u2="&#xfd;" k="30" />
    <hkern u1="&#xbf;" u2="y" k="30" />
    <hkern u1="&#xbf;" u2="&#x1e81;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e85;" k="20" />
    <hkern u1="&#xbf;" u2="&#x175;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e83;" k="20" />
    <hkern u1="&#xbf;" u2="w" k="20" />
    <hkern u1="&#xbf;" u2="v" k="30" />
    <hkern u1="&#xbf;" u2="&#x1e6b;" k="42" />
    <hkern u1="&#xbf;" u2="&#x21b;" k="42" />
    <hkern u1="&#xbf;" u2="&#x163;" k="42" />
    <hkern u1="&#xbf;" u2="&#x165;" k="42" />
    <hkern u1="&#xbf;" u2="&#x167;" k="42" />
    <hkern u1="&#xbf;" u2="t" k="42" />
    <hkern u1="&#xbf;" u2="&#x1e6a;" k="42" />
    <hkern u1="&#xbf;" u2="&#x21a;" k="42" />
    <hkern u1="&#xbf;" u2="&#x162;" k="42" />
    <hkern u1="&#xbf;" u2="&#x1ef2;" k="30" />
    <hkern u1="&#xbf;" u2="&#x178;" k="30" />
    <hkern u1="&#xbf;" u2="&#x176;" k="30" />
    <hkern u1="&#xbf;" u2="&#xdd;" k="30" />
    <hkern u1="&#xbf;" u2="Y" k="30" />
    <hkern u1="&#xbf;" u2="&#x1e80;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e84;" k="20" />
    <hkern u1="&#xbf;" u2="&#x174;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e82;" k="20" />
    <hkern u1="&#xbf;" u2="W" k="20" />
    <hkern u1="&#xbf;" u2="V" k="30" />
    <hkern u1="&#xbf;" u2="&#x164;" k="42" />
    <hkern u1="&#xbf;" u2="&#x166;" k="42" />
    <hkern u1="&#xbf;" u2="T" k="42" />
    <hkern u1="&#x22;" g2="four.alt" k="40" />
    <hkern u1="&#x27;" g2="four.alt" k="40" />
    <hkern u1="&#x3b;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x3b;" u2="&#xff;" k="20" />
    <hkern u1="&#x3b;" u2="&#x177;" k="20" />
    <hkern u1="&#x3b;" u2="&#xfd;" k="20" />
    <hkern u1="&#x3b;" u2="y" k="20" />
    <hkern u1="&#x3b;" u2="&#x1e6b;" k="35" />
    <hkern u1="&#x3b;" u2="&#x21b;" k="35" />
    <hkern u1="&#x3b;" u2="&#x163;" k="35" />
    <hkern u1="&#x3b;" u2="&#x165;" k="35" />
    <hkern u1="&#x3b;" u2="&#x167;" k="35" />
    <hkern u1="&#x3b;" u2="t" k="35" />
    <hkern u1="&#x3b;" u2="&#x1e6a;" k="35" />
    <hkern u1="&#x3b;" u2="&#x21a;" k="35" />
    <hkern u1="&#x3b;" u2="&#x162;" k="35" />
    <hkern u1="&#x3b;" u2="&#x1ef2;" k="20" />
    <hkern u1="&#x3b;" u2="&#x178;" k="20" />
    <hkern u1="&#x3b;" u2="&#x176;" k="20" />
    <hkern u1="&#x3b;" u2="&#xdd;" k="20" />
    <hkern u1="&#x3b;" u2="Y" k="20" />
    <hkern u1="&#x3b;" u2="&#x164;" k="35" />
    <hkern u1="&#x3b;" u2="&#x166;" k="35" />
    <hkern u1="&#x3b;" u2="T" k="35" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="180" />
    <hkern u1="&#x7b;" u2="&#x1ef3;" k="-5" />
    <hkern u1="&#x7b;" u2="&#xff;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x177;" k="-5" />
    <hkern u1="&#x7b;" u2="&#xfd;" k="-5" />
    <hkern u1="&#x7b;" u2="y" k="-5" />
    <hkern u1="&#x7b;" u2="&#x1e81;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e85;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x175;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e83;" k="-3" />
    <hkern u1="&#x7b;" u2="w" k="-3" />
    <hkern u1="&#x7b;" u2="v" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e6b;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x21b;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x163;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x165;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x167;" k="-8" />
    <hkern u1="&#x7b;" u2="t" k="-8" />
    <hkern u1="&#x7b;" u2="&#x1e6a;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x21a;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x162;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x1ef2;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x178;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x176;" k="-5" />
    <hkern u1="&#x7b;" u2="&#xdd;" k="-5" />
    <hkern u1="&#x7b;" u2="Y" k="-5" />
    <hkern u1="&#x7b;" u2="&#x1e80;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e84;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x174;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e82;" k="-3" />
    <hkern u1="&#x7b;" u2="W" k="-3" />
    <hkern u1="&#x7b;" u2="V" k="-3" />
    <hkern u1="&#x7b;" u2="&#x164;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x166;" k="-8" />
    <hkern u1="&#x7b;" u2="T" k="-8" />
    <hkern u1="[" u2="&#x1ef3;" k="-5" />
    <hkern u1="[" u2="&#xff;" k="-5" />
    <hkern u1="[" u2="&#x177;" k="-5" />
    <hkern u1="[" u2="&#xfd;" k="-5" />
    <hkern u1="[" u2="y" k="-5" />
    <hkern u1="[" u2="&#x1e81;" k="-3" />
    <hkern u1="[" u2="&#x1e85;" k="-3" />
    <hkern u1="[" u2="&#x175;" k="-3" />
    <hkern u1="[" u2="&#x1e83;" k="-3" />
    <hkern u1="[" u2="w" k="-3" />
    <hkern u1="[" u2="v" k="-3" />
    <hkern u1="[" u2="&#x1e6b;" k="-8" />
    <hkern u1="[" u2="&#x21b;" k="-8" />
    <hkern u1="[" u2="&#x163;" k="-8" />
    <hkern u1="[" u2="&#x165;" k="-8" />
    <hkern u1="[" u2="&#x167;" k="-8" />
    <hkern u1="[" u2="t" k="-8" />
    <hkern u1="[" u2="&#x1e6a;" k="-8" />
    <hkern u1="[" u2="&#x21a;" k="-8" />
    <hkern u1="[" u2="&#x162;" k="-8" />
    <hkern u1="[" u2="&#x1ef2;" k="-5" />
    <hkern u1="[" u2="&#x178;" k="-5" />
    <hkern u1="[" u2="&#x176;" k="-5" />
    <hkern u1="[" u2="&#xdd;" k="-5" />
    <hkern u1="[" u2="Y" k="-5" />
    <hkern u1="[" u2="&#x1e80;" k="-3" />
    <hkern u1="[" u2="&#x1e84;" k="-3" />
    <hkern u1="[" u2="&#x174;" k="-3" />
    <hkern u1="[" u2="&#x1e82;" k="-3" />
    <hkern u1="[" u2="W" k="-3" />
    <hkern u1="[" u2="V" k="-3" />
    <hkern u1="[" u2="&#x164;" k="-8" />
    <hkern u1="[" u2="&#x166;" k="-8" />
    <hkern u1="[" u2="T" k="-8" />
    <hkern u1="&#x28;" u2="&#x1ef3;" k="-5" />
    <hkern u1="&#x28;" u2="&#xff;" k="-5" />
    <hkern u1="&#x28;" u2="&#x177;" k="-5" />
    <hkern u1="&#x28;" u2="&#xfd;" k="-5" />
    <hkern u1="&#x28;" u2="y" k="-5" />
    <hkern u1="&#x28;" u2="&#x1e81;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e85;" k="-3" />
    <hkern u1="&#x28;" u2="&#x175;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e83;" k="-3" />
    <hkern u1="&#x28;" u2="w" k="-3" />
    <hkern u1="&#x28;" u2="v" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e6b;" k="-8" />
    <hkern u1="&#x28;" u2="&#x21b;" k="-8" />
    <hkern u1="&#x28;" u2="&#x163;" k="-8" />
    <hkern u1="&#x28;" u2="&#x165;" k="-8" />
    <hkern u1="&#x28;" u2="&#x167;" k="-8" />
    <hkern u1="&#x28;" u2="t" k="-8" />
    <hkern u1="&#x28;" u2="&#x1e6a;" k="-8" />
    <hkern u1="&#x28;" u2="&#x21a;" k="-8" />
    <hkern u1="&#x28;" u2="&#x162;" k="-8" />
    <hkern u1="&#x28;" u2="&#x1ef2;" k="-5" />
    <hkern u1="&#x28;" u2="&#x178;" k="-5" />
    <hkern u1="&#x28;" u2="&#x176;" k="-5" />
    <hkern u1="&#x28;" u2="&#xdd;" k="-5" />
    <hkern u1="&#x28;" u2="Y" k="-5" />
    <hkern u1="&#x28;" u2="&#x1e80;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e84;" k="-3" />
    <hkern u1="&#x28;" u2="&#x174;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e82;" k="-3" />
    <hkern u1="&#x28;" u2="W" k="-3" />
    <hkern u1="&#x28;" u2="V" k="-3" />
    <hkern u1="&#x28;" u2="&#x164;" k="-8" />
    <hkern u1="&#x28;" u2="&#x166;" k="-8" />
    <hkern u1="&#x28;" u2="T" k="-8" />
    <hkern u1="&#x2d;" u2="x" k="15" />
    <hkern u1="&#x2d;" u2="v" k="10" />
    <hkern u1="&#x2d;" u2="X" k="15" />
    <hkern u1="&#x2d;" u2="V" k="10" />
    <hkern u1="&#xad;" u2="x" k="15" />
    <hkern u1="&#xad;" u2="v" k="10" />
    <hkern u1="&#xad;" u2="X" k="15" />
    <hkern u1="&#xad;" u2="V" k="10" />
    <hkern u1="&#x201e;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x201e;" u2="&#xff;" k="80" />
    <hkern u1="&#x201e;" u2="&#x177;" k="80" />
    <hkern u1="&#x201e;" u2="&#xfd;" k="80" />
    <hkern u1="&#x201e;" u2="y" k="80" />
    <hkern u1="&#x201e;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x201e;" u2="&#x175;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x201e;" u2="w" k="40" />
    <hkern u1="&#x201e;" u2="v" k="60" />
    <hkern u1="&#x201e;" u2="&#x169;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16f;" k="6" />
    <hkern u1="&#x201e;" u2="&#x173;" k="6" />
    <hkern u1="&#x201e;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x201e;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201e;" u2="&#x21b;" k="60" />
    <hkern u1="&#x201e;" u2="&#x219;" k="5" />
    <hkern u1="&#x201e;" u2="&#x163;" k="60" />
    <hkern u1="&#x201e;" u2="&#x123;" k="10" />
    <hkern u1="&#x201e;" u2="&#x16b;" k="6" />
    <hkern u1="&#x201e;" u2="&#x171;" k="6" />
    <hkern u1="&#x201e;" u2="&#xf9;" k="6" />
    <hkern u1="&#x201e;" u2="&#xfc;" k="6" />
    <hkern u1="&#x201e;" u2="&#xfb;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16d;" k="6" />
    <hkern u1="&#x201e;" u2="&#xfa;" k="6" />
    <hkern u1="&#x201e;" u2="u" k="6" />
    <hkern u1="&#x201e;" u2="&#x165;" k="60" />
    <hkern u1="&#x201e;" u2="&#x167;" k="60" />
    <hkern u1="&#x201e;" u2="t" k="60" />
    <hkern u1="&#x201e;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201e;" u2="&#x161;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201e;" u2="s" k="5" />
    <hkern u1="&#x201e;" u2="q" k="10" />
    <hkern u1="&#x201e;" u2="&#x153;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201e;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201e;" u2="&#x151;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201e;" u2="o" k="10" />
    <hkern u1="&#x201e;" u2="&#x121;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201e;" u2="g" k="10" />
    <hkern u1="&#x201e;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201e;" u2="&#x109;" k="10" />
    <hkern u1="&#x201e;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201e;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201e;" u2="&#x107;" k="10" />
    <hkern u1="&#x201e;" u2="c" k="10" />
    <hkern u1="&#x201e;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x201e;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201e;" u2="&#x21a;" k="60" />
    <hkern u1="&#x201e;" u2="&#x218;" k="5" />
    <hkern u1="&#x201e;" u2="&#x162;" k="60" />
    <hkern u1="&#x201e;" u2="&#x122;" k="10" />
    <hkern u1="&#x201e;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x201e;" u2="&#x178;" k="80" />
    <hkern u1="&#x201e;" u2="&#x176;" k="80" />
    <hkern u1="&#x201e;" u2="&#xdd;" k="80" />
    <hkern u1="&#x201e;" u2="Y" k="80" />
    <hkern u1="&#x201e;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x201e;" u2="&#x174;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x201e;" u2="W" k="40" />
    <hkern u1="&#x201e;" u2="V" k="60" />
    <hkern u1="&#x201e;" u2="&#x168;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16e;" k="6" />
    <hkern u1="&#x201e;" u2="&#x172;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16a;" k="6" />
    <hkern u1="&#x201e;" u2="&#x170;" k="6" />
    <hkern u1="&#x201e;" u2="&#xd9;" k="6" />
    <hkern u1="&#x201e;" u2="&#xdc;" k="6" />
    <hkern u1="&#x201e;" u2="&#xdb;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16c;" k="6" />
    <hkern u1="&#x201e;" u2="&#xda;" k="6" />
    <hkern u1="&#x201e;" u2="U" k="6" />
    <hkern u1="&#x201e;" u2="&#x164;" k="60" />
    <hkern u1="&#x201e;" u2="&#x166;" k="60" />
    <hkern u1="&#x201e;" u2="T" k="60" />
    <hkern u1="&#x201e;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201e;" u2="&#x160;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201e;" u2="S" k="5" />
    <hkern u1="&#x201e;" u2="Q" k="10" />
    <hkern u1="&#x201e;" u2="&#x152;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201e;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201e;" u2="&#x150;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201e;" u2="O" k="10" />
    <hkern u1="&#x201e;" u2="&#x120;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201e;" u2="G" k="10" />
    <hkern u1="&#x201e;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201e;" u2="&#x108;" k="10" />
    <hkern u1="&#x201e;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201e;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201e;" u2="&#x106;" k="10" />
    <hkern u1="&#x201e;" u2="C" k="10" />
    <hkern u1="&#x201c;" u2="&#xc1;" k="60" />
    <hkern u1="&#x201c;" u2="A" k="60" />
    <hkern u1="&#x201c;" u2="&#x102;" k="60" />
    <hkern u1="&#x201c;" u2="&#x17c;" k="4" />
    <hkern u1="&#x201c;" u2="&#x17e;" k="4" />
    <hkern u1="&#x201c;" u2="&#x17a;" k="4" />
    <hkern u1="&#x201c;" u2="z" k="4" />
    <hkern u1="&#x201c;" u2="x" k="2" />
    <hkern u1="&#x201c;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201c;" u2="&#x219;" k="5" />
    <hkern u1="&#x201c;" u2="&#x123;" k="10" />
    <hkern u1="&#x201c;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201c;" u2="&#x161;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201c;" u2="s" k="5" />
    <hkern u1="&#x201c;" u2="q" k="10" />
    <hkern u1="&#x201c;" u2="&#x153;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201c;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201c;" u2="&#x151;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201c;" u2="o" k="10" />
    <hkern u1="&#x201c;" u2="&#x135;" k="40" />
    <hkern u1="&#x201c;" u2="&#x237;" k="40" />
    <hkern u1="&#x201c;" u2="j" k="40" />
    <hkern u1="&#x201c;" u2="&#x133;" k="40" />
    <hkern u1="&#x201c;" u2="&#x121;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201c;" u2="g" k="10" />
    <hkern u1="&#x201c;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201c;" u2="&#x109;" k="10" />
    <hkern u1="&#x201c;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201c;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201c;" u2="&#x107;" k="10" />
    <hkern u1="&#x201c;" u2="c" k="10" />
    <hkern u1="&#x201c;" u2="&#xe3;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe5;" k="60" />
    <hkern u1="&#x201c;" u2="&#x105;" k="60" />
    <hkern u1="&#x201c;" u2="&#x101;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe0;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe4;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe2;" k="60" />
    <hkern u1="&#x201c;" u2="&#x103;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe1;" k="60" />
    <hkern u1="&#x201c;" u2="a" k="60" />
    <hkern u1="&#x201c;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201c;" u2="&#x218;" k="5" />
    <hkern u1="&#x201c;" u2="&#x122;" k="10" />
    <hkern u1="&#x201c;" u2="&#x17b;" k="4" />
    <hkern u1="&#x201c;" u2="&#x17d;" k="4" />
    <hkern u1="&#x201c;" u2="&#x179;" k="4" />
    <hkern u1="&#x201c;" u2="Z" k="4" />
    <hkern u1="&#x201c;" u2="X" k="2" />
    <hkern u1="&#x201c;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201c;" u2="&#x160;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201c;" u2="S" k="5" />
    <hkern u1="&#x201c;" u2="Q" k="10" />
    <hkern u1="&#x201c;" u2="&#x152;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201c;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201c;" u2="&#x150;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201c;" u2="O" k="10" />
    <hkern u1="&#x201c;" u2="&#x134;" k="40" />
    <hkern u1="&#x201c;" u2="J" k="40" />
    <hkern u1="&#x201c;" u2="&#x132;" k="40" />
    <hkern u1="&#x201c;" u2="&#x120;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201c;" u2="G" k="10" />
    <hkern u1="&#x201c;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201c;" u2="&#x108;" k="10" />
    <hkern u1="&#x201c;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201c;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201c;" u2="&#x106;" k="10" />
    <hkern u1="&#x201c;" u2="C" k="10" />
    <hkern u1="&#x201c;" u2="&#xc3;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc5;" k="60" />
    <hkern u1="&#x201c;" u2="&#x104;" k="60" />
    <hkern u1="&#x201c;" u2="&#x100;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc0;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc4;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc2;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc1;" k="60" />
    <hkern u1="&#x201d;" u2="A" k="60" />
    <hkern u1="&#x201d;" u2="&#x102;" k="60" />
    <hkern u1="&#x201d;" u2="&#x17c;" k="4" />
    <hkern u1="&#x201d;" u2="&#x17e;" k="4" />
    <hkern u1="&#x201d;" u2="&#x17a;" k="4" />
    <hkern u1="&#x201d;" u2="z" k="4" />
    <hkern u1="&#x201d;" u2="x" k="2" />
    <hkern u1="&#x201d;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201d;" u2="&#x219;" k="5" />
    <hkern u1="&#x201d;" u2="&#x123;" k="10" />
    <hkern u1="&#x201d;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201d;" u2="&#x161;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201d;" u2="s" k="5" />
    <hkern u1="&#x201d;" u2="q" k="10" />
    <hkern u1="&#x201d;" u2="&#x153;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201d;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201d;" u2="&#x151;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201d;" u2="o" k="10" />
    <hkern u1="&#x201d;" u2="&#x135;" k="40" />
    <hkern u1="&#x201d;" u2="&#x237;" k="40" />
    <hkern u1="&#x201d;" u2="j" k="40" />
    <hkern u1="&#x201d;" u2="&#x133;" k="40" />
    <hkern u1="&#x201d;" u2="&#x121;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201d;" u2="g" k="10" />
    <hkern u1="&#x201d;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201d;" u2="&#x109;" k="10" />
    <hkern u1="&#x201d;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201d;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201d;" u2="&#x107;" k="10" />
    <hkern u1="&#x201d;" u2="c" k="10" />
    <hkern u1="&#x201d;" u2="&#xe3;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe5;" k="60" />
    <hkern u1="&#x201d;" u2="&#x105;" k="60" />
    <hkern u1="&#x201d;" u2="&#x101;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe0;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe4;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe2;" k="60" />
    <hkern u1="&#x201d;" u2="&#x103;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe1;" k="60" />
    <hkern u1="&#x201d;" u2="a" k="60" />
    <hkern u1="&#x201d;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201d;" u2="&#x218;" k="5" />
    <hkern u1="&#x201d;" u2="&#x122;" k="10" />
    <hkern u1="&#x201d;" u2="&#x17b;" k="4" />
    <hkern u1="&#x201d;" u2="&#x17d;" k="4" />
    <hkern u1="&#x201d;" u2="&#x179;" k="4" />
    <hkern u1="&#x201d;" u2="Z" k="4" />
    <hkern u1="&#x201d;" u2="X" k="2" />
    <hkern u1="&#x201d;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201d;" u2="&#x160;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201d;" u2="S" k="5" />
    <hkern u1="&#x201d;" u2="Q" k="10" />
    <hkern u1="&#x201d;" u2="&#x152;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201d;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201d;" u2="&#x150;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201d;" u2="O" k="10" />
    <hkern u1="&#x201d;" u2="&#x134;" k="40" />
    <hkern u1="&#x201d;" u2="J" k="40" />
    <hkern u1="&#x201d;" u2="&#x132;" k="40" />
    <hkern u1="&#x201d;" u2="&#x120;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201d;" u2="G" k="10" />
    <hkern u1="&#x201d;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201d;" u2="&#x108;" k="10" />
    <hkern u1="&#x201d;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201d;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201d;" u2="&#x106;" k="10" />
    <hkern u1="&#x201d;" u2="C" k="10" />
    <hkern u1="&#x201d;" u2="&#xc3;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc5;" k="60" />
    <hkern u1="&#x201d;" u2="&#x104;" k="60" />
    <hkern u1="&#x201d;" u2="&#x100;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc0;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc4;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc2;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc1;" k="60" />
    <hkern u1="&#x2018;" u2="A" k="60" />
    <hkern u1="&#x2018;" u2="&#x102;" k="60" />
    <hkern u1="&#x2018;" u2="&#x17c;" k="4" />
    <hkern u1="&#x2018;" u2="&#x17e;" k="4" />
    <hkern u1="&#x2018;" u2="&#x17a;" k="4" />
    <hkern u1="&#x2018;" u2="z" k="4" />
    <hkern u1="&#x2018;" u2="x" k="2" />
    <hkern u1="&#x2018;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2018;" u2="&#x219;" k="5" />
    <hkern u1="&#x2018;" u2="&#x123;" k="10" />
    <hkern u1="&#x2018;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2018;" u2="&#x161;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2018;" u2="s" k="5" />
    <hkern u1="&#x2018;" u2="q" k="10" />
    <hkern u1="&#x2018;" u2="&#x153;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2018;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2018;" u2="&#x151;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2018;" u2="o" k="10" />
    <hkern u1="&#x2018;" u2="&#x135;" k="40" />
    <hkern u1="&#x2018;" u2="&#x237;" k="40" />
    <hkern u1="&#x2018;" u2="j" k="40" />
    <hkern u1="&#x2018;" u2="&#x133;" k="40" />
    <hkern u1="&#x2018;" u2="&#x121;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2018;" u2="g" k="10" />
    <hkern u1="&#x2018;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2018;" u2="&#x109;" k="10" />
    <hkern u1="&#x2018;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2018;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2018;" u2="&#x107;" k="10" />
    <hkern u1="&#x2018;" u2="c" k="10" />
    <hkern u1="&#x2018;" u2="&#xe3;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe5;" k="60" />
    <hkern u1="&#x2018;" u2="&#x105;" k="60" />
    <hkern u1="&#x2018;" u2="&#x101;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe0;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe4;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe2;" k="60" />
    <hkern u1="&#x2018;" u2="&#x103;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe1;" k="60" />
    <hkern u1="&#x2018;" u2="a" k="60" />
    <hkern u1="&#x2018;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2018;" u2="&#x218;" k="5" />
    <hkern u1="&#x2018;" u2="&#x122;" k="10" />
    <hkern u1="&#x2018;" u2="&#x17b;" k="4" />
    <hkern u1="&#x2018;" u2="&#x17d;" k="4" />
    <hkern u1="&#x2018;" u2="&#x179;" k="4" />
    <hkern u1="&#x2018;" u2="Z" k="4" />
    <hkern u1="&#x2018;" u2="X" k="2" />
    <hkern u1="&#x2018;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2018;" u2="&#x160;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2018;" u2="S" k="5" />
    <hkern u1="&#x2018;" u2="Q" k="10" />
    <hkern u1="&#x2018;" u2="&#x152;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2018;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2018;" u2="&#x150;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2018;" u2="O" k="10" />
    <hkern u1="&#x2018;" u2="&#x134;" k="40" />
    <hkern u1="&#x2018;" u2="J" k="40" />
    <hkern u1="&#x2018;" u2="&#x132;" k="40" />
    <hkern u1="&#x2018;" u2="&#x120;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2018;" u2="G" k="10" />
    <hkern u1="&#x2018;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2018;" u2="&#x108;" k="10" />
    <hkern u1="&#x2018;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2018;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2018;" u2="&#x106;" k="10" />
    <hkern u1="&#x2018;" u2="C" k="10" />
    <hkern u1="&#x2018;" u2="&#xc3;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc5;" k="60" />
    <hkern u1="&#x2018;" u2="&#x104;" k="60" />
    <hkern u1="&#x2018;" u2="&#x100;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc0;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc4;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc2;" k="60" />
    <hkern u1="&#x2019;" u2="x" k="2" />
    <hkern u1="&#x2019;" u2="q" k="10" />
    <hkern u1="&#x2019;" u2="&#x153;" k="10" />
    <hkern u1="&#x2019;" u2="X" k="2" />
    <hkern u1="&#x2019;" u2="Q" k="10" />
    <hkern u1="&#x2019;" u2="&#x152;" k="10" />
    <hkern u1="&#x201a;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x201a;" u2="&#xff;" k="80" />
    <hkern u1="&#x201a;" u2="&#x177;" k="80" />
    <hkern u1="&#x201a;" u2="&#xfd;" k="80" />
    <hkern u1="&#x201a;" u2="y" k="80" />
    <hkern u1="&#x201a;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x201a;" u2="&#x175;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x201a;" u2="w" k="40" />
    <hkern u1="&#x201a;" u2="v" k="60" />
    <hkern u1="&#x201a;" u2="&#x169;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16f;" k="6" />
    <hkern u1="&#x201a;" u2="&#x173;" k="6" />
    <hkern u1="&#x201a;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x201a;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201a;" u2="&#x21b;" k="60" />
    <hkern u1="&#x201a;" u2="&#x219;" k="5" />
    <hkern u1="&#x201a;" u2="&#x163;" k="60" />
    <hkern u1="&#x201a;" u2="&#x123;" k="10" />
    <hkern u1="&#x201a;" u2="&#x16b;" k="6" />
    <hkern u1="&#x201a;" u2="&#x171;" k="6" />
    <hkern u1="&#x201a;" u2="&#xf9;" k="6" />
    <hkern u1="&#x201a;" u2="&#xfc;" k="6" />
    <hkern u1="&#x201a;" u2="&#xfb;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16d;" k="6" />
    <hkern u1="&#x201a;" u2="&#xfa;" k="6" />
    <hkern u1="&#x201a;" u2="u" k="6" />
    <hkern u1="&#x201a;" u2="&#x165;" k="60" />
    <hkern u1="&#x201a;" u2="&#x167;" k="60" />
    <hkern u1="&#x201a;" u2="t" k="60" />
    <hkern u1="&#x201a;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201a;" u2="&#x161;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201a;" u2="s" k="5" />
    <hkern u1="&#x201a;" u2="q" k="10" />
    <hkern u1="&#x201a;" u2="&#x153;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201a;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201a;" u2="&#x151;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201a;" u2="o" k="10" />
    <hkern u1="&#x201a;" u2="&#x121;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201a;" u2="g" k="10" />
    <hkern u1="&#x201a;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201a;" u2="&#x109;" k="10" />
    <hkern u1="&#x201a;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201a;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201a;" u2="&#x107;" k="10" />
    <hkern u1="&#x201a;" u2="c" k="10" />
    <hkern u1="&#x201a;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x201a;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201a;" u2="&#x21a;" k="60" />
    <hkern u1="&#x201a;" u2="&#x218;" k="5" />
    <hkern u1="&#x201a;" u2="&#x162;" k="60" />
    <hkern u1="&#x201a;" u2="&#x122;" k="10" />
    <hkern u1="&#x201a;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x201a;" u2="&#x178;" k="80" />
    <hkern u1="&#x201a;" u2="&#x176;" k="80" />
    <hkern u1="&#x201a;" u2="&#xdd;" k="80" />
    <hkern u1="&#x201a;" u2="Y" k="80" />
    <hkern u1="&#x201a;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x201a;" u2="&#x174;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x201a;" u2="W" k="40" />
    <hkern u1="&#x201a;" u2="V" k="60" />
    <hkern u1="&#x201a;" u2="&#x168;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16e;" k="6" />
    <hkern u1="&#x201a;" u2="&#x172;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16a;" k="6" />
    <hkern u1="&#x201a;" u2="&#x170;" k="6" />
    <hkern u1="&#x201a;" u2="&#xd9;" k="6" />
    <hkern u1="&#x201a;" u2="&#xdc;" k="6" />
    <hkern u1="&#x201a;" u2="&#xdb;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16c;" k="6" />
    <hkern u1="&#x201a;" u2="&#xda;" k="6" />
    <hkern u1="&#x201a;" u2="U" k="6" />
    <hkern u1="&#x201a;" u2="&#x164;" k="60" />
    <hkern u1="&#x201a;" u2="&#x166;" k="60" />
    <hkern u1="&#x201a;" u2="T" k="60" />
    <hkern u1="&#x201a;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201a;" u2="&#x160;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201a;" u2="S" k="5" />
    <hkern u1="&#x201a;" u2="Q" k="10" />
    <hkern u1="&#x201a;" u2="&#x152;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201a;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201a;" u2="&#x150;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201a;" u2="O" k="10" />
    <hkern u1="&#x201a;" u2="&#x120;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201a;" u2="G" k="10" />
    <hkern u1="&#x201a;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201a;" u2="&#x108;" k="10" />
    <hkern u1="&#x201a;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201a;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201a;" u2="&#x106;" k="10" />
    <hkern u1="&#x201a;" u2="C" k="10" />
    <hkern u1="&#x102;" u2="&#x2018;" k="60" />
    <hkern u1="&#x102;" u2="&#x201d;" k="60" />
    <hkern u1="&#x102;" u2="&#x201c;" k="60" />
    <hkern u1="&#x102;" u2="&#x3f;" k="30" />
    <hkern u1="&#x102;" u2="v" k="25" />
    <hkern u1="&#x102;" u2="q" k="1" />
    <hkern u1="&#x102;" u2="&#x153;" k="1" />
    <hkern u1="&#x102;" u2="V" k="25" />
    <hkern u1="&#x102;" u2="Q" k="1" />
    <hkern u1="&#x102;" u2="&#x152;" k="1" />
    <hkern u1="&#x2bc;" u2="x" k="2" />
    <hkern u1="&#x2bc;" u2="q" k="10" />
    <hkern u1="&#x2bc;" u2="&#x153;" k="10" />
    <hkern u1="&#x2bc;" u2="X" k="2" />
    <hkern u1="&#x2bc;" u2="Q" k="10" />
    <hkern u1="&#x2bc;" u2="&#x152;" k="10" />
    <hkern u1="A" u2="&#x2018;" k="60" />
    <hkern u1="A" u2="&#x201d;" k="60" />
    <hkern u1="A" u2="&#x201c;" k="60" />
    <hkern u1="A" u2="&#x3f;" k="30" />
    <hkern u1="A" u2="v" k="25" />
    <hkern u1="A" u2="q" k="1" />
    <hkern u1="A" u2="&#x153;" k="1" />
    <hkern u1="A" u2="V" k="25" />
    <hkern u1="A" u2="Q" k="1" />
    <hkern u1="A" u2="&#x152;" k="1" />
    <hkern u1="&#xc1;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc1;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc1;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc1;" u2="v" k="25" />
    <hkern u1="&#xc1;" u2="q" k="1" />
    <hkern u1="&#xc1;" u2="&#x153;" k="1" />
    <hkern u1="&#xc1;" u2="V" k="25" />
    <hkern u1="&#xc1;" u2="Q" k="1" />
    <hkern u1="&#xc1;" u2="&#x152;" k="1" />
    <hkern g1="hyphen,uni00AD"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="10" />
    <hkern g1="hyphen,uni00AD"
	g2="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	k="32" />
    <hkern g1="hyphen,uni00AD"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="4" />
    <hkern g1="hyphen,uni00AD"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="18" />
    <hkern g1="hyphen,uni00AD"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="10" />
    <hkern g1="hyphen,uni00AD"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="10" />
    <hkern g1="hyphen,uni00AD"
	g2="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	k="32" />
    <hkern g1="hyphen,uni00AD"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="4" />
    <hkern g1="hyphen,uni00AD"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="18" />
    <hkern g1="hyphen,uni00AD"
	g2="z,zacute,zcaron,zdotaccent"
	k="10" />
    <hkern g1="B,uni1E02"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="1" />
    <hkern g1="B,uni1E02"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="B,uni1E02"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="1" />
    <hkern g1="B,uni1E02"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="B,uni1E02"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="11" />
    <hkern g1="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="11" />
    <hkern g1="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	g2="quoteright,uni02BC"
	k="6" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="1" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="1" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="F,uni1E1E"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="13" />
    <hkern g1="F,uni1E1E"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="10" />
    <hkern g1="F,uni1E1E"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="13" />
    <hkern g1="F,uni1E1E"
	g2="z,zacute,zcaron,zdotaccent"
	k="10" />
    <hkern g1="F,uni1E1E"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="F,uni1E1E"
	g2="ae,aeacute"
	k="40" />
    <hkern g1="F,uni1E1E"
	g2="IJ,J,Jcircumflex"
	k="20" />
    <hkern g1="F,uni1E1E"
	g2="ij,j,uni0237,jcircumflex"
	k="20" />
    <hkern g1="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="1" />
    <hkern g1="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="1" />
    <hkern g1="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="J,Jcircumflex"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="J,Jcircumflex"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="K,uni0136"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	k="7" />
    <hkern g1="K,uni0136"
	g2="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	k="7" />
    <hkern g1="K,uni0136"
	g2="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	k="7" />
    <hkern g1="K,uni0136"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	k="5" />
    <hkern g1="K,uni0136"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde"
	k="4" />
    <hkern g1="K,uni0136"
	g2="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	k="7" />
    <hkern g1="K,uni0136"
	g2="g,gbreve,gcircumflex,gdotaccent,uni0123"
	k="7" />
    <hkern g1="K,uni0136"
	g2="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	k="7" />
    <hkern g1="K,uni0136"
	g2="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	k="5" />
    <hkern g1="K,uni0136"
	g2="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde"
	k="4" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	k="50" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="17" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="62" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	k="50" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="17" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="62" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="quoteright,uni02BC"
	k="75" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="IJ,J,Jcircumflex"
	k="-5" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="ij,j,uni0237,jcircumflex"
	k="-5" />
    <hkern g1="L,Lacute,Ldot,Lslash,uni013B"
	g2="hyphen,uni00AD"
	k="40" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="1" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="1" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="P,uni1E56"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="13" />
    <hkern g1="P,uni1E56"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="2" />
    <hkern g1="P,uni1E56"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="13" />
    <hkern g1="P,uni1E56"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="2" />
    <hkern g1="P,uni1E56"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="P,uni1E56"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="P,uni1E56"
	g2="IJ,J,Jcircumflex"
	k="18" />
    <hkern g1="P,uni1E56"
	g2="ij,j,uni0237,jcircumflex"
	k="18" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="R,Racute,Rcaron,uni0156"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="10" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="7" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="7" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	g2="quoteright,uni02BC"
	k="5" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="38" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="38" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	g2="AE,AEacute"
	k="50" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	g2="ae,aeacute"
	k="50" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	g2="IJ,J,Jcircumflex"
	k="30" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	g2="ij,j,uni0237,jcircumflex"
	k="30" />
    <hkern g1="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	g2="hyphen,uni00AD"
	k="32" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="15" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="15" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="AE,AEacute"
	k="25" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="ae,aeacute"
	k="25" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="IJ,J,Jcircumflex"
	k="13" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="ij,j,uni0237,jcircumflex"
	k="13" />
    <hkern g1="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	g2="hyphen,uni00AD"
	k="4" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="49" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="49" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="AE,AEacute"
	k="63" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ae,aeacute"
	k="63" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="IJ,J,Jcircumflex"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ij,j,uni0237,jcircumflex"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	k="7" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="g,gbreve,gcircumflex,gdotaccent,uni0123"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	k="7" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,uni00AD"
	k="18" />
    <hkern g1="Z,Zacute,Zcaron,Zdotaccent"
	g2="hyphen,uni00AD"
	k="15" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	k="38" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="15" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="49" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	k="38" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="15" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="49" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="quoteright,uni02BC"
	k="60" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	k="1" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	k="1" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	k="1" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	k="1" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="g,gbreve,gcircumflex,gdotaccent,uni0123"
	k="1" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	k="1" />
    <hkern g1="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	g2="hyphen,uni00AD"
	k="10" />
    <hkern g1="b,uni1E03"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="1" />
    <hkern g1="b,uni1E03"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="b,uni1E03"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="1" />
    <hkern g1="b,uni1E03"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="b,uni1E03"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="11" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="11" />
    <hkern g1="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	g2="quoteright,uni02BC"
	k="6" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="1" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="1" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="f,uni1E1F"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="13" />
    <hkern g1="f,uni1E1F"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="10" />
    <hkern g1="f,uni1E1F"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="13" />
    <hkern g1="f,uni1E1F"
	g2="z,zacute,zcaron,zdotaccent"
	k="10" />
    <hkern g1="f,uni1E1F"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="f,uni1E1F"
	g2="ae,aeacute"
	k="40" />
    <hkern g1="f,uni1E1F"
	g2="IJ,J,Jcircumflex"
	k="20" />
    <hkern g1="f,uni1E1F"
	g2="ij,j,uni0237,jcircumflex"
	k="20" />
    <hkern g1="g,gbreve,gcircumflex,gdotaccent,uni0123"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="1" />
    <hkern g1="g,gbreve,gcircumflex,gdotaccent,uni0123"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="g,gbreve,gcircumflex,gdotaccent,uni0123"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="1" />
    <hkern g1="g,gbreve,gcircumflex,gdotaccent,uni0123"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="g,gbreve,gcircumflex,gdotaccent,uni0123"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="g,gbreve,gcircumflex,gdotaccent,uni0123"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="g,gbreve,gcircumflex,gdotaccent,uni0123"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="j,uni0237,jcircumflex"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="j,uni0237,jcircumflex"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	k="7" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	k="7" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	k="7" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	k="5" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="U,Uacute,Ubreve,Ucircumflex,Udieresis,Ugrave,Uhungarumlaut,Umacron,Uogonek,Uring,Utilde"
	k="4" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	k="7" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="g,gbreve,gcircumflex,gdotaccent,uni0123"
	k="7" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	k="7" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	k="5" />
    <hkern g1="k,kgreenlandic,uni0137"
	g2="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde"
	k="4" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="T,Tbar,Tcaron,uni0162,uni021A,uni1E6A"
	k="50" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="W,Wacute,Wcircumflex,Wdieresis,Wgrave"
	k="17" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="62" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	k="50" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="w,wacute,wcircumflex,wdieresis,wgrave"
	k="17" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="62" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="quoteright,uni02BC"
	k="75" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="IJ,J,Jcircumflex"
	k="-5" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="ij,j,uni0237,jcircumflex"
	k="-5" />
    <hkern g1="l,lacute,ldot,lslash,uni013C"
	g2="hyphen,uni00AD"
	k="40" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="1" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="1" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="15" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	g2="quoteright,uni02BC"
	k="10" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="p,uni1E57"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="13" />
    <hkern g1="p,uni1E57"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="2" />
    <hkern g1="p,uni1E57"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="13" />
    <hkern g1="p,uni1E57"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="2" />
    <hkern g1="p,uni1E57"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="p,uni1E57"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="p,uni1E57"
	g2="IJ,J,Jcircumflex"
	k="18" />
    <hkern g1="p,uni1E57"
	g2="ij,j,uni0237,jcircumflex"
	k="18" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="r,racute,rcaron,uni0157"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="10" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="7" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	g2="y,yacute,ycircumflex,ydieresis,ygrave"
	k="7" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	g2="quoteright,uni02BC"
	k="5" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="38" />
    <hkern g1="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="38" />
    <hkern g1="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	g2="AE,AEacute"
	k="50" />
    <hkern g1="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	g2="ae,aeacute"
	k="50" />
    <hkern g1="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	g2="IJ,J,Jcircumflex"
	k="30" />
    <hkern g1="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	g2="ij,j,uni0237,jcircumflex"
	k="30" />
    <hkern g1="t,tbar,tcaron,uni0163,uni021B,uni1E6B"
	g2="hyphen,uni00AD"
	k="32" />
    <hkern g1="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="u,uacute,ubreve,ucircumflex,udieresis,ugrave,uhungarumlaut,umacron,uogonek,uring,utilde"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="15" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="15" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="AE,AEacute"
	k="25" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="ae,aeacute"
	k="25" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="IJ,J,Jcircumflex"
	k="13" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="ij,j,uni0237,jcircumflex"
	k="13" />
    <hkern g1="w,wacute,wcircumflex,wdieresis,wgrave"
	g2="hyphen,uni00AD"
	k="4" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="49" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="49" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="AE,AEacute"
	k="63" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="ae,aeacute"
	k="63" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="IJ,J,Jcircumflex"
	k="35" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="ij,j,uni0237,jcircumflex"
	k="35" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	k="15" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	k="15" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	k="15" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	k="7" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	k="15" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="g,gbreve,gcircumflex,gdotaccent,uni0123"
	k="15" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	k="15" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	k="7" />
    <hkern g1="y,yacute,ycircumflex,ydieresis,ygrave"
	g2="hyphen,uni00AD"
	k="18" />
    <hkern g1="z,zacute,zcaron,zdotaccent"
	g2="hyphen,uni00AD"
	k="15" />
    <hkern g1="quoteright,uni02BC"
	g2="Acircumflex,Adieresis,Agrave,Amacron,Aogonek,Aring,Atilde,Abreve,A,Aacute"
	k="60" />
    <hkern g1="quoteright,uni02BC"
	g2="Z,Zacute,Zcaron,Zdotaccent"
	k="4" />
    <hkern g1="quoteright,uni02BC"
	g2="a,aacute,abreve,acircumflex,adieresis,agrave,amacron,aogonek,aring,atilde"
	k="60" />
    <hkern g1="quoteright,uni02BC"
	g2="z,zacute,zcaron,zdotaccent"
	k="4" />
    <hkern g1="quoteright,uni02BC"
	g2="IJ,J,Jcircumflex"
	k="40" />
    <hkern g1="quoteright,uni02BC"
	g2="ij,j,uni0237,jcircumflex"
	k="40" />
    <hkern g1="quoteright,uni02BC"
	g2="C,Cacute,Ccaron,Ccedilla,Ccircumflex,Cdotaccent"
	k="10" />
    <hkern g1="quoteright,uni02BC"
	g2="G,Gbreve,Gcircumflex,Gdotaccent,uni0122"
	k="10" />
    <hkern g1="quoteright,uni02BC"
	g2="O,Oacute,Obreve,Ocircumflex,Odieresis,Ograve,Ohungarumlaut,Omacron,Oslash,Oslashacute,Otilde"
	k="10" />
    <hkern g1="quoteright,uni02BC"
	g2="S,Sacute,Scaron,Scedilla,Scircumflex,uni0218,uni1E60"
	k="5" />
    <hkern g1="quoteright,uni02BC"
	g2="c,cacute,ccaron,ccedilla,ccircumflex,cdotaccent"
	k="10" />
    <hkern g1="quoteright,uni02BC"
	g2="g,gbreve,gcircumflex,gdotaccent,uni0123"
	k="10" />
    <hkern g1="quoteright,uni02BC"
	g2="o,oacute,obreve,ocircumflex,odieresis,ograve,ohungarumlaut,omacron,oslash,oslashacute,otilde"
	k="10" />
    <hkern g1="quoteright,uni02BC"
	g2="s,sacute,scaron,scedilla,scircumflex,germandbls,uni0219,uni1E61"
	k="5" />
  </font>
</defs></svg>
