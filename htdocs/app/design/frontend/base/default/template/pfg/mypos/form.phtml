<?php
/**
 * MyPOS Payment Form Template
 *
 * @var $this PFG_MyPos_Block_Checkout_Form
 */
?>
<fieldset class="form-list">
    <?php $_code = $this->getMethodCode() ?>
    <ul id="payment_form_<?php echo $_code ?>" style="display:none;">
        <li>
            <div class="payment-method-content">
                <div class="payment-method-title">
                    <strong><?php echo $this->escapeHtml($this->getMethodTitle()) ?></strong>
                </div>
                
                <?php if ($this->getInstructions()): ?>
                <div class="payment-method-instructions">
                    <p><?php echo $this->escapeHtml($this->getInstructions()) ?></p>
                </div>
                <?php endif; ?>
                
                <div class="payment-method-description">
                    <p><?php echo $this->__('You will be redirected to MyPOS secure payment page to complete your payment.') ?></p>
                    
                    <?php if (!$this->isCurrencySupported()): ?>
                    <div class="payment-method-warning" style="color: #ff0000; font-weight: bold;">
                        <p><?php echo $this->__('Warning: Current currency (%s) is not supported by MyPOS. Supported currencies: %s', 
                            $this->getQuoteCurrency(), 
                            implode(', ', $this->getSupportedCurrencies())
                        ) ?></p>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($this->isSandboxMode()): ?>
                    <div class="payment-method-sandbox" style="color: #ff6600; font-weight: bold;">
                        <p><?php echo $this->__('SANDBOX MODE: This is a test environment. No real payments will be processed.') ?></p>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="payment-method-features">
                    <ul style="list-style: disc; margin-left: 20px;">
                        <li><?php echo $this->__('Secure payment processing') ?></li>
                        <li><?php echo $this->__('Support for major credit and debit cards') ?></li>
                        <li><?php echo $this->__('Real-time transaction processing') ?></li>
                        <li><?php echo $this->__('PCI DSS compliant') ?></li>
                    </ul>
                </div>
                
                <div class="payment-method-logos" style="margin-top: 10px;">
                    <img src="<?php echo $this->getSkinUrl('images/pfg/mypos/visa.png') ?>" alt="Visa" style="height: 30px; margin-right: 5px;" />
                    <img src="<?php echo $this->getSkinUrl('images/pfg/mypos/mastercard.png') ?>" alt="Mastercard" style="height: 30px; margin-right: 5px;" />
                    <img src="<?php echo $this->getSkinUrl('images/pfg/mypos/mypos-logo.png') ?>" alt="MyPOS" style="height: 30px;" />
                </div>
            </div>
        </li>
    </ul>
</fieldset>

<script type="text/javascript">
//<![CDATA[
document.observe('dom:loaded', function() {
    // Add any JavaScript functionality for the payment form here
    var paymentForm = $('payment_form_<?php echo $_code ?>');
    if (paymentForm) {
        // Form is ready
        console.log('MyPOS payment form loaded');
    }
});
//]]>
</script>
