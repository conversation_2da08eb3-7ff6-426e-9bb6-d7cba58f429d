<?php

    $order           = $this->getOrder();
    $speedyAddress   = $this->getSpeedyAddress();
    $allOffices      = $this->getAllOffices();
    $shippingAddress = $this->getOrder()->getShippingAddress();

    if (!$shippingAddress) {
        $shippingAddress = new Varien_Object();
    }
?>

<div class="entry-edit">
    <div class="entry-edit-head">
        <h4><?php echo $this->escapeHtml($this->getTabTitle()) ?></h4>
    </div>
</div>

<div class="fieldset">
    <form action="<?php echo $this->getPostActionUrl() ?>" id="stenik_speedy_shippinglabel_create_form" method="post">
        <input type="hidden" name="form_key" value="<?php echo $this->getFormKey() ?>">
        <div class="box-left">
            <div class="entry-edit">
                <div class="entry-edit-head collapseable">
                    <a id="speedy_shippinglabel_address_fieldset-head"
                       href="#"
                       onclick="Fieldset.toggleCollapse('speedy_shippinglabel_address_fieldset'); return false;"
                       class="open"
                    >
                        <?php echo $this->__('Receiver Data') ?>
                    </a>

                    <input type="hidden" id="speedy_shippinglabel_address_fieldset-state" value="<?php echo Mage::helper('stenik_speedy')->getShippingLabelDefaultCollapseReceiver() ? 0 : 1 ?>">
                </div>

                <fieldset class="fieldset collapseable" id="speedy_shippinglabel_address_fieldset">
                    <table cellspacing="0" class="form-list">
                        <tbody>
                            <tr>
                                <td class="label">
                                    <label for=""><?php echo $this->__('Firstname') ?> <span class="required">*</span></label>
                                </td>
                                <td class="value">
                                    <input type="text" class="required-entry" name="speedy_shippinglabel[receiver_data][firstname]" value="<?php echo $this->escapeHtml($shippingAddress->getFirstname()) ?>">
                                </td>
                            </tr>
                            <tr>
                                <td class="label">
                                    <label for=""><?php echo $this->__('Lastname') ?> <span class="required">*</span></label>
                                </td>
                                <td class="value">
                                    <input type="text" class="required-entry" name="speedy_shippinglabel[receiver_data][lastname]" value="<?php echo $this->escapeHtml($shippingAddress->getLastname()) ?>">
                                </td>
                            </tr>
                            <tr>
                                <td class="label">
                                    <label for=""><?php echo $this->__('Email') ?> <span class="required">*</span></label>
                                </td>
                                <td class="value">
                                    <input type="text" class="required-entry" name="speedy_shippinglabel[receiver_data][email]" value="<?php echo $this->escapeHtml($shippingAddress->getEmail() ? : $order->getCustomerEmail()) ?>">
                                </td>
                            </tr>
                            <tr>
                                <td class="label">
                                    <label><?php echo $this->__('Country') ?> <span class="required">*</span></label>
                                </td>
                                <td class="value">
                                    <strong>BG</strong>
                                </td>
                            </tr>
                            <tr>
                                <td class="label">
                                    <label for=""><?php echo $this->__('City') ?> <span class="required">*</span></label>
                                </td>
                                <td class="value">
                                    <input type="text" class="required-entry" id="speedy_shippinglabel:receiver_address:city" name="speedy_shippinglabel[receiver_address][city]" value="<?php echo $this->escapeHtml($speedyAddress->getCityName()) ?>">
                                </td>
                            </tr>
                            <tr>
                                <td class="label">
                                    <label for=""><?php echo $this->__('Postcode') ?> <span class="required">*</span></label>
                                </td>
                                <td class="value">
                                    <input type="text" class="required-entry" name="speedy_shippinglabel[receiver_address][postcode]" value="<?php echo $this->escapeHtml($shippingAddress->getPostcode()) ?>">
                                </td>
                            </tr>
                            <tr>
                                <td class="label">
                                    <label for=""><?php echo $this->__('Phone') ?> <span class="required">*</span></label>
                                </td>
                                <td class="value">
                                    <input type="text" class="required-entry speedy-phone-validation" name="speedy_shippinglabel[receiver_address][phone]" value="<?php echo $this->escapeHtml($shippingAddress->getTelephone()) ?>">
                                </td>
                            </tr>
                            <tr><td colspan="2" class="separator">&nbsp;</td></tr>
                        </tbody>
                    </table>

                    <?php
                        $speedyAddressRenderer = $this->getLayout()->createBlock('stenik_speedy/adminhtml_address_form_renderer');
                        echo $speedyAddressRenderer
                            ->setAddress($this->getOrder()->getShippingAddress())
                            ->setFieldIdFormat('speedy_shippinglabel:receiver_address:%s')
                            ->setFieldNameFormat('speedy_shippinglabel[receiver_address][%s]')
                            ->setCustomerAddressFieldIdFormat('speedy_shippinglabel:receiver_address:%s')
                            ->setCustomerAddressFieldNameFormat('speedy_shippinglabel[receiver_address][%s]')
                            ->toHtml();
                    ?>
                </fieldset>

                <script type="text/javascript">
                //<![CDATA[
                    Fieldset.applyCollapse('speedy_shippinglabel_address_fieldset');
                //]]></script>
                </script>
            </div>
        </div>

        <div class="box-right">
            <div class="entry-edit">
                <div class="entry-edit-head collapseable">
                    <a id="speedy_shippinglabel_sender_data_fieldset-head"
                       href="#"
                       onclick="Fieldset.toggleCollapse('speedy_shippinglabel_sender_data_fieldset'); return false;"
                       class="open"
                    >
                        <?php echo $this->__('Sender Data') ?>
                    </a>

                    <input type="hidden" id="speedy_shippinglabel_sender_data_fieldset-state" value="<?php echo Mage::helper('stenik_speedy')->getShippingLabelDefaultCollapseSender() ? 0 : 1 ?>">
                </div>
            </div>

            <div class="fieldset collapseable" id="speedy_shippinglabel_sender_data_fieldset">
                <table cellspacing="0" class="form-list">
                    <tbody>
                        <tr>
                            <td class="label">
                                <label><?php echo $this->__('Speedy ID') ?></label>
                            </td>
                            <td class="value">
                                <strong><?php echo $this->escapeHtml(Mage::helper('stenik_speedy')->getServiceUsername()) ?></strong>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">
                                <label for=""><?php echo $this->__('Phone') ?></label>
                            </td>
                            <td class="value">
                                <input type="text" name="speedy_shippinglabel[sender_data][phone]" value="<?php echo $this->escapeHtml(Mage::helper('stenik_speedy')->getSenderPhone()) ?>">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <script type="text/javascript">
            //<![CDATA[
                Fieldset.applyCollapse('speedy_shippinglabel_sender_data_fieldset');
            //]]></script>
            </script>
        </div>

        <div class="clear"></div>
        <br>
        <div class="clear"></div>

        <div class="entry-edit">
            <div class="entry-edit-head collapseable">
                <a id="speedy_shippinglabel_items-head"
                   href="#"
                   onclick="Fieldset.toggleCollapse('speedy_shippinglabel_items'); return false;"
                   class="open"
                >
                    <?php echo $this->escapeHtml($this->__('Items')) ?>
                </a>

                <input type="hidden" id="speedy_shippinglabel_items-state" value="<?php echo Mage::helper('stenik_speedy')->getShippingLabelDefaultCollapseProducts() ? 0 : 1 ?>">
            </div>

            <?php
                $order = $this->getOrder();
                $orderItemsBlock = $this->getLayout()->getBlock('order_items');

                if (!$orderItemsBlock) {
                    $orderItemsBlock = $this->getLayout()->createBlock('adminhtml/sales_items_abstract');
                    $orderItemsBlock->addItemRender('default', 'adminhtml/sales_order_view_items_renderer_default', 'stenik/speedy/sales/order/view/tab/shipping_label/items/renderer/default.phtml');
                    $orderItemsBlock->addColumnRender('qty', 'adminhtml/sales_items_column_qty', 'sales/items/column/qty.phtml');
                    $orderItemsBlock->addColumnRender('name', 'adminhtml/sales_items_column_name', 'sales/items/column/name.phtml');
                    $orderItemsBlock->addColumnRender('name', 'adminhtml/sales_items_column_name_grouped', 'sales/items/column/name.phtml', 'grouped');
                }

                $selectedProductsTotalWeight = 0;
            ?>

            <fieldset class="fieldset collapseable" id="speedy_shippinglabel_items">
                <div class="grid np fieldset" style="border: none;">
                    <div class="hor-scroll">
                        <table cellspacing="0" class="data order-tables">
                            <tr>
                                <td>
                                    <input
                                        type="checkbox"
                                        class="select-all"
                                        data-weight="0"
                                        value="0"
                                        style="width: 20px; height: 20px;"
                                        <?php if (Mage::helper('stenik_speedy')->getShippingLabelDefaultSelectProducts()): ?>
                                            checked="checked"
                                        <?php endif ?>
                                    >
                                </td>
                                <td>
                                    <?php echo $this->__('All');?>
                                </td>
                            </tr>
                            <?php foreach ($order->getAllVisibleItems() as $item): ?>
                                <tr>
                                    <td>
                                        <br>
                                        <input
                                            type="checkbox"
                                            class="speedy-item-rowtotal"
                                            data-weight="<?php echo (float) $item->getQtyOrdered() * $item->getWeight() ?>"
                                            value="<?php echo $item->getBaseRowTotal() + $item->getBaseTaxAmount() + $item->getBaseHiddenTaxAmount() + $item->getBaseWeeeTaxAppliedRowAmount() - $item->getBaseDiscountAmount() ?>"
                                            style="width: 25px; height: 25px;"
                                            <?php if (Mage::helper('stenik_speedy')->getShippingLabelDefaultSelectProducts()): ?>
                                                <?php $selectedProductsTotalWeight += $item->getQtyOrdered() * $item->getWeight() ?>
                                                checked="checked"
                                            <?php endif ?>
                                        >
                                    </td>
                                    <td>
                                        <table cellspacing="0" class="data order-tables" style="border: none">
                                            <thead>
                                                <tr class="headings">
                                                    <th><?php echo $this->__('Product') ?></th>
                                                    <th><?php echo $this->__('Image') ?></th>
                                                    <th><span class="nobr"><?php echo $this->__('Item Status') ?></span></th>
                                                    <th><span class="nobr"><?php echo $this->__('Original Price') ?></span></th>
                                                    <th><?php echo $this->__('Price') ?></th>
                                                    <th><?php echo $this->__('Weight') ?></th>
                                                    <th class="a-center"><?php echo $this->__('Qty') ?></th>
                                                    <th><?php echo $this->__('Weight Total') ?></th>
                                                    <th><?php echo $this->__('Subtotal') ?></th>
                                                    <th><span class="nobr"><?php echo $this->__('Tax Amount') ?></span></th>
                                                    <th><span class="nobr"><?php echo $this->__('Tax Percent') ?></span></th>
                                                    <th><span class="nobr"><?php echo $this->__('Discount Amount') ?></span></th>
                                                    <th class="last"><span class="nobr"><?php echo $this->__('Row Total') ?></span></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                    $itemHtml = $orderItemsBlock->getItemHtml($item);
                                                    $itemHtml = str_replace('id="', 'id="speedy_', $itemHtml);
                                                    $itemHtml = str_replace("$('id", "$('speedy_id", $itemHtml); // Truncated name support
                                                    $itemHtml = str_replace("$('dots", "$('speedy_dots", $itemHtml); // Truncated name support
                                                    echo $itemHtml;
                                                ?>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            <?php endforeach ?>
                        </table>
                    </div>
                </div>
            </fieldset>

            <script type="text/javascript">
                //<![CDATA[
                    Fieldset.applyCollapse('speedy_shippinglabel_items');
                //]]>
            </script>
        </div>
        <div class="clear"></div>
        <br>
        <div class="clear"></div>

        <div class="entry-edit">
            <div class="entry-edit-head collapseable">
                <a id="speedy_shippinglabel_picking_fieldset-head"
                   href="#"
                   onclick="return false;"
                   class="open"
                >
                    <?php echo $this->escapeHtml($this->__('Picking')) ?>
                </a>
            </div>

            <div class="fieldset">
                <table cellspacing="0" class="form-list box-left">
                    <tbody>
                        <tr>
                            <td class="label">
                                <label for="speedy_shippinglabel:picking:reference"><?php echo $this->__('Reference') ?> <span class="required">*</span></label>
                            </td>
                            <td class="value">
                                <?php
                                    $defaultReference = '';
                                    if (Mage::helper('stenik_speedy')->getShippingLabelDefaultReferenceIsOrderIid()) {
                                        $defaultReference = $this->getOrder()->getIncrementId();
                                    }
                                ?>
                                <input type="text"
                                       id="speedy_shippinglabel:picking:reference"
                                       name="speedy_shippinglabel[picking][reference]"
                                       class="required-entry"
                                       value="<?php echo $this->escapeHtml($defaultReference) ?>"
                                >
                            </td>
                        </tr>
                        <tr>
                            <td class="label">
                                <label for="speedy_shippinglabel:picking:reference2"><?php echo $this->__('Reference 2') ?></label>
                            </td>
                            <td class="value">
                                <input type="text"
                                       id="speedy_shippinglabel:picking:reference2"
                                       name="speedy_shippinglabel[picking][reference2]"
                                       value=""
                                >
                            </td>
                        </tr>
                        <tr>
                            <td class="label">
                                <label for="speedy_shippinglabel:picking:note"><?php echo $this->__('Remark') ?> <span class="required">*</span></label>
                            </td>
                            <td class="value">
                                <input type="text"
                                       id="speedy_shippinglabel:picking:note"
                                       name="speedy_shippinglabel[picking][note]"
                                       class="required-entry"
                                       value="<?php echo $this->escapeHtml($this->getOrder()->getIncrementId()) ?>"
                                >
                            </td>
                        </tr>
                        <tr>
                            <td class="label">
                                <label for="speedy_shippinglabel:picking:parcel_count"><?php echo $this->__('Parcel Count') ?> <span class="required">*</span></label>
                            </td>
                            <td class="value">
                                <input type="text"
                                       id="speedy_shippinglabel:picking:parcel_count"
                                       name="speedy_shippinglabel[picking][parcel_count]"
                                       class="required-entry"
                                       disabled="disabled"
                                       value="1"
                                >
                            </td>
                        </tr>
                        <tr>
                            <td class="label">
                                <label for="speedy_shippinglabel:picking:weight"><?php echo $this->__('Weight') ?> <span class="required">*</span></label>
                            </td>
                            <td class="value">
                                <input type="text"
                                       id="speedy_shippinglabel:picking:weight"
                                       name="speedy_shippinglabel[picking][weight]"
                                       class="required-entry"
                                       value="<?php echo ((float) $selectedProductsTotalWeight) ? : Mage::helper('stenik_speedy')->getShippingLabelDefaultWeightValueInPlaceOfZero() ?>"
                                > <small><?php echo $this->__('kg.');?></small>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">
                                <label for="speedy_shippinglabel:picking:items_total"><?php echo $this->__('Items Total Amount') ?> <span class="required">*</span></label>
                            </td>
                            <td class="value">
                                <input type="text"
                                       id="speedy_shippinglabel:picking:items_total"
                                       name="speedy_shippinglabel[picking][items_total]"
                                       class="required-entry"
                                       value=""
                                > <small><?php echo $order->getBaseCurrencyCode() ?></small>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">
                                <label for="speedy_shippinglabel:picking:shipping_amount"><?php echo $this->__('Shipping Amount') ?> <span class="required">*</span></label>
                            </td>
                            <td class="value">
                                <input type="text"
                                       id="speedy_shippinglabel:picking:shipping_amount"
                                       name="speedy_shippinglabel[picking][shipping_amount]"
                                       class="required-entry"
                                       value="<?php echo $order->getBaseShippingAmount() ?>"
                                > <small><?php echo $order->getBaseCurrencyCode() ?></small>
                            </td>
                        </tr>

                        <?php
                            $isCod = false;
                            if ($orderPayment = $order->getPayment()) {
                                $isCod = (bool) ($orderPayment->getMethod() == 'cashondelivery');
                            }

                            $defaultCustomerPaymentType = Mage::helper('stenik_speedy')->getShippingLabelDefaultCustomerPaymentType();

                            if ($isCod) {
                                $defaultCustomerPaymentType = Mage::helper('stenik_speedy')->getShippingLabelDefaultCustomerPaymentTypeWhenOrderIsCod();
                            }

                            $customerPaymentTypeSelect = $this->getLayout()->createBlock('adminhtml/html_select')
                                ->setOptions(Mage::helper('stenik_speedy')->getCustomerPaymentTypeOptions())
                                ->setId('speedy_shippinglabel:picking:customer_payment_type')
                                ->setName('speedy_shippinglabel[picking][customer_payment_type]')
                                ->setValue($defaultCustomerPaymentType);
                        ?>

                        <tr>
                            <td class="label">
                                <label for="speedy_shippinglabel:picking:customer_payment_type"><?php echo $this->__('Customer Payment') ?> <span class="required">*</span></label>
                            </td>
                            <td class="value">
                                <?php echo $customerPaymentTypeSelect->toHtml() ?>
                            </td>
                        </tr>

                        <?php $selectedSenderClient = null; ?>
                        <tr>
                            <td class="label">
                                <label for=""><?php echo $this->__('Sender');?> <span class="required">*</span></label>
                            </td>
                            <td class="value">
                                <select
                                    id="speedy_shippinglabel:picking:sender_client_id"
                                    name="speedy_shippinglabel[picking][sender_client_id]"
                                    class="required-entry"
                                >
                                    <option value=""></option>
                                    <?php $clients = $this->getAllSenderClients(); ?>
                                    <?php foreach ($clients as $client): ?>
                                        <option
                                            value="<?php echo $client->getId() ?>"
                                            data-city-id="<?php echo $client->getCityId() ?>"
                                            <?php if (count($clients) == 1): $selectedSenderClient = $client; ?>selected="selected"<?php endif ?>
                                        >
                                            <?php echo $this->escapeHtml($client->getObjectName()) ?>,
                                            <?php echo $this->escapeHtml($client->getCityName()) ?>,
                                            <?php echo $this->escapeHtml($client->getAddress()) ?>
                                        </option>
                                    <?php endforeach ?>
                                </select>
                            </td>
                        </tr>

                        <?php $selectedServiceType = null; ?>
                        <tr>
                            <td class="label">
                                <label for=""><?php echo $this->__('Service Type') ?> <span class="required">*</span></label>
                            </td>
                            <td class="value">
                                <select
                                       id="speedy_shippinglabel:picking:service_type_id"
                                       name="speedy_shippinglabel[picking][service_type_id]"
                                       class="required-entry"
                                >
                                    <?php
                                        $serviceTypesOptionsHtml = '<option value=""></option>';
                                        foreach (Mage::helper('stenik_speedy')->getAllowedServiceTypes() as $serviceType) {
                                            $disable = false;
                                            $select  = false;

                                            // Do not disable and select services if sender client is not autoselected
                                            if ($selectedSenderClient) {
                                                $receiverCityId  = $speedyAddressRenderer->getFieldValue('city_id');

                                                $isDeliveryLocal = (bool) ($receiverCityId == $selectedSenderClient->getCityId());
                                                $isServiceLocal  = (bool) $serviceType->getIsLocal();

                                                if ($isDeliveryLocal !== $isServiceLocal) {
                                                    $disable = true;
                                                }

                                                if (!$disable && !$selectedServiceType) {
                                                    $select = true;
                                                    $selectedServiceType = $serviceType;
                                                }
                                            }

                                            $serviceTypesOptionsHtml .= sprintf('<option value="%s" data-is-local="%s" %s %s>%s</option>"',
                                                $serviceType->getServiceTypeId(),
                                                $serviceType->getIsLocal() ? 1 : 0,
                                                $disable ? 'disabled="disabled"' : null,
                                                $select ? 'selected="selected"' : null,
                                                $this->escapeHtml($serviceType->getName())
                                            );
                                        }

                                        echo $serviceTypesOptionsHtml;
                                    ?>
                                </select>
                            </td>
                        </tr>

                        <tr>
                            <td class="label">
                                <label for="speedy_shippinglabel:picking:taking_date"><?php echo $this->__('Taking Date') ?> <span class="required">*</span></label>
                            </td>
                            <td class="value">
                                <select
                                    class="required-entry"
                                    id="speedy_shippinglabel:picking:taking_date"
                                    name="speedy_shippinglabel[picking][taking_date]"
                                    disabled="disabled"
                                ></select>

                                <small style="display:none" id="speedy_shippinglabel:picking:taking_date-loader">
                                    <img src="<?php echo $this->getSkinUrl('images/rule-ajax-loader.gif') ?>" alt="loader">
                                </small>

                                <span class="error" id="speedy_shippinglabel:picking:taking_date-error"></span>
                            </td>
                        </tr>

                        <?php
                            $payerSelect = $this->getLayout()->createBlock('adminhtml/html_select')
                                ->setOptions(Mage::helper('stenik_speedy')->getPayerOptions())
                                ->setId('speedy_shippinglabel:picking:payer')
                                ->setName('speedy_shippinglabel[picking][payer]')
                                ->setValue(Mage::helper('stenik_speedy')->getShippingLabelDefaultPayer());
                        ?>

                        <tr>
                            <td class="label">
                                <label for="speedy_shippinglabel:picking:payer"><?php echo $this->__('Payer') ?></label>
                            </td>
                            <td class="value">
                                <?php echo $payerSelect->toHtml() ?>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div class="entry-edit box-right">
                    <div class="entry-edit-head collapseable">
                        <a id="speedy_shippinglabel_picking_default_fieldset-head"
                           href="#"
                           onclick="Fieldset.toggleCollapse('speedy_shippinglabel_picking_default_fieldset'); return false;"
                           class="open"
                        >
                            <?php echo $this->__('More options');?>
                        </a>
                    </div>

                    <div class="fieldset collapseable" id="speedy_shippinglabel_picking_default_fieldset">
                        <table cellspacing="0" class="form-list">
                            <?php
                                $backDocumentsRequestSelect = $this->getLayout()->createBlock('adminhtml/html_select')
                                    ->setOptions(array(0 => $this->__('No'), 1 => $this->__('Yes')))
                                    ->setId('speedy_shippinglabel:picking:back_documents_request')
                                    ->setName('speedy_shippinglabel[picking][back_documents_request]')
                                    ->setValue(0);

                                $backBackReceiptRequestSelect = $this->getLayout()->createBlock('adminhtml/html_select')
                                    ->setOptions(array(0 => $this->__('No'), 1 => $this->__('Yes')))
                                    ->setId('speedy_shippinglabel:picking:back_receipt_request')
                                    ->setName('speedy_shippinglabel[picking][back_receipt_request]')
                                    ->setValue(0);

                                $containsDocumentsSelect = $this->getLayout()->createBlock('adminhtml/html_select')
                                    ->setOptions(array(0 => $this->__('No'), 1 => $this->__('Yes')))
                                    ->setId('speedy_shippinglabel:picking:contains_documents')
                                    ->setName('speedy_shippinglabel[picking][contains_documents]')
                                    ->setValue(0);

                                $insuranceSelect = $this->getLayout()->createBlock('adminhtml/html_select')
                                    ->setOptions(array(0 => $this->__('No'), 1 => $this->__('Yes')))
                                    ->setId('speedy_shippinglabel:picking:insurance')
                                    ->setName('speedy_shippinglabel[picking][insurance]')
                                    ->setValue(Mage::helper('stenik_speedy')->getShippingLabelInsurance());

                                $fragileSelect = $this->getLayout()->createBlock('adminhtml/html_select')
                                    ->setOptions(array(0 => $this->__('No'), 1 => $this->__('Yes')))
                                    ->setId('speedy_shippinglabel:picking:fragile')
                                    ->setName('speedy_shippinglabel[picking][fragile]')
                                    ->setValue(Mage::helper('stenik_speedy')->getShippingLabelDefaultFragile());

                                $canOpenBeforePayment = !$isCod ? false : Mage::helper('stenik_speedy')->getShippingLabelDefaultCanOpenBeforePayment();
                                $canTestBeforePayment = !$isCod ? false : Mage::helper('stenik_speedy')->getShippingLabelDefaultCanTestBeforePayment();

                                $canOpenBeforePaymentSelect = $this->getLayout()->createBlock('adminhtml/html_select')
                                    ->setOptions(array(0 => $this->__('No'), 1 => $this->__('Yes')))
                                    ->setId('speedy_shippinglabel:picking:can_open_before_payment')
                                    ->setName('speedy_shippinglabel[picking][can_open_before_payment]')
                                    ->setValue($canOpenBeforePayment ? 1 : 0);

                                $canTestBeforePaymentSelect = $this->getLayout()->createBlock('adminhtml/html_select')
                                    ->setOptions(array(0 => $this->__('No'), 1 => $this->__('Yes')))
                                    ->setId('speedy_shippinglabel:picking:can_test_before_payment')
                                    ->setName('speedy_shippinglabel[picking][can_test_before_payment]')
                                    ->setValue($canTestBeforePayment ? 1 : 0);

                                $payerOnReturnBeforePaymentSelect = $this->getLayout()->createBlock('adminhtml/html_select')
                                    ->setOptions(Mage::helper('stenik_speedy')->getPayerOptions())
                                    ->setId('speedy_shippinglabel:picking:payer_on_return_before_payment')
                                    ->setName('speedy_shippinglabel[picking][payer_on_return_before_payment]')
                                    ->setClass('required-entry')
                                    ->setValue(Mage::helper('stenik_speedy')->getShippingLabelDefaultPayerOnReturnBeforePayment());

                                $showReturnOptionsByDefault = $canOpenBeforePayment || $canTestBeforePayment;
                            ?>
                            <tbody>
                                <tr>
                                    <td class="label">
                                        <label for="speedy_shippinglabel:picking:contents"><?php echo $this->__('Contents') ?> <span class="required">*</span></label>
                                    </td>
                                    <td class="value">
                                        <input type="text"
                                               id="speedy_shippinglabel:picking:contents"
                                               name="speedy_shippinglabel[picking][contents]"
                                               class="required-entry"
                                               value="<?php echo Mage::helper('stenik_speedy')->getShippingLabelContents() ?>"
                                        >
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label">
                                        <label for=""><?php echo $this->__('Fixed Time') ?></label>
                                    </td>
                                    <td class="value">
                                        <input type="text"
                                               name="speedy_shippinglabel[picking][fixed_time_delivery]"
                                               value="<?php echo $this->escapeHtml($speedyAddress->getFixedTimeDelivery()) ?>"
                                        > <small>(HH:mm)</small>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="label">
                                        <label for="speedy_shippinglabel:picking:back_documents_request"><?php echo $this->__('Back Documents Request') ?></label>
                                    </td>
                                    <td class="value">
                                        <?php echo $backDocumentsRequestSelect->toHtml() ?>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="label">
                                        <label for="speedy_shippinglabel:picking:back_receipt_request"><?php echo $this->__('Back Receipt Request') ?></label>
                                    </td>
                                    <td class="value">
                                        <?php echo $backBackReceiptRequestSelect->toHtml() ?>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="label">
                                        <label for="speedy_shippinglabel:picking:contains_documents"><?php echo $this->__('Contains Documents') ?></label>
                                    </td>
                                    <td class="value">
                                        <?php echo $containsDocumentsSelect->toHtml() ?>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="label">
                                        <label for="speedy_shippinglabel:picking:packing"><?php echo $this->__('Packing') ?></label>
                                    </td>
                                    <td class="value">
                                        <input type="text"
                                               id="speedy_shippinglabel:picking:packing"
                                               name="speedy_shippinglabel[picking][packing]"
                                               value=""
                                        >
                                    </td>
                                </tr>

                                <tr>
                                    <td class="label">
                                        <label for="speedy_shippinglabel:picking:insurance"><?php echo $this->__('Insurance') ?></label>
                                    </td>
                                    <td class="value">
                                        <?php echo $insuranceSelect->toHtml() ?>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="label">
                                        <label for="speedy_shippinglabel:picking:insurance_amount"><?php echo $this->__('Insurance Amount') ?></label>
                                    </td>
                                    <td class="value">
                                        <input type="text"
                                               id="speedy_shippinglabel:picking:insurance_amount"
                                               name="speedy_shippinglabel[picking][insurance_amount]"
                                               value=""
                                        > <small><?php echo $order->getBaseCurrencyCode() ?></small>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="label">
                                        <label for="speedy_shippinglabel:picking:fragile"><?php echo $this->__('Fragile') ?></label>
                                    </td>
                                    <td class="value">
                                        <?php echo $fragileSelect->toHtml() ?>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="label">
                                        <label for="speedy_shippinglabel:picking:can_open_before_payment"><?php echo $this->__('Can Open Before Payment') ?></label>
                                    </td>
                                    <td class="value">
                                        <?php echo $canOpenBeforePaymentSelect->toHtml() ?>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="label">
                                        <label for="speedy_shippinglabel:picking:can_test_before_payment"><?php echo $this->__('Can Test Before Payment') ?></label>
                                    </td>
                                    <td class="value">
                                        <?php echo $canTestBeforePaymentSelect->toHtml() ?>
                                    </td>
                                </tr>

                                <tr class="return_options_before_payment" <?php if (!$showReturnOptionsByDefault): ?>style="display: none"<?php endif ?>>
                                    <td class="label">
                                        <label for="speedy_shippinglabel:picking:service_type_id_on_return_before_payment"><?php echo $this->__('Service On Return Before Payment') ?> <span class="required">*</span></label>
                                    </td>
                                    <td class="value">
                                        <select
                                               id="speedy_shippinglabel:picking:service_type_id_on_return_before_payment"
                                               name="speedy_shippinglabel[picking][service_type_id_on_return_before_payment]"
                                               class="required-entry"
                                        >
                                            <?php echo $serviceTypesOptionsHtml ?>
                                        </select>
                                    </td>
                                </tr>

                                <tr class="return_options_before_payment" <?php if (!$showReturnOptionsByDefault): ?>style="display: none"<?php endif ?>>
                                    <td class="label">
                                        <label for="speedy_shippinglabel:picking:payer_on_return_before_payment"><?php echo $this->__('Payer On Return Before Payment') ?> <span class="required">*</span></label>
                                    </td>
                                    <td class="value">
                                        <?php echo $payerOnReturnBeforePaymentSelect->toHtml() ?>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <script type="text/javascript">
                    //<![CDATA[
                        Fieldset.applyCollapse('speedy_shippinglabel_picking_default_fieldset');
                    //]]></script>
                    </script>
                </div>
                <div class="clear"></div>

                <div class="entry-edit">
                    &nbsp;<br>
                    <div class="parcel-wrapper" id="stenik_speedy_shippinglabel_create_form_parcel_wrapper">
                        <div class="parcels"></div>
                        <div class="clear"></div>
                        <a href="javascript:;" class="addParcel">+ <?php echo $this->__('Add parcel');?></a>
                    </div>
                </div>
            </div>
        </div>
        <div class="clear"></div>

        <button><?php echo $this->__('Create');?></button>
    </form>

    <script>
        var stenikSpeedyShippinglabelCreateForm = new varienForm('stenik_speedy_shippinglabel_create_form');

        $$('#stenik_speedy_shippinglabel_create_form .entry-edit').forEach(function(section) {
            var statusBar = section;
            section.select('.entry-edit-head a').each(function(el) {
                statusBar = el;
                throw $break;
            });

            section.select('input,textarea,select').each(function(el) {
                el.statusBar = statusBar;
            });
        });

        (function() {
            var $receiverCityIdInput                = $('speedy_shippinglabel:receiver_address:city_id');

            var $rowTotalCheckboxes                 = $$('#stenik_speedy_shippinglabel_create_form .speedy-item-rowtotal');
            var $selectAllRowTotalsInputs           = $$('#stenik_speedy_shippinglabel_create_form .select-all');

            var $senderClientSelect                 = $('speedy_shippinglabel:picking:sender_client_id');
            var $serviceTypeSelect                  = $('speedy_shippinglabel:picking:service_type_id');
            var $takingDateSelect                   = $('speedy_shippinglabel:picking:taking_date');
            var $takingDateLoader                   = $('speedy_shippinglabel:picking:taking_date-loader');
            var $takingDateError                    = $('speedy_shippinglabel:picking:taking_date-error');

            var $serviceTypeOnReturnBeforePaymentSelect = $('speedy_shippinglabel:picking:service_type_id_on_return_before_payment');
            var $canOpenBeforePaymentSelect             = $('speedy_shippinglabel:picking:can_open_before_payment');
            var $canTestBeforePaymentSelect             = $('speedy_shippinglabel:picking:can_test_before_payment');
            var $returnOptionsBeforePaymentWrappers     = $$('#stenik_speedy_shippinglabel_create_form .return_options_before_payment');

            var refreshSelectAllRowTotalsInput = function() {
                var allSelected = true;
                $rowTotalCheckboxes.forEach(function(el) {
                    if (!el.checked) {
                        allSelected = false;
                    }
                });

                $selectAllRowTotalsInputs.forEach(function(el) {
                    el.checked = allSelected;
                });
            };

            var recalcRowTotals = function() {
                var total       = 0;
                var weightTotal = 0;
                $rowTotalCheckboxes.forEach(function(el) {
                    if (el.checked) {
                        total += parseFloat(el.value);
                        weightTotal += parseFloat(el.getAttribute('data-weight'));
                    }
                });

                $('speedy_shippinglabel:picking:weight').value           = weightTotal ? Math.round(weightTotal*10000)/10000 : '<?php echo Mage::helper('stenik_speedy')->getShippingLabelDefaultWeightValueInPlaceOfZero() ?>';
                $('speedy_shippinglabel:picking:items_total').value      = total ? Math.round(total*10000)/10000 : 0;
                $('speedy_shippinglabel:picking:insurance_amount').value = total ? Math.round(total*10000)/10000 : 0;

                refreshSelectAllRowTotalsInput();
            };

            $rowTotalCheckboxes.forEach(function(el) {
                Event.observe(el, 'change', recalcRowTotals);
            });

            $selectAllRowTotalsInputs.forEach(function(el) {
                Event.observe(el, 'change', function() {
                    $rowTotalCheckboxes.forEach(function(rowTotalEl) {
                        rowTotalEl.checked = el.checked;
                    });
                    recalcRowTotals();
                });
            });

            var reloadAllowedDaysForTaking = function() {
                $takingDateSelect.descendants().each(Element.remove);
                $takingDateSelect.disable();
                $takingDateError.update();

                if (!$senderClientSelect.value || !$serviceTypeSelect.value) {
                    return;
                }

                $takingDateLoader.show();
                new Ajax.Request(<?php echo json_encode($this->getUrl('adminhtml/stenik_speedy/getAllowedDaysForTaking')) ?>, {
                    method: 'post',
                    parameters: {
                        sender_client_id: $senderClientSelect.value,
                        service_type_id:  $serviceTypeSelect.value
                    },
                    onSuccess: function(transport) {
                        var response = transport.responseText || '{}';
                        try {
                            response = JSON.parse(response);
                            if (typeof response.datetimes !== 'undefined') {
                                response.datetimes[''] = '';

                                $takingDateSelect.update($H(response.datetimes).map(function(o) {
                                    return '<option value="' + o.key + '">' + o.value + '</option>';
                                }).join(''));

                                $takingDateSelect.enable();
                            }
                            $takingDateLoader.hide();

                            if (typeof response.error !== 'undefined') {
                                $takingDateError.update(response.error);
                            }
                        } catch(e) {
                            $takingDateError.update(Translator.translate('Something went wrong.'));
                        }
                    },
                    onFailure: function() {
                        $takingDateError.update(Translator.translate('Something went wrong.'));
                        $takingDateLoader.hide();
                    }
                });
            };

            var processServiceTypes = function() {

                var senderClientCityId = null;
                var receiverCityId = $receiverCityIdInput.value;

                $senderClientSelect.select('option:selected').forEach(function(option) {
                    senderClientCityId = option.readAttribute('data-city-id');
                });

                var processOption = function(option) {
                    if (senderClientCityId == receiverCityId) {
                        option.disabled = option.readAttribute('data-is-local') == 0;
                    } else {
                        option.disabled = option.readAttribute('data-is-local') != 0;
                    }

                    if (option.disabled && option.selected) {
                        option.selected = false;
                    }
                };

                $serviceTypeSelect.select('option').forEach(processOption);
                $serviceTypeOnReturnBeforePaymentSelect.select('option').forEach(processOption);

                reloadAllowedDaysForTaking();
            }

            var refreshReturnOptionsBeforePaymentVisibility = function() {
                if ($canOpenBeforePaymentSelect.value == 1 || $canTestBeforePaymentSelect.value == 1) {
                    $returnOptionsBeforePaymentWrappers.each(Element.show);
                } else {
                    $returnOptionsBeforePaymentWrappers.each(Element.hide);
                }
            }

            $senderClientSelect.observe('change', processServiceTypes);
            $receiverCityIdInput.observe('change', processServiceTypes);
            $senderClientSelect.observe('change', reloadAllowedDaysForTaking);
            $serviceTypeSelect.observe('change', reloadAllowedDaysForTaking);

            $canOpenBeforePaymentSelect.observe('change', refreshReturnOptionsBeforePaymentVisibility);
            $canTestBeforePaymentSelect.observe('change', refreshReturnOptionsBeforePaymentVisibility);

            $serviceTypeSelect.observe('change', function() {
                $serviceTypeOnReturnBeforePaymentSelect.value = $serviceTypeSelect.value;
                $serviceTypeOnReturnBeforePaymentSelect.select('option').forEach(function(option) {
                    option.selected = (option.value == $serviceTypeSelect.value);
                });
            });

            recalcRowTotals();
            processServiceTypes();
            refreshReturnOptionsBeforePaymentVisibility();


            var $parcelWrapper = $('stenik_speedy_shippinglabel_create_form_parcel_wrapper');
            var $parcelCountInput = $('speedy_shippinglabel:picking:parcel_count');

            var refreshParcelCountInput = function() {
                $parcelCountInput.value = Math.max(1, $parcelWrapper.select('.parcels > .parcel').length);
            };

            var parcelSequenceCounter = 0;

            var parcelSeq = 0;
            var addParcel = function() {
                var parcelHtml =
                '<div class="entry-edit parcel">\
                    <div class="entry-edit-head">\
                        <h4>' + <?php echo json_encode($this->__('Parcel')) ?> + '\
                            &nbsp; / &nbsp; <a href="javascript:;" class="delete">' + <?php echo json_encode($this->__('Delete')) ?> + '</a> \
                        </h4>\
                    </div>\
                    <div class="fieldset">\
                        <table cellspacing="0" class="form-list">\
                            <tr>\
                                <td class="label">\
                                    <label for="speedy_shippinglabel:parcels:parcel{{parcelSeq}}_weight">' + <?php echo json_encode($this->__('Weight')) ?> + '</label>\
                                </td>\
                                <td class="value">\
                                    <input type="text" id="speedy_shippinglabel:parcels:parcel{{parcelSeq}}_weight" name="speedy_shippinglabel[parcels][{{parcelSeq}}][weight]" value="">\
                                    ' + <?php echo json_encode($this->__('kg.')) ?> + '\
                                </td>\
                            </tr>\
                            <tr>\
                                <td class="label">\
                                    <label for="speedy_shippinglabel:parcels:parcel{{parcelSeq}}_weight">' + <?php echo json_encode($this->__('Size')) ?> + '</label>\
                                </td>\
                                <td class="value">\
                                    <label style="width: 70px; display: inline-block;" for="speedy_shippinglabel:parcels:parcel{{parcelSeq}}_width">' + <?php echo json_encode($this->__('Width')) ?> + '</label>\
                                    <input type="text" id="speedy_shippinglabel:parcels:parcel{{parcelSeq}}_width" name="speedy_shippinglabel[parcels][{{parcelSeq}}][width]" value="">\
                                    ' + <?php echo json_encode($this->__('cm.')) ?> + '\
                                    <br>\
                                    <label style="width: 70px; display: inline-block;" for="speedy_shippinglabel:parcels:parcel{{parcelSeq}}_height">' + <?php echo json_encode($this->__('Height')) ?> + '</label>\
                                    <input type="text" id="speedy_shippinglabel:parcels:parcel{{parcelSeq}}_height" name="speedy_shippinglabel[parcels][{{parcelSeq}}][height]" value="">\
                                    ' + <?php echo json_encode($this->__('cm.')) ?> + '\
                                    <br>\
                                    <label style="width: 70px; display: inline-block;" for="speedy_shippinglabel:parcels:parcel{{parcelSeq}}_depth">' + <?php echo json_encode($this->__('Depth')) ?> + '</label>\
                                    <input type="text" id="speedy_shippinglabel:parcels:parcel{{parcelSeq}}_depth" name="speedy_shippinglabel[parcels][{{parcelSeq}}][depth]" value="">\
                                    ' + <?php echo json_encode($this->__('cm.')) ?> + '\
                                    <br>\
                                </td>\
                            </tr>\
                        </table>\
                    </div>\
                    <div class="clear"></div>\
                </div>';

                parcelSeq++;
                parcelHtml = parcelHtml.split('{{parcelSeq}}').join(parcelSeq);
                parcelHtml = parcelHtml.trim();

                var $parcel = document.createElement('div');
                $parcel.innerHTML = parcelHtml.trim();
                $parcel = $parcel.firstChild;

                $parcelWrapper.select('.parcels')[0].insert($parcel);

                $parcel.select('.delete').each(function(el) {
                    Event.observe(el, 'click', function() {
                        $parcel.remove();
                        refreshParcelCountInput();
                    });
                });

                refreshParcelCountInput();
            };

            var $parcels = $parcelWrapper.select('.addParcel');
            if ($parcels && $parcels.length) {
                $parcels.each(function(el) { Event.observe(el, 'click', addParcel) });
            }

        })();

        Validation.addAllThese([
            [
                'speedy-phone-validation',
                <?php echo json_encode($this->__('Phone must start with "0" or "+" followed by only digits and spaces and be no longer than 20 characters.')); ?>,
                function(v) {
                    return /^[0\+][0-9\+\ ]{0,19}$/.test(v);
                }
            ]
        ]);
    </script>
</div>