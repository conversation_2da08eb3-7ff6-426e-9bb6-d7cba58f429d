<?php
/**
 * @package  Stenik_Zeron
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Zeron_Process_Product_ModelShortNameMap extends Mage_Core_Model_Abstract
{
    /**
     * @param string $makeCode
     * @param string $modelName
     * @return string|bool
     */
    public function getModelShortName($makeCode, $modelName)
    {
        $makeCode = trim($makeCode);
        $modelName = trim($modelName);

        foreach (self::$_map as $row) {
            if ($makeCode == trim($row[0]) && $modelName == trim($row[1])) {
                return $row[2];
            }
        }

        return false;
    }

    /**
     * @var array
     */
    protected static $_map = array(
        // Make Code, Model Name or the text in parentheses, Short Model Name
        array('ALFA', '116', 'GTV (116)'),
        array('ALFA', '145 (930)', '145 (930)'),
        array('ALFA', '146 (930)', '146 (930)'),
        array('ALFA', '162B', '75 (162B)'),
        array('ALFA', '164', '164 (164)'),
        array('ALFA', '167', '155 (167)'),
        array('ALFA', '907B', '33 (907B)'),
        array('ALFA', '916C_', 'GTV (916C_)'),
        array('ALFA', '930', '145/146 (930)'),
        array('ALFA', '932', '156 (932)'),
        array('ALFA', '936', '166 (936)'),
        array('ALFA', '937', '147 (937)'),
        array('AUDI', '100 Avant (4A, C4)', '100 (4A, C4)'),
        array('AUDI', '100 седан (4A, C4)', '100 (4A, C4)'),
        array('AUDI', '4A, C4', '100 (4A, C4)'),
        array('AUDI', '4B2, C5', 'A6 (4B2/4B5, C5)'),
        array('AUDI', '4B5, C5', 'A6 (4B2/4B5, C5)'),
        array('AUDI', '4D2, 4D8', 'A8 (4D2, 4D8)'),
        array('AUDI', '4E_', 'A8 (4E_)'),
        array('AUDI', '4F2, C6', 'A6 (4F2/4F5, C6)'),
        array('AUDI', '4F5, C6', 'A6 (4F2/4F5, C6)'),
        array('AUDI', '4H_', 'A8 (4H_)'),
        array('AUDI', '81, 85, B2', '80 (81, 85, B2)'),
        array('AUDI', '89, 89Q, 8A, B3', '80 (89, 89Q, 8A, B3)'),
        array('AUDI', '8C, B4', '80 (8C, B4)'),
        array('AUDI', '8D2, B5', 'A4 (8D2/8D5, B5)'),
        array('AUDI', '8D5, B5', 'A4 (8D2/8D5, B5)'),
        array('AUDI', '8E2, B6', 'A4 (8E2/8E5, B6)'),
        array('AUDI', '8E5, B6', 'A4 (8E2/8E5, B6)'),
        array('AUDI', '8G7, B4', 'Audi 80 Cabrio (8G7, B4)'),
        array('AUDI', '8L1', 'A3 (8L1)'),
        array('AUDI', '8N3', 'TT (8N3)'),
        array('AUDI', '8PA', 'A3 (8PA)'),
        array('AUDI', 'A4 Avant (8ED, B7)', 'A4 (8ED, B7)'),
        array('AUDI', 'A6 Avant (4A, C4)', 'A6 (4A, C4)'),
        array('AUDI', 'A6 седан (4A, C4)', 'A6 (4A, C4)'),
        array('BMW', 'E30', '3 Series (E30)'),
        array('BMW', 'E34', '5 Series (E34)'),
        array('BMW', 'E36', '3 Series (E36)'),
        array('BMW', 'E38', '7 Series (E38)'),
        array('BMW', 'E39', '5 Series (E39)'),
        array('BMW', 'E46', '3 Series (E46)'),
        array('BMW', 'E60', '5 Series (E60/E61)'),
        array('BMW', 'E61', '5 Series (E60/E61)'),
        array('BMW', 'E65, E66, E67', '7 Series (E65, E66, E67)'),
        array('BMW', 'E70', 'X5 Series (E70)'),
        array('BMW', 'E83', 'X3 Series (E83)'),
        array('BMW', 'E90', '3 Series (E90/E91/E92)'),
        array('BMW', 'E92', '3 Series (E90/E91/E92)'),
        array('BMW', 'F11', '5 Touring (F11)'),
        array('BMW', 'F30, F35, F80', '3 Series (F30, F35, F80)'),
        array('BMW', 'X5 (E53)', 'X5 (E53)'),
        array('CADIL', 'BLS', 'BLS'),
        array('CHEVR', 'ALERO седан', 'ALERO'),
        array('CHEVR', 'AVEO хетчбек (T300)', 'Aveo (T300)'),
        array('CHEVR', 'AVEO седан (T300)', 'Aveo (T300)'),
        array('CHEVR', 'J200', 'LACETTI (J200)'),
        array('CHEVR', 'KALOS седан', 'KALOS'),
        array('CHEVR', 'M200, M250', 'MATIZ (M200,M250)'),
        array('CHEVR', 'SPARK', 'SPARK'),
        array('CHEVR', 'T250, T255 ', 'AVEO (T250,T255)'),
        array('CHRYS', 'ES', 'Voyager II (ES)'),
        array('CHRYS', 'GS', 'GRAND VOYAGER III (GS)'),
        array('CHRYS', 'JA', 'Stratus (JA)'),
        array('CHRYS', 'JR', 'Sebring (JR)'),
        array('CHRYS', 'LR', '300 M (LR)'),
        array('CHRYS', 'NEON II седан', 'Neon II'),
        array('CHRYS', 'PL', 'Neon (PL)'),
        array('CHRYS', 'PT_', 'PT Cruiser (PT_)'),
        array('CHRYS', 'RG, RS', 'Voyager IV (RG, RS)'),
        array('CHRYS', 'SARATOGA седан', 'Saratoga'),
        array('CITRO', '230L', 'Jumper (230L)'),
        array('CITRO', '244', 'JUMPER кутия (244)'),
        array('CITRO', '3', 'Saxo (1996 - 2004)'),
        array('CITRO', 'B9', 'Berlingo (B9)'),
        array('CITRO', 'BS_, BT_, BY_, BZ_', 'Jumpy (BS_, BT_, BY_, BZ_)'),
        array('CITRO', 'C3 II', 'C3 II'),
        array('CITRO', 'C3 Pluriel (HB_)', 'C3 Pluriel (HB_)'),
        array('CITRO', 'C4 Grand Picasso I (UA_)', 'C4 Grand Picasso (UA_)'),
        array('CITRO', 'C8 (EA_, EB_)', 'C8 (EA_, EB_)'),
        array('CITRO', 'CITROEN SAXO 1.5d 54hp', 'Saxo (1996 - 2004)'),
        array('CITRO', 'DC_ ', 'C5 I (DC_, DE_)'),
        array('CITRO', 'DE_', 'C5 I (DC_, DE_)'),
        array('CITRO', 'Evasion', 'Evasion'),
        array('CITRO', 'EVASION (22, U6)', 'Evasion (22, U6)'),
        array('CITRO', 'FC_', 'C3 I (FC_)'),
        array('CITRO', 'JM_', 'C2 (JM_)'),
        array('CITRO', 'JUMPER автобус (244, Z_)', 'JUMPER автобус (244, Z_)'),
        array('CITRO', 'JUMPER (бордова) платформа/ шаси', 'JUMPER (бордова) платформа/ шаси'),
        array('CITRO', 'JUMPER (бордова) платформа/ шаси (230)', 'JUMPER (бордова) платформа/ шаси (230)'),
        array('CITRO', 'JUMPER (бордова) платформа/ шаси (244)', 'JUMPER (бордова) платформа/ шаси (244)'),
        array('CITRO', 'LA_', 'C4 I (LA_, LC_)'),
        array('CITRO', 'LC_', 'C4 I (LA_, LC_)'),
        array('CITRO', 'MF', 'Berlingo (MF / M_)'),
        array('CITRO', 'M_', 'Berlingo (MF / M_)'),
        array('CITRO', 'N0', 'XSARA (N0, N1, N2)'),
        array('CITRO', 'N68', 'XSARA PICASSO (N68)'),
        array('CITRO', 'OLTCIT', 'Oltcit'),
        array('CITRO', 'PM_, PN_', 'C1 (PM_,PN_)'),
        array('CITRO', 'RC_', 'C5 II (RC_)'),
        array('CITRO', 'SAXO ', 'Saxo (1996 - 2004)'),
        array('CITRO', 'SAXO 1.1i 60hp ', 'Saxo (2000-2003)'),
        array('CITRO', 'XANTIA (X1)', 'XANTIA (X1)'),
        array('CITRO', 'XANTIA (X2)', 'XANTIA (X2)'),
        array('CITRO', 'XANTIA Estate (X1)', 'XANTIA (X1)'),
        array('CITRO', 'XANTIA Estate (X2)', 'XANTIA (X2)'),
        array('CITRO', 'XB-_', 'BX (XB-_)'),
        array('CITRO', 'XM (Y4)', 'XM (Y4)'),
        array('CITRO', 'XM Estate (Y4)', 'XM (Y4)'),
        array('CITRO', 'XSARA (N1)', 'XSARA (N0, N1, N2)'),
        array('CITRO', 'XSARA Estate (N2)', 'XSARA (N0, N1, N2)'),
        array('CITRO', 'XSARA PICASSO MPV (N68)', 'XSARA PICASSO (N68)'),
        array('CITRO', 'XSARA купе (N0)', 'XSARA (N0, N1, N2)'),
        array('CITRO', 'ZA-_', 'AX (ZA-_)'),
        array('CITRO', 'ZX (N2)', 'ZX (N2)'),
        array('CITRO', 'ZX Estate (N2)', 'ZX (N2)'),
        array('DACIA', 'DUSTER', 'DUSTER'),
        array('DACIA', 'KS_', 'Logan (KS_)'),
        array('DACIA', 'LS_', 'Logan (LS_)'),
        array('DACIA', 'SANDERO', 'SANDERO'),
        array('DACIA', 'SOLENZA', 'SOLENZA'),
        array('DAEWOO', 'FJ', 'Musso (FJ)'),
        array('DAEWOO', 'KLAJ', 'Nubira (KLAJ)'),
        array('DAEWOO', 'KALOS (KLAS)', 'Kalos (KLAS)'),
        array('DAEWOO', 'KALOS седан (KLAS)', 'Kalos (KLAS)'),
        array('DAEWOO', 'KLAT', 'Lanos/Sens (KLAT)'),
        array('DAEWOO', 'KLAU, U100', 'Tacuma (KLAU, U100)'),
        array('DAEWOO', 'KLAV', 'Leganza (KLAV)'),
        array('DAEWOO', 'KLEJ', 'Espero (KLEJ)'),
        array('DAEWOO', 'KLY3', 'Tico (KLY3)'),
        array('DAEWOO', 'KLYA', 'Matiz (KLYA)'),
        array('DAIHA', 'CHARADE Mk III (G100, G101, G102)', 'CHARADE Mk III (G100, G101, G102)'),
        array('DAIHA', 'F70', 'Wildcat/Rocky (F70)'),
        array('DAIHA', 'G101, G102, G103', 'Charade MK III (G101, G102, G103)'),
        array('DAIHA', 'G3', 'Gran Move (G3)'),
        array('DAIHA', 'J11_, J13_', 'Terios KID (J11_, J13_)'),
        array('DAIHA', 'J2_', 'Terios (J2_)'),
        array('DAIHA', 'L201', 'Mira MK III (L201)'),
        array('DAIHA', 'L6_, L9_', 'Move (L6_, L9_)'),
        array('DAIHA', 'L7_', 'Cuore MK IV (L7_)'),
        array('DAIHA', 'M1', 'Sirion (M1)'),
        array('DAIHA', 'M2', 'YRV (M2)'),
        array('FIAT', '125_', '125 (125_)'),
        array('FIAT', '138A', 'Ritmo (138A)'),
        array('FIAT', '141A_', 'Panda (141A_)'),
        array('FIAT', '141_', 'Panda (141_)'),
        array('FIAT', '146A/E', 'Uno (146A/E)'),
        array('FIAT', '159', 'Tempra (159)'),
        array('FIAT', '160', 'Tipo (160)'),
        array('FIAT', '169', 'Panda (169)'),
        array('FIAT', '170', 'Cinquecento (170)'),
        array('FIAT', '176', 'Punto (176, 176C)'),
        array('FIAT', '176C', 'Punto (176, 176C)'),
        array('FIAT', '178BX', 'Palio (178BX, 178DX)'),
        array('FIAT', '178DX', 'Palio (178BX, 178DX)'),
        array('FIAT', '179AX', 'Ulysse (179AX)'),
        array('FIAT', '185', 'Marea (185)'),
        array('FIAT', '186', 'Multipla (186)'),
        array('FIAT', '187', 'Seicento / 600 (187)'),
        array('FIAT', '188', 'Punto (188)'),
        array('FIAT', '192', 'Stilo (192)'),
        array('FIAT', '194', 'Croma (194)'),
        array('FIAT', '220', 'Ulysse (220)'),
        array('FIAT', '223', 'Doblo (223, 119)'),
        array('FIAT', '223, 119', 'Doblo (223, 119)'),
        array('FIAT', '230', 'Ducato (230, 230L)'),
        array('FIAT', '230L', 'Ducato (230, 230L)'),
        array('FIAT', 'BRAVA (182)', 'BRAVA (182)'),
        array('FIAT', 'BRAVO I (182)', 'BRAVO I (182)'),
        array('FIAT', 'FA/175', 'Coupe (FA/175)'),
        array('FIAT', 'SCUDO (бордова) платформа/ шаси (220_) (US)', 'Scudo (220_) (US)'),
        array('FORD', ' ALL', 'Escort Mk VII (GAL, AAL, ABL, ALL, ANL, AFL)'),
        array('FORD', 'ABFT', 'Escort MK IV (GAF, AWF, ABFT, AVF)'),
        array('FORD', 'AVF', 'Escort MK IV (GAF, AWF, ABFT, AVF)'),
        array('FORD', 'AWF', 'Escort MK IV (GAF, AWF, ABFT, AVF)'),
        array('FORD', 'AWF', 'Escort MK IV (GAF, AWF, ABFT, AVF)'),
        array('FORD', 'B4Y', 'Mondeo III (B5Y, BWY, B4Y)'),
        array('FORD', 'B5Y', 'Mondeo III (B5Y, BWY, B4Y)'),
        array('FORD', 'BAP', 'Mondeo II (BAP, BNP)'),
        array('FORD', 'BWY', 'Mondeo III (B5Y, BWY, B4Y)'),
        array('FORD', 'GBC, GBG', 'Sierra (GBG, GB4, GBC)'),
        array('FORD', 'DAW, DBW', 'Focus (DAW, DBW, DNW, DFW)'),
        array('FORD', 'DA_', 'Focus II (DA_)'),
        array('FORD', 'DFW', 'Focus (DAW, DBW, DNW, DFW)'),
        array('FORD', 'DNW', 'Focus (DAW, DBW, DNW, DFW)'),
        array('FORD', 'EC_', 'Puma (EC_)'),
        array('FORD', 'E_ _', 'Transit (E_ _)'),
        array('FORD', 'ESCORT CLASSIC Turnier (ANL)', 'Escort Mk VII (GAL, AAL, ABL, ALL, ANL, AFL)'),
        array('FORD', 'ESCORT Mk IV (GAF, AWF, ABFT)', 'Escort Mk IV (GAF, AWF, AVF, ABFT)'),
        array('FORD', 'ESCORT Mk IV комби (AWF, AVF)', 'Escort Mk IV (GAF, AWF, AVF, ABFT)'),
        array('FORD', 'FA_ _', 'Transit (FA_ _)'),
        array('FORD', 'FBD', 'Fiesta MK II (FBD)'),
        array('FORD', 'Fiesta V Van', 'Fiesta V (JH_, JD_)'),
        array('FORD', 'GAE, GGE', 'Scorpio MK I (GAE, GGE)'),
        array('FORD', 'GAF', 'Escort MK IV (GAF, AWF, ABFT, AVF)'),
        array('FORD', 'GAL', 'Escort Mk V (GAL, AVL)'),
        array('FORD', 'GAL, AAL, ABL', 'Escort Mk VII (GAL, AAL, ABL, ALL, ANL, AFL)'),
        array('FORD', 'GAL, AFL', 'Escort Mk VII (GAL, AAL, ABL, ALL, ANL, AFL)'),
        array('FORD', 'GAL, ANL', 'Escort Mk VII (GAL, AAL, ABL, ALL, ANL, AFL)'),
        array('FORD', 'GAL, AVL', 'Escort Mk V (GAL, AVL)'),
        array('FORD', 'GB4', 'Sierra (GBG, GB4, GBC)'),
        array('FORD', 'GBC', 'Sierra (GBG, GB4, GBC)'),
        array('FORD', 'GBG', 'Sierra (GBG, GB4, GBC)'),
        array('FORD', 'GBG', 'Sierra (GBG, GB4, GBC)'),
        array('FORD', 'GBP', 'Mondeo I (GBP, BNP)'),
        array('FORD', 'GFBT', 'Fiesta (GFBT)'),
        array('FORD', 'GFJ', 'Fiesta MK III (GFJ)'),
        array('FORD', 'JA_, JB_', 'Fiesta MK IV (JA_, JB_)'),
        array('FORD', 'JH_, JD_', 'Fiesta V (JH_, JD_)'),
        array('FORD', 'JU_', 'Fusion (JU_)'),
        array('FORD', 'MAVERICK VAN', 'Maverick (UDS, UNS)'),
        array('FORD', 'MONDEO I Clipper (BNP)', 'Mondeo I (GBP, BNP)'),
        array('FORD', 'MONDEO I Clipper', 'Mondeo I (GBP, BNP)'),
        array('FORD', 'MONDEO Mk II combi', 'Mondeo II (BAP, BNP)'),
        array('FORD', 'MONDEO Mk II седан (BFP)', 'Mondeo II (BAP, BNP, BFP)'),
        array('FORD', 'MONDEO Mk II (BAP)', 'Mondeo II (BAP, BNP, BFP)'),
        array('FORD', 'MONDEO Mk II комби (BNP)', 'Mondeo II (BAP, BNP, BFP)'),
        array('FORD', 'P65_, P70_, P80_', 'Transit Connect (P65_, P70_, P80_)'),
        array('FORD', 'RB_', 'KA (RB_)'),
        array('FORD', 'SCORPIO Mk I', 'Scorpio MK I (GAE, GGE)'),
        array('FORD', 'SIERRA (GBG, GB4)', 'Sierra (GBG, GB4, GBC)'),
        array('FORD', 'TRANSIT кутия', 'Transit'),
        array('FORD', 'UDS, UNS', 'Maverick (UDS, UNS)'),
        array('FORD', 'WGR', 'Galaxy (WGR)'),
        array('FORD_USA', 'ECP', 'Probe MK II (ECP)'),
        array('FORD_USA', 'U2', 'Explorer (U2)'),
        array('HONDA', 'AA', 'City I (AA)'),
        array('HONDA', 'AN, AR', 'Honda Civic I Shuttle (AN, AR)'),
        array('HONDA', 'BB', 'Prelude Mk IV (BB)'),
        array('HONDA', 'CC, CD ', 'Accord V (CD, CC, CE, CF)'),
        array('HONDA', 'CD', 'Accord V (CD, CC, CE, CF)'),
        array('HONDA', 'CE, CF', 'Accord V (CD, CC, CE, CF)'),
        array('HONDA', 'CG, CK', 'Accord VII (CH, CG, CK)'),
        array('HONDA', 'CH', 'Accord VII (CH, CG, CK)'),
        array('HONDA', 'CIVIC Mk II комби (EE)', 'Civic Mk II (EE)'),
        array('HONDA', 'EC, ED, EE', 'Civic III (EC, ED, EE)'),
        array('HONDA', 'EG', 'Civic Mk V (EG)'),
        array('HONDA', 'EH, EG', 'CRX MK III (EH, EG)'),
        array('HONDA', 'EJ', 'Civic Mk VI (EJ, EK)'),
        array('HONDA', 'EJ, EK', 'Civic Mk VI (EJ, EK)'),
        array('HONDA', 'EU, EP, EV', 'Civic VII (EU, EP, EV)'),
        array('HONDA', 'FN, FK', 'Civic VIII (FN, FK)'),
        array('HONDA', 'GE', 'Jazz III (GE)'),
        array('HONDA', 'GH', 'HR-V (GH)'),
        array('HONDA', 'HWW', 'Concerto(HWW)'),
        array('HONDA', 'MA, MB', 'Civic Mk VI (MA, MB)'),
        array('HONDA', 'MB, MC', 'Civic VI (MB, MC)'),
        array('HONDA', 'RA_', 'Shuttle (RA_)'),
        array('HONDA', 'RD', 'CR-V I (RD)'),
        array('HYUNDAI', 'CM', 'Santa fe II (CM)'),
        array('HYUNDAI', 'EF', 'Sonata IV (EF)'),
        array('HYUNDAI', 'FO', 'Trajet (FO)'),
        array('HYUNDAI', 'GK', 'Tiburon (GK)'),
        array('HYUNDAI', 'HP', 'Terracan (HP)'),
        array('HYUNDAI', 'J-2 ', 'Lantra II (J-2)'),
        array('HYUNDAI', 'LC', 'Accent II (LC)'),
        array('HYUNDAI', 'MX', 'Atos Prime (MX)'),
        array('HYUNDAI', 'NF', 'Sonata V (NF)'),
        array('HYUNDAI', 'RD', 'Tiburon (RD)'),
        array('HYUNDAI', 'SLC', 'S Coupe (SLC)'),
        array('HYUNDAI', 'SM', 'Santa Fe I (SM)'),
        array('HYUNDAI', 'TB', 'Getz Prime (TB)'),
        array('HYUNDAI', 'X-2', 'Pony / Excel (X-2)'),
        array('HYUNDAI', 'X-3', 'Accent I (X-3)'),
        array('HYUNDAI', 'XD', 'Elantra (XD)'),
        array('HYUNDAI', 'Y-3', 'Sonata III'),
        array('ISUZU', 'UBS', 'Trooper (UBS)'),
        array('IZH', '0119-1220', 'IZH (0119-1220)'),
        array('JAGUA', 'CCX', 'S-Type (CCX)'),
        array('JAGUA', 'CF1', 'X-Type (CF1)'),
        array('JEEP', 'WJ, WG', 'Grand Cherokee II (WJ, WG)'),
        array('JEEP', 'XJ ', 'Cherokee (XJ)'),
        array('JEEP', 'ZJ', 'Grand Cherokee (ZJ)'),
        array('KIA', 'AVELLA', ' Avella'),
        array('KIA', 'DC', 'Rio (DC_, DC)'),
        array('KIA', 'DC_ ', 'Rio (DC_, DC)'),
        array('KIA', 'FB', 'Mentor/Shuma (FB)'),
        array('KIA', 'FC', 'Carens I (FC)'),
        array('KIA', 'FJ', 'Carens II (FJ)'),
        array('KIA', 'GC', 'Clarus (GC)'),
        array('KIA', 'GD', 'Optima/Magentis (GD)'),
        array('KIA', 'JB', 'Rio II (JB)'),
        array('KIA', 'JC', 'Sorento I (JC)'),
        array('KIA', 'JE_, KM_', 'Sportage (JE_, KM_)'),
        array('KIA', 'JOICE', 'Joice'),
        array('KIA', 'K00', 'Sportage (K00)'),
        array('KIA', 'K9A', 'Clarus (K9A)'),
        array('KIA', 'MG', 'Magentis (MG)'),
        array('KIA', 'PRIDE комби', 'Pride'),
        array('KIA', 'PICANTO (BA)', 'PICANTO (BA)'),
        array('KIA', 'SPORTAGE', 'Sportage'),
        array('KIA', 'TB', 'Pregio (TB)'),
        array('KIA', 'TD', 'Cerato II (TD)'),
        array('KIA', 'UP', 'Carnival I (UP)'),
        array('LADA', '1200-1600', '1200-1600'),
        array('LADA', '2105', '2105'),
        array('LADA', '2107', '2105'),
        array('LADA', '21099', 'Samara Forma (21099)'),
        array('LADA', '21110 1.5i 70HP', '21110'),
        array('LADA', '2121', 'Niva (2121)'),
        array('LADA', 'KALINA комби', 'Kalina'),
        array('LANCIA', '179', 'Phedra (179)'),
        array('LANCIA', '220', 'Zeta (220)'),
        array('LANCIA', '835', 'Dedra (835)'),
        array('LANCIA', '836', 'Delta Mk II (836)'),
        array('LANCIA', '838A', 'Kappa (838A)'),
        array('LANCIA', '839AX', 'Lybra (839AX, 839BX)'),
        array('LANCIA', '839BX', 'Lybra (839AX, 839BX)'),
        array('LANCIA', '840A', 'Y (840A)'),
        array('LANCIA', '843', 'Ypsilon (843)'),
        array('LANCIA', 'THESIS (841AX)', 'THESIS (841AX)'),
        array('LANDROVER', 'LA_, TAA', 'Discovery III (LA_, TAA)'),
        array('LANDROVER', 'LJ, LG', 'Discovery (LJ, LG)'),
        array('LANDROVER', 'LN', 'Freelander (LN, LN_)'),
        array('LANDROVER', 'LN_', 'Freelander (LN, LN_)'),
        array('LANDROVER', 'LP', 'Range Rover MK II (LP)'),
        array('LEXUS', 'IS SportCross', 'IS SportCross'),
        array('LEXUS', 'IS I седан (JCE1_, GXE1_)', 'IS (JCE1_, GXE1_)'),
        array('LEXUS', 'MHU3_, GSU3_, MCU3_', 'RX (MHU3_, GSU3, MCU3_)'),
        array('MAZDA', 'BA', '323 V (BA)'),
        array('MAZDA', 'BF', '323 III (BF)'),
        array('MAZDA', 'BG', '323 IV (BG)'),
        array('MAZDA', 'BJ', '323 VI (BJ)'),
        array('MAZDA', 'BL', '3 (BL)'),
        array('MAZDA', 'CP ', 'Premacy (CP)'),
        array('MAZDA', 'DA', '121 (DA)'),
        array('MAZDA', 'DB', '121 (DB)'),
        array('MAZDA', 'DEMIO 1.3i 16V', 'Demio'),
        array('MAZDA', 'DY', 'Demio (DY)'),
        array('MAZDA', 'GD', '626 III (GD)'),
        array('MAZDA', 'GF', '626 V (GW, GF)'),
        array('MAZDA', 'GG', '6 (GY, GG)'),
        array('MAZDA', 'GW', '626 V (GW, GF)'),
        array('MAZDA', 'GY', '6 (GY, GG)'),
        array('MAZDA', 'LW', 'MPV MK II (LW)'),
        array('MAZDA', 'TA', 'Xedos 9 (TA)'),
        array('MAZDA', '3 (BK)', '3 (BK)'),
        array('MAZDA', '121 Mk III (JASM, JBSM)', '121 Mk III (JASM, JBSM)'),
        array('MERCE', '190 седан (W201)', '190 (W201)'),
        array('MERCE', '414', 'Vaneo (414)'),
        array('MERCE', '601', 'T1 (601,602)'),
        array('MERCE', '602', 'T1 (601,602)'),
        array('MERCE', '638', 'Vito (638)'),
        array('MERCE', '901, 902', 'Sprinter T-2 (901, 902)'),
        array('MERCE', 'C124', 'E-Class Coupe (C124)'),
        array('MERCE', 'C208', 'CLK (C208)'),
        array('MERCE', 'E-CLASS T-Model (S211)', 'E-Class (W211/S211)'),
        array('MERCE', 'E-CLASS седан (W211)', 'E-Class (W211/S211)'),
        array('MERCE', 'KPA', 'MB100 (KPA)'),
        array('MERCE', 'S124', '124 (W124, S124)'),
        array('MERCE', 'S202', 'C-Class (W202/S202)'),
        array('MERCE', 'S210', 'E-Class (W210/S210)'),
        array('MERCE', 'T1 (бордова) платформа/ шаси (601)', 'T1 (601,602)'),
        array('MERCE', 'T1 (бордова) платформа/ шаси (602)', 'T1 (601,602)'),
        array('MERCE', 'W123', '123 (W123)'),
        array('MERCE', 'W124', '124 (W124, S124)'),
        array('MERCE', 'W163', 'M-Class (W163)'),
        array('MERCE', 'W164', 'M-Class (W164)'),
        array('MERCE', 'W168', 'A-Class (W168)'),
        array('MERCE', 'W202', 'C-Class (W202/S202)'),
        array('MERCE', 'W203', 'C-Class (W203)'),
        array('MERCE', 'W210', 'E-Class (W210/S210)'),
        array('MERCE', 'W211', 'E-Class (W211)'),
        array('MERCE', 'W220', 'S-Class (W220)'),
        array('MERCE', 'W222, V222, X222', 'S-Class (W222, V222, X222)'),
        array('MERCE', 'w201', '190 (W201)'),
        array('MG', 'MG ZS Hatchback', 'MG ZS'),
        array('MITSUBISHI', 'C5_A', 'Colt Mk III (C5_A)'),
        array('MITSUBISHI', 'CA_A', 'Colt Mk IV (CA_A)'),
        array('MITSUBISHI', 'CJ_, CP_', 'Colt MK V (CJ_, CP_)'),
        array('MITSUBISHI', 'D2_A', 'Eclipse (D2_A)'),
        array('MITSUBISHI', 'D3_A', 'Eclipse II (D3_A)'),
        array('MITSUBISHI', 'DA_', 'Carisma (DA_)'),
        array('MITSUBISHI', 'DG_A', 'Space Star (DG_A)'),
        array('MITSUBISHI', 'E3_A', 'Galant Mk IV (E3_A)'),
        array('MITSUBISHI', 'E5_A, E7_A, E8_A', 'Galant Mk V (E5_A, E7_A, E8_A)'),
        array('MITSUBISHI', 'EA_', 'Galant Mk VI (EA_)'),
        array('MITSUBISHI', 'H57A', 'Pajero Junior (H57A)'),
        array('MITSUBISHI', 'L 300 STARWAGON автобус (P0_W, P1_W, P2_W, SD, SE, SF, SG, S', 'L 300 STARWAGON (P24W, P24V)'),
        array('MITSUBISHI', 'N1_W, N2_W', 'Space Runner (N1_W, N2_W)'),
        array('MITSUBISHI', 'N3_W, N4_W', 'Space Wagon (N3_W, N4_W)'),
        array('MITSUBISHI', 'N6_W', 'Space Runner (N6_W)'),
        array('MITSUBISHI', 'N9_, N8_', 'Space Wagon (N9_, N8_, N9_W, N8_W)'),
        array('MITSUBISHI', 'N9_W, N8_W', 'Space Wagon (N9_, N8_, N9_W, N8_W)'),
        array('MITSUBISHI', 'PD_W, PC_W, PA_V, PB_V, PA_W', 'L 400 SPACE GEAR (PD5V/W)'),
        array('MITSUBISHI', 'Pajero II 3.5i V6 280hp', 'Pajero II (V44W, V24W)'),
        array('MITSUBISHI', 'V3_W, V2_W, V4_W, NH, NJ, NK, NL', 'Pajero II (V44W, V24W)'),
        array('NISSA', 'A32', 'Maxima (A32)'),
        array('NISSA', 'B13', '100 NX (B13)'),
        array('NISSA', 'E11', 'Note (E11)'),
        array('NISSA', 'HC 23', 'Vanette Cargo (HC 23)'),
        array('NISSA', 'J10, JJ10', 'Qashqai (J10, JJ10)'),
        array('NISSA', 'K11', 'Micra (K11)'),
        array('NISSA', 'K12', 'Micra (K12)'),
        array('NISSA', 'MICRA K10 1.0i 50HP', 'Micra (K10)'),
        array('NISSA', 'MURANO (Z50)', 'MURANO (Z50)'),
        array('NISSA', 'N13', 'Sunny MK II (N13)'),
        array('NISSA', 'N14', 'Sunny MK III (N14)'),
        array('NISSA', 'N15', 'Almera (N15)'),
        array('NISSA', 'N16', 'Almera (N16)'),
        array('NISSA', 'NISSAN MICRA K11 1.5D 57hp', 'Micra (K11)'),
        array('NISSA', 'P11', 'Primera (P11, WP11)'),
        array('NISSA', 'PRIMERA (P10)', 'Primera (P10)'),
        array('NISSA', 'PRIMERA (P12)', 'Primera (P12, WP12)'),
        array('NISSA', 'PRIMERA Hatchback (P10)', 'Primera (P10)'),
        array('NISSA', 'PRIMERA Hatchback (P12)', 'Primera (P12, WP12)'),
        array('NISSA', 'PRIMERA Traveller (WP12)', 'Primera (P12, WP12)'),
        array('NISSA', 'Q BIC III кутия (Y10)', 'Sunny III (Y10)'),
        array('NISSA', 'R20', 'Sunny III (Y10)'),
        array('NISSA', 'SERENA (C23M)', 'SERENA (C23M)'),
        array('NISSA', 'SUNNY III Traveller (Y10)', 'Sunny III (Y10)'),
        array('NISSA', 'TERRANO II (R20)', 'TERRANO II (R20)'),
        array('NISSA', 'TERRANO II Van (R20)', 'TERRANO II Van (R20)'),
        array('NISSA', 'V10', 'Almera Tino (V10)'),
        array('NISSA', 'WD21', 'Terrano I (WD21)'),
        array('NISSA', 'WP11', 'Primera (P11, WP11)'),
        array('NISSA', 'T30', 'X-TRAIL (T30)'),
        array('OPEL', '16_, 17_, 19_', 'Omega A (16_, 17_, 19_)'),
        array('OPEL', '21_, 22_, 23_', 'Omega B (21_,22_,23_,25_,26_,27)'),
        array('OPEL', '25_, 26_, 27_', 'Omega B (21_,22_,23_,25_,26_,27)'),
        array('OPEL', '31_', 'Vectra B (36_, 31_, 38_)'),
        array('OPEL', '35_, 36_, 45_, 46_', 'Kadett (39_,49_ + 38_,48_ + 35_, 36_,45_,46_ + 37_,47_)'),
        array('OPEL', '36_', 'Vectra B (36_, 31_, 38_)'),
        array('OPEL', '37_, 47_', 'Kadett (39_,49_ + 38_,48_ + 35_, 36_,45_,46_ + 37_,47_)'),
        array('OPEL', '38_', 'Vectra B (36_, 31_, 38_)'),
        array('OPEL', '38_, 48_', 'Kadett (39_,49_ + 38_,48_ + 35_, 36_,45_,46_ + 37_,47_)'),
        array('OPEL', '39_, 49_', 'Kadett (39_,49_ + 38_,48_ + 35_, 36_,45_,46_ + 37_,47_)'),
        array('OPEL', '51_, 52_', 'Astra F (56_, 57_,51_,52_,53_,54_,58_,58_)'),
        array('OPEL', '53_, 54_, 58_, 59_', 'Astra F (56_, 57_,51_,52_,53_,54_,58_,58_)'),
        array('OPEL', '56_, 57_ ', 'Astra F (56_, 57_,51_,52_,53_,54_,58_,58_)'),
        array('OPEL', '5_MWL4', 'Frontera A (5_MWL4)'),
        array('OPEL', '5_SUD2', 'Frontera A Sport (5_SUD2)'),
        array('OPEL', '73_, 78_, 79_', 'Corsa B (73_,78_, 79_ ,F35)'),
        array('OPEL', '85_', 'Calibra A (85_)'),
        array('OPEL', '86_, 87_', 'Vectra A (86_,87_,88_,89_)'),
        array('OPEL', '88_, 89_', 'Vectra A (86_,87_,88_,89_)'),
        array('OPEL', '93_, 94_, 98_, 99_', 'Corsa A (93_, 94_, 98_, 99_)'),
        array('OPEL', '95_', 'Tigra (95_)'),
        array('OPEL', 'A05', 'Zafira B (A05)'),
        array('OPEL', 'AGILA (A) (H00)', 'Agila (A) (H00)'),
        array('OPEL', 'ASTRA F CLASSIC хетчбек', 'Astra F'),
        array('OPEL', 'COMBO фургон/комби', 'Combo'),
        array('OPEL', 'F07_', 'Astra G (F67,F35_,F07_,F69_,F48_,F08_)'),
        array('OPEL', 'F08, F68', 'Corsa C (F08,F68,W5L)'),
        array('OPEL', 'F08, W5L', 'Corsa C (F08,F68,W5L)'),
        array('OPEL', 'F35', 'Corsa B (73_,78_, 79_ ,F35)'),
        array('OPEL', 'F35_', 'Astra G (F67,F35_,F07_,F69_,F48_,F08_)'),
        array('OPEL', 'F48_, F08_', 'Astra G (F67,F35_,F07_,F69_,F48_,F08_)'),
        array('OPEL', 'F67', 'Astra G (F67,F35_,F07_,F69_,F48_,F08_)'),
        array('OPEL', 'F69_', 'Astra G (F67,F35_,F07_,F69_,F48_,F08_)'),
        array('OPEL', 'F75_', 'Zafira A (F75_)'),
        array('OPEL', 'INSIGNIA комби', 'Insignia'),
        array('OPEL', 'L35', 'Astra H (L35, L48)'),
        array('OPEL', 'L48', 'Astra H (L35, L48)'),
        array('OPEL', 'MERIVA', 'Meriva'),
        array('OPEL', 'REKORD C', 'Rekord C'),
        array('OPEL', 'SIGNUM', 'Signum'),
        array('OPEL', 'SINTRA', 'Sintra'),
        array('OPEL', 'TIGRA TwinTop', 'Tigra TwinTop'),
        array('OPEL', 'VECTRA C', 'Vectra C'),
        array('PEUGE', '1', '106 Mk II (1)'),
        array('PEUGE', '107', '107'),
        array('PEUGE', '15B', '405 I (15B)'),
        array('PEUGE', '1A, 1C', '106'),
        array('PEUGE', '205   (741A/C)', '205 I (741A/C)'),
        array('PEUGE', '206 CC (2D)', '206 (2A/C, 2D, 2E/K)'),
        array('PEUGE', '206 хетчбек (2A/C)', '206 (2A/C, 2D, 2E/K)'),
        array('PEUGE', '207 (2A/C, 2D, 2E/K)', '206 (2A/C, 2D, 2E/K)'),
        array('PEUGE', '208 (2A/C, 2D, 2E/K)', '206 (2A/C, 2D, 2E/K)'),
        array('PEUGE', '20A/C', '205 Mk II (20A/C)'),
        array('PEUGE', '221', '806 (221)'),
        array('PEUGE', '222', 'Expert (224,222)'),
        array('PEUGE', '224', 'Expert (224,222)'),
        array('PEUGE', '230L', 'Boxer (230L)'),
        array('PEUGE', '244', 'Boxer (244)'),
        array('PEUGE', '2A/C', '208 (2A/C, 2D, 2E/K)'),
        array('PEUGE', '2D', '207 (2A/C, 2D, 2E/K)'),
        array('PEUGE', '2E/K', '206 (2A/C, 2D, 2E/K)'),
        array('PEUGE', '306 кабриолет (7D, N3, N5)', '306 (7E,7D,7B,7A,7C,N5,N3)'),
        array('PEUGE', '306 седан (7B, N3, N5)', '306 (7E,7D,7B,7A,7C,N5,N3)'),
        array('PEUGE', '306 хетчбек (7A, 7C, N3, N5)', '306 (7E,7D,7B,7A,7C,N5,N3)'),
        array('PEUGE', '307 (7E,7D,7B,7A,7C,N5,N3)', '306 (7E,7D,7B,7A,7C,N5,N3)'),
        array('PEUGE', '307 CC (3B)', '307 (3A/C, 3B, 3E, 3H)'),
        array('PEUGE', '307 Estate (3E)', '307 (3A/C, 3B, 3E, 3H)'),
        array('PEUGE', '307 SW (3H)', '307 (3A/C, 3B, 3E, 3H)'),
        array('PEUGE', '308 (3A/C, 3B, 3E, 3H)', '307 (3A/C, 3B, 3E, 3H)'),
        array('PEUGE', '308 (7E,7D,7B,7A,7C,N5,N3)', '306 (7E,7D,7B,7A,7C,N5,N3)'),
        array('PEUGE', '309 (3A/C, 3B, 3E, 3H)', '307 (3A/C, 3B, 3E, 3H)'),
        array('PEUGE', '310 (3A/C, 3B, 3E, 3H)', '307 (3A/C, 3B, 3E, 3H)'),
        array('PEUGE', '3A/C', '307 (3A/C, 3B, 3E, 3H)'),
        array('PEUGE', '3B', '308 (3A/C, 3B, 3E, 3H)'),
        array('PEUGE', '3C, 3A', '309 MK II (3C, 3A)'),
        array('PEUGE', '3E', '309 (3A/C, 3B, 3E, 3H)'),
        array('PEUGE', '3H', '310 (3A/C, 3B, 3E, 3H)'),
        array('PEUGE', '405 II (4E)', '405 II (4B, 4E)'),
        array('PEUGE', '405 Mk II Estate (4E)', '405 II (4B, 4E)'),
        array('PEUGE', '405 II седан (4B)', '405 II (4B, 4E)'),
        array('PEUGE', '406 купе (8C)', '406 (8E/F, 8C, 8B)'),
        array('PEUGE', '406 седан (8B)', '406 (8E/F, 8C, 8B)'),
        array('PEUGE', '407 (8E/F, 8C, 8B)', '406 (8E/F, 8C, 8B)'),
        array('PEUGE', '408 (8E/F, 8C, 8B)', '406 (8E/F, 8C, 8B)'),
        array('PEUGE', '4A_, 4C_', '308 (4A_, 4C_)'),
        array('PEUGE', '4B', '405 II (4B, 4E)'),
        array('PEUGE', '4E', '405 II (4E)'),
        array('PEUGE', '5', 'Partner (5)'),
        array('PEUGE', '6B', '605 (6B)'),
        array('PEUGE', '7A, 7C, N5, N3', '306 (7E,7D,7B,7A,7C,N5,N3)'),
        array('PEUGE', '7B, N3, N5', '308 (7E,7D,7B,7A,7C,N5,N3)'),
        array('PEUGE', '7D, N3, N5', '307 (7E,7D,7B,7A,7C,N5,N3)'),
        array('PEUGE', '7E, N3, N5', '306 (7E,7D,7B,7A,7C,N5,N3)'),
        array('PEUGE', '8B', '408 (8E/F, 8C, 8B)'),
        array('PEUGE', '8C', '407 (8E/F, 8C, 8B)'),
        array('PEUGE', '8E/F', '406 (8E/F, 8C, 8B)'),
        array('PEUGE', '9D, 9U', '607 (9D, 9U)'),
        array('PEUGE', 'E', '807 (E)'),
        array('PEUGE', 'WA_, WC_', '207 (WA_, WC_)'),
        array('PORSC', '9PA, 955', 'Cayenne (9PA, 955)'),
        array('RENAU', '19 I Chamade (L53_)', '19 I (B/C53_, L53_)'),
        array('RENAU', '19   (B/C53_) ', '19 I (B/C53_, L53_)'),
        array('RENAU', '21 (B48_)', '21 (B48_)'),
        array('RENAU', 'B/C40_', 'Super 5 (B/C40_)'),
        array('RENAU', 'B/C53_', '19 Mk2 (B/C53_,L53_)'),
        array('RENAU', 'B/C57_ , 5/357_', 'Lutecia I (B/C57_, 5/357_)'),
        array('RENAU', 'B/C57_, 5/357_', 'LUTECIA I (B/C57_, 5/357_)'),
        array('RENAU', 'B56_, 556_', 'Laguna I (B56_, 556_, K56_)'),
        array('RENAU', 'BA0/1_', 'Megane I (BA0/1_)'),
        array('RENAU', 'BB0/1/2_', 'Clio II (SB0/1/2_,BB0/1/2_,CB0/1/2_)'),
        array('RENAU', 'BB0/1/2_, CB0/1/2_', 'Clio II (SB0/1/2_,BB0/1/2_,CB0/1/2_)'),
        array('RENAU', 'BG0/1_', 'Laguna II (BG0/1_,KG0/1_)'),
        array('RENAU', 'BJ0_', 'Vel Satis (BJ0_)'),
        array('RENAU', 'BM0/1_, CM0/1_', 'Megane II (BM0/1_, CM0/1_, KM0/1_, LM0/1_)'),
        array('RENAU', 'BR0/1, CR0/1', 'Clio III (BR0/1, CR0/1)'),
        array('RENAU', 'BZ0', 'Megane III (BZ0)'),
        array('RENAU', 'BZ0_', 'MEGANE III hatchback (BZ0_)'),
        array('RENAU', 'C06_', 'Twingo I (C06_, S06_)'),
        array('RENAU', 'CB0/1/2_', 'Clio II (SB0/1/2_,BB0/1/2_,CB0/1/2_)'),
        array('RENAU', 'CLIO Mk II (BB0/1/2_, CB0/1/2_)', 'Clio II (SB0/1/2_,BB0/1/2_,CB0/1/2_)'),
        array('RENAU', 'DA0/1_', 'Megane I Coach (DA0/1_)'),
        array('RENAU', 'EA0/1_', 'Megane I Cabriolet (EA0/1_)'),
        array('RENAU', 'F/JP0_', 'Modus/Grand Modus (F/JP0_)'),
        array('RENAU', 'FC0/1_', 'Kangoo (KC0/1,FC0/1)'),
        array('RENAU', 'GRAND SCÉNIC II (JM0/1_)', 'Grand Scenic II (JM0/1_)'),
        array('RENAU', 'J/S63_', 'Espace II (J/S63_)'),
        array('RENAU', 'J11_', 'Espace (J11_)'),
        array('RENAU', 'JA0/1_', 'Scenic / Megane Scenic (JA0/1_)'),
        array('RENAU', 'JE0_', 'Espace III (JE0_)'),
        array('RENAU', 'JK0/1_', 'Espace IV (JK0/1_)'),
        array('RENAU', 'K56_', 'Laguna I (B56_, 556_, K56_)'),
        array('RENAU', 'KA0/1_', 'Megane I Grandtour (KA0/1_)'),
        array('RENAU', 'KC0/1_', 'Kangoo (KC0/1,FC0/1)'),
        array('RENAU', 'KG0/1_', 'Laguna II (BG0/1_,KG0/1_)'),
        array('RENAU', 'KM0/1_', 'Megane II (BM0/1_, CM0/1_, KM0/1_, LM0/1_)'),
        array('RENAU', 'KT0/1', 'Laguna III (KT0/1)'),
        array('RENAU', 'L30_', 'Fluence (L30_)'),
        array('RENAU', 'L53_', '19 Mk2 (B/C53_,L53_)'),
        array('RENAU', 'LA0/1_', 'Megane I Classic (LA0/1_)'),
        array('RENAU', 'LB0/1/2_', 'Symbol I (LB0/1/2_)'),
        array('RENAU', 'LM0/1_', 'Megane II (BM0/1_, CM0/1_, KM0/1_, LM0/1_)'),
        array('RENAU', 'LUTECIA I (B/C57_, 5/357_)', 'LUTECIA I (B/C57_, 5/357_)'),
        array('RENAU', 'RENAULT MEGANE SCENIC 1.9DCI 120hp', 'Megane Scenic'),
        array('RENAU', 'MEGANE III хетчбек (BZ0_)', 'MEGANE III hatchback (BZ0_)'),
        array('RENAU', 'S06_', 'Twingo I (C06_, S06_)'),
        array('RENAU', 'S37_', '11 (S37_)'),
        array('RENAU', 'S57_', 'Clio (S57_)'),
        array('RENAU', 'SAFRANE Mk II (B54_)', 'Safrane Mk II (B54_)'),
        array('RENAU', 'SB0/1/2_', 'Clio II (SB0/1/2_,BB0/1/2_,CB0/1/2_)'),
        array('RENAU', 'TXW', 'Trafic (TXW)'),
        array('ROVER', '200 (RF)', '200 (RF)'),
        array('ROVER', '200 купе (XW)', '200 (XW)'),
        array('ROVER', '200 хетчбек (XW)', '200 (XW)'),
        array('ROVER', '25 (RF)', '25 (RF)'),
        array('ROVER', '400 (RT)', '400 (RT)'),
        array('ROVER', '400 (XW)', '400 (XW)'),
        array('ROVER', '400 Tourer (XW)', '400 (XW)'),
        array('ROVER', '400 хетчбек (RT)', '400 (RT)'),
        array('ROVER', '45 (RT)', '45 (RT)'),
        array('ROVER', '45 седан (RT)', '45 (RT)'),
        array('ROVER', 'MAESTRO', 'Maestro'),
        array('ROVER', 'RF', '200 (RF)'),
        array('ROVER', 'RF', '25 (RF)'),
        array('ROVER', 'RJ', '75 (RJ)'),
        array('ROVER', 'RT', '45 / 400 (RT)'),
        array('ROVER', 'XW', '200 / 400(XW)'),
        array('SAAB', '900 Mk II кабриолет', '900 Mk II'),
        array('SAAB', '900 Mk II купе', '900 Mk II'),
        array('SAAB', '900 Mk II', '900 Mk II'),
        array('SAAB', '9000 хетчбек', '9000'),
        array('SAAB', 'YS3D', '9-3 (YS3D)'),
        array('SAAB', 'YS3E', '9-5 (YS3E)'),
        array('SEAT', '021A', 'Ibiza (021A)'),
        array('SEAT', '1L', 'Toledo (1L)'),
        array('SEAT', '1M1', 'Leon (1M1)'),
        array('SEAT', '1M2', 'Toledo (1M2)'),
        array('SEAT', '6H', 'Arosa (6H)'),
        array('SEAT', '6K1, 6K2', 'Cordoba (6K1,6K2,6K5)'),
        array('SEAT', '6K2', 'Cordoba (6K2)'),
        array('SEAT', '6K5', 'Cordoba (6K1,6K2,6K5)'),
        array('SEAT', '6K9', 'Inca (6K9)'),
        array('SEAT', '6L1', 'Ibiza MK IV (6L1)'),
        array('SEAT', '6L2', 'Cordoba (6L2)'),
        array('SEAT', 'ALHAMBRA (7V8, 7V9)', 'ALHAMBRA (7V8, 7V9)'),
        array('SEAT', 'Cordoba (6K2)', 'Cordoba (6K1,6K2,6K5)'),
        array('SEAT', 'CORDOBA (6K2)', 'Cordoba (6K1,6K2,6K5)'),
        array('SEAT', 'IBIZA Mk II (6K1)', 'IBIZA Mk II (6K1)'),
        array('SEAT', 'IBIZA Mk III (6K1)', 'IBIZA Mk III (6K1)'),
        array('SKODA', ' 6Y3', 'Fabia (6Y2,6Y3, 6Y5)'),
        array('SKODA', '1U2', 'Octavia (1U2, 1U5)'),
        array('SKODA', '1U5', 'Octavia (1U2, 1U5)'),
        array('SKODA', '1Z3', 'Octavia (1Z3)'),
        array('SKODA', '5J', 'Roomster (5J)'),
        array('SKODA', '6U1', 'Felicia Mk II (6U1, 6U5)'),
        array('SKODA', '6U5', 'Felicia Mk II (6U1, 6U5)'),
        array('SKODA', '6Y2', 'Fabia (6Y2,6Y3, 6Y5)'),
        array('SKODA', '6Y5', 'Fabia (6Y2,6Y3, 6Y5)'),
        array('SKODA', '742', '105,120 (742)'),
        array('SKODA', '745', '105,120 (745)'),
        array('SKODA', '781', 'Favorit (781)'),
        array('SKODA', 'FABIA Combi', 'Fabia'),
        array('SKODA', 'FABIA', 'Fabia'),
        array('SSANG', 'FJ', 'Musso (FJ)'),
        array('SSANG', 'KJ', 'Korando (KJ)'),
        array('SUBAR', 'BD, BG', 'Legacy Mk II (BD,BG)'),
        array('SUBAR', 'BE, BH', 'Legacy Mk III (BE, BH)'),
        array('SUBAR', 'JMA, MS', 'Justy Mk II (JMA,MS)'),
        array('SUBAR', 'KAD', 'Justy (KAD)'),
        array('SUBAR', 'SF', 'Forester (SF)'),
        array('SUZUK', 'AH, AJ', 'Swift MK II (AH, AJ, EA, MA)'),
        array('SUZUK', 'AH,AJ', 'Swift Mk II  (AH,AJ,EA,MA)'),
        array('SUZUK', 'AERIO комби (ER)', 'Liana/Aerio (ER)'),
        array('SUZUK', 'EA, MA', 'Swift MK II (AH, AJ, EA, MA)'),
        array('SUZUK', 'EA,MA', 'Swift Mk II  (AH,AJ,EA,MA)'),
        array('SUZUK', 'EG', 'Baleno (EG)'),
        array('SUZUK', 'EM', 'Wagon R+ (EM)'),
        array('SUZUK', 'ER', 'Aerio (ER)'),
        array('SUZUK', 'FT', 'Grand Vitara (FT, GT)'),
        array('SUZUK', 'GT', 'Grand Vitara (FT, GT)'),
        array('SUZUK', 'HA11', 'Alto (HA11)'),
        array('SUZUK', 'HA12, HA23', 'Alto (HA12, HA23)'),
        array('SUZUK', 'HA24', 'Alto (HA24)'),
        array('SUZUK', 'INC XL-7', 'Grand Vitara (FT, GT)'),
        array('SUZUK', 'MARUTI', 'Maruti'),
        array('SUZUK', 'SAMURAI', 'Samurai'),
        array('SUZUK', 'SJ', 'Samurai (SJ)'),
        array('SMART', 'CITY-COUPE (450)', 'CITY-COUPE (450)'),
        array('TATA', '40_FD', 'Telcoline (40_FD)'),
        array('TATA', 'INDICA VISTA', 'Indica Vista'),
        array('TOYOT', '4 RUNNER (RN10_, VZN13_, VZN10, RN13_', '4 RUNNER (RN10_, VZN13_, VZN10, RN13_)'),
        array('TOYOT', 'ACA3_, ACE_, ALA3_, GSA3_, ZSA3_', 'Rav 4 III (ACA3_, ACE_, ALA3_, GSA3_, ZSA3_)'),
        array('TOYOT', 'AT18_, ST18_', 'Celica (AT18_, ST18_ )'),
        array('TOYOT', 'AT20_, ST20_', 'Celica (ST20_,  AT20_)'),
        array('TOYOT', 'AVENSIS (_T22_)', 'Avensis (_T22_)'),
        array('TOYOT', 'AVENSIS Liftback (_T22_)', 'Avensis (_T22_)'),
        array('TOYOT', 'AVENSIS Combi (T25)', 'AVENSIS Combi (T25)'),
        array('TOYOT', 'AVENSIS Station Wagon (_T22_)', 'Avensis (_T22_)'),
        array('TOYOT', 'COROLLA седан (E15_)', 'Corolla (E15_)'),
        array('TOYOT', 'CDE12_, ZZE12_, NDE12_, ZDE12_', 'Corolla (CDE12_, ZZE12_, NDE12_, ZDE12_)'),
        array('TOYOT', 'EL54', 'Paseo (EL54)'),
        array('TOYOT', 'Echo', 'Echo'),
        array('TOYOT', 'HILUX (VIGO) III пикап (KUN_, TGN_, LAN_, GGN_)', 'Hilux III пикап (KUN_, TGN_, LAN_, GGN_)'),
        array('TOYOT', 'RN10_, VZN13_, VZN10_, RN13_', '4 RUNNER (RN10_, VZN13_, VZN10_, RN13_)'),
        array('TOYOT', 'RZN1_, LN1_', 'Hilux (RZN1_, LN1_)'),
        array('TOYOT', 'SCP1_, NLP1_, NCP1_', 'Yaris (SCP1_, NLP1_, NCP1_)'),
        array('TOYOT', 'SCP9_, NSP9_, KSP9_, NCP9_, ZSP9_', 'Yaris (SCP9_, NSP9_, KSP9_, NCP9_, ZSP9_)'),
        array('TOYOT', 'ST16_, AT16_', 'Celica (ST16_, AT16_)'),
        array('TOYOT', 'ST20_,  AT20_', 'Celica (ST20_,  AT20_)'),
        array('TOYOT', 'ST20_, AT20_', 'Celica (ST20_, AT20_)'),
        array('TOYOT', 'SXA1_', 'Rav 4 (SXA1_)'),
        array('TOYOT', 'Scion', 'Scion'),
        array('TOYOT', 'TCR2_, TCR1_', 'Previa (TCR2_, TCR1_)'),
        array('TOYOT', 'YARIS/VITZ (NHP13_, NSP13_, NCP13_, KSP13_, NLP13_)', 'Yaris (NHP13_, NSP13_, NCP13_, KSP13_, NLP13_)'),
        array('TOYOT', 'ZZT23_', 'Celica (ZZT23_)'),
        array('TOYOT', '_E10_', 'Corolla (_E10_)'),
        array('TOYOT', '_E11_', 'Corolla (_E11_)'),
        array('TOYOT', '_E9_', 'Corolla (_E9_)'),
        array('TOYOT', '_NLP2_, _NCP2_', 'Yaris Verso (_NLP2_, _NCP2_)'),
        array('TOYOT', '_T19_', 'Carina (_T19_)'),
        array('TOYOT', '_XM10', 'Picnic (_XM10)'),
        array('TOYOT', '__E11_', 'Corolla (_E11_)'),
        array('TRABA', 'P 601 кутия', 'P 601'),
        array('TRABA', 'P 601 кутия', 'P 601'),
        array('VOLVO', '445', '440 (445)'),
        array('VOLVO', '464', '460 (464)'),
        array('VOLVO', '964', '960 (964)'),
        array('VOLVO', 'LS', '850 (LS,LW)'),
        array('VOLVO', 'LS', 'S70/V70 (LS,LV)'),
        array('VOLVO', 'LV', 'S70/V70 (LS,LV)'),
        array('VOLVO', 'LW', '850 (LS,LW)'),
        array('VOLVO', 'P245', '240 (P245)'),
        array('VOLVO', 'S60 I седан', 'S60'),
        array('VOLVO', 'S70 седан (LS)', 'S70/V70 (LS,LV)'),
        array('VOLVO', 'SW', 'V70 Mk II (SW)'),
        array('VOLVO', 'TS, XY', 'S80 (TS, XY)'),
        array('VOLVO', 'VS', 'S40/V40 (VS,VW)'),
        array('VOLVO', 'VW', 'S40/V40 (VS,VW)'),
        array('VW', ' 6KV5', 'Polo (6KV2, 6KV5)'),
        array('VW', '19E, 1G1', 'Golf Mk II (19E, 1G1)'),
        array('VW', '1H1', 'Golf III (1H1, 1H5, 1E7)'),
        array('VW', '1H2', 'Vento (1H2)'),
        array('VW', '1H5', 'Golf III (1H1, 1H5, 1E7)'),
        array('VW', '1J1', 'Golf IV (1J1, 1J5)'),
        array('VW', '1J2', 'Bora (1J2)'),
        array('VW', '1J5', 'Golf IV (1J1, 1J5)'),
        array('VW', '1K2', 'Jetta / Bora III'),
        array('VW', '1Е7', 'Golf III (1H1, 1H5, 1E7)'),
        array('VW', '281-363', 'LT 28-35 I (281-363)'),
        array('VW', '2KA, 2KH, 2CA, 2CH', 'Caddy III (2KA, 2KH, 2CA, 2CH)'),
        array('VW', '3A2, 35I', 'Passat (3A2/3A5,35I)'),
        array('VW', '3A5, 35I', 'Passat (3A2/3A5,35I)'),
        array('VW', '3B2', 'Passat (3B2, 3B5)'),
        array('VW', '3B3', 'Passat (3B3, 3B6)'),
        array('VW', '3B5', 'Passat (3B2, 3B5)'),
        array('VW', '3B6', 'Passat (3B3, 3B6)'),
        array('VW', '3C2', 'Passat (3C2, 3C5)'),
        array('VW', '3C5', 'Passat (3C2, 3C5)'),
        array('VW', '5K1', 'GOLF VI (5K1)'),
        array('VW', '5Z1, 5Z3', 'Fox (5Z1, 5Z3)'),
        array('VW', '6KV2', 'Polo (6KV2, 6KV5)'),
        array('VW', '6N1', 'Polo (6n1)'),
        array('VW', '6N2', 'Polo (6n2)'),
        array('VW', '6NF', 'Polo (6NF)'),
        array('VW', '6R, 6C', 'Polo (6R, 6C)'),
        array('VW', '6X1, 6E1', 'Lupo (6X1, 6E1)'),
        array('VW', '70XA', 'Transporter / Caravelle (70XB, 70XC, 7DB, 7DW, 7D, 70XA)'),
        array('VW', '7LA, 7L6, 7L7', 'Touareg (7LA, 7L6, 7L7)'),
        array('VW', '7M8, 7M9, 7M6', 'Sharan (7M8, 7M9, 7M6)'),
        array('VW', '86C, 80', 'Polo (86C, 80)'),
        array('VW', '86CF', 'Polo (86CF)'),
        array('VW', '9C1, 1C1', 'New Beetle (9C1, 1C1)'),
        array('VW', '9K9A', 'Caddy II (9K9A, 9U7)'),
        array('VW', '9N_', 'Polo (9N_)'),
        array('VW', '9U7', 'Caddy II (9K9A, 9U7)'),
        array('VW', 'GOLF Mk III кабриолет (1E7)', 'Golf III (1H1, 1H5, 1E7)'),
        array('VW', 'GOLF  V (1K1)', 'GOLF V (1K1)'),
        array('VW', 'GOLF V 1.9TDI BKC 2003-2008', 'GOLF V (1K1)'),
        array('VW', 'PASSAT ALLTRACK (365)', 'PASSAT VII (362/365)'),
        array('VW', 'PASSAT Variant (365)', 'PASSAT VII (362/365)'),
        array('VW', 'TRANSPORTER / CARAVELLE IV автобус (70XB, 70XC, 7DB, 7DW, 7D', 'Transporter / Caravelle (70XB, 70XC, 7DB, 7DW, 7D, 70XA)'),
        array('VW', 'TRANSPORTER / CARAVELLE Mk III автобус', 'Transporter / Caravelle III '),
        array('ZAZ', '1102', 'Tavria (1102)'),
    );
}
