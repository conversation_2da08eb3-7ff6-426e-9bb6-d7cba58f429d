<?php
/**
 * Order Send Process
 *
 * @package  Stenik_Zeron
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * @method Stenik_Zeron_Process_Resource_Order getResource()
 */
class Stenik_Zeron_Process_Order_Send extends Stenik_Zeron_Process_Order_Abstract
{
    /**
     * Init.
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init('stenik_zeron_process/order');
    }

    /**
     * Run
     *
     * @return void
     */
    public function run()
    {
        $this->verbose("Send orders start: %s\n", date('Y-m-d H:i:s'));

        $orderCollection = Mage::getModel('sales/order')->getCollection()
            ->addAttributeToFilter('status', ['nin' => ['pending_payment', 'canceled']])
            ->addFieldToFilter('stenik_zeron_process_status', [
                'or' => [
                    ['null' => true],
                    ['eq' => self::PROCESS_STATUS_NEW]
                ]
            ]);

        $orderCollection->getSelect()->reset(Zend_Db_Select::COLUMNS);
        $orderCollection->getSelect()->columns('increment_id');

        $orderIncrementIdsToSend = $orderCollection->getConnection()->fetchCol($orderCollection->getSelect());
        $this->verbose("%s orders were found to be sent.\n", count($orderIncrementIdsToSend));

        if (count($orderIncrementIdsToSend)) {
            Mage::helper('stenik_logger')->log(
                'stenik_zeron_order_send_before_summary',
                sprintf('%s order(s) were found to be sent to Zeron.', count($orderIncrementIdsToSend)),
                'Order Send: Summary before sending.',
                Stenik_Logger_Helper_Data::SEVERITY_INFO
            );

            foreach ($orderIncrementIdsToSend as $orderIncrementId) {
                $this->_sendOrder($orderIncrementId);
            }
        }


        $this->verbose("Send orders end: %s\n", date('Y-m-d H:i:s'));
    }


    /**
     * Send one order
     *
     * @param mixed $order
     * @return Varien_Object|boolean
     */
    public function sendOrder($order)
    {
        return $this->_sendOrder($order);
    }

    /**
     * Send order
     *
     * @param mixed $order Object or Increment ID
     * @return Varien_Object|boolean
     */
    protected function _sendOrder($order)
    {
        $data = [];
        try {
            if (!($order instanceof Mage_Sales_Model_Order)) {
                $order = Mage::getModel('sales/order')->loadByIncrementId($order);
            }

            if (!$order || !$order->getId()) {
                $this->verbose("Order not found in Magento\n");
                return false;
            }

            $shippingAddress = $order->getShippingAddress();

            /* Order Info */

            $deliveryDate = Mage::app()->getLocale()->date();
            $deliveryDate->addDay(1);

            if (!empty($shippingAddress->getStreetFull())) {
                $deliveryAddressSreet = $shippingAddress->getStreetFull();
            } else {
                $deliveryAddressSreet = "Няма Адрес";
            }

            if (!empty($shippingAddress->getName())) {
                $deliveryAddressName = $shippingAddress->getName();
            } else {
                $deliveryAddressName = "Няма Име";
            }

            if (!empty($shippingAddress->getCity())) {
                $deliveryAddressCity = $shippingAddress->getCity();
            } else {
                $deliveryAddressCity = "Няма Град";
            }

            $data = [
                'WEBDocID'            => $order->getIncrementId(),
                'EshopPartner'        => $order->getCustomerId() ?: 'guest',
                'PartnerCode'         => null,
                'TaxNumber'           => *********, // This value will be replaced if there is an invoice
                'VATNumber'           => null, // Invoice.
                'PartnerName'         => !$order->getCustomerId() ? 'Guest' : $order->getCustomerName(),
                'DocPartnerName'      => $shippingAddress->getName(),
                // This value will be replaced if there is an invoice
                'InvoiceAddress'      => $deliveryAddressSreet, // This value will be replaced if there is an invoice
                'DeliveryAddress'     => $deliveryAddressSreet . ', ' . $deliveryAddressName . ', ' . $deliveryAddressCity,
                'DeliveryPhoneNumber' => $shippingAddress->getTelephone(),
                'Email'               => $order->getCustomerEmail(),
                'ContactPerson'       => null, // Invoice.
                'DeliveryDate'        => $deliveryDate->toString('y-MM-dd'),
                'CurrencyCode'        => $order->getBaseCurrencyCode(),
                'TransportationCost'  => round($order->getBaseShippingAmount(),2),
                'Storehouse'          => 1,
                'InvoiceRequired'     => 0, // Invoice
                'Comment'             => $this->_getComment($order),
            ];

            /* Payment */
            $paymentMethodFromOrder = $order->getPayment()->getMethod();
            $shippingMethodFromOrder = $order->getShippingMethod();
            switch ($paymentMethodFromOrder) {
                case 'pfg_borica':
                    if (
                        strpos($shippingMethodFromOrder, 'extensa_econt_') !== false ||
                        strpos($shippingMethodFromOrder, PFG_Speedy_Model_Carrier_Speedy::CARRIER_CODE) !== false
                    ) {
                        // when shipping is Econt
                        $data['PaymentType'] = 3; // 3 = С Карта
                    } else {
                        $data['PaymentType'] = 3; // 3 = С Карта
//                        $data['PaymentType'] = 1; // 1 = В Брой
                    }
                    break;
                case 'banktransfer':
                    $data['PaymentType'] = 2; // 2 = По Банк`а
                    break;
                case 'checkmo':
                case 'cashondelivery':
                case 'extensa_econt':
                default:
                    if (
                        strpos($shippingMethodFromOrder, 'extensa_econt_') !== false ||
                        strpos($shippingMethodFromOrder, PFG_Speedy_Model_Carrier_Speedy::CARRIER_CODE) !== false
                    ) {
                        $data['PaymentType'] = 8; // 8 = Наложен платеж
                    } else {
                        $data['PaymentType'] = 1; // 1 = В Брой
                    }

                    break;
            }

            /* Shipping */
            switch ($shippingMethodFromOrder) {
                case Carco_InternalTransport_Model_Carrier_Internal::FULL_METHOD_CODE:
                case 'pfg_store_pickup_pfg_store_pickup':
                case PFG_Shop_Model_Carrier::FULL_METHOD_CODE:
                    $data['CouriersCode'] = null; // null = Собствен транспорт
                    break;
                case 'stenikspeedy_speedy':
                case PFG_Speedy_Model_Carrier_Speedy::SPEEDY_TO_DOOR:
                case PFG_Speedy_Model_Carrier_Speedy::SPEEDY_TO_OFFICE:
                    $data['CouriersCode'] = 2; // 2 = Доставка със Спиди
                    break;
                case 'extensa_econt_econt_office':
                case 'extensa_econt_econt_door':
                default:
                    $data['CouriersCode'] = 1; // 1 = Доставка с Еконт
                    break;
            }

            /* Invoice */

            if ((int)$order->getCustomerInvoice()) {
                switch ($order->getCustomerInvoiceType()) {
                    case Stenik_CustomerInvoiceFields_Model_Entity_Attribute_Source_InvoiceType::VALUE_PERSONAL:
                        $data['TaxNumber'] = $order->getCustomerInvoicePersonalPin();
                        $data['VATNumber'] = null;
                        $data['DocPartnerName'] = $order->getCustomerInvoicePersonalName();
                        $data['ContactPerson'] = $order->getCustomerInvoicePersonalName();
                        $data['InvoiceAddress'] = trim(trim(sprintf('%s, %s',
                            $order->getCustomerInvoicePersonalCity(),
                            $order->getCustomerInvoicePersonalAddr()
                        ), ','));
                        $data['InvoiceRequired'] = 1;

                        break;
                    case Stenik_CustomerInvoiceFields_Model_Entity_Attribute_Source_InvoiceType::VALUE_COMPANY:
                        $data['TaxNumber'] = $order->getCustomerInvoiceCompanyUrn();
                        $data['VATNumber'] = $order->getCustomerInvoiceCompanyUrn() != $order->getCustomerInvoiceCompanyVat() ?
                            $order->getCustomerInvoiceCompanyVat() :
                            null;
                        $data['DocPartnerName'] = $order->getCustomerInvoiceCompanyName();
                        $data['ContactPerson'] = $order->getCustomerInvoiceCompanyPic();
                        $data['InvoiceAddress'] = trim(trim(sprintf('%s, %s',
                            $order->getCustomerInvoiceCompanyCity(),
                            $order->getCustomerInvoiceCompanyAddr()
                        ), ','));
                        $data['InvoiceRequired'] = 1;
                        break;
                }

            }

            /* Order items */

            $data['OrderLines'] = ['Row' => []];

            $gt = $order->getBaseGrandTotal();
            $sh = $order->getBaseShippingAmount();
            $dis = -$order->getDiscountAmount();
            $discount1 = round(($dis / ($gt + $dis - $sh)) * 100, 7); // % Discount

            $lineCounter = 0;
            foreach ($order->getAllVisibleItems() as $orderItem) {
                $data['OrderLines']['Row'][] = [
                    'WEBDocID'    => $order->getIncrementId(),
                    'LineID'      => ++$lineCounter,
                    'QttySplitID' => $orderItem->getSku(),
                    'OrigQtty'    => (float)$orderItem->getQtyOrdered(),
                    'OrigPrice'   => (float)$orderItem->getBasePriceInclTax(),
                    'Discount1'   => $discount1,
                    'Discount2'   => 0,
                ];
            }

            /* Shipping Amount as order item */
            if ($deliveryItemId = Mage::helper('stenik_zeron/order')->getOrderSendDeliveryItemQttySplitId()) {
                $data['OrderLines']['Row'][] = [
                    'WEBDocID'    => $order->getIncrementId(),
                    'LineID'      => ++$lineCounter,
                    'QttySplitID' => $deliveryItemId,
                    'OrigQtty'    => 1,
                    'OrigPrice'   => (float)$order->getBaseShippingAmount(),
                    'Discount1'   => 0,
                    'Discount2'   => 0,
                ];
            }


            $result = $this->getResource()->send($data);

            $infoMessage = sprintf('Order was sent to Zeron. Magento ID: %s, Magento IID: %s, Zeron DocNo: %s, Zeron TaskEntryID: %s, Zeron PartnerCode: %s.',
                $order->getId(),
                $order->getIncrementId(),
                $result->getDocNo(),
                $result->getTaskEntryId(),
                $result->getPartnerCode()
            );

            $order->addStatusHistoryComment($infoMessage);
            $order->setStenikZeronProcessStatus(self::PROCESS_STATUS_SENT);
            $order->setStenikZeronDocNo($result->getDocNo());
            $order->setStenikZeronTaskEntryId($result->getTaskEntryId());
            $order->setStenikZeronPartnerCode($result->getPartnerCode());
            $order->save();

            Mage::helper('stenik_logger')->log(
                'stenik_zeron_order_send_success',
                $infoMessage,
                sprintf('Order Send: Successfully sent order #%s', $order->getIncrementId()),
                Stenik_Logger_Helper_Data::SEVERITY_INFO,
                ['sent_data' => $data]
            );

            return $result;
        } catch (Exception $e) {
            Mage::helper('stenik_logger')->log(
                'stenik_zeron_order_send_error',
                $infoMessage,
                sprintf('Order Send: Error occurred while sending order #%s', $order->getIncrementId()),
                Stenik_Logger_Helper_Data::SEVERITY_CRIT,
                array_merge(['sent_data' => $data], $this->getResource()->getLastMethodCallDebugInfo())
            );

            $order->addStatusHistoryComment('Error occurred while sending the order to Zeron.');
            $order->setStenikZeronProcessStatus(self::PROCESS_STATUS_ERROR);
            $order->save();
        }

        return false;
    }

    private function _getComment(Mage_Sales_Model_Order $order): string
    {
        $comment = '';
        $shipping = $order->getShippingMethod();
        if (strpos($shipping, 'carco_internaltransport') !== false) {
            $comment = Mage::getStoreConfig('carriers/carco_internaltransport/send_to_zeron_message');
        } elseif (strpos($shipping, 'pfg_partner_shipping') !== false) {
            $comment = Mage::getStoreConfig('carriers/pfg_partner_shipping/send_to_zeron_message');
        }

        if ($order->getCustomerNote()) {
            if ($comment) {
                $comment = $comment . ' - ';
            }

            $comment = $comment . $order->getCustomerNote();
        }


        return $comment;
    }
}
