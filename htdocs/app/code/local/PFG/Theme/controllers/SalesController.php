<?php

class PFG_Theme_SalesController extends PFG_Theme_Controller_AbstractController
{
    public function applyCouponCodeAction()
    {
        try {
            $collectTotalsRequest = $this->getBody();
            $quote = $this->_getQuoteByUUID($collectTotalsRequest['cartId']);
            $couponCode = $collectTotalsRequest['promoCode'] ?? '';

            $this->_recollectTotals($quote, $collectTotalsRequest);

            $quote = $this->_getQuoteByUUID($collectTotalsRequest['cartId']);
            $couponCode = $couponCode === '_pfg_remove_coupon' ? '' : $couponCode;

            if ($quote->getCouponCode() != $couponCode) {
                // get role from promo code
                if (!$this->promoIsAllowedForSpecialPrice($couponCode)) {
                    foreach ($quote->getAllVisibleItems() as $item) {
                        if ($this->itemIsDiscounted($item)) {
                            throw new Exception(
                                'промокодът не може да се прилага за части на промоция'
                            );
                        }
                    }
                }

                throw new Exception('Невалиден промо код');
            }

            $this->_sendOk();
        } catch (Exception $e) {
            $this->_handleException($e);
        }
    }

    private function promoIsAllowedForSpecialPrice(string $promoCode): bool
    {
        /** @var Mage_SalesRule_Model_Rule $rule */
        $coupon = Mage::getModel('salesrule/coupon')->load($promoCode, 'code');
        if ($coupon->getId()) {
            /** @var Mage_SalesRule_Model_Rule $rule */
            $rule = Mage::getModel('salesrule/rule')->load($coupon->getRuleId());
            if ($rule->getId() && $rule->getIsActive()) {
                $conditions = $rule->getConditionsSerialized();
                if (strpos($conditions, '"special_price";s:8:"operator";s:2:">')) {
                    return false;
                }

                $actions = $rule->getActionsSerialized();
                if (strpos($actions, '"special_price";s:8:"operator";s:2:">')) {
                    return false;
                }
            }
        }

        return true;
    }

    private function itemIsDiscounted(Mage_Sales_Model_Quote_Item $item): bool
    {
        $product = $item->getProduct();

        return $product->getSpecialPrice() > 0
            && $product->getSpecialPrice() != $product->getPrice();
    }

    public function collectTotalsAction(): void
    {
        try {
            $collectTotalsRequest = $this->getBody();

            // Debug logging
            Mage::log('CollectTotals request data: ' . json_encode($collectTotalsRequest), null, 'graph_api_debug.log', true);

            $quote = $this->_getQuoteByUUID($collectTotalsRequest['cartId']);
            Mage::log('Quote loaded successfully, ID: ' . $quote->getId(), null, 'graph_api_debug.log', true);

            $this->_recollectTotals($quote, $collectTotalsRequest);
            Mage::log('Totals recollected successfully', null, 'graph_api_debug.log', true);

            $this->_sendOk();
        } catch (Exception $e) {
            Mage::log('CollectTotals error: ' . $e->getMessage(), null, 'graph_api_debug.log', true);
            $this->_handleException($e);
        }
    }

    public function calculateShippingAction(): void
    {
        try {
            $shippingRequest = $this->getBody();

            $quote = $this->_getQuoteByUUID($shippingRequest['cartId']);
            $carrier = PFG_Theme_Model_Checkout_ShippingRequest::getShippingCarrier($shippingRequest['shipping']['shippingMethodCode']);
            $method = PFG_Theme_Model_Checkout_ShippingRequest::getCarrierShippingMethod($shippingRequest['shipping']['shippingMethodCode']);

            $shippingRequest = PFG_Theme_Model_Checkout_ShippingRequest::init($quote, $shippingRequest['shipping']);
            $shippingRequest->save();

            $this->_helper()->setCustomShippingRequest($shippingRequest);

            $result = $carrier->collectRates($this->_getShippingRequest($quote, $shippingRequest));

            $this->_logEcontErrors();
            foreach ($result->getAllRates() as $rate) {
                if ($rate->getMethod() === $method) {
                    $this->_sendOk([
                        'label'    => $rate->getMethodTitle(),
                        'price'    => $rate->getPrice(),
                        'currency' => $quote->getQuoteCurrencyCode(),
                    ]);
                    return;
                }
            }

            throw new Exception('Няма намерена цена за избрания метод на доставка');
        } catch (Exception $e) {
            $this->_handleException($e);
        }
    }

    public function createNewOrderAction()
    {
        try {
            $collectTotalsRequest = $this->getBody();

            $quote = $this->_getQuoteByUUID($collectTotalsRequest['cartId']);
            $this->_recollectTotals($quote, $collectTotalsRequest);

            /** @var Mage_Sales_Model_Service_Quote $service */
            $service = Mage::getModel('sales/service_quote', $quote);
            $service->submitAll();

            $this->_logEcontErrors();
            $order = $service->getOrder();

            $redirectUrl = $quote->getPayment()->getOrderPlaceRedirectUrl();
            if (!$redirectUrl && $order->getCanSendNewEmailFlag()) {
                try {
                    $order->queueNewOrderEmail();
                } catch (Exception $e) {
                    $this->_log($e);
                }
            } else if ($redirectUrl) {
                // Check which payment method is being used and call the appropriate helper
                $paymentMethodCode = $order->getPayment()->getMethod();

                if ($paymentMethodCode === 'pfg_borica') {
                    $helper = Mage::helper('pfg_borica/borica');
                    $salesRequest = $helper->getSalesRequest($order);
                    if (!is_object($salesRequest)) {
                        Mage::throwException($this->__('Could not generate sales request. Please contact administrator.'));
                    }
                    $postData = $salesRequest->toPostData();
                } elseif ($paymentMethodCode === 'pfg_mypos') {
                    // Process MyPOS payment and get redirect data
                    $myposHelper = Mage::helper('pfg_mypos/mypos');
                    $myposResult = $myposHelper->processPurchase($order);

                    if (!$myposResult['success']) {
                        Mage::throwException($this->__('MyPOS payment processing failed: %s', $myposResult['error']));
                    }

                    // Override redirect URL and data for MyPOS
                    $redirectUrl = $myposResult['redirect_url'];
                    $postData = $myposResult['form_data'];
                } else {
                    // For other payment methods, no additional data needed
                    $postData = array();
                }

                // Determine the correct redirect URL based on payment method
                $finalRedirectUrl = $redirectUrl;
                if ($paymentMethodCode === 'pfg_borica') {
                    $finalRedirectUrl = $helper->getApiUrl();
                }

                $this->_sendOk([
                    'orderId'       => $order->getEntityId(),
                    'incrementId'   => $order->getIncrementId(),
                    'redirect_url'  => $finalRedirectUrl,
                    'redirect_data' => $postData,
                ]);

                return;
            }

            $this->_sendOk([
                'orderId'     => $order->getEntityId(),
                'incrementId' => $order->getIncrementId(),
            ]);
        } catch (Exception $e) {
            $this->_handleException($e);
        }
    }

    /**
     * @param Mage_Sales_Model_Quote $quote
     * @param array $addressData
     * @return void
     * @throws Exception
     */
    protected function _recollectTotals(Mage_Sales_Model_Quote $quote, array $addressData = []): void
    {
        Mage::log('Starting _recollectTotals for quote ID: ' . $quote->getId(), null, 'graph_api_debug.log', true);
        Mage::log('Address data: ' . json_encode($addressData), null, 'graph_api_debug.log', true);

        $shippingRequest = PFG_Theme_Model_Checkout_ShippingRequest::init($quote, $addressData['shipping'] ?? []);
        Mage::log('ShippingRequest initialized', null, 'graph_api_debug.log', true);

        $shippingRequest->save();
        Mage::log('ShippingRequest saved', null, 'graph_api_debug.log', true);

        $this->_helper()->setCustomShippingRequest($shippingRequest);

        if ($shippingRequest->hasShippingMethod()) {
            foreach ($quote->getAddressesCollection() as $address) {
                $shippingRequest->updateQuoteAddress($address);
            }
        }

        if (!empty($addressData['invoice'])) {
            $invoiceData = $addressData['invoice'];
            $quote->addData($invoiceData);
        }

        Mage::log('Setting totals collection flag and clearing cache', null, 'graph_api_debug.log', true);
        $quote->setTotalsCollectedFlag(false);
        $quote->getShippingAddress()
            ->unsetData('cached_items_all')
            ->unsetData('cached_items_nominal')
            ->unsetData('cached_items_nonnominal')
            ->setCollectShippingRates(true);

        if (!empty($addressData['promoCode'])) {
            $couponCode = $addressData['promoCode'];
            if ($couponCode === '_pfg_remove_coupon') {
                $couponCode = '';
            } else {
                /** @var Mage_SalesRule_Model_Coupon $oCoupon */
                $oCoupon = Mage::getModel('salesrule/coupon')->load(trim($couponCode), 'code');
                if ($oCoupon->getRuleId() < 1) {
                    throw new Exception('Невалиден промо код');
                }
            }

            $quote->setCouponCode($couponCode);
        }

        $paymentMethod = $addressData['paymentMethod'] ?? null;
        if ($paymentMethod) {
            // if $paymentMethod is on  of 'cashOnDelivery' | 'creditCard' | 'bankTransfer' | 'mypos' | ''
            if ($paymentMethod === 'cashOnDelivery') {
                $paymentMethod = 'extensa_econt';
            } elseif ($paymentMethod === 'creditCard') {
                $paymentMethod = 'pfg_borica';
            } elseif ($paymentMethod === 'bankTransfer') {
                $paymentMethod = 'banktransfer';
            } elseif ($paymentMethod === 'mypos') {
                $paymentMethod = 'pfg_mypos';
            } else {
                throw new Exception('Невалиден метод на плащане');
            }

            if (!Mage::getStoreConfigFlag('payment/' . $paymentMethod . '/active')) {
                throw new Exception('Неактивен метод на плащане');
            }

            $quote->getPayment()->setMethod($paymentMethod);
        }

        if (!empty($addressData['customerNote'])) {
            // add customer note to quote
            $quote->setCustomerNote($addressData['customerNote']);
        }

        Mage::log('Collecting totals and saving quote', null, 'graph_api_debug.log', true);
        $quote->collectTotals();
        $quote->save();
        Mage::log('Quote saved successfully', null, 'graph_api_debug.log', true);
    }

    protected function _getShippingRequest(Mage_Sales_Model_Quote $quote, PFG_Theme_Model_Checkout_ShippingRequest $shippingRequest): Mage_Shipping_Model_Rate_Request
    {
        $address = $quote->getShippingAddress();
        $store = $quote->getStore();
        $request = Mage::getModel('shipping/rate_request');
        $request->setAllItems($quote->getAllItems());
        $request->setDestCountryId("BG");
        $request->setDestPostcode($shippingRequest->getPostCode() ?? '1000');
        $request->setPackageValue($address->getBaseSubtotal());
        $request->setPackageValueWithDiscount($address->getBaseSubtotalWithDiscount());
        $request->setPackageWeight($address->getWeight());
        $request->setPackageQty($address->getItemQty());
        $request->setStoreId($store->getId());
        $request->setWebsiteId($store->getWebsiteId());
        $request->setBaseCurrency($store->getBaseCurrency());
        $request->setPackageCurrency($store->getCurrentCurrency());

        return $request;
    }

    private function _helper(): PFG_Theme_Helper_Checkout_Shipping
    {
        return Mage::helper('pfg_theme/checkout_shipping');
    }

    public function _logEcontErrors(): void
    {
        $errorInfo = Mage::registry('theme_extensa_econt_error');

        if ($errorInfo) {
            Mage::log($errorInfo, null, 'pfg_theme_econt_recalculation.log');
        }
    }
}
