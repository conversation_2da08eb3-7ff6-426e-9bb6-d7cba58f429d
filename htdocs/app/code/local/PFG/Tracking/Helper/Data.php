<?php

class PFG_Tracking_Helper_Data extends Mage_Core_Helper_Abstract
{
    /**
     * Placeholder tracking helper
     * This helper was created to fix the missing dependency in the cookie consent template
     */
    
    /**
     * Check if tracking is enabled
     *
     * @return bool
     */
    public function isTrackingEnabled()
    {
        return true;
    }
    
    /**
     * Get tracking configuration
     *
     * @return array
     */
    public function getTrackingConfig()
    {
        return array();
    }
    
    /**
     * Log tracking event
     *
     * @param string $event
     * @param array $data
     */
    public function logEvent($event, $data = array())
    {
        // Placeholder method
    }
}
