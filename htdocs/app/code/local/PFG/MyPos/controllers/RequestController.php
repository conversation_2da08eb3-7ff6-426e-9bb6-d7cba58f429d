<?php

class PFG_MyPos_RequestController extends Mage_Core_Controller_Front_Action
{
    /**
     * Redirect to MyPOS payment page
     */
    public function redirectAction()
    {
        try {
            $session = Mage::getSingleton('checkout/session');
            $order = Mage::getModel('sales/order')->loadByIncrementId($session->getLastRealOrderId());
            
            if (!$order->getId()) {
                Mage::getSingleton('core/session')->addError($this->__('Order not found.'));
                $this->_redirect('checkout/cart');
                return;
            }
            
            // Check if payment method is MyPOS
            if ($order->getPayment()->getMethodInstance()->getCode() !== PFG_MyPos_Model_Payment_MyPos::PAYMENT_METHOD_CODE) {
                Mage::getSingleton('core/session')->addError($this->__('Invalid payment method.'));
                $this->_redirect('checkout/cart');
                return;
            }
            
            $helper = Mage::helper('pfg_mypos/mypos');
            $result = $helper->processPurchase($order);
            
            if (!$result['success']) {
                Mage::getSingleton('core/session')->addError($this->__('Payment processing failed: %s', $result['error']));
                $this->_redirect('checkout/cart');
                return;
            }
            
            // If we reach here, the user should have been redirected to MyPOS
            // This is a fallback in case the redirect didn't work
            Mage::getSingleton('core/session')->addError($this->__('Payment redirect failed. Please try again.'));
            $this->_redirect('checkout/cart');
            
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
            Mage::getSingleton('core/session')->addError($this->__('An error occurred during payment processing.'));
            $this->_redirect('checkout/cart');
        }
    }

    /**
     * Test action for development
     */
    public function testAction()
    {
        if (!Mage::getIsDeveloperMode()) {
            $this->norouteAction();
            return;
        }
        
        echo "MyPOS Test Action - Module is working!";
        exit;
    }
}
