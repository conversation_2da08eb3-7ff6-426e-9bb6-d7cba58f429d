<?php

class PFG_MyPos_Theme_ApiController extends Mage_Core_Controller_Front_Action
{
    /**
     * Handle order creation for MyPOS payment method
     * This is called by the GraphQL service via theme_api route
     */
    public function salesCreateNewOrderAction()
    {
        try {
            // Verify API authentication
            if (!$this->_validateApiAuth()) {
                $this->_returnError('Unauthorized', 401);
                return;
            }

            $request = $this->getRequest();
            if (!$request->isPost()) {
                $this->_returnError('Method not allowed', 405);
                return;
            }

            // Get JSON data
            $jsonData = json_decode($request->getRawBody(), true);
            if (!$jsonData) {
                $this->_returnError('Invalid JSON data', 400);
                return;
            }

            // Check if this is a MyPOS payment
            if (!isset($jsonData['paymentMethod']) || $jsonData['paymentMethod'] !== 'mypos') {
                // Let other controllers handle non-MyPOS payments
                $this->norouteAction();
                return;
            }

            // Process MyPOS order
            $result = $this->_processMyPosOrder($jsonData);
            $this->_returnSuccess($result);

        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
            $this->_returnError('Internal server error: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Process MyPOS order creation
     *
     * @param array $data
     * @return array
     */
    protected function _processMyPosOrder(array $data)
    {
        $helper = Mage::helper('pfg_mypos');
        $myposHelper = Mage::helper('pfg_mypos/mypos');

        // Validate required fields
        $requiredFields = ['cartId', 'paymentMethod', 'shipping'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                throw new Exception("Missing required field: {$field}");
            }
        }

        // Get quote
        $quote = Mage::getModel('sales/quote')->load($data['cartId']);
        if (!$quote->getId()) {
            throw new Exception('Quote not found');
        }

        // Set customer information
        $customer = $data['shipping']['customer'];
        $quote->setCustomerEmail($customer['email']);
        $quote->setCustomerFirstname($customer['firstname']);
        $quote->setCustomerLastname($customer['lastname']);
        $quote->setCustomerIsGuest(true);

        // Set addresses
        $this->_setQuoteAddresses($quote, $data['shipping']);

        // Set payment method
        $payment = $quote->getPayment();
        $payment->setMethod(PFG_MyPos_Model_Payment_MyPos::PAYMENT_METHOD_CODE);

        // Set shipping method if provided
        if (isset($data['shipping']['shippingMethodCode'])) {
            $shippingMethod = $this->_mapShippingMethod($data['shipping']['shippingMethodCode']);
            $quote->getShippingAddress()->setShippingMethod($shippingMethod);
        }

        // Apply coupon if provided
        if (!empty($data['promoCode'])) {
            $quote->setCouponCode($data['promoCode']);
        }

        // Collect totals
        $quote->collectTotals();
        $quote->save();

        // Create order
        $service = Mage::getModel('sales/service_quote', $quote);
        $service->submitAll();
        $order = $service->getOrder();

        if (!$order->getId()) {
            throw new Exception('Failed to create order');
        }

        // Add order comment if provided
        if (!empty($data['customerNote'])) {
            $order->addStatusHistoryComment('Customer note: ' . $data['customerNote']);
        }

        // Process invoice data if provided
        if (isset($data['invoice'])) {
            $this->_processInvoiceData($order, $data['invoice']);
        }

        $order->save();

        // Generate MyPOS order ID for tracking
        $myposOrderId = $helper->generateMyPosOrderId($order);

        // Save transaction record
        $myposHelper->saveTransaction($order, $myposOrderId, 'pending');

        // Return order data with redirect information
        return array(
            'orderId' => $order->getId(),
            'incrementId' => $order->getIncrementId(),
            'status' => $order->getStatus(),
            'redirect_url' => Mage::getUrl('pfg_mypos/request/redirect', array('_secure' => true)),
            'redirect_data' => array(
                'order_id' => $order->getIncrementId(),
                'mypos_order_id' => $myposOrderId,
                'method' => 'GET',
                'auto_redirect' => true
            )
        );
    }

    /**
     * Set quote addresses from shipping data
     *
     * @param Mage_Sales_Model_Quote $quote
     * @param array $shippingData
     */
    protected function _setQuoteAddresses(Mage_Sales_Model_Quote $quote, array $shippingData)
    {
        $customer = $shippingData['customer'];
        $address = $shippingData['address'];

        // Set billing address
        $billingAddress = $quote->getBillingAddress();
        $billingAddress->setFirstname($customer['firstname']);
        $billingAddress->setLastname($customer['lastname']);
        $billingAddress->setEmail($customer['email']);
        $billingAddress->setTelephone($customer['phone']);
        
        if (isset($address['street'])) {
            $billingAddress->setStreet($address['street']);
        }
        if (isset($address['postCode'])) {
            $billingAddress->setPostcode($address['postCode']);
        }
        
        // Set default country
        $billingAddress->setCountryId('BG');
        $billingAddress->setCity('Sofia'); // Default city

        // Set shipping address (same as billing for now)
        $shippingAddress = $quote->getShippingAddress();
        $shippingAddress->setFirstname($customer['firstname']);
        $shippingAddress->setLastname($customer['lastname']);
        $shippingAddress->setEmail($customer['email']);
        $shippingAddress->setTelephone($customer['phone']);
        
        if (isset($address['street'])) {
            $shippingAddress->setStreet($address['street']);
        }
        if (isset($address['postCode'])) {
            $shippingAddress->setPostcode($address['postCode']);
        }
        
        $shippingAddress->setCountryId('BG');
        $shippingAddress->setCity('Sofia'); // Default city
    }

    /**
     * Map shipping method code
     *
     * @param string $methodCode
     * @return string
     */
    protected function _mapShippingMethod($methodCode)
    {
        $mapping = array(
            'ECONT_TO_OFFICE' => 'econt_econt',
            'ECONT_TO_DOOR' => 'econt_door',
            'SPEEDY_TO_OFFICE' => 'speedy_office',
            'SPEEDY_TO_DOOR' => 'speedy_door',
            'OWN_TRANSPORT' => 'own_transport',
            'PARTNER_PICKUP' => 'partner_pickup',
            'CARCO_TRANSPORT' => 'carco_transport'
        );

        return isset($mapping[$methodCode]) ? $mapping[$methodCode] : 'flatrate_flatrate';
    }

    /**
     * Process invoice data
     *
     * @param Mage_Sales_Model_Order $order
     * @param array $invoiceData
     */
    protected function _processInvoiceData(Mage_Sales_Model_Order $order, array $invoiceData)
    {
        // Add invoice information to order comments
        $invoiceInfo = array();
        
        if (!empty($invoiceData['company'])) {
            $invoiceInfo[] = 'Company: ' . $invoiceData['company'];
        }
        if (!empty($invoiceData['eik'])) {
            $invoiceInfo[] = 'EIK: ' . $invoiceData['eik'];
        }
        if (!empty($invoiceData['address'])) {
            $invoiceInfo[] = 'Address: ' . $invoiceData['address'];
        }

        if (!empty($invoiceInfo)) {
            $order->addStatusHistoryComment('Invoice data: ' . implode(', ', $invoiceInfo));
        }
    }

    /**
     * Validate API authentication
     *
     * @return bool
     */
    protected function _validateApiAuth()
    {
        $authHeader = $this->getRequest()->getHeader('PFG-Auth-Service-Token');
        
        // For development, allow requests without token
        if (Mage::getIsDeveloperMode()) {
            return true;
        }
        
        $expectedToken = Mage::getStoreConfig('pfg_mypos/api/auth_token');
        return !empty($authHeader) && !empty($expectedToken) && $authHeader === $expectedToken;
    }

    /**
     * Return success response
     *
     * @param array $data
     */
    protected function _returnSuccess(array $data)
    {
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(json_encode($data));
    }

    /**
     * Return error response
     *
     * @param string $message
     * @param int $code
     */
    protected function _returnError($message, $code = 400)
    {
        $this->getResponse()
            ->setHttpResponseCode($code)
            ->setHeader('Content-Type', 'application/json')
            ->setBody(json_encode(array(
                'error' => $message,
                'success' => false
            )));
    }
}
