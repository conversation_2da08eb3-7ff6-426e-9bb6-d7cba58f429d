<?php

class PFG_MyPos_Model_Payment_MyPos extends Mage_Payment_Model_Method_Abstract
{
    const PAYMENT_METHOD_CODE = 'pfg_mypos';

    protected $_code = self::PAYMENT_METHOD_CODE;

    protected $_isGateway = true;
    protected $_canCapture = true;
    protected $_canRefund = true;
    protected $_canRefundInvoicePartial = true;
    protected $_canVoid = false;
    protected $_canUseInternal = false;
    protected $_canUseCheckout = true;
    protected $_canUseForMultishipping = false;
    protected $_isInitializeNeeded = false;
    protected $_canFetchTransactionInfo = true;
    protected $_canReviewPayment = false;
    protected $_canCreateBillingAgreement = false;
    protected $_canManageRecurringProfiles = false;

    protected $_formBlockType = 'pfg_mypos/checkout_form';
    protected $_infoBlockType = 'pfg_mypos/info';

    /**
     * Get order place redirect URL
     *
     * @return string
     */
    public function getOrderPlaceRedirectUrl()
    {
        return Mage::getUrl('pfg_mypos/request/redirect', array('_secure' => true));
    }

    /**
     * Get instructions text from config
     *
     * @return string
     */
    public function getInstructions()
    {
        return trim($this->getConfigData('instructions'));
    }

    /**
     * Check whether payment method can be used
     *
     * @param Mage_Sales_Model_Quote|null $quote
     * @return bool
     */
    public function isAvailable($quote = null)
    {
        if (!parent::isAvailable($quote)) {
            return false;
        }

        if (!$this->getConfigData('merchant_id') || !$this->getConfigData('wallet_number')) {
            return false;
        }

        // Check if we have either configuration package or manual keys
        $hasConfigPackage = !empty($this->getConfigData('configuration_package'));
        $hasManualKeys = !empty($this->getConfigData('private_key')) && !empty($this->getConfigData('api_public_key'));

        if (!$hasConfigPackage && !$hasManualKeys) {
            return false;
        }

        return true;
    }

    /**
     * Capture payment
     *
     * @param Varien_Object $payment
     * @param float $amount
     * @return $this
     */
    public function capture(Varien_Object $payment, $amount)
    {
        if (!$this->canCapture()) {
            Mage::throwException(Mage::helper('payment')->__('Capture action is not available.'));
        }

        $order = $payment->getOrder();
        $transactionId = $payment->getParentTransactionId() ?: $payment->getTransactionId();

        if (!$transactionId) {
            Mage::throwException(Mage::helper('pfg_mypos')->__('Transaction ID is required for capture.'));
        }

        // For MyPOS, capture is usually automatic, but we can implement manual capture if needed
        $payment->setTransactionId($transactionId . '-capture')
                ->setIsTransactionClosed(true);

        return $this;
    }

    /**
     * Refund payment
     *
     * @param Varien_Object $payment
     * @param float $amount
     * @return $this
     */
    public function refund(Varien_Object $payment, $amount)
    {
        if (!$this->canRefund()) {
            Mage::throwException(Mage::helper('payment')->__('Refund action is not available.'));
        }

        $order = $payment->getOrder();
        $transactionId = $payment->getParentTransactionId() ?: $payment->getTransactionId();

        if (!$transactionId) {
            Mage::throwException(Mage::helper('pfg_mypos')->__('Transaction ID is required for refund.'));
        }

        try {
            $helper = Mage::helper('pfg_mypos/mypos');
            $result = $helper->processRefund($order, $amount, $transactionId);

            if ($result['success']) {
                $payment->setTransactionId($result['refund_transaction_id'])
                        ->setIsTransactionClosed(true);
            } else {
                Mage::throwException($result['error_message']);
            }
        } catch (Exception $e) {
            Mage::logException($e);
            Mage::throwException(Mage::helper('pfg_mypos')->__('Refund failed: %s', $e->getMessage()));
        }

        return $this;
    }

    /**
     * Validate payment method information object
     *
     * @return $this
     */
    public function validate()
    {
        parent::validate();
        return $this;
    }

    /**
     * Get MyPOS configuration
     *
     * @return array
     */
    public function getMyPosConfig()
    {
        return array(
            'merchant_id' => $this->getConfigData('merchant_id'),
            'wallet_number' => $this->getConfigData('wallet_number'),
            'key_index' => $this->getConfigData('key_index') ?: 1,
            'configuration_package' => $this->getConfigData('configuration_package'),
            'private_key' => $this->getConfigData('private_key'),
            'api_public_key' => $this->getConfigData('api_public_key'),
            'sandbox_mode' => $this->getConfigData('sandbox_mode'),
            'currency' => $this->getConfigData('currency') ?: 'BGN',
        );
    }

    /**
     * Get checkout session
     *
     * @return Mage_Checkout_Model_Session
     */
    public function getCheckout()
    {
        return Mage::getSingleton('checkout/session');
    }

    /**
     * Get current quote
     *
     * @return Mage_Sales_Model_Quote
     */
    public function getQuote()
    {
        return $this->getCheckout()->getQuote();
    }
}
