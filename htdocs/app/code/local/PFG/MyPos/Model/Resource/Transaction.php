<?php

class PFG_MyPos_Model_Resource_Transaction extends Mage_Core_Model_Resource_Db_Abstract
{
    protected function _construct()
    {
        $this->_init('pfg_mypos/transaction', 'id');
    }

    /**
     * Load transaction by MyPOS order ID
     *
     * @param PFG_MyPos_Model_Transaction $object
     * @param string $myposOrderId
     * @return $this
     */
    public function loadByMyPosOrderId(PFG_MyPos_Model_Transaction $object, $myposOrderId)
    {
        $adapter = $this->_getReadAdapter();
        $select = $adapter->select()
            ->from($this->getMainTable())
            ->where('mypos_order_id = ?', $myposOrderId)
            ->order('created_at DESC')
            ->limit(1);

        $data = $adapter->fetchRow($select);
        if ($data) {
            $object->setData($data);
        }

        $this->_afterLoad($object);
        return $this;
    }

    /**
     * Load transaction by order ID
     *
     * @param PFG_MyPos_Model_Transaction $object
     * @param int $orderId
     * @return $this
     */
    public function loadByOrderId(PFG_MyPos_Model_Transaction $object, $orderId)
    {
        $adapter = $this->_getReadAdapter();
        $select = $adapter->select()
            ->from($this->getMainTable())
            ->where('order_id = ?', $orderId)
            ->order('created_at DESC')
            ->limit(1);

        $data = $adapter->fetchRow($select);
        if ($data) {
            $object->setData($data);
        }

        $this->_afterLoad($object);
        return $this;
    }

    /**
     * Get transactions by order ID
     *
     * @param int $orderId
     * @return array
     */
    public function getTransactionsByOrderId($orderId)
    {
        $adapter = $this->_getReadAdapter();
        $select = $adapter->select()
            ->from($this->getMainTable())
            ->where('order_id = ?', $orderId)
            ->order('created_at DESC');

        return $adapter->fetchAll($select);
    }

    /**
     * Get transactions by status
     *
     * @param string $status
     * @param int $limit
     * @return array
     */
    public function getTransactionsByStatus($status, $limit = null)
    {
        $adapter = $this->_getReadAdapter();
        $select = $adapter->select()
            ->from($this->getMainTable())
            ->where('status = ?', $status)
            ->order('created_at DESC');

        if ($limit) {
            $select->limit($limit);
        }

        return $adapter->fetchAll($select);
    }

    /**
     * Get pending transactions older than specified time
     *
     * @param int $minutes
     * @return array
     */
    public function getPendingTransactionsOlderThan($minutes = 30)
    {
        $adapter = $this->_getReadAdapter();
        $cutoffTime = date('Y-m-d H:i:s', time() - ($minutes * 60));
        
        $select = $adapter->select()
            ->from($this->getMainTable())
            ->where('status = ?', 'pending')
            ->where('created_at < ?', $cutoffTime)
            ->order('created_at ASC');

        return $adapter->fetchAll($select);
    }

    /**
     * Update transaction status
     *
     * @param int $transactionId
     * @param string $status
     * @param array $responseData
     * @return int
     */
    public function updateTransactionStatus($transactionId, $status, $responseData = array())
    {
        $adapter = $this->_getWriteAdapter();
        $data = array(
            'status' => $status,
            'updated_at' => now()
        );

        if (!empty($responseData)) {
            $data['response_data'] = json_encode($responseData);
        }

        return $adapter->update(
            $this->getMainTable(),
            $data,
            array('id = ?' => $transactionId)
        );
    }

    /**
     * Clean old transactions
     *
     * @param int $days
     * @return int
     */
    public function cleanOldTransactions($days = 90)
    {
        $adapter = $this->_getWriteAdapter();
        $cutoffDate = date('Y-m-d H:i:s', time() - ($days * 24 * 60 * 60));

        return $adapter->delete(
            $this->getMainTable(),
            array('created_at < ?' => $cutoffDate)
        );
    }
}
