<?php

class PFG_MyPos_Model_Resource_Transaction_Collection extends Mage_Core_Model_Resource_Db_Collection_Abstract
{
    protected function _construct()
    {
        $this->_init('pfg_mypos/transaction');
    }

    /**
     * Filter by order ID
     *
     * @param int $orderId
     * @return $this
     */
    public function addOrderIdFilter($orderId)
    {
        $this->addFieldToFilter('order_id', $orderId);
        return $this;
    }

    /**
     * Filter by status
     *
     * @param string $status
     * @return $this
     */
    public function addStatusFilter($status)
    {
        $this->addFieldToFilter('status', $status);
        return $this;
    }

    /**
     * Filter by MyPOS order ID
     *
     * @param string $myposOrderId
     * @return $this
     */
    public function addMyPosOrderIdFilter($myposOrderId)
    {
        $this->addFieldToFilter('mypos_order_id', $myposOrderId);
        return $this;
    }

    /**
     * Filter by date range
     *
     * @param string $from
     * @param string $to
     * @return $this
     */
    public function addDateRangeFilter($from = null, $to = null)
    {
        if ($from) {
            $this->addFieldToFilter('created_at', array('gteq' => $from));
        }
        if ($to) {
            $this->addFieldToFilter('created_at', array('lteq' => $to));
        }
        return $this;
    }

    /**
     * Filter successful transactions
     *
     * @return $this
     */
    public function addSuccessfulFilter()
    {
        $this->addFieldToFilter('status', array('in' => array('00', 'success')));
        return $this;
    }

    /**
     * Filter pending transactions
     *
     * @return $this
     */
    public function addPendingFilter()
    {
        $this->addFieldToFilter('status', 'pending');
        return $this;
    }

    /**
     * Filter failed transactions
     *
     * @return $this
     */
    public function addFailedFilter()
    {
        $this->addFieldToFilter('status', array('nin' => array('00', 'success', 'pending')));
        return $this;
    }

    /**
     * Join with sales order table
     *
     * @return $this
     */
    public function joinOrderTable()
    {
        $this->getSelect()->join(
            array('order' => $this->getTable('sales/order')),
            'main_table.order_id = order.entity_id',
            array(
                'order_increment_id' => 'order.increment_id',
                'order_status' => 'order.status',
                'order_state' => 'order.state',
                'customer_email' => 'order.customer_email',
                'order_created_at' => 'order.created_at'
            )
        );
        return $this;
    }

    /**
     * Get transactions summary
     *
     * @return array
     */
    public function getTransactionsSummary()
    {
        $select = clone $this->getSelect();
        $select->reset(Zend_Db_Select::COLUMNS);
        $select->columns(array(
            'total_count' => 'COUNT(*)',
            'total_amount' => 'SUM(amount)',
            'successful_count' => 'SUM(CASE WHEN status IN ("00", "success") THEN 1 ELSE 0 END)',
            'successful_amount' => 'SUM(CASE WHEN status IN ("00", "success") THEN amount ELSE 0 END)',
            'pending_count' => 'SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END)',
            'failed_count' => 'SUM(CASE WHEN status NOT IN ("00", "success", "pending") THEN 1 ELSE 0 END)'
        ));

        return $this->getConnection()->fetchRow($select);
    }

    /**
     * Order by creation date descending
     *
     * @return $this
     */
    public function orderByCreatedAtDesc()
    {
        $this->setOrder('created_at', 'DESC');
        return $this;
    }

    /**
     * Order by creation date ascending
     *
     * @return $this
     */
    public function orderByCreatedAtAsc()
    {
        $this->setOrder('created_at', 'ASC');
        return $this;
    }
}
