<?php
/**
 * MyPOS HTTPS URL Backend Model
 * 
 * Validates that URLs start with https:// as required by MyPOS
 */
class PFG_MyPos_Model_System_Config_Backend_Httpsurl extends Mage_Core_Model_Config_Data
{
    /**
     * Validate that the URL starts with https://
     *
     * @return PFG_MyPos_Model_System_Config_Backend_Httpsurl
     * @throws Mage_Core_Exception
     */
    protected function _beforeSave()
    {
        $value = $this->getValue();
        
        if (!empty($value)) {
            // Check if URL starts with https://
            if (!preg_match('/^https:\/\//', $value)) {
                $fieldName = $this->getFieldConfig()->label;
                Mage::throwException(
                    Mage::helper('pfg_mypos')->__(
                        '%s must start with https:// as required by MyPOS security policy.',
                        $fieldName
                    )
                );
            }
            
            // Validate URL format
            if (!filter_var($value, FILTER_VALIDATE_URL)) {
                $fieldName = $this->getFieldConfig()->label;
                Mage::throwException(
                    Mage::helper('pfg_mypos')->__(
                        '%s must be a valid URL.',
                        $fieldName
                    )
                );
            }
        }
        
        return parent::_beforeSave();
    }
}
