<?php

class PFG_MyPos_Model_Transaction extends Mage_Core_Model_Abstract
{
    protected function _construct()
    {
        $this->_init('pfg_mypos/transaction');
    }

    /**
     * Get transaction by MyPOS order ID
     *
     * @param string $myposOrderId
     * @return $this
     */
    public function loadByMyPosOrderId($myposOrderId)
    {
        $this->_getResource()->loadByMyPosOrderId($this, $myposOrderId);
        return $this;
    }

    /**
     * Get transaction by order ID
     *
     * @param int $orderId
     * @return $this
     */
    public function loadByOrderId($orderId)
    {
        $this->_getResource()->loadByOrderId($this, $orderId);
        return $this;
    }

    /**
     * Get order associated with this transaction
     *
     * @return Mage_Sales_Model_Order
     */
    public function getOrder()
    {
        if (!$this->hasData('order')) {
            $order = Mage::getModel('sales/order')->load($this->getOrderId());
            $this->setData('order', $order);
        }
        return $this->getData('order');
    }

    /**
     * Get decoded request data
     *
     * @return array
     */
    public function getDecodedRequestData()
    {
        $data = $this->getRequestData();
        return $data ? json_decode($data, true) : array();
    }

    /**
     * Get decoded response data
     *
     * @return array
     */
    public function getDecodedResponseData()
    {
        $data = $this->getResponseData();
        return $data ? json_decode($data, true) : array();
    }

    /**
     * Set request data (will be JSON encoded)
     *
     * @param array $data
     * @return $this
     */
    public function setRequestDataArray(array $data)
    {
        $this->setRequestData(json_encode($data));
        return $this;
    }

    /**
     * Set response data (will be JSON encoded)
     *
     * @param array $data
     * @return $this
     */
    public function setResponseDataArray(array $data)
    {
        $this->setResponseData(json_encode($data));
        return $this;
    }

    /**
     * Check if transaction is successful
     *
     * @return bool
     */
    public function isSuccessful()
    {
        return $this->getStatus() === '00' || $this->getStatus() === 'success';
    }

    /**
     * Check if transaction is pending
     *
     * @return bool
     */
    public function isPending()
    {
        return $this->getStatus() === 'pending';
    }

    /**
     * Check if transaction is failed
     *
     * @return bool
     */
    public function isFailed()
    {
        return !$this->isSuccessful() && !$this->isPending();
    }

    /**
     * Get formatted amount
     *
     * @return string
     */
    public function getFormattedAmount()
    {
        $order = $this->getOrder();
        if ($order && $order->getId()) {
            return $order->formatPrice($this->getAmount());
        }
        return number_format($this->getAmount(), 2) . ' ' . $this->getCurrency();
    }

    /**
     * Get status label
     *
     * @return string
     */
    public function getStatusLabel()
    {
        switch ($this->getStatus()) {
            case '00':
            case 'success':
                return 'Successful';
            case 'pending':
                return 'Pending';
            case 'cancelled':
                return 'Cancelled';
            case 'failed':
                return 'Failed';
            default:
                return 'Unknown';
        }
    }

    /**
     * Before save processing
     *
     * @return $this
     */
    protected function _beforeSave()
    {
        parent::_beforeSave();
        
        if (!$this->getCreatedAt()) {
            $this->setCreatedAt(now());
        }
        $this->setUpdatedAt(now());
        
        return $this;
    }
}
