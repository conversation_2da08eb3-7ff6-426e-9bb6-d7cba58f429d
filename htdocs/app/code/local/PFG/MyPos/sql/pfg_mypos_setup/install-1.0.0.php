<?php

$installer = $this;
$installer->startSetup();

/**
 * Create table 'pfg_mypos_transactions'
 */
$table = $installer->getConnection()
    ->newTable($installer->getTable('pfg_mypos/transaction'))
    ->addColumn('id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
        'identity'  => true,
        'unsigned'  => true,
        'nullable'  => false,
        'primary'   => true,
    ), 'Transaction ID')
    ->addColumn('order_id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
        'unsigned'  => true,
        'nullable'  => false,
    ), 'Magento Order ID')
    ->addColumn('mypos_order_id', Varien_Db_Ddl_Table::TYPE_VARCHAR, 255, array(
        'nullable'  => false,
    ), 'MyPOS Order ID')
    ->addColumn('transaction_id', Varien_Db_Ddl_Table::TYPE_VARCHAR, 255, array(
        'nullable'  => true,
    ), 'MyPOS Transaction ID')
    ->addColumn('status', Varien_Db_Ddl_Table::TYPE_VARCHAR, 50, array(
        'nullable'  => false,
        'default'   => 'pending',
    ), 'Transaction Status')
    ->addColumn('amount', Varien_Db_Ddl_Table::TYPE_DECIMAL, '12,4', array(
        'nullable'  => false,
        'default'   => '0.0000',
    ), 'Transaction Amount')
    ->addColumn('currency', Varien_Db_Ddl_Table::TYPE_VARCHAR, 3, array(
        'nullable'  => false,
        'default'   => 'BGN',
    ), 'Transaction Currency')
    ->addColumn('request_data', Varien_Db_Ddl_Table::TYPE_TEXT, '64k', array(
        'nullable'  => true,
    ), 'Request Data (JSON)')
    ->addColumn('response_data', Varien_Db_Ddl_Table::TYPE_TEXT, '64k', array(
        'nullable'  => true,
    ), 'Response Data (JSON)')
    ->addColumn('created_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
        'nullable'  => false,
        'default'   => Varien_Db_Ddl_Table::TIMESTAMP_INIT,
    ), 'Created At')
    ->addColumn('updated_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
        'nullable'  => false,
        'default'   => Varien_Db_Ddl_Table::TIMESTAMP_INIT_UPDATE,
    ), 'Updated At')
    ->addIndex(
        $installer->getIdxName('pfg_mypos/transaction', array('order_id')),
        array('order_id')
    )
    ->addIndex(
        $installer->getIdxName('pfg_mypos/transaction', array('mypos_order_id')),
        array('mypos_order_id'),
        array('type' => Varien_Db_Adapter_Interface::INDEX_TYPE_UNIQUE)
    )
    ->addIndex(
        $installer->getIdxName('pfg_mypos/transaction', array('transaction_id')),
        array('transaction_id')
    )
    ->addIndex(
        $installer->getIdxName('pfg_mypos/transaction', array('status')),
        array('status')
    )
    ->addIndex(
        $installer->getIdxName('pfg_mypos/transaction', array('created_at')),
        array('created_at')
    )
    ->addForeignKey(
        $installer->getFkName('pfg_mypos/transaction', 'order_id', 'sales/order', 'entity_id'),
        'order_id',
        $installer->getTable('sales/order'),
        'entity_id',
        Varien_Db_Ddl_Table::ACTION_CASCADE,
        Varien_Db_Ddl_Table::ACTION_CASCADE
    )
    ->setComment('MyPOS Transactions Table');

$installer->getConnection()->createTable($table);

$installer->endSetup();
