<?php
/**
 * MyPOS Test Configuration Script
 * 
 * This script configures the MyPOS payment module with test credentials
 * from the official MyPOS documentation.
 */

// Include Magento
require_once 'app/Mage.php';
Mage::app();

// Test credentials from MyPOS documentation
$testCredentials = array(
    'active' => '1',
    'sandbox_mode' => '1',
    'merchant_id' => '000000000000010',
    'wallet_number' => '61938166610',
    'key_index' => '1',
    'title' => 'MyPOS Card Payment (Test Mode)',
    'currency' => 'EUR',
    'new_order_status' => 'pending',
    'order_status_after_payment' => 'processing',
    'sort_order' => '200',
    'allowspecific' => '0',
    
    // Test Private Key
    'private_key' => '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    
    // MyPOS Test Public Certificate
    'api_public_key' => '-----BEGIN CERTIFICATE-----
MIIBsTCCARoCCQCCPjNttGNQWDANBgkqhkiG9w0BAQsFADAdMQswCQYDVQQGEwJC
RzEOMAwGA1UECgwFbXlQT1MwHhcNMTgxMDEyMDcwOTEzWhcNMjgxMDA5MDcwOTEz
WjAdMQswCQYDVQQGEwJCRzEOMAwGA1UECgwFbXlQT1MwgZ8wDQYJKoZIhvcNAQEB
BQADgY0AMIGJAoGBAML+VTmiY4yChoOTMZTXAIG/mk+xf/9mjwHxWzxtBJbNncNK
0OLI0VXYKW2GgVklGHHQjvew1hTFkEGjnCJ7f5CDnbgxevtyASDGst92a6xcAedE
adP0nFXhUz+cYYIgIcgfDcX3ZWeNEF5kscqy52kpD2O7nFNCV+85vS4duJBNAgMB
AAEwDQYJKoZIhvcNAQELBQADgYEACj0xb+tNYERJkL+p+zDcBsBK4RvknPlpk+YP
ephunG2dBGOmg/WKgoD1PLWD2bEfGgJxYBIg9r1wLYpDC1txhxV+2OBQS86KULh0
NEcr0qEY05mI4FlE+D/BpT/+WFyKkZug92rK0Flz71Xy/9mBXbQfm+YK6l9roRYd
J4sHeQc=
-----END CERTIFICATE-----',

    // Configuration Package (Base64 encoded - alternative to manual keys)
    'configuration_package' => '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'
);

echo "Configuring MyPOS Test Environment...\n";

try {
    // Configure payment method settings
    foreach ($testCredentials as $field => $value) {
        $configPath = 'payment/pfg_mypos/' . $field;
        Mage::getModel('core/config')->saveConfig($configPath, $value);
        echo "Set {$field}: " . (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value) . "\n";
    }
    
    // Configure general settings
    Mage::getModel('core/config')->saveConfig('pfg_mypos/general/sandbox_url', 'https://www.mypos.com/vmp/checkout-test');
    Mage::getModel('core/config')->saveConfig('pfg_mypos/general/production_url', 'https://www.mypos.com/vmp/checkout');
    
    // Configure advanced settings
    Mage::getModel('core/config')->saveConfig('pfg_mypos/advanced/force_log', '1');
    Mage::getModel('core/config')->saveConfig('pfg_mypos/advanced/log_file_name', 'pfg_mypos.log');
    Mage::getModel('core/config')->saveConfig('pfg_mypos/advanced/exceptions_file_name', 'pfg_mypos_exceptions.log');
    
    // Set API auth token for development
    Mage::getModel('core/config')->saveConfig('pfg_mypos/api/auth_token', 'dev-test-token-' . time());
    
    // Clear cache
    Mage::app()->getCacheInstance()->flush();
    
    echo "\n✅ MyPOS Test Configuration Complete!\n";
    echo "\nTest Credentials Configured:\n";
    echo "- Merchant ID (SID): 000000000000010\n";
    echo "- Wallet Number: 61938166610\n";
    echo "- Key Index: 1\n";
    echo "- Sandbox Mode: Enabled\n";
    echo "- Test Environment URL: https://www.mypos.com/vmp/checkout-test\n";
    echo "\nNext Steps:\n";
    echo "1. Clear Magento cache: System > Cache Management > Flush Cache Storage\n";
    echo "2. Check payment method: System > Configuration > Payment Methods > PFG MyPOS\n";
    echo "3. Test checkout flow with MyPOS payment option\n";
    echo "\nTest Card Numbers (from MyPOS documentation):\n";
    echo "- Use any real card number for testing (card will NOT be charged)\n";
    echo "- Or use MyPOS test card numbers from their documentation\n";
    
} catch (Exception $e) {
    echo "❌ Error configuring MyPOS: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nConfiguration script completed.\n";
