<?php
/**
 * MyPOS Signature Test Script
 * 
 * This script tests the signature generation process to debug
 * the E_SIGNATURE_FAILED error.
 */

// Include Magento
require_once '/magento/app/Mage.php';
Mage::app();

echo "🔐 MyPOS Signature Generation Test\n";
echo "==================================\n\n";

try {
    // Get MyPOS configuration
    $helper = Mage::helper('pfg_mypos');
    $myposHelper = Mage::helper('pfg_mypos/mypos');
    
    echo "1. Testing MyPOS Configuration:\n";
    echo "   - Merchant ID: " . $helper->getPaymentConfig('merchant_id') . "\n";
    echo "   - Wallet Number: " . $helper->getPaymentConfig('wallet_number') . "\n";
    echo "   - Key Index: " . $helper->getPaymentConfig('key_index') . "\n";
    echo "   - Sandbox Mode: " . ($helper->getPaymentConfig('sandbox_mode') ? 'Yes' : 'No') . "\n";
    echo "   - Currency: " . $helper->getPaymentConfig('currency') . "\n\n";
    
    // Test private key
    $privateKey = $helper->getPaymentConfig('private_key');
    echo "2. Testing Private Key:\n";
    $privKey = openssl_get_privatekey($privateKey);
    if ($privKey) {
        $details = openssl_pkey_get_details($privKey);
        echo "   ✅ Private key is valid\n";
        echo "   - Key size: " . $details['bits'] . " bits\n";
        echo "   - Key type: " . $details['type'] . "\n";
    } else {
        echo "   ❌ Private key is INVALID\n";
        echo "   - Error: " . openssl_error_string() . "\n";
        exit(1);
    }
    echo "\n";
    
    // Test signature generation with simple data
    echo "3. Testing Signature Generation:\n";
    
    // Create a simple test signature
    $testParams = array(
        'SID' => '000000000000010',
        'WalletNumber' => '61938166610',
        'KeyIndex' => '1',
        'Amount' => '10.00',
        'Currency' => 'EUR',
        'OrderID' => 'TEST_' . time()
    );
    
    echo "   Test parameters:\n";
    foreach ($testParams as $key => $value) {
        echo "   - $key: $value\n";
    }
    
    // Generate signature manually
    $concData = base64_encode(implode('-', $testParams));
    echo "\n   Concatenated data (base64): " . substr($concData, 0, 50) . "...\n";
    
    $signature = '';
    if (openssl_sign($concData, $signature, $privKey, OPENSSL_ALGO_SHA256)) {
        $signatureBase64 = base64_encode($signature);
        echo "   ✅ Signature generated successfully\n";
        echo "   - Signature (first 50 chars): " . substr($signatureBase64, 0, 50) . "...\n";
        echo "   - Signature length: " . strlen($signatureBase64) . " characters\n";
    } else {
        echo "   ❌ Failed to generate signature\n";
        echo "   - Error: " . openssl_error_string() . "\n";
    }
    
    echo "\n";
    
    // Test with actual MyPOS SDK
    echo "4. Testing MyPOS SDK Configuration:\n";
    
    $config = $myposHelper->createConfig();
    echo "   ✅ MyPOS config created successfully\n";
    echo "   - IPC URL: " . $config->getIpcURL() . "\n";
    echo "   - SID: " . $config->getSid() . "\n";
    echo "   - Wallet: " . $config->getWallet() . "\n";
    echo "   - Key Index: " . $config->getKeyIndex() . "\n";
    echo "   - Language: " . $config->getLang() . "\n";
    echo "   - Version: " . $config->getVersion() . "\n";
    
    echo "\n✅ All tests completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Check if the test credentials are correct\n";
    echo "2. Verify parameter ordering with MyPOS documentation\n";
    echo "3. Test with minimal parameter set\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
