<?php
/**
 * MyPOS Signature Test Script
 *
 * This script tests the signature generation process to debug
 * the E_SIGNATURE_FAILED error.
 */

// Include Magento
require_once '/magento/app/Mage.php';
Mage::app();

echo "🔐 MyPOS Signature Generation Test\n";
echo "==================================\n\n";

// Include MyPOS SDK
require_once Mage::getModuleDir('', 'PFG_MyPos') . DS . 'lib' . DS . 'mypos-sdk' . DS . 'IPC' . DS . 'Loader.php';

try {
    // Get MyPOS configuration
    $helper = Mage::helper('pfg_mypos');
    $myposHelper = Mage::helper('pfg_mypos/mypos');

    echo "1. Testing MyPOS Configuration:\n";
    echo "   - Merchant ID: " . $helper->getPaymentConfig('merchant_id') . "\n";
    echo "   - Wallet Number: " . $helper->getPaymentConfig('wallet_number') . "\n";
    echo "   - Key Index: " . $helper->getPaymentConfig('key_index') . "\n";
    echo "   - Sandbox Mode: " . ($helper->getPaymentConfig('sandbox_mode') ? 'Yes' : 'No') . "\n";
    echo "   - Currency: " . $helper->getPaymentConfig('currency') . "\n";
    echo "   - API URL: " . $helper->getApiUrl() . "\n\n";
    
    // Test private key
    $privateKey = $helper->getPaymentConfig('private_key');
    echo "2. Testing Private Key:\n";
    $privKey = openssl_get_privatekey($privateKey);
    if ($privKey) {
        $details = openssl_pkey_get_details($privKey);
        echo "   ✅ Private key is valid\n";
        echo "   - Key size: " . $details['bits'] . " bits\n";
        echo "   - Key type: " . $details['type'] . "\n";
    } else {
        echo "   ❌ Private key is INVALID\n";
        echo "   - Error: " . openssl_error_string() . "\n";
        exit(1);
    }
    echo "\n";
    
    // Test signature generation with simple data
    echo "3. Testing Signature Generation:\n";
    
    // Create a simple test signature
    $testParams = array(
        'SID' => '000000000000010',
        'WalletNumber' => '61938166610',
        'KeyIndex' => '1',
        'Amount' => '10.00',
        'Currency' => 'EUR',
        'OrderID' => 'TEST_' . time()
    );
    
    echo "   Test parameters:\n";
    foreach ($testParams as $key => $value) {
        echo "   - $key: $value\n";
    }
    
    // Generate signature manually
    $concData = base64_encode(implode('-', $testParams));
    echo "\n   Concatenated data (base64): " . substr($concData, 0, 50) . "...\n";
    
    $signature = '';
    if (openssl_sign($concData, $signature, $privKey, OPENSSL_ALGO_SHA256)) {
        $signatureBase64 = base64_encode($signature);
        echo "   ✅ Signature generated successfully\n";
        echo "   - Signature (first 50 chars): " . substr($signatureBase64, 0, 50) . "...\n";
        echo "   - Signature length: " . strlen($signatureBase64) . " characters\n";
    } else {
        echo "   ❌ Failed to generate signature\n";
        echo "   - Error: " . openssl_error_string() . "\n";
    }
    
    echo "\n";
    
    // Test with actual MyPOS SDK
    echo "4. Testing MyPOS SDK Configuration:\n";
    
    $config = $myposHelper->createConfig();
    echo "   ✅ MyPOS config created successfully\n";
    echo "   - IPC URL: " . $config->getIpcURL() . "\n";
    echo "   - SID: " . $config->getSid() . "\n";
    echo "   - Wallet: " . $config->getWallet() . "\n";
    echo "   - Key Index: " . $config->getKeyIndex() . "\n";
    echo "   - Language: " . $config->getLang() . "\n";
    echo "   - Version: " . $config->getVersion() . "\n";
    
    echo "\n";

    // Test with actual parameters from the failed request
    echo "5. Testing with HAR File Parameters (Correct Order):\n";

    // Parameters in the exact order that MyPOS SDK uses
    $harParams = array(
        'IPCmethod' => 'IPCPurchase',
        'IPCVersion' => '1.4',
        'IPCLanguage' => 'bg',
        'SID' => '000000000000010',
        'WalletNumber' => '61938166610',
        'KeyIndex' => '1',
        'Source' => 'SDK_PHP_1.3.1',
        'Currency' => 'BGN',
        'Amount' => '21.43',
        'OrderID' => '100120969_1753971162',
        'URL_OK' => 'https://carco.bg/pfg_mypos/response/success',
        'URL_Cancel' => 'https://carco.bg/pfg_mypos/response/cancel',
        'URL_Notify' => 'https://carco.bg/pfg_mypos/response/notify',
        'Note' => 'Order #100120969',
        'expires_in' => '86400',
        'ApplicationID' => '',
        'PartnerID' => '',
        'customeremail' => '<EMAIL>',
        'customerphone' => '0883555204',
        'customerfirstnames' => 'Stoyan',
        'customerfamilyname' => 'Atanasov',
        'customercountry' => 'BG',
        'customercity' => 'София',
        'customerzipcode' => '1849',
        'customeraddress' => 'ул. „Свети Георги Победоносец" №1А',
        'CartItems' => '1',
        'Article_1' => 'Греда под арматурно табло за AUDI A4 Avant (8D5, B5) 2.8 quattro (1994 - 2001)',
        'Quantity_1' => '1',
        'Price_1' => '21.43',
        'Amount_1' => '21.43',
        'Currency_1' => 'BGN',
        'CardTokenRequest' => '0',
        'PaymentParametersRequired' => '1',
        'PaymentMethod' => '3'
    );

    echo "   Testing signature with " . count($harParams) . " parameters...\n";

    // Generate signature using MyPOS SDK method
    $concData = base64_encode(implode('-', $harParams));
    echo "   - Concatenated data length: " . strlen($concData) . " characters\n";
    echo "   - First 100 chars: " . substr($concData, 0, 100) . "...\n";

    $harSignature = '';
    if (openssl_sign($concData, $harSignature, $privKey, OPENSSL_ALGO_SHA256)) {
        $harSignatureBase64 = base64_encode($harSignature);
        echo "   ✅ HAR signature generated successfully\n";
        echo "   - Generated signature: " . substr($harSignatureBase64, 0, 50) . "...\n";
        echo "   - Expected signature:  XxOEOCVFb/a7NpUqburCGALZs3P93cH++6ce+tynZWZ4JsXa7fOd5WSp9goZPW3zHX99WnDrtLMSJa/RPOqf2kp4pkr/qP0LHMxZj5H/6HjhaUFGuXvXqz0nRcsdbCggo+YVKggjLHV73diPq9+eu7nw2QXmt0oHiuN1RU3ZGyo=\n";

        // Compare signatures
        $expectedSignature = 'XxOEOCVFb/a7NpUqburCGALZs3P93cH++6ce+tynZWZ4JsXa7fOd5WSp9goZPW3zHX99WnDrtLMSJa/RPOqf2kp4pkr/qP0LHMxZj5H/6HjhaUFGuXvXqz0nRcsdbCggo+YVKggjLHV73diPq9+eu7nw2QXmt0oHiuN1RU3ZGyo=';
        if ($harSignatureBase64 === $expectedSignature) {
            echo "   ✅ Signatures MATCH! The issue is not with signature generation.\n";
        } else {
            echo "   ❌ Signatures DO NOT MATCH! This is the source of the problem.\n";
        }
    } else {
        echo "   ❌ Failed to generate HAR signature\n";
        echo "   - Error: " . openssl_error_string() . "\n";
    }

    echo "\n";

    // Test with actual MyPOS SDK to get the correct signature
    echo "6. Testing with MyPOS SDK Purchase Object:\n";

    try {
        // Create a test order
        $testOrder = new Varien_Object(array(
            'increment_id' => '100120969',
            'grand_total' => 21.43,
            'order_currency_code' => 'BGN',
            'store_id' => 1
        ));

        // Create customer data
        $customer = new \Mypos\IPC\Customer();
        $customer->setEmail('<EMAIL>');
        $customer->setPhone('0883555204');
        $customer->setFirstName('Stoyan');
        $customer->setLastName('Atanasov');
        $customer->setCountry('BG');
        $customer->setCity('София');
        $customer->setZip('1849');
        $customer->setAddress('ул. „Свети Георги Победоносец" №1А');

        // Create cart
        $cart = new \Mypos\IPC\Cart();
        $cart->add('Греда под арматурно табло за AUDI A4 Avant (8D5, B5) 2.8 quattro (1994 - 2001)', 1, 21.43);

        // Create purchase object
        $config = $myposHelper->createConfig();
        $purchase = new \Mypos\IPC\Purchase($config);

        // Set all the same parameters
        $purchase->setOrderID('100120969_1753971162');
        $purchase->setCurrency('BGN');
        $purchase->setNote('Order #100120969');
        $purchase->setUrlOk('https://carco.bg/pfg_mypos/response/success');
        $purchase->setUrlCancel('https://carco.bg/pfg_mypos/response/cancel');
        $purchase->setUrlNotify('https://carco.bg/pfg_mypos/response/notify');
        $purchase->setCustomer($customer);
        $purchase->setCart($cart);
        $purchase->setCardTokenRequest(0);
        $purchase->setPaymentParametersRequired(1);
        $purchase->setPaymentMethod(3);
        $purchase->setExpiresIn('86400');

        // Process without submitting to get form parameters
        $purchase->process(false);
        $formParams = $purchase->getFormParameters();

        echo "   ✅ MyPOS SDK purchase object created successfully\n";
        echo "   - SDK Generated signature: " . substr($formParams['FormData']['Signature'], 0, 50) . "...\n";
        echo "   - Expected signature:      XxOEOCVFb/a7NpUqburCGALZs3P93cH++6ce+tynZWZ4JsXa7fOd5WSp9goZPW3zHX99WnDrtLMSJa/RPOqf2kp4pkr/qP0LHMxZj5H/6HjhaUFGuXvXqz0nRcsdbCggo+YVKggjLHV73diPq9+eu7nw2QXmt0oHiuN1RU3ZGyo=\n";

        if ($formParams['FormData']['Signature'] === 'XxOEOCVFb/a7NpUqburCGALZs3P93cH++6ce+tynZWZ4JsXa7fOd5WSp9goZPW3zHX99WnDrtLMSJa/RPOqf2kp4pkr/qP0LHMxZj5H/6HjhaUFGuXvXqz0nRcsdbCggo+YVKggjLHV73diPq9+eu7nw2QXmt0oHiuN1RU3ZGyo=') {
            echo "   ✅ SDK signature MATCHES expected signature!\n";
        } else {
            echo "   ❌ SDK signature does NOT match expected signature.\n";
            echo "   \nForm parameters from SDK:\n";
            foreach ($formParams['FormData'] as $key => $value) {
                if ($key !== 'Signature') {
                    echo "   - $key: $value\n";
                }
            }
        }

    } catch (Exception $e) {
        echo "   ❌ Error creating MyPOS purchase: " . $e->getMessage() . "\n";
    }

    echo "\n✅ All tests completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Check if the test credentials are correct\n";
    echo "2. Verify parameter ordering with MyPOS documentation\n";
    echo "3. Test with minimal parameter set\n";
    echo "4. If signatures don't match, check parameter encoding/escaping\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
