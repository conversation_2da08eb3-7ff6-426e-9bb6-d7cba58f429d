<?php
/**
 * Simple MyPOS Test Script
 */

// Include Magento
require_once 'app/Mage.php';
Mage::app();

echo "Simple MyPOS Test...\n\n";

try {
    // Test 1: Check available payment methods
    echo "1. Checking available payment methods...\n";
    
    $paymentMethods = Mage::helper('payment')->getStoreMethods(Mage::app()->getStore()->getId());
    $myposFound = false;
    
    foreach ($paymentMethods as $method) {
        echo "   - " . $method->getCode() . ": " . $method->getTitle() . "\n";
        if ($method->getCode() === 'pfg_mypos') {
            $myposFound = true;
            echo "     ✅ MyPOS payment method found!\n";
        }
    }
    
    if (!$myposFound) {
        echo "   ❌ MyPOS payment method not found\n";
    }
    
    // Test 2: Test MyPOS configuration
    echo "\n2. Testing MyPOS configuration...\n";
    $paymentMethod = Mage::getModel('pfg_mypos/payment_mypos');
    echo "   Code: " . $paymentMethod->getCode() . "\n";
    echo "   Title: " . $paymentMethod->getTitle() . "\n";
    echo "   Active: " . ($paymentMethod->getConfigData('active') ? 'Yes' : 'No') . "\n";
    echo "   Sandbox: " . ($paymentMethod->getConfigData('sandbox_mode') ? 'Yes' : 'No') . "\n";
    
    // Test 3: Test callback URLs
    echo "\n3. Testing callback URLs...\n";
    $helper = Mage::helper('pfg_mypos');
    $urls = $helper->getCallbackUrls();
    echo "   Success: " . $urls['success'] . "\n";
    echo "   Cancel: " . $urls['cancel'] . "\n";
    echo "   Notify: " . $urls['notify'] . "\n";
    
    echo "\n✅ All tests passed!\n";
    echo "\nTo test the payment flow:\n";
    echo "1. Go to: http://localhost\n";
    echo "2. Add any product to cart\n";
    echo "3. Go to checkout\n";
    echo "4. Look for 'MyPOS плащане' option\n";
    echo "5. Complete the order to test redirect\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\nTest completed.\n";
