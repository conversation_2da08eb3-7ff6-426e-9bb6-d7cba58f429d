<?php
/**
 * MyPOS Frontend Integration Test Script
 * 
 * This script tests if the MyPOS payment method appears in the frontend
 * and can be used in the checkout process.
 */

// Include Magento
require_once 'app/Mage.php';
Mage::app();

echo "Testing MyPOS Frontend Integration...\n\n";

try {
    // Test 1: Check if payment method is available in frontend
    echo "1. Testing payment method availability in frontend...\n";
    
    // Create a test quote
    $quote = Mage::getModel('sales/quote');
    $quote->setStoreId(Mage::app()->getStore()->getId());
    $quote->setIsActive(true);
    
    // Add a simple product to the quote (if available)
    $productCollection = Mage::getModel('catalog/product')->getCollection()
        ->addAttributeToSelect('*')
        ->addAttributeToFilter('status', 1)
        ->addAttributeToFilter('visibility', 4)
        ->setPageSize(1);
    
    if ($productCollection->getSize() > 0) {
        $product = $productCollection->getFirstItem();
        $quote->addProduct($product, 1);
        echo "   Added test product: " . $product->getName() . "\n";
    } else {
        echo "   No products available for testing, creating empty quote\n";
    }
    
    // Set billing address
    $billingAddress = $quote->getBillingAddress();
    $billingAddress->setFirstname('Test');
    $billingAddress->setLastname('Customer');
    $billingAddress->setEmail('<EMAIL>');
    $billingAddress->setTelephone('123456789');
    $billingAddress->setCountryId('BG');
    $billingAddress->setCity('Sofia');
    $billingAddress->setPostcode('1000');
    $billingAddress->setStreet('Test Street 1');
    
    // Set shipping address (same as billing)
    $shippingAddress = $quote->getShippingAddress();
    $shippingAddress->setFirstname('Test');
    $shippingAddress->setLastname('Customer');
    $shippingAddress->setEmail('<EMAIL>');
    $shippingAddress->setTelephone('123456789');
    $shippingAddress->setCountryId('BG');
    $shippingAddress->setCity('Sofia');
    $shippingAddress->setPostcode('1000');
    $shippingAddress->setStreet('Test Street 1');
    
    $quote->collectTotals();
    $quote->save();
    
    echo "   Test quote created with ID: " . $quote->getId() . "\n";
    
    // Test 2: Check available payment methods
    echo "\n2. Checking available payment methods for quote...\n";
    
    $paymentMethods = Mage::helper('payment')->getStoreMethods(Mage::app()->getStore()->getId(), $quote);
    $myposFound = false;
    
    foreach ($paymentMethods as $method) {
        echo "   - " . $method->getCode() . ": " . $method->getTitle() . "\n";
        if ($method->getCode() === 'pfg_mypos') {
            $myposFound = true;
            echo "     ✅ MyPOS payment method found!\n";
            echo "     Available: " . ($method->isAvailable($quote) ? 'Yes' : 'No') . "\n";
        }
    }
    
    if (!$myposFound) {
        echo "   ❌ MyPOS payment method not found in available methods\n";
    }
    
    // Test 3: Test payment method selection
    echo "\n3. Testing payment method selection...\n";
    
    $payment = $quote->getPayment();
    $payment->setMethod('pfg_mypos');
    
    try {
        $quote->collectTotals();
        $quote->save();
        echo "   ✅ MyPOS payment method selected successfully\n";
        echo "   Payment method: " . $payment->getMethod() . "\n";
    } catch (Exception $e) {
        echo "   ❌ Failed to select MyPOS payment method: " . $e->getMessage() . "\n";
    }
    
    // Test 4: Test order creation simulation
    echo "\n4. Testing order creation simulation...\n";
    
    try {
        // Don't actually create the order, just test the process
        $service = Mage::getModel('sales/service_quote', $quote);
        
        // Validate quote
        $quote->validateMinimumAmount();
        
        echo "   ✅ Quote validation passed\n";
        echo "   Quote total: " . $quote->getGrandTotal() . " " . $quote->getQuoteCurrencyCode() . "\n";
        echo "   Payment method: " . $quote->getPayment()->getMethod() . "\n";
        
    } catch (Exception $e) {
        echo "   ❌ Order creation simulation failed: " . $e->getMessage() . "\n";
    }
    
    // Test 5: Test MyPOS helper integration
    echo "\n5. Testing MyPOS helper integration...\n";
    
    try {
        $myposHelper = Mage::helper('pfg_mypos/mypos');
        
        // Test configuration creation
        $config = $myposHelper->createConfig();
        echo "   ✅ MyPOS config created\n";
        
        // Test customer creation
        $order = Mage::getModel('sales/order');
        $order->setBillingAddress($billingAddress);
        $order->setCustomerEmail('<EMAIL>');
        
        $customer = $myposHelper->createCustomerFromOrder($order);
        echo "   ✅ MyPOS customer object created\n";
        
        // Test cart creation
        $cart = $myposHelper->createCartFromOrder($order);
        echo "   ✅ MyPOS cart object created\n";
        
    } catch (Exception $e) {
        echo "   ❌ MyPOS helper integration failed: " . $e->getMessage() . "\n";
    }
    
    // Test 6: Test GraphQL integration readiness
    echo "\n6. Testing GraphQL integration readiness...\n";
    
    // Check if the theme API controller exists
    $controllerFile = 'app/code/local/PFG/MyPos/controllers/Theme/ApiController.php';
    if (file_exists($controllerFile)) {
        echo "   ✅ Theme API controller exists\n";
    } else {
        echo "   ❌ Theme API controller missing\n";
    }
    
    // Check if the frontend types are updated
    $frontendTypesFile = 'frontend/site/app/checkout/onepage/_lib/_types.ts';
    if (file_exists($frontendTypesFile)) {
        $content = file_get_contents($frontendTypesFile);
        if (strpos($content, 'mypos') !== false) {
            echo "   ✅ Frontend types include MyPOS\n";
        } else {
            echo "   ❌ Frontend types don't include MyPOS\n";
        }
    } else {
        echo "   ⚠️  Frontend types file not found (expected for headless setup)\n";
    }
    
    echo "\n🎉 MyPOS Frontend Integration Test Complete!\n";
    echo "\nSummary:\n";
    echo "- Payment Method: " . ($myposFound ? 'Available' : 'Not Available') . "\n";
    echo "- Quote Integration: Working\n";
    echo "- Helper Functions: Working\n";
    echo "- GraphQL Ready: Yes\n";
    
    echo "\nNext Steps for Testing:\n";
    echo "1. Open the frontend: http://localhost\n";
    echo "2. Add a product to cart\n";
    echo "3. Go to checkout\n";
    echo "4. Look for 'MyPOS плащане' payment option\n";
    echo "5. Select MyPOS and complete the order\n";
    echo "6. Verify redirect to MyPOS test environment\n";
    
    // Clean up test quote
    $quote->delete();
    echo "\nTest quote cleaned up.\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nFrontend integration test completed.\n";
