<?php
/**
 * MyPOS Payment Method Test Script
 * 
 * This script tests if the MyPOS payment method is properly configured
 * and can be loaded by Magento.
 */

// Include Magento
require_once 'app/Mage.php';
Mage::app();

echo "Testing MyPOS Payment Method Configuration...\n\n";

try {
    // Test 1: Check if module is enabled
    echo "1. Checking if PFG_MyPos module is enabled...\n";
    $moduleConfig = Mage::getConfig()->getModuleConfig('PFG_MyPos');
    if ($moduleConfig && $moduleConfig->is('active', 'true')) {
        echo "   ✅ Module is enabled\n";
    } else {
        echo "   ❌ Module is not enabled\n";
        exit(1);
    }
    
    // Test 2: Check if payment method is available
    echo "\n2. Checking if payment method is available...\n";
    $paymentMethod = Mage::getModel('pfg_mypos/payment_mypos');
    if ($paymentMethod) {
        echo "   ✅ Payment method model loaded successfully\n";
        echo "   Payment method code: " . $paymentMethod->getCode() . "\n";
        echo "   Payment method title: " . $paymentMethod->getTitle() . "\n";
    } else {
        echo "   ❌ Failed to load payment method model\n";
        exit(1);
    }
    
    // Test 3: Check configuration
    echo "\n3. Checking payment method configuration...\n";
    $isActive = $paymentMethod->getConfigData('active');
    $isSandbox = $paymentMethod->getConfigData('sandbox_mode');
    $merchantId = $paymentMethod->getConfigData('merchant_id');
    $walletNumber = $paymentMethod->getConfigData('wallet_number');
    
    echo "   Active: " . ($isActive ? 'Yes' : 'No') . "\n";
    echo "   Sandbox Mode: " . ($isSandbox ? 'Yes' : 'No') . "\n";
    echo "   Merchant ID: " . $merchantId . "\n";
    echo "   Wallet Number: " . $walletNumber . "\n";
    
    if ($isActive && $merchantId && $walletNumber) {
        echo "   ✅ Configuration looks good\n";
    } else {
        echo "   ❌ Configuration is incomplete\n";
    }
    
    // Test 4: Check if payment method is available for checkout
    echo "\n4. Checking if payment method is available for checkout...\n";
    $quote = Mage::getModel('sales/quote');
    $quote->setStoreId(Mage::app()->getStore()->getId());
    
    if ($paymentMethod->isAvailable($quote)) {
        echo "   ✅ Payment method is available for checkout\n";
    } else {
        echo "   ❌ Payment method is not available for checkout\n";
        echo "   This might be due to missing credentials or configuration\n";
    }
    
    // Test 5: Test MyPOS SDK loading
    echo "\n5. Testing MyPOS SDK loading...\n";
    try {
        $helper = Mage::helper('pfg_mypos/mypos');
        $config = $helper->createConfig();
        echo "   ✅ MyPOS SDK loaded successfully\n";
        echo "   SDK Config created\n";
    } catch (Exception $e) {
        echo "   ❌ Failed to load MyPOS SDK: " . $e->getMessage() . "\n";
    }
    
    // Test 6: Test helper functions
    echo "\n6. Testing helper functions...\n";
    $dataHelper = Mage::helper('pfg_mypos');
    $isSandboxMode = $dataHelper->isSandboxMode();
    $apiUrl = $dataHelper->getApiUrl();
    $callbackUrls = $dataHelper->getCallbackUrls();
    
    echo "   Sandbox Mode: " . ($isSandboxMode ? 'Yes' : 'No') . "\n";
    echo "   API URL: " . $apiUrl . "\n";
    echo "   Success URL: " . $callbackUrls['success'] . "\n";
    echo "   Cancel URL: " . $callbackUrls['cancel'] . "\n";
    echo "   Notify URL: " . $callbackUrls['notify'] . "\n";
    echo "   ✅ Helper functions working correctly\n";
    
    // Test 7: Check database table
    echo "\n7. Checking database table...\n";
    try {
        $transaction = Mage::getModel('pfg_mypos/transaction');
        $collection = $transaction->getCollection();
        $collection->getSize(); // This will trigger the query
        echo "   ✅ Database table 'pfg_mypos_transactions' exists and is accessible\n";
    } catch (Exception $e) {
        echo "   ❌ Database table issue: " . $e->getMessage() . "\n";
    }
    
    echo "\n🎉 MyPOS Payment Method Test Complete!\n";
    echo "\nSummary:\n";
    echo "- Module: Enabled\n";
    echo "- Payment Method: Available\n";
    echo "- Configuration: " . ($isActive && $merchantId && $walletNumber ? 'Complete' : 'Incomplete') . "\n";
    echo "- SDK: Loaded\n";
    echo "- Database: Ready\n";
    
    echo "\nNext Steps:\n";
    echo "1. Access Magento Admin: http://localhost/admin\n";
    echo "2. Go to System > Configuration > Payment Methods > PFG MyPOS\n";
    echo "3. Verify all settings are correct\n";
    echo "4. Test the checkout flow on the frontend\n";
    echo "5. Create a test order with MyPOS payment method\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nTest script completed.\n";
