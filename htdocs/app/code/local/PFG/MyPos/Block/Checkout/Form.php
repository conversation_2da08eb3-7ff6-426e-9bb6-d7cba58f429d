<?php

class PFG_MyPos_Block_Checkout_Form extends Mage_Payment_Block_Form
{
    protected $_instructions;

    protected function _construct()
    {
        parent::_construct();
        $this->setTemplate('pfg/mypos/form.phtml');
    }

    /**
     * Get instructions text from config
     *
     * @return string
     */
    public function getInstructions()
    {
        if (is_null($this->_instructions)) {
            $this->_instructions = $this->getMethod()->getInstructions();
        }
        return $this->_instructions;
    }

    /**
     * Get payment method title
     *
     * @return string
     */
    public function getMethodTitle()
    {
        return $this->getMethod()->getTitle();
    }

    /**
     * Check if sandbox mode is enabled
     *
     * @return bool
     */
    public function isSandboxMode()
    {
        return Mage::helper('pfg_mypos')->isSandboxMode();
    }

    /**
     * Get supported currencies
     *
     * @return array
     */
    public function getSupportedCurrencies()
    {
        return Mage::helper('pfg_mypos')->getSupportedCurrencies();
    }

    /**
     * Get current quote currency
     *
     * @return string
     */
    public function getQuoteCurrency()
    {
        return $this->getMethod()->getQuote()->getQuoteCurrencyCode();
    }

    /**
     * Check if current currency is supported
     *
     * @return bool
     */
    public function isCurrencySupported()
    {
        return Mage::helper('pfg_mypos')->isCurrencySupported($this->getQuoteCurrency());
    }
}
