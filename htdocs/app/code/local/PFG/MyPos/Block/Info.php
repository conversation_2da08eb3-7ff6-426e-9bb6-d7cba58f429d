<?php

class PFG_MyPos_Block_Info extends Mage_Payment_Block_Info
{
    protected function _construct()
    {
        parent::_construct();
        $this->setTemplate('pfg/mypos/info.phtml');
    }

    /**
     * Get payment method title
     *
     * @return string
     */
    public function getMethodTitle()
    {
        return $this->getMethod()->getTitle();
    }

    /**
     * Get MyPOS order ID
     *
     * @return string|null
     */
    public function getMyPosOrderId()
    {
        return $this->getInfo()->getAdditionalInformation('mypos_order_id');
    }

    /**
     * Get transaction ID
     *
     * @return string|null
     */
    public function getTransactionId()
    {
        return $this->getInfo()->getTransactionId();
    }

    /**
     * Get payment status
     *
     * @return string
     */
    public function getPaymentStatus()
    {
        $order = $this->getInfo()->getOrder();
        if ($order) {
            return $order->getStatus();
        }
        return 'Unknown';
    }

    /**
     * Check if payment is completed
     *
     * @return bool
     */
    public function isPaymentCompleted()
    {
        $order = $this->getInfo()->getOrder();
        if ($order) {
            return $order->getState() === Mage_Sales_Model_Order::STATE_PROCESSING ||
                   $order->getState() === Mage_Sales_Model_Order::STATE_COMPLETE;
        }
        return false;
    }

    /**
     * Get formatted payment amount
     *
     * @return string
     */
    public function getFormattedAmount()
    {
        $order = $this->getInfo()->getOrder();
        if ($order) {
            return $order->formatPrice($order->getGrandTotal());
        }
        return '';
    }

    /**
     * Get payment currency
     *
     * @return string
     */
    public function getPaymentCurrency()
    {
        $order = $this->getInfo()->getOrder();
        if ($order) {
            return $order->getOrderCurrencyCode();
        }
        return '';
    }
}
