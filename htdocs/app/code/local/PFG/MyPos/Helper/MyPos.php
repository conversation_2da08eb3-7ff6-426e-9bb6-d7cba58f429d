<?php

// Include MyPOS SDK
require_once Mage::getModuleDir('', 'PFG_MyPos') . DS . 'lib' . DS . 'mypos-sdk' . DS . 'IPC' . DS . 'Loader.php';

class PFG_MyPos_Helper_MyPos extends Mage_Core_Helper_Abstract
{
    /**
     * Create MyPOS configuration object
     *
     * @param int|null $storeId
     * @return \Mypos\IPC\Config
     */
    public function createConfig($storeId = null)
    {
        $helper = Mage::helper('pfg_mypos');
        $config = new \Mypos\IPC\Config();
        
        // Set basic configuration
        $config->setIpcURL($helper->getApiUrl($storeId));
        $config->setLang('bg');
        $config->setVersion('1.4');
        
        // Check if we have configuration package
        $configPackage = $helper->getPaymentConfig('configuration_package', $storeId);
        if (!empty($configPackage)) {
            $config->loadConfigurationPackage($configPackage);
        } else {
            // Use manual configuration
            $config->setSid($helper->getPaymentConfig('merchant_id', $storeId));
            $config->setWallet($helper->getPaymentConfig('wallet_number', $storeId));
            $config->setKeyIndex((int) $helper->getPaymentConfig('key_index', $storeId) ?: 1);
            
            // Set RSA keys
            $privateKey = $helper->getPaymentConfig('private_key', $storeId);
            $publicKey = $helper->getPaymentConfig('api_public_key', $storeId);
            
            if (!empty($privateKey)) {
                $config->setPrivateKey($privateKey);
            }
            if (!empty($publicKey)) {
                $config->setAPIPublicKey($publicKey);
            }
        }
        
        return $config;
    }

    /**
     * Create customer object from order
     *
     * @param Mage_Sales_Model_Order $order
     * @return \Mypos\IPC\Customer
     */
    public function createCustomerFromOrder(Mage_Sales_Model_Order $order)
    {
        $billingAddress = $order->getBillingAddress();
        $customer = new \Mypos\IPC\Customer();
        
        $customer->setFirstName($billingAddress->getFirstname());
        $customer->setLastName($billingAddress->getLastname());
        $customer->setEmail($order->getCustomerEmail());
        $customer->setPhone($billingAddress->getTelephone());
        
        // Set address information
        $customer->setCountry($billingAddress->getCountryId());
        $customer->setCity($billingAddress->getCity());
        $customer->setZip($billingAddress->getPostcode());
        $customer->setAddress($billingAddress->getStreetFull());
        
        return $customer;
    }

    /**
     * Create cart object from order
     *
     * @param Mage_Sales_Model_Order $order
     * @return \Mypos\IPC\Cart
     */
    public function createCartFromOrder(Mage_Sales_Model_Order $order)
    {
        $cart = new \Mypos\IPC\Cart();
        
        // Add order items
        foreach ($order->getAllVisibleItems() as $item) {
            $cart->add(
                $item->getName(),
                (int) $item->getQtyOrdered(),
                (float) $item->getPrice()
            );
        }
        
        // Add shipping if applicable
        if ($order->getShippingAmount() > 0) {
            $cart->add(
                'Shipping - ' . $order->getShippingDescription(),
                1,
                (float) $order->getShippingAmount()
            );
        }
        
        // Add tax if applicable
        if ($order->getTaxAmount() > 0) {
            $cart->add(
                'Tax',
                1,
                (float) $order->getTaxAmount()
            );
        }
        
        // Add discount if applicable
        if ($order->getDiscountAmount() < 0) {
            $cart->add(
                'Discount',
                1,
                (float) $order->getDiscountAmount()
            );
        }
        
        return $cart;
    }

    /**
     * Create purchase request
     *
     * @param Mage_Sales_Model_Order $order
     * @return \Mypos\IPC\Purchase
     */
    public function createPurchaseRequest(Mage_Sales_Model_Order $order)
    {
        $helper = Mage::helper('pfg_mypos');
        $config = $this->createConfig($order->getStoreId());
        $purchase = new \Mypos\IPC\Purchase($config);
        
        // Set callback URLs
        $urls = $helper->getCallbackUrls($order->getStoreId());
        $purchase->setUrlOk($urls['success']);
        $purchase->setUrlCancel($urls['cancel']);
        $purchase->setUrlNotify($urls['notify']);
        
        // Set order information
        $myposOrderId = $helper->generateMyPosOrderId($order);
        $purchase->setOrderID($myposOrderId);
        $purchase->setCurrency($helper->getPaymentConfig('currency', $order->getStoreId()) ?: 'BGN');
        $purchase->setNote('Order #' . $order->getIncrementId());
        
        // Set customer and cart
        $purchase->setCustomer($this->createCustomerFromOrder($order));
        $purchase->setCart($this->createCartFromOrder($order));
        
        // Set payment parameters
        $purchase->setCardTokenRequest(\Mypos\IPC\Purchase::CARD_TOKEN_REQUEST_NONE);
        $purchase->setPaymentParametersRequired(\Mypos\IPC\Purchase::PURCHASE_TYPE_FULL);
        $purchase->setPaymentMethod(\Mypos\IPC\Purchase::PAYMENT_METHOD_BOTH);
        
        return $purchase;
    }

    /**
     * Process purchase request
     *
     * @param Mage_Sales_Model_Order $order
     * @return array
     */
    public function processPurchase(Mage_Sales_Model_Order $order)
    {
        $helper = Mage::helper('pfg_mypos');

        try {
            $purchase = $this->createPurchaseRequest($order);

            // Log the request
            $helper->log('Creating MyPOS purchase request for order: ' . $order->getIncrementId());

            // Save transaction record
            $this->saveTransaction($order, $purchase->getOrderID(), 'pending');

            // Process the purchase without auto-submitting the form
            $purchase->process(false);

            // Get form parameters for manual redirect
            $formParams = $purchase->getFormParameters();

            return array(
                'success' => true,
                'mypos_order_id' => $purchase->getOrderID(),
                'redirect_url' => $formParams['ActionUrl'],
                'form_data' => $formParams['FormData']
            );

        } catch (\Mypos\IPC\IPC_Exception $e) {
            $helper->logException($e);
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        } catch (Exception $e) {
            $helper->logException($e);
            return array(
                'success' => false,
                'error' => 'An unexpected error occurred'
            );
        }
    }

    /**
     * Process refund request
     *
     * @param Mage_Sales_Model_Order $order
     * @param float $amount
     * @param string $transactionId
     * @return array
     */
    public function processRefund(Mage_Sales_Model_Order $order, $amount, $transactionId)
    {
        $helper = Mage::helper('pfg_mypos');
        
        try {
            $config = $this->createConfig($order->getStoreId());
            $refund = new \Mypos\IPC\Refund($config);
            
            $refund->setOrderID($transactionId);
            $refund->setAmount($amount);
            $refund->setCurrency($helper->getPaymentConfig('currency', $order->getStoreId()) ?: 'BGN');
            $refund->setNote('Refund for order #' . $order->getIncrementId());
            
            // Process refund
            $response = $refund->process();
            
            $helper->log('MyPOS refund processed for order: ' . $order->getIncrementId());
            
            return array(
                'success' => true,
                'refund_transaction_id' => $response->getTransactionId()
            );
            
        } catch (\Mypos\IPC\IPC_Exception $e) {
            $helper->logException($e);
            return array(
                'success' => false,
                'error_message' => $e->getMessage()
            );
        } catch (Exception $e) {
            $helper->logException($e);
            return array(
                'success' => false,
                'error_message' => 'An unexpected error occurred during refund'
            );
        }
    }

    /**
     * Save transaction record
     *
     * @param Mage_Sales_Model_Order $order
     * @param string $myposOrderId
     * @param string $status
     * @param array $responseData
     */
    public function saveTransaction(Mage_Sales_Model_Order $order, $myposOrderId, $status, $responseData = array())
    {
        try {
            $transaction = Mage::getModel('pfg_mypos/transaction');
            $transaction->setData(array(
                'order_id' => $order->getId(),
                'mypos_order_id' => $myposOrderId,
                'status' => $status,
                'amount' => $order->getGrandTotal(),
                'currency' => $order->getOrderCurrencyCode(),
                'request_data' => json_encode(array('order_id' => $myposOrderId)),
                'response_data' => json_encode($responseData),
                'created_at' => now(),
                'updated_at' => now()
            ));
            $transaction->save();
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->logException($e);
        }
    }
}
