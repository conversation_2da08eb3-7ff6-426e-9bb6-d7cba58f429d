<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Downloader
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<targets>
    <target name="magelocal" label="Magento Local module file" uri="./app/code/local" />
    <target name="magecommunity" label="Magento Community module file" uri="./app/code/community" />
    <target name="magecore" label="Magento Core team module file" uri="./app/code/core" />
    <target name="magedesign" label="Magento User Interface (layouts, templates)" uri="./app/design" />
    <target name="mageetc" label="Magento Global Configuration" uri="./app/etc" />
    <target name="magelib" label="Magento PHP Library file" uri="./lib" />
    <target name="magelocale" label="Magento Locale language file" uri="./app/locale" />
    <target name="magemedia" label="Magento Media library" uri="./media" />
    <target name="mageskin" label="Magento Theme Skin (Images, CSS, JS)" uri="./skin" />
    <target name="mageweb" label="Magento Other web accessible file" uri="." />
    <target name="magetest" label="Magento PHPUnit test" uri="./tests" />
    <target name="mage" label="Magento other" uri="." />
</targets>
